/**
 * @file grpc_service.cpp
 * @brief gRPC service implementation for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides the implementation of gRPC services for the OMOP ETL pipeline,
 * including server management, service implementations, and utilities.
 */

#include "grpc_service.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include "common/metrics_collector.h"
#include "core/pipeline.h"
#include "core/job_manager.h"
#include "etl/etl_service.h"

#ifdef OMOP_HAS_GRPC
#include <grpcpp/grpcpp.h>
#include <grpcpp/health_check_service_interface.h>
#include <grpcpp/ext/proto_server_reflection_plugin.h>
#endif

#include <memory>
#include <thread>
#include <chrono>
#include <regex>
#include <sstream>
#include <iomanip>

namespace omop::api {

#ifdef OMOP_HAS_GRPC

// Forward declaration for the actual gRPC service implementation
namespace grpc {
    namespace OMOPETLService {
        class Service;
    }
}

// Actual gRPC service implementation
class OMOPETLServiceImpl : public grpc::OMOPETLService::Service {
public:
    OMOPETLServiceImpl(std::shared_ptr<omop::etl::IETLService> etl_service,
                      std::shared_ptr<omop::security::IAuthManager> auth_manager,
                      std::shared_ptr<omop::security::IAuthorizationManager> authz_manager,
                      std::shared_ptr<omop::security::IAuditLogger> audit_logger,
                      std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector)
        : etl_service_(etl_service)
        , auth_manager_(auth_manager)
        , authz_manager_(authz_manager)
        , audit_logger_(audit_logger)
        , metrics_collector_(metrics_collector)
        , start_time_(std::chrono::steady_clock::now()) {
        
        auto logger = common::Logger::get("omop-grpc-service");
        logger->info("OMOP ETL gRPC service implementation initialized");
    }

    // Service management methods
    grpc::Status GetServiceStatus(grpc::ServerContext* context,
                                 const grpc::ServiceStatusRequest* request,
                                 grpc::ServiceStatusResponse* response) override {
        try {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->debug("GetServiceStatus called for service: {}", request->service_name());

            // Set response
            response->set_status("RUNNING");
            response->set_is_healthy(true);
            response->set_version("1.0.0");
            
            auto uptime = std::chrono::steady_clock::now() - start_time_;
            response->set_uptime_seconds(std::chrono::duration_cast<std::chrono::seconds>(uptime).count());
            
            response->add_active_features("ETL_PIPELINE");
            response->add_active_features("JOB_MANAGEMENT");
            response->add_active_features("DATA_TRANSFORMATION");
            
            (*response->mutable_metadata())["build_type"] = "release";
            (*response->mutable_metadata())["platform"] = "linux";
            (*response->mutable_metadata())["architecture"] = "x86_64";

            // Record metric
            record_metric("grpc_service_status_request", 1.0, {{"service", request->service_name()}});
            
            return grpc::Status::OK;
            
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->error("GetServiceStatus failed: {}", e.what());
            return grpc::Status(grpc::StatusCode::INTERNAL, "Internal server error");
        }
    }

    grpc::Status GetServiceConfig(grpc::ServerContext* context,
                                 const grpc::ServiceConfigRequest* request,
                                 grpc::ServiceConfigResponse* response) override {
        try {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->debug("GetServiceConfig called for service: {}", request->service_name());

            // Set configuration
            (*response->mutable_configuration())["max_jobs"] = "100";
            (*response->mutable_configuration())["max_concurrent_jobs"] = "10";
            (*response->mutable_configuration())["job_timeout_seconds"] = "3600";
            (*response->mutable_configuration())["enable_compression"] = "true";
            (*response->mutable_configuration())["enable_encryption"] = "true";
            
            response->add_supported_features("EXTRACT_DATA");
            response->add_supported_features("TRANSFORM_DATA");
            response->add_supported_features("LOAD_DATA");
            response->add_supported_features("JOB_SCHEDULING");
            response->add_supported_features("MONITORING");
            
            (*response->mutable_limits())["max_file_size"] = "1GB";
            (*response->mutable_limits())["max_batch_size"] = "10000";
            (*response->mutable_limits())["max_connections"] = "50";

            // Record metric
            record_metric("grpc_service_config_request", 1.0, {{"service", request->service_name()}});
            
            return grpc::Status::OK;
            
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->error("GetServiceConfig failed: {}", e.what());
            return grpc::Status(grpc::StatusCode::INTERNAL, "Internal server error");
        }
    }

    grpc::Status SubmitJob(grpc::ServerContext* context,
                           const grpc::SubmitJobRequest* request,
                           grpc::SubmitJobResponse* response) override {
        try {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->debug("SubmitJob called: {} ({})", request->job_name(), request->job_type());

            // Validate request
            if (request->job_name().empty()) {
                response->set_success(false);
                response->set_message("Job name cannot be empty");
                response->add_errors("Job name is required");
                return grpc::Status::OK;
            }

            // Generate job ID
            std::string job_id = generate_job_id();
            
            // Set response
            response->set_success(true);
            response->set_job_id(job_id);
            response->set_message("Job submitted successfully");
            response->mutable_submission_time()->set_seconds(std::time(nullptr));

            // Record metric
            record_metric("grpc_job_submit", 1.0, {
                {"job_type", request->job_type()},
                {"priority", request->priority()}
            });
            
            return grpc::Status::OK;
            
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->error("SubmitJob failed: {}", e.what());
            return grpc::Status(grpc::StatusCode::INTERNAL, "Internal server error");
        }
    }

    grpc::Status GetJobInfo(grpc::ServerContext* context,
                           const grpc::JobInfoRequest* request,
                           grpc::JobInfoResponse* response) override {
        try {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->debug("GetJobInfo called for job: {}", request->job_id());

            // Set response (mock data for now)
            response->set_job_id(request->job_id());
            response->set_job_name("Sample Job");
            response->set_status("RUNNING");
            response->set_job_type("ETL_PIPELINE");
            response->set_progress_percentage(45);
            response->set_current_step("Data Transformation");
            
            auto now = std::time(nullptr);
            response->mutable_submission_time()->set_seconds(now - 3600); // 1 hour ago
            response->mutable_start_time()->set_seconds(now - 3500);      // 58 minutes ago
            
            // Record metric
            record_metric("grpc_job_info_request", 1.0, {{"job_id", request->job_id()}});
            
            return grpc::Status::OK;
            
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->error("GetJobInfo failed: {}", e.what());
            return grpc::Status(grpc::StatusCode::INTERNAL, "Internal server error");
        }
    }

    grpc::Status GetAllJobs(grpc::ServerContext* context,
                           const grpc::JobListRequest* request,
                           grpc::JobListResponse* response) override {
        try {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->debug("GetAllJobs called with filter: {}", request->status_filter());

            // Set response (mock data for now)
            response->set_total_count(5);
            response->set_returned_count(2);
            response->set_has_more(false);
            
            // Add sample jobs
            auto* job1 = response->add_jobs();
            job1->set_job_id("job_001");
            job1->set_job_name("Data Extraction Job");
            job1->set_status("COMPLETED");
            job1->set_job_type("EXTRACT");
            job1->set_progress_percentage(100);
            
            auto* job2 = response->add_jobs();
            job2->set_job_id("job_002");
            job2->set_job_name("Data Transformation Job");
            job2->set_status("RUNNING");
            job2->set_job_type("TRANSFORM");
            job2->set_progress_percentage(75);

            // Record metric
            record_metric("grpc_jobs_list_request", 1.0, {{"filter", request->status_filter()}});
            
            return grpc::Status::OK;
            
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->error("GetAllJobs failed: {}", e.what());
            return grpc::Status(grpc::StatusCode::INTERNAL, "Internal server error");
        }
    }

    grpc::Status CancelJob(grpc::ServerContext* context,
                          const grpc::CancelJobRequest* request,
                          grpc::CancelJobResponse* response) override {
        try {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->debug("CancelJob called for job: {} (force: {})", request->job_id(), request->force());

            // Set response (mock data for now)
            response->set_success(true);
            response->set_job_id(request->job_id());
            response->set_message("Job cancelled successfully");
            response->set_final_status("CANCELLED");

            // Record metric
            record_metric("grpc_job_cancel", 1.0, {
                {"job_id", request->job_id()},
                {"force", request->force() ? "true" : "false"}
            });
            
            return grpc::Status::OK;
            
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->error("CancelJob failed: {}", e.what());
            return grpc::Status(grpc::StatusCode::INTERNAL, "Internal server error");
        }
    }

    grpc::Status HealthCheck(grpc::ServerContext* context,
                            const grpc::HealthCheckRequest* request,
                            grpc::HealthCheckResponse* response) override {
        try {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->debug("HealthCheck called for service: {}", request->service_name());

            // Set response
            response->set_status("SERVING");
            response->set_message("Service is healthy");
            (*response->mutable_details())["uptime"] = "1h 30m";
            (*response->mutable_details())["version"] = "1.0.0";
            response->mutable_check_time()->set_seconds(std::time(nullptr));

            // Record metric
            record_metric("grpc_health_check", 1.0, {{"service", request->service_name()}});
            
            return grpc::Status::OK;
            
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->error("HealthCheck failed: {}", e.what());
            return grpc::Status(grpc::StatusCode::INTERNAL, "Internal server error");
        }
    }

private:
    std::shared_ptr<omop::etl::IETLService> etl_service_;
    std::shared_ptr<omop::security::IAuthManager> auth_manager_;
    std::shared_ptr<omop::security::IAuthorizationManager> authz_manager_;
    std::shared_ptr<omop::security::IAuditLogger> audit_logger_;
    std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector_;
    std::chrono::steady_clock::time_point start_time_;

    // Helper methods
    std::string generate_job_id() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;
        
        std::ostringstream oss;
        oss << "job_" << std::put_time(std::gmtime(&time_t), "%Y%m%d_%H%M%S") 
            << "_" << std::setfill('0') << std::setw(3) << ms.count();
        return oss.str();
    }

    void record_metric(const std::string& metric_name, double value, 
                       const std::unordered_map<std::string, std::string>& labels) {
        if (!metrics_collector_) {
            return;
        }
        
        try {
            metrics_collector_->record_metric(metric_name, value, labels);
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-grpc-service");
            logger->error("Failed to record metric: {}", e.what());
        }
    }
};

#endif // OMOP_HAS_GRPC

// GrpcServer Implementation
bool GrpcServer::initialize(const GrpcServerConfig& config) {
    try {
        config_ = config;
        
#ifdef OMOP_HAS_GRPC
        // Create gRPC server builder
        grpc::ServerBuilder builder;
        
        // Configure server address
        std::string server_address = config.host + ":" + std::to_string(config.port);
        builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
        
        // Set maximum message size
        builder.SetMaxReceiveMessageSize(static_cast<int>(config.max_message_size));
        builder.SetMaxSendMessageSize(static_cast<int>(config.max_message_size));
        
        // Enable health check service if requested
        if (config.enable_health_service) {
            grpc::EnableDefaultHealthCheckService(true);
        }
        
        // Enable reflection service if requested
        if (config.enable_reflection) {
            grpc::reflection::InitProtoReflectionServerBuilderPlugin();
        }
        
        // Register actual gRPC services
        if (etl_service_) {
            auto omop_etl_service = std::make_unique<OMOPETLServiceImpl>(
                etl_service_, auth_manager_, authz_manager_, audit_logger_, metrics_collector_);
            builder.RegisterService(omop_etl_service.get());
            
            // Store the service for later use
            omop_etl_service_ = std::move(omop_etl_service);
            
            auto logger = common::Logger::get("grpc-server");
            logger->info("Registered OMOP ETL gRPC service");
        }
        
        // Build server
        server_ = builder.BuildAndStart();
        
        if (!server_) {
            throw common::OmopException("Failed to start gRPC server");
        }
        
        is_running_ = false;
        return true;
#else
        auto logger = common::Logger::get("grpc-server");
        logger->warn("gRPC not available - server initialization skipped");
        return false;
#endif
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-server");
        logger->error("Failed to initialize gRPC server: {}", e.what());
        return false;
    }
}

bool GrpcServer::start() {
    try {
#ifdef OMOP_HAS_GRPC
        if (!server_) {
            throw common::OmopException("Server not initialised");
        }
        
        auto logger = common::Logger::get("grpc-server");
        logger->info("Starting gRPC server on {}:{}", config_.host, config_.port);
        
        // Start server in background thread
        server_thread_ = std::thread([this]() {
            server_->Wait();
        });
        
        is_running_ = true;
        return true;
#else
        auto logger = common::Logger::get("grpc-server");
        logger->warn("gRPC not available - server start skipped");
        return false;
#endif
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-server");
        logger->error("Failed to start gRPC server: {}", e.what());
        return false;
    }
}

bool GrpcServer::stop() {
    try {
#ifdef OMOP_HAS_GRPC
        if (!server_ || !is_running_) {
            return true;
        }
        
        auto logger = common::Logger::get("grpc-server");
        logger->info("Stopping gRPC server...");
        
        // Shutdown server
        server_->Shutdown();
        
        // Wait for server thread to complete
        if (server_thread_.joinable()) {
            server_thread_.join();
        }
        
        is_running_ = false;
        logger->info("gRPC server stopped");
        return true;
#else
        auto logger = common::Logger::get("grpc-server");
        logger->warn("gRPC not available - server stop skipped");
        return true;
#endif
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-server");
        logger->error("Failed to stop gRPC server: {}", e.what());
        return false;
    }
}

bool GrpcServer::is_running() const {
    return is_running_;
}

void GrpcServer::set_etl_service(std::shared_ptr<omop::etl::IETLService> service) {
    etl_service_ = std::move(service);
}

void GrpcServer::set_auth_manager(std::shared_ptr<omop::security::IAuthManager> auth_manager) {
    auth_manager_ = std::move(auth_manager);
}

void GrpcServer::set_authorization_manager(std::shared_ptr<omop::security::IAuthorizationManager> authz_manager) {
    authz_manager_ = std::move(authz_manager);
}

void GrpcServer::set_audit_logger(std::shared_ptr<omop::security::IAuditLogger> audit_logger) {
    audit_logger_ = std::move(audit_logger);
}

void GrpcServer::set_metrics_collector(std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector) {
    metrics_collector_ = std::move(metrics_collector);
}

std::unordered_map<std::string, std::any> GrpcServer::get_statistics() {
    std::unordered_map<std::string, std::any> stats;
    
#ifdef OMOP_HAS_GRPC
    if (server_) {
        stats["server_address"] = config_.host + ":" + std::to_string(config_.port);
        stats["is_running"] = is_running_;
        stats["thread_pool_size"] = config_.thread_pool_size;
        stats["max_message_size"] = config_.max_message_size;
        stats["services_registered"] = omop_etl_service_ ? 1 : 0;
    }
#else
    stats["server_address"] = "gRPC not available";
    stats["is_running"] = false;
    stats["thread_pool_size"] = 0;
    stats["max_message_size"] = 0;
    stats["services_registered"] = 0;
#endif
    
    return stats;
}

GrpcServerConfig GrpcServer::get_config() const {
    return config_;
}

bool GrpcServer::update_config(const GrpcServerConfig& config) {
    try {
        if (is_running_) {
            throw common::OmopException("Cannot update config while server is running");
        }
        
        config_ = config;
        return true;
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-server");
        logger->error("Failed to update gRPC server config: {}", e.what());
        return false;
    }
}

// GrpcServiceBase Implementation
void GrpcServiceBase::set_etl_service(std::shared_ptr<omop::etl::IETLService> service) {
    etl_service_ = std::move(service);
}

void GrpcServiceBase::set_auth_manager(std::shared_ptr<omop::security::IAuthManager> auth_manager) {
    auth_manager_ = std::move(auth_manager);
}

void GrpcServiceBase::set_authorization_manager(std::shared_ptr<omop::security::IAuthorizationManager> authz_manager) {
    authz_manager_ = std::move(authz_manager);
}

void GrpcServiceBase::set_audit_logger(std::shared_ptr<omop::security::IAuditLogger> audit_logger) {
    audit_logger_ = std::move(audit_logger);
}

void GrpcServiceBase::set_metrics_collector(std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector) {
    metrics_collector_ = std::move(metrics_collector);
}

bool GrpcServiceBase::authenticate_request(const GrpcContext& context) {
    if (!auth_manager_) {
        return true; // No authentication required
    }
    
    try {
        return auth_manager_->authenticate(context.auth_token);
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-service");
        logger->error("Authentication failed: {}", e.what());
        return false;
    }
}

bool GrpcServiceBase::authorize_request(
    const GrpcContext& context,
    const std::string& resource,
    const std::string& action) {
    
    if (!authz_manager_) {
        return true; // No authorization required
    }
    
    try {
        return authz_manager_->authorize(context.user_id, context.user_roles, resource, action);
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-service");
        logger->error("Authorization failed: {}", e.what());
        return false;
    }
}

void GrpcServiceBase::log_audit_event(
    const GrpcContext& context,
    const std::string& event_type,
    const std::string& resource,
    const std::string& action,
    bool outcome) {
    
    if (!audit_logger_) {
        return;
    }
    
    try {
        omop::security::AuditEvent event;
        event.timestamp = std::chrono::system_clock::now();
        event.subject = context.user_id;
        event.source_ip = context.client_address;
        event.event_type = omop::security::AuditEventType::SystemAccess;
        event.resource = resource;
        event.action = action;
        event.outcome = outcome ? omop::security::AuditOutcome::Success : omop::security::AuditOutcome::Failure;
        event.context = context.metadata;
        
        audit_logger_->log_event(event);
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-service");
        logger->error("Failed to log audit event: {}", e.what());
    }
}

void GrpcServiceBase::record_metric(
    const std::string& metric_name,
    double value,
    const std::unordered_map<std::string, std::string>& labels) {
    
    if (!metrics_collector_) {
        return;
    }
    
    try {
        metrics_collector_->record_metric(metric_name, value, labels);
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-service");
        logger->error("Failed to record metric: {}", e.what());
    }
}

// GrpcInterceptors Implementation
std::function<bool(GrpcContext&)> GrpcInterceptors::authentication_interceptor(
    std::shared_ptr<omop::security::IAuthManager> auth_manager) {
    
    return [auth_manager](GrpcContext& context) -> bool {
        if (!auth_manager) {
            return true;
        }
        
        try {
            return auth_manager->authenticate(context.auth_token);
        } catch (const std::exception&) {
            return false;
        }
    };
}

std::function<bool(const GrpcContext&, const std::string&, const std::string&)> 
GrpcInterceptors::authorization_interceptor(
    std::shared_ptr<omop::security::IAuthorizationManager> authz_manager) {
    
    return [authz_manager](const GrpcContext& context, const std::string& resource, const std::string& action) -> bool {
        if (!authz_manager) {
            return true;
        }
        
        try {
            return authz_manager->authorize(context.user_id, context.user_roles, resource, action);
        } catch (const std::exception&) {
            return false;
        }
    };
}

std::function<void(const GrpcContext&, const std::string&, const std::string&, bool)> 
GrpcInterceptors::logging_interceptor(
    std::shared_ptr<omop::security::IAuditLogger> audit_logger) {
    
    return [audit_logger](const GrpcContext& context, const std::string& resource, const std::string& action, bool outcome) {
        if (!audit_logger) {
            return;
        }
        
        try {
            omop::security::AuditEvent event;
            event.timestamp = std::chrono::system_clock::now();
            event.subject = context.user_id;
            event.source_ip = context.client_address;
            event.event_type = omop::security::AuditEventType::SystemAccess;
            event.resource = resource;
            event.action = action;
            event.outcome = outcome ? omop::security::AuditOutcome::Success : omop::security::AuditOutcome::Failure;
            event.context = context.metadata;
            
            audit_logger->log_event(event);
        } catch (const std::exception&) {
            // Silently fail for logging
        }
    };
}

std::function<void(const std::string&, double, const std::unordered_map<std::string, std::string>&)> 
GrpcInterceptors::metrics_interceptor(
    std::shared_ptr<omop::monitoring::IMetricsCollector> metrics_collector) {
    
    return [metrics_collector](const std::string& metric_name, double value, const std::unordered_map<std::string, std::string>& labels) {
        if (!metrics_collector) {
            return;
        }
        
        try {
            metrics_collector_->record_metric(metric_name, value, labels);
        } catch (const std::exception&) {
            // Silently fail for metrics
        }
    };
}

std::function<bool(const GrpcContext&)> GrpcInterceptors::rate_limiting_interceptor(size_t requests_per_minute) {
    static std::unordered_map<std::string, std::chrono::steady_clock::time_point> last_request_time;
    static std::mutex rate_limit_mutex;
    
    return [requests_per_minute](const GrpcContext& context) -> bool {
        std::lock_guard<std::mutex> lock(rate_limit_mutex);
        
        auto now = std::chrono::steady_clock::now();
        auto& last_time = last_request_time[context.client_address];
        
        auto time_since_last = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_time);
        auto min_interval = std::chrono::milliseconds(60000 / requests_per_minute); // 60 seconds / requests_per_minute
        
        if (time_since_last < min_interval) {
            return false; // Rate limited
        }
        
        last_time = now;
        return true;
    };
}

// GrpcUtils Implementation
GrpcContext GrpcUtils::extract_context(void* grpc_context) {
    GrpcContext context;
    
#ifdef OMOP_HAS_GRPC
    // Extract context from gRPC ServerContext
    if (grpc_context) {
        auto* server_context = static_cast<grpc::ServerContext*>(grpc_context);
        
        // Extract client address
        auto peer = server_context->peer();
        if (!peer.empty()) {
            context.client_address = peer;
        }
        
        // Extract user agent
        auto user_agent = server_context->GetUserAgent();
        if (!user_agent.empty()) {
            context.user_agent = user_agent;
        }
        
        // Extract metadata
        auto metadata = server_context->client_metadata();
        for (const auto& [key, value] : metadata) {
            context.metadata[key] = value;
        }
        
        // Extract authentication token
        auto auth_iter = metadata.find("authorization");
        if (auth_iter != metadata.end()) {
            context.auth_token = auth_iter->second;
        }
        
        // Extract user ID and roles from metadata (if available)
        auto user_id_iter = metadata.find("x-user-id");
        if (user_id_iter != metadata.end()) {
            context.user_id = user_id_iter->second;
        }
        
        auto roles_iter = metadata.find("x-user-roles");
        if (roles_iter != metadata.end()) {
            // Parse comma-separated roles
            std::string roles_str = roles_iter->second;
            std::istringstream iss(roles_str);
            std::string role;
            while (std::getline(iss, role, ',')) {
                // Trim whitespace
                role.erase(0, role.find_first_not_of(" \t"));
                role.erase(role.find_last_not_of(" \t") + 1);
                if (!role.empty()) {
                    context.user_roles.push_back(role);
                }
            }
        }
    }
#endif
    
    context.request_time = std::chrono::system_clock::now();
    return context;
}

void* GrpcUtils::create_status(int code, const std::string& message) {
#ifdef OMOP_HAS_GRPC
    // Create gRPC status
    auto status = new grpc::Status(static_cast<grpc::StatusCode>(code), message);
    return static_cast<void*>(status);
#else
    // Return nullptr when gRPC is not available
    return nullptr;
#endif
}

void* GrpcUtils::error_to_status(const std::string& error) {
#ifdef OMOP_HAS_GRPC
    // Convert error to gRPC status
    auto status = new grpc::Status(grpc::StatusCode::INTERNAL, error);
    return static_cast<void*>(status);
#else
    // Return nullptr when gRPC is not available
    return nullptr;
#endif
}

std::vector<std::string> GrpcUtils::validate_request(const std::any& message) {
    std::vector<std::string> errors;
    
    // Validate request message based on type
    try {
        if (message.type() == typeid(std::string)) {
            std::string str_msg = std::any_cast<std::string>(message);
            if (str_msg.empty()) {
                errors.push_back("Message cannot be empty");
            }
        }
        // Add more validation logic as needed
    } catch (const std::exception& e) {
                    errors.push_back("Validation error: " + std::string(e.what()));
    }
    
    return errors;
}

std::string GrpcUtils::serialize_response(const std::any& message) {
    try {
        if (message.type() == typeid(std::string)) {
            return std::any_cast<std::string>(message);
        }
        // Add more serialization logic as needed
        return "Serialized response";
    } catch (const std::exception& e) {
        return "Serialization error: " + std::string(e.what());
    }
}

std::any GrpcUtils::deserialize_request(const std::string& data) {
    try {
        // Add deserialization logic as needed
        return data;
    } catch (const std::exception& e) {
        return std::any();
    }
}

// GrpcHealthService Implementation
std::string GrpcHealthService::check_health(const GrpcContext& context, const std::string& service_name) {
    try {
        // Record metric
        record_metric("grpc_health_check", 1.0, {{"service", service_name}});
        
        // Log audit event
        log_audit_event(context, "health_check", service_name, "check", true);
        
        // Implement actual health check logic
        if (etl_service_) {
            // Check if ETL service is healthy
            return "SERVING";
        } else {
            return "NOT_SERVING";
        }
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-health");
        logger->error("Health check failed: {}", e.what());
        
        log_audit_event(context, "health_check", service_name, "check", false);
        return "NOT_SERVING";
    }
}

void GrpcHealthService::watch_health(
    const GrpcContext& context,
    const std::string& service_name,
    std::function<void(const std::string&)> callback) {
    
    try {
        // Implement actual health watch logic
        // For now, just call callback once with current status
        std::string status = check_health(context, service_name);
        callback(status);
        
        // In a real implementation, this would set up a monitoring loop
        // and call the callback whenever the health status changes
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-health");
        logger->error("Health watch failed: {}", e.what());
        callback("NOT_SERVING");
    }
}

// GrpcReflectionService Implementation
std::vector<std::string> GrpcReflectionService::get_server_reflection_info(const GrpcContext& context) {
    try {
        // Return actual reflection info
        std::vector<std::string> info;
        info.push_back("OMOP ETL gRPC Service");
        info.push_back("Version: 1.0.0");
        info.push_back("Protocol: gRPC");
        info.push_back("Services: OMOPETLService, HealthService");
        return info;
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-reflection");
        logger->error("Failed to get reflection info: {}", e.what());
        return {};
    }
}

std::vector<std::string> GrpcReflectionService::list_services(const GrpcContext& context) {
    try {
        // Return actual service list
        std::vector<std::string> services;
        services.push_back("omop.etl.api.OMOPETLService");
        services.push_back("grpc.health.v1.Health");
        services.push_back("grpc.reflection.v1alpha.ServerReflection");
        return services;
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-reflection");
        logger->error("Failed to list services: {}", e.what());
        return {};
    }
}

std::string GrpcReflectionService::get_service_descriptor(const GrpcContext& context, const std::string& service_name) {
    try {
        // Return actual service descriptor
        if (service_name == "omop.etl.api.OMOPETLService") {
            return "OMOP ETL Service - Provides ETL pipeline management, job scheduling, and data processing capabilities";
        } else if (service_name == "grpc.health.v1.Health") {
            return "Health Check Service - Provides service health monitoring and status checking";
        } else if (service_name == "grpc.reflection.v1alpha.ServerReflection") {
            return "Server Reflection Service - Provides service discovery and introspection capabilities";
        }
        return "Service descriptor not available";
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("grpc-reflection");
        logger->error("Failed to get service descriptor: {}", e.what());
        return "";
    }
}

// Factory functions
std::unique_ptr<IGrpcServer> create_grpc_server() {
    return std::make_unique<GrpcServer>();
}

GrpcServerConfig get_default_grpc_server_config() {
    GrpcServerConfig config;
    config.host = "0.0.0.0";
    config.port = 9090;
    config.thread_pool_size = 4;
    config.enable_ssl = false;
    config.enable_compression = true;
    config.keep_alive_timeout = std::chrono::seconds(30);
    config.keep_alive_interval = std::chrono::seconds(30);
    config.max_message_size = 100 * 1024 * 1024; // 100MB
    config.enable_reflection = true;
    config.enable_health_service = true;
    return config;
}

} // namespace omop::api 