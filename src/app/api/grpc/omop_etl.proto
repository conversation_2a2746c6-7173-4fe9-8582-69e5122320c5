syntax = "proto3";

package omop.etl.api;

import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";

// Service status and configuration
message ServiceStatusRequest {
  string service_name = 1;
}

message ServiceStatusResponse {
  string status = 1;
  bool is_healthy = 2;
  string version = 3;
  int64 uptime_seconds = 4;
  repeated string active_features = 5;
  map<string, string> metadata = 6;
}

message ServiceConfigRequest {
  string service_name = 1;
}

message ServiceConfigResponse {
  map<string, string> configuration = 1;
  repeated string supported_features = 2;
  map<string, string> limits = 3;
}

message UpdateServiceConfigRequest {
  string service_name = 1;
  map<string, string> configuration = 2;
}

message UpdateServiceConfigResponse {
  bool success = 1;
  string message = 2;
  repeated string errors = 3;
}

// Job management
message SubmitJobRequest {
  string job_name = 1;
  string job_type = 2;
  map<string, string> parameters = 3;
  string priority = 4;
  google.protobuf.Timestamp scheduled_time = 5;
  map<string, string> metadata = 6;
}

message SubmitJobResponse {
  bool success = 1;
  string job_id = 2;
  string message = 3;
  repeated string errors = 4;
  google.protobuf.Timestamp submission_time = 5;
}

message JobInfoRequest {
  string job_id = 1;
}

message JobInfoResponse {
  string job_id = 1;
  string job_name = 2;
  string status = 3;
  string job_type = 4;
  map<string, string> parameters = 5;
  google.protobuf.Timestamp submission_time = 6;
  google.protobuf.Timestamp start_time = 7;
  google.protobuf.Timestamp completion_time = 8;
  google.protobuf.Duration duration = 9;
  int32 progress_percentage = 10;
  string current_step = 11;
  map<string, string> metadata = 12;
  repeated string errors = 13;
  map<string, string> metrics = 14;
}

message JobListRequest {
  string status_filter = 1;
  string job_type_filter = 2;
  google.protobuf.Timestamp from_time = 3;
  google.protobuf.Timestamp to_time = 4;
  int32 limit = 5;
  int32 offset = 6;
}

message JobListResponse {
  repeated JobInfoResponse jobs = 1;
  int32 total_count = 2;
  int32 returned_count = 3;
  bool has_more = 4;
}

message CancelJobRequest {
  string job_id = 1;
  string reason = 2;
  bool force = 3;
}

message CancelJobResponse {
  bool success = 1;
  string message = 2;
  string job_id = 3;
  string final_status = 4;
}

message WaitForJobRequest {
  string job_id = 1;
  google.protobuf.Duration timeout = 2;
  bool wait_for_completion = 3;
}

message WaitForJobResponse {
  bool success = 1;
  string job_id = 2;
  string final_status = 3;
  string message = 4;
  google.protobuf.Duration wait_duration = 5;
}

// Authentication and authorization
message AuthenticateRequest {
  string username = 1;
  string password = 2;
  string auth_method = 3;
  map<string, string> additional_data = 4;
}

message AuthenticateResponse {
  bool success = 1;
  string auth_token = 2;
  string refresh_token = 3;
  string user_id = 4;
  repeated string user_roles = 5;
  google.protobuf.Timestamp token_expiry = 6;
  string message = 7;
}

// Health checking and monitoring
message HealthCheckRequest {
  string service_name = 1;
  bool detailed = 2;
}

message HealthCheckResponse {
  string status = 1;
  string message = 2;
  map<string, string> details = 3;
  google.protobuf.Timestamp check_time = 4;
  repeated string warnings = 5;
}

// Metrics and monitoring
message MetricsRequest {
  string metric_name = 1;
  google.protobuf.Timestamp from_time = 2;
  google.protobuf.Timestamp to_time = 3;
  string aggregation = 4;
  map<string, string> filters = 5;
}

message MetricsResponse {
  bool success = 1;
  string metric_name = 2;
  repeated MetricDataPoint data_points = 3;
  map<string, string> metadata = 4;
  string aggregation = 5;
  repeated string errors = 6;
}

message MetricDataPoint {
  google.protobuf.Timestamp timestamp = 1;
  double value = 2;
  map<string, string> labels = 3;
}

// Pipeline operations
message PipelineRequest {
  string pipeline_name = 1;
  string operation = 2;
  map<string, string> parameters = 3;
  map<string, string> metadata = 4;
}

message PipelineResponse {
  bool success = 1;
  string pipeline_id = 2;
  string status = 3;
  string message = 4;
  repeated string errors = 5;
  map<string, string> results = 6;
}

// Data extraction operations
message ExtractRequest {
  string source_name = 1;
  string source_type = 2;
  map<string, string> parameters = 3;
  map<string, string> filters = 4;
  int32 batch_size = 5;
}

message ExtractResponse {
  bool success = 1;
  string extraction_id = 2;
  int64 records_extracted = 3;
  string status = 4;
  string message = 5;
  repeated string errors = 6;
  map<string, string> metadata = 7;
}

// Data transformation operations
message TransformRequest {
  string transformation_name = 1;
  string input_data_id = 2;
  map<string, string> parameters = 3;
  repeated string rules = 4;
  map<string, string> metadata = 5;
}

message TransformResponse {
  bool success = 1;
  string transformation_id = 2;
  int64 records_transformed = 3;
  string status = 4;
  string message = 5;
  repeated string errors = 6;
  map<string, string> results = 7;
}

// Data loading operations
message LoadRequest {
  string target_name = 1;
  string target_type = 2;
  string data_id = 3;
  map<string, string> parameters = 4;
  map<string, string> metadata = 5;
}

message LoadResponse {
  bool success = 1;
  string load_id = 2;
  int64 records_loaded = 3;
  string status = 4;
  string message = 5;
  repeated string errors = 6;
  map<string, string> results = 7;
}

// Main ETL service
service OMOPETLService {
  // Service management
  rpc GetServiceStatus(ServiceStatusRequest) returns (ServiceStatusResponse);
  rpc GetServiceConfig(ServiceConfigRequest) returns (ServiceConfigResponse);
  rpc UpdateServiceConfig(UpdateServiceConfigRequest) returns (UpdateServiceConfigResponse);
  
  // Job management
  rpc SubmitJob(SubmitJobRequest) returns (SubmitJobResponse);
  rpc GetJobInfo(JobInfoRequest) returns (JobInfoResponse);
  rpc GetAllJobs(JobListRequest) returns (JobListResponse);
  rpc GetRunningJobs(JobListRequest) returns (JobListResponse);
  rpc CancelJob(CancelJobRequest) returns (CancelJobResponse);
  rpc WaitForJob(WaitForJobRequest) returns (WaitForJobResponse);
  
  // Authentication
  rpc Authenticate(AuthenticateRequest) returns (AuthenticateResponse);
  
  // Health and monitoring
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
  rpc GetMetrics(MetricsRequest) returns (MetricsResponse);
  
  // Pipeline operations
  rpc ExecutePipeline(PipelineRequest) returns (PipelineResponse);
  rpc GetPipelineStatus(PipelineRequest) returns (PipelineResponse);
  
  // ETL operations
  rpc ExtractData(ExtractRequest) returns (ExtractResponse);
  rpc TransformData(TransformRequest) returns (TransformResponse);
  rpc LoadData(LoadRequest) returns (LoadResponse);
  
  // Streaming operations
  rpc StreamJobProgress(JobInfoRequest) returns (stream JobInfoResponse);
  rpc StreamMetrics(MetricsRequest) returns (stream MetricsResponse);
}

// Health service
service HealthService {
  rpc Check(HealthCheckRequest) returns (HealthCheckResponse);
  rpc Watch(HealthCheckRequest) returns (stream HealthCheckResponse);
}

// Reflection service
service ServerReflection {
  rpc ServerReflectionInfo(stream google.protobuf.Any) returns (stream google.protobuf.Any);
}
