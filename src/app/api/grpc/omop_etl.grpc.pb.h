#pragma once

// Generated protobuf headers for OMOP ETL gRPC service
// This file would normally be generated by protoc from .proto files

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>

namespace omop {
namespace api {
namespace grpc {

// Forward declarations for gRPC generated classes
class ServiceStatusRequest;
class ServiceStatusResponse;
class ServiceConfigRequest;
class ServiceConfigResponse;
class UpdateServiceConfigRequest;
class UpdateServiceConfigResponse;
class SubmitJobRequest;
class SubmitJobResponse;
class JobInfoRequest;
class JobInfoResponse;
class JobListRequest;
class JobListResponse;
class CancelJobRequest;
class CancelJobResponse;
class WaitForJobRequest;
class WaitForJobResponse;
class AuthenticateRequest;
class AuthenticateResponse;
class HealthCheckRequest;
class HealthCheckResponse;
class MetricsRequest;
class MetricsResponse;

/**
 * @brief Service status request message
 */
class ServiceStatusRequest {
public:
    ServiceStatusRequest() = default;
    ~ServiceStatusRequest() = default;

    // Serialization methods (would be generated by protoc)
    std::string SerializeAsString() const;
    bool ParseFromString(const std::string& data);
    void Clear();
    bool IsInitialised() const;
};

/**
 * @brief Service status response message
 */
class ServiceStatusResponse {
public:
    ServiceStatusResponse() = default;
    ~ServiceStatusResponse() = default;

    // Field accessors
    const std::string& status() const { return status_; }
    void set_status(const std::string& value) { status_ = value; }

    bool is_healthy() const { return is_healthy_; }
    void set_is_healthy(bool value) { is_healthy_ = value; }

    const std::string& version() const { return version_; }
    void set_version(const std::string& value) { version_ = value; }

    int64_t uptime_seconds() const { return uptime_seconds_; }
    void set_uptime_seconds(int64_t value) { uptime_seconds_ = value; }

    // Serialization methods
    std::string SerializeAsString() const;
    bool ParseFromString(const std::string& data);
    void Clear();
    bool IsInitialised() const;

private:
    std::string status_;
    bool is_healthy_{false};
    std::string version_;
    int64_t uptime_seconds_{0};
};

/**
 * @brief Submit job request message
 */
class SubmitJobRequest {
public:
    SubmitJobRequest() = default;
    ~SubmitJobRequest() = default;

    // Field accessors
    const std::string& job_name() const { return job_name_; }
    void set_job_name(const std::string& value) { job_name_ = value; }

    const std::string& pipeline_config() const { return pipeline_config_; }
    void set_pipeline_config(const std::string& value) { pipeline_config_ = value; }

    const std::string& extractor_type() const { return extractor_type_; }
    void set_extractor_type(const std::string& value) { extractor_type_ = value; }

    const std::string& loader_type() const { return loader_type_; }
    void set_loader_type(const std::string& value) { loader_type_ = value; }

    // Serialization methods
    std::string SerializeAsString() const;
    bool ParseFromString(const std::string& data);
    void Clear();
    bool IsInitialised() const;

private:
    std::string job_name_;
    std::string pipeline_config_;
    std::string extractor_type_;
    std::string loader_type_;
};

/**
 * @brief Submit job response message
 */
class SubmitJobResponse {
public:
    SubmitJobResponse() = default;
    ~SubmitJobResponse() = default;

    // Field accessors
    const std::string& job_id() const { return job_id_; }
    void set_job_id(const std::string& value) { job_id_ = value; }

    bool success() const { return success_; }
    void set_success(bool value) { success_ = value; }

    const std::string& error_message() const { return error_message_; }
    void set_error_message(const std::string& value) { error_message_ = value; }

    // Serialization methods
    std::string SerializeAsString() const;
    bool ParseFromString(const std::string& data);
    void Clear();
    bool IsInitialised() const;

private:
    std::string job_id_;
    bool success_{false};
    std::string error_message_;
};

/**
 * @brief Job info request message
 */
class JobInfoRequest {
public:
    JobInfoRequest() = default;
    ~JobInfoRequest() = default;

    // Field accessors
    const std::string& job_id() const { return job_id_; }
    void set_job_id(const std::string& value) { job_id_ = value; }

    // Serialization methods
    std::string SerializeAsString() const;
    bool ParseFromString(const std::string& data);
    void Clear();
    bool IsInitialised() const;

private:
    std::string job_id_;
};

/**
 * @brief Job info response message
 */
class JobInfoResponse {
public:
    JobInfoResponse() = default;
    ~JobInfoResponse() = default;

    // Field accessors
    const std::string& job_id() const { return job_id_; }
    void set_job_id(const std::string& value) { job_id_ = value; }

    const std::string& job_name() const { return job_name_; }
    void set_job_name(const std::string& value) { job_name_ = value; }

    const std::string& status() const { return status_; }
    void set_status(const std::string& value) { status_ = value; }

    int64_t processed_records() const { return processed_records_; }
    void set_processed_records(int64_t value) { processed_records_ = value; }

    int64_t failed_records() const { return failed_records_; }
    void set_failed_records(int64_t value) { failed_records_ = value; }

    int64_t created_at() const { return created_at_; }
    void set_created_at(int64_t value) { created_at_ = value; }

    int64_t completed_at() const { return completed_at_; }
    void set_completed_at(int64_t value) { completed_at_ = value; }

    const std::string& error_message() const { return error_message_; }
    void set_error_message(const std::string& value) { error_message_ = value; }

    // Serialization methods
    std::string SerializeAsString() const;
    bool ParseFromString(const std::string& data);
    void Clear();
    bool IsInitialised() const;

private:
    std::string job_id_;
    std::string job_name_;
    std::string status_;
    int64_t processed_records_{0};
    int64_t failed_records_{0};
    int64_t created_at_{0};
    int64_t completed_at_{0};
    std::string error_message_;
};

/**
 * @brief Authentication request message
 */
class AuthenticateRequest {
public:
    AuthenticateRequest() = default;
    ~AuthenticateRequest() = default;

    // Field accessors
    const std::string& username() const { return username_; }
    void set_username(const std::string& value) { username_ = value; }

    const std::string& password() const { return password_; }
    void set_password(const std::string& value) { password_ = value; }

    const std::string& token() const { return token_; }
    void set_token(const std::string& value) { token_ = value; }

    const std::string& auth_method() const { return auth_method_; }
    void set_auth_method(const std::string& value) { auth_method_ = value; }

    // Serialization methods
    std::string SerializeAsString() const;
    bool ParseFromString(const std::string& data);
    void Clear();
    bool IsInitialised() const;

private:
    std::string username_;
    std::string password_;
    std::string token_;
    std::string auth_method_;
};

/**
 * @brief Authentication response message
 */
class AuthenticateResponse {
public:
    AuthenticateResponse() = default;
    ~AuthenticateResponse() = default;

    // Field accessors
    bool success() const { return success_; }
    void set_success(bool value) { success_ = value; }

    const std::string& token() const { return token_; }
    void set_token(const std::string& value) { token_ = value; }

    const std::string& refresh_token() const { return refresh_token_; }
    void set_refresh_token(const std::string& value) { refresh_token_ = value; }

    int64_t expires_at() const { return expires_at_; }
    void set_expires_at(int64_t value) { expires_at_ = value; }

    const std::string& user_id() const { return user_id_; }
    void set_user_id(const std::string& value) { user_id_ = value; }

    const std::string& error_message() const { return error_message_; }
    void set_error_message(const std::string& value) { error_message_ = value; }

    // Serialization methods
    std::string SerializeAsString() const;
    bool ParseFromString(const std::string& data);
    void Clear();
    bool IsInitialised() const;

private:
    bool success_{false};
    std::string token_;
    std::string refresh_token_;
    int64_t expires_at_{0};
    std::string user_id_;
    std::string error_message_;
};

/**
 * @brief Health check request message
 */
class HealthCheckRequest {
public:
    HealthCheckRequest() = default;
    ~HealthCheckRequest() = default;

    // Field accessors
    const std::string& service() const { return service_; }
    void set_service(const std::string& value) { service_ = value; }

    // Serialization methods
    std::string SerializeAsString() const;
    bool ParseFromString(const std::string& data);
    void Clear();
    bool IsInitialised() const;

private:
    std::string service_;
};

/**
 * @brief Health check response message
 */
class HealthCheckResponse {
public:
    enum ServingStatus {
        UNKNOWN = 0,
        SERVING = 1,
        NOT_SERVING = 2,
        SERVICE_UNKNOWN = 3
    };

    HealthCheckResponse() = default;
    ~HealthCheckResponse() = default;

    // Field accessors
    ServingStatus status() const { return status_; }
    void set_status(ServingStatus value) { status_ = value; }

    const std::string& message() const { return message_; }
    void set_message(const std::string& value) { message_ = value; }

    // Serialization methods
    std::string SerializeAsString() const;
    bool ParseFromString(const std::string& data);
    void Clear();
    bool IsInitialised() const;

private:
    ServingStatus status_{UNKNOWN};
    std::string message_;
};

/**
 * @brief gRPC service stub interface
 * 
 * This interface would normally be generated by protoc from .proto files.
 * It provides the client-side interface for calling gRPC services.
 */
class OMOPETLService {
public:
    class Stub {
    public:
        virtual ~Stub() = default;

        // Service management methods
        virtual std::unique_ptr<ServiceStatusResponse> GetServiceStatus(
            const ServiceStatusRequest& request) = 0;

        virtual std::unique_ptr<ServiceConfigResponse> GetServiceConfig(
            const ServiceConfigRequest& request) = 0;

        virtual std::unique_ptr<UpdateServiceConfigResponse> UpdateServiceConfig(
            const UpdateServiceConfigRequest& request) = 0;

        // Job management methods
        virtual std::unique_ptr<SubmitJobResponse> SubmitJob(
            const SubmitJobRequest& request) = 0;

        virtual std::unique_ptr<JobInfoResponse> GetJobInfo(
            const JobInfoRequest& request) = 0;

        virtual std::unique_ptr<JobListResponse> GetAllJobs(
            const JobListRequest& request) = 0;

        virtual std::unique_ptr<JobListResponse> GetRunningJobs(
            const JobListRequest& request) = 0;

        virtual std::unique_ptr<CancelJobResponse> CancelJob(
            const CancelJobRequest& request) = 0;

        virtual std::unique_ptr<WaitForJobResponse> WaitForJob(
            const WaitForJobRequest& request) = 0;

        // Authentication methods
        virtual std::unique_ptr<AuthenticateResponse> Authenticate(
            const AuthenticateRequest& request) = 0;

        // Health and monitoring methods
        virtual std::unique_ptr<HealthCheckResponse> HealthCheck(
            const HealthCheckRequest& request) = 0;

        virtual std::unique_ptr<MetricsResponse> GetMetrics(
            const MetricsRequest& request) = 0;
    };

    /**
     * @brief Create service stub
     * @param channel gRPC channel
     * @return std::unique_ptr<Stub> Service stub
     */
    static std::unique_ptr<Stub> NewStub(std::shared_ptr<void> channel);
};

/**
 * @brief gRPC service implementation interface
 * 
 * This interface would normally be generated by protoc from .proto files.
 * It provides the server-side interface for implementing gRPC services.
 */
class OMOPETLServiceImpl {
public:
    virtual ~OMOPETLServiceImpl() = default;

    // Service management methods
    virtual std::unique_ptr<ServiceStatusResponse> GetServiceStatus(
        const ServiceStatusRequest& request, void* context) = 0;

    virtual std::unique_ptr<ServiceConfigResponse> GetServiceConfig(
        const ServiceConfigRequest& request, void* context) = 0;

    virtual std::unique_ptr<UpdateServiceConfigResponse> UpdateServiceConfig(
        const UpdateServiceConfigRequest& request, void* context) = 0;

    // Job management methods
    virtual std::unique_ptr<SubmitJobResponse> SubmitJob(
        const SubmitJobRequest& request, void* context) = 0;

    virtual std::unique_ptr<JobInfoResponse> GetJobInfo(
        const JobInfoRequest& request, void* context) = 0;

    virtual std::unique_ptr<JobListResponse> GetAllJobs(
        const JobListRequest& request, void* context) = 0;

    virtual std::unique_ptr<JobListResponse> GetRunningJobs(
        const JobListRequest& request, void* context) = 0;

    virtual std::unique_ptr<CancelJobResponse> CancelJob(
        const CancelJobRequest& request, void* context) = 0;

    virtual std::unique_ptr<WaitForJobResponse> WaitForJob(
        const WaitForJobRequest& request, void* context) = 0;

    // Authentication methods
    virtual std::unique_ptr<AuthenticateResponse> Authenticate(
        const AuthenticateRequest& request, void* context) = 0;

    // Health and monitoring methods
    virtual std::unique_ptr<HealthCheckResponse> HealthCheck(
        const HealthCheckRequest& request, void* context) = 0;

    virtual std::unique_ptr<MetricsResponse> GetMetrics(
        const MetricsRequest& request, void* context) = 0;
};

// Additional message classes (simplified for brevity)
class ServiceConfigRequest { /* ... */ };
class ServiceConfigResponse { /* ... */ };
class UpdateServiceConfigRequest { /* ... */ };
class UpdateServiceConfigResponse { /* ... */ };
class JobListRequest { /* ... */ };
class JobListResponse { /* ... */ };
class CancelJobRequest { /* ... */ };
class CancelJobResponse { /* ... */ };
class WaitForJobRequest { /* ... */ };
class WaitForJobResponse { /* ... */ };
class MetricsRequest { /* ... */ };
class MetricsResponse { /* ... */ };

} // namespace grpc
} // namespace api
} // namespace omop