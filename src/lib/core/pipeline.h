#pragma once

#include "interfaces.h"
#include "common/utilities.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include "common/metrics_collector.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <chrono>
#include <optional>
#include <thread>
#include <atomic>
#include <future>
#include <queue>
#include <condition_variable>
#include <nlohmann/json.hpp>

namespace omop::core {

/**
 * @brief Pipeline stage enumeration
 */
enum class PipelineStage {
    Extract,
    Transform,
    Load,
    Validate,
    Monitor,
    Cleanup,
    Custom
};

/**
 * @brief Pipeline execution mode
 */
enum class ExecutionMode {
    Sequential,
    Parallel,
    Streaming,
    Batch,
    Hybrid
};

/**
 * @brief Pipeline status enumeration
 */
enum class PipelineStatus {
    Created,
    Initializing,
    Running,
    Paused,
    Completed,
    Failed,
    Cancelled,
    Cleanup
};

/**
 * @brief ETL job status (legacy - kept for backward compatibility)
 */
enum class JobStatus {
    Created = static_cast<int>(PipelineStatus::Created),
    Initializing = static_cast<int>(PipelineStatus::Initializing),
    Running = static_cast<int>(PipelineStatus::Running),
    Paused = static_cast<int>(PipelineStatus::Paused),
    Completed = static_cast<int>(PipelineStatus::Completed),
    Failed = static_cast<int>(PipelineStatus::Failed),
    Cancelled = static_cast<int>(PipelineStatus::Cancelled)
};

/**
 * @brief Pipeline execution statistics
 */
struct PipelineExecutionStats {
    std::string pipeline_id;
    PipelineStatus status{PipelineStatus::Created};
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    std::chrono::duration<double> total_duration{std::chrono::duration<double>::zero()};
    std::unordered_map<PipelineStage, std::chrono::duration<double>> stage_durations;
    size_t total_records_processed{0};
    size_t successful_records{0};
    size_t failed_records{0};
    size_t skipped_records{0};
    std::unordered_map<PipelineStage, size_t> records_per_stage;
    double throughput_records_per_second{0.0};
    size_t memory_peak_bytes{0};
    std::vector<std::string> errors;
    std::vector<std::string> warnings;
    std::unordered_map<std::string, std::any> custom_metrics;
};

/**
 * @brief ETL job information
 */
class JobInfo {
public:
    std::string job_id;
    std::string job_name;
    PipelineStatus status{PipelineStatus::Created};
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    size_t total_records{0};
    size_t processed_records{0};
    size_t error_records{0};
    std::vector<std::string> error_messages;
    std::unordered_map<std::string, std::any> metadata;

    /**
     * @brief Get job duration
     * @return std::chrono::duration<double> Duration in seconds
     */
    [[nodiscard]] std::chrono::duration<double> duration() const {
        if (status == PipelineStatus::Running) {
            return std::chrono::system_clock::now() - start_time;
        }
        return end_time - start_time;
    }

    /**
     * @brief Get progress percentage
     * @return double Progress (0-100)
     */
    [[nodiscard]] double progress() const {
        if (total_records == 0) return 0.0;
        return (static_cast<double>(processed_records) / total_records) * 100.0;
    }

    /**
     * @brief Get error rate
     * @return double Error rate (0-1)
     */
    [[nodiscard]] double error_rate() const {
        if (processed_records == 0) return 0.0;
        return static_cast<double>(error_records) / processed_records;
    }

    /**
     * @brief Convert to PipelineExecutionStats
     * @return PipelineExecutionStats Converted statistics
     */
    [[nodiscard]] PipelineExecutionStats to_pipeline_stats() const {
        PipelineExecutionStats stats;
        stats.pipeline_id = job_id;
        stats.status = status; // No conversion needed anymore
        stats.start_time = start_time;
        stats.end_time = end_time;
        stats.total_duration = duration();
        stats.total_records_processed = total_records;
        stats.successful_records = processed_records;
        stats.failed_records = error_records;
        stats.errors = error_messages;
        stats.custom_metrics = metadata;
        return stats;
    }
};

/**
 * @brief ETL pipeline configuration (legacy - kept for backward compatibility)
 */
struct PipelineConfig {
    size_t batch_size{1000};
    size_t max_parallel_batches{4};
    size_t queue_size{10000};
    size_t commit_interval{10000};
    double error_threshold{0.01};
    bool stop_on_error{true};
    bool validate_records{true};
    bool enable_checkpointing{false};
    std::chrono::seconds checkpoint_interval{300}; // 5 minutes
    std::string checkpoint_dir;
};

/**
 * @brief Pipeline configuration
 */
struct ETLPipelineConfig {
    std::string pipeline_id;
    std::string pipeline_name;
    ExecutionMode execution_mode{ExecutionMode::Sequential};
    size_t batch_size{1000};
    size_t max_parallel_stages{4};
    std::chrono::seconds stage_timeout{300};
    std::chrono::seconds pipeline_timeout{3600};
    bool enable_checkpointing{false};
    std::string checkpoint_directory;
    bool enable_retry{true};
    size_t max_retry_attempts{3};
    std::chrono::seconds retry_delay{30};
    bool continue_on_error{false};
    double error_threshold{0.1}; // 10% error threshold
    bool enable_monitoring{true};
    bool enable_profiling{false};
    std::vector<PipelineStage> stages;
    PipelineConfig core_config;
    std::unordered_map<std::string, std::any> stage_configs;
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief Pipeline stage interface
 */
class IPipelineStage {
public:
    virtual ~IPipelineStage() = default;

    /**
     * @brief Get stage type
     * @return PipelineStage Stage type
     */
    virtual PipelineStage get_stage_type() const = 0;

    /**
     * @brief Initialize stage
     * @param config Stage configuration
     * @param context Processing context
     * @return bool True if initialization successful
     */
    virtual bool initialize(
        const std::unordered_map<std::string, std::any>& config,
        ProcessingContext& context) = 0;

    /**
     * @brief Execute stage
     * @param input Input data
     * @param context Processing context
     * @return std::any Output data
     */
    virtual std::any execute(
        const std::any& input,
        ProcessingContext& context) = 0;

    /**
     * @brief Finalize stage
     * @param context Processing context
     * @return bool True if finalization successful
     */
    virtual bool finalize(ProcessingContext& context) = 0;

    /**
     * @brief Get stage statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;

    /**
     * @brief Check if stage supports parallel execution
     * @return bool True if supports parallel execution
     */
    virtual bool supports_parallel_execution() const = 0;

    /**
     * @brief Check if stage supports streaming
     * @return bool True if supports streaming
     */
    virtual bool supports_streaming() const = 0;
};

/**
 * @brief ETL pipeline interface
 * 
 * This interface defines the contract for ETL pipelines that orchestrate
 * the complete data processing workflow from extraction to loading.
 */
class IETLPipeline {
public:
    virtual ~IETLPipeline() = default;

    /**
     * @brief Initialize pipeline
     * @param config Pipeline configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const ETLPipelineConfig& config) = 0;

    /**
     * @brief Start pipeline execution
     * @return std::string Pipeline execution ID
     */
    virtual std::string start() = 0;

    /**
     * @brief Start pipeline execution asynchronously
     * @return std::future<std::string> Future containing execution ID
     */
    virtual std::future<std::string> start_async() = 0;

    /**
     * @brief Pause pipeline execution
     * @return bool True if paused successfully
     */
    virtual bool pause() = 0;

    /**
     * @brief Resume pipeline execution
     * @return bool True if resumed successfully
     */
    virtual bool resume() = 0;

    /**
     * @brief Stop pipeline execution
     * @param force Force stop without cleanup
     * @return bool True if stopped successfully
     */
    virtual bool stop(bool force = false) = 0;

    /**
     * @brief Wait for pipeline completion
     * @param timeout Timeout duration
     * @return PipelineStatus Final status
     */
    virtual PipelineStatus wait_for_completion(
        std::chrono::seconds timeout = std::chrono::seconds(0)) = 0;

    /**
     * @brief Get pipeline status
     * @return PipelineStatus Current status
     */
    virtual PipelineStatus get_status() const = 0;

    /**
     * @brief Get execution statistics
     * @return PipelineExecutionStats Current statistics
     */
    virtual PipelineExecutionStats get_execution_stats() const = 0;

    /**
     * @brief Add pipeline stage
     * @param stage Pipeline stage
     * @return bool True if stage added successfully
     */
    virtual bool add_stage(std::unique_ptr<IPipelineStage> stage) = 0;

    /**
     * @brief Remove pipeline stage
     * @param stage_type Stage type to remove
     * @return bool True if stage removed successfully
     */
    virtual bool remove_stage(PipelineStage stage_type) = 0;

    /**
     * @brief Get pipeline stages
     * @return std::vector<PipelineStage> List of stages
     */
    virtual std::vector<PipelineStage> get_stages() const = 0;

    /**
     * @brief Set metrics collector
     * @param metrics_collector Metrics collector instance
     */
    virtual void set_metrics_collector(
        std::shared_ptr<monitoring::IMetricsCollector> metrics_collector) = 0;

    /**
     * @brief Register progress callback
     * @param callback Progress callback function
     */
    virtual void register_progress_callback(
        std::function<void(const PipelineExecutionStats&)> callback) = 0;

    /**
     * @brief Register completion callback
     * @param callback Completion callback function
     */
    virtual void register_completion_callback(
        std::function<void(const PipelineExecutionStats&)> callback) = 0;

    /**
     * @brief Register error callback
     * @param callback Error callback function
     */
    virtual void register_error_callback(
        std::function<void(const std::string&, const PipelineExecutionStats&)> callback) = 0;

    /**
     * @brief Create checkpoint
     * @param checkpoint_name Checkpoint name
     * @return bool True if checkpoint created successfully
     */
    virtual bool create_checkpoint(const std::string& checkpoint_name) = 0;

    /**
     * @brief Restore from checkpoint
     * @param checkpoint_name Checkpoint name
     * @return bool True if restored successfully
     */
    virtual bool restore_from_checkpoint(const std::string& checkpoint_name) = 0;

    /**
     * @brief Get pipeline configuration
     * @return ETLPipelineConfig Current configuration
     */
    virtual ETLPipelineConfig get_config() const = 0;

    /**
     * @brief Update pipeline configuration
     * @param config New configuration
     * @return bool True if configuration updated successfully
     */
    virtual bool update_config(const ETLPipelineConfig& config) = 0;
};

/**
 * @brief ETL pipeline orchestrator
 *
 * This class coordinates the entire ETL process, managing extractors,
 * transformers, and loaders to process data efficiently.
 */
class ETLPipeline : public IETLPipeline {
public:
    /**
     * @brief Constructor
     * @param config Pipeline configuration
     */
    explicit ETLPipeline(PipelineConfig config = {});

    /**
     * @brief Destructor
     */
    ~ETLPipeline();

    // Interface methods
    bool initialize(const ETLPipelineConfig& config) override;
    std::string start() override;
    std::future<std::string> start_async() override;
    bool pause() override;
    bool resume() override;
    bool stop(bool force = false) override;
    PipelineStatus wait_for_completion(std::chrono::seconds timeout = std::chrono::seconds(0)) override;
    PipelineStatus get_status() const override;
    PipelineExecutionStats get_execution_stats() const override;
    bool add_stage(std::unique_ptr<IPipelineStage> stage) override;
    bool remove_stage(PipelineStage stage_type) override;
    std::vector<PipelineStage> get_stages() const override;
    void set_metrics_collector(std::shared_ptr<monitoring::IMetricsCollector> metrics_collector) override;
    void register_progress_callback(std::function<void(const PipelineExecutionStats&)> callback) override;
    void register_completion_callback(std::function<void(const PipelineExecutionStats&)> callback) override;
    void register_error_callback(std::function<void(const std::string&, const PipelineExecutionStats&)> callback) override;
    bool create_checkpoint(const std::string& checkpoint_name) override;
    bool restore_from_checkpoint(const std::string& checkpoint_name) override;
    ETLPipelineConfig get_config() const override;
    bool update_config(const ETLPipelineConfig& config) override;



protected:
    /**
     * @brief Main pipeline execution method
     */
    void run_pipeline();

    /**
     * @brief Extract data in a separate thread
     */
    void extraction_worker();

    /**
     * @brief Transform data in a separate thread
     */
    void transformation_worker();

    /**
     * @brief Load data in a separate thread
     */
    void loading_worker();

    /**
     * @brief Handle pipeline error
     * @param stage Processing stage
     * @param error Error message
     * @param exception Optional exception
     */
    void handle_error(ProcessingContext::Stage stage,
                     const std::string& error,
                     const std::exception* exception = nullptr);

    /**
     * @brief Save checkpoint
     */
    void save_checkpoint();

    /**
     * @brief Load checkpoint
     * @return bool True if checkpoint loaded
     */
    bool load_checkpoint();

    /**
     * @brief Check if extraction is complete
     */
    bool is_extraction_complete() const;

    // Job information - protected for testing
    JobInfo job_info_;
    std::atomic<bool> should_stop_{false};
    std::atomic<bool> is_paused_{false};
    std::atomic<bool> extraction_complete_{false};
    std::atomic<size_t> batches_in_flight_{0};
    std::function<void(const JobInfo&)> progress_callback_;

private:
    // Configuration
    PipelineConfig config_;
    ETLPipelineConfig etl_config_;

    // Components
    std::unique_ptr<IExtractor> extractor_;
    std::unique_ptr<ITransformer> transformer_;
    std::unique_ptr<ILoader> loader_;

    // Pre/post processors
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> pre_processors_;
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> post_processors_;

    // Processing context
    ProcessingContext context_;

    // Thread management
    std::vector<std::thread> workers_;
    std::atomic<PipelineStatus> status_{PipelineStatus::Created};
    std::condition_variable pause_cv_;

    // Queues
    std::queue<RecordBatch> extract_queue_;
    std::queue<RecordBatch> transform_queue_;
    std::mutex extract_mutex_;
    std::mutex transform_mutex_;
    std::condition_variable extract_cv_;
    std::condition_variable transform_cv_;

    // Error callback
    std::function<void(const std::string&, const std::exception&)> error_callback_;

    // Statistics
    mutable std::mutex stats_mutex_;
    std::chrono::steady_clock::time_point last_checkpoint_;

    // New pipeline stage management
    std::vector<std::unique_ptr<IPipelineStage>> stages_;
    std::unordered_map<PipelineStage, std::unique_ptr<IPipelineStage>> stage_map_;
    std::shared_ptr<monitoring::IMetricsCollector> metrics_collector_;
    std::function<void(const PipelineExecutionStats&)> progress_callback_new_;
    std::function<void(const PipelineExecutionStats&)> completion_callback_;
    std::function<void(const std::string&, const PipelineExecutionStats&)> error_callback_new_;
    PipelineExecutionStats execution_stats_;
};

/**
 * @brief Pipeline stage implementations
 */

/**
 * @brief Extract stage
 */
class ExtractStage : public IPipelineStage {
public:
    explicit ExtractStage(std::unique_ptr<IExtractor> extractor) : extractor_(std::move(extractor)) {}
    
    PipelineStage get_stage_type() const override { return PipelineStage::Extract; }
    bool initialize(const std::unordered_map<std::string, std::any>& config, ProcessingContext& context) override;
    std::any execute(const std::any& input, ProcessingContext& context) override;
    bool finalize(ProcessingContext& context) override;
    std::unordered_map<std::string, std::any> get_statistics() const override;
    bool supports_parallel_execution() const override { return true; }
    bool supports_streaming() const override { return true; }

private:
    std::unique_ptr<IExtractor> extractor_;
};

/**
 * @brief Transform stage
 */
class TransformStage : public IPipelineStage {
public:
    explicit TransformStage(std::unique_ptr<ITransformer> transformer) : transformer_(std::move(transformer)) {}
    
    PipelineStage get_stage_type() const override { return PipelineStage::Transform; }
    bool initialize(const std::unordered_map<std::string, std::any>& config, ProcessingContext& context) override;
    std::any execute(const std::any& input, ProcessingContext& context) override;
    bool finalize(ProcessingContext& context) override;
    std::unordered_map<std::string, std::any> get_statistics() const override;
    bool supports_parallel_execution() const override { return true; }
    bool supports_streaming() const override { return true; }

private:
    std::unique_ptr<ITransformer> transformer_;
};

/**
 * @brief Load stage
 */
class LoadStage : public IPipelineStage {
public:
    explicit LoadStage(std::unique_ptr<ILoader> loader) : loader_(std::move(loader)) {}
    
    PipelineStage get_stage_type() const override { return PipelineStage::Load; }
    bool initialize(const std::unordered_map<std::string, std::any>& config, ProcessingContext& context) override;
    std::any execute(const std::any& input, ProcessingContext& context) override;
    bool finalize(ProcessingContext& context) override;
    std::unordered_map<std::string, std::any> get_statistics() const override;
    bool supports_parallel_execution() const override { return true; }
    bool supports_streaming() const override { return false; }
    
    // Call commit on the underlying loader
    void commit(ProcessingContext& context) {
        if (loader_) {
            loader_->commit(context);
        }
    }

private:
    std::unique_ptr<ILoader> loader_;
    mutable size_t loaded_records_{0}; // Track loaded records
};

/**
 * @brief Custom pipeline stage for user-defined processing
 */
class CustomStage : public IPipelineStage {
public:
    using ProcessFunction = std::function<std::any(const std::any&, ProcessingContext&)>;
    
    CustomStage(const std::string& name, ProcessFunction process_func)
        : name_(name), process_func_(std::move(process_func)) {}

    PipelineStage get_stage_type() const override { return PipelineStage::Custom; }
    
    bool initialize(const std::unordered_map<std::string, std::any>& config, ProcessingContext& context) override {
        return true; // Custom stages don't need initialization
    }
    
    std::any execute(const std::any& input, ProcessingContext& context) override {
        if (process_func_) {
            return process_func_(input, context);
        }
        return input;
    }
    
    bool finalize(ProcessingContext& context) override {
        return true; // Custom stages don't need finalization
    }
    
    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {{"type", "custom"}, {"name", name_}};
    }
    
    bool supports_parallel_execution() const override { return true; }
    bool supports_streaming() const override { return true; }
    
    std::string get_type() const { return name_; }

private:
    std::string name_;
    ProcessFunction process_func_;
};

/**
 * @brief Pipeline factory
 */
class ETLPipelineFactory {
public:
    /**
     * @brief Create standard ETL pipeline
     * @param config Pipeline configuration
     * @return std::unique_ptr<IETLPipeline> Pipeline instance
     */
    static std::unique_ptr<IETLPipeline> create_standard_pipeline(
        const ETLPipelineConfig& config);

    /**
     * @brief Create streaming pipeline
     * @param config Pipeline configuration
     * @return std::unique_ptr<IETLPipeline> Pipeline instance
     */
    static std::unique_ptr<IETLPipeline> create_streaming_pipeline(
        const ETLPipelineConfig& config);

    /**
     * @brief Create parallel pipeline
     * @param config Pipeline configuration
     * @return std::unique_ptr<IETLPipeline> Pipeline instance
     */
    static std::unique_ptr<IETLPipeline> create_parallel_pipeline(
        const ETLPipelineConfig& config);

    /**
     * @brief Create custom pipeline
     * @param config Pipeline configuration
     * @param stages Custom stages
     * @return std::unique_ptr<IETLPipeline> Pipeline instance
     */
    static std::unique_ptr<IETLPipeline> create_custom_pipeline(
        const ETLPipelineConfig& config,
        std::vector<std::unique_ptr<IPipelineStage>> stages);
};

/**
 * @brief Pipeline utilities
 */
class PipelineUtils {
public:
    /**
     * @brief Convert pipeline status to string
     * @param status Pipeline status
     * @return std::string Status string
     */
    static std::string status_to_string(PipelineStatus status);

    /**
     * @brief Convert string to pipeline status
     * @param status_str Status string
     * @return PipelineStatus Pipeline status
     */
    static PipelineStatus string_to_status(const std::string& status_str);

    /**
     * @brief Convert stage to string
     * @param stage Pipeline stage
     * @return std::string Stage string
     */
    static std::string stage_to_string(PipelineStage stage);

    /**
     * @brief Convert string to stage
     * @param stage_str Stage string
     * @return PipelineStage Pipeline stage
     */
    static PipelineStage string_to_stage(const std::string& stage_str);

    /**
     * @brief Convert execution mode to string
     * @param mode Execution mode
     * @return std::string Mode string
     */
    static std::string execution_mode_to_string(ExecutionMode mode);

    /**
     * @brief Convert string to execution mode
     * @param mode_str Mode string
     * @return ExecutionMode Execution mode
     */
    static ExecutionMode string_to_execution_mode(const std::string& mode_str);

    /**
     * @brief Validate pipeline configuration
     * @param config Configuration to validate
     * @return std::vector<std::string> Validation errors
     */
    static std::vector<std::string> validate_config(const ETLPipelineConfig& config);

    /**
     * @brief Optimize pipeline configuration
     * @param config Base configuration
     * @param system_info System information
     * @param performance_requirements Performance requirements
     * @return ETLPipelineConfig Optimised configuration
     */
    static ETLPipelineConfig optimize_config(
        const ETLPipelineConfig& config,
        const std::unordered_map<std::string, std::any>& system_info,
        const std::unordered_map<std::string, std::any>& performance_requirements);

private:
    static const std::unordered_map<PipelineStatus, std::string> status_string_map_;
    static const std::unordered_map<std::string, PipelineStatus> string_status_map_;
    static const std::unordered_map<PipelineStage, std::string> stage_string_map_;
    static const std::unordered_map<std::string, PipelineStage> string_stage_map_;
    static const std::unordered_map<ExecutionMode, std::string> mode_string_map_;
    static const std::unordered_map<std::string, ExecutionMode> string_mode_map_;
};

/**
 * @brief Create ETL pipeline instance
 * @return std::unique_ptr<IETLPipeline> Pipeline instance
 */
std::unique_ptr<IETLPipeline> create_etl_pipeline();

/**
 * @brief Get default pipeline configuration
 * @return ETLPipelineConfig Default configuration
 */
ETLPipelineConfig get_default_pipeline_config();

/**
 * @brief Pipeline builder for fluent API
 *
 * Provides a builder pattern for constructing ETL pipelines.
 */
class PipelineBuilder {
public:
    /**
     * @brief Constructor
     */
    PipelineBuilder() : pipeline_(std::make_unique<ETLPipeline>()) {}

    /**
     * @brief Set configuration
     * @param config Pipeline configuration
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_config(const PipelineConfig& config);

    /**
     * @brief Set configuration from file
     * @param config_file Configuration file path
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_config_file(const std::string& config_file);

    /**
     * @brief Set extractor by type
     * @param type Extractor type
     * @param params Extractor parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_extractor(const std::string& type,
                                   const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set custom extractor
     * @param extractor Extractor instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_extractor(std::unique_ptr<IExtractor> extractor);

    /**
     * @brief Set transformer by type
     * @param type Transformer type
     * @param params Transformer parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer(const std::string& type,
                                     const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set transformer for table
     * @param table_name OMOP table name
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer_for_table(const std::string& table_name);

    /**
     * @brief Set custom transformer
     * @param transformer Transformer instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_transformer(std::unique_ptr<ITransformer> transformer);

    /**
     * @brief Set loader by type
     * @param type Loader type
     * @param params Loader parameters
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_loader(const std::string& type,
                                const std::unordered_map<std::string, std::any>& params);

    /**
     * @brief Set custom loader
     * @param loader Loader instance
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_loader(std::unique_ptr<ILoader> loader);

    /**
     * @brief Add pre-processor
     * @param processor Pre-processor function
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_pre_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Add post-processor
     * @param processor Post-processor function
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_post_processor(
        std::function<void(RecordBatch&, ProcessingContext&)> processor);

    /**
     * @brief Set progress callback
     * @param callback Progress callback
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_progress_callback(
        std::function<void(const JobInfo&)> callback);

    /**
     * @brief Set error callback
     * @param callback Error callback
     * @return PipelineBuilder& Builder reference
     */
    PipelineBuilder& with_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

    /**
     * @brief Build the pipeline
     * @return std::unique_ptr<ETLPipeline> Constructed pipeline
     */
    [[nodiscard]] std::unique_ptr<ETLPipeline> build();

private:
    std::unique_ptr<ETLPipeline> pipeline_;
    ProcessingContext context_;
    
    // Store components for later configuration
    std::unique_ptr<IExtractor> extractor_;
    std::unique_ptr<ITransformer> transformer_;
    std::unique_ptr<ILoader> loader_;
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> pre_processors_;
    std::vector<std::function<void(RecordBatch&, ProcessingContext&)>> post_processors_;
};

/**
 * @brief Pipeline manager for managing multiple ETL jobs
 *
 * This class manages multiple ETL pipelines, providing job scheduling,
 * monitoring, and resource management.
 */
class PipelineManager {
public:
    /**
     * @brief Constructor
     * @param max_concurrent_jobs Maximum concurrent jobs
     */
    explicit PipelineManager(size_t max_concurrent_jobs = 4);

    /**
     * @brief Destructor
     */
    ~PipelineManager();

    /**
     * @brief Submit job
     * @param job_name Job name
     * @param pipeline Pipeline to execute
     * @return std::string Job ID
     */
    std::string submit_job(const std::string& job_name,
                          std::unique_ptr<ETLPipeline> pipeline);

    /**
     * @brief Get job status
     * @param job_id Job ID
     * @return std::optional<PipelineStatus> Job status if found
     */
    [[nodiscard]] std::optional<PipelineStatus> get_job_status(const std::string& job_id) const;

    /**
     * @brief Get job information
     * @param job_id Job ID
     * @return std::optional<JobInfo> Job information if found
     */
    [[nodiscard]] std::optional<JobInfo> get_job_info(const std::string& job_id) const;

    /**
     * @brief Get all jobs
     * @return std::vector<JobInfo> All job information
     */
    [[nodiscard]] std::vector<JobInfo> get_all_jobs() const;

    /**
     * @brief Cancel job
     * @param job_id Job ID
     * @return bool True if cancelled
     */
    bool cancel_job(const std::string& job_id);

    /**
     * @brief Pause job
     * @param job_id Job ID
     * @return bool True if paused
     */
    bool pause_job(const std::string& job_id);

    /**
     * @brief Resume job
     * @param job_id Job ID
     * @return bool True if resumed
     */
    bool resume_job(const std::string& job_id);

    /**
     * @brief Wait for job completion
     * @param job_id Job ID
     * @param timeout_ms Timeout in milliseconds (-1 for no timeout)
     * @return bool True if completed within timeout
     */
    bool wait_for_job(const std::string& job_id, int timeout_ms = -1);

    /**
     * @brief Shutdown manager
     * @param wait_for_jobs Whether to wait for running jobs
     */
    void shutdown(bool wait_for_jobs = true);

private:
    struct JobEntry {
        std::string job_id;
        std::unique_ptr<ETLPipeline> pipeline;
        std::future<JobInfo> future;
        JobInfo info;
    };

    size_t max_concurrent_jobs_;
    std::unordered_map<std::string, std::unique_ptr<JobEntry>> jobs_;
    std::queue<std::string> job_queue_;
    std::vector<std::thread> scheduler_threads_;

    mutable std::mutex jobs_mutex_;
    std::condition_variable job_cv_;
    std::atomic<bool> shutdown_{false};

    void scheduler_worker();
    std::string generate_job_id();
};

} // namespace omop::core