/**
 * @file job_manager.h
 * @brief Job management system for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains the job management system for orchestrating and
 * monitoring ETL jobs throughout their lifecycle.
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <thread>
#include <functional>
#include <deque>

#include "interfaces.h"
#include "pipeline.h"
#include "common/logging.h"
#include "common/configuration.h"

namespace omop::core {

// PipelineStatus is defined in pipeline.h

/**
 * @brief ETL job priority levels
 */
enum class JobPriority {
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    CRITICAL = 3
};

/**
 * @brief Job execution statistics
 */
struct JobStatistics {
    size_t total_records_processed{0};    ///< Total records processed
    size_t successful_records{0};         ///< Successfully processed records
    size_t failed_records{0};             ///< Failed records
    size_t skipped_records{0};            ///< Skipped records
    double processing_rate{0.0};          ///< Records per second
    size_t memory_usage_mb{0};            ///< Memory usage in MB
    double cpu_usage_percent{0.0};        ///< CPU usage percentage
    std::chrono::duration<double> elapsed_time; ///< Total elapsed time
    std::unordered_map<std::string, double> stage_timings; ///< Timing per stage
};

/**
 * @brief ETL job configuration
 */
struct JobConfig {
    std::string job_id;                   ///< Unique job identifier
    std::string job_name;                 ///< Human-readable job name
    std::string pipeline_config_path;     ///< Path to pipeline configuration
    JobPriority priority{JobPriority::NORMAL}; ///< Job priority
    size_t max_retries{3};                ///< Maximum retry attempts
    std::chrono::seconds retry_delay{60}; ///< Delay between retries
    std::chrono::seconds timeout{0};      ///< Job timeout (0 = no timeout)
    bool enable_checkpointing{true};      ///< Enable job checkpointing
    size_t checkpoint_interval{10000};    ///< Checkpoint interval (records)
    std::unordered_map<std::string, std::string> parameters; ///< Job parameters
    std::unordered_map<std::string, std::string> metadata;   ///< Job metadata
};

/**
 * @brief ETL job instance
 */
class Job {
public:
    /**
     * @brief Constructor
     * @param config Job configuration
     * @param pipeline Pipeline instance
     */
    Job(const JobConfig& config, std::unique_ptr<ETLPipeline> pipeline);

    /**
     * @brief Get job ID
     * @return Job identifier
     */
    const std::string& getId() const { return config_.job_id; }

    /**
     * @brief Get job name
     * @return Job name
     */
    const std::string& getName() const { return config_.job_name; }

    /**
     * @brief Get job status
     * @return Current job status
     */
    PipelineStatus getStatus() const { return status_.load(); }

    /**
     * @brief Set job status
     * @param status New status
     */
    void setStatus(PipelineStatus status);

    /**
     * @brief Thread-safe status update
     */
    void updateStatus(PipelineStatus new_status);

    /**
     * @brief Get job configuration
     * @return Job configuration
     */
    const JobConfig& getConfig() const { return config_; }

    /**
     * @brief Get job statistics
     * @return Job statistics
     */
    JobStatistics getStatistics() const;

    /**
     * @brief Get pipeline instance
     * @return Pipeline pointer
     */
    ETLPipeline* getPipeline() { return pipeline_.get(); }

    /**
     * @brief Get creation time
     * @return Creation timestamp
     */
    std::chrono::system_clock::time_point getCreationTime() const { return creation_time_; }

    /**
     * @brief Get start time
     * @return Start timestamp
     */
    std::chrono::system_clock::time_point getStartTime() const { return start_time_; }

    /**
     * @brief Get end time
     * @return End timestamp
     */
    std::chrono::system_clock::time_point getEndTime() const { return end_time_; }

    /**
     * @brief Get error message
     * @return Error message if job failed
     */
    const std::string& getErrorMessage() const { return error_message_; }

    /**
     * @brief Set error message
     * @param message Error message
     */
    void setErrorMessage(const std::string& message) { error_message_ = message; }

    /**
     * @brief Get retry count
     * @return Number of retries
     */
    size_t getRetryCount() const { return retry_count_; }

    /**
     * @brief Increment retry count
     */
    void incrementRetryCount() { ++retry_count_; }

    /**
     * @brief Update job statistics
     * @param stats New statistics
     */
    void updateStatistics(const JobStatistics& stats);

    /**
     * @brief Check if job can be retried
     * @return true if job can be retried
     */
    bool canRetry() const;

    /**
     * @brief Save checkpoint
     * @return true if checkpoint saved successfully
     */
    bool saveCheckpoint();

    /**
     * @brief Load checkpoint
     * @return true if checkpoint loaded successfully
     */
    bool loadCheckpoint();

private:
    friend class JobManager; // Allow JobManager to access private members

    JobConfig config_;                              ///< Job configuration
    std::unique_ptr<ETLPipeline> pipeline_;         ///< Pipeline instance
    std::atomic<PipelineStatus> status_{PipelineStatus::Created}; ///< Current status
    JobStatistics statistics_;                      ///< Job statistics
    mutable std::mutex stats_mutex_;                ///< Statistics mutex

    std::chrono::system_clock::time_point creation_time_; ///< Creation time
    std::chrono::system_clock::time_point start_time_;    ///< Start time
    std::chrono::system_clock::time_point end_time_;      ///< End time

    std::string error_message_;                     ///< Error message
    size_t retry_count_{0};                         ///< Retry count
    std::string checkpoint_path_;                   ///< Checkpoint file path
};

/**
 * @brief Job execution context
 */
struct JobExecutionContext {
    std::shared_ptr<Job> job;                       ///< Job instance
    std::shared_ptr<common::Logger> logger;         ///< Logger instance
    std::function<void(const JobStatistics&)> progress_callback; ///< Progress callback
    std::atomic<bool> should_stop{false};           ///< Stop flag
};

/**
 * @brief Job comparator for priority queue
 */
struct JobPriorityComparator {
    bool operator()(const std::shared_ptr<Job>& a, const std::shared_ptr<Job>& b) const {
        // Higher priority jobs should be processed first
        if (a->getConfig().priority != b->getConfig().priority) {
            return static_cast<int>(a->getConfig().priority) <
                   static_cast<int>(b->getConfig().priority);
        }
        // If priorities are equal, older jobs should be processed first (FIFO)
        return a->getCreationTime() > b->getCreationTime();
    }
};

/**
 * @brief Job manager for orchestrating ETL jobs
 */
class JobManager {
public:
    /**
     * @brief Constructor
     * @param config Configuration
     * @param logger Logger instance
     */
    JobManager(std::shared_ptr<common::ConfigurationManager> config,
               std::shared_ptr<common::Logger> logger);

    /**
     * @brief Destructor
     */
    ~JobManager();

    /**
     * @brief Start the job manager
     * @return true if started successfully
     */
    bool start();

    /**
     * @brief Stop the job manager
     */
    void stop();

    /**
     * @brief Submit a job for execution
     * @param config Job configuration
     * @return Job ID
     */
    std::string submitJob(const JobConfig& config);

    /**
     * @brief Get job by ID
     * @param job_id Job identifier
     * @return Job instance or nullptr
     */
    std::shared_ptr<Job> getJob(const std::string& job_id) const;

    /**
     * @brief Get all jobs
     * @return Vector of all jobs
     */
    std::vector<std::shared_ptr<Job>> getAllJobs() const;

    /**
     * @brief Get jobs by status
     * @param status Job status
     * @return Vector of jobs with given status
     */
    std::vector<std::shared_ptr<Job>> getJobsByStatus(PipelineStatus status) const;

    /**
     * @brief Cancel a job
     * @param job_id Job identifier
     * @return true if job was cancelled
     */
    bool cancelJob(const std::string& job_id);

    /**
     * @brief Pause a job
     * @param job_id Job identifier
     * @return true if job was paused
     */
    bool pauseJob(const std::string& job_id);

    /**
     * @brief Resume a paused job
     * @param job_id Job identifier
     * @return true if job was resumed
     */
    bool resumeJob(const std::string& job_id);


    /**
     * @brief Retry a failed job
     * @param job_id Job identifier
     * @return true if job was queued for retry
     */
    bool retryJob(const std::string& job_id);

    /**
     * @brief Get number of active jobs
     * @return Active job count
     */
    size_t getActiveJobCount() const;

    /**
     * @brief Get number of queued jobs
     * @return Queued job count
     */
    size_t getQueuedJobCount() const;

    /**
     * @brief Set maximum concurrent jobs
     * @param max_jobs Maximum concurrent jobs
     */
    void setMaxConcurrentJobs(size_t max_jobs);

    /**
     * @brief Register job event callback
     * @param callback Event callback function
     */
    void registerJobEventCallback(
        std::function<void(const std::string&, PipelineStatus, PipelineStatus)> callback);

    /**
     * @brief Clean up completed jobs older than specified duration
     * @param age Maximum age of completed jobs to keep
     * @return Number of jobs cleaned up
     */
    size_t cleanupOldJobs(std::chrono::hours age);

    /**
     * @brief Add a job to the manager
     * @param job Job to add
     */
    void addJob(std::shared_ptr<Job> job);

    /**
     * @brief Remove a job from the manager
     * @param job_id Job identifier
     */
    void removeJob(const std::string& job_id);

    /**
     * @brief Execute a pipeline directly
     * @param pipeline Pipeline to execute
     */
    void executePipeline(std::unique_ptr<ETLPipeline> pipeline);

private:
    /**
     * @brief Worker thread function
     */
    void workerThread();

    /**
     * @brief Execute a job
     * @param context Execution context
     */
    void executeJob(JobExecutionContext& context);

    /**
     * @brief Create pipeline from configuration
     * @param config_path Configuration file path
     * @return Pipeline instance
     */
    std::unique_ptr<ETLPipeline> createPipeline(const std::string& config_path);

    /**
     * @brief Get next job from queue
     * @return Next job or nullptr
     */
    std::shared_ptr<Job> getNextJob();

    /**
     * @brief Handle job completion
     * @param job Completed job
     */
    void handleJobCompletion(std::shared_ptr<Job> job);

    /**
     * @brief Handle job failure
     * @param job Failed job
     */
    void handleJobFailure(std::shared_ptr<Job> job);

    /**
     * @brief Notify job status change
     * @param job_id Job identifier
     * @param old_status Old status
     * @param new_status New status
     */
    void notifyPipelineStatusChange(const std::string& job_id,
                              PipelineStatus old_status,
                              PipelineStatus new_status);

private:
    std::shared_ptr<common::ConfigurationManager> config_;     ///< Configuration
    std::shared_ptr<common::Logger> logger_;            ///< Logger

    std::unordered_map<std::string, std::shared_ptr<Job>> jobs_; ///< All jobs
    mutable std::mutex jobs_mutex_;                     ///< Jobs map mutex

    // Use deque for thread-safe queue operations
    std::deque<std::shared_ptr<Job>> job_queue_;       ///< Job queue
    JobPriorityComparator job_comparator_;              ///< Job comparator
    std::mutex queue_mutex_;                            ///< Queue mutex
    std::condition_variable queue_cv_;                  ///< Queue condition variable

    std::vector<std::thread> worker_threads_;           ///< Worker threads
    std::atomic<bool> running_{false};                  ///< Running flag
    std::atomic<size_t> active_jobs_{0};                ///< Active job count
    size_t max_concurrent_jobs_{4};                     ///< Maximum concurrent jobs

    std::vector<std::function<void(const std::string&, PipelineStatus, PipelineStatus)>>
        event_callbacks_;                               ///< Event callbacks
    std::mutex callbacks_mutex_;                        ///< Callbacks mutex
};

} // namespace omop::core