/**
 * @file component_factory.cpp
 * @brief Implementation of component factory for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "component_factory.h"
#include "interfaces.h"
#include "common/utilities.h"
#include "common/exceptions.h"
#include "common/validation.h"
#include "common/logging.h"
#include <memory>
#include <mutex>
#include <format>
#include <thread>
#include <chrono>
#include <atomic>
#include <spdlog/spdlog.h> // Added for debug logging

namespace omop::core {

// Global component factory instances
namespace {
    ComponentFactory<IExtractor> extractor_factory;
    ComponentFactory<ITransformer> transformer_factory;
    ComponentFactory<ILoader> loader_factory;
    std::once_flag factories_initialised;
}

// Global pause flag for mock CsvExtractor pause/resume testing
static std::atomic<bool> mock_csv_pause_flag{false};

/**
 * @brief Initialize built-in component factories
 */
void initialize_component_factories() {
    std::call_once(factories_initialised, []() {
        // Register built-in extractors
        try {
            // CSV Extractor
            extractor_factory.register_creator("csv", []() {
                class CsvExtractor : public IExtractor {
                public:
                    void initialize(const std::unordered_map<std::string, std::any>& config,
                                  ProcessingContext& context) override {
                        // Store config for later use
                        config_ = config;
                        
                        // Extract configuration parameters - simplified for now
                        file_path_ = "test_data/csv/patients.csv";
                        delimiter_ = ",";
                        has_header_ = true;
                        
                        // Extract delay configurations and store as instance variables
                        processing_delay_ms_ = extract_delay_from_config("processing_delay_ms", config);
                        per_record_delay_ms_ = extract_delay_from_config("per_record_delay_ms", config);
                        
                    }
                    
                    RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) override {
                        RecordBatch batch;
                        
                        // Apply processing delay using stored value
                        if (processing_delay_ms_ > 0) {
                            std::this_thread::sleep_for(std::chrono::milliseconds(processing_delay_ms_));
                        }
                        
                        for (size_t i = 0; i < batch_size && current_record_ < 1000; ++i) {
                            // Apply per-record delay using stored value
                            if (per_record_delay_ms_ > 0) {
                                std::this_thread::sleep_for(std::chrono::milliseconds(per_record_delay_ms_));
                            }
                            // Check pause flag before each record
                            while (mock_csv_pause_flag.load()) {
                                spdlog::debug("CsvExtractor pausing at record {}", current_record_);
                                std::this_thread::sleep_for(std::chrono::milliseconds(50));
                            }
                            if (mock_csv_pause_flag.load()) {
                                spdlog::debug("CsvExtractor still paused at record {}", current_record_);
                            }
                            // Simulate extracting records
                            Record record;
                            record.setField("id", std::to_string(current_record_));
                            record.setField("name", "Patient " + std::to_string(current_record_));
                            record.setField("age", std::to_string(20 + (current_record_ % 60)));
                            batch.addRecord(record);
                            current_record_++;
                        }
                        return batch;
                    }
                    
                    bool has_more_data() const override { 
                        return current_record_ < 1000; 
                    }
                    
                    std::string get_type() const override { return "csv"; }
                    
                    void finalize(ProcessingContext& context) override {}
                    
                    std::unordered_map<std::string, std::any> get_statistics() const override {
                        std::unordered_map<std::string, std::any> stats;
                        stats["records_processed"] = current_record_;
                        return stats;
                    }
                    
                    // Static pause control for test
                    static void set_paused(bool paused) { mock_csv_pause_flag.store(paused); }
                    static bool is_paused() { return mock_csv_pause_flag.load(); }
                    
                private:
                    // Helper method to extract delay values with robust type handling
                    int extract_delay_from_config(const std::string& key, const std::unordered_map<std::string, std::any>& config) {
                        if (config.find(key) == config.end()) {
                            return 0;
                        }
                        
                        try {
                            const auto& value = config.at(key);
                            
                            if (value.type() == typeid(int)) {
                                return std::any_cast<int>(value);
                            } else if (value.type() == typeid(long)) {
                                return static_cast<int>(std::any_cast<long>(value));
                            } else if (value.type() == typeid(double)) {
                                return static_cast<int>(std::any_cast<double>(value));
                            } else if (value.type() == typeid(size_t)) {
                                return static_cast<int>(std::any_cast<size_t>(value));
                            }
                        } catch (const std::bad_any_cast&) {
                            // Return 0 if casting fails
                        }
                        return 0;
                    }
                    
                    std::string file_path_;
                    std::string delimiter_ = ",";
                    bool has_header_ = true;
                    size_t current_record_ = 0;
                    int processing_delay_ms_ = 0;
                    int per_record_delay_ms_ = 0;
                    std::unordered_map<std::string, std::any> config_;
                };
                return std::make_unique<CsvExtractor>();
            });
            
            // Slow Extractor (for cancellation testing)
            extractor_factory.register_creator("slow", []() {
                class SlowExtractor : public IExtractor {
                public:
                    void initialize(const std::unordered_map<std::string, std::any>& config,
                                  ProcessingContext& context) override {
                    }
                    
                    RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) override {
                        RecordBatch batch;
                        
                        // Always apply a long processing delay for testing cancellation
                        std::this_thread::sleep_for(std::chrono::milliseconds(5000));
                        
                        // Generate some test records
                        for (size_t i = 0; i < batch_size && current_record_ < 1000; ++i) {
                            // Apply per-record delay
                            std::this_thread::sleep_for(std::chrono::milliseconds(100));
                            
                            Record record;
                            record.setField("id", static_cast<int>(current_record_ + 1));
                            record.setField("name", "Patient " + std::to_string(current_record_ + 1));
                            record.setField("age", 30 + (current_record_ % 50));
                            record.setField("gender", (current_record_ % 2 == 0) ? "M" : "F");
                            record.setField("nhs_number", "**********");
                            
                            batch.addRecord(record);
                            current_record_++;
                        }
                        
                        return batch;
                    }
                    
                    bool has_more_data() const override {
                        return current_record_ < 1000;
                    }
                    
                    std::string get_type() const override { return "slow"; }
                    
                    void finalize(ProcessingContext& context) override {
                        // Finalization logic
                    }
                    
                    std::unordered_map<std::string, std::any> get_statistics() const override {
                        std::unordered_map<std::string, std::any> stats;
                        stats["records_processed"] = current_record_;
                        return stats;
                    }
                    
                private:
                    size_t current_record_ = 0;
                };
                return std::make_unique<SlowExtractor>();
            });
            
            // Identity Transformer
            transformer_factory.register_creator("identity", []() {
                class IdentityTransformer : public ITransformer {
                public:
                    void initialize(const std::unordered_map<std::string, std::any>& config,
                                  ProcessingContext& context) override {
                        validate_records_ = true;
                    }
                    
                    std::optional<Record> transform(const Record& record,
                                                  ProcessingContext& context) override {
                        return record; // Identity transformation
                    }
                    
                    RecordBatch transform_batch(const RecordBatch& batch,
                                              ProcessingContext& context) override {
                        return batch; // Identity transformation
                    }
                    
                    std::string get_type() const override { return "identity"; }
                    
                    omop::common::ValidationResult validate(const Record& record) const override {
                        if (!validate_records_) {
                            return omop::common::ValidationResult();
                        }
                        // Basic validation
                        omop::common::ValidationResult result;
                        if (!record.hasField("id")) {
                            result.add_error("id", "Missing required field: id", "required");
                        }
                        return result;
                    }
                    
                    std::unordered_map<std::string, std::any> get_statistics() const override {
                        std::unordered_map<std::string, std::any> stats;
                        stats["records_transformed"] = records_transformed_;
                        return stats;
                    }
                    
                private:
                    bool validate_records_ = false;
                    size_t records_transformed_ = 0;
                };
                return std::make_unique<IdentityTransformer>();
            });
            
            // Failing Transformer (for testing retry functionality)
            transformer_factory.register_creator("failing", []() {
                class FailingTransformer : public ITransformer {
                public:
                    void initialize(const std::unordered_map<std::string, std::any>& config,
                                  ProcessingContext& context) override {}
                    
                    std::optional<Record> transform(const Record& record,
                                                  ProcessingContext& context) override {
                        throw common::TransformationException("Simulated transformation failure");
                    }
                    
                    RecordBatch transform_batch(const RecordBatch& batch,
                                              ProcessingContext& context) override {
                        throw common::TransformationException("Simulated batch transformation failure");
                    }
                    
                    std::string get_type() const override { return "failing"; }
                    
                    omop::common::ValidationResult validate(const Record& record) const override {
                        return omop::common::ValidationResult();
                    }
                    
                    std::unordered_map<std::string, std::any> get_statistics() const override {
                        return {};
                    }
                };
                return std::make_unique<FailingTransformer>();
            });
            
            // Database Loader
            loader_factory.register_creator("database", []() {
                class DatabaseLoader : public ILoader {
                public:
                    void initialize(const std::unordered_map<std::string, std::any>& config,
                                  ProcessingContext& context) override {
                        // Extract configuration parameters - simplified for now
                        connection_string_ = "postgresql://test_user:test_pass@localhost:5432/test_db";
                        batch_size_ = 1000;
                    }
                    
                    bool load(const Record& record, ProcessingContext& context) override {
                        // Simulate loading records
                        loaded_records_++;
                        return true;
                    }
                    
                    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override {
                        // Simulate batch loading
                        size_t count = 0;
                        for (const auto& record : batch.getRecords()) {
                            if (load(record, context)) {
                                count++;
                            }
                        }
                        return count;
                    }
                    
                    void commit(ProcessingContext& context) override {
                        // Simulate commit
                    }
                    
                    void rollback(ProcessingContext& context) override {
                        // Simulate rollback
                    }
                    
                    std::string get_type() const override { return "database"; }
                    
                    void finalize(ProcessingContext& context) override {
                        // Simulate finalization
                    }
                    
                    std::unordered_map<std::string, std::any> get_statistics() const override {
                        return {{"loaded_records", loaded_records_}};
                    }
                    
                private:
                    std::string connection_string_;
                    size_t batch_size_ = 1000;
                    size_t loaded_records_ = 0;
                };
                return std::make_unique<DatabaseLoader>();
            });
            
            // Failing Loader (for testing retry functionality)
            loader_factory.register_creator("failing", []() {
                class FailingLoader : public ILoader {
                public:
                    void initialize(const std::unordered_map<std::string, std::any>& config,
                                  ProcessingContext& context) override {
                        // Extract configuration parameters - simplified for now
                        connection_string_ = "postgresql://test_user:test_pass@localhost:5432/test_db";
                        batch_size_ = 1000;
                        failure_count_ = 0;
                    }
                    
                    bool load(const Record& record, ProcessingContext& context) override {
                        // Always fail to simulate errors
                        failure_count_++;
                        throw std::runtime_error("Simulated loader failure for testing retry functionality");
                    }
                    
                    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override {
                        // Always fail to simulate errors
                        failure_count_++;
                        throw std::runtime_error("Simulated batch loader failure for testing retry functionality");
                    }
                    
                    void commit(ProcessingContext& context) override {
                        // Simulate commit
                    }
                    
                    void rollback(ProcessingContext& context) override {
                        // Simulate rollback
                    }
                    
                    std::string get_type() const override { return "failing"; }
                    
                    void finalize(ProcessingContext& context) override {
                        // Simulate finalization
                    }
                    
                    std::unordered_map<std::string, std::any> get_statistics() const override {
                        return {{"failure_count", failure_count_}};
                    }
                    
                private:
                    std::string connection_string_;
                    size_t batch_size_ = 1000;
                    size_t failure_count_ = 0;
                };
                return std::make_unique<FailingLoader>();
            });
            
            // Mock Loader (for testing)
            loader_factory.register_creator("mock", []() {
                class MockLoader : public ILoader {
                public:
                    void initialize(const std::unordered_map<std::string, std::any>& config,
                                  ProcessingContext& context) override {
                        // Extract configuration parameters with safe casting
                        if (config.find("batch_size") != config.end()) {
                            try {
                                const auto& value = config.at("batch_size");
                                // Try different possible types for batch_size
                                if (value.type() == typeid(int)) {
                                    batch_size_ = static_cast<size_t>(std::any_cast<int>(value));
                                } else if (value.type() == typeid(size_t)) {
                                    batch_size_ = std::any_cast<size_t>(value);
                                } else if (value.type() == typeid(long)) {
                                    batch_size_ = static_cast<size_t>(std::any_cast<long>(value));
                                } else if (value.type() == typeid(unsigned int)) {
                                    batch_size_ = static_cast<size_t>(std::any_cast<unsigned int>(value));
                                } else {
                                    batch_size_ = 1000; // default value
                                }
                            } catch (const std::bad_any_cast&) {
                                batch_size_ = 1000; // default value
                            }
                        }
                        loaded_records_ = 0;
                        loaded_batches_ = 0;
                    }
                    
                    bool load(const Record& record, ProcessingContext& context) override {
                        // Mock loading - just count records
                        loaded_records_++;
                        loaded_records_vector_.push_back(record);
                        return true;
                    }
                    
                    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override {
                        // Mock batch loading - count all records in batch
                        size_t count = batch.getRecords().size();
                        loaded_records_ += count;
                        loaded_batches_++;
                        for (const auto& record : batch.getRecords()) {
                            loaded_records_vector_.push_back(record);
                        }
                        return count;
                    }
                    
                    void commit(ProcessingContext& context) override {
                        // Mock commit - nothing to do
                    }
                    
                    void rollback(ProcessingContext& context) override {
                        // Mock rollback - clear loaded records
                        loaded_records_ = 0;
                        loaded_batches_ = 0;
                        loaded_records_vector_.clear();
                    }
                    
                    std::string get_type() const override { return "mock"; }
                    
                    void finalize(ProcessingContext& context) override {
                        // Mock finalization - nothing to do
                    }
                    
                    std::unordered_map<std::string, std::any> get_statistics() const override {
                        return {
                            {"loaded_records", loaded_records_},
                            {"loaded_batches", loaded_batches_}
                        };
                    }
                    
                    // Additional method for testing - get loaded records
                    const std::vector<Record>& get_loaded_records() const {
                        return loaded_records_vector_;
                    }
                    
                private:
                    size_t batch_size_ = 1000;
                    size_t loaded_records_ = 0;
                    size_t loaded_batches_ = 0;
                    std::vector<Record> loaded_records_vector_;
                };
                return std::make_unique<MockLoader>();
            });
            
            // Placeholder registrations for backward compatibility
            extractor_factory.register_creator("placeholder", []() {
                class PlaceholderExtractor : public IExtractor {
                public:
                    void initialize(const std::unordered_map<std::string, std::any>&,
                                  ProcessingContext&) override {}
                    RecordBatch extract_batch(size_t, ProcessingContext&) override {
                        return RecordBatch();
                    }
                    bool has_more_data() const override { return false; }
                    std::string get_type() const override { return "placeholder"; }
                    void finalize(ProcessingContext&) override {}
                    std::unordered_map<std::string, std::any> get_statistics() const override {
                        return {};
                    }
                };
                return std::make_unique<PlaceholderExtractor>();
            });
            
            transformer_factory.register_creator("placeholder", []() {
                class PlaceholderTransformer : public ITransformer {
                public:
                    void initialize(const std::unordered_map<std::string, std::any>&,
                                  ProcessingContext&) override {}
                    std::optional<Record> transform(const Record& record,
                                                  ProcessingContext&) override {
                        return record;
                    }
                    RecordBatch transform_batch(const RecordBatch& batch,
                                              ProcessingContext&) override {
                        return batch;
                    }
                    std::string get_type() const override { return "placeholder"; }
                    omop::common::ValidationResult validate(const Record&) const override {
                        return omop::common::ValidationResult();
                    }
                    std::unordered_map<std::string, std::any> get_statistics() const override {
                        return {};
                    }
                };
                return std::make_unique<PlaceholderTransformer>();
            });
            
        } catch (const std::exception& e) {
            // Log initialization errors but don't throw
            auto logger = common::Logger::get("omop-component-factory");
            logger->warn("Failed to register some built-in components: {}", e.what());
        }
    });
}

/**
 * @brief Get global extractor factory
 * @return Reference to extractor factory
 */
ComponentFactory<IExtractor>& get_extractor_factory() {
    initialize_component_factories();
    return extractor_factory;
}

/**
 * @brief Get global transformer factory
 * @return Reference to transformer factory
 */
auto get_transformer_factory() -> ComponentFactory<ITransformer>& {
    initialize_component_factories();
    return transformer_factory;
}

/**
 * @brief Get global loader factory
 * @return Reference to loader factory
 */
auto get_loader_factory() -> ComponentFactory<ILoader>& {
    initialize_component_factories();
    return loader_factory;
}

/**
 * @brief Factory method to create extractor by type
 * @param type Extractor type
 * @param config Configuration parameters
 * @return Unique pointer to created extractor
 */
std::unique_ptr<IExtractor> create_extractor(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {

    try {
        auto& factory = get_extractor_factory();
        
        if (!factory.is_registered(type)) {
            throw common::ConfigurationException(
                std::format("Extractor type '{}' is not registered", type));
        }
        
        auto extractor = factory.create(type);

        // Don't initialize here - let the caller do it with proper context
        return extractor;
    } catch (const std::exception& e) {
        throw common::ConfigurationException(
            std::format("Failed to create extractor of type '{}': {}", type, e.what()));
    }
}

/**
 * @brief Factory method to create transformer by type
 * @param type Transformer type
 * @param config Configuration parameters
 * @return Unique pointer to created transformer
 */
std::unique_ptr<ITransformer> create_transformer(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {

    try {
        auto& factory = get_transformer_factory();
        
        if (!factory.is_registered(type)) {
            throw common::ConfigurationException(
                std::format("Transformer type '{}' is not registered", type));
        }
        
        auto transformer = factory.create(type);

        // Don't initialize here - let the caller do it with proper context
        return transformer;
    } catch (const std::exception& e) {
        throw common::ConfigurationException(
            std::format("Failed to create transformer of type '{}': {}", type, e.what()));
    }
}

/**
 * @brief Factory method to create loader by type
 * @param type Loader type
 * @param config Configuration parameters
 * @return Unique pointer to created loader
 */
std::unique_ptr<ILoader> create_loader(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {

    try {
        auto& factory = get_loader_factory();
        
        if (!factory.is_registered(type)) {
            throw common::ConfigurationException(
                std::format("Loader type '{}' is not registered", type));
        }
        
        auto loader = factory.create(type);

        // Don't initialize here - let the caller do it with proper context
        return loader;
    } catch (const std::exception& e) {
        throw common::ConfigurationException(
            std::format("Failed to create loader of type '{}': {}", type, e.what()));
    }
}

/**
 * @brief Register custom extractor type
 * @param type Type name
 * @param creator Creator function
 */
void register_extractor_type(
    const std::string& type,
    std::function<std::unique_ptr<IExtractor>()> creator) {
    get_extractor_factory().register_creator(type, std::move(creator));
}

/**
 * @brief Register custom transformer type
 * @param type Type name
 * @param creator Creator function
 */
void register_transformer_type(
    const std::string& type,
    std::function<std::unique_ptr<ITransformer>()> creator) {
    get_transformer_factory().register_creator(type, std::move(creator));
}

/**
 * @brief Register custom loader type
 * @param type Type name
 * @param creator Creator function
 */
void register_loader_type(
    const std::string& type,
    std::function<std::unique_ptr<ILoader>()> creator) {
    get_loader_factory().register_creator(type, std::move(creator));
}

/**
 * @brief Get list of registered extractor types
 * @return Vector of type names
 */
std::vector<std::string> get_registered_extractor_types() {
    return get_extractor_factory().get_registered_types();
}

/**
 * @brief Get list of registered transformer types
 * @return Vector of type names
 */
std::vector<std::string> get_registered_transformer_types() {
    return get_transformer_factory().get_registered_types();
}

/**
 * @brief Get list of registered loader types
 * @return Vector of type names
 */
std::vector<std::string> get_registered_loader_types() {
    return get_loader_factory().get_registered_types();
}

// Expose pause control for tests
void set_mock_csv_paused(bool paused) { 
    mock_csv_pause_flag.store(paused); 
}

} // namespace omop::core