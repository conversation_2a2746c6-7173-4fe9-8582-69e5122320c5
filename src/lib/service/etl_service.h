#pragma once

#include "core/pipeline.h"
#include "common/configuration.h"
#include "common/logging.h"
#include "common/utilities.h"
#include <memory>
#include <functional>
#include <chrono>

namespace omop::service {

/**
 * @brief ETL job request
 */
struct ETLJobRequest {
    std::string name;
    std::string description;
    std::string source_table;
    std::string target_table;
    std::string extractor_type{"database"};
    std::string loader_type{"omop_database"};
    std::unordered_map<std::string, std::any> extractor_config;
    std::unordered_map<std::string, std::any> loader_config;
    core::PipelineConfig pipeline_config;
    bool dry_run{false};
    std::optional<std::chrono::system_clock::time_point> scheduled_time;
};

/**
 * @brief ETL job result
 */
struct ETLJobResult {
    std::string job_id;
    PipelineStatus status;
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    size_t total_records{0};
    size_t processed_records{0};
    size_t error_records{0};
    std::vector<std::string> errors;
    std::unordered_map<std::string, std::any> metrics;
};

/**
 * @brief ETL service for managing ETL operations
 *
 * This service provides high-level ETL operations, job management,
 * and coordination between different components.
 */
class ETLService {
public:
    /**
     * @brief Constructor
     * @param config Configuration manager
     * @param pipeline_manager Pipeline manager
     */
    ETLService(std::shared_ptr<common::ConfigurationManager> config,
               std::shared_ptr<core::PipelineManager> pipeline_manager);

    /**
     * @brief Create and start an ETL job
     * @param request Job request
     * @return Job ID
     */
    std::string create_job(const ETLJobRequest& request);

    /**
     * @brief Get job status
     * @param job_id Job ID
     * @return Job result
     */
    std::optional<ETLJobResult> get_job_result(const std::string& job_id);

    /**
     * @brief Get all job results
     * @return Vector of job results
     */
    std::vector<ETLJobResult> get_all_job_results() const;

    /**
     * @brief Cancel a job
     * @param job_id Job ID
     * @return True if cancelled
     */
    bool cancel_job(const std::string& job_id);

    /**
     * @brief Pause a job
     * @param job_id Job ID
     * @return True if paused
     */
    bool pause_job(const std::string& job_id);

    /**
     * @brief Resume a job
     * @param job_id Job ID
     * @return True if resumed
     */
    bool resume_job(const std::string& job_id);

    /**
     * @brief Schedule a job
     * @param request Job request with scheduled time
     * @return Job ID
     */
    std::string schedule_job(const ETLJobRequest& request);

    /**
     * @brief Run ETL for all configured tables
     * @param parallel Whether to run tables in parallel
     * @return Map of table name to job ID
     */
    std::unordered_map<std::string, std::string> run_all_tables(bool parallel = false);

    /**
     * @brief Validate ETL configuration for a table
     * @param table_name Table name
     * @return Validation errors (empty if valid)
     */
    std::vector<std::string> validate_table_config(const std::string& table_name);

    /**
     * @brief Get ETL statistics
     * @return Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const;

    /**
     * @brief Set job completion callback
     * @param callback Callback function
     */
    void set_completion_callback(
        std::function<void(const std::string&, const ETLJobResult&)> callback);

    /**
     * @brief Set job error callback
     * @param callback Callback function
     */
    void set_error_callback(
        std::function<void(const std::string&, const std::exception&)> callback);

protected:
    /**
     * @brief Build pipeline from request
     * @param request Job request
     * @return ETL pipeline
     */
    std::unique_ptr<core::ETLPipeline> build_pipeline(const ETLJobRequest& request);

    /**
     * @brief Create extractor
     * @param type Extractor type
     * @param config Configuration
     * @return Extractor instance
     */
    std::unique_ptr<core::IExtractor> create_extractor(
        const std::string& type,
        const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Create transformer
     * @param table_name Target table name
     * @return Transformer instance
     */
    std::unique_ptr<core::ITransformer> create_transformer(
        const std::string& table_name);

    /**
     * @brief Create loader
     * @param type Loader type
     * @param config Configuration
     * @return Loader instance
     */
    std::unique_ptr<core::ILoader> create_loader(
        const std::string& type,
        const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Generate job ID
     * @return Unique job ID
     */
    std::string generate_job_id();

private:
    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<common::Logger> logger_;

    // Job tracking
    std::unordered_map<std::string, ETLJobResult> job_results_;
    mutable std::mutex results_mutex_;

    // Callbacks
    std::function<void(const std::string&, const ETLJobResult&)> completion_callback_;
    std::function<void(const std::string&, const std::exception&)> error_callback_;

    // Statistics
    std::atomic<size_t> total_jobs_created_{0};
    std::atomic<size_t> total_jobs_completed_{0};
    std::atomic<size_t> total_jobs_failed_{0};
};

/**
 * @brief ETL scheduler service
 *
 * Manages scheduled ETL jobs and recurring tasks.
 */
class ETLScheduler {
public:
    /**
     * @brief Schedule type
     */
    enum class ScheduleType {
        Once,
        Daily,
        Weekly,
        Monthly,
        Cron
    };

    /**
     * @brief Schedule definition
     */
    struct Schedule {
        ScheduleType type;
        std::chrono::system_clock::time_point start_time;
        std::optional<std::chrono::system_clock::time_point> end_time;
        std::string cron_expression;
        std::chrono::minutes interval{0};
    };

    /**
     * @brief Constructor
     * @param etl_service ETL service
     */
    explicit ETLScheduler(std::shared_ptr<ETLService> etl_service);

    /**
     * @brief Destructor
     */
    ~ETLScheduler();

    /**
     * @brief Start the scheduler
     */
    void start();

    /**
     * @brief Stop the scheduler
     */
    void stop();

    /**
     * @brief Schedule a job
     * @param job_id Unique job identifier
     * @param request Job request
     * @param schedule Schedule definition
     */
    void schedule_job(const std::string& job_id,
                     const ETLJobRequest& request,
                     const Schedule& schedule);

    /**
     * @brief Cancel a scheduled job
     * @param job_id Job identifier
     * @return True if cancelled
     */
    bool cancel_scheduled_job(const std::string& job_id);

    /**
     * @brief Get scheduled jobs
     * @return Map of job ID to schedule
     */
    std::unordered_map<std::string, Schedule> get_scheduled_jobs() const;

    /**
     * @brief Get next run time for a job
     * @param job_id Job identifier
     * @return Next run time
     */
    std::optional<std::chrono::system_clock::time_point> get_next_run_time(
        const std::string& job_id) const;

private:
    struct ScheduledJob {
        ETLJobRequest request;
        Schedule schedule;
        std::chrono::system_clock::time_point next_run;
        size_t run_count{0};
    };

    std::shared_ptr<ETLService> etl_service_;
    std::shared_ptr<common::Logger> logger_;

    // Scheduler state
    std::unordered_map<std::string, ScheduledJob> scheduled_jobs_;
    mutable std::mutex jobs_mutex_;

    // Scheduler thread
    std::unique_ptr<std::thread> scheduler_thread_;
    std::condition_variable scheduler_cv_;
    std::atomic<bool> running_{false};

    /**
     * @brief Scheduler loop
     */
    void scheduler_loop();

    /**
     * @brief Calculate next run time
     * @param schedule Schedule definition
     * @param last_run Last run time
     * @return Next run time
     */
    std::chrono::system_clock::time_point calculate_next_run(
        const Schedule& schedule,
        const std::chrono::system_clock::time_point& last_run) const;

    /**
     * @brief Parse cron expression
     * @param expression Cron expression
     * @param reference_time Reference time
     * @return Next run time
     */
    std::chrono::system_clock::time_point parse_cron(
        const std::string& expression,
        const std::chrono::system_clock::time_point& reference_time) const;
};

/**
 * @brief ETL monitoring service
 *
 * Monitors ETL jobs and provides metrics and alerts.
 */
class ETLMonitor {
public:
    /**
     * @brief Alert type
     */
    enum class AlertType {
        JobFailed,
        HighErrorRate,
        SlowPerformance,
        ResourceUsage,
        DataQuality
    };

    /**
     * @brief Alert definition
     */
    struct Alert {
        AlertType type;
        std::string job_id;
        std::string message;
        std::chrono::system_clock::time_point timestamp;
        std::unordered_map<std::string, std::any> details;
    };

    /**
     * @brief Constructor
     * @param etl_service ETL service
     */
    explicit ETLMonitor(std::shared_ptr<ETLService> etl_service);

    /**
     * @brief Start monitoring
     */
    void start();

    /**
     * @brief Stop monitoring
     */
    void stop();

    /**
     * @brief Get current alerts
     * @return Vector of alerts
     */
    std::vector<Alert> get_alerts() const;

    /**
     * @brief Clear alerts
     */
    void clear_alerts();

    /**
     * @brief Set alert callback
     * @param callback Callback function
     */
    void set_alert_callback(std::function<void(const Alert&)> callback);

    /**
     * @brief Get job metrics
     * @param job_id Job ID
     * @return Metrics map
     */
    std::unordered_map<std::string, double> get_job_metrics(
        const std::string& job_id) const;

    /**
     * @brief Get system metrics
     * @return Metrics map
     */
    std::unordered_map<std::string, double> get_system_metrics() const;

    /**
     * @brief Set alert thresholds
     * @param error_rate_threshold Error rate threshold (0-1)
     * @param performance_threshold Performance threshold in records/sec
     * @param memory_threshold Memory usage threshold in MB
     */
    void set_thresholds(double error_rate_threshold = 0.05,
                       double performance_threshold = 100.0,
                       size_t memory_threshold = 1024);

private:
    std::shared_ptr<ETLService> etl_service_;
    std::shared_ptr<common::Logger> logger_;

    // Monitoring state
    std::vector<Alert> alerts_;
    mutable std::mutex alerts_mutex_;

    // Metrics
    std::unordered_map<std::string, std::unordered_map<std::string, double>> job_metrics_;
    mutable std::mutex metrics_mutex_;

    // Thresholds
    double error_rate_threshold_{0.05};
    double performance_threshold_{100.0};
    size_t memory_threshold_{1024};

    // Monitoring thread
    std::unique_ptr<std::thread> monitor_thread_;
    std::atomic<bool> running_{false};

    // Callback
    std::function<void(const Alert&)> alert_callback_;

    /**
     * @brief Monitor loop
     */
    void monitor_loop();

    /**
     * @brief Check job health
     * @param job_id Job ID
     * @param result Job result
     */
    void check_job_health(const std::string& job_id, const ETLJobResult& result);

    /**
     * @brief Create alert
     * @param type Alert type
     * @param job_id Job ID
     * @param message Alert message
     * @param details Additional details
     */
    void create_alert(AlertType type,
                     const std::string& job_id,
                     const std::string& message,
                     const std::unordered_map<std::string, std::any>& details = {});

    /**
     * @brief Collect system metrics
     */
    void collect_system_metrics();
};

} // namespace omop::service