/**
 * @file etl_service.cpp
 * @brief Implementation of high-level ETL service
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "etl_service.h"
#include "core/pipeline.h"
#include "extract/extractor_factory.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "load/batch_loader.h"
#include <filesystem>
#include <fstream>
#include <sstream>
#include <future>
#include <random>
#include <iomanip>

namespace omop::service {

ETLService::ETLService(std::shared_ptr<common::ConfigurationManager> config,
                       std::shared_ptr<core::PipelineManager> pipeline_manager)
    : config_(config), pipeline_manager_(pipeline_manager) {

    logger_ = common::Logger::get("omop-etl-service");
}

std::string ETLService::create_job(const ETLJobRequest& request) {
    logger_->info("Creating ETL job: {}", request.name);

    // Generate job ID
    std::string job_id = generate_job_id();

    // Build pipeline
    auto pipeline = build_pipeline(request);

    // Create job result
    ETLJobResult result;
    result.job_id = job_id;
    result.status = PipelineStatus::Created;
    result.start_time = std::chrono::system_clock::now();
    result.total_records = 0;
    result.processed_records = 0;
    result.error_records = 0;

    // Store job result
    {
        std::lock_guard<std::mutex> lock(results_mutex_);
        job_results_[job_id] = result;
    }

    // Set up progress callback to update job results
    pipeline->register_progress_callback([this, job_id](const core::PipelineExecutionStats& stats) {
        std::lock_guard<std::mutex> lock(results_mutex_);
        auto it = job_results_.find(job_id);
        if (it != job_results_.end()) {
            it->second.status = stats.status;
            it->second.total_records = stats.total_records_processed;
            it->second.processed_records = stats.successful_records;
            it->second.error_records = stats.failed_records;
            if (!stats.errors.empty()) {
                it->second.errors = stats.errors;
            }
            
            // Call completion callback if job finished
            if (stats.status == PipelineStatus::Completed || 
                stats.status == PipelineStatus::Failed) {
                it->second.end_time = std::chrono::system_clock::now();
                if (completion_callback_) {
                    completion_callback_(job_id, it->second);
                }
            }
        }
    });

    // Set up error callback
    pipeline->register_error_callback([this, job_id](const std::string& error, const core::PipelineExecutionStats& stats) {
        logger_->error("Job {} error: {}", job_id, error);
        if (error_callback_) {
            std::runtime_error e(error);
            error_callback_(job_id, e);
        }
    });

    // Submit to pipeline manager
    pipeline_manager_->submit_job(job_id, std::move(pipeline));

    total_jobs_created_++;

    return job_id;
}

std::optional<ETLJobResult> ETLService::get_job_result(const std::string& job_id) {
    std::lock_guard<std::mutex> lock(results_mutex_);
    auto it = job_results_.find(job_id);
    if (it != job_results_.end()) {
        return it->second;
    }
    return std::nullopt;
}

std::vector<ETLJobResult> ETLService::get_all_job_results() const {
    std::lock_guard<std::mutex> lock(results_mutex_);
    std::vector<ETLJobResult> results;
    results.reserve(job_results_.size());

    for (const auto& [job_id, result] : job_results_) {
        results.push_back(result);
    }

    return results;
}

bool ETLService::cancel_job(const std::string& job_id) {
    logger_->info("Cancelling job: {}", job_id);
    return pipeline_manager_->cancel_job(job_id);
}

bool ETLService::pause_job(const std::string& job_id) {
    logger_->info("Pausing job: {}", job_id);
    return pipeline_manager_->pause_job(job_id);
}

bool ETLService::resume_job(const std::string& job_id) {
    logger_->info("Resuming job: {}", job_id);
    return pipeline_manager_->resume_job(job_id);
}

std::string ETLService::schedule_job(const ETLJobRequest& request) {
    // For now, just create the job immediately
    // TODO: Implement actual scheduling
    return create_job(request);
}

std::unordered_map<std::string, std::string> ETLService::run_all_tables(bool parallel) {
    std::unordered_map<std::string, std::string> results;
    logger_->info("Running all tables (parallel: {})", parallel);
    
    // Get all configured table mappings
    auto mappings = config_->get_all_mappings();
    
    if (parallel) {
        std::vector<std::future<std::pair<std::string, std::string>>> futures;
        
        for (const auto& [table_name, mapping] : mappings) {
            futures.push_back(std::async(std::launch::async, [this, table_name, &mapping]() {
                ETLJobRequest request;
                request.name = std::format("ETL_{}_auto", table_name);
                request.source_table = mapping.source_table();
                request.target_table = mapping.target_table();
                std::string job_id = create_job(request);
                return std::make_pair(table_name, job_id);
            }));
        }
        
        for (auto& future : futures) {
            auto [table_name, job_id] = future.get();
            results[table_name] = job_id;
        }
    } else {
        for (const auto& [table_name, mapping] : mappings) {
            ETLJobRequest request;
            request.name = std::format("ETL_{}_auto", table_name);
            request.source_table = mapping.source_table();
            request.target_table = mapping.target_table();
            results[table_name] = create_job(request);
        }
    }
    
    return results;
}

std::vector<std::string> ETLService::validate_table_config(const std::string& table_name) {
    std::vector<std::string> errors;
    logger_->info("Validating table config: {}", table_name);
    
    try {
        auto mapping = config_->get_table_mapping(table_name);
        if (!mapping) {
            errors.push_back(std::format("No mapping found for table: {}", table_name));
            return errors;
        }
        
        // Validate source table exists
        if (mapping->source_table().empty()) {
            errors.push_back("Source table name is empty");
        }
        
        // Validate target table is valid OMOP table
        auto supported_tables = cdm::OmopTableFactory::get_supported_tables();
        if (std::find(supported_tables.begin(), supported_tables.end(), 
                      mapping->target_table()) == supported_tables.end()) {
            errors.push_back(std::format("Target table '{}' is not a valid OMOP table", 
                                       mapping->target_table()));
        }
        
        // Validate transformation rules
        for (const auto& rule : mapping->transformations()) {
            if (rule.type() == common::TransformationRule::Type::Custom && rule.parameters().IsNull()) {
                errors.push_back(std::format("Empty transformation parameters for field: {}", 
                                           rule.source_column()));
            }
        }
        
    } catch (const std::exception& e) {
        errors.push_back(std::format("Validation error: {}", e.what()));
    }
    
    return errors;
}

std::unordered_map<std::string, std::any> ETLService::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;

    stats["total_jobs_created"] = total_jobs_created_.load();
    stats["total_jobs_completed"] = total_jobs_completed_.load();
    stats["total_jobs_failed"] = total_jobs_failed_.load();

    // Count current job statuses
    size_t pending = 0, running = 0, completed = 0, failed = 0;
    {
        std::lock_guard<std::mutex> lock(results_mutex_);
        for (const auto& [job_id, result] : job_results_) {
            switch (result.status) {
                case PipelineStatus::Created:
                    pending++;
                    break;
                case PipelineStatus::Running:
                    running++;
                    break;
                case PipelineStatus::Completed:
                    completed++;
                    break;
                case PipelineStatus::Failed:
                    failed++;
                    break;
                default:
                    break;
            }
        }
    }

    stats["pending_jobs"] = pending;
    stats["running_jobs"] = running;
    stats["completed_jobs"] = completed;
    stats["failed_jobs"] = failed;

    return stats;
}

void ETLService::set_completion_callback(
    std::function<void(const std::string&, const ETLJobResult&)> callback) {
    completion_callback_ = std::move(callback);
}

void ETLService::set_error_callback(
    std::function<void(const std::string&, const std::exception&)> callback) {
    error_callback_ = std::move(callback);
}

std::unique_ptr<core::ETLPipeline> ETLService::build_pipeline(const ETLJobRequest& request) {
    logger_->info("Building pipeline for job: {}", request.name);
    
    // Create pipeline configuration  
    core::PipelineConfig pipeline_config;
    pipeline_config.batch_size = request.pipeline_config.batch_size;
    pipeline_config.max_parallel_batches = request.pipeline_config.max_parallel_batches;
    pipeline_config.stop_on_error = !request.pipeline_config.continue_on_error;
    pipeline_config.error_threshold = request.pipeline_config.error_threshold;
    pipeline_config.enable_checkpointing = request.pipeline_config.enable_checkpointing;
    
    // Use PipelineBuilder for proper architecture
    core::PipelineBuilder builder;
    builder.with_config(pipeline_config);
    
    // Create extractor parameters
    std::unordered_map<std::string, std::any> extractor_params;
    for (const auto& [key, value] : request.extractor_config) {
        extractor_params[key] = value;
    }
    
    // Create loader parameters  
    std::unordered_map<std::string, std::any> loader_params;
    for (const auto& [key, value] : request.loader_config) {
        loader_params[key] = value;
    }
    
    // Create transformer parameters (target table)
    std::unordered_map<std::string, std::any> transformer_params;
    transformer_params["target_table"] = request.target_table;
    
    // Build pipeline with stages using PipelineBuilder
    auto pipeline = builder
        .with_extractor(request.extractor_type, extractor_params)
        .with_transformer("table_mapper", transformer_params) 
        .with_loader(request.loader_type, loader_params)
        .build();
    
    if (!pipeline) {
        throw std::runtime_error("Failed to build pipeline using PipelineBuilder");
    }
    
    // Initialize with ETL-specific configuration
    core::ETLPipelineConfig etl_config;
    etl_config.pipeline_id = generate_job_id();
    etl_config.pipeline_name = request.name;
    etl_config.batch_size = request.pipeline_config.batch_size;
    etl_config.max_parallel_stages = request.pipeline_config.max_parallel_batches;
    etl_config.continue_on_error = !request.pipeline_config.stop_on_error;
    etl_config.error_threshold = request.pipeline_config.error_threshold;
    etl_config.enable_checkpointing = request.pipeline_config.enable_checkpointing;
    
    if (!pipeline->initialize(etl_config)) {
        throw std::runtime_error("Failed to initialize pipeline with ETL configuration");
    }
    
    return pipeline;
}

std::unique_ptr<core::IExtractor> ETLService::create_extractor(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {
    logger_->info("Creating extractor of type: {}", type);
    
    try {
        // Initialize extractors if not already done
        extract::initialize_extractors();
        
        // Merge with default configuration
        auto merged_config = config;
        
        // Add database connection from configuration if needed
        if (type == "database" || type == "postgresql" || type == "mysql") {
            if (merged_config.find("connection") == merged_config.end()) {
                // Create database connection from configuration
                auto db_config = config_->get_source_db();
                extract::IDatabaseConnection::ConnectionParams params;
                params.host = db_config.host();
                params.port = db_config.port();
                params.database = db_config.database();
                params.username = db_config.username();
                params.password = db_config.password();
                
                std::string db_type = (db_config.type() == common::DatabaseConfig::Type::PostgreSQL) ? "postgresql" : "mysql";
                auto connection = extract::DatabaseConnectionFactory::instance().create(db_type, params);
                merged_config["connection"] = connection.get();
            }
        }
        
        return extract::create_extractor(type, merged_config);
        
    } catch (const std::exception& e) {
        logger_->error("Failed to create extractor: {}", e.what());
        throw;
    }
}

std::unique_ptr<core::ITransformer> ETLService::create_transformer(
    const std::string& table_name) {
    logger_->info("Creating transformer for table: {}", table_name);
    
    try {
        // Get table mapping configuration
        auto mapping = config_->get_table_mapping(table_name);
        if (!mapping) {
            throw std::runtime_error(std::format("No mapping found for table: {}", table_name));
        }
        
        // Create transformation engine
        auto engine = std::make_unique<transform::TransformationEngine>();
        
        // Initialize with configuration
        std::unordered_map<std::string, std::any> init_config;
        init_config["mapping"] = *mapping;
        init_config["target_table"] = table_name;
        
        // Create processing context
        core::ProcessingContext context;
        engine->initialize(init_config, context);
        
        return engine;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to create transformer: {}", e.what());
        throw;
    }
}

std::unique_ptr<core::ILoader> ETLService::create_loader(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {
    logger_->info("Creating loader of type: {}", type);
    
    try {
        // Register loaders
        load::LoaderFactory::register_loaders();
        
        // Create database connection for database loaders
        if (type == "database" || type == "omop_database") {
            auto db_config = config_->get_target_db();
            extract::IDatabaseConnection::ConnectionParams params;
            params.host = db_config.host();
            params.port = db_config.port();
            params.database = db_config.database();
            params.username = db_config.username();
            params.password = db_config.password();
            
            auto connection = extract::DatabaseConnectionFactory::instance().create(
                db_config.type() == common::DatabaseConfig::Type::PostgreSQL ? "postgresql" : "mysql", params);
            
            load::DatabaseLoaderOptions options;
            if (config.find("batch_size") != config.end()) {
                options.batch_size = std::any_cast<size_t>(config.at("batch_size"));
            }
            
            return load::LoaderFactory::create(type, std::move(connection), options);
        }
        
        // For other loader types, use factory directly
        return core::ComponentFactory<core::ILoader>().create(type);
        
    } catch (const std::exception& e) {
        logger_->error("Failed to create loader: {}", e.what());
        throw;
    }
}

std::string ETLService::generate_job_id() {
    // Generate unique job ID with timestamp and random component
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::mt19937 gen([]() -> std::mt19937::result_type {
        try {
            return std::random_device{}();
        } catch (...) {
            return static_cast<std::mt19937::result_type>(
                std::chrono::steady_clock::now().time_since_epoch().count()
            );
        }
    }());
    std::uniform_int_distribution<> dis(1000, 9999);
    
    std::stringstream ss;
    ss << "job_" << std::put_time(std::gmtime(&time_t), "%Y%m%d_%H%M%S") 
       << "_" << dis(gen);
    
    return ss.str();
}

} // namespace omop::service