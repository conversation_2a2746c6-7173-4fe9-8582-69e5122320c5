#include "transform_service.h"
#include "extract/database_connector.h"
#include <future>

namespace omop::service {

TransformService::TransformService(std::shared_ptr<common::ConfigurationManager> config)
    : config_(config), logger_(common::Logger::get("omop-transform-service")) {
    
    service_start_time_ = std::chrono::steady_clock::now();
    logger_->info("Transform service initialised");
    
    // Initialize vocabulary service
    initialize_vocabulary_service();
}

TransformService::~TransformService() {
    logger_->info("Transform service shutting down");
}

std::future<TransformResponse> TransformService::transform(const TransformRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        TransformResponse response;
        response.request_id = request.request_id;
        response.timestamp = std::chrono::system_clock::now();
        
        total_requests_++;
        
        try {
            logger_->info("Processing transform request for job: {}", request.job_id);
            
            // Get or create session
            auto& session = get_or_create_session(request.job_id, request.target_table);
            
            // Transform batch
            auto transformed_batch = session.transformer->transform_batch(
                request.input_records, session.context);
            
            // Count results
            size_t successful = 0;
            size_t failed = 0;
            
            for (const auto& record : request.input_records) {
                // Validate each transformed record
                if (!transformed_batch.isEmpty()) {
                    auto validation_result = session.transformer->validate(record);
                    session.total_validations++;
                    
                    if (validation_result.is_valid()) {
                        successful++;
                    } else {
                        failed++;
                        session.validation_failures++;
                        
                        // Add validation errors to response
                        for (const auto& error : validation_result.errors()) {
                            response.validation_errors.push_back(
                                std::format("{}: {}", error.field_name, error.error_message));
                        }
                    }
                }
            }
            
            // Update statistics
            session.total_transformed += successful;
            session.total_errors += failed;
            session.last_accessed = std::chrono::steady_clock::now();
            
            // Prepare response
            response.transformed_records = std::move(transformed_batch);
            response.successful_transformations = successful;
            response.failed_transformations = failed;
            response.success = true;
            
            successful_requests_++;
            
        } catch (const std::exception& e) {
            logger_->error("Transform request failed: {}", e.what());
            response.success = false;
            response.error_message = e.what();
            failed_requests_++;
        }
        
        return response;
    });
}

std::future<ServiceResponse> TransformService::validate_config(const ServiceRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        ServiceResponse response;
        response.request_id = request.request_id;
        response.timestamp = std::chrono::system_clock::now();
        
        try {
            auto table_name_it = request.parameters.find("table_name");
            if (table_name_it == request.parameters.end()) {
                throw std::runtime_error("Missing table_name parameter");
            }
            
            std::string table_name = std::any_cast<std::string>(table_name_it->second);
            
            // Validate table mapping exists
            auto mapping = config_->get_table_mapping(table_name);
            if (!mapping) {
                response.success = false;
                response.error_message = std::format("No mapping found for table: {}", table_name);
                return response;
            }
            
            // Validate transformation rules
            std::vector<std::string> validation_errors;
            for (const auto& rule : mapping->transformations()) {
                if (rule.type.empty()) {
                    validation_errors.push_back(
                        std::format("Empty transformation type for field: {}", rule.source_field));
                }
            }
            
            response.success = validation_errors.empty();
            if (!response.success) {
                response.data["validation_errors"] = validation_errors;
            }
            
        } catch (const std::exception& e) {
            response.success = false;
            response.error_message = e.what();
        }
        
        return response;
    });
}

std::future<ServiceResponse> TransformService::load_vocabularies(const ServiceRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        ServiceResponse response;
        response.request_id = request.request_id;
        response.timestamp = std::chrono::system_clock::now();
        
        try {
            auto mapping_file_it = request.parameters.find("mapping_file");
            if (mapping_file_it != request.parameters.end()) {
                std::string mapping_file = std::any_cast<std::string>(mapping_file_it->second);
                vocabulary_service_->load_mappings(mapping_file);
                response.success = true;
                response.data["mappings_loaded"] = true;
            } else {
                response.success = false;
                response.error_message = "Missing mapping_file parameter";
            }
            
        } catch (const std::exception& e) {
            response.success = false;
            response.error_message = e.what();
        }
        
        return response;
    });
}

std::future<ServiceResponse> TransformService::get_statistics(const std::string& job_id) {
    return std::async(std::launch::async, [this, job_id]() {
        ServiceResponse response;
        response.timestamp = std::chrono::system_clock::now();
        
        try {
            std::lock_guard<std::mutex> lock(sessions_mutex_);
            auto it = sessions_.find(job_id);
            if (it != sessions_.end()) {
                response.success = true;
                response.data["total_transformed"] = it->second.total_transformed;
                response.data["total_errors"] = it->second.total_errors;
                response.data["total_validations"] = it->second.total_validations;
                response.data["validation_failures"] = it->second.validation_failures;
                response.data["validation_success_rate"] = 
                    it->second.total_validations > 0 ? 
                    1.0 - (double)it->second.validation_failures / it->second.total_validations : 1.0;
            } else {
                response.success = false;
                response.error_message = "Session not found";
            }
            
        } catch (const std::exception& e) {
            response.success = false;
            response.error_message = e.what();
        }
        
        return response;
    });
}

std::future<HealthCheckResponse> TransformService::health_check(const HealthCheckRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        HealthCheckResponse response;
        response.healthy = true;
        response.status = "healthy";
        
        auto uptime = std::chrono::steady_clock::now() - service_start_time_;
        auto uptime_seconds = std::chrono::duration_cast<std::chrono::seconds>(uptime).count();
        
        response.metrics["uptime_seconds"] = uptime_seconds;
        response.metrics["total_requests"] = total_requests_.load();
        response.metrics["successful_requests"] = successful_requests_.load();
        response.metrics["failed_requests"] = failed_requests_.load();
        response.metrics["active_sessions"] = sessions_.size();
        response.metrics["vocabulary_cache_size"] = vocabulary_service_->get_cache_stats().cache_size;
        
        return response;
    });
}

std::future<ServiceResponse> TransformService::finalize_session(const ServiceRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        ServiceResponse response;
        response.request_id = request.request_id;
        response.timestamp = std::chrono::system_clock::now();
        
        try {
            std::lock_guard<std::mutex> lock(sessions_mutex_);
            auto it = sessions_.find(request.job_id);
            if (it != sessions_.end()) {
                sessions_.erase(it);
                response.success = true;
                response.data["session_removed"] = true;
            } else {
                response.success = false;
                response.error_message = "Session not found";
            }
        } catch (const std::exception& e) {
            response.success = false;
            response.error_message = e.what();
        }
        
        return response;
    });
}

TransformService::TransformSession& TransformService::get_or_create_session(
    const std::string& job_id, const std::string& target_table) {
    
    std::lock_guard<std::mutex> lock(sessions_mutex_);
    
    auto it = sessions_.find(job_id);
    if (it != sessions_.end()) {
        return it->second;
    }
    
    // Create new session
    TransformSession session;
    session.transformer = create_transformer(target_table);
    session.context.set_job_id(job_id);
    session.created_at = std::chrono::steady_clock::now();
    session.last_accessed = session.created_at;
    
    // Initialize transformer
    std::unordered_map<std::string, std::any> init_config;
    init_config["table_name"] = target_table;
    session.transformer->initialize(init_config, session.context);
    
    auto [inserted_it, success] = sessions_.emplace(job_id, std::move(session));
    return inserted_it->second;
}

std::unique_ptr<core::ITransformer> TransformService::create_transformer(
    const std::string& target_table) {
    
    auto mapping = config_->get_table_mapping(target_table);
    if (!mapping) {
        throw std::runtime_error(std::format("No mapping found for table: {}", target_table));
    }
    
    auto engine = std::make_unique<transform::TransformationEngine>();
    return engine;
}

void TransformService::initialize_vocabulary_service() {
    // Create database connection for vocabulary lookups
    auto db_config = config_->get_target_db();
    extract::IDatabaseConnection::ConnectionParams params;
    params.host = db_config.host();
    params.port = db_config.port();
    params.database = db_config.database();
    params.username = db_config.username();
    params.password = db_config.password();
    
    auto connection = extract::DatabaseConnectionFactory::instance().create(
        db_config.database_type(), params);
    
    vocabulary_service_ = std::make_unique<transform::VocabularyService>(std::move(connection));
    vocabulary_service_->initialize();
}

std::unique_ptr<ITransformService> TransformServiceFactory::create(
    std::shared_ptr<common::ConfigurationManager> config) {
    return std::make_unique<TransformService>(config);
}

} // namespace omop::service

