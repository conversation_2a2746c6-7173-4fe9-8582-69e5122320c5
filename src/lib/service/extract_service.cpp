#include "extract_service.h"
#include "extract/database_connector.h"
#include <future>
#include <thread>

namespace omop::service {

ExtractService::ExtractService(std::shared_ptr<common::ConfigurationManager> config)
    : config_(config), logger_(common::Logger::get("omop-extract-service")) {
    
    service_start_time_ = std::chrono::steady_clock::now();
    logger_->info("Extract service initialised");
    
    // Start cleanup thread
    std::thread cleanup_thread([this]() {
        while (true) {
            std::this_thread::sleep_for(std::chrono::minutes(5));
            cleanup_expired_sessions();
        }
    });
    cleanup_thread.detach();
}

ExtractService::~ExtractService() {
    logger_->info("Extract service shutting down");
}

std::future<ExtractResponse> ExtractService::extract(const ExtractRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        ExtractResponse response;
        response.request_id = request.request_id;
        response.timestamp = std::chrono::system_clock::now();
        
        total_requests_++;
        
        try {
            logger_->info("Processing extract request for job: {}", request.job_id);
            
            // Get or create session
            auto& session = get_or_create_session(request.job_id);
            
            // Initialize if needed
            if (!session.initialised) {
                auto init_config = request.extractor_config;
                init_config["table"] = request.source_table;
                
                session.extractor->initialize(init_config, session.context);
                session.initialised = true;
            }
            
            // Extract batch
            auto batch = session.extractor->extract_batch(request.batch_size, session.context);
            
            // Update statistics
            session.total_extracted += batch.size();
            session.last_accessed = std::chrono::steady_clock::now();
            
            // Prepare response
            response.records = std::move(batch);
            response.total_records = session.total_extracted;
            response.has_more_data = session.extractor->has_more_data();
            response.statistics = session.extractor->get_statistics();
            response.success = true;
            
            successful_requests_++;
            
        } catch (const std::exception& e) {
            logger_->error("Extract request failed: {}", e.what());
            response.success = false;
            response.error_message = e.what();
            failed_requests_++;
        }
        
        return response;
    });
}

std::future<ServiceResponse> ExtractService::initialize_source(const ServiceRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        ServiceResponse response;
        response.request_id = request.request_id;
        response.timestamp = std::chrono::system_clock::now();
        
        try {
            logger_->info("Initializing source for job: {}", request.job_id);
            
            // Get extractor type from parameters
            auto type_it = request.parameters.find("extractor_type");
            if (type_it == request.parameters.end()) {
                throw std::runtime_error("Missing extractor_type parameter");
            }
            std::string extractor_type = std::any_cast<std::string>(type_it->second);
            
            // Create new session
            auto& session = get_or_create_session(request.job_id);
            session.extractor = create_extractor(extractor_type, request.parameters);
            session.context.set_job_id(request.job_id);
            
            response.success = true;
            response.data["session_created"] = true;
            
        } catch (const std::exception& e) {
            logger_->error("Failed to initialize source: {}", e.what());
            response.success = false;
            response.error_message = e.what();
        }
        
        return response;
    });
}

std::future<ServiceResponse> ExtractService::finalize_source(const ServiceRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        ServiceResponse response;
        response.request_id = request.request_id;
        response.timestamp = std::chrono::system_clock::now();
        
        try {
            logger_->info("Finalizing source for job: {}", request.job_id);
            
            std::lock_guard<std::mutex> lock(sessions_mutex_);
            auto it = sessions_.find(request.job_id);
            if (it != sessions_.end()) {
                it->second.extractor->finalize(it->second.context);
                sessions_.erase(it);
                response.success = true;
                response.data["session_removed"] = true;
            } else {
                response.success = false;
                response.error_message = "Session not found";
            }
            
        } catch (const std::exception& e) {
            logger_->error("Failed to finalize source: {}", e.what());
            response.success = false;
            response.error_message = e.what();
        }
        
        return response;
    });
}

std::future<ServiceResponse> ExtractService::get_statistics(const std::string& job_id) {
    return std::async(std::launch::async, [this, job_id]() {
        ServiceResponse response;
        response.timestamp = std::chrono::system_clock::now();
        
        try {
            std::lock_guard<std::mutex> lock(sessions_mutex_);
            auto it = sessions_.find(job_id);
            if (it != sessions_.end()) {
                response.success = true;
                response.data = it->second.extractor->get_statistics();
                response.data["total_extracted"] = it->second.total_extracted;
                response.data["total_errors"] = it->second.total_errors;
            } else {
                response.success = false;
                response.error_message = "Session not found";
            }
            
        } catch (const std::exception& e) {
            response.success = false;
            response.error_message = e.what();
        }
        
        return response;
    });
}

std::future<HealthCheckResponse> ExtractService::health_check(const HealthCheckRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        HealthCheckResponse response;
        response.healthy = true;
        response.status = "healthy";
        
        // Calculate service metrics
        auto uptime = std::chrono::steady_clock::now() - service_start_time_;
        auto uptime_seconds = std::chrono::duration_cast<std::chrono::seconds>(uptime).count();
        
        response.metrics["uptime_seconds"] = uptime_seconds;
        response.metrics["total_requests"] = total_requests_.load();
        response.metrics["successful_requests"] = successful_requests_.load();
        response.metrics["failed_requests"] = failed_requests_.load();
        response.metrics["active_sessions"] = sessions_.size();
        
        // Check if service is responsive
        if (failed_requests_ > successful_requests_ * 2) {
            response.healthy = false;
            response.status = "degraded - high failure rate";
        }
        
        return response;
    });
}

ExtractService::ExtractorSession& ExtractService::get_or_create_session(const std::string& job_id) {
    std::lock_guard<std::mutex> lock(sessions_mutex_);
    
    auto it = sessions_.find(job_id);
    if (it != sessions_.end()) {
        return it->second;
    }
    
    // Create new session
    ExtractorSession session;
    session.created_at = std::chrono::steady_clock::now();
    session.last_accessed = session.created_at;
    
    auto [inserted_it, success] = sessions_.emplace(job_id, std::move(session));
    return inserted_it->second;
}

std::unique_ptr<core::IExtractor> ExtractService::create_extractor(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {
    
    // Initialize extractors
    extract::initialize_extractors();
    
    // Handle database extractors
    if (type == "database" || type == "postgresql" || type == "mysql") {
        auto db_config = config_->get_source_db();
        extract::IDatabaseConnection::ConnectionParams params;
        params.host = db_config.host();
        params.port = db_config.port();
        params.database = db_config.database();
        params.username = db_config.username();
        params.password = db_config.password();
        
        auto connection = extract::DatabaseConnectionFactory::instance().create(
            db_config.database_type(), params);
        
        auto db_extractor = std::make_unique<extract::DatabaseExtractor>(std::move(connection));
        return db_extractor;
    }
    
    // Create other extractor types
    return extract::create_extractor(type, config);
}

void ExtractService::cleanup_expired_sessions() {
    std::lock_guard<std::mutex> lock(sessions_mutex_);
    
    auto now = std::chrono::steady_clock::now();
    auto expired_threshold = now - session_timeout_;
    
    for (auto it = sessions_.begin(); it != sessions_.end(); ) {
        if (it->second.last_accessed < expired_threshold) {
            logger_->info("Cleaning up expired session: {}", it->first);
            it->second.extractor->finalize(it->second.context);
            it = sessions_.erase(it);
        } else {
            ++it;
        }
    }
}

std::unique_ptr<IExtractService> ExtractServiceFactory::create(
    std::shared_ptr<common::ConfigurationManager> config) {
    return std::make_unique<ExtractService>(config);
}

} // namespace omop::service
