#include "performance_profiler.h"
#include <algorithm>
#include <iomanip>
#include <sstream>
#include <fstream>
#include <numeric>
#include <limits>
#include <cmath>

#ifdef __linux__
#include <sys/resource.h>
#include <unistd.h>
#include <cstring>
#elif defined(__APPLE__)
#include <mach/mach.h>
#include <mach/mach_time.h>
#include <sys/sysctl.h>
#elif defined(_WIN32)
#include <windows.h>
#include <psapi.h>
#endif

namespace omop::extract {

PerformanceProfiler::PerformanceProfiler(const BenchmarkConfig& config)
    : config_(config)
    , start_time_(std::chrono::high_resolution_clock::now())
    , batch_start_time_(start_time_)
    , io_start_time_(start_time_)
    , parse_start_time_(start_time_)
    , conversion_start_time_(start_time_) {
    reset();
}

PerformanceProfiler::~PerformanceProfiler() = default;

void PerformanceProfiler::start_monitoring(const std::string& description) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    if (!monitoring_active_) {
        monitoring_active_ = true;
        start_time_ = std::chrono::high_resolution_clock::now();
        if (!description.empty()) {
            // Could store description for reporting
        }
    }
}

void PerformanceProfiler::stop_monitoring() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    if (monitoring_active_) {
        auto end_time = std::chrono::high_resolution_clock::now();
        metrics_.total_time = end_time - start_time_;
        monitoring_active_ = false;
        update_computed_metrics();
    }
}

void PerformanceProfiler::start_batch(size_t batch_size) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    if (!batch_active_) {
        batch_active_ = true;
        batch_start_time_ = std::chrono::high_resolution_clock::now();
        metrics_.total_batches++;
    }
}

void PerformanceProfiler::end_batch(size_t records_processed, size_t errors) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    if (batch_active_) {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto batch_time = std::chrono::duration<double>(end_time - batch_start_time_).count();
        
        batch_times_.push_back(batch_time);
        batch_sizes_.push_back(records_processed);
        batch_errors_.push_back(errors);
        
        metrics_.total_records += records_processed;
        metrics_.parsing_errors += errors;
        
        batch_active_ = false;
        update_computed_metrics();
    }
}

void PerformanceProfiler::start_io() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    if (!io_active_) {
        io_active_ = true;
        io_start_time_ = std::chrono::high_resolution_clock::now();
    }
}

void PerformanceProfiler::end_io(size_t bytes_transferred) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    if (io_active_) {
        auto end_time = std::chrono::high_resolution_clock::now();
        metrics_.io_time += end_time - io_start_time_;
        metrics_.total_bytes += bytes_transferred;
        metrics_.disk_reads++;
        io_active_ = false;
    }
}

void PerformanceProfiler::start_parse() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    if (!parse_active_) {
        parse_active_ = true;
        parse_start_time_ = std::chrono::high_resolution_clock::now();
    }
}

void PerformanceProfiler::end_parse(size_t records_parsed, size_t errors) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    if (parse_active_) {
        auto end_time = std::chrono::high_resolution_clock::now();
        metrics_.parse_time += end_time - parse_start_time_;
        metrics_.parsing_errors += errors;
        parse_active_ = false;
    }
}

void PerformanceProfiler::start_conversion() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    if (!conversion_active_) {
        conversion_active_ = true;
        conversion_start_time_ = std::chrono::high_resolution_clock::now();
    }
}

void PerformanceProfiler::end_conversion(size_t records_converted, size_t errors) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    if (conversion_active_) {
        auto end_time = std::chrono::high_resolution_clock::now();
        metrics_.conversion_time += end_time - conversion_start_time_;
        metrics_.conversion_errors += errors;
        conversion_active_ = false;
    }
}

void PerformanceProfiler::record_memory_allocation(size_t bytes) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    current_allocated_memory_ += bytes;
    peak_allocated_memory_ = std::max(peak_allocated_memory_.load(), current_allocated_memory_.load());
    metrics_.memory_allocations++;
    metrics_.peak_memory_usage = peak_allocated_memory_.load();
    metrics_.current_memory_usage = current_allocated_memory_.load();
}

void PerformanceProfiler::record_memory_deallocation(size_t bytes) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    if (current_allocated_memory_ >= bytes) {
        current_allocated_memory_ -= bytes;
    } else {
        current_allocated_memory_ = 0;
    }
    metrics_.memory_deallocations++;
    metrics_.current_memory_usage = current_allocated_memory_.load();
}

void PerformanceProfiler::update_memory_usage() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    metrics_.current_memory_usage = get_current_memory_usage();
    metrics_.peak_memory_usage = std::max(metrics_.peak_memory_usage, metrics_.current_memory_usage);
}

void PerformanceProfiler::record_quality_metrics(size_t null_count, size_t duplicate_count, double quality_score) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    metrics_.null_values += null_count;
    metrics_.duplicate_records += duplicate_count;
    metrics_.data_quality_score = std::min(1.0, std::max(0.0, quality_score));
}

PerformanceMetrics PerformanceProfiler::get_metrics() const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    return metrics_;
}

bool PerformanceProfiler::meets_performance_target() const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    if (metrics_.total_time.count() > 0) {
        double current_rps = metrics_.total_records / metrics_.total_time.count();
        return current_rps >= config_.target_records_per_second;
    }
    return false;
}

std::string PerformanceProfiler::generate_report(const std::string& format) const {
    if (format == "json") {
        return generate_json_report();
    } else if (format == "csv") {
        return generate_csv_report();
    } else {
        return generate_text_report();
    }
}

void PerformanceProfiler::save_report(const std::string& filepath, const std::string& format) const {
    std::ofstream file(filepath);
    if (file.is_open()) {
        file << generate_report(format);
        file.close();
    }
}

std::string PerformanceProfiler::compare_with_baseline(const PerformanceMetrics& baseline) const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    std::ostringstream oss;
    
    oss << "Performance Comparison Report\n";
    oss << "=============================\n\n";
    
    // Timing comparison
    if (baseline.total_time.count() > 0) {
        double time_ratio = metrics_.total_time.count() / baseline.total_time.count();
        oss << "Total Time: " << (time_ratio < 1.0 ? "IMPROVED" : "DEGRADED") 
            << " (" << std::fixed << std::setprecision(2) << (time_ratio * 100) << "% of baseline)\n";
    }
    
    // Throughput comparison
    if (baseline.records_per_second > 0) {
        double throughput_ratio = metrics_.records_per_second / baseline.records_per_second;
        oss << "Throughput: " << (throughput_ratio > 1.0 ? "IMPROVED" : "DEGRADED")
            << " (" << std::fixed << std::setprecision(2) << (throughput_ratio * 100) << "% of baseline)\n";
    }
    
    // Memory comparison
    if (baseline.peak_memory_usage > 0) {
        double memory_ratio = static_cast<double>(metrics_.peak_memory_usage) / baseline.peak_memory_usage;
        oss << "Memory Usage: " << (memory_ratio < 1.0 ? "IMPROVED" : "DEGRADED")
            << " (" << std::fixed << std::setprecision(2) << (memory_ratio * 100) << "% of baseline)\n";
    }
    
    return oss.str();
}

void PerformanceProfiler::reset() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    metrics_ = PerformanceMetrics{};
    batch_times_.clear();
    batch_sizes_.clear();
    batch_errors_.clear();
    current_allocated_memory_ = 0;
    peak_allocated_memory_ = 0;
    monitoring_active_ = false;
    batch_active_ = false;
    io_active_ = false;
    parse_active_ = false;
    conversion_active_ = false;
}

size_t PerformanceProfiler::get_current_memory_usage() const {
#ifdef __linux__
    FILE* file = fopen("/proc/self/status", "r");
    if (file) {
        char line[128];
        while (fgets(line, 128, file) != nullptr) {
            if (strncmp(line, "VmRSS:", 6) == 0) {
                long rss;
                if (sscanf(line, "VmRSS: %ld", &rss) == 1) {
                    fclose(file);
                    return static_cast<size_t>(rss * 1024); // Convert KB to bytes
                }
            }
        }
        fclose(file);
    }
#elif defined(__APPLE__)
    struct task_basic_info t_info;
    mach_msg_type_number_t t_info_count = TASK_BASIC_INFO_COUNT;
    if (task_info(mach_task_self(), TASK_BASIC_INFO, (task_info_t)&t_info, &t_info_count) == KERN_SUCCESS) {
        return t_info.resident_size;
    }
#elif defined(_WIN32)
    PROCESS_MEMORY_COUNTERS_EX pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), (PROCESS_MEMORY_COUNTERS*)&pmc, sizeof(pmc))) {
        return pmc.WorkingSetSize;
    }
#endif
    return 0;
}

double PerformanceProfiler::get_cpu_usage() const {
    // This is a simplified implementation
    // In a real implementation, you would track CPU time over wall clock time
    return 0.0;
}

void PerformanceProfiler::update_computed_metrics() {
    if (metrics_.total_time.count() > 0) {
        metrics_.records_per_second = metrics_.total_records / metrics_.total_time.count();
        metrics_.bytes_per_second = metrics_.total_bytes / metrics_.total_time.count();
        metrics_.megabytes_per_second = metrics_.bytes_per_second / (1024.0 * 1024.0);
    }
    
    if (!batch_times_.empty()) {
        metrics_.avg_batch_time = std::accumulate(batch_times_.begin(), batch_times_.end(), 0.0) / batch_times_.size();
        metrics_.min_batch_time = *std::min_element(batch_times_.begin(), batch_times_.end());
        metrics_.max_batch_time = *std::max_element(batch_times_.begin(), batch_times_.end());
    }
    
    if (!batch_sizes_.empty()) {
        metrics_.avg_batch_size = std::accumulate(batch_sizes_.begin(), batch_sizes_.end(), size_t{0}) / batch_sizes_.size();
    }
}

std::string PerformanceProfiler::generate_json_report() const {
    std::ostringstream oss;
    oss << "{\n";
    oss << "  \"performance_metrics\": {\n";
    oss << "    \"timing\": {\n";
    oss << "      \"total_time\": " << std::fixed << std::setprecision(6) << metrics_.total_time.count() << ",\n";
    oss << "      \"io_time\": " << metrics_.io_time.count() << ",\n";
    oss << "      \"parse_time\": " << metrics_.parse_time.count() << ",\n";
    oss << "      \"conversion_time\": " << metrics_.conversion_time.count() << "\n";
    oss << "    },\n";
    oss << "    \"throughput\": {\n";
    oss << "      \"total_records\": " << metrics_.total_records << ",\n";
    oss << "      \"total_bytes\": " << metrics_.total_bytes << ",\n";
    oss << "      \"records_per_second\": " << std::fixed << std::setprecision(2) << metrics_.records_per_second << ",\n";
    oss << "      \"megabytes_per_second\": " << metrics_.megabytes_per_second << "\n";
    oss << "    },\n";
    oss << "    \"memory\": {\n";
    oss << "      \"peak_usage\": " << metrics_.peak_memory_usage << ",\n";
    oss << "      \"current_usage\": " << metrics_.current_memory_usage << ",\n";
    oss << "      \"allocations\": " << metrics_.memory_allocations << ",\n";
    oss << "      \"deallocations\": " << metrics_.memory_deallocations << "\n";
    oss << "    },\n";
    oss << "    \"quality\": {\n";
    oss << "      \"data_quality_score\": " << std::fixed << std::setprecision(3) << metrics_.data_quality_score << ",\n";
    oss << "      \"null_values\": " << metrics_.null_values << ",\n";
    oss << "      \"duplicate_records\": " << metrics_.duplicate_records << "\n";
    oss << "    }\n";
    oss << "  }\n";
    oss << "}\n";
    return oss.str();
}

std::string PerformanceProfiler::generate_csv_report() const {
    std::ostringstream oss;
    oss << "Metric,Value\n";
    oss << "Total Time (s)," << std::fixed << std::setprecision(6) << metrics_.total_time.count() << "\n";
    oss << "IO Time (s)," << metrics_.io_time.count() << "\n";
    oss << "Parse Time (s)," << metrics_.parse_time.count() << "\n";
    oss << "Conversion Time (s)," << metrics_.conversion_time.count() << "\n";
    oss << "Total Records," << metrics_.total_records << "\n";
    oss << "Total Bytes," << metrics_.total_bytes << "\n";
    oss << "Records per Second," << std::fixed << std::setprecision(2) << metrics_.records_per_second << "\n";
    oss << "Megabytes per Second," << metrics_.megabytes_per_second << "\n";
    oss << "Peak Memory (bytes)," << metrics_.peak_memory_usage << "\n";
    oss << "Current Memory (bytes)," << metrics_.current_memory_usage << "\n";
    oss << "Data Quality Score," << std::fixed << std::setprecision(3) << metrics_.data_quality_score << "\n";
    return oss.str();
}

std::string PerformanceProfiler::generate_text_report() const {
    std::ostringstream oss;
    oss << "Performance Report\n";
    oss << "==================\n\n";
    
    oss << "Timing Metrics:\n";
    oss << "  Total Time: " << std::fixed << std::setprecision(6) << metrics_.total_time.count() << " seconds\n";
    oss << "  IO Time: " << metrics_.io_time.count() << " seconds\n";
    oss << "  Parse Time: " << metrics_.parse_time.count() << " seconds\n";
    oss << "  Conversion Time: " << metrics_.conversion_time.count() << " seconds\n\n";
    
    oss << "Throughput Metrics:\n";
    oss << "  Total Records: " << metrics_.total_records << "\n";
    oss << "  Total Bytes: " << metrics_.total_bytes << "\n";
    oss << "  Records per Second: " << std::fixed << std::setprecision(2) << metrics_.records_per_second << "\n";
    oss << "  Megabytes per Second: " << metrics_.megabytes_per_second << "\n\n";
    
    oss << "Memory Metrics:\n";
    oss << "  Peak Memory Usage: " << metrics_.peak_memory_usage << " bytes\n";
    oss << "  Current Memory Usage: " << metrics_.current_memory_usage << " bytes\n";
    oss << "  Memory Allocations: " << metrics_.memory_allocations << "\n";
    oss << "  Memory Deallocations: " << metrics_.memory_deallocations << "\n\n";
    
    oss << "Quality Metrics:\n";
    oss << "  Data Quality Score: " << std::fixed << std::setprecision(3) << metrics_.data_quality_score << "\n";
    oss << "  Null Values: " << metrics_.null_values << "\n";
    oss << "  Duplicate Records: " << metrics_.duplicate_records << "\n\n";
    
    oss << "Batch Metrics:\n";
    oss << "  Total Batches: " << metrics_.total_batches << "\n";
    oss << "  Average Batch Size: " << metrics_.avg_batch_size << "\n";
    oss << "  Average Batch Time: " << std::fixed << std::setprecision(6) << metrics_.avg_batch_time << " seconds\n";
    oss << "  Min Batch Time: " << metrics_.min_batch_time << " seconds\n";
    oss << "  Max Batch Time: " << metrics_.max_batch_time << " seconds\n";
    
    return oss.str();
}

// BenchmarkSuite implementation
template<typename TestFunc>
void BenchmarkSuite::add_benchmark(const std::string& name, 
                                  const BenchmarkConfig& config,
                                  TestFunc test_function,
                                  const std::string& test_description) {
    benchmarks_.push_back({name, config, test_function, test_description, PerformanceMetrics{}});
}

std::unordered_map<std::string, PerformanceMetrics> BenchmarkSuite::run_all_benchmarks() {
    results_.clear();
    for (const auto& benchmark : benchmarks_) {
        PerformanceProfiler profiler(benchmark.config);
        profiler.start_monitoring(benchmark.description);
        benchmark.test_function(profiler);
        profiler.stop_monitoring();
        results_[benchmark.name] = profiler.get_metrics();
    }
    return results_;
}

PerformanceMetrics BenchmarkSuite::run_benchmark(const std::string& name) {
    auto it = std::find_if(benchmarks_.begin(), benchmarks_.end(),
                           [&name](const BenchmarkTest& test) { return test.name == name; });
    if (it != benchmarks_.end()) {
        PerformanceProfiler profiler(it->config);
        profiler.start_monitoring(it->description);
        it->test_function(profiler);
        profiler.stop_monitoring();
        results_[name] = profiler.get_metrics();
        return results_[name];
    }
    return PerformanceMetrics{};
}

std::string BenchmarkSuite::generate_comparison_report() const {
    std::ostringstream oss;
    oss << "Benchmark Comparison Report\n";
    oss << "==========================\n\n";
    
    for (const auto& [name, metrics] : results_) {
        oss << "Benchmark: " << name << "\n";
        oss << "  Records per Second: " << std::fixed << std::setprecision(2) << metrics.records_per_second << "\n";
        oss << "  Total Time: " << metrics.total_time.count() << " seconds\n";
        oss << "  Peak Memory: " << metrics.peak_memory_usage << " bytes\n";
        oss << "  Data Quality: " << std::fixed << std::setprecision(3) << metrics.data_quality_score << "\n\n";
    }
    
    return oss.str();
}

void BenchmarkSuite::save_all_results(const std::string& directory, const std::string& format) const {
    for (const auto& [name, metrics] : results_) {
        std::string filename = directory + "/" + name + "." + format;
        PerformanceProfiler temp_profiler;
        temp_profiler.save_report(filename, format);
    }
}

// ScopedTimer implementation
ScopedTimer::ScopedTimer(PerformanceProfiler& profiler, const std::string& operation_type)
    : profiler_(profiler)
    , operation_type_(operation_type)
    , start_time_(std::chrono::high_resolution_clock::now()) {
    profiler_.start_monitoring(operation_type);
}

ScopedTimer::~ScopedTimer() {
    profiler_.stop_monitoring();
}

} // namespace omop::extract
