#pragma once

#include "csv_extractor.h"
#include <memory>
#include <mutex>
#include <thread>
#include <queue>
#include <condition_variable>
#include <atomic>

namespace omop::extract {

/**
 * @brief Streaming options for large file processing
 */
struct StreamingOptions {
    size_t read_buffer_size{8 * 1024 * 1024};    // 8MB read buffer
    size_t parse_buffer_size{2 * 1024 * 1024};   // 2MB parse buffer
    size_t max_line_size{1024 * 1024};           // 1MB max line size
    size_t prefetch_batches{3};                  // Number of batches to prefetch
    bool async_parsing{true};                    // Enable async parsing
    bool memory_mapped{false};                   // Use memory mapping for very large files
    double memory_limit_ratio{0.1};              // Use max 10% of available RAM
};

/**
 * @brief Advanced streaming CSV extractor for very large files
 * 
 * Optimizations include:
 * - Large read buffers to reduce I/O calls
 * - Asynchronous parsing pipeline
 * - Memory-mapped file access for very large files
 * - Adaptive buffer sizing based on available memory
 * - Prefetching and background processing
 */
class StreamingCsvExtractor : public CsvExtractor {
public:
    /**
     * @brief Constructor
     */
    StreamingCsvExtractor();
    
    /**
     * @brief Destructor
     */
    ~StreamingCsvExtractor();

    /**
     * @brief Initialize the streaming extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                    core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch using streaming optimizations
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                    core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "streaming_csv"; }

    /**
     * @brief Finalize extraction and cleanup
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get streaming statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Connect to the CSV data source with streaming setup
     * @return bool True if connection successful
     */
    bool connect() override;
    
    /**
     * @brief Disconnect from the CSV data source
     */
    void disconnect() override;

    /**
     * @brief Extract a single batch using streaming (implementation)
     * @param batch_size Size of batch to extract
     * @return std::vector<core::Record> Vector of records
     */
    std::vector<core::Record> extractBatchImpl(size_t batch_size) override;

    /**
     * @brief Extract batch from memory-mapped file
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_memory_mapped_batch(size_t batch_size,
                                                   core::ProcessingContext& context);

    /**
     * @brief Extract batch using streaming I/O
     * @param batch_size Maximum number of records to extract  
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_streaming_batch(size_t batch_size,
                                               core::ProcessingContext& context);

private:
    /**
     * @brief Initialize streaming buffers based on file size and available memory
     */
    void initialize_streaming_buffers();
    
    /**
     * @brief Setup memory mapping for very large files
     * @return bool True if memory mapping was successful
     */
    bool setup_memory_mapping();
    
    /**
     * @brief Background thread function for reading data
     */
    void background_reader_thread();
    
    /**
     * @brief Background thread function for parsing data
     */
    void background_parser_thread();
    
    /**
     * @brief Get system available memory
     * @return size_t Available memory in bytes
     */
    size_t get_available_memory() const;
    
    /**
     * @brief Adaptive buffer sizing based on file size and memory
     * @param file_size Size of the file to process
     */
    void adapt_buffer_sizes(size_t file_size);
    
    /**
     * @brief Parse lines from buffer into records
     * @param buffer Buffer containing CSV data
     * @param start_pos Starting position in buffer
     * @param end_pos Ending position in buffer
     * @return std::vector<core::Record> Parsed records
     */
    std::vector<core::Record> parse_buffer_chunk(const std::vector<char>& buffer,
                                                  size_t start_pos, size_t end_pos);
    
    /**
     * @brief Find complete lines in buffer
     * @param buffer Buffer to search
     * @param start_pos Starting position
     * @param end_pos Ending position
     * @return std::vector<std::pair<size_t, size_t>> Line start/end positions
     */
    std::vector<std::pair<size_t, size_t>> find_complete_lines(
        const std::vector<char>& buffer, size_t start_pos, size_t end_pos);

    // Streaming configuration
    StreamingOptions streaming_options_;
    
    // Memory mapping support
    void* mapped_memory_{nullptr};
    size_t mapped_size_{0};
    size_t mapped_offset_{0};
    
    // Streaming buffers
    std::unique_ptr<std::vector<char>> read_buffer_;
    std::unique_ptr<std::vector<char>> parse_buffer_;
    
    // Asynchronous processing
    std::unique_ptr<std::thread> reader_thread_;
    std::unique_ptr<std::thread> parser_thread_;
    
    // Thread synchronization
    mutable std::mutex buffer_mutex_;
    mutable std::mutex batch_queue_mutex_;
    std::condition_variable reader_cv_;
    std::condition_variable parser_cv_;
    std::condition_variable consumer_cv_;
    
    // Background processing state
    std::atomic<bool> stop_threads_{false};
    std::atomic<bool> reader_finished_{false};
    std::atomic<bool> parser_finished_{false};
    
    // Batch queue for prefetching
    std::queue<core::RecordBatch> prefetch_queue_;
    size_t max_prefetch_batches_;
    
    // Performance metrics
    std::atomic<size_t> bytes_read_{0};
    std::atomic<size_t> lines_parsed_{0};
    std::atomic<size_t> buffer_fills_{0};
    std::chrono::high_resolution_clock::time_point last_read_time_;
    std::chrono::high_resolution_clock::time_point last_parse_time_;
};

/**
 * @brief Memory-mapped file reader for very large CSV files
 */
class MemoryMappedCsvReader {
public:
    /**
     * @brief Constructor
     * @param filepath Path to CSV file
     */
    explicit MemoryMappedCsvReader(const std::string& filepath);
    
    /**
     * @brief Destructor
     */
    ~MemoryMappedCsvReader();
    
    /**
     * @brief Map file into memory
     * @return bool True if mapping successful
     */
    bool map_file();
    
    /**
     * @brief Unmap file from memory
     */
    void unmap_file();
    
    /**
     * @brief Get pointer to mapped memory
     * @return const char* Pointer to file data
     */
    const char* data() const { return static_cast<const char*>(mapped_data_); }
    
    /**
     * @brief Get size of mapped file
     * @return size_t File size in bytes
     */
    size_t size() const { return file_size_; }
    
    /**
     * @brief Check if file is mapped
     * @return bool True if file is mapped
     */
    bool is_mapped() const { return mapped_data_ != nullptr; }

private:
    std::string filepath_;
    void* mapped_data_{nullptr};
    size_t file_size_{0};
    int file_descriptor_{-1};
};

} // namespace omop::extract