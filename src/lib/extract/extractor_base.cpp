/**
 * @file extractor_base.cpp
 * @brief Implementation of abstract base class for data extractors
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "extractor_base.h"
#include <chrono>
#include <algorithm>
#include <thread>
#include <memory>
#include <mutex>

#ifdef __linux__
#include <sys/resource.h>
#endif

namespace omop::extract {



// Thread-local storage for error accumulation
thread_local std::vector<std::string> ExtractionErrorPolicy::accumulated_errors_;
thread_local std::mutex ExtractionErrorPolicy::accumulated_errors_mutex_;

// ExtractionErrorPolicy implementation

bool ExtractionErrorPolicy::handle_error(const std::string& error,
                                        const std::any& context,
                                        std::shared_ptr<common::Logger> logger,
                                        ErrorAction action) {
    switch (action) {
        case ErrorAction::THROW_IMMEDIATELY:
            throw common::ExtractionException(error, "extraction");

        case ErrorAction::LOG_AND_CONTINUE:
            logger->error("Extraction error: {}", error);
            if (context.has_value()) {
                try {
                    logger->debug("Error context: {}", std::any_cast<std::string>(context));
                } catch (...) {
                    // Context not convertible to string
                }
            }
            return true;  // Continue processing

        case ErrorAction::ACCUMULATE_ERRORS: {
            std::lock_guard<std::mutex> lock(accumulated_errors_mutex_);
            if (accumulated_errors_.size() < MAX_ACCUMULATED_ERRORS) {
                accumulated_errors_.push_back(error);
                logger->debug("Error accumulated ({}/{}): {}", 
                    accumulated_errors_.size(), MAX_ACCUMULATED_ERRORS, error);
            } else {
                logger->warn("Maximum accumulated errors ({}) reached, discarding: {}", 
                    MAX_ACCUMULATED_ERRORS, error);
            }
            return true;  // Continue processing
        }
    }

    return false;
}

std::vector<std::string> ExtractionErrorPolicy::get_accumulated_errors() {
    std::lock_guard<std::mutex> lock(accumulated_errors_mutex_);
    return accumulated_errors_;
}

void ExtractionErrorPolicy::clear_accumulated_errors() {
    std::lock_guard<std::mutex> lock(accumulated_errors_mutex_);
    accumulated_errors_.clear();
}

ExtractorBase::ExtractorBase(const std::string& name,
                           std::shared_ptr<common::ConfigurationManager> config,
                           std::shared_ptr<common::Logger> logger)
    : name_(name), config_(config), logger_(logger) {

    if (!logger_) {
        logger_ = common::Logger::get("ExtractorBase");
    }
}

void ExtractorBase::initialize(const std::unordered_map<std::string, std::any>& config,
                              core::ProcessingContext& context) {
    if (is_initialised_) {
        logger_->warn("Extractor {} already initialised", name_);
        return;
    }

    logger_->info("Initializing extractor: {}", name_);
    start_time_ = std::chrono::steady_clock::now();

    try {
        // Update options from config
        if (auto it = config.find("error_policy"); it != config.end()) {
            std::string policy = std::any_cast<std::string>(it->second);
            error_action_ = (policy == "throw") ? ExtractionErrorPolicy::ErrorAction::THROW_IMMEDIATELY :
                           (policy == "accumulate") ? ExtractionErrorPolicy::ErrorAction::ACCUMULATE_ERRORS :
                           ExtractionErrorPolicy::ErrorAction::LOG_AND_CONTINUE;
        }
        if (auto it = config.find("batch_size"); it != config.end()) {
            try {
                options_.batch_size = std::any_cast<size_t>(it->second);
            } catch (const std::bad_any_cast& e) {
                logger_->warn("Invalid type for batch_size config, using default: {}", options_.batch_size);
            }
        }
        if (auto it = config.find("max_records"); it != config.end()) {
            try {
                options_.max_records = std::any_cast<size_t>(it->second);
            } catch (const std::bad_any_cast& e) {
                logger_->warn("Invalid type for max_records config, using default: {}", options_.max_records);
            }
        }
        if (auto it = config.find("continue_on_error"); it != config.end()) {
            try {
                options_.continue_on_error = std::any_cast<bool>(it->second);
            } catch (const std::bad_any_cast& e) {
                logger_->warn("Invalid type for continue_on_error config, using default: {}", options_.continue_on_error);
            }
        }
        if (auto it = config.find("validate_schema"); it != config.end()) {
            try {
                options_.validate_schema = std::any_cast<bool>(it->second);
            } catch (const std::bad_any_cast& e) {
                logger_->warn("Invalid type for validate_schema config, using default: {}", options_.validate_schema);
            }
        }

        // Connect to data source
        if (!connect()) {
            logger_->error("Failed to connect to data source for extractor: {}", name_);
            throw std::runtime_error("Failed to connect to data source");
        }

        is_connected_ = true;

        // Validate source schema if enabled
        if (options_.validate_schema) {
            auto validation_result = validateSource();
            if (!validation_result.is_valid()) {
                std::string error_msg = "Unknown error";
                if (!validation_result.errors().empty()) {
                    error_msg = validation_result.errors()[0].error_message;
                }
                logger_->error("Source validation failed for extractor {}: {}", name_, error_msg);
                throw std::runtime_error("Source validation failed");
            }
        }

        is_initialised_ = true;
        was_ever_initialised_ = true;
        logger_->info("Extractor {} initialised successfully", name_);
        stats_.start_time = std::chrono::system_clock::now();

    } catch (const std::exception& e) {
        logger_->error("Exception during extractor initialization: {}", e.what());
        throw;
    }
}

core::RecordBatch ExtractorBase::extract_batch(size_t batch_size, [[maybe_unused]] core::ProcessingContext& context) {
    core::RecordBatch batch(batch_size);

    if (!is_initialised_) {
        logger_->error("Extractor not initialised");
        // Check if it was never initialised vs closed after initialization
        if (!was_ever_initialised_) {
            // Never initialised - throw exception
            throw std::runtime_error("Extractor not initialised");
        } else {
            // Was initialised but then closed - return empty batch
            return batch;
        }
    }

    try {
        // Extract raw batch from source
        auto raw_batch = extractBatchImpl(batch_size);
        common::ScopedPerformanceTimer perf_monitor("batch_processing", stats_.operation_timings, stats_.operation_counts);

        // Process each record
        for (const auto& raw_record : raw_batch) {
            try {
                common::ScopedPerformanceTimer record_monitor("record_conversion", stats_.operation_timings, stats_.operation_counts);
                stats_.total_records++;

                // Convert to standard record format
                auto record = convertToRecord(raw_record);

                // Apply column selection if specified
                if (!options_.columns.empty()) {
                    record = selectColumns(record);
                }

                // Apply filter if specified
                if (!options_.filter_expression.empty() && !applyFilter(record)) {
                    stats_.skipped_records++;
                    continue;
                }

                batch.addRecord(std::move(record));
                stats_.successful_records++;

            } catch (const std::exception& e) {
                stats_.failed_records++;
                bool should_continue = ExtractionErrorPolicy::handle_error(
                    "Record processing error: " + std::string(e.what()), raw_record, logger_, error_action_);
                if (!should_continue) {
                    break;
                }
            }
        }

        current_position_ += raw_batch.size();
        
        // Check if we've reached the end of data
        if (raw_batch.empty() || raw_batch.size() < batch_size) {
            has_more_data_ = false;
        }

    } catch (const std::exception& e) {
        logger_->error("Batch extraction error: {}", e.what());
        if (!options_.continue_on_error) {
            throw;
        }
    }

    return batch;
}

bool ExtractorBase::has_more_data() const {
    return is_connected_ && is_initialised_ && has_more_data_;
}

std::string ExtractorBase::get_type() const {
    return "base";  // Derived classes should override
}

void ExtractorBase::finalize([[maybe_unused]] core::ProcessingContext& context) {
    logger_->info("Finalizing extractor: {}", name_);

    // Calculate final statistics
    auto end_time = std::chrono::steady_clock::now();
    std::chrono::duration<double> elapsed = end_time - start_time_;
    stats_.extraction_time_seconds = elapsed.count();
    stats_.end_time = std::chrono::system_clock::now();
    stats_.calculate_rates();

    // Log comprehensive metrics
    logger_->info("Extraction completed: {} records extracted, {} failed, {:.2f} seconds",
                 stats_.successful_records, stats_.failed_records, stats_.extraction_time_seconds);
    logger_->info("Performance: {:.2f} records/sec, {:.2f} MB/sec",
                 stats_.records_per_second, stats_.bytes_per_second / (1024 * 1024));

    // Log operation timings
    for (const auto& [op, timing] : stats_.operation_timings) {
        auto count = stats_.operation_counts[op];
        logger_->debug("Operation '{}': {} calls, {:.2f}ms total, {:.2f}ms avg",
                      op, count, timing, timing / count);
    }

    close();
}

std::unordered_map<std::string, std::any> ExtractorBase::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;
    stats["total_records"] = stats_.total_records;
    stats["successful_records"] = stats_.successful_records;
    stats["failed_records"] = stats_.failed_records;
    stats["skipped_records"] = stats_.skipped_records;
    stats["extraction_time_seconds"] = stats_.extraction_time_seconds;
    stats["bytes_processed"] = stats_.bytes_processed;
    stats["records_per_second"] = stats_.records_per_second;
    stats["bytes_per_second"] = stats_.bytes_per_second;
    stats["peak_memory_usage"] = stats_.peak_memory_usage;

    // Add timing breakdown
    std::unordered_map<std::string, std::any> timings;
    for (const auto& [op, timing] : stats_.operation_timings) {
        timings[op + "_total_ms"] = timing;
        timings[op + "_count"] = stats_.operation_counts.at(op);
        timings[op + "_avg_ms"] = timing / stats_.operation_counts.at(op);
    }
    stats["operation_timings"] = timings;

    return stats;
}

void ExtractorBase::reset() {
    logger_->info("Resetting extractor: {}", name_);

    current_position_ = 0;
    has_more_data_ = true;
    stats_ = ExtractionStats();

    // Call derived class reset implementation
    resetImpl();

    // Disconnect and reconnect
    if (is_connected_) {
        disconnect();
        is_connected_ = false;
    }

    if (is_initialised_) {
        connect();
        is_connected_ = true;
    }
}

void ExtractorBase::close() {
    logger_->info("Closing extractor: {}", name_);

    if (is_connected_) {
        disconnect();
        is_connected_ = false;
    }

    is_initialised_ = false;
}

void ExtractorBase::handleError(const std::string& error,
                               const std::optional<std::any>& record_context) {
    logger_->error("Extraction error in {}: {}", name_, error);

    // Increment failed records count
    stats_.failed_records++;

    // Count error types
    size_t colon_pos = error.find(':');
    std::string error_type = (colon_pos != std::string::npos)
        ? error.substr(0, colon_pos)
        : "Unknown";

    stats_.error_counts[error_type]++;

    // Log record context if available
    if (record_context.has_value()) {
        logger_->debug("Error context: record at position {}", current_position_);
    }
}

void ExtractorBase::updateProgress(size_t current, size_t total) {
    if (progress_callback_) {
        progress_callback_(current, total);
    }

    // Log progress periodically
    if (current % 10000 == 0 || current == total) {
        if (total > 0) {
            double percent = (static_cast<double>(current) / total) * 100.0;
            logger_->info("Extraction progress: {}/{} ({:.1f}%)", current, total, percent);
        } else {
            logger_->info("Extraction progress: {} records", current);
        }
    }
}

bool ExtractorBase::applyFilter(const core::Record& record) {
    if (options_.filter_expression.empty()) {
        return true;
    }
    
    // Basic filter implementation using simple field comparisons
    // Format: "field_name=value" or "field_name!=value" or "field_name>value" etc.
    std::string expr = options_.filter_expression;
    
    // Find operator
    std::string operators[] = {"!=", "<=", ">=", "=", "<", ">"};
    std::string op;
    size_t op_pos = std::string::npos;
    
    for (const auto& test_op : operators) {
        size_t pos = expr.find(test_op);
        if (pos != std::string::npos) {
            op = test_op;
            op_pos = pos;
            break;
        }
    }
    
    if (op_pos == std::string::npos) {
        logger_->warn("Invalid filter expression: {}", expr);
        return true; // Pass on invalid filter
    }
    
    std::string field_name = expr.substr(0, op_pos);
    std::string value = expr.substr(op_pos + op.length());
    
    // Trim whitespace
    field_name.erase(0, field_name.find_first_not_of(" \t"));
    field_name.erase(field_name.find_last_not_of(" \t") + 1);
    value.erase(0, value.find_first_not_of(" \t"));
    value.erase(value.find_last_not_of(" \t") + 1);
    
    if (!record.hasField(field_name)) {
        return false; // Field not found, fail filter
    }
    
    try {
        auto field_value = record.getField(field_name);
        std::string field_str;
        
        // Convert field value to string for comparison
        if (field_value.type() == typeid(std::string)) {
            field_str = std::any_cast<std::string>(field_value);
        } else if (field_value.type() == typeid(int)) {
            field_str = std::to_string(std::any_cast<int>(field_value));
        } else if (field_value.type() == typeid(long long)) {
            field_str = std::to_string(std::any_cast<long long>(field_value));
        } else if (field_value.type() == typeid(double)) {
            field_str = std::to_string(std::any_cast<double>(field_value));
        } else if (field_value.type() == typeid(bool)) {
            field_str = std::any_cast<bool>(field_value) ? "true" : "false";
        } else {
            logger_->debug("Unsupported field type for filtering: {}", field_name);
            return true; // Pass on unsupported types
        }
        
        // Apply comparison
        if (op == "=") {
            return field_str == value;
        } else if (op == "!=") {
            return field_str != value;
        } else if (op == "<") {
            return field_str < value;
        } else if (op == ">") {
            return field_str > value;
        } else if (op == "<=") {
            return field_str <= value;
        } else if (op == ">=") {
            return field_str >= value;
        }
        
    } catch (const std::exception& e) {
        logger_->warn("Error applying filter '{}': {}", expr, e.what());
        return true; // Pass on error
    }
    
    return true;
}

core::Record ExtractorBase::selectColumns(const core::Record& record) {
    if (options_.columns.empty()) {
        return record;
    }

    core::Record filtered_record;

    // Copy only selected columns
    for (const auto& column : options_.columns) {
        if (record.hasField(column)) {
            filtered_record.setField(column, record.getField(column));
        }
    }

    // Preserve metadata
    filtered_record.setMetadata(record.getMetadata());

    return filtered_record;
}

// ExtractionStats implementation

void ExtractionStats::calculate_rates() {
    if (extraction_time_seconds > 0) {
        records_per_second = static_cast<double>(successful_records) / extraction_time_seconds;
        bytes_per_second = static_cast<double>(bytes_processed) / extraction_time_seconds;
    }

    // Get peak memory usage and total memory allocated
#ifdef __linux__
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        peak_memory_usage = static_cast<size_t>(usage.ru_maxrss * 1024); // Convert KB to bytes
        total_memory_allocated = static_cast<size_t>(usage.ru_inblock * 1024); // Estimate total allocated
    }
#else
    // For non-Linux systems, use a basic estimation
    peak_memory_usage = total_records * 1024; // Rough estimate: 1KB per record
    total_memory_allocated = peak_memory_usage;
#endif
}

} // namespace omop::extract