/**
 * @file pooled_csv_extractor.cpp
 * @brief Implementation of memory-optimized CSV extractor
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "pooled_csv_extractor.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <algorithm>
#include <sstream>

namespace omop::extract {

// PooledCsvExtractor implementation

PooledCsvExtractor::PooledCsvExtractor() 
    : CsvExtractor()
    , pool_created_time_(std::chrono::steady_clock::now()) {
}

void PooledCsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                   core::ProcessingContext& context) {
    // Initialize base CSV extractor first
    CsvExtractor::initialize(config, context);
    
    auto logger = common::Logger::get("omop-pooled-csv-extractor");
    logger->info("Initializing pooled CSV extractor with memory optimization");

    try {
        // Extract pool configuration
        if (auto it = config.find("record_pool_size"); it != config.end()) {
            record_pool_size_ = std::any_cast<size_t>(it->second);
        }

        if (auto it = config.find("field_pool_size"); it != config.end()) {
            field_pool_size_ = std::any_cast<size_t>(it->second);
        }

        if (auto it = config.find("max_pool_size"); it != config.end()) {
            max_pool_size_ = std::any_cast<size_t>(it->second);
        }

        // Initialize object pools
        record_pool_ = std::make_unique<ObjectPool<core::Record>>(
            record_pool_size_, max_pool_size_,
            []() { return std::make_unique<core::Record>(); }
        );

        field_vector_pool_ = std::make_unique<ObjectPool<std::vector<std::string>>>(
            field_pool_size_, max_pool_size_,
            []() { return std::make_unique<std::vector<std::string>>(); }
        );

        logger->info("Initialized memory pools: records={}, fields={}, max={}",
                    record_pool_size_, field_pool_size_, max_pool_size_);

    } catch (const std::bad_any_cast& e) {
        throw common::ConfigurationException("Invalid pool configuration parameter: " + std::string(e.what()));
    }
}

core::RecordBatch PooledCsvExtractor::extract_batch(size_t batch_size,
                                                    core::ProcessingContext& context) {
    auto start_time = std::chrono::steady_clock::now();
    
    // Use base implementation but with our optimized extractBatchImpl
    auto batch = CsvExtractor::extract_batch(batch_size, context);
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Update pooling statistics
    auto logger = common::Logger::get("omop-pooled-csv-extractor");
    logger->debug("Extracted batch of {} records in {}ms using pooled resources",
                 batch.size(), duration.count());
    
    return batch;
}

std::vector<core::Record> PooledCsvExtractor::extractBatchImpl(size_t batch_size) {
    std::vector<core::Record> records;
    records.reserve(batch_size);

    std::string line;
    size_t extracted = 0;

    // Use pooled string buffer for line processing
    [[maybe_unused]] auto& string_pool = PoolManager::instance().get_string_buffer_pool();

    while (extracted < batch_size && std::getline(file_stream_, line)) {
        current_line_++;

        // Skip empty lines if configured
        if (options_.skip_empty_lines && line.empty()) {
            continue;
        }

        try {
            // Parse line into fields using pooled field vector
            auto pooled_fields = field_vector_pool_->acquire();
            pooled_fields->clear();
            
            *pooled_fields = parser_->parse_line(line);
            
            // Create optimized record
            auto record = create_optimized_record(*pooled_fields);
            
            // Return field vector to pool
            field_vector_pool_->release(std::move(pooled_fields));
            
            records.push_back(std::move(record));
            extracted++;
            pooled_records_created_++;

        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-pooled-csv-extractor");
            logger->warn("Error parsing line {}: {}", current_line_, e.what());
            // Continue with next line
        }
    }

    return records;
}

PooledObject<core::Record> PooledCsvExtractor::create_pooled_record() {
    auto record = record_pool_->acquire();
    return PooledObject<core::Record>(std::move(record), record_pool_.get());
}

core::Record PooledCsvExtractor::create_optimized_record(const std::vector<std::string>& fields) {
    // Create record using pooled resources where possible
    core::Record record;
    
    // Set source reference in metadata
    auto& metadata = record.getMetadataMutable();
    metadata.source_table = filepath_;
    metadata.source_row_number = current_line_;

    // Convert fields to typed values with minimal allocations
    for (size_t i = 0; i < fields.size() && i < column_names_.size(); ++i) {
        std::string type_hint = i < column_types_.size() ? column_types_[i] : "";
        auto value = parser_->convert_field(fields[i], type_hint);
        
        // Use string interning for column names to reduce memory
        record.setField(column_names_[i], value);
    }

    return record;
}

std::unordered_map<std::string, std::any> PooledCsvExtractor::get_statistics() const {
    auto stats = CsvExtractor::get_statistics();
    
    // Add pooling statistics
    if (record_pool_) {
        auto record_stats = record_pool_->get_statistics();
        stats["record_pool_size"] = record_stats.available_objects;
        stats["record_pool_total_created"] = record_stats.total_created;
        stats["record_pool_total_reused"] = record_stats.total_reused;
        stats["record_pool_reuse_ratio"] = record_stats.reuse_ratio;
    }
    
    if (field_vector_pool_) {
        auto field_stats = field_vector_pool_->get_statistics();
        stats["field_pool_size"] = field_stats.available_objects;
        stats["field_pool_total_created"] = field_stats.total_created;
        stats["field_pool_total_reused"] = field_stats.total_reused;
        stats["field_pool_reuse_ratio"] = field_stats.reuse_ratio;
    }
    
    stats["pooled_records_created"] = pooled_records_created_;
    stats["pooled_fields_reused"] = pooled_fields_reused_;
    
    auto uptime = std::chrono::steady_clock::now() - pool_created_time_;
    stats["pool_uptime_ms"] = std::chrono::duration_cast<std::chrono::milliseconds>(uptime).count();
    
    return stats;
}

void PooledCsvExtractor::finalize(core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-pooled-csv-extractor");
    
    // Log pooling statistics before cleanup
    if (record_pool_ && field_vector_pool_) {
        auto record_stats = record_pool_->get_statistics();
        auto field_stats = field_vector_pool_->get_statistics();
        
        logger->info("Pool statistics - Records: {:.1f}% reuse ({}/{}), Fields: {:.1f}% reuse ({}/{})",
                    record_stats.reuse_ratio * 100, record_stats.total_reused, record_stats.total_created,
                    field_stats.reuse_ratio * 100, field_stats.total_reused, field_stats.total_created);
    }
    
    // Clear pools before finalizing
    if (record_pool_) record_pool_->clear();
    if (field_vector_pool_) field_vector_pool_->clear();
    
    CsvExtractor::finalize(context);
}

// BatchProcessor implementation

BatchProcessor::BatchProcessor(size_t batch_pool_size, size_t record_pool_size)
    : batch_pool_(batch_pool_size, batch_pool_size * 2,
                 []() { return std::make_unique<core::RecordBatch>(); })
    , record_pool_(record_pool_size, record_pool_size * 3,
                  []() { return std::make_unique<core::Record>(); })
    , string_pool_(PoolManager::instance().get_string_buffer_pool()) {
}

core::RecordBatch BatchProcessor::process_batch(const std::vector<std::string>& lines,
                                               CsvFieldParser& parser,
                                               const std::vector<std::string>& column_names,
                                               const std::vector<std::string>& column_types) {
    auto start_time = std::chrono::steady_clock::now();
    
    core::RecordBatch batch;
    batch.reserve(lines.size());
    
    for (const auto& line : lines) {
        if (line.empty()) continue;
        
        try {
            // Parse line
            auto fields = parser.parse_line(line);
            
            // Create record using pool
            auto pooled_record = record_pool_.acquire();
            
            // Set source reference in metadata
            auto& metadata = pooled_record->getMetadataMutable();
            metadata.source_table = "batch";
            metadata.source_row_number = 0;
            
            // Convert fields
            for (size_t i = 0; i < fields.size() && i < column_names.size(); ++i) {
                std::string type_hint = i < column_types.size() ? column_types[i] : "";
                auto value = parser.convert_field(fields[i], type_hint);
                pooled_record->setField(column_names[i], value);
            }
            
            batch.addRecord(*pooled_record);
            
            // Return record to pool
            record_pool_.release(std::move(pooled_record));
            
            stats_.pool_hits++;
        } catch (const std::exception&) {
            stats_.pool_misses++;
        }
    }
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Update statistics
    stats_.total_batches_processed++;
    stats_.total_records_processed += batch.size();
    stats_.total_processing_time += duration;
    stats_.avg_batch_processing_time_ms = 
        static_cast<double>(stats_.total_processing_time.count()) / stats_.total_batches_processed;
    
    batch.setExtractionTime(duration);
    batch.setTotalSize(batch.size());
    
    return batch;
}

// MemoryAwareCsvUtils implementation

std::vector<std::string>& MemoryAwareCsvUtils::parse_line_optimized(
    const std::string& line,
    char delimiter,
    std::vector<std::string>& fields) {
    
    fields.clear();
    
    if (line.empty()) {
        return fields;
    }
    
    size_t start = 0;
    size_t pos = 0;
    
    while (pos < line.length()) {
        if (line[pos] == delimiter) {
            fields.emplace_back(line.substr(start, pos - start));
            start = pos + 1;
        }
        pos++;
    }
    
    // Add last field
    if (start <= line.length()) {
        fields.emplace_back(line.substr(start));
    }
    
    return fields;
}

size_t MemoryAwareCsvUtils::estimate_memory_usage(size_t file_size,
                                                  size_t avg_line_length,
                                                  size_t batch_size) {
    // Rough estimates based on typical CSV processing patterns
    [[maybe_unused]] size_t estimated_records = file_size / avg_line_length;
    size_t avg_fields_per_record = avg_line_length / 10; // Assume 10 chars per field on average
    
    // Memory for records in processing
    size_t record_memory = batch_size * (sizeof(core::Record) + avg_fields_per_record * 50);
    
    // Memory for field vectors and strings
    size_t field_memory = batch_size * avg_fields_per_record * 20;
    
    // Buffer memory for file I/O
    size_t buffer_memory = std::min(file_size, size_t(1024 * 1024)); // Up to 1MB buffer
    
    return record_memory + field_memory + buffer_memory;
}

MemoryAwareCsvUtils::PoolConfig MemoryAwareCsvUtils::calculate_optimal_pools(
    size_t expected_records,
    size_t avg_fields_per_record,
    size_t available_memory) {
    
    // Conservative memory allocation (use 50% of available)
    size_t usable_memory = available_memory / 2;
    
    // Estimate memory per record
    size_t memory_per_record = sizeof(core::Record) + avg_fields_per_record * 30;
    
    // Calculate optimal pool sizes
    size_t max_records_in_memory = usable_memory / memory_per_record;
    size_t record_pool_size = std::min(max_records_in_memory / 4, size_t(100));
    size_t field_pool_size = record_pool_size / 2;
    size_t string_buffer_pool_size = std::max(field_pool_size / 5, size_t(5));
    size_t max_batch_size = std::min(max_records_in_memory / 10, size_t(1000));
    
    return PoolConfig{
        .record_pool_size = record_pool_size,
        .field_pool_size = field_pool_size,
        .string_buffer_pool_size = string_buffer_pool_size,
        .max_batch_size = max_batch_size
    };
}

} // namespace omop::extract