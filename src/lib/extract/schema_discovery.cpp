#include "schema_discovery.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <iomanip>
#include <limits>
#include <unordered_set>

namespace omop::extract {

SchemaDiscoveryEngine::SchemaDiscoveryEngine(const SchemaDiscoveryOptions& options)
    : options_(options) {
    initialize_patterns();
}

void SchemaDiscoveryEngine::initialize_patterns() {
    // Email pattern (simplified but robust)
    email_regex_ = std::regex(R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
    
    // UK phone number patterns
    phone_uk_regex_ = std::regex(R"(^(\+44\s?|0)(\d{2}\s?\d{4}\s?\d{4}|\d{3}\s?\d{3}\s?\d{4}|\d{4}\s?\d{6})$)");
    
    // URL pattern
    url_regex_ = std::regex(R"(^https?://[^\s/$.?#].[^\s]*$)", std::regex_constants::icase);
    
    // UUID pattern (standard format)
    uuid_regex_ = std::regex(R"([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})");
    
    // UK postcode pattern
    postcode_uk_regex_ = std::regex(R"(^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$)", std::regex_constants::icase);
    
    // NHS number pattern (10 digits with optional spaces/dashes)
    nhs_number_regex_ = std::regex(R"(^\s*\d{3}[\s-]?\d{3}[\s-]?\d{4}\s*$)");
    
    // IP address pattern (IPv4)
    ip_address_regex_ = std::regex(R"(^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$)");
    
    // Currency pattern (£, $, €, with optional commas and decimals)
    currency_regex_ = std::regex(R"(^[£$€]?[\d,]+\.?\d{0,2}$)");
}

DiscoveredSchema SchemaDiscoveryEngine::discover_csv_schema(const std::string& filepath) {
    auto logger = common::Logger::get("omop-schema-discovery");
    logger->info("Starting schema discovery for file: {}", filepath);
    
    DiscoveredSchema schema;
    
    std::ifstream file(filepath);
    if (!file.is_open()) {
        throw common::ExtractionException("Cannot open file for schema discovery: " + filepath,
                                         "schema_discovery");
    }
    
    // Read sample lines to detect delimiter and header
    std::vector<std::string> sample_lines;
    std::string line;
    size_t lines_read = 0;
    
    while (std::getline(file, line) && lines_read < std::min(options_.sample_size, 100UL)) {
        if (!line.empty()) {
            sample_lines.push_back(line);
            lines_read++;
        }
    }
    
    if (sample_lines.empty()) {
        throw common::ExtractionException("File appears to be empty: " + filepath,
                                         "schema_discovery");
    }
    
    // Detect delimiter
    schema.delimiter = std::string(1, detect_delimiter(sample_lines));
    logger->debug("Detected delimiter: '{}'", schema.delimiter);
    
    // Parse sample data
    std::vector<std::vector<std::string>> sample_data;
    for (const auto& sample_line : sample_lines) {
        std::vector<std::string> fields;
        std::stringstream ss(sample_line);
        std::string field;
        
        while (std::getline(ss, field, schema.delimiter[0])) {
            // Simple field trimming
            if (!field.empty() && field.front() == '"' && field.back() == '"') {
                field = field.substr(1, field.length() - 2);
            }
            fields.push_back(field);
        }
        
        if (!fields.empty()) {
            sample_data.push_back(fields);
        }
    }
    
    // Detect header
    if (sample_data.size() >= 2) {
        schema.has_header = detect_header(sample_data[0], sample_data[1]);
    } else if (sample_data.size() == 1) {
        schema.has_header = detect_header(sample_data[0]);
    }
    
    logger->debug("Detected header: {}", schema.has_header ? "yes" : "no");
    
    // Extract headers and data
    std::vector<std::string> headers;
    std::vector<std::vector<std::string>> data_rows;
    
    if (schema.has_header && !sample_data.empty()) {
        headers = sample_data[0];
        data_rows.assign(sample_data.begin() + 1, sample_data.end());
    } else {
        // Generate column names
        if (!sample_data.empty()) {
            for (size_t i = 0; i < sample_data[0].size(); ++i) {
                headers.push_back("column_" + std::to_string(i + 1));
            }
            data_rows = sample_data;
        }
    }
    
    // Continue reading more sample data if needed
    if (data_rows.size() < options_.sample_size && file.good()) {
        while (std::getline(file, line) && data_rows.size() < options_.sample_size) {
            if (line.empty()) continue;
            
            std::vector<std::string> fields;
            std::stringstream ss(line);
            std::string field;
            
            while (std::getline(ss, field, schema.delimiter[0])) {
                if (!field.empty() && field.front() == '"' && field.back() == '"') {
                    field = field.substr(1, field.length() - 2);
                }
                fields.push_back(field);
            }
            
            if (fields.size() == headers.size()) {
                data_rows.push_back(fields);
                schema.valid_rows++;
            } else {
                schema.malformed_rows++;
            }
        }
    }
    
    file.close();
    
    // Analyze schema from sample data
    auto discovered = discover_schema_from_sample(headers, data_rows);
    
    // Merge results
    schema.columns = std::move(discovered.columns);
    schema.total_rows_analyzed = data_rows.size();
    schema.discovery_confidence = discovered.discovery_confidence;
    schema.warnings = discovered.warnings;
    schema.recommendations = discovered.recommendations;
    
    // Estimate total row count
    try {
        auto file_size = std::filesystem::file_size(filepath);
        if (schema.total_rows_analyzed > 0) {
            size_t avg_line_size = file_size / (schema.total_rows_analyzed + (schema.has_header ? 1 : 0));
            schema.estimated_row_count = file_size / avg_line_size;
        }
    } catch (...) {
        // Ignore file size errors
    }
    
    generate_recommendations(schema);
    
    logger->info("Schema discovery completed: {} columns, {:.1f}% confidence",
                 schema.columns.size(), schema.discovery_confidence * 100);
    
    return schema;
}

DiscoveredSchema SchemaDiscoveryEngine::discover_schema_from_sample(
    const std::vector<std::string>& headers,
    const std::vector<std::vector<std::string>>& sample_data) {
    
    DiscoveredSchema schema;
    schema.columns.reserve(headers.size());
    
    auto logger = common::Logger::get("omop-schema-discovery");
    
    // Analyze each column
    for (size_t col_idx = 0; col_idx < headers.size(); ++col_idx) {
        const std::string& header = headers[col_idx];
        
        // Extract column values
        std::vector<std::string> column_values;
        column_values.reserve(sample_data.size());
        
        for (const auto& row : sample_data) {
            if (col_idx < row.size()) {
                column_values.push_back(row[col_idx]);
            } else {
                column_values.push_back(""); // Missing value
            }
        }
        
        // Analyze column
        auto column_schema = analyze_column(header, column_values);
        schema.columns.push_back(column_schema);
        
        logger->debug("Column '{}': type={}, confidence={:.2f}",
                     column_schema.name,
                     data_type_to_string(column_schema.inferred_type),
                     column_schema.confidence);
    }
    
    // Calculate overall confidence
    double total_confidence = 0.0;
    for (const auto& col : schema.columns) {
        total_confidence += col.confidence;
    }
    schema.discovery_confidence = headers.empty() ? 0.0 : total_confidence / headers.size();
    
    schema.total_rows_analyzed = sample_data.size();
    schema.valid_rows = sample_data.size();
    
    return schema;
}

ColumnSchema SchemaDiscoveryEngine::analyze_column(const std::string& column_name,
                                                  const std::vector<std::string>& values) {
    ColumnSchema column;
    column.name = column_name;
    
    ColumnStatistics& stats = column.statistics;
    stats.total_values = values.size();
    
    std::unordered_map<std::string, size_t> value_counts;
    
    // Collect statistics
    for (const auto& value : values) {
        if (value.empty() || value == "NULL" || value == "null" || value == "NA") {
            stats.null_values++;
            continue;
        }
        
        // Track value frequency
        value_counts[value]++;
        
        // Length statistics
        size_t len = value.length();
        stats.min_length = std::min(stats.min_length, len);
        stats.max_length = std::max(stats.max_length, len);
        stats.avg_length = (stats.avg_length * (stats.total_values - stats.null_values - 1) + len) / 
                          (stats.total_values - stats.null_values);
        
        // Type classification
        DataType detected = classify_value(value);
        switch (detected) {
            case DataType::INTEGER:
                stats.integer_count++;
                try {
                    long long val = std::stoll(value);
                    stats.min_integer = std::min(stats.min_integer, val);
                    stats.max_integer = std::max(stats.max_integer, val);
                } catch (...) {}
                break;
            case DataType::DOUBLE:
                stats.double_count++;
                try {
                    double val = std::stod(value);
                    stats.min_double = std::min(stats.min_double, val);
                    stats.max_double = std::max(stats.max_double, val);
                } catch (...) {}
                break;
            case DataType::BOOLEAN:
                stats.boolean_count++;
                break;
            case DataType::DATE:
                stats.date_count++;
                break;
            case DataType::DATETIME:
                stats.datetime_count++;
                break;
            case DataType::EMAIL:
                stats.email_count++;
                break;
            case DataType::PHONE:
                stats.phone_count++;
                break;
            case DataType::URL:
                stats.url_count++;
                break;
            case DataType::UUID:
                stats.uuid_count++;
                break;
            case DataType::POSTCODE_UK:
                stats.postcode_count++;
                break;
            case DataType::NHS_NUMBER:
                stats.nhs_number_count++;
                break;
            default:
                break;
        }
        
        // Sample values for pattern analysis
        if (stats.sample_values.size() < 10) {
            stats.sample_values.push_back(value);
        }
    }
    
    stats.unique_values = value_counts.size();
    stats.value_frequencies = std::move(value_counts);
    
    // Determine final type
    column.inferred_type = determine_column_type(stats);
    column.confidence = calculate_confidence(stats, column.inferred_type);
    
    // Set nullable based on null ratio
    size_t non_null_values = stats.total_values - stats.null_values;
    column.nullable = stats.null_values > 0;
    
    // Set max length
    column.max_length = stats.max_length;
    
    // Set type-specific properties
    if (column.inferred_type == DataType::INTEGER || column.inferred_type == DataType::LONG) {
        if (stats.min_integer != LLONG_MAX) {
            column.min_value = stats.min_integer;
            column.max_value = stats.max_integer;
        }
    }
    
    // Check for enum-like columns
    if (stats.unique_values <= options_.max_unique_values_for_enum && 
        stats.unique_values > 1 && non_null_values > 0) {
        
        double unique_ratio = static_cast<double>(stats.unique_values) / non_null_values;
        if (unique_ratio < 0.5) { // Less than 50% unique values suggests enum
            for (const auto& [value, count] : stats.value_frequencies) {
                column.enum_values.push_back(value);
            }
            std::sort(column.enum_values.begin(), column.enum_values.end());
        }
    }
    
    // Generate validation pattern
    column.pattern = create_validation_pattern(column);
    
    // Generate description
    std::stringstream desc;
    desc << data_type_to_string(column.inferred_type);
    if (column.nullable) desc << ", nullable";
    if (!column.enum_values.empty()) {
        desc << ", enum with " << column.enum_values.size() << " values";
    }
    if (column.max_length > 0) {
        desc << ", max length " << column.max_length;
    }
    column.description = desc.str();
    
    return column;
}

char SchemaDiscoveryEngine::detect_delimiter(const std::vector<std::string>& sample_lines) {
    std::vector<char> candidates = {',', ';', '\t', '|', ':'};
    std::unordered_map<char, std::vector<size_t>> delimiter_counts;
    
    // Count occurrences of each delimiter in each line
    for (const auto& line : sample_lines) {
        for (char delim : candidates) {
            size_t count = std::count(line.begin(), line.end(), delim);
            delimiter_counts[delim].push_back(count);
        }
    }
    
    // Find delimiter with most consistent count across lines
    char best_delimiter = ',';
    double best_score = -1.0;
    
    for (char delim : candidates) {
        const auto& counts = delimiter_counts[delim];
        if (counts.empty()) continue;
        
        // Calculate variance in counts
        double mean = std::accumulate(counts.begin(), counts.end(), 0.0) / counts.size();
        if (mean < 1.0) continue; // Must have at least 1 occurrence on average
        
        double variance = 0.0;
        for (size_t count : counts) {
            variance += (count - mean) * (count - mean);
        }
        variance /= counts.size();
        
        // Score: higher mean, lower variance is better
        double score = mean / (1.0 + std::sqrt(variance));
        
        if (score > best_score) {
            best_score = score;
            best_delimiter = delim;
        }
    }
    
    return best_delimiter;
}

bool SchemaDiscoveryEngine::detect_header(const std::vector<std::string>& first_row,
                                         const std::vector<std::string>& second_row) {
    if (first_row.empty()) return false;
    
    // Check if first row values look like headers (mostly text, no numbers)
    size_t text_fields = 0;
    size_t numeric_fields = 0;
    
    for (const auto& field : first_row) {
        if (field.empty()) continue;
        
        if (is_integer(field) || is_double(field)) {
            numeric_fields++;
        } else {
            text_fields++;
        }
    }
    
    // If mostly text in first row, likely header
    if (text_fields > numeric_fields) {
        // If we have a second row, compare patterns
        if (!second_row.empty() && second_row.size() == first_row.size()) {
            size_t second_text = 0;
            size_t second_numeric = 0;
            
            for (const auto& field : second_row) {
                if (field.empty()) continue;
                
                if (is_integer(field) || is_double(field)) {
                    second_numeric++;
                } else {
                    second_text++;
                }
            }
            
            // If second row has more numeric values than first, first is likely header
            return second_numeric > text_fields;
        }
        return true;
    }
    
    return false;
}

DataType SchemaDiscoveryEngine::classify_value(const std::string& value) {
    if (value.empty()) return DataType::NULL_TYPE;
    
    // Check specific patterns first (most restrictive)
    if (options_.detect_uk_specific) {
        if (is_nhs_number(value)) return DataType::NHS_NUMBER;
        if (is_uk_postcode(value)) return DataType::POSTCODE_UK;
    }
    
    if (options_.detect_patterns) {
        if (std::regex_match(value, email_regex_)) return DataType::EMAIL;
        if (std::regex_match(value, phone_uk_regex_)) return DataType::PHONE;
        if (std::regex_match(value, url_regex_)) return DataType::URL;
        if (std::regex_match(value, uuid_regex_)) return DataType::UUID;
        if (std::regex_match(value, ip_address_regex_)) return DataType::IP_ADDRESS;
        if (std::regex_match(value, currency_regex_)) return DataType::CURRENCY;
    }
    
    // Check date/datetime
    std::string format;
    if (is_datetime(value, format)) return DataType::DATETIME;
    if (is_date(value, format)) return DataType::DATE;
    
    // Check basic types
    if (is_boolean(value)) return DataType::BOOLEAN;
    if (is_integer(value)) return DataType::INTEGER;
    if (is_double(value)) return DataType::DOUBLE;
    
    return DataType::STRING;
}

bool SchemaDiscoveryEngine::is_integer(const std::string& value) {
    if (value.empty()) return false;
    
    try {
        size_t pos;
        std::stoll(value, &pos);
        return pos == value.length();
    } catch (...) {
        return false;
    }
}

bool SchemaDiscoveryEngine::is_double(const std::string& value) {
    if (value.empty()) return false;
    
    try {
        size_t pos;
        std::stod(value, &pos);
        return pos == value.length();
    } catch (...) {
        return false;
    }
}

bool SchemaDiscoveryEngine::is_boolean(const std::string& value) {
    static const std::unordered_set<std::string> boolean_values = {
        "true", "false", "1", "0", "yes", "no", "y", "n", "on", "off"
    };
    
    std::string lower = value;
    std::transform(lower.begin(), lower.end(), lower.begin(), ::tolower);
    return boolean_values.count(lower) > 0;
}

bool SchemaDiscoveryEngine::is_date(const std::string& value, std::string& format) {
    std::tm tm = {};
    
    for (const auto& fmt : options_.date_formats) {
        std::istringstream ss(value);
        ss >> std::get_time(&tm, fmt.c_str());
        if (!ss.fail() && ss.eof()) {
            format = fmt;
            return true;
        }
    }
    
    return false;
}

bool SchemaDiscoveryEngine::is_datetime(const std::string& value, std::string& format) {
    std::tm tm = {};
    
    for (const auto& fmt : options_.datetime_formats) {
        std::istringstream ss(value);
        ss >> std::get_time(&tm, fmt.c_str());
        if (!ss.fail() && ss.eof()) {
            format = fmt;
            return true;
        }
    }
    
    return false;
}

bool SchemaDiscoveryEngine::is_nhs_number(const std::string& value) {
    if (!std::regex_match(value, nhs_number_regex_)) {
        return false;
    }
    
    // Remove spaces and dashes
    std::string digits;
    for (char c : value) {
        if (std::isdigit(c)) {
            digits += c;
        }
    }
    
    if (digits.length() != 10) return false;
    
    // NHS number checksum validation
    int checksum = 0;
    for (int i = 0; i < 9; i++) {
        checksum += (digits[i] - '0') * (10 - i);
    }
    
    int check_digit = 11 - (checksum % 11);
    if (check_digit == 11) check_digit = 0;
    if (check_digit == 10) return false; // Invalid NHS number
    
    return check_digit == (digits[9] - '0');
}

bool SchemaDiscoveryEngine::is_uk_postcode(const std::string& value) {
    return std::regex_match(value, postcode_uk_regex_);
}

DataType SchemaDiscoveryEngine::determine_column_type(const ColumnStatistics& stats) {
    size_t non_null_values = stats.total_values - stats.null_values;
    if (non_null_values == 0) return DataType::STRING;
    
    double null_ratio = static_cast<double>(stats.null_values) / stats.total_values;
    if (null_ratio > options_.null_tolerance) {
        // Too many nulls, default to string unless very high confidence in type
    }
    
    // Check specialized types first
    if (options_.detect_uk_specific) {
        if (static_cast<double>(stats.nhs_number_count) / non_null_values >= options_.type_confidence_threshold) {
            return DataType::NHS_NUMBER;
        }
        if (static_cast<double>(stats.postcode_count) / non_null_values >= options_.type_confidence_threshold) {
            return DataType::POSTCODE_UK;
        }
    }
    
    if (options_.detect_patterns) {
        if (static_cast<double>(stats.email_count) / non_null_values >= options_.type_confidence_threshold) {
            return DataType::EMAIL;
        }
        if (static_cast<double>(stats.phone_count) / non_null_values >= options_.type_confidence_threshold) {
            return DataType::PHONE;
        }
        if (static_cast<double>(stats.url_count) / non_null_values >= options_.type_confidence_threshold) {
            return DataType::URL;
        }
        if (static_cast<double>(stats.uuid_count) / non_null_values >= options_.type_confidence_threshold) {
            return DataType::UUID;
        }
    }
    
    // Check basic types
    if (static_cast<double>(stats.datetime_count) / non_null_values >= options_.type_confidence_threshold) {
        return DataType::DATETIME;
    }
    if (static_cast<double>(stats.date_count) / non_null_values >= options_.type_confidence_threshold) {
        return DataType::DATE;
    }
    if (static_cast<double>(stats.boolean_count) / non_null_values >= options_.type_confidence_threshold) {
        return DataType::BOOLEAN;
    }
    if (static_cast<double>(stats.integer_count) / non_null_values >= options_.type_confidence_threshold) {
        return DataType::INTEGER;
    }
    if (static_cast<double>(stats.double_count) / non_null_values >= options_.type_confidence_threshold) {
        return DataType::DOUBLE;
    }
    
    return DataType::STRING;
}

double SchemaDiscoveryEngine::calculate_confidence(const ColumnStatistics& stats, DataType assigned_type) {
    size_t non_null_values = stats.total_values - stats.null_values;
    if (non_null_values == 0) return 0.0;
    
    size_t matching_count = 0;
    
    switch (assigned_type) {
        case DataType::INTEGER: matching_count = stats.integer_count; break;
        case DataType::DOUBLE: matching_count = stats.double_count; break;
        case DataType::BOOLEAN: matching_count = stats.boolean_count; break;
        case DataType::DATE: matching_count = stats.date_count; break;
        case DataType::DATETIME: matching_count = stats.datetime_count; break;
        case DataType::EMAIL: matching_count = stats.email_count; break;
        case DataType::PHONE: matching_count = stats.phone_count; break;
        case DataType::URL: matching_count = stats.url_count; break;
        case DataType::UUID: matching_count = stats.uuid_count; break;
        case DataType::POSTCODE_UK: matching_count = stats.postcode_count; break;
        case DataType::NHS_NUMBER: matching_count = stats.nhs_number_count; break;
        default: return 1.0; // STRING type always gets full confidence
    }
    
    return static_cast<double>(matching_count) / non_null_values;
}

void SchemaDiscoveryEngine::generate_recommendations(DiscoveredSchema& schema) {
    // Add recommendations based on discovered patterns
    
    size_t low_confidence_columns = 0;
    for (const auto& col : schema.columns) {
        if (col.confidence < 0.7) {
            low_confidence_columns++;
        }
    }
    
    if (low_confidence_columns > 0) {
        schema.recommendations.push_back(
            "Consider increasing sample size for " + std::to_string(low_confidence_columns) + 
            " columns with low confidence");
    }
    
    if (schema.malformed_rows > schema.valid_rows * 0.1) {
        schema.warnings.push_back("High number of malformed rows detected");
        schema.recommendations.push_back("Review data quality and delimiter detection");
    }
    
    // Check for potential PII
    for (const auto& col : schema.columns) {
        if (col.inferred_type == DataType::EMAIL || 
            col.inferred_type == DataType::PHONE ||
            col.inferred_type == DataType::NHS_NUMBER) {
            schema.warnings.push_back("Potential PII detected in column: " + col.name);
            schema.recommendations.push_back("Consider data masking for PII columns");
            break;
        }
    }
}

std::string SchemaDiscoveryEngine::create_validation_pattern(const ColumnSchema& column) {
    switch (column.inferred_type) {
        case DataType::INTEGER:
            return R"(^-?\d+$)";
        case DataType::DOUBLE:
            return R"(^-?\d+\.?\d*$)";
        case DataType::BOOLEAN:
            return R"(^(true|false|1|0|yes|no|y|n)$)";
        case DataType::EMAIL:
            return R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})";
        case DataType::PHONE:
            return R"(^(\+44\s?|0)(\d{2}\s?\d{4}\s?\d{4}|\d{3}\s?\d{3}\s?\d{4}|\d{4}\s?\d{6})$)";
        case DataType::UUID:
            return R"([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})";
        case DataType::POSTCODE_UK:
            return R"(^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$)";
        case DataType::NHS_NUMBER:
            return R"(^\s*\d{3}[\s-]?\d{3}[\s-]?\d{4}\s*$)";
        default:
            if (column.max_length > 0) {
                return "^.{0," + std::to_string(column.max_length) + "}$";
            }
            return "^.*$";
    }
}

std::string SchemaDiscoveryEngine::data_type_to_string(DataType type) {
    switch (type) {
        case DataType::STRING: return "string";
        case DataType::INTEGER: return "integer";
        case DataType::LONG: return "long";
        case DataType::DOUBLE: return "double";
        case DataType::BOOLEAN: return "boolean";
        case DataType::DATE: return "date";
        case DataType::DATETIME: return "datetime";
        case DataType::TIME: return "time";
        case DataType::UUID: return "uuid";
        case DataType::EMAIL: return "email";
        case DataType::PHONE: return "phone";
        case DataType::URL: return "url";
        case DataType::IP_ADDRESS: return "ip_address";
        case DataType::POSTCODE_UK: return "postcode_uk";
        case DataType::NHS_NUMBER: return "nhs_number";
        case DataType::CURRENCY: return "currency";
        case DataType::PERCENTAGE: return "percentage";
        case DataType::JSON: return "json";
        case DataType::ARRAY: return "array";
        case DataType::NULL_TYPE: return "null";
        default: return "unknown";
    }
}

// Factory implementations

std::unique_ptr<SchemaDiscoveryEngine> SchemaDiscoveryFactory::create_general() {
    SchemaDiscoveryOptions options;
    options.detect_uk_specific = false;
    options.detect_healthcare = false;
    return std::make_unique<SchemaDiscoveryEngine>(options);
}

std::unique_ptr<SchemaDiscoveryEngine> SchemaDiscoveryFactory::create_uk_healthcare() {
    SchemaDiscoveryOptions options;
    options.detect_uk_specific = true;
    options.detect_healthcare = true;
    options.sample_size = 2000; // Larger sample for healthcare data
    return std::make_unique<SchemaDiscoveryEngine>(options);
}

std::unique_ptr<SchemaDiscoveryEngine> SchemaDiscoveryFactory::create_financial() {
    SchemaDiscoveryOptions options;
    options.detect_patterns = true;
    options.detect_uk_specific = false;
    options.detect_healthcare = false;
    return std::make_unique<SchemaDiscoveryEngine>(options);
}

std::unique_ptr<SchemaDiscoveryEngine> SchemaDiscoveryFactory::create_custom(
    const SchemaDiscoveryOptions& options) {
    return std::make_unique<SchemaDiscoveryEngine>(options);
}

} // namespace omop::extract