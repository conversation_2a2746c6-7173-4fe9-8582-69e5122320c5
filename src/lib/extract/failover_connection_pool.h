#pragma once

#include "extract/database_connector.h"
#include "common/logging.h"
#include <vector>
#include <memory>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <chrono>
#include <queue>
#include <unordered_map>
#include <functional>

namespace omop::extract {

/**
 * @brief Database connection health status
 */
enum class ConnectionHealth {
    HEALTHY,      ///< Connection is operational
    DEGRADED,     ///< Connection has issues but may recover
    FAILED,       ///< Connection has failed and needs replacement
    UNKNOWN       ///< Health status not yet determined
};

/**
 * @brief Database server configuration for failover
 */
struct DatabaseServerConfig {
    std::string host;
    int port{0};
    std::string database;
    std::string username;
    std::string password;
    std::string connection_string;
    int priority{1};                    ///< Lower number = higher priority
    size_t max_connections{10};         ///< Max connections for this server
    std::chrono::seconds timeout{30};   ///< Connection timeout
    bool ssl_enabled{false};
    std::unordered_map<std::string, std::string> extra_params;
    
    // Health check configuration
    std::chrono::seconds health_check_interval{60};
    std::string health_check_query{"SELECT 1"};
    size_t max_consecutive_failures{3};
    std::chrono::seconds failure_recovery_time{300}; // 5 minutes
};

/**
 * @brief Connection with health monitoring
 */
class HealthAwareConnection {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param server_config Server configuration
     */
    HealthAwareConnection(std::unique_ptr<IDatabaseConnection> connection,
                         const DatabaseServerConfig& server_config);
    
    /**
     * @brief Get the underlying connection
     * @return IDatabaseConnection* Connection pointer
     */
    IDatabaseConnection* get_connection() { return connection_.get(); }
    
    /**
     * @brief Check connection health
     * @return ConnectionHealth Current health status
     */
    ConnectionHealth check_health();
    
    /**
     * @brief Get last health check time
     * @return std::chrono::system_clock::time_point Last check time
     */
    std::chrono::system_clock::time_point get_last_health_check() const {
        return last_health_check_;
    }
    
    /**
     * @brief Get consecutive failure count
     * @return size_t Number of consecutive failures
     */
    size_t get_consecutive_failures() const { return consecutive_failures_; }
    
    /**
     * @brief Record successful operation
     */
    void record_success();
    
    /**
     * @brief Record failed operation
     */
    void record_failure();
    
    /**
     * @brief Check if connection should be retired
     * @return bool True if connection should be removed from pool
     */
    bool should_retire() const;
    
    /**
     * @brief Get server configuration
     * @return const DatabaseServerConfig& Server config
     */
    const DatabaseServerConfig& get_server_config() const { return server_config_; }

private:
    std::unique_ptr<IDatabaseConnection> connection_;
    DatabaseServerConfig server_config_;
    std::atomic<ConnectionHealth> health_status_{ConnectionHealth::UNKNOWN};
    std::atomic<size_t> consecutive_failures_{0};
    std::atomic<size_t> total_operations_{0};
    std::atomic<size_t> successful_operations_{0};
    std::chrono::system_clock::time_point last_health_check_;
    std::chrono::system_clock::time_point last_failure_time_;
    mutable std::mutex health_mutex_;
};

/**
 * @brief Failover strategy for connection selection
 */
enum class FailoverStrategy {
    PRIORITY_BASED,    ///< Use servers in priority order
    ROUND_ROBIN,       ///< Rotate through all healthy servers
    LOAD_BALANCED,     ///< Choose server with least load
    RANDOM,            ///< Random selection from healthy servers
    LOCALITY_AWARE     ///< Prefer local/regional servers
};

/**
 * @brief Load balancing metrics for a server
 */
struct ServerMetrics {
    std::atomic<size_t> active_connections{0};
    std::atomic<size_t> total_requests{0};
    std::atomic<double> avg_response_time{0.0};
    std::atomic<size_t> error_count{0};
    std::chrono::system_clock::time_point last_update;
    std::atomic<ConnectionHealth> server_health{ConnectionHealth::UNKNOWN};
    
    // Default constructor
    ServerMetrics() = default;
    
    // Delete copy and move operations due to atomic members
    ServerMetrics(const ServerMetrics&) = delete;
    ServerMetrics& operator=(const ServerMetrics&) = delete;
    ServerMetrics(ServerMetrics&&) = delete;
    ServerMetrics& operator=(ServerMetrics&&) = delete;
};

/**
 * @brief Advanced connection pool with failover and load balancing
 */
class FailoverConnectionPool {
public:
    /**
     * @brief Constructor
     * @param servers List of database servers for failover
     * @param strategy Failover strategy to use
     */
    FailoverConnectionPool(const std::vector<DatabaseServerConfig>& servers,
                          FailoverStrategy strategy = FailoverStrategy::PRIORITY_BASED);
    
    /**
     * @brief Destructor
     */
    ~FailoverConnectionPool();
    
    /**
     * @brief Get a connection from the pool
     * @param timeout Maximum time to wait for connection
     * @return std::unique_ptr<HealthAwareConnection> Connection (nullptr if failed)
     */
    std::unique_ptr<HealthAwareConnection> get_connection(
        std::chrono::seconds timeout = std::chrono::seconds(30));
    
    /**
     * @brief Return a connection to the pool
     * @param connection Connection to return
     */
    void return_connection(std::unique_ptr<HealthAwareConnection> connection);
    
    /**
     * @brief Get connection pool statistics
     * @return std::unordered_map<std::string, size_t> Statistics map
     */
    std::unordered_map<std::string, size_t> get_statistics() const;
    
    /**
     * @brief Check health of all servers
     */
    void check_server_health();
    
    /**
     * @brief Start background health monitoring
     */
    void start_health_monitoring();
    
    /**
     * @brief Stop background health monitoring
     */
    void stop_health_monitoring();
    
    /**
     * @brief Add new server to pool
     * @param server_config Server configuration
     */
    void add_server(const DatabaseServerConfig& server_config);
    
    /**
     * @brief Remove server from pool
     * @param host Server host to remove
     * @param port Server port
     */
    void remove_server(const std::string& host, int port);
    
    /**
     * @brief Set failover strategy
     * @param strategy New strategy to use
     */
    void set_failover_strategy(FailoverStrategy strategy) {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        failover_strategy_ = strategy;
    }
    
    /**
     * @brief Get list of healthy servers
     * @return std::vector<DatabaseServerConfig> Healthy servers
     */
    std::vector<DatabaseServerConfig> get_healthy_servers() const;
    
    /**
     * @brief Force failover to next available server
     * @param failed_server_host Host of failed server
     * @param failed_server_port Port of failed server
     */
    void force_failover(const std::string& failed_server_host, int failed_server_port);

private:
    std::vector<DatabaseServerConfig> servers_;
    FailoverStrategy failover_strategy_;
    
    // Connection pools per server
    std::unordered_map<std::string, std::queue<std::unique_ptr<HealthAwareConnection>>> server_pools_;
    std::unordered_map<std::string, ServerMetrics> server_metrics_;
    
    // Synchronization
    mutable std::mutex pool_mutex_;
    std::condition_variable pool_condition_;
    
    // Health monitoring
    std::unique_ptr<std::thread> health_monitor_thread_;
    std::atomic<bool> health_monitoring_active_{false};
    
    // Statistics
    std::atomic<size_t> total_connections_created_{0};
    std::atomic<size_t> total_connections_destroyed_{0};
    std::atomic<size_t> total_get_requests_{0};
    std::atomic<size_t> failed_get_requests_{0};
    std::atomic<size_t> server_failovers_{0};
    
    // Current server selection state
    std::atomic<size_t> round_robin_index_{0};
    
    /**
     * @brief Create connection to specific server
     * @param server_config Server configuration
     * @return std::unique_ptr<HealthAwareConnection> New connection
     */
    std::unique_ptr<HealthAwareConnection> create_connection(
        const DatabaseServerConfig& server_config);
    
    /**
     * @brief Select server based on current strategy
     * @return DatabaseServerConfig* Selected server config (nullptr if none available)
     */
    const DatabaseServerConfig* select_server();
    
    /**
     * @brief Select server using priority-based strategy
     * @return const DatabaseServerConfig* Selected server
     */
    const DatabaseServerConfig* select_priority_based();
    
    /**
     * @brief Select server using round-robin strategy
     * @return const DatabaseServerConfig* Selected server
     */
    const DatabaseServerConfig* select_round_robin();
    
    /**
     * @brief Select server using load-balanced strategy
     * @return const DatabaseServerConfig* Selected server
     */
    const DatabaseServerConfig* select_load_balanced();
    
    /**
     * @brief Select server using random strategy
     * @return const DatabaseServerConfig* Selected server
     */
    const DatabaseServerConfig* select_random();
    
    /**
     * @brief Check if server is healthy
     * @param server_config Server to check
     * @return bool True if server is healthy
     */
    bool is_server_healthy(const DatabaseServerConfig& server_config) const;
    
    /**
     * @brief Update server metrics
     * @param server_key Server identifier
     * @param operation_time Operation duration
     * @param success Whether operation was successful
     */
    void update_server_metrics(const std::string& server_key, 
                              std::chrono::milliseconds operation_time,
                              bool success);
    
    /**
     * @brief Get server key for identification
     * @param server_config Server configuration
     * @return std::string Server key
     */
    std::string get_server_key(const DatabaseServerConfig& server_config) const;
    
    /**
     * @brief Background health monitoring function
     */
    void health_monitor_loop();
    
    /**
     * @brief Initialize connection pool for server
     * @param server_config Server configuration
     */
    void initialize_server_pool(const DatabaseServerConfig& server_config);
    
    /**
     * @brief Clean up failed connections
     * @param server_key Server identifier
     */
    void cleanup_failed_connections(const std::string& server_key);
};

/**
 * @brief Circuit breaker for database connections
 */
class DatabaseCircuitBreaker {
public:
    /**
     * @brief Circuit breaker states
     */
    enum class State {
        CLOSED,    ///< Normal operation
        OPEN,      ///< Circuit is open, rejecting requests
        HALF_OPEN  ///< Testing if service has recovered
    };
    
    /**
     * @brief Constructor
     * @param failure_threshold Number of failures before opening circuit
     * @param recovery_timeout Time before attempting recovery
     * @param success_threshold Successes needed to close circuit from half-open
     */
    DatabaseCircuitBreaker(size_t failure_threshold = 5,
                          std::chrono::seconds recovery_timeout = std::chrono::seconds(60),
                          size_t success_threshold = 3);
    
    /**
     * @brief Check if request is allowed
     * @return bool True if request should be allowed
     */
    bool is_request_allowed();
    
    /**
     * @brief Record successful operation
     */
    void record_success();
    
    /**
     * @brief Record failed operation
     */
    void record_failure();
    
    /**
     * @brief Get current circuit state
     * @return State Current state
     */
    State get_state() const { return state_; }
    
    /**
     * @brief Reset circuit breaker to closed state
     */
    void reset();

private:
    std::atomic<State> state_{State::CLOSED};
    std::atomic<size_t> failure_count_{0};
    std::atomic<size_t> success_count_{0};
    std::chrono::system_clock::time_point last_failure_time_;
    
    const size_t failure_threshold_;
    const std::chrono::seconds recovery_timeout_;
    const size_t success_threshold_;
    
    mutable std::mutex state_mutex_;
    
    /**
     * @brief Transition to open state
     */
    void transition_to_open();
    
    /**
     * @brief Transition to half-open state
     */
    void transition_to_half_open();
    
    /**
     * @brief Transition to closed state
     */
    void transition_to_closed();
};

/**
 * @brief RAII connection wrapper with automatic failover
 */
class FailoverConnection {
public:
    /**
     * @brief Constructor
     * @param connection Health-aware connection
     * @param pool Connection pool reference
     */
    FailoverConnection(std::unique_ptr<HealthAwareConnection> connection,
                      FailoverConnectionPool* pool);
    
    /**
     * @brief Destructor - returns connection to pool
     */
    ~FailoverConnection();
    
    /**
     * @brief Get the underlying database connection
     * @return IDatabaseConnection* Connection pointer
     */
    IDatabaseConnection* operator->() { return connection_->get_connection(); }
    
    /**
     * @brief Get the underlying database connection
     * @return IDatabaseConnection& Connection reference
     */
    IDatabaseConnection& operator*() { return *connection_->get_connection(); }
    
    /**
     * @brief Check if connection is valid
     * @return bool True if connection is valid
     */
    bool is_valid() const { return connection_ && connection_->get_connection(); }
    
    /**
     * @brief Record operation result for health monitoring
     * @param success Whether operation was successful
     */
    void record_operation_result(bool success);

private:
    std::unique_ptr<HealthAwareConnection> connection_;
    FailoverConnectionPool* pool_;
};

} // namespace omop::extract