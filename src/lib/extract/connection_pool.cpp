/**
 * @file connection_pool.cpp
 * @brief Implementation of database connection pooling
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "extract/database_connector.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <chrono>
#include <thread>
#include <queue>
#include <condition_variable>
#include <shared_mutex>
#include <format>

namespace omop::extract {

/**
 * @brief RAII wrapper for connection pool connections
 *
 * Automatically returns connection to pool when destroyed
 */
class PooledConnection {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param pool Connection pool reference
     */
    PooledConnection(std::unique_ptr<IDatabaseConnection> connection,
                     ConnectionPool* pool)
        : connection_(std::move(connection)), pool_(pool) {}

    /**
     * @brief Destructor - returns connection to pool
     */
    ~PooledConnection() {
        if (connection_ && pool_) {
            pool_->release(std::move(connection_));
        }
    }

    /**
     * @brief Move constructor
     */
    PooledConnection(PooledConnection&& other) noexcept
        : connection_(std::move(other.connection_)), pool_(other.pool_) {
        other.pool_ = nullptr;
    }

    /**
     * @brief Move assignment operator
     */
    PooledConnection& operator=(PooledConnection&& other) noexcept {
        if (this != &other) {
            if (connection_ && pool_) {
                pool_->release(std::move(connection_));
            }
            connection_ = std::move(other.connection_);
            pool_ = other.pool_;
            other.pool_ = nullptr;
        }
        return *this;
    }

    // Delete copy operations
    PooledConnection(const PooledConnection&) = delete;
    PooledConnection& operator=(const PooledConnection&) = delete;

    /**
     * @brief Get connection pointer
     * @return IDatabaseConnection* Connection pointer
     */
    IDatabaseConnection* get() { return connection_.get(); }

    /**
     * @brief Dereference operator
     * @return IDatabaseConnection& Connection reference
     */
    IDatabaseConnection& operator*() { return *connection_; }

    /**
     * @brief Arrow operator
     * @return IDatabaseConnection* Connection pointer
     */
    IDatabaseConnection* operator->() { return connection_.get(); }

    /**
     * @brief Check if connection is valid
     * @return bool True if connection exists
     */
    explicit operator bool() const { return connection_ != nullptr; }

private:
    std::unique_ptr<IDatabaseConnection> connection_;
    ConnectionPool* pool_;
};

/**
 * @brief Connection health checker
 *
 * Periodically validates idle connections and removes invalid ones
 */
class ConnectionHealthChecker {
public:
    /**
     * @brief Constructor
     * @param pool Connection pool to monitor
     * @param check_interval Interval between health checks (seconds)
     */
    ConnectionHealthChecker(ConnectionPool* pool, int check_interval = 60)
        : pool_(pool), check_interval_(check_interval), running_(true) {

        health_check_thread_ = std::thread(&ConnectionHealthChecker::run, this);
    }

    /**
     * @brief Destructor
     */
    ~ConnectionHealthChecker() {
        running_ = false;
        cv_.notify_all();

        if (health_check_thread_.joinable()) {
            health_check_thread_.join();
        }
    }

    /**
     * @brief Stop health checking
     */
    void stop() {
        running_ = false;
        cv_.notify_all();
    }

private:
    /**
     * @brief Health check thread function
     */
    void run() {
        auto logger = common::Logger::get("omop-connection-health");

        while (running_) {
            std::unique_lock<std::mutex> lock(mutex_);
            cv_.wait_for(lock, std::chrono::seconds(check_interval_),
                        [this] { return !running_; });

            if (!running_) break;

            try {
                size_t invalid_count = pool_->validate_connections();
                if (invalid_count > 0) {
                    logger->info("Removed {} invalid connections from pool", invalid_count);
                }
            } catch (const std::exception& e) {
                logger->error("Error during connection validation: {}", e.what());
            }
        }
    }

    ConnectionPool* pool_;
    int check_interval_;
    std::atomic<bool> running_;
    std::thread health_check_thread_;
    std::mutex mutex_;
    std::condition_variable cv_;
};

/**
 * @brief Advanced connection pool with monitoring and auto-scaling
 */
class AdvancedConnectionPool : public ConnectionPool {
public:
    /**
     * @brief Pool configuration
     */
    struct PoolConfig {
        size_t min_connections{5};
        size_t max_connections{50};
        std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory;
        bool enable_health_checks{true};
        int health_check_interval{60};  // seconds
        double scale_up_threshold{0.8};
        double scale_down_threshold{0.2};
        double scale_factor{1.5};
        int connection_timeout_ms{5000};
        int max_retry_attempts{3};
    };

    /**
     * @brief Constructor
     * @param config Pool configuration
     */
    explicit AdvancedConnectionPool(const PoolConfig& config)
        : ConnectionPool(config.min_connections, config.max_connections,
                        config.connection_factory),
          config_(config) {

        if (config.enable_health_checks) {
            health_checker_ = std::make_unique<ConnectionHealthChecker>(
                this, config.health_check_interval);
        }

        start_time_ = std::chrono::steady_clock::now();
    }

    /**
     * @brief Get detailed pool metrics
     * @return PoolMetrics Detailed metrics
     */
    struct PoolMetrics {
        ConnectionPool::PoolStats basic_stats;
        double avg_connection_lifetime_seconds;
        double avg_acquisition_rate_per_minute;
        size_t connection_errors;
        size_t timeout_errors;
        std::chrono::seconds uptime;
    };

    PoolMetrics get_metrics() const {
        PoolMetrics metrics;
        metrics.basic_stats = get_statistics();

        auto current_time = std::chrono::steady_clock::now();
        metrics.uptime = std::chrono::duration_cast<std::chrono::seconds>(
            current_time - start_time_);

        if (metrics.uptime.count() > 0) {
            double minutes = metrics.uptime.count() / 60.0;
            if (minutes > 0.0) {
                metrics.avg_acquisition_rate_per_minute =
                    metrics.basic_stats.total_acquisitions / minutes;
            } else {
                metrics.avg_acquisition_rate_per_minute = 0.0;
            }
        }

        metrics.connection_errors = connection_errors_.load();
        metrics.timeout_errors = timeout_errors_.load();

        return metrics;
    }

    /**
     * @brief Auto-scale pool based on usage patterns
     */
    void auto_scale() {
        auto stats = get_statistics();

        // Calculate utilization
        double utilization = 0.0;
        if (stats.total_connections > 0) {
            utilization = static_cast<double>(stats.active_connections) /
                         stats.total_connections;
        }

        // Scale up if high utilization
        if (utilization > config_.scale_up_threshold) {
            size_t new_size = std::min(
                static_cast<size_t>(stats.total_connections * config_.scale_factor),
                config_.max_connections
            );
            resize_pool(new_size);
        }
        // Scale down if low utilization
        else if (utilization < config_.scale_down_threshold) {
            size_t new_size = std::max(
                static_cast<size_t>(stats.total_connections / config_.scale_factor),
                config_.min_connections
            );
            resize_pool(new_size);
        }
    }



private:
    /**
     * @brief Resize connection pool
     * @param new_size Target pool size
     */
    void resize_pool(size_t new_size) {
        auto logger = common::Logger::get("omop-connection-pool");
        auto current_stats = get_statistics();

        if (new_size == current_stats.total_connections) {
            return;
        }

        logger->info("Resizing connection pool from {} to {} connections",
                    current_stats.total_connections, new_size);

        if (new_size > current_stats.total_connections) {
            // Add connections
            size_t to_add = new_size - current_stats.total_connections;
            for (size_t i = 0; i < to_add; ++i) {
                try {
                    release(config_.connection_factory());
                } catch (const std::exception& e) {
                    logger->error("Failed to create connection: {}", e.what());
                    connection_errors_++;
                }
            }
        } else {
            // Remove idle connections
            size_t to_remove = current_stats.total_connections - new_size;
            for (size_t i = 0; i < to_remove && current_stats.idle_connections > 0; ++i) {
                clear_idle_connections();
            }
        }
    }

    PoolConfig config_;
    std::unique_ptr<ConnectionHealthChecker> health_checker_;
    std::chrono::steady_clock::time_point start_time_;
    std::atomic<size_t> connection_errors_{0};
    std::atomic<size_t> timeout_errors_{0};
};

/**
 * @brief Connection pool manager for multiple databases
 *
 * Manages connection pools for different database instances
 */
class ConnectionPoolManager {
public:
    /**
     * @brief Get singleton instance
     * @return ConnectionPoolManager& Instance reference
     */
    static ConnectionPoolManager& instance() {
        static ConnectionPoolManager instance;
        return instance;
    }

    /**
     * @brief Create or get connection pool
     * @param name Pool name
     * @param config Pool configuration
     * @return std::shared_ptr<ConnectionPool> Connection pool
     */
    std::shared_ptr<ConnectionPool> get_pool(
        const std::string& name,
        const AdvancedConnectionPool::PoolConfig& config) {

        std::unique_lock<std::shared_mutex> lock(mutex_);

        auto it = pools_.find(name);
        if (it != pools_.end()) {
            return it->second;
        }

        auto pool = std::make_shared<AdvancedConnectionPool>(config);
        pools_[name] = pool;

        auto logger = common::Logger::get("omop-pool-manager");
        logger->info("Created connection pool '{}'", name);

        return pool;
    }

    /**
     * @brief Get existing pool
     * @param name Pool name
     * @return std::shared_ptr<ConnectionPool> Connection pool or nullptr
     */
    std::shared_ptr<ConnectionPool> get_pool(const std::string& name) const {
        std::shared_lock<std::shared_mutex> lock(mutex_);

        auto it = pools_.find(name);
        return (it != pools_.end()) ? it->second : nullptr;
    }

    /**
     * @brief Remove connection pool
     * @param name Pool name
     */
    void remove_pool(const std::string& name) {
        std::unique_lock<std::shared_mutex> lock(mutex_);

        auto it = pools_.find(name);
        if (it != pools_.end()) {
            pools_.erase(it);

            auto logger = common::Logger::get("omop-pool-manager");
            logger->info("Removed connection pool '{}'", name);
        }
    }

    /**
     * @brief Get all pool names
     * @return std::vector<std::string> Pool names
     */
    std::vector<std::string> get_pool_names() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);

        std::vector<std::string> names;
        names.reserve(pools_.size());

        for (const auto& [name, _] : pools_) {
            names.push_back(name);
        }

        return names;
    }

    /**
     * @brief Get metrics for all pools
     * @return std::unordered_map<std::string, AdvancedConnectionPool::PoolMetrics> Metrics
     */
    std::unordered_map<std::string, AdvancedConnectionPool::PoolMetrics>
    get_all_metrics() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);

        std::unordered_map<std::string, AdvancedConnectionPool::PoolMetrics> metrics;

        for (const auto& [name, pool] : pools_) {
            if (auto advanced_pool =
                std::dynamic_pointer_cast<AdvancedConnectionPool>(pool)) {
                metrics[name] = advanced_pool->get_metrics();
            }
        }

        return metrics;
    }

    /**
     * @brief Clear all pools
     */
    void clear_all() {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        pools_.clear();

        auto logger = common::Logger::get("omop-pool-manager");
        logger->info("Cleared all connection pools");
    }

private:
    ConnectionPoolManager() = default;
    ~ConnectionPoolManager() = default;

    // Delete copy/move operations
    ConnectionPoolManager(const ConnectionPoolManager&) = delete;
    ConnectionPoolManager& operator=(const ConnectionPoolManager&) = delete;
    ConnectionPoolManager(ConnectionPoolManager&&) = delete;
    ConnectionPoolManager& operator=(ConnectionPoolManager&&) = delete;

    mutable std::shared_mutex mutex_;
    std::unordered_map<std::string, std::shared_ptr<ConnectionPool>> pools_;
};

} // namespace omop::extract