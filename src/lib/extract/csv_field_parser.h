#pragma once

#include <string>
#include <vector>
#include <any>
#include <chrono>
#include <optional>

namespace omop::extract {

/**
 * @brief CSV parsing options
 */
struct CsvOptions {
    char delimiter{','};
    char quote_char{'"'};
    char escape_char{'\\'};
    bool has_header{true};
    std::string encoding{"UTF-8"};
    std::vector<std::string> column_names;
    std::vector<std::string> column_types;
    bool skip_empty_lines{true};
    bool trim_fields{true};
    size_t skip_lines{0};
    std::optional<size_t> max_lines;
    std::string null_string{"NULL"};
    std::string true_string{"TRUE"};
    std::string false_string{"FALSE"};
    std::string date_format{"%Y-%m-%d"};
    std::string datetime_format{"%Y-%m-%d %H:%M:%S"};
};

/**
 * @brief CSV field parser
 *
 * Handles parsing of individual CSV fields with proper quote and escape handling.
 */
class CsvFieldParser {
public:
    /**
     * @brief Constructor
     * @param options CSV parsing options
     */
    explicit CsvFieldParser(const CsvOptions& options) : options_(options) {}

    /**
     * @brief Parse a single field
     * @param input Input string
     * @param pos Current position (updated)
     * @return std::string Parsed field value
     */
    std::string parse_field(const std::string& input, size_t& pos);

    /**
     * @brief Parse a complete line
     * @param line Input line
     * @return std::vector<std::string> Parsed fields
     */
    std::vector<std::string> parse_line(const std::string& line);

    /**
     * @brief Convert field to typed value
     * @param field Field value
     * @param type_hint Type hint (optional)
     * @return std::any Typed value
     */
    std::any convert_field(const std::string& field,
                          const std::string& type_hint = "");

    /**
     * @brief Parse date/time value
     * @param value String value
     * @param format Format string
     * @return std::chrono::system_clock::time_point Parsed time
     */
    std::chrono::system_clock::time_point parse_datetime(
        const std::string& value, const std::string& format) const;

    /**
     * @brief Trim whitespace from string
     * @param str String to trim
     * @return std::string Trimmed string
     */
    std::string trim(const std::string& str) const;

    /**
     * @brief Check if a string is valid UTF-8
     * @param str String to check
     * @return bool True if valid UTF-8, false otherwise
     */
    bool is_valid_utf8(const std::string& str) const;

private:
    CsvOptions options_;
};

// Inline implementations for performance-critical methods

inline std::string CsvFieldParser::trim(const std::string& str) const {
    if (!options_.trim_fields) return str;

    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";

    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

inline bool CsvFieldParser::is_valid_utf8(const std::string& str) const {
    const unsigned char* bytes = reinterpret_cast<const unsigned char*>(str.c_str());
    const unsigned char* end = bytes + str.length();

    while (bytes < end) {
        if (*bytes <= 0x7F) {
            // ASCII
            bytes++;
        } else if ((*bytes & 0xE0) == 0xC0) {
            // 2-byte sequence
            if (bytes + 1 >= end || (bytes[1] & 0xC0) != 0x80) return false;
            bytes += 2;
        } else if ((*bytes & 0xF0) == 0xE0) {
            // 3-byte sequence
            if (bytes + 2 >= end || (bytes[1] & 0xC0) != 0x80 ||
                (bytes[2] & 0xC0) != 0x80) return false;
            bytes += 3;
        } else if ((*bytes & 0xF8) == 0xF0) {
            // 4-byte sequence
            if (bytes + 3 >= end || (bytes[1] & 0xC0) != 0x80 ||
                (bytes[2] & 0xC0) != 0x80 || (bytes[3] & 0xC0) != 0x80) return false;
            bytes += 4;
        } else {
            return false;
        }
    }
    return true;
}

} // namespace omop::extract