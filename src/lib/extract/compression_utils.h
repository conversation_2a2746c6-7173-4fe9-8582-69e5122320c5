#pragma once

#include <string>

namespace omop::extract {

/**
 * @brief Compression format enumeration
 */
enum class CompressionFormat {
    None,
    Gzip,
    Zip,
    Bzip2,
    Xz
};

/**
 * @brief Compression utilities for file decompression
 */
class CompressionUtils {
public:
    /**
     * @brief Detect compression format from file path
     * @param filepath File path to analyze
     * @return CompressionFormat Detected compression format
     */
    static CompressionFormat detect_compression(const std::string& filepath);

    /**
     * @brief Check if a file is compressed based on extension
     * @param filepath File path to check
     * @return bool True if file appears to be compressed
     */
    static bool is_compressed_file(const std::string& filepath);

    /**
     * @brief Decompress a file to a temporary location
     * @param src_path Source compressed file path
     * @param dst_path Destination path for decompressed file
     * @param format Compression format
     * @return bool True if decompression was successful
     */
    static bool decompress_file(const std::string& src_path,
                               const std::string& dst_path,
                               CompressionFormat format);

    /**
     * @brief Convert compression format to string
     * @param format Compression format
     * @return std::string String representation
     */
    static std::string format_to_string(CompressionFormat format);

    /**
     * @brief Convert string to compression format
     * @param format_str String representation
     * @return CompressionFormat Compression format
     */
    static CompressionFormat string_to_format(const std::string& format_str);

    /**
     * @brief Generate temporary file path for decompressed content
     * @param original_path Original compressed file path
     * @return std::string Temporary file path
     */
    static std::string generate_temp_path(const std::string& original_path);

private:
    /**
     * @brief Decompress gzip file
     * @param src Source file path
     * @param dst Destination file path
     * @return bool Success status
     */
    static bool decompress_gzip(const std::string& src, const std::string& dst);
    
    /**
     * @brief Decompress zip file (extract first entry)
     * @param src Source file path
     * @param dst Destination file path
     * @return bool Success status
     */
    static bool decompress_zip(const std::string& src, const std::string& dst);
    
    /**
     * @brief Decompress bzip2 file
     * @param src Source file path
     * @param dst Destination file path
     * @return bool Success status
     */
    static bool decompress_bzip2(const std::string& src, const std::string& dst);
    
    /**
     * @brief Decompress xz file
     * @param src Source file path
     * @param dst Destination file path
     * @return bool Success status
     */
    static bool decompress_xz(const std::string& src, const std::string& dst);
};

} // namespace omop::extract