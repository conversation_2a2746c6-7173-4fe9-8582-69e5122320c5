/**
 * @file mysql_connector.cpp
 * @brief MySQL database connector implementation
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "mysql_connector.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <format>
#include <chrono>
#include <ctime>
#include <sstream>
#include <iomanip>
#include <cstring>
#include <unordered_map>

namespace omop::extract {

// MySQL type mapping
static const std::unordered_map<enum_field_types, std::string> MYSQL_TYPE_NAMES = {
    {MYSQL_TYPE_DECIMAL, "DECIMAL"},
    {MYSQL_TYPE_TINY, "TINYINT"},
    {MYSQL_TYPE_SHORT, "SMALLINT"},
    {MYSQL_TYPE_LONG, "INT"},
    {MYSQL_TYPE_FLOAT, "FLOAT"},
    {MYSQL_TYPE_DOUBLE, "DOUBLE"},
    {MY<PERSON><PERSON>_TYPE_NULL, "NULL"},
    {MY<PERSON><PERSON>_TYPE_TIMESTAMP, "TIMESTAMP"},
    {MYSQL_TYPE_LONGLONG, "BIGINT"},
    {MYSQL_TYPE_INT24, "MEDIUMINT"},
    {MYSQL_TYPE_DATE, "DATE"},
    {MYSQL_TYPE_TIME, "TIME"},
    {MYSQL_TYPE_DATETIME, "DATETIME"},
    {MYSQL_TYPE_YEAR, "YEAR"},
    {MYSQL_TYPE_NEWDATE, "DATE"},
    {MYSQL_TYPE_VARCHAR, "VARCHAR"},
    {MYSQL_TYPE_BIT, "BIT"},
    {MYSQL_TYPE_TIMESTAMP2, "TIMESTAMP"},
    {MYSQL_TYPE_DATETIME2, "DATETIME"},
    {MYSQL_TYPE_TIME2, "TIME"},
    {MYSQL_TYPE_JSON, "JSON"},
    {MYSQL_TYPE_NEWDECIMAL, "DECIMAL"},
    {MYSQL_TYPE_ENUM, "ENUM"},
    {MYSQL_TYPE_SET, "SET"},
    {MYSQL_TYPE_TINY_BLOB, "TINYBLOB"},
    {MYSQL_TYPE_MEDIUM_BLOB, "MEDIUMBLOB"},
    {MYSQL_TYPE_LONG_BLOB, "LONGBLOB"},
    {MYSQL_TYPE_BLOB, "BLOB"},
    {MYSQL_TYPE_VAR_STRING, "VARCHAR"},
    {MYSQL_TYPE_STRING, "CHAR"},
    {MYSQL_TYPE_GEOMETRY, "GEOMETRY"}
};

// MySQLResultSet implementation

MySQLResultSet::MySQLResultSet(std::shared_ptr<MySQLStatement> statement)
    : statement_(statement) {

    if (!statement_) {
        throw common::DatabaseException("Invalid MySQL statement", "MySQL", 0);
    }

    // Store result set
    result_ = mysql_stmt_result_metadata(statement_->get());
    if (!result_) {
        throw common::DatabaseException("No result set available", "MySQL",
                                      mysql_stmt_errno(statement_->get()));
    }

    // Load metadata
    load_metadata();
}

MySQLResultSet::~MySQLResultSet() {
    // Free bind buffers
    for (auto& bind : bind_buffers_) {
        if (bind.buffer) {
            free(bind.buffer);
        }
    }

    if (result_) {
        mysql_free_result(result_);
    }
}

void MySQLResultSet::load_metadata() {
    column_count_ = mysql_num_fields(result_);
    columns_.reserve(column_count_);
    bind_buffers_.resize(column_count_);

    MYSQL_FIELD* fields = mysql_fetch_fields(result_);

    for (unsigned int i = 0; i < column_count_; ++i) {
        ColumnInfo info;
        info.name = fields[i].name;
        info.type = fields[i].type;
        info.length = fields[i].length;
        info.flags = fields[i].flags;
        info.decimals = fields[i].decimals;
        columns_.push_back(info);

        // Set up bind buffers
        memset(&bind_buffers_[i], 0, sizeof(MYSQL_BIND));
        auto& bind = bind_buffers_[i];
        auto& buffer_info = reinterpret_cast<BindBuffer&>(bind);

        bind.buffer_type = fields[i].type;

        // Allocate buffer based on type
        switch (fields[i].type) {
            case MYSQL_TYPE_TINY:
                bind.buffer_length = sizeof(signed char);
                break;
            case MYSQL_TYPE_SHORT:
            case MYSQL_TYPE_YEAR:
                bind.buffer_length = sizeof(short);
                break;
            case MYSQL_TYPE_LONG:
            case MYSQL_TYPE_INT24:
                bind.buffer_length = sizeof(int);
                break;
            case MYSQL_TYPE_LONGLONG:
                bind.buffer_length = sizeof(long long);
                break;
            case MYSQL_TYPE_FLOAT:
                bind.buffer_length = sizeof(float);
                break;
            case MYSQL_TYPE_DOUBLE:
                bind.buffer_length = sizeof(double);
                break;
            case MYSQL_TYPE_TIME:
            case MYSQL_TYPE_DATE:
            case MYSQL_TYPE_DATETIME:
            case MYSQL_TYPE_TIMESTAMP:
                bind.buffer_length = sizeof(MYSQL_TIME);
                break;
            default:
                // String types and others
                bind.buffer_length = fields[i].length + 1;
                break;
        }

        bind.buffer = malloc(bind.buffer_length);
        buffer_info.is_null = &buffer_info.is_null_value;
        buffer_info.length = &buffer_info.length_value;
        buffer_info.error = &buffer_info.error_value;
        bind.is_null = buffer_info.is_null;
        bind.length = buffer_info.length;
        bind.error = buffer_info.error;
    }

    // Bind result buffers
    if (mysql_stmt_bind_result(statement_->get(), bind_buffers_.data()) != 0) {
        throw common::DatabaseException("Failed to bind result buffers", "MySQL",
                                      mysql_stmt_errno(statement_->get()));
    }

    // Store the result set
    if (mysql_stmt_store_result(statement_->get()) != 0) {
        throw common::DatabaseException("Failed to store result set", "MySQL",
                                      mysql_stmt_errno(statement_->get()));
    }

    row_count_ = mysql_stmt_num_rows(statement_->get());
}

bool MySQLResultSet::next() {
    int ret = mysql_stmt_fetch(statement_->get());

    if (ret == 0) {
        current_row_++;
        return true;
    } else if (ret == MYSQL_NO_DATA) {
        return false;
    } else if (ret == MYSQL_DATA_TRUNCATED) {
        // Handle truncated data
        auto logger = common::Logger::get("omop-mysql");
        logger->warn("Data truncated in row {}", current_row_ + 1);
        current_row_++;
        return true;
    } else {
        throw common::DatabaseException(
            std::format("Failed to fetch row: {}", mysql_stmt_error(statement_->get())),
            "MySQL", mysql_stmt_errno(statement_->get()));
    }
}

std::any MySQLResultSet::get_value(size_t index) const {
    if (index >= column_count_) {
        throw common::DatabaseException(
            std::format("Column index {} out of range (0-{})", index, column_count_ - 1),
            "MySQL", 0);
    }

    return convert_value(index);
}

std::any MySQLResultSet::get_value(const std::string& column_name) const {
    size_t index = get_column_index(column_name);
    return get_value(index);
}

bool MySQLResultSet::is_null(size_t index) const {
    if (index >= column_count_) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "MySQL", 0);
    }

    const auto& buffer_info = reinterpret_cast<const BindBuffer&>(bind_buffers_[index]);
    return buffer_info.is_null_value;
}

bool MySQLResultSet::is_null(const std::string& column_name) const {
    size_t index = get_column_index(column_name);
    return is_null(index);
}

size_t MySQLResultSet::column_count() const {
    return column_count_;
}

std::string MySQLResultSet::column_name(size_t index) const {
    if (index >= column_count_) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "MySQL", 0);
    }

    return columns_[index].name;
}

std::string MySQLResultSet::column_type(size_t index) const {
    if (index >= column_count_) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "MySQL", 0);
    }

    auto it = MYSQL_TYPE_NAMES.find(columns_[index].type);
    if (it != MYSQL_TYPE_NAMES.end()) {
        return it->second;
    }

    return std::format("MYSQL_TYPE_{}", static_cast<int>(columns_[index].type));
}

size_t MySQLResultSet::get_column_index(const std::string& column_name) const {
    // Check cache first
    auto it = column_index_cache_.find(column_name);
    if (it != column_index_cache_.end()) {
        return it->second;
    }

    // Search for column
    for (size_t i = 0; i < column_count_; ++i) {
        if (columns_[i].name == column_name) {
            column_index_cache_[column_name] = i;
            return i;
        }
    }

    throw common::DatabaseException(
        std::format("Column '{}' not found", column_name),
        "MySQL", 0);
}

std::any MySQLResultSet::convert_value(size_t index) const {
    const auto& bind = bind_buffers_[index];
    const auto& buffer_info = reinterpret_cast<const BindBuffer&>(bind);

    if (buffer_info.is_null_value) {
        return std::any{};
    }

    switch (columns_[index].type) {
        case MYSQL_TYPE_TINY: {
            signed char value = *reinterpret_cast<signed char*>(bind.buffer);
            return static_cast<int>(value);
        }

        case MYSQL_TYPE_SHORT:
        case MYSQL_TYPE_YEAR: {
            short value = *reinterpret_cast<short*>(bind.buffer);
            return static_cast<int>(value);
        }

        case MYSQL_TYPE_LONG:
        case MYSQL_TYPE_INT24: {
            int value = *reinterpret_cast<int*>(bind.buffer);
            return value;
        }

        case MYSQL_TYPE_LONGLONG: {
            long long value = *reinterpret_cast<long long*>(bind.buffer);
            return value;
        }

        case MYSQL_TYPE_FLOAT: {
            float value = *reinterpret_cast<float*>(bind.buffer);
            return static_cast<double>(value);
        }

        case MYSQL_TYPE_DOUBLE: {
            double value = *reinterpret_cast<double*>(bind.buffer);
            return value;
        }

        case MYSQL_TYPE_DATE:
        case MYSQL_TYPE_TIME:
        case MYSQL_TYPE_DATETIME:
        case MYSQL_TYPE_TIMESTAMP: {
            MYSQL_TIME* time = reinterpret_cast<MYSQL_TIME*>(bind.buffer);

            std::tm tm = {};
            tm.tm_year = time->year - 1900;
            tm.tm_mon = time->month - 1;
            tm.tm_mday = time->day;
            tm.tm_hour = time->hour;
            tm.tm_min = time->minute;
            tm.tm_sec = time->second;

            auto time_point = std::chrono::system_clock::from_time_t(std::mktime(&tm));

            // Add microseconds
            if (time->second_part > 0) {
                auto micros = std::chrono::microseconds(time->second_part);
                time_point += micros;
            }

            return time_point;
        }

        case MYSQL_TYPE_BIT: {
            // Convert BIT to boolean for single bit, or integer for multiple bits
            // Bounds check for buffer access
            if (buffer_info.length_value > 8) {
                throw common::DatabaseException(
                    std::format("BIT field too large: {} bytes", buffer_info.length_value),
                    "MySQL", 0);
            }
            if (columns_[index].length == 1) {
                unsigned char value = *reinterpret_cast<unsigned char*>(bind.buffer);
                return static_cast<bool>(value & 1);
            } else {
                // Return as integer for multi-bit fields
                long long value = 0;
                unsigned char* bytes = reinterpret_cast<unsigned char*>(bind.buffer);
                for (unsigned long i = 0; i < buffer_info.length_value && i < 8; ++i) {
                    value = (value << 8) | bytes[i];
                }
                return value;
            }
        }

        default: {
            // String types and others
            std::string value(reinterpret_cast<char*>(bind.buffer), buffer_info.length_value);
            return value;
        }
    }
}

// MySQLPreparedStatement implementation

MySQLPreparedStatement::MySQLPreparedStatement(MYSQL* mysql, const std::string& sql)
    : sql_(sql) {

    statement_ = std::make_shared<MySQLStatement>(mysql);

    // Prepare the statement
    if (mysql_stmt_prepare(statement_->get(), sql.c_str(), sql.length()) != 0) {
        throw common::DatabaseException(
            std::format("Failed to prepare statement: {}", mysql_stmt_error(statement_->get())),
            "MySQL", mysql_stmt_errno(statement_->get()));
    }

    // Get parameter count
    param_count_ = mysql_stmt_param_count(statement_->get());
    if (param_count_ > 0) {
        parameters_.resize(param_count_);
    }
}

MySQLPreparedStatement::~MySQLPreparedStatement() {
    // Clean up parameter buffers
    for (auto& param : parameters_) {
        if (param.bind.buffer && param.bind.buffer != param.buffer.data()) {
            free(param.bind.buffer);
        }
    }
}

void MySQLPreparedStatement::bind(size_t index, const std::any& value) {
    if (index == 0) {
        throw common::DatabaseException("Parameter index must be 1-based", "MySQL", 0);
    }

    if (index > param_count_) {
        throw common::DatabaseException(
            std::format("Parameter index {} exceeds parameter count {}", index, param_count_),
            "MySQL", 0);
    }

    setup_parameter_binding(parameters_[index - 1], value);
}

void MySQLPreparedStatement::setup_parameter_binding(ParameterBinding& binding, const std::any& value) {
    binding.value = value;
    memset(&binding.bind, 0, sizeof(MYSQL_BIND));

    if (!value.has_value()) {
        binding.is_null = 1;
        binding.bind.is_null = &binding.is_null;
        binding.bind.buffer_type = MYSQL_TYPE_NULL;
    }
    else if (value.type() == typeid(bool)) {
        binding.is_null = 0;
        binding.buffer.resize(sizeof(signed char));
        *reinterpret_cast<signed char*>(binding.buffer.data()) =
            std::any_cast<bool>(value) ? 1 : 0;
        binding.bind.buffer_type = MYSQL_TYPE_TINY;
        binding.bind.buffer = binding.buffer.data();
        binding.bind.is_null = &binding.is_null;
    }
    else if (value.type() == typeid(int)) {
        binding.is_null = 0;
        binding.buffer.resize(sizeof(int));
        *reinterpret_cast<int*>(binding.buffer.data()) = std::any_cast<int>(value);
        binding.bind.buffer_type = MYSQL_TYPE_LONG;
        binding.bind.buffer = binding.buffer.data();
        binding.bind.is_null = &binding.is_null;
    }
    else if (value.type() == typeid(long long)) {
        binding.is_null = 0;
        binding.buffer.resize(sizeof(long long));
        *reinterpret_cast<long long*>(binding.buffer.data()) = std::any_cast<long long>(value);
        binding.bind.buffer_type = MYSQL_TYPE_LONGLONG;
        binding.bind.buffer = binding.buffer.data();
        binding.bind.is_null = &binding.is_null;
    }
    else if (value.type() == typeid(double)) {
        binding.is_null = 0;
        binding.buffer.resize(sizeof(double));
        *reinterpret_cast<double*>(binding.buffer.data()) = std::any_cast<double>(value);
        binding.bind.buffer_type = MYSQL_TYPE_DOUBLE;
        binding.bind.buffer = binding.buffer.data();
        binding.bind.is_null = &binding.is_null;
    }
    else if (value.type() == typeid(std::string)) {
        binding.is_null = 0;
        std::string str_val = std::any_cast<std::string>(value);
        binding.buffer.assign(str_val.begin(), str_val.end());
        binding.length = binding.buffer.size();
        binding.bind.buffer_type = MYSQL_TYPE_STRING;
        binding.bind.buffer = binding.buffer.data();
        binding.bind.buffer_length = binding.buffer.size();
        binding.bind.length = &binding.length;
        binding.bind.is_null = &binding.is_null;
    }
    else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
        binding.is_null = 0;
        binding.bind.buffer_type = MYSQL_TYPE_TIMESTAMP;

        auto tp = std::any_cast<std::chrono::system_clock::time_point>(value);
        auto time_t_val = std::chrono::system_clock::to_time_t(tp);
        std::tm tm = *std::localtime(&time_t_val);

        MYSQL_TIME* mysql_time = reinterpret_cast<MYSQL_TIME*>(malloc(sizeof(MYSQL_TIME)));
        memset(mysql_time, 0, sizeof(MYSQL_TIME));

        mysql_time->year = tm.tm_year + 1900;
        mysql_time->month = tm.tm_mon + 1;
        mysql_time->day = tm.tm_mday;
        mysql_time->hour = tm.tm_hour;
        mysql_time->minute = tm.tm_min;
        mysql_time->second = tm.tm_sec;

        // Add microseconds if available
        auto duration = tp.time_since_epoch();
        auto micros = std::chrono::duration_cast<std::chrono::microseconds>(duration).count() % 1000000;
        mysql_time->second_part = micros;

        binding.bind.buffer = mysql_time;
        binding.bind.buffer_length = sizeof(MYSQL_TIME);
        binding.bind.is_null = &binding.is_null;
    }
    else {
        throw common::DatabaseException(
            "Unsupported parameter type for MySQL", "MySQL", 0);
    }
}

void MySQLPreparedStatement::bind_parameters() {
    if (param_count_ == 0) return;

    std::vector<MYSQL_BIND> binds(param_count_);
    for (size_t i = 0; i < param_count_; ++i) {
        binds[i] = parameters_[i].bind;
    }

    if (mysql_stmt_bind_param(statement_->get(), binds.data()) != 0) {
        throw common::DatabaseException(
            std::format("Failed to bind parameters: {}", mysql_stmt_error(statement_->get())),
            "MySQL", mysql_stmt_errno(statement_->get()));
    }
}

std::unique_ptr<IResultSet> MySQLPreparedStatement::execute_query() {
    bind_parameters();

    if (mysql_stmt_execute(statement_->get()) != 0) {
        throw common::DatabaseException(
            std::format("Query execution failed: {}", mysql_stmt_error(statement_->get())),
            "MySQL", mysql_stmt_errno(statement_->get()));
    }

    return std::make_unique<MySQLResultSet>(statement_);
}

size_t MySQLPreparedStatement::execute_update() {
    bind_parameters();

    if (mysql_stmt_execute(statement_->get()) != 0) {
        throw common::DatabaseException(
            std::format("Update execution failed: {}", mysql_stmt_error(statement_->get())),
            "MySQL", mysql_stmt_errno(statement_->get()));
    }

    return mysql_stmt_affected_rows(statement_->get());
}

void MySQLPreparedStatement::clear_parameters() {
    for (auto& param : parameters_) {
        if (param.bind.buffer && param.bind.buffer != param.buffer.data()) {
            free(param.bind.buffer);
            param.bind.buffer = nullptr;
        }
        param.buffer.clear();
        memset(&param.bind, 0, sizeof(MYSQL_BIND));
    }

    if (mysql_stmt_reset(statement_->get()) != 0) {
        auto logger = common::Logger::get("omop-mysql");
        logger->warn("Failed to reset statement: {}", mysql_stmt_error(statement_->get()));
    }
}

// MySQLConnection implementation

MySQLConnection::MySQLConnection() {
    mysql_ = mysql_init(nullptr);
    if (!mysql_) {
        throw common::DatabaseException("Failed to initialize MySQL connection", "MySQL", 0);
    }
}

MySQLConnection::~MySQLConnection() {
    disconnect_internal();

    if (mysql_) {
        mysql_close(mysql_);
    }
}

void MySQLConnection::connect(const ConnectionParams& params) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (connected_) {
        disconnect();
    }

    // Set connection options
    set_connection_options(params);

    // Connect
    unsigned int port = params.port > 0 ? params.port : 3306;
    const char* unix_socket = nullptr;
    unsigned long client_flags = CLIENT_MULTI_STATEMENTS;

    // Check for SSL options
    auto ssl_it = params.options.find("ssl_mode");
    if (ssl_it != params.options.end() && ssl_it->second != "DISABLED") {
        client_flags |= CLIENT_SSL;
    }

    if (!mysql_real_connect(mysql_,
                           params.host.c_str(),
                           params.username.c_str(),
                           params.password.c_str(),
                           params.database.c_str(),
                           port,
                           unix_socket,
                           client_flags)) {
        throw common::DatabaseException(
            std::format("Failed to connect to MySQL: {}", mysql_error(mysql_)),
            "MySQL", mysql_errno(mysql_));
    }

    connected_ = true;

    // Set character set to UTF8
    if (mysql_set_character_set(mysql_, "utf8mb4") != 0) {
        auto logger = common::Logger::get("omop-mysql");
        logger->warn("Failed to set character set to utf8mb4: {}", mysql_error(mysql_));
    }

    auto logger = common::Logger::get("omop-mysql");
    logger->info("Connected to MySQL database '{}' on {}:{}",
                params.database, params.host, port);
}

void MySQLConnection::disconnect() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (connected_) {
        if (in_transaction_) {
            try {
                rollback_internal();
            } catch (...) {
                // Ignore errors during disconnect
            }
        }

        connected_ = false;

        auto logger = common::Logger::get("omop-mysql");
        logger->info("Disconnected from MySQL database");
    }
}

void MySQLConnection::disconnect_internal() noexcept {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (connected_) {
        if (in_transaction_) {
            try {
                rollback_internal();
            } catch (...) {
                // Ignore errors during disconnect in destructor
            }
        }
        connected_ = false;
    }
}

void MySQLConnection::rollback_internal() noexcept {
    if (!connected_ || !mysql_ || !in_transaction_) {
        return;
    }

    try {
        if (mysql_rollback(mysql_) == 0) {
            in_transaction_ = false;
        }
        // Don't throw exceptions in internal methods used by destructor
    } catch (...) {
        // Ignore all exceptions in destructor context
    }
}

bool MySQLConnection::is_connected() const {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connected_ || !mysql_) {
        return false;
    }

    // Ping to check connection
    return mysql_ping(mysql_) == 0;
}

std::unique_ptr<IResultSet> MySQLConnection::execute_query(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connected_) {
        throw common::DatabaseException("Not connected to database", "MySQL", 0);
    }

    // For queries that return results, use prepared statements
    auto stmt = std::make_shared<MySQLStatement>(mysql_);

    if (mysql_stmt_prepare(stmt->get(), sql.c_str(), sql.length()) != 0) {
        throw common::DatabaseException(
            std::format("Failed to prepare query: {}", mysql_stmt_error(stmt->get())),
            "MySQL", mysql_stmt_errno(stmt->get()));
    }

    if (mysql_stmt_execute(stmt->get()) != 0) {
        throw common::DatabaseException(
            std::format("Query execution failed: {}", mysql_stmt_error(stmt->get())),
            "MySQL", mysql_stmt_errno(stmt->get()));
    }

    return std::make_unique<MySQLResultSet>(stmt);
}

size_t MySQLConnection::execute_update(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connected_) {
        throw common::DatabaseException("Not connected to database", "MySQL", 0);
    }

    if (mysql_query(mysql_, sql.c_str()) != 0) {
        throw common::DatabaseException(
            std::format("Update execution failed: {}", mysql_error(mysql_)),
            "MySQL", mysql_errno(mysql_));
    }

    return mysql_affected_rows(mysql_);
}

std::unique_ptr<IPreparedStatement> MySQLConnection::prepare_statement(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!connected_) {
        throw common::DatabaseException("Not connected to database", "MySQL", 0);
    }

    return std::make_unique<MySQLPreparedStatement>(mysql_, sql);
}

void MySQLConnection::begin_transaction() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (in_transaction_) {
        throw common::DatabaseException("Transaction already in progress", "MySQL", 0);
    }

    execute_update("START TRANSACTION");
    in_transaction_ = true;
}

void MySQLConnection::commit() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!in_transaction_) {
        throw common::DatabaseException("No transaction in progress", "MySQL", 0);
    }

    execute_update("COMMIT");
    in_transaction_ = false;
}

void MySQLConnection::rollback() {
    std::lock_guard<std::mutex> lock(connection_mutex_);

    if (!in_transaction_) {
        throw common::DatabaseException("No transaction in progress", "MySQL", 0);
    }

    execute_update("ROLLBACK");
    in_transaction_ = false;
}

std::string MySQLConnection::get_version() const {
    if (!connected_) {
        return "Not connected";
    }

    return mysql_get_server_info(mysql_);
}

void MySQLConnection::set_query_timeout(int seconds) {
    query_timeout_ = seconds;

    if (connected_) {
        std::string timeout_sql = std::format("SET SESSION max_execution_time = {}", seconds * 1000);
        const_cast<MySQLConnection*>(this)->execute_update(timeout_sql);
    }
}

bool MySQLConnection::table_exists(const std::string& table_name,
                                 const std::string& schema) const {
    if (!connected_) {
        return false;
    }

    std::string query;
    if (schema.empty()) {
        query = std::format(
            "SELECT COUNT(*) FROM information_schema.tables "
            "WHERE table_schema = DATABASE() AND table_name = '{}'",
            table_name);
    } else {
        query = std::format(
            "SELECT COUNT(*) FROM information_schema.tables "
            "WHERE table_schema = '{}' AND table_name = '{}'",
            schema, table_name);
    }

    if (mysql_query(mysql_, query.c_str()) != 0) {
        return false;
    }

    MYSQL_RES* result = mysql_store_result(mysql_);
    if (!result) {
        return false;
    }

    bool exists = false;
    MYSQL_ROW row = mysql_fetch_row(result);
    if (row && row[0]) {
        exists = std::stoi(row[0]) > 0;
    }

    mysql_free_result(result);
    return exists;
}

void MySQLConnection::check_error(const std::string& operation) const {
    if (mysql_errno(mysql_) != 0) {
        throw common::DatabaseException(
            std::format("{} failed: {}", operation, mysql_error(mysql_)),
            "MySQL", mysql_errno(mysql_));
    }
}

void MySQLConnection::set_connection_options(const ConnectionParams& params) {
    // Set connection timeout
    unsigned int timeout = 10;  // Default 10 seconds
    auto timeout_it = params.options.find("connect_timeout");
    if (timeout_it != params.options.end()) {
        timeout = std::stoi(timeout_it->second);
    }
    mysql_options(mysql_, MYSQL_OPT_CONNECT_TIMEOUT, &timeout);

    // Set read timeout
    if (query_timeout_ > 0) {
        unsigned int read_timeout = query_timeout_;
        mysql_options(mysql_, MYSQL_OPT_READ_TIMEOUT, &read_timeout);
    }

    // Enable automatic reconnection
    bool reconnect = true;
    mysql_options(mysql_, MYSQL_OPT_RECONNECT, &reconnect);

    // Set SSL options if provided
    auto ssl_mode_it = params.options.find("ssl_mode");
    if (ssl_mode_it != params.options.end()) {
        const char* ssl_mode = ssl_mode_it->second.c_str();
        mysql_options(mysql_, MYSQL_OPT_SSL_MODE, ssl_mode);
    }

    auto ssl_ca_it = params.options.find("ssl_ca");
    if (ssl_ca_it != params.options.end()) {
        mysql_options(mysql_, MYSQL_OPT_SSL_CA, ssl_ca_it->second.c_str());
    }

    auto ssl_cert_it = params.options.find("ssl_cert");
    if (ssl_cert_it != params.options.end()) {
        mysql_options(mysql_, MYSQL_OPT_SSL_CERT, ssl_cert_it->second.c_str());
    }

    auto ssl_key_it = params.options.find("ssl_key");
    if (ssl_key_it != params.options.end()) {
        mysql_options(mysql_, MYSQL_OPT_SSL_KEY, ssl_key_it->second.c_str());
    }
}

// MySQLExtractor implementation

std::string MySQLExtractor::build_query() const {
    // Use parent implementation and add MySQL-specific optimizations
    std::string query = DatabaseExtractor::build_query();

    // Add MySQL-specific query hints if needed
    // For example, adding SQL_BUFFER_RESULT for large result sets
    if (query.find("SELECT") == 0) {
        query.insert(6, " SQL_BUFFER_RESULT");
    }

    return query;
}

// MySQLRegistrar implementation
void MySQLRegistrar::register_components() {
    DatabaseConnectionFactory::instance().register_type(
        "mysql",
        [](const IDatabaseConnection::ConnectionParams& params) {
            auto conn = std::make_unique<MySQLConnection>();
            conn->connect(params);
            return conn;
        }
    );

    DatabaseConnectionFactory::instance().register_type(
        "mariadb",  // Alias for MariaDB
        [](const IDatabaseConnection::ConnectionParams& params) {
            auto conn = std::make_unique<MySQLConnection>();
            conn->connect(params);
            return conn;
        }
    );
}

} // namespace omop::extract