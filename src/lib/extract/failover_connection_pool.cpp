#include "failover_connection_pool.h"
#include "database_connector.h"
#include <algorithm>
#include <random>
#include <chrono>
#include <thread>
#include <sstream>
#include <iostream>
#include <limits>
#include <numeric>
#include <tuple>

namespace omop::extract {

// HealthAwareConnection implementation
HealthAwareConnection::HealthAwareConnection(std::unique_ptr<IDatabaseConnection> connection,
                                           const DatabaseServerConfig& server_config)
    : connection_(std::move(connection))
    , server_config_(server_config)
    , last_health_check_(std::chrono::system_clock::now())
    , last_failure_time_(std::chrono::system_clock::now()) {
}

ConnectionHealth HealthAwareConnection::check_health() {
    std::lock_guard<std::mutex> lock(health_mutex_);
    
    auto now = std::chrono::system_clock::now();
    auto time_since_last_check = std::chrono::duration_cast<std::chrono::seconds>(
        now - last_health_check_).count();
    
    // Only check health if enough time has passed
    if (time_since_last_check < server_config_.health_check_interval.count()) {
        return health_status_.load();
    }
    
    last_health_check_ = now;
    
    try {
        // Execute health check query
        if (connection_ && connection_->execute_query(server_config_.health_check_query)) {
            record_success();
            return ConnectionHealth::HEALTHY;
        } else {
            record_failure();
            return ConnectionHealth::FAILED;
        }
    } catch (const std::exception&) {
        record_failure();
        return ConnectionHealth::FAILED;
    }
}

void HealthAwareConnection::record_success() {
    consecutive_failures_ = 0;
    total_operations_++;
    successful_operations_++;
    health_status_ = ConnectionHealth::HEALTHY;
}

void HealthAwareConnection::record_failure() {
    consecutive_failures_++;
    total_operations_++;
    last_failure_time_ = std::chrono::system_clock::now();
    
    if (consecutive_failures_ >= server_config_.max_consecutive_failures) {
        health_status_ = ConnectionHealth::FAILED;
    } else if (consecutive_failures_ > 0) {
        health_status_ = ConnectionHealth::DEGRADED;
    }
}

bool HealthAwareConnection::should_retire() const {
    auto now = std::chrono::system_clock::now();
    auto time_since_failure = std::chrono::duration_cast<std::chrono::seconds>(
        now - last_failure_time_).count();
    
    return consecutive_failures_ >= server_config_.max_consecutive_failures &&
           time_since_failure > server_config_.failure_recovery_time.count();
}

// FailoverConnectionPool implementation
FailoverConnectionPool::FailoverConnectionPool(const std::vector<DatabaseServerConfig>& servers,
                                             FailoverStrategy strategy)
    : servers_(servers)
    , failover_strategy_(strategy) {
    for (const auto& server : servers_) {
        initialize_server_pool(server);
    }
}

FailoverConnectionPool::~FailoverConnectionPool() {
    stop_health_monitoring();
}

std::unique_ptr<HealthAwareConnection> FailoverConnectionPool::get_connection(
    std::chrono::seconds timeout) {
    total_get_requests_++;
    
    auto start_time = std::chrono::steady_clock::now();
    std::unique_ptr<HealthAwareConnection> connection;
    
    while (std::chrono::steady_clock::now() - start_time < timeout) {
        const DatabaseServerConfig* selected_server = select_server();
        if (!selected_server) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }
        
        std::string server_key = get_server_key(*selected_server);
        
        {
            std::lock_guard<std::mutex> lock(pool_mutex_);
            auto& pool = server_pools_[server_key];
            
            if (!pool.empty()) {
                connection = std::move(pool.front());
                pool.pop();
                server_metrics_[server_key].active_connections++;
                break;
            }
        }
        
        // Try to create a new connection
        try {
            connection = create_connection(*selected_server);
            if (connection) {
                server_metrics_[server_key].active_connections++;
                total_connections_created_++;
                break;
            }
        } catch (const std::exception&) {
            // Server failed, try next one
            continue;
        }
    }
    
    if (!connection) {
        failed_get_requests_++;
    }
    
    return connection;
}

void FailoverConnectionPool::return_connection(std::unique_ptr<HealthAwareConnection> connection) {
    if (!connection) return;
    
    std::string server_key = get_server_key(connection->get_server_config());
    
    {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        auto& pool = server_pools_[server_key];
        auto& metrics = server_metrics_[server_key];
        
        if (pool.size() < connection->get_server_config().max_connections) {
            pool.push(std::move(connection));
        } else {
            total_connections_destroyed_++;
        }
        
        if (metrics.active_connections > 0) {
            metrics.active_connections--;
        }
    }
    
    pool_condition_.notify_one();
}

std::unordered_map<std::string, size_t> FailoverConnectionPool::get_statistics() const {
    std::lock_guard<std::mutex> lock(pool_mutex_);
    
    std::unordered_map<std::string, size_t> stats;
    stats["total_connections_created"] = total_connections_created_.load();
    stats["total_connections_destroyed"] = total_connections_destroyed_.load();
    stats["total_get_requests"] = total_get_requests_.load();
    stats["failed_get_requests"] = failed_get_requests_.load();
    stats["server_failovers"] = server_failovers_.load();
    
    for (const auto& [server_key, metrics] : server_metrics_) {
        stats[server_key + "_active_connections"] = metrics.active_connections.load();
        stats[server_key + "_total_requests"] = metrics.total_requests.load();
        stats[server_key + "_error_count"] = metrics.error_count.load();
    }
    
    return stats;
}

void FailoverConnectionPool::check_server_health() {
    for (auto& [server_key, pool] : server_pools_) {
        cleanup_failed_connections(server_key);
    }
}

void FailoverConnectionPool::start_health_monitoring() {
    if (health_monitoring_active_.load()) return;
    
    health_monitoring_active_ = true;
    health_monitor_thread_ = std::make_unique<std::thread>([this]() {
        health_monitor_loop();
    });
}

void FailoverConnectionPool::stop_health_monitoring() {
    if (!health_monitoring_active_.load()) return;
    
    health_monitoring_active_ = false;
    if (health_monitor_thread_ && health_monitor_thread_->joinable()) {
        health_monitor_thread_->join();
    }
}

void FailoverConnectionPool::add_server(const DatabaseServerConfig& server_config) {
    std::lock_guard<std::mutex> lock(pool_mutex_);
    servers_.push_back(server_config);
    initialize_server_pool(server_config);
}

void FailoverConnectionPool::remove_server(const std::string& host, int port) {
    std::lock_guard<std::mutex> lock(pool_mutex_);
    
    auto it = std::remove_if(servers_.begin(), servers_.end(),
                             [&](const DatabaseServerConfig& config) {
                                 return config.host == host && config.port == port;
                             });
    servers_.erase(it, servers_.end());
    
    std::string server_key = host + ":" + std::to_string(port);
    server_pools_.erase(server_key);
    server_metrics_.erase(server_key);
}

std::vector<DatabaseServerConfig> FailoverConnectionPool::get_healthy_servers() const {
    std::lock_guard<std::mutex> lock(pool_mutex_);
    std::vector<DatabaseServerConfig> healthy_servers;
    
    for (const auto& server : servers_) {
        if (is_server_healthy(server)) {
            healthy_servers.push_back(server);
        }
    }
    
    return healthy_servers;
}

void FailoverConnectionPool::force_failover(const std::string& failed_server_host, int failed_server_port) {
    std::lock_guard<std::mutex> lock(pool_mutex_);
    server_failovers_++;
    
    // Mark server as failed and trigger health check
    std::string server_key = get_server_key({failed_server_host, failed_server_port});
    if (server_metrics_.count(server_key)) {
        server_metrics_[server_key].server_health = ConnectionHealth::FAILED;
    }
}

std::unique_ptr<HealthAwareConnection> FailoverConnectionPool::create_connection(
    const DatabaseServerConfig& server_config) {
    // This is a simplified implementation
    // In a real implementation, you would use the appropriate database connector
    // based on the server configuration
    return nullptr;
}

const DatabaseServerConfig* FailoverConnectionPool::select_server() {
    switch (failover_strategy_) {
        case FailoverStrategy::PRIORITY_BASED:
            return select_priority_based();
        case FailoverStrategy::ROUND_ROBIN:
            return select_round_robin();
        case FailoverStrategy::LOAD_BALANCED:
            return select_load_balanced();
        case FailoverStrategy::RANDOM:
            return select_random();
        case FailoverStrategy::LOCALITY_AWARE:
            return select_priority_based(); // Simplified for now
        default:
            return select_priority_based();
    }
}

const DatabaseServerConfig* FailoverConnectionPool::select_priority_based() {
    std::lock_guard<std::mutex> lock(pool_mutex_);
    
    // Sort servers by priority (lower number = higher priority)
    std::vector<DatabaseServerConfig> sorted_servers = servers_;
    std::sort(sorted_servers.begin(), sorted_servers.end(),
              [](const DatabaseServerConfig& a, const DatabaseServerConfig& b) {
                  return a.priority < b.priority;
              });
    
    for (const auto& server : sorted_servers) {
        if (is_server_healthy(server)) {
            return &server;
        }
    }
    
    return nullptr;
}

const DatabaseServerConfig* FailoverConnectionPool::select_round_robin() {
    std::lock_guard<std::mutex> lock(pool_mutex_);
    
    std::vector<DatabaseServerConfig> healthy_servers;
    for (const auto& server : servers_) {
        if (is_server_healthy(server)) {
            healthy_servers.push_back(server);
        }
    }
    
    if (healthy_servers.empty()) return nullptr;
    
    size_t index = round_robin_index_++ % healthy_servers.size();
    return &healthy_servers[index];
}

const DatabaseServerConfig* FailoverConnectionPool::select_load_balanced() {
    std::lock_guard<std::mutex> lock(pool_mutex_);
    
    const DatabaseServerConfig* best_server = nullptr;
    size_t min_load = std::numeric_limits<size_t>::max();
    
    for (const auto& server : servers_) {
        if (!is_server_healthy(server)) continue;
        
        std::string server_key = get_server_key(server);
        if (server_metrics_.count(server_key)) {
            size_t load = server_metrics_.at(server_key).active_connections.load();
            if (load < min_load) {
                min_load = load;
                best_server = &server;
            }
        }
    }
    
    return best_server;
}

const DatabaseServerConfig* FailoverConnectionPool::select_random() {
    std::lock_guard<std::mutex> lock(pool_mutex_);
    
    std::vector<const DatabaseServerConfig*> healthy_servers;
    for (const auto& server : servers_) {
        if (is_server_healthy(server)) {
            healthy_servers.push_back(&server);
        }
    }
    
    if (healthy_servers.empty()) return nullptr;
    
    static std::random_device rd;
    static std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, healthy_servers.size() - 1);
    
    return healthy_servers[dis(gen)];
}

bool FailoverConnectionPool::is_server_healthy(const DatabaseServerConfig& server_config) const {
    std::string server_key = get_server_key(server_config);
    
    if (server_metrics_.count(server_key)) {
        return server_metrics_.at(server_key).server_health.load() == ConnectionHealth::HEALTHY;
    }
    
    return true; // Assume healthy if no metrics available
}

void FailoverConnectionPool::update_server_metrics(const std::string& server_key,
                                                  std::chrono::milliseconds operation_time,
                                                  bool success) {
    if (server_metrics_.count(server_key)) {
        auto& metrics = server_metrics_[server_key];
        metrics.total_requests++;
        
        if (!success) {
            metrics.error_count++;
        }
        
        // Update average response time
        double current_avg = metrics.avg_response_time.load();
        size_t total_requests = metrics.total_requests.load();
        double new_avg = (current_avg * (total_requests - 1) + operation_time.count()) / total_requests;
        metrics.avg_response_time = new_avg;
        
        metrics.last_update = std::chrono::system_clock::now();
    }
}

std::string FailoverConnectionPool::get_server_key(const DatabaseServerConfig& server_config) const {
    return server_config.host + ":" + std::to_string(server_config.port);
}

void FailoverConnectionPool::health_monitor_loop() {
    while (health_monitoring_active_.load()) {
        check_server_health();
        std::this_thread::sleep_for(std::chrono::seconds(30));
    }
}

void FailoverConnectionPool::initialize_server_pool(const DatabaseServerConfig& server_config) {
    std::string server_key = get_server_key(server_config);
    server_pools_[server_key] = std::queue<std::unique_ptr<HealthAwareConnection>>();
    server_metrics_.emplace(std::piecewise_construct, 
                            std::forward_as_tuple(server_key),
                            std::forward_as_tuple());
}

void FailoverConnectionPool::cleanup_failed_connections(const std::string& server_key) {
    if (!server_pools_.count(server_key)) return;
    
    auto& pool = server_pools_[server_key];
    std::queue<std::unique_ptr<HealthAwareConnection>> cleaned_pool;
    
    while (!pool.empty()) {
        auto connection = std::move(pool.front());
        pool.pop();
        
        if (connection && !connection->should_retire()) {
            cleaned_pool.push(std::move(connection));
        } else {
            total_connections_destroyed_++;
        }
    }
    
    pool = std::move(cleaned_pool);
}

// DatabaseCircuitBreaker implementation
DatabaseCircuitBreaker::DatabaseCircuitBreaker(size_t failure_threshold,
                                               std::chrono::seconds recovery_timeout,
                                               size_t success_threshold)
    : failure_threshold_(failure_threshold)
    , recovery_timeout_(recovery_timeout)
    , success_threshold_(success_threshold) {
}

bool DatabaseCircuitBreaker::is_request_allowed() {
    std::lock_guard<std::mutex> lock(state_mutex_);
    
    switch (state_.load()) {
        case State::CLOSED:
            return true;
        case State::OPEN: {
            auto now = std::chrono::system_clock::now();
            if (now - last_failure_time_ >= recovery_timeout_) {
                transition_to_half_open();
                return true;
            }
            return false;
        }
        case State::HALF_OPEN:
            return true;
        default:
            return false;
    }
}

void DatabaseCircuitBreaker::record_success() {
    std::lock_guard<std::mutex> lock(state_mutex_);
    
    if (state_.load() == State::HALF_OPEN) {
        success_count_++;
        if (success_count_ >= success_threshold_) {
            transition_to_closed();
        }
    }
}

void DatabaseCircuitBreaker::record_failure() {
    std::lock_guard<std::mutex> lock(state_mutex_);
    
    failure_count_++;
    last_failure_time_ = std::chrono::system_clock::now();
    
    if (state_.load() == State::CLOSED && failure_count_ >= failure_threshold_) {
        transition_to_open();
    } else if (state_.load() == State::HALF_OPEN) {
        transition_to_open();
    }
}

void DatabaseCircuitBreaker::reset() {
    std::lock_guard<std::mutex> lock(state_mutex_);
    transition_to_closed();
}

void DatabaseCircuitBreaker::transition_to_open() {
    state_ = State::OPEN;
}

void DatabaseCircuitBreaker::transition_to_half_open() {
    state_ = State::HALF_OPEN;
    success_count_ = 0;
}

void DatabaseCircuitBreaker::transition_to_closed() {
    state_ = State::CLOSED;
    failure_count_ = 0;
    success_count_ = 0;
}

// FailoverConnection implementation
FailoverConnection::FailoverConnection(std::unique_ptr<HealthAwareConnection> connection,
                                     FailoverConnectionPool* pool)
    : connection_(std::move(connection))
    , pool_(pool) {
}

FailoverConnection::~FailoverConnection() {
    if (pool_ && connection_) {
        pool_->return_connection(std::move(connection_));
    }
}

void FailoverConnection::record_operation_result(bool success) {
    if (connection_) {
        if (success) {
            connection_->record_success();
        } else {
            connection_->record_failure();
        }
    }
}

} // namespace omop::extract
