#pragma once

#include <chrono>
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <atomic>
#include <mutex>
#include <fstream>
#include <functional>

namespace omop::extract {

/**
 * @brief Performance metrics collected during extraction
 */
struct PerformanceMetrics {
    // Timing metrics
    std::chrono::duration<double> total_time{0};
    std::chrono::duration<double> io_time{0};
    std::chrono::duration<double> parse_time{0};
    std::chrono::duration<double> conversion_time{0};
    
    // Throughput metrics
    size_t total_records{0};
    size_t total_bytes{0};
    double records_per_second{0.0};
    double bytes_per_second{0.0};
    double megabytes_per_second{0.0};
    
    // Memory metrics
    size_t peak_memory_usage{0};
    size_t current_memory_usage{0};
    size_t memory_allocations{0};
    size_t memory_deallocations{0};
    
    // Error metrics
    size_t parsing_errors{0};
    size_t conversion_errors{0};
    size_t io_errors{0};
    
    // Batch metrics
    size_t total_batches{0};
    size_t avg_batch_size{0};
    double avg_batch_time{0.0};
    double min_batch_time{std::numeric_limits<double>::max()};
    double max_batch_time{0.0};
    
    // CPU metrics (if available)
    double cpu_usage_percent{0.0};
    size_t context_switches{0};
    
    // I/O metrics
    size_t disk_reads{0};
    size_t cache_hits{0};
    size_t cache_misses{0};
    
    // Quality metrics
    double data_quality_score{1.0};
    size_t null_values{0};
    size_t duplicate_records{0};
};

/**
 * @brief Benchmark configuration
 */
struct BenchmarkConfig {
    std::string name;
    std::string description;
    size_t target_records_per_second{15000};    // Target performance
    size_t warmup_iterations{3};                // Number of warmup runs
    size_t benchmark_iterations{5};             // Number of benchmark runs
    bool enable_memory_profiling{true};         // Enable memory tracking
    bool enable_cpu_profiling{false};           // Enable CPU tracking (requires platform support)
    bool save_detailed_metrics{true};           // Save detailed per-batch metrics
    std::string output_format{"json"};          // Output format: json, csv, txt
    std::string output_file;                    // Output file path (empty = stdout)
};

/**
 * @brief Real-time performance monitor with benchmarking capabilities
 */
class PerformanceProfiler {
public:
    /**
     * @brief Constructor
     * @param config Benchmark configuration
     */
    explicit PerformanceProfiler(const BenchmarkConfig& config = {});
    
    /**
     * @brief Destructor
     */
    ~PerformanceProfiler();
    
    /**
     * @brief Start performance monitoring
     * @param description Optional description of the operation
     */
    void start_monitoring(const std::string& description = "");
    
    /**
     * @brief Stop performance monitoring
     */
    void stop_monitoring();
    
    /**
     * @brief Record start of batch processing
     * @param batch_size Size of the batch being processed
     */
    void start_batch(size_t batch_size);
    
    /**
     * @brief Record end of batch processing
     * @param records_processed Number of records actually processed
     * @param errors Number of errors encountered
     */
    void end_batch(size_t records_processed, size_t errors = 0);
    
    /**
     * @brief Record I/O operation start
     */
    void start_io();
    
    /**
     * @brief Record I/O operation end
     * @param bytes_transferred Number of bytes read/written
     */
    void end_io(size_t bytes_transferred = 0);
    
    /**
     * @brief Record parsing operation start
     */
    void start_parse();
    
    /**
     * @brief Record parsing operation end
     * @param records_parsed Number of records parsed
     * @param errors Number of parsing errors
     */
    void end_parse(size_t records_parsed = 0, size_t errors = 0);
    
    /**
     * @brief Record conversion operation start
     */
    void start_conversion();
    
    /**
     * @brief Record conversion operation end
     * @param records_converted Number of records converted
     * @param errors Number of conversion errors
     */
    void end_conversion(size_t records_converted = 0, size_t errors = 0);
    
    /**
     * @brief Record memory allocation
     * @param bytes Number of bytes allocated
     */
    void record_memory_allocation(size_t bytes);
    
    /**
     * @brief Record memory deallocation
     * @param bytes Number of bytes deallocated
     */
    void record_memory_deallocation(size_t bytes);
    
    /**
     * @brief Update memory usage snapshot
     */
    void update_memory_usage();
    
    /**
     * @brief Record data quality metrics
     * @param null_count Number of null values encountered
     * @param duplicate_count Number of duplicate records
     * @param quality_score Overall quality score (0.0 to 1.0)
     */
    void record_quality_metrics(size_t null_count, size_t duplicate_count, double quality_score);
    
    /**
     * @brief Get current performance metrics
     * @return PerformanceMetrics Current metrics snapshot
     */
    PerformanceMetrics get_metrics() const;
    
    /**
     * @brief Check if target performance is being met
     * @return bool True if performance meets or exceeds target
     */
    bool meets_performance_target() const;
    
    /**
     * @brief Generate performance report
     * @param format Output format ("json", "csv", "txt")
     * @return std::string Formatted performance report
     */
    std::string generate_report(const std::string& format = "txt") const;
    
    /**
     * @brief Save performance report to file
     * @param filepath Output file path
     * @param format Output format
     */
    void save_report(const std::string& filepath, const std::string& format = "json") const;
    
    /**
     * @brief Run benchmark test
     * @param test_function Function to benchmark
     * @param test_data Test data description
     * @return PerformanceMetrics Benchmark results
     */
    template<typename TestFunc>
    PerformanceMetrics run_benchmark(TestFunc test_function, const std::string& test_data);
    
    /**
     * @brief Compare with baseline performance
     * @param baseline Baseline metrics to compare against
     * @return std::string Comparison report
     */
    std::string compare_with_baseline(const PerformanceMetrics& baseline) const;

    /**
     * @brief Reset all metrics
     */
    void reset();

private:
    BenchmarkConfig config_;
    mutable std::mutex metrics_mutex_;
    
    // Timing state
    std::chrono::high_resolution_clock::time_point start_time_;
    std::chrono::high_resolution_clock::time_point batch_start_time_;
    std::chrono::high_resolution_clock::time_point io_start_time_;
    std::chrono::high_resolution_clock::time_point parse_start_time_;
    std::chrono::high_resolution_clock::time_point conversion_start_time_;
    
    // Metrics storage
    PerformanceMetrics metrics_;
    
    // Detailed batch metrics for analysis
    std::vector<double> batch_times_;
    std::vector<size_t> batch_sizes_;
    std::vector<size_t> batch_errors_;
    
    // Memory tracking
    std::atomic<size_t> current_allocated_memory_{0};
    std::atomic<size_t> peak_allocated_memory_{0};
    
    // Performance monitoring state
    bool monitoring_active_{false};
    bool batch_active_{false};
    bool io_active_{false};
    bool parse_active_{false};
    bool conversion_active_{false};
    
    /**
     * @brief Calculate current memory usage (platform specific)
     * @return size_t Current memory usage in bytes
     */
    size_t get_current_memory_usage() const;
    
    /**
     * @brief Calculate CPU usage (platform specific)
     * @return double CPU usage percentage
     */
    double get_cpu_usage() const;
    
    /**
     * @brief Update computed metrics from raw data
     */
    void update_computed_metrics();
    
    /**
     * @brief Generate JSON format report
     * @return std::string JSON report
     */
    std::string generate_json_report() const;
    
    /**
     * @brief Generate CSV format report
     * @return std::string CSV report
     */
    std::string generate_csv_report() const;
    
    /**
     * @brief Generate text format report
     * @return std::string Text report
     */
    std::string generate_text_report() const;
};

/**
 * @brief Benchmark suite for systematic performance testing
 */
class BenchmarkSuite {
public:
    /**
     * @brief Constructor
     */
    BenchmarkSuite() = default;
    
    /**
     * @brief Add benchmark test
     * @param name Test name
     * @param config Benchmark configuration
     * @param test_function Test function to run
     * @param test_description Description of test
     */
    template<typename TestFunc>
    void add_benchmark(const std::string& name, 
                      const BenchmarkConfig& config,
                      TestFunc test_function,
                      const std::string& test_description = "");
    
    /**
     * @brief Run all benchmarks
     * @return std::unordered_map<std::string, PerformanceMetrics> Results by test name
     */
    std::unordered_map<std::string, PerformanceMetrics> run_all_benchmarks();
    
    /**
     * @brief Run specific benchmark
     * @param name Benchmark name
     * @return PerformanceMetrics Benchmark results
     */
    PerformanceMetrics run_benchmark(const std::string& name);
    
    /**
     * @brief Generate comparison report across all benchmarks
     * @return std::string Comparison report
     */
    std::string generate_comparison_report() const;
    
    /**
     * @brief Save all benchmark results
     * @param directory Output directory
     * @param format Output format
     */
    void save_all_results(const std::string& directory, const std::string& format = "json") const;

private:
    struct BenchmarkTest {
        std::string name;
        BenchmarkConfig config;
        std::function<void(PerformanceProfiler&)> test_function;
        std::string description;
        PerformanceMetrics last_result;
    };
    
    std::vector<BenchmarkTest> benchmarks_;
    std::unordered_map<std::string, PerformanceMetrics> results_;
};

/**
 * @brief RAII performance timer for automatic measurement
 */
class ScopedTimer {
public:
    /**
     * @brief Constructor - starts timing
     * @param profiler Performance profiler to use
     * @param operation_type Type of operation being timed
     */
    ScopedTimer(PerformanceProfiler& profiler, const std::string& operation_type);
    
    /**
     * @brief Destructor - stops timing
     */
    ~ScopedTimer();

private:
    PerformanceProfiler& profiler_;
    std::string operation_type_;
    std::chrono::high_resolution_clock::time_point start_time_;
};

// Template implementation
template<typename TestFunc>
PerformanceMetrics PerformanceProfiler::run_benchmark(TestFunc test_function, const std::string& test_data) {
    reset();
    
    // Warmup runs
    for (size_t i = 0; i < config_.warmup_iterations; ++i) {
        reset();
        start_monitoring("Warmup run " + std::to_string(i + 1));
        test_function(*this);
        stop_monitoring();
    }
    
    // Benchmark runs
    std::vector<PerformanceMetrics> run_metrics;
    run_metrics.reserve(config_.benchmark_iterations);
    
    for (size_t i = 0; i < config_.benchmark_iterations; ++i) {
        reset();
        start_monitoring("Benchmark run " + std::to_string(i + 1) + ": " + test_data);
        test_function(*this);
        stop_monitoring();
        run_metrics.push_back(get_metrics());
    }
    
    // Calculate average metrics
    PerformanceMetrics avg_metrics{};
    for (const auto& metrics : run_metrics) {
        avg_metrics.total_time += metrics.total_time;
        avg_metrics.io_time += metrics.io_time;
        avg_metrics.parse_time += metrics.parse_time;
        avg_metrics.conversion_time += metrics.conversion_time;
        avg_metrics.total_records += metrics.total_records;
        avg_metrics.total_bytes += metrics.total_bytes;
        avg_metrics.peak_memory_usage = std::max(avg_metrics.peak_memory_usage, metrics.peak_memory_usage);
        avg_metrics.parsing_errors += metrics.parsing_errors;
        avg_metrics.conversion_errors += metrics.conversion_errors;
        avg_metrics.io_errors += metrics.io_errors;
        avg_metrics.total_batches += metrics.total_batches;
    }
    
    // Average the accumulated values
    if (config_.benchmark_iterations > 0) {
        avg_metrics.total_time /= config_.benchmark_iterations;
        avg_metrics.io_time /= config_.benchmark_iterations;
        avg_metrics.parse_time /= config_.benchmark_iterations;
        avg_metrics.conversion_time /= config_.benchmark_iterations;
        avg_metrics.total_records /= config_.benchmark_iterations;
        avg_metrics.total_bytes /= config_.benchmark_iterations;
        avg_metrics.parsing_errors /= config_.benchmark_iterations;
        avg_metrics.conversion_errors /= config_.benchmark_iterations;
        avg_metrics.io_errors /= config_.benchmark_iterations;
        avg_metrics.total_batches /= config_.benchmark_iterations;
        
        // Calculate derived metrics
        if (avg_metrics.total_time.count() > 0) {
            avg_metrics.records_per_second = avg_metrics.total_records / avg_metrics.total_time.count();
            avg_metrics.bytes_per_second = avg_metrics.total_bytes / avg_metrics.total_time.count();
            avg_metrics.megabytes_per_second = avg_metrics.bytes_per_second / (1024.0 * 1024.0);
        }
    }
    
    return avg_metrics;
}

} // namespace omop::extract