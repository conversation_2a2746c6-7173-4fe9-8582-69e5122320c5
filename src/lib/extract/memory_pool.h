#pragma once

#include <memory>
#include <vector>
#include <stack>
#include <mutex>
#include <atomic>
#include <functional>
#include <chrono>

namespace omop::extract {

/**
 * @brief Generic object pool for memory optimization
 * 
 * Provides a thread-safe pool of reusable objects to reduce allocation overhead.
 * Particularly useful for frequently created/destroyed objects like Records.
 */
template<typename T>
class ObjectPool {
public:
    /**
     * @brief Constructor
     * @param initial_size Initial number of objects to pre-allocate
     * @param max_size Maximum number of objects to keep in pool (0 = unlimited)
     * @param factory Factory function to create new objects
     */
    explicit ObjectPool(size_t initial_size = 10, 
                       size_t max_size = 100,
                       std::function<std::unique_ptr<T>()> factory = nullptr)
        : max_size_(max_size)
        , factory_(factory ? std::move(factory) : []() { return std::make_unique<T>(); })
        , total_created_(0)
        , total_reused_(0) {
        
        // Pre-allocate initial objects
        for (size_t i = 0; i < initial_size; ++i) {
            auto obj = factory_();
            pool_.push(std::move(obj));
        }
        total_created_.store(initial_size);
    }

    /**
     * @brief Acquire an object from the pool
     * @return std::unique_ptr<T> Object ready for use
     */
    std::unique_ptr<T> acquire() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!pool_.empty()) {
            auto obj = std::move(pool_.top());
            pool_.pop();
            total_reused_.fetch_add(1);
            return obj;
        }
        
        // No objects available, create new one
        total_created_.fetch_add(1);
        return factory_();
    }

    /**
     * @brief Return an object to the pool
     * @param obj Object to return (will be reset if possible)
     */
    void release(std::unique_ptr<T> obj) {
        if (!obj) return;
        
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Check pool size limit
        if (max_size_ > 0 && pool_.size() >= max_size_) {
            // Pool is full, just let the object be destroyed
            return;
        }
        
        // Reset object state if it has a reset method
        if constexpr (requires { obj->reset(); }) {
            obj->reset();
        }
        
        pool_.push(std::move(obj));
    }

    /**
     * @brief Get pool statistics
     * @return Statistics about pool usage
     */
    struct Statistics {
        size_t available_objects;
        size_t total_created;
        size_t total_reused;
        double reuse_ratio;
    };

    Statistics get_statistics() const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        size_t created = total_created_.load();
        size_t reused = total_reused_.load();
        
        return Statistics{
            .available_objects = pool_.size(),
            .total_created = created,
            .total_reused = reused,
            .reuse_ratio = created > 0 ? static_cast<double>(reused) / created : 0.0
        };
    }

    /**
     * @brief Clear all objects from the pool
     */
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        while (!pool_.empty()) {
            pool_.pop();
        }
    }

    /**
     * @brief Get current pool size
     * @return Number of available objects in pool
     */
    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return pool_.size();
    }

private:
    mutable std::mutex mutex_;
    std::stack<std::unique_ptr<T>> pool_;
    size_t max_size_;
    std::function<std::unique_ptr<T>()> factory_;
    std::atomic<size_t> total_created_;
    std::atomic<size_t> total_reused_;
};

/**
 * @brief RAII wrapper for automatically returning objects to pool
 */
template<typename T>
class PooledObject {
public:
    /**
     * @brief Constructor
     * @param obj Object from pool
     * @param pool Pool to return object to
     */
    PooledObject(std::unique_ptr<T> obj, ObjectPool<T>* pool)
        : obj_(std::move(obj)), pool_(pool) {}

    /**
     * @brief Destructor - automatically returns object to pool
     */
    ~PooledObject() {
        if (obj_ && pool_) {
            pool_->release(std::move(obj_));
        }
    }

    // Non-copyable but movable
    PooledObject(const PooledObject&) = delete;
    PooledObject& operator=(const PooledObject&) = delete;
    
    PooledObject(PooledObject&& other) noexcept
        : obj_(std::move(other.obj_)), pool_(other.pool_) {
        other.pool_ = nullptr;
    }
    
    PooledObject& operator=(PooledObject&& other) noexcept {
        if (this != &other) {
            if (obj_ && pool_) {
                pool_->release(std::move(obj_));
            }
            obj_ = std::move(other.obj_);
            pool_ = other.pool_;
            other.pool_ = nullptr;
        }
        return *this;
    }

    /**
     * @brief Access the underlying object
     */
    T* operator->() { return obj_.get(); }
    const T* operator->() const { return obj_.get(); }
    
    T& operator*() { return *obj_; }
    const T& operator*() const { return *obj_; }
    
    T* get() { return obj_.get(); }
    const T* get() const { return obj_.get(); }

    /**
     * @brief Check if object is valid
     */
    explicit operator bool() const { return obj_ != nullptr; }

private:
    std::unique_ptr<T> obj_;
    ObjectPool<T>* pool_;
};

/**
 * @brief Memory buffer pool for string operations
 */
class StringBufferPool {
public:
    /**
     * @brief Constructor
     * @param initial_size Initial number of buffers
     * @param buffer_size Size of each buffer
     * @param max_buffers Maximum number of buffers to keep
     */
    explicit StringBufferPool(size_t initial_size = 5, 
                             size_t buffer_size = 8192,
                             size_t max_buffers = 50)
        : buffer_size_(buffer_size), max_buffers_(max_buffers) {
        
        for (size_t i = 0; i < initial_size; ++i) {
            buffers_.emplace(buffer_size, '\0');
        }
    }

    /**
     * @brief Acquire a string buffer
     * @return std::string Buffer ready for use
     */
    std::string acquire() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!buffers_.empty()) {
            auto buffer = std::move(buffers_.top());
            buffers_.pop();
            buffer.clear();
            return buffer;
        }
        
        // No buffers available, create new one
        std::string buffer;
        buffer.reserve(buffer_size_);
        return buffer;
    }

    /**
     * @brief Return a buffer to the pool
     * @param buffer Buffer to return
     */
    void release(std::string buffer) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (buffers_.size() >= max_buffers_) {
            // Pool is full
            return;
        }
        
        // Clear and return to pool
        buffer.clear();
        if (buffer.capacity() >= buffer_size_) {
            buffers_.push(std::move(buffer));
        }
    }

    /**
     * @brief Get number of available buffers
     */
    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return buffers_.size();
    }

private:
    mutable std::mutex mutex_;
    std::stack<std::string> buffers_;
    size_t buffer_size_;
    size_t max_buffers_;
};

/**
 * @brief Pool manager for coordinating multiple object pools
 */
class PoolManager {
public:
    /**
     * @brief Get singleton instance
     */
    static PoolManager& instance() {
        static PoolManager instance;
        return instance;
    }

    /**
     * @brief Get string buffer pool
     */
    StringBufferPool& get_string_buffer_pool() {
        return string_buffer_pool_;
    }

    /**
     * @brief Get statistics for all pools
     */
    struct AllStatistics {
        size_t string_buffers_available;
        std::chrono::steady_clock::time_point created_at;
    };

    AllStatistics get_all_statistics() const {
        return AllStatistics{
            .string_buffers_available = string_buffer_pool_.size(),
            .created_at = created_at_
        };
    }

private:
    PoolManager() : created_at_(std::chrono::steady_clock::now()) {}
    
    StringBufferPool string_buffer_pool_;
    std::chrono::steady_clock::time_point created_at_;
};

} // namespace omop::extract