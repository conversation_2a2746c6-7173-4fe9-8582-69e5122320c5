/**
 * @file extractor_base.h
 * @brief Abstract base class for data extractors in the OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains the abstract base class for all data extractors,
 * defining the interface for extracting data from various sources.
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <any>
#include <optional>
#include <chrono>

#include "core/interfaces.h"
#include "core/record.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include "common/configuration.h"
#include "common/validation.h"
#include "common/performance_monitor.h"

namespace omop::extract {

/**
 * @brief Extraction statistics
 */
struct ExtractionStats {
    size_t total_records{0};          ///< Total records processed
    size_t successful_records{0};     ///< Successfully processed records
    size_t failed_records{0};         ///< Failed records
    size_t skipped_records{0};        ///< Skipped records
    double extraction_time_seconds{0.0}; ///< Total extraction time
    std::unordered_map<std::string, size_t> error_counts; ///< Error type counts

    // Enhanced metrics
    size_t bytes_processed{0};        ///< Total bytes processed
    double records_per_second{0.0};   ///< Processing rate
    double bytes_per_second{0.0};     ///< Throughput
    size_t peak_memory_usage{0};      ///< Peak memory usage
    size_t total_memory_allocated{0}; ///< Total memory allocated
    std::chrono::system_clock::time_point start_time; ///< Start timestamp
    std::chrono::system_clock::time_point end_time;   ///< End timestamp

    // Performance metrics
    std::unordered_map<std::string, double> operation_timings; ///< Timing for operations
    std::unordered_map<std::string, size_t> operation_counts;  ///< Count of operations

    void calculate_rates();  ///< Calculate derived metrics
};

/**
 * @brief Extraction options
 */
struct ExtractionOptions {
    size_t batch_size{1000};          ///< Number of records per batch
    size_t max_records{0};            ///< Maximum records to extract (0 = no limit)
    size_t skip_records{0};           ///< Number of records to skip
    bool continue_on_error{true};     ///< Continue extraction on errors
    bool validate_schema{true};       ///< Validate source schema
    std::vector<std::string> columns; ///< Specific columns to extract (empty = all)
    std::string filter_expression;    ///< Filter expression (source-specific)
    std::unordered_map<std::string, std::any> custom_options; ///< Custom extractor options
};

/**
 * @brief Schema information for a data source
 */
struct SourceSchema {
    /**
     * @brief Column information
     */
    struct Column {
        std::string name;             ///< Column name
        std::string data_type;        ///< Data type
        bool nullable{true};          ///< Whether column is nullable
        std::optional<size_t> max_length; ///< Maximum length for string types
        std::optional<std::string> default_value; ///< Default value
        std::string description;      ///< Column description
    };

    std::string source_name;          ///< Source name/identifier
    std::string source_type;          ///< Source type (table, file, etc.)
    std::vector<Column> columns;      ///< Column definitions
    std::vector<std::string> primary_keys; ///< Primary key columns
    std::unordered_map<std::string, std::string> metadata; ///< Additional metadata
};

/**
 * @brief Standardised error handling policy
 */
class ExtractionErrorPolicy {
public:
    enum class ErrorAction {
        THROW_IMMEDIATELY,  ///< Throw exception on first error
        LOG_AND_CONTINUE,   ///< Log error and continue processing
        ACCUMULATE_ERRORS   ///< Accumulate errors for later reporting
    };

    /**
     * @brief Handle extraction error
     * @param error Error message
     * @param context Error context
     * @param logger Logger instance
     * @param action Error action to take
     * @return bool True to continue processing
     */
    static bool handle_error(const std::string& error,
                           const std::any& context,
                           std::shared_ptr<common::Logger> logger,
                           ErrorAction action = ErrorAction::LOG_AND_CONTINUE);

    static constexpr size_t MAX_ACCUMULATED_ERRORS = 1000;

private:
    // Thread-safe error accumulation container
    static thread_local std::vector<std::string> accumulated_errors_;
    static thread_local std::mutex accumulated_errors_mutex_;

public:
    /**
     * @brief Get accumulated errors for current thread
     * @return std::vector<std::string> List of accumulated errors
     */
    static std::vector<std::string> get_accumulated_errors();

    /**
     * @brief Clear accumulated errors for current thread
     */
    static void clear_accumulated_errors();
};

/**
 * @brief Abstract base class for data extractors
 *
 * This class defines the interface that all concrete extractors must implement.
 * It provides common functionality for batch processing, error handling, and
 * progress tracking.
 */
class ExtractorBase : public core::IExtractor {
public:
    /**
     * @brief Constructor
     * @param name Extractor name
     * @param config Configuration object
     * @param logger Logger instance
     */
    ExtractorBase(const std::string& name,
                  std::shared_ptr<common::ConfigurationManager> config,
                  std::shared_ptr<common::Logger> logger);

    /**
     * @brief Virtual destructor
     */
    virtual ~ExtractorBase() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return RecordBatch Extracted records (empty if no more data)
     */
    core::RecordBatch extract_batch(size_t batch_size, core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override;

    /**
     * @brief Finalize extraction and clean up resources
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Get the schema of the data source
     * @return Source schema
     * @note This method must be implemented by derived classes
     */
    virtual SourceSchema getSchema() const = 0;

    /**
     * @brief Validate the data source
     * @return Validation result
     * @note This method must be implemented by derived classes
     */
    virtual omop::common::ValidationResult validateSource() = 0;

    /**
     * @brief Get extraction statistics (internal)
     * @return Extraction statistics
     */
    ExtractionStats getStatistics() const { return stats_; }

    /**
     * @brief Reset the extractor to initial state
     */
     virtual void reset();

    /**
     * @brief Close the extractor and release resources
     */
    virtual void close();

    /**
     * @brief Set progress callback
     * @param callback Progress callback function
     */
    void setProgressCallback(std::function<void(size_t, size_t)> callback) {
        progress_callback_ = callback;
    }

    /**
     * @brief Get extractor name
     * @return Extractor name
     */
    const std::string& getName() const { return name_; }

protected:
    /**
     * @brief Reset implementation for derived classes
     */
    virtual void resetImpl() {}

    /**
     * @brief Connect to the data source
     * @return true if connection successful
     * @note This method must be implemented by derived classes
     */
    virtual bool connect() = 0;

    /**
     * @brief Disconnect from the data source
     * @note This method must be implemented by derived classes
     */
    virtual void disconnect() = 0;

    /**
     * @brief Extract a single batch of records (implementation)
     * @param batch_size Size of batch to extract
     * @return Vector of records
     * @note This method must be implemented by derived classes
     */
    virtual std::vector<core::Record> extractBatchImpl(size_t batch_size) = 0;

    /**
     * @brief Convert source data to Record format
     * @param source_data Source data in native format
     * @return Converted record
     * @note This method must be implemented by derived classes
     */
    virtual core::Record convertToRecord(const std::any& source_data) = 0;

    /**
     * @brief Handle extraction error
     * @param error Error message
     * @param record_context Record context (if applicable)
     */
    void handleError(const std::string& error,
                    const std::optional<std::any>& record_context = std::nullopt);

    /**
     * @brief Update progress
     * @param current Current record count
     * @param total Total record count (0 if unknown)
     */
    void updateProgress(size_t current, size_t total = 0);

    /**
     * @brief Apply filter to record
     * @param record Record to filter
     * @return true if record passes filter
     */
    virtual bool applyFilter(const core::Record& record);

    /**
     * @brief Apply column selection to record
     * @param record Record to process
     * @return Record with selected columns
     */
    virtual core::Record selectColumns(const core::Record& record);

protected:
    std::string name_;                              ///< Extractor name
    std::shared_ptr<common::ConfigurationManager> config_; ///< Configuration
    std::shared_ptr<common::Logger> logger_;        ///< Logger
    ExtractionStats stats_;                         ///< Extraction statistics
    ExtractionOptions options_;                     ///< Current extraction options
    bool is_connected_{false};                      ///< Connection status
    bool is_initialised_{false};                    ///< Initialization status
    bool was_ever_initialised_{false};              ///< Whether extractor was ever initialised
    bool has_more_data_{true};                      ///< Whether more data is available
    size_t current_position_{0};                    ///< Current position in data source
    std::function<void(size_t, size_t)> progress_callback_; ///< Progress callback
    ExtractionErrorPolicy::ErrorAction error_action_{ExtractionErrorPolicy::ErrorAction::LOG_AND_CONTINUE};

private:
    std::chrono::steady_clock::time_point start_time_; ///< Extraction start time
};

} // namespace omop::extract