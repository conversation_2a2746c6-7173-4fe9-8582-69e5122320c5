/**
 * @file compression_utils.cpp
 * @brief Implementation of compression utilities
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "compression_utils.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <filesystem>
#include <fstream>
#include <algorithm>
#include <cstdio>
#include <cstdlib>

// Platform-specific includes
#include <zlib.h>
#ifdef HAVE_BZLIB
#include <bzlib.h>
#endif
#ifdef HAVE_LZMA
#include <lzma.h>
#endif
#ifdef HAVE_LIBARCHIVE
#include <archive.h>
#include <archive_entry.h>
#endif

namespace omop::extract {

CompressionFormat CompressionUtils::detect_compression(const std::string& filepath) {
    auto extension = std::filesystem::path(filepath).extension().string();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
    
    if (extension == ".gz" || extension == ".gzip") {
        return CompressionFormat::Gzip;
    } else if (extension == ".zip") {
        return CompressionFormat::Zip;
    } else if (extension == ".bz2" || extension == ".bzip2") {
        return CompressionFormat::Bzip2;
    } else if (extension == ".xz" || extension == ".lzma") {
        return CompressionFormat::Xz;
    }
    
    return CompressionFormat::None;
}

bool CompressionUtils::is_compressed_file(const std::string& filepath) {
    return detect_compression(filepath) != CompressionFormat::None;
}

bool CompressionUtils::decompress_file(const std::string& src_path,
                                      const std::string& dst_path,
                                      CompressionFormat format) {
    auto logger = common::Logger::get("omop-compression-utils");
    logger->info("Decompressing {} -> {} (format: {})", 
                src_path, dst_path, format_to_string(format));
    
    try {
        switch (format) {
            case CompressionFormat::Gzip:
                return decompress_gzip(src_path, dst_path);
            case CompressionFormat::Zip:
                return decompress_zip(src_path, dst_path);
            case CompressionFormat::Bzip2:
                return decompress_bzip2(src_path, dst_path);
            case CompressionFormat::Xz:
                return decompress_xz(src_path, dst_path);
            case CompressionFormat::None:
                // Just copy the file
                std::filesystem::copy_file(src_path, dst_path);
                return true;
            default:
                logger->error("Unsupported compression format");
                return false;
        }
    } catch (const std::exception& e) {
        logger->error("Decompression failed: {}", e.what());
        return false;
    }
}

std::string CompressionUtils::format_to_string(CompressionFormat format) {
    switch (format) {
        case CompressionFormat::None: return "none";
        case CompressionFormat::Gzip: return "gzip";
        case CompressionFormat::Zip: return "zip";
        case CompressionFormat::Bzip2: return "bzip2";
        case CompressionFormat::Xz: return "xz";
        default: return "unknown";
    }
}

CompressionFormat CompressionUtils::string_to_format(const std::string& format_str) {
    std::string lower_format = format_str;
    std::transform(lower_format.begin(), lower_format.end(), lower_format.begin(), ::tolower);
    
    if (lower_format == "gzip" || lower_format == "gz") {
        return CompressionFormat::Gzip;
    } else if (lower_format == "zip") {
        return CompressionFormat::Zip;
    } else if (lower_format == "bzip2" || lower_format == "bz2") {
        return CompressionFormat::Bzip2;
    } else if (lower_format == "xz" || lower_format == "lzma") {
        return CompressionFormat::Xz;
    }
    
    return CompressionFormat::None;
}

std::string CompressionUtils::generate_temp_path(const std::string& original_path) {
    auto temp_dir = std::filesystem::temp_directory_path();
    auto filename = std::filesystem::path(original_path).stem(); // Remove compression extension
    auto temp_path = temp_dir / ("omop_extract_" + filename.string() + "_" + 
                                std::to_string(std::rand()));
    return temp_path.string();
}

bool CompressionUtils::decompress_gzip(const std::string& src, const std::string& dst) {
    auto logger = common::Logger::get("omop-compression-utils");
    
    gzFile src_file = gzopen(src.c_str(), "rb");
    if (!src_file) {
        logger->error("Failed to open gzip file: {}", src);
        return false;
    }
    
    std::ofstream dst_file(dst, std::ios::binary);
    if (!dst_file.is_open()) {
        logger->error("Failed to create output file: {}", dst);
        gzclose(src_file);
        return false;
    }
    
    constexpr size_t buffer_size = 8192;
    char buffer[buffer_size];
    int bytes_read;
    
    while ((bytes_read = gzread(src_file, buffer, buffer_size)) > 0) {
        dst_file.write(buffer, bytes_read);
        if (dst_file.fail()) {
            logger->error("Failed to write to output file: {}", dst);
            gzclose(src_file);
            return false;
        }
    }
    
    if (bytes_read < 0) {
        int err;
        const char* error_msg = gzerror(src_file, &err);
        logger->error("Error reading gzip file: {}", error_msg);
        gzclose(src_file);
        return false;
    }
    
    gzclose(src_file);
    dst_file.close();
    
    logger->info("Successfully decompressed gzip file");
    return true;
}

bool CompressionUtils::decompress_zip(const std::string& src, const std::string& dst) {
#ifdef HAVE_LIBARCHIVE
    auto logger = common::Logger::get("omop-compression-utils");
    
    struct archive* a = archive_read_new();
    archive_read_support_filter_all(a);
    archive_read_support_format_all(a);
    
    int r = archive_read_open_filename(a, src.c_str(), 10240);
    if (r != ARCHIVE_OK) {
        logger->error("Failed to open zip file: {}", archive_error_string(a));
        archive_read_free(a);
        return false;
    }
    
    struct archive_entry* entry;
    r = archive_read_next_header(a, &entry);
    if (r != ARCHIVE_OK) {
        logger->error("Failed to read zip entry: {}", archive_error_string(a));
        archive_read_free(a);
        return false;
    }
    
    // Extract first file entry to destination
    std::ofstream dst_file(dst, std::ios::binary);
    if (!dst_file.is_open()) {
        logger->error("Failed to create output file: {}", dst);
        archive_read_free(a);
        return false;
    }
    
    constexpr size_t buffer_size = 8192;
    char buffer[buffer_size];
    la_ssize_t size;
    
    while ((size = archive_read_data(a, buffer, buffer_size)) > 0) {
        dst_file.write(buffer, size);
        if (dst_file.fail()) {
            logger->error("Failed to write to output file: {}", dst);
            archive_read_free(a);
            return false;
        }
    }
    
    if (size < 0) {
        logger->error("Error reading zip data: {}", archive_error_string(a));
        archive_read_free(a);
        return false;
    }
    
    dst_file.close();
    archive_read_free(a);
    
    logger->info("Successfully decompressed zip file");
    return true;
#else
    auto logger = common::Logger::get("omop-compression-utils");
    logger->error("Zip support not available (libarchive not compiled)");
    return false;
#endif
}

bool CompressionUtils::decompress_bzip2(const std::string& src, const std::string& dst) {
#ifdef HAVE_BZLIB
    auto logger = common::Logger::get("omop-compression-utils");
    
    FILE* src_file = fopen(src.c_str(), "rb");
    if (!src_file) {
        logger->error("Failed to open bzip2 file: {}", src);
        return false;
    }
    
    BZFILE* bz_file = BZ2_bzReadOpen(nullptr, src_file, 0, 0, nullptr, 0);
    if (!bz_file) {
        logger->error("Failed to open bzip2 stream");
        fclose(src_file);
        return false;
    }
    
    std::ofstream dst_file(dst, std::ios::binary);
    if (!dst_file.is_open()) {
        logger->error("Failed to create output file: {}", dst);
        BZ2_bzReadClose(nullptr, bz_file);
        fclose(src_file);
        return false;
    }
    
    constexpr size_t buffer_size = 8192;
    char buffer[buffer_size];
    int bytes_read;
    
    while ((bytes_read = BZ2_bzRead(nullptr, bz_file, buffer, buffer_size)) > 0) {
        dst_file.write(buffer, bytes_read);
        if (dst_file.fail()) {
            logger->error("Failed to write to output file: {}", dst);
            BZ2_bzReadClose(nullptr, bz_file);
            fclose(src_file);
            return false;
        }
    }
    
    if (bytes_read < 0) {
        logger->error("Error reading bzip2 file");
        BZ2_bzReadClose(nullptr, bz_file);
        fclose(src_file);
        return false;
    }
    
    BZ2_bzReadClose(nullptr, bz_file);
    fclose(src_file);
    dst_file.close();
    
    logger->info("Successfully decompressed bzip2 file");
    return true;
#else
    auto logger = common::Logger::get("omop-compression-utils");
    logger->error("Bzip2 support not available (bzlib not compiled)");
    return false;
#endif
}

bool CompressionUtils::decompress_xz(const std::string& src, const std::string& dst) {
#ifdef HAVE_LZMA
    auto logger = common::Logger::get("omop-compression-utils");
    
    FILE* src_file = fopen(src.c_str(), "rb");
    if (!src_file) {
        logger->error("Failed to open xz file: {}", src);
        return false;
    }
    
    std::ofstream dst_file(dst, std::ios::binary);
    if (!dst_file.is_open()) {
        logger->error("Failed to create output file: {}", dst);
        fclose(src_file);
        return false;
    }
    
    lzma_stream strm = LZMA_STREAM_INIT;
    lzma_ret ret = lzma_stream_decoder(&strm, UINT64_MAX, LZMA_CONCATENATED);
    
    if (ret != LZMA_OK) {
        logger->error("Failed to initialize LZMA decoder");
        fclose(src_file);
        return false;
    }
    
    constexpr size_t buffer_size = 8192;
    uint8_t inbuf[buffer_size];
    uint8_t outbuf[buffer_size];
    
    lzma_action action = LZMA_RUN;
    strm.next_in = nullptr;
    strm.avail_in = 0;
    strm.next_out = outbuf;
    strm.avail_out = buffer_size;
    
    while (true) {
        if (strm.avail_in == 0 && !feof(src_file)) {
            strm.next_in = inbuf;
            strm.avail_in = fread(inbuf, 1, buffer_size, src_file);
            
            if (ferror(src_file)) {
                logger->error("Error reading xz file");
                lzma_end(&strm);
                fclose(src_file);
                return false;
            }
            
            if (feof(src_file)) {
                action = LZMA_FINISH;
            }
        }
        
        ret = lzma_code(&strm, action);
        
        if (strm.avail_out == 0 || ret == LZMA_STREAM_END) {
            size_t write_size = buffer_size - strm.avail_out;
            dst_file.write(reinterpret_cast<char*>(outbuf), write_size);
            
            if (dst_file.fail()) {
                logger->error("Failed to write to output file: {}", dst);
                lzma_end(&strm);
                fclose(src_file);
                return false;
            }
            
            strm.next_out = outbuf;
            strm.avail_out = buffer_size;
        }
        
        if (ret != LZMA_OK) {
            if (ret == LZMA_STREAM_END) {
                break;
            }
            logger->error("LZMA decoding error: {}", static_cast<int>(ret));
            lzma_end(&strm);
            fclose(src_file);
            return false;
        }
    }
    
    lzma_end(&strm);
    fclose(src_file);
    dst_file.close();
    
    logger->info("Successfully decompressed xz file");
    return true;
#else
    auto logger = common::Logger::get("omop-compression-utils");
    logger->error("XZ support not available (liblzma not compiled)");
    return false;
#endif
}

} // namespace omop::extract