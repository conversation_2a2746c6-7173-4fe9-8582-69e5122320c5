#include "streaming_csv_extractor.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <algorithm>
#include <filesystem>
#include <fstream>

#ifdef __unix__
#include <sys/mman.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/sysinfo.h>
#elif defined(__APPLE__)
#include <sys/mman.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/sysctl.h>
#elif defined(_WIN32)
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#include <sys/stat.h>
#endif

namespace omop::extract {

StreamingCsvExtractor::StreamingCsvExtractor() 
    : CsvExtractor()
    , max_prefetch_batches_(3) {
}

StreamingCsvExtractor::~StreamingCsvExtractor() {
    stop_threads_ = true;
    reader_cv_.notify_all();
    parser_cv_.notify_all();
    
    if (reader_thread_ && reader_thread_->joinable()) {
        reader_thread_->join();
    }
    if (parser_thread_ && parser_thread_->joinable()) {
        parser_thread_->join();
    }
    
    if (mapped_memory_ != nullptr) {
#ifdef __unix__
        munmap(mapped_memory_, mapped_size_);
#elif defined(_WIN32)
        UnmapViewOfFile(mapped_memory_);
#endif
        mapped_memory_ = nullptr;
    }
}

void StreamingCsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                       core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-streaming-csv-extractor");
    
    // Parse streaming-specific options
    if (auto it = config.find("read_buffer_size"); it != config.end()) {
        streaming_options_.read_buffer_size = std::any_cast<size_t>(it->second);
    }
    if (auto it = config.find("parse_buffer_size"); it != config.end()) {
        streaming_options_.parse_buffer_size = std::any_cast<size_t>(it->second);
    }
    if (auto it = config.find("max_line_size"); it != config.end()) {
        streaming_options_.max_line_size = std::any_cast<size_t>(it->second);
    }
    if (auto it = config.find("prefetch_batches"); it != config.end()) {
        streaming_options_.prefetch_batches = std::any_cast<size_t>(it->second);
        max_prefetch_batches_ = streaming_options_.prefetch_batches;
    }
    if (auto it = config.find("async_parsing"); it != config.end()) {
        streaming_options_.async_parsing = std::any_cast<bool>(it->second);
    }
    if (auto it = config.find("memory_mapped"); it != config.end()) {
        streaming_options_.memory_mapped = std::any_cast<bool>(it->second);
    }
    if (auto it = config.find("memory_limit_ratio"); it != config.end()) {
        streaming_options_.memory_limit_ratio = std::any_cast<double>(it->second);
    }
    
    // Initialize base CSV extractor
    CsvExtractor::initialize(config, context);
    
    // Initialize streaming optimizations
    initialize_streaming_buffers();
    
    logger->info("Streaming CSV extractor initialized with read_buffer={}MB, parse_buffer={}MB, async={}", 
                 streaming_options_.read_buffer_size / (1024*1024),
                 streaming_options_.parse_buffer_size / (1024*1024),
                 streaming_options_.async_parsing);
}

void StreamingCsvExtractor::initialize_streaming_buffers() {
    auto logger = common::Logger::get("omop-streaming-csv-extractor");
    
    try {
        // Get file size for adaptive buffer sizing
        size_t file_size = std::filesystem::file_size(filepath_);
        
        // Adapt buffer sizes based on file size and available memory
        adapt_buffer_sizes(file_size);
        
        // Try memory mapping for very large files
        if (streaming_options_.memory_mapped && file_size > 100 * 1024 * 1024) { // 100MB threshold
            if (setup_memory_mapping()) {
                logger->info("Using memory mapping for large file: {} MB", file_size / (1024*1024));
                return;
            }
        }
        
        // Initialize streaming buffers
        read_buffer_ = std::make_unique<std::vector<char>>(streaming_options_.read_buffer_size);
        parse_buffer_ = std::make_unique<std::vector<char>>(streaming_options_.parse_buffer_size);
        
        logger->info("Initialized streaming buffers: read={}MB, parse={}MB", 
                     streaming_options_.read_buffer_size / (1024*1024),
                     streaming_options_.parse_buffer_size / (1024*1024));
                     
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-streaming-csv-extractor");
        logger->warn("Failed to initialize streaming optimizations: {}, falling back to standard mode", e.what());
        
        // Fallback to smaller buffers
        streaming_options_.read_buffer_size = 1024 * 1024;  // 1MB
        streaming_options_.parse_buffer_size = 512 * 1024;  // 512KB
        streaming_options_.async_parsing = false;
        streaming_options_.memory_mapped = false;
        
        read_buffer_ = std::make_unique<std::vector<char>>(streaming_options_.read_buffer_size);
        parse_buffer_ = std::make_unique<std::vector<char>>(streaming_options_.parse_buffer_size);
    }
}

bool StreamingCsvExtractor::setup_memory_mapping() {
#if defined(__unix__) || defined(__APPLE__)
    auto logger = common::Logger::get("omop-streaming-csv-extractor");
    
    int fd = open(filepath_.c_str(), O_RDONLY);
    if (fd == -1) {
        logger->warn("Failed to open file for memory mapping: {}", filepath_);
        return false;
    }
    
    struct stat file_stat;
    if (fstat(fd, &file_stat) == -1) {
        ::close(fd);
        logger->warn("Failed to get file stats for memory mapping: {}", filepath_);
        return false;
    }
    
    mapped_size_ = file_stat.st_size;
    mapped_memory_ = mmap(nullptr, mapped_size_, PROT_READ, MAP_PRIVATE, fd, 0);
    ::close(fd);
    
    if (mapped_memory_ == MAP_FAILED) {
        mapped_memory_ = nullptr;
        logger->warn("Memory mapping failed for file: {}", filepath_);
        return false;
    }
    
    // Advise kernel about access pattern
    if (madvise(mapped_memory_, mapped_size_, MADV_SEQUENTIAL) != 0) {
        logger->warn("Failed to set memory access advice");
    }
    
    logger->info("Successfully memory mapped file: {} MB", mapped_size_ / (1024*1024));
    return true;
    
#elif defined(_WIN32)
    // Windows memory mapping implementation
    auto logger = common::Logger::get("omop-streaming-csv-extractor");
    
    HANDLE file_handle = CreateFileA(filepath_.c_str(), GENERIC_READ, FILE_SHARE_READ,
                                    nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
    if (file_handle == INVALID_HANDLE_VALUE) {
        logger->warn("Failed to open file for memory mapping: {}", filepath_);
        return false;
    }
    
    LARGE_INTEGER file_size;
    if (!GetFileSizeEx(file_handle, &file_size)) {
        CloseHandle(file_handle);
        logger->warn("Failed to get file size for memory mapping: {}", filepath_);
        return false;
    }
    
    HANDLE mapping_handle = CreateFileMappingA(file_handle, nullptr, PAGE_READONLY,
                                              file_size.HighPart, file_size.LowPart, nullptr);
    CloseHandle(file_handle);
    
    if (mapping_handle == nullptr) {
        logger->warn("Failed to create file mapping: {}", filepath_);
        return false;
    }
    
    mapped_memory_ = MapViewOfFile(mapping_handle, FILE_MAP_READ, 0, 0, 0);
    CloseHandle(mapping_handle);
    
    if (mapped_memory_ == nullptr) {
        logger->warn("Failed to map view of file: {}", filepath_);
        return false;
    }
    
    mapped_size_ = file_size.QuadPart;
    logger->info("Successfully memory mapped file: {} MB", mapped_size_ / (1024*1024));
    return true;
    
#else
    return false;
#endif
}

size_t StreamingCsvExtractor::get_available_memory() const {
#if defined(__linux__)
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        return info.freeram * info.mem_unit;
    }
#elif defined(__APPLE__)
    int mib[2] = {CTL_HW, HW_MEMSIZE};
    uint64_t physical_memory = 0;
    size_t length = sizeof(physical_memory);
    if (sysctl(mib, 2, &physical_memory, &length, nullptr, 0) == 0) {
        return static_cast<size_t>(physical_memory * 0.8); // Assume 80% available
    }
#elif defined(_WIN32)
    MEMORYSTATUSEX status;
    status.dwLength = sizeof(status);
    if (GlobalMemoryStatusEx(&status)) {
        return static_cast<size_t>(status.ullAvailPhys);
    }
#endif
    
    // Fallback: assume 1GB available
    return 1024 * 1024 * 1024;
}

void StreamingCsvExtractor::adapt_buffer_sizes(size_t file_size) {
    // Ensure minimum buffer sizes
    streaming_options_.read_buffer_size = std::min(streaming_options_.read_buffer_size, static_cast<size_t>(1024 * 1024)); // 1MB
    streaming_options_.parse_buffer_size = std::min(streaming_options_.parse_buffer_size, static_cast<size_t>(512 * 1024)); // 512KB
    
    // Calculate available memory for buffers (use 10% of system memory)
    size_t max_memory_for_buffers = get_available_memory() * streaming_options_.memory_limit_ratio;
    
    if (max_memory_for_buffers > 0) {
        streaming_options_.read_buffer_size = std::min(static_cast<size_t>(32 * 1024 * 1024), max_memory_for_buffers / 2); // Up to 32MB
        streaming_options_.parse_buffer_size = std::min(static_cast<size_t>(16 * 1024 * 1024), max_memory_for_buffers / 4); // Up to 16MB
    }
    
    auto logger = common::Logger::get("omop-streaming-csv-extractor");
    logger->debug("Adapting buffer sizes: file_size={}MB, available_memory={}MB, max_buffer_memory={}MB",
                  file_size / (1024*1024), get_available_memory() / (1024*1024), max_memory_for_buffers / (1024*1024));
    
    // For small files, use smaller buffers
    if (file_size < 10 * 1024 * 1024) { // < 10MB
        streaming_options_.read_buffer_size = std::min(streaming_options_.read_buffer_size, static_cast<size_t>(1024 * 1024)); // 1MB
        streaming_options_.parse_buffer_size = std::min(streaming_options_.parse_buffer_size, static_cast<size_t>(512 * 1024)); // 512KB
    }
    // For very large files, use larger buffers if memory allows
    else if (file_size > 1024 * 1024 * 1024) { // > 1GB
        if (max_memory_for_buffers > 50 * 1024 * 1024) { // If we have > 50MB for buffers
            streaming_options_.read_buffer_size = std::min(static_cast<size_t>(32 * 1024 * 1024), max_memory_for_buffers / 2); // Up to 32MB
            streaming_options_.parse_buffer_size = std::min(static_cast<size_t>(16 * 1024 * 1024), max_memory_for_buffers / 4); // Up to 16MB
        }
    }
    
    // Ensure buffers don't exceed memory limits
    size_t total_buffer_size = streaming_options_.read_buffer_size + streaming_options_.parse_buffer_size;
    if (total_buffer_size > max_memory_for_buffers) {
        double scale_factor = static_cast<double>(max_memory_for_buffers) / total_buffer_size;
        streaming_options_.read_buffer_size = static_cast<size_t>(streaming_options_.read_buffer_size * scale_factor);
        streaming_options_.parse_buffer_size = static_cast<size_t>(streaming_options_.parse_buffer_size * scale_factor);
        
        logger->info("Scaled buffer sizes due to memory constraints: read={}MB, parse={}MB",
                     streaming_options_.read_buffer_size / (1024*1024),
                     streaming_options_.parse_buffer_size / (1024*1024));
    }
}

bool StreamingCsvExtractor::connect() {
    if (mapped_memory_ != nullptr) {
        // For memory-mapped files, we're already "connected"
        return true;
    }
    
    // Use base class connection for file streams
    return CsvExtractor::connect();
}

void StreamingCsvExtractor::disconnect() {
    stop_threads_ = true;
    reader_cv_.notify_all();
    parser_cv_.notify_all();
    
    if (reader_thread_ && reader_thread_->joinable()) {
        reader_thread_->join();
        reader_thread_.reset();
    }
    if (parser_thread_ && parser_thread_->joinable()) {
        parser_thread_->join();
        parser_thread_.reset();
    }
    
    CsvExtractor::disconnect();
}

core::RecordBatch StreamingCsvExtractor::extract_batch(size_t batch_size,
                                                        core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-streaming-csv-extractor");
    
    // For memory-mapped files, use specialized extraction
    if (mapped_memory_ != nullptr) {
        return extract_memory_mapped_batch(batch_size, context);
    }
    
    // For async parsing, check prefetch queue first
    if (streaming_options_.async_parsing && !prefetch_queue_.empty()) {
        std::lock_guard<std::mutex> lock(batch_queue_mutex_);
        if (!prefetch_queue_.empty()) {
            auto batch = std::move(prefetch_queue_.front());
            prefetch_queue_.pop();
            consumer_cv_.notify_one(); // Signal that space is available
            
            logger->debug("Returned prefetched batch with {} records", batch.size());
            return batch;
        }
    }
    
    // Fall back to synchronous streaming extraction
    return extract_streaming_batch(batch_size, context);
}

core::RecordBatch StreamingCsvExtractor::extract_memory_mapped_batch(size_t batch_size,
                                                                      core::ProcessingContext& context) {
    core::RecordBatch batch(batch_size);
    batch.reserve(batch_size);
    
    auto logger = common::Logger::get("omop-streaming-csv-extractor");
    const char* data = static_cast<const char*>(mapped_memory_);
    size_t count = 0;
    
    while (mapped_offset_ < mapped_size_ && count < batch_size) {
        // Find end of current line
        size_t line_end = mapped_offset_;
        while (line_end < mapped_size_ && data[line_end] != '\n') {
            line_end++;
        }
        
        if (line_end >= mapped_size_) {
            // Last line without newline
            has_more_data_ = false;
            if (mapped_offset_ == line_end) break; // Empty line at end
        }
        
        // Extract line
        std::string line(data + mapped_offset_, line_end - mapped_offset_);
        mapped_offset_ = line_end + 1; // Skip newline
        current_line_++;
        
        // Skip empty lines if configured
        if (options_.skip_empty_lines && line.empty()) {
            continue;
        }
        
        try {
            // Parse line into fields
            auto fields = parser_->parse_line(line);
            
            // Create record from fields
            auto record = create_record(fields);
            batch.addRecord(std::move(record));
            count++;
            
            stats_.successful_records++;
            
        } catch (const std::exception& e) {
            stats_.failed_records++;
            logger->warn("Error parsing line {}: {}", current_line_, e.what());
            
            if (stats_.failed_records > 100) { // Error threshold
                throw common::ExtractionException(
                    "Too many parsing errors, aborting extraction", "streaming_csv");
            }
        }
    }
    
    // Update has_more_data_
    if (mapped_offset_ >= mapped_size_) {
        has_more_data_ = false;
    }
    
    logger->debug("Memory-mapped batch extraction: {} records", count);
    return batch;
}

core::RecordBatch StreamingCsvExtractor::extract_streaming_batch(size_t batch_size,
                                                                  core::ProcessingContext& context) {
    core::RecordBatch batch(batch_size);
    batch.reserve(batch_size);
    
    auto logger = common::Logger::get("omop-streaming-csv-extractor");
    size_t count = 0;
    
    // Use larger read buffer for better I/O performance
    std::vector<char> buffer(streaming_options_.read_buffer_size);
    std::string accumulated_line;
    accumulated_line.reserve(streaming_options_.max_line_size);
    
    while (file_stream_.good() && count < batch_size && has_more_data_) {
        // Read chunk into buffer
        file_stream_.read(buffer.data(), buffer.size());
        std::streamsize bytes_read = file_stream_.gcount();
        
        if (bytes_read == 0) {
            has_more_data_ = false;
            break;
        }
        
        bytes_read_ += bytes_read;
        buffer_fills_++;
        
        // Process buffer and extract complete lines
        size_t buffer_pos = 0;
        while (buffer_pos < static_cast<size_t>(bytes_read) && count < batch_size) {
            // Find next newline
            size_t line_end = buffer_pos;
            while (line_end < static_cast<size_t>(bytes_read) && buffer[line_end] != '\n') {
                line_end++;
            }
            
            // Add chunk to accumulated line
            accumulated_line.append(buffer.data() + buffer_pos, line_end - buffer_pos);
            
            if (line_end < static_cast<size_t>(bytes_read)) {
                // Found complete line
                current_line_++;
                
                try {
                    if (!options_.skip_empty_lines || !accumulated_line.empty()) {
                        auto fields = parser_->parse_line(accumulated_line);
                        auto record = create_record(fields);
                        batch.addRecord(std::move(record));
                        count++;
                        stats_.successful_records++;
                    }
                } catch (const std::exception& e) {
                    stats_.failed_records++;
                    logger->warn("Error parsing line {}: {}", current_line_, e.what());
                }
                
                accumulated_line.clear();
                buffer_pos = line_end + 1; // Skip newline
            } else {
                // Incomplete line, will continue in next read
                buffer_pos = bytes_read;
            }
        }
    }
    
    // Handle final accumulated line if file ended
    if (!file_stream_.good() && !accumulated_line.empty()) {
        try {
            auto fields = parser_->parse_line(accumulated_line);
            auto record = create_record(fields);
            batch.addRecord(std::move(record));
            count++;
            stats_.successful_records++;
        } catch (const std::exception& e) {
            stats_.failed_records++;
            logger->warn("Error parsing final line: {}", e.what());
        }
        has_more_data_ = false;
    }
    
    logger->debug("Streaming batch extraction: {} records, {} bytes read, {} buffer fills",
                  count, bytes_read_.load(), buffer_fills_.load());
    
    return batch;
}

bool StreamingCsvExtractor::has_more_data() const {
    if (mapped_memory_ != nullptr) {
        return mapped_offset_ < mapped_size_;
    }
    return CsvExtractor::has_more_data();
}

std::vector<core::Record> StreamingCsvExtractor::extractBatchImpl(size_t batch_size) {
    // This method is called by the base class, redirect to streaming implementation
    core::ProcessingContext dummy_context;
    auto batch = extract_batch(batch_size, dummy_context);
    
    std::vector<core::Record> records;
    records.reserve(batch.size());
    
    for (size_t i = 0; i < batch.size(); ++i) {
        records.push_back(std::move(batch.getRecord(i)));
    }
    
    return records;
}

void StreamingCsvExtractor::finalize(core::ProcessingContext& context) {
    disconnect();
    CsvExtractor::finalize(context);
}

std::unordered_map<std::string, std::any> StreamingCsvExtractor::get_statistics() const {
    auto stats = CsvExtractor::get_statistics();
    
    // Add streaming-specific statistics
    stats["streaming_enabled"] = true;
    stats["memory_mapped"] = (mapped_memory_ != nullptr);
    stats["async_parsing"] = streaming_options_.async_parsing;
    stats["read_buffer_size"] = streaming_options_.read_buffer_size;
    stats["parse_buffer_size"] = streaming_options_.parse_buffer_size;
    stats["bytes_read"] = bytes_read_.load();
    stats["lines_parsed"] = lines_parsed_.load();
    stats["buffer_fills"] = buffer_fills_.load();
    
    if (buffer_fills_ > 0) {
        stats["avg_bytes_per_buffer"] = bytes_read_.load() / buffer_fills_.load();
    }
    
    return stats;
}

// MemoryMappedCsvReader implementation

MemoryMappedCsvReader::MemoryMappedCsvReader(const std::string& filepath) 
    : filepath_(filepath) {
}

MemoryMappedCsvReader::~MemoryMappedCsvReader() {
    unmap_file();
}

bool MemoryMappedCsvReader::map_file() {
#if defined(__unix__) || defined(__APPLE__)
    file_descriptor_ = open(filepath_.c_str(), O_RDONLY);
    if (file_descriptor_ == -1) {
        return false;
    }
    
    struct stat file_stat;
    if (fstat(file_descriptor_, &file_stat) == -1) {
        ::close(file_descriptor_);
        file_descriptor_ = -1;
        return false;
    }
    
    file_size_ = file_stat.st_size;
    mapped_data_ = mmap(nullptr, file_size_, PROT_READ, MAP_PRIVATE, file_descriptor_, 0);
    
    if (mapped_data_ == MAP_FAILED) {
        mapped_data_ = nullptr;
        ::close(file_descriptor_);
        file_descriptor_ = -1;
        return false;
    }
    
    // Advise sequential access
    madvise(mapped_data_, file_size_, MADV_SEQUENTIAL);
    return true;
    
#else
    return false;
#endif
}

void MemoryMappedCsvReader::unmap_file() {
#if defined(__unix__) || defined(__APPLE__)
    if (mapped_data_ != nullptr) {
        munmap(mapped_data_, file_size_);
        mapped_data_ = nullptr;
    }
    if (file_descriptor_ != -1) {
        ::close(file_descriptor_);
        file_descriptor_ = -1;
    }
#endif
    file_size_ = 0;
}

} // namespace omop::extract