/**
 * @file csv_field_parser.cpp
 * @brief Implementation of CSV field parsing functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "csv_field_parser.h"
#include "common/logging.h"
#include <algorithm>
#include <cctype>
#include <iomanip>
#include <sstream>
#include <optional>

namespace omop::extract {

std::string CsvFieldParser::parse_field(const std::string& input, size_t& pos) {
    std::string field;
    bool in_quotes = false;
    bool was_quoted = false;  // Track if field was originally quoted
    bool escape_next = false;

    // Check if we have leading whitespace before potential quote
    [[maybe_unused]] size_t leading_spaces = 0;
    size_t check_pos = pos;
    while (check_pos < input.length() && std::isspace(input[check_pos]) && input[check_pos] != '\n') {
        leading_spaces++;
        check_pos++;
    }

    // Check if field starts with quote (after any leading whitespace)
    if (check_pos < input.length() && input[check_pos] == options_.quote_char) {
        // This is a quoted field - skip leading whitespace and start after quote
        pos = check_pos + 1;
        in_quotes = true;
        was_quoted = true;
    }
    // If not quoted, start from original position (including any leading whitespace)

    while (pos < input.length()) {
        char c = input[pos];

        if (escape_next) {
            // Handle escape sequences
            switch (c) {
                case 't': field += '\t'; break;
                case 'n': field += '\n'; break;
                case 'r': field += '\r'; break;
                case '\\': field += '\\'; break;
                case '"': field += '"'; break;
                default: field += c; break;
            }
            escape_next = false;
            ++pos;
            continue;
        }

        if (c == options_.escape_char) {
            escape_next = true;
            ++pos;
            continue;
        }

        if (in_quotes) {
            if (c == options_.quote_char) {
                // Check for escaped quote (double quote)
                if (pos + 1 < input.length() && input[pos + 1] == options_.quote_char) {
                    field += c;
                    pos += 2;
                    continue;
                }
                in_quotes = false;
                ++pos;
                // Skip to delimiter or end of line
                while (pos < input.length() &&
                       input[pos] != options_.delimiter &&
                       input[pos] != '\n') {
                    ++pos;
                }
                break;
            }
            field += c;
            ++pos;
        } else {
            if (c == options_.delimiter || c == '\n') {
                break;
            }
            field += c;
            ++pos;
        }
    }

    // Skip delimiter if present
    if (pos < input.length() && input[pos] == options_.delimiter) {
        ++pos;
    }
    
    // Skip newline if present (to handle lines ending with \n)
    if (pos < input.length() && input[pos] == '\n') {
        ++pos;
    }

    // Validate UTF-8 encoding if specified
    if (options_.encoding == "UTF-8" && !field.empty() && !is_valid_utf8(field)) {
        auto logger = common::Logger::get("omop-csv-parser");
        logger->warn("Invalid UTF-8 sequence detected in field, attempting to sanitize");

        // Basic sanitization - replace invalid bytes with ?
        for (size_t i = 0; i < field.length(); ++i) {
            if (static_cast<unsigned char>(field[i]) > 0x7F && !is_valid_utf8(field.substr(i, 1))) {
                field[i] = '?';
            }
        }
    }

    // Only trim unquoted fields if trim_fields is enabled
    // Use was_quoted to preserve whitespace in originally quoted fields
    if (was_quoted) {
        // Preserve whitespace in quoted fields
        return field;
    } else if (options_.trim_fields) {
        return trim(field);
    }
    
    return field;
}

std::vector<std::string> CsvFieldParser::parse_line(const std::string& line) {
    std::vector<std::string> fields;
    size_t pos = 0;

    while (pos < line.length()) {
        std::string field = parse_field(line, pos);
        fields.push_back(field);
        
        // If we've reached the end of the line, break
        if (pos >= line.length()) {
            break;
        }
    }

    return fields;
}

std::any CsvFieldParser::convert_field(const std::string& field, const std::string& type_hint) {
    // Handle null values - check for empty, null string, and case-insensitive "null"
    if (field.empty() || field == options_.null_string) {
        return std::any{};
    }
    
    // Check for case-insensitive "null"
    std::string lower_field = field;
    std::transform(lower_field.begin(), lower_field.end(), lower_field.begin(), ::tolower);
    if (lower_field == "null") {
        return std::any{};
    }

    // If no type hint, try to infer type
    if (type_hint.empty()) {
        // Try integer first (prioritize numbers over booleans)
        try {
            size_t pos;
            long long int_val = std::stoll(field, &pos);
            if (pos == field.length()) {
                return int_val;
            }
        } catch (const std::exception&) {
            // Fall through to try double
        }

        // Try double
        try {
            size_t pos;
            double double_val = std::stod(field, &pos);
            if (pos == field.length()) {
                return double_val;
            }
        } catch (const std::exception&) {
            // Fall through to try boolean
        }

        // Try boolean
        std::string lower_trimmed = field;
        std::transform(lower_trimmed.begin(), lower_trimmed.end(), 
                      lower_trimmed.begin(), ::tolower);
        lower_trimmed = trim(lower_trimmed);
        
        if (lower_trimmed == "true" || lower_trimmed == "1" || 
            lower_trimmed == "yes" || lower_trimmed == "on" ||
            lower_trimmed == options_.true_string) {
            return true;
        }
        if (lower_trimmed == "false" || lower_trimmed == "0" || 
            lower_trimmed == "no" || lower_trimmed == "off" ||
            lower_trimmed == options_.false_string) {
            return false;
        }

        // Try date/datetime parsing
        try {
            auto time_point = parse_datetime(field, options_.datetime_format);
            return time_point;
        } catch (const std::exception&) {
            try {
                auto time_point = parse_datetime(field, options_.date_format);
                return time_point;
            } catch (const std::exception&) {
                // Fall through to string
            }
        }

        // Default to string
        return field;
    }

    // Handle specific type hints
    if (type_hint == "integer" || type_hint == "int") {
        try {
            return static_cast<long long>(std::stoll(field));
        } catch (const std::exception&) {
            return std::any{};
        }
    }

    if (type_hint == "double" || type_hint == "float" || type_hint == "real") {
        try {
            return std::stod(field);
        } catch (const std::exception&) {
            return std::any{};
        }
    }

    if (type_hint == "boolean" || type_hint == "bool") {
        std::string lower_field = field;
        std::transform(lower_field.begin(), lower_field.end(), 
                      lower_field.begin(), ::tolower);
        lower_field = trim(lower_field);
        
        if (lower_field == "true" || lower_field == "1" || 
            lower_field == "yes" || lower_field == "on" ||
            lower_field == options_.true_string) {
            return true;
        }
        if (lower_field == "false" || lower_field == "0" || 
            lower_field == "no" || lower_field == "off" ||
            lower_field == options_.false_string) {
            return false;
        }
        return std::any{};
    }

    if (type_hint == "date") {
        try {
            return parse_datetime(field, options_.date_format);
        } catch (const std::exception&) {
            return std::any{};
        }
    }

    if (type_hint == "datetime" || type_hint == "timestamp") {
        try {
            return parse_datetime(field, options_.datetime_format);
        } catch (const std::exception&) {
            return std::any{};
        }
    }

    // Default to string for unknown types
    return field;
}

std::chrono::system_clock::time_point CsvFieldParser::parse_datetime(
    const std::string& value, const std::string& format) const {
    
    std::tm tm = {};
    std::istringstream ss(value);
    ss >> std::get_time(&tm, format.c_str());
    
    if (ss.fail()) {
        throw std::runtime_error("Failed to parse datetime: " + value);
    }
    
    return std::chrono::system_clock::from_time_t(std::mktime(&tm));
}

} // namespace omop::extract