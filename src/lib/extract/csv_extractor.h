#pragma once

#include "core/interfaces.h"
#include "extractor_base.h"
#include "csv_field_parser.h"
#include "compression_utils.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <string>
#include <optional>
#include <vector>
#include <unordered_map>
#include <any>
#include <chrono>
#include "common/validation.h"

namespace omop::extract {

// Forward declarations from csv_field_parser.h
struct CsvOptions;
class CsvFieldParser;

/**
 * @brief CSV file extractor
 *
 * Extends ExtractorBase for CSV file sources,
 * providing efficient streaming extraction from CSV files with
 * comprehensive error handling, statistics, and progress tracking.
 */
class CsvExtractor : public ExtractorBase {
public:
    /**
     * @brief Constructor
     */
    CsvExtractor();

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                    core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                    core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "csv"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Check if a file is a compressed CSV file by extension
     * @param filepath File path
     * @return bool True if file is compressed
     */
    static bool is_compressed_file(const std::string& filepath);

    /**
     * @brief Get the schema of the CSV data source
     * @return SourceSchema CSV schema information
     */
    virtual SourceSchema getSchema() const override;

    /**
     * @brief Validate the source CSV file
     * @return omop::common::ValidationResult Validation result
     */
    virtual omop::common::ValidationResult validateSource() override;

protected:
    // ExtractorBase pure virtual method implementations
    
    /**
     * @brief Connect to the CSV data source
     * @return bool True if connection successful
     */
    bool connect() override;
    
    /**
     * @brief Disconnect from the CSV data source
     */
    void disconnect() override;
    
    /**
     * @brief Extract a single batch of records (implementation)
     * @param batch_size Size of batch to extract
     * @return std::vector<core::Record> Vector of records
     */
    std::vector<core::Record> extractBatchImpl(size_t batch_size) override;
    
    /**
     * @brief Convert source data to Record format
     * @param source_data Source data (vector<string> of CSV fields)
     * @return core::Record Converted record
     */
    core::Record convertToRecord(const std::any& source_data) override;

    // CSV-specific methods
    
    /**
     * @brief Open CSV file
     * @param filepath File path
     */
    void open_file(const std::string& filepath);

    /**
     * @brief Read and parse header
     */
    void read_header();

    /**
     * @brief Infer column types from data
     * @param sample_size Number of rows to sample
     */
    void infer_column_types(size_t sample_size = 100);

    /**
     * @brief Create record from parsed fields
     * @param fields Field values
     * @return core::Record Created record
     */
    core::Record create_record(const std::vector<std::string>& fields);

    /**
     * @brief Read a complete CSV record (handling multi-line quoted fields)
     * @param line Output line containing complete record
     * @return bool True if record was read successfully
     */
    bool read_complete_record(std::string& line);

protected:
    // CSV-specific members (protected so derived classes can access)
    std::ifstream file_stream_;                    ///< CSV file stream
    std::string filepath_;                         ///< Path to CSV file
    CsvOptions options_;                           ///< CSV parsing options
    std::unique_ptr<CsvFieldParser> parser_;       ///< CSV field parser
    std::vector<std::string> column_names_;        ///< Column names from header
    std::vector<std::string> column_types_;        ///< Inferred column types
    size_t current_line_{0};                       ///< Current line number in file
    size_t total_lines_{0};                        ///< Total lines in file (if known)
    
    // Note: The following are now handled by ExtractorBase:
    // - extracted_count_ -> use stats_.successful_records
    // - error_count_ -> use stats_.failed_records  
    // - max_records_ -> use options_.max_records
    // - has_more_ -> use has_more_data_
    // - start_time_ -> use stats_.start_time
};


/**
 * @brief CSV extractor factory
 */
class CsvExtractorFactory {
public:
    /**
     * @brief Create CSV extractor
     * @param type Extractor type (csv, multi_csv, csv_directory, compressed_csv)
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Register CSV extractors with the main factory
     */
    static void register_extractors();
};


} // namespace omop::extract