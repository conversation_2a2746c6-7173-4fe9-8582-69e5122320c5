#pragma once

#include "core/interfaces.h"
#include "extractor_factory.h"
#include <string>
#include <memory>
#include <unordered_map>
#include <vector>
#include <functional>
#include <filesystem>
#include <mutex>

namespace omop::extract {

/**
 * @brief Plugin metadata information
 */
struct PluginMetadata {
    std::string name;
    std::string version;
    std::string description;
    std::string author;
    std::string license;
    std::vector<std::string> supported_formats;
    std::vector<std::string> dependencies;
    std::string api_version;
    bool is_stable{true};
    std::unordered_map<std::string, std::string> custom_properties;
};

/**
 * @brief Plugin API version for compatibility checking
 */
struct PluginApiVersion {
    int major{1};
    int minor{0};
    int patch{0};
    
    bool is_compatible(const PluginApiVersion& other) const {
        return major == other.major && minor >= other.minor;
    }
    
    std::string to_string() const {
        return std::to_string(major) + "." + std::to_string(minor) + "." + std::to_string(patch);
    }
};

/**
 * @brief Plugin interface that all dynamic plugins must implement
 */
class IExtractorPlugin {
public:
    virtual ~IExtractorPlugin() = default;
    
    /**
     * @brief Get plugin metadata
     * @return PluginMetadata Plugin information
     */
    virtual PluginMetadata get_metadata() const = 0;
    
    /**
     * @brief Get plugin API version
     * @return PluginApiVersion Supported API version
     */
    virtual PluginApiVersion get_api_version() const = 0;
    
    /**
     * @brief Initialize the plugin
     * @return bool True if initialization was successful
     */
    virtual bool initialize() = 0;
    
    /**
     * @brief Shutdown the plugin
     */
    virtual void shutdown() = 0;
    
    /**
     * @brief Create an extractor instance
     * @param type Extractor type
     * @param config Configuration parameters
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     */
    virtual std::unique_ptr<core::IExtractor> create_extractor(
        const std::string& type,
        const std::unordered_map<std::string, std::any>& config) = 0;
    
    /**
     * @brief Get list of supported extractor types
     * @return std::vector<std::string> Supported types
     */
    virtual std::vector<std::string> get_supported_types() const = 0;
    
    /**
     * @brief Validate configuration for extractor type
     * @param type Extractor type
     * @param config Configuration to validate
     * @return bool True if configuration is valid
     */
    virtual bool validate_config(const std::string& type,
                               const std::unordered_map<std::string, std::any>& config) const = 0;
};

/**
 * @brief Plugin loading status
 */
enum class PluginStatus {
    NOT_LOADED,
    LOADING,
    LOADED,
    FAILED,
    INCOMPATIBLE,
    UNLOADING
};

/**
 * @brief Information about a loaded plugin
 */
struct LoadedPlugin {
    std::string path;
    PluginMetadata metadata;
    PluginApiVersion api_version;
    PluginStatus status;
    std::string error_message;
    void* library_handle{nullptr};
    std::unique_ptr<IExtractorPlugin> plugin_instance;
    std::chrono::system_clock::time_point load_time;
    size_t reference_count{0};
};

/**
 * @brief Plugin manager for dynamic loading and management of extractor plugins
 */
class PluginManager {
public:
    /**
     * @brief Constructor
     * @param plugin_directory Directory to search for plugins
     */
    explicit PluginManager(const std::string& plugin_directory = "plugins");
    
    /**
     * @brief Destructor
     */
    ~PluginManager();
    
    /**
     * @brief Scan for plugins in the plugin directory
     * @return size_t Number of plugins found
     */
    size_t scan_for_plugins();
    
    /**
     * @brief Load a specific plugin
     * @param plugin_path Path to the plugin library
     * @return bool True if plugin was loaded successfully
     */
    bool load_plugin(const std::string& plugin_path);
    
    /**
     * @brief Unload a plugin
     * @param plugin_name Name of the plugin to unload
     * @return bool True if plugin was unloaded successfully
     */
    bool unload_plugin(const std::string& plugin_name);
    
    /**
     * @brief Reload a plugin (unload and load again)
     * @param plugin_name Name of the plugin to reload
     * @return bool True if plugin was reloaded successfully
     */
    bool reload_plugin(const std::string& plugin_name);
    
    /**
     * @brief Get list of all loaded plugins
     * @return std::vector<std::string> Plugin names
     */
    std::vector<std::string> get_loaded_plugins() const;
    
    /**
     * @brief Get plugin metadata
     * @param plugin_name Plugin name
     * @return PluginMetadata Plugin metadata (empty if not found)
     */
    PluginMetadata get_plugin_metadata(const std::string& plugin_name) const;
    
    /**
     * @brief Get plugin status
     * @param plugin_name Plugin name
     * @return PluginStatus Current status
     */
    PluginStatus get_plugin_status(const std::string& plugin_name) const;
    
    /**
     * @brief Check if a plugin supports a specific extractor type
     * @param extractor_type Type to check
     * @return std::string Plugin name that supports the type (empty if none)
     */
    std::string find_plugin_for_type(const std::string& extractor_type) const;
    
    /**
     * @brief Create an extractor using plugins
     * @param type Extractor type
     * @param config Configuration parameters
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     */
    std::unique_ptr<core::IExtractor> create_extractor(
        const std::string& type,
        const std::unordered_map<std::string, std::any>& config);
    
    /**
     * @brief Register plugin manager with extractor factory
     */
    void register_with_factory();
    
    /**
     * @brief Set plugin directory
     * @param directory New plugin directory
     */
    void set_plugin_directory(const std::string& directory);
    
    /**
     * @brief Enable or disable plugin hot reloading
     * @param enable True to enable hot reloading
     */
    void enable_hot_reloading(bool enable);
    
    /**
     * @brief Check for plugin file changes and reload if necessary
     */
    void check_for_changes();
    
    /**
     * @brief Get detailed plugin information
     * @return std::vector<LoadedPlugin> Plugin information
     */
    std::vector<LoadedPlugin> get_plugin_details() const;

private:
    std::string plugin_directory_;
    mutable std::mutex plugins_mutex_;
    std::unordered_map<std::string, std::unique_ptr<LoadedPlugin>> loaded_plugins_;
    PluginApiVersion current_api_version_;
    bool hot_reloading_enabled_{false};
    std::unordered_map<std::string, std::filesystem::file_time_type> plugin_file_times_;
    
    /**
     * @brief Load plugin from library file
     * @param library_path Path to shared library
     * @return std::unique_ptr<LoadedPlugin> Loaded plugin info
     */
    std::unique_ptr<LoadedPlugin> load_plugin_library(const std::string& library_path);
    
    /**
     * @brief Unload plugin library
     * @param plugin Loaded plugin to unload
     */
    void unload_plugin_library(LoadedPlugin& plugin);
    
    /**
     * @brief Check API compatibility
     * @param plugin_api Plugin API version
     * @return bool True if compatible
     */
    bool is_api_compatible(const PluginApiVersion& plugin_api) const;
    
    /**
     * @brief Get platform-specific library extension
     * @return std::string Library extension (.so, .dll, .dylib)
     */
    std::string get_library_extension() const;
    
    /**
     * @brief Get plugin entry point symbol name
     * @return std::string Entry point symbol
     */
    std::string get_entry_point_symbol() const;
    
    /**
     * @brief Validate plugin before loading
     * @param plugin_path Path to plugin file
     * @return bool True if plugin is valid
     */
    bool validate_plugin_file(const std::string& plugin_path) const;
    
    /**
     * @brief Update plugin file time tracking
     * @param plugin_path Path to plugin file
     */
    void update_file_time(const std::string& plugin_path);
    
    /**
     * @brief Check if plugin file has been modified
     * @param plugin_path Path to plugin file
     * @return bool True if file was modified
     */
    bool is_file_modified(const std::string& plugin_path) const;
};

/**
 * @brief Plugin factory for creating plugin instances
 */
class PluginFactory {
public:
    /**
     * @brief Get singleton instance
     * @return PluginFactory& Factory instance
     */
    static PluginFactory& instance();
    
    /**
     * @brief Create plugin manager
     * @param plugin_directory Plugin directory
     * @return std::unique_ptr<PluginManager> Plugin manager instance
     */
    std::unique_ptr<PluginManager> create_plugin_manager(
        const std::string& plugin_directory = "plugins");
    
    /**
     * @brief Register global plugin manager
     * @param manager Plugin manager to register
     */
    void register_plugin_manager(std::shared_ptr<PluginManager> manager);
    
    /**
     * @brief Get global plugin manager
     * @return std::shared_ptr<PluginManager> Global plugin manager
     */
    std::shared_ptr<PluginManager> get_plugin_manager();

private:
    PluginFactory() = default;
    std::shared_ptr<PluginManager> global_plugin_manager_;
    mutable std::mutex factory_mutex_;
};

/**
 * @brief Base class for implementing extractor plugins
 */
class ExtractorPluginBase : public IExtractorPlugin {
public:
    /**
     * @brief Constructor
     * @param metadata Plugin metadata
     */
    explicit ExtractorPluginBase(const PluginMetadata& metadata);
    
    /**
     * @brief Get plugin metadata
     * @return PluginMetadata Plugin information
     */
    PluginMetadata get_metadata() const override;
    
    /**
     * @brief Get plugin API version
     * @return PluginApiVersion Supported API version
     */
    PluginApiVersion get_api_version() const override;
    
    /**
     * @brief Initialize the plugin (default implementation)
     * @return bool True if initialization was successful
     */
    bool initialize() override;
    
    /**
     * @brief Shutdown the plugin (default implementation)
     */
    void shutdown() override;

protected:
    PluginMetadata metadata_;
    PluginApiVersion api_version_;
    bool initialized_{false};
};

/**
 * @brief Macro to define plugin entry point
 */
#define OMOP_PLUGIN_ENTRY_POINT(PluginClass) \
    extern "C" { \
        omop::extract::IExtractorPlugin* create_plugin() { \
            return new PluginClass(); \
        } \
        void destroy_plugin(omop::extract::IExtractorPlugin* plugin) { \
            delete plugin; \
        } \
        const char* get_plugin_api_version() { \
            static omop::extract::PluginApiVersion version; \
            static std::string version_str = version.to_string(); \
            return version_str.c_str(); \
        } \
    }

} // namespace omop::extract