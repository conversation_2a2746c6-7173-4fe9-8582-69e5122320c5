/**
 * @file csv_extractor.cpp
 * @brief Refactored implementation of CSV data extractor
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "csv_extractor.h"
#include "csv_field_parser.h"
#include "compression_utils.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include "extractor_factory.h"
#include <algorithm>
#include <fstream>
#include <filesystem>

namespace omop::extract {

// CsvExtractor implementation

CsvExtractor::CsvExtractor() 
    : ExtractorBase("csv", nullptr, common::Logger::get("omop-csv-extractor")) {
    // Initialize parser will be done in initialize() method
}

void CsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                             core::ProcessingContext& context) {
    ExtractorBase::initialize(config, context);
    
    auto logger = common::Logger::get("omop-csv-extractor");
    logger->info("Initializing CSV extractor");

    // Extract configuration
    try {
        if (auto it = config.find("filepath"); it != config.end()) {
            filepath_ = std::any_cast<std::string>(it->second);
        } else {
            throw common::ConfigurationException("CSV extractor requires 'filepath' parameter");
        }

        // Configure CSV options
        if (auto it = config.find("delimiter"); it != config.end()) {
            auto delim_str = std::any_cast<std::string>(it->second);
            if (!delim_str.empty()) {
                options_.delimiter = delim_str[0];
            }
        }

        if (auto it = config.find("quote_char"); it != config.end()) {
            auto quote_str = std::any_cast<std::string>(it->second);
            if (!quote_str.empty()) {
                options_.quote_char = quote_str[0];
            }
        }

        if (auto it = config.find("has_header"); it != config.end()) {
            options_.has_header = std::any_cast<bool>(it->second);
        }

        if (auto it = config.find("encoding"); it != config.end()) {
            options_.encoding = std::any_cast<std::string>(it->second);
        }

        if (auto it = config.find("skip_empty_lines"); it != config.end()) {
            options_.skip_empty_lines = std::any_cast<bool>(it->second);
        }

        if (auto it = config.find("trim_fields"); it != config.end()) {
            options_.trim_fields = std::any_cast<bool>(it->second);
        }

        // Initialize parser with configured options
        parser_ = std::make_unique<CsvFieldParser>(options_);

        logger->info("CSV extractor configured: file={}, delimiter='{}', has_header={}", 
                    filepath_, options_.delimiter, options_.has_header);

    } catch (const std::bad_any_cast& e) {
        throw common::ConfigurationException("Invalid configuration parameter type: " + std::string(e.what()));
    }
}

bool CsvExtractor::connect() {
    auto logger = common::Logger::get("omop-csv-extractor");
    
    try {
        // Check if file exists
        if (!std::filesystem::exists(filepath_)) {
            logger->error("CSV file does not exist: {}", filepath_);
            return false;
        }

        // Open file
        file_stream_.open(filepath_, std::ios::in);
        if (!file_stream_.is_open()) {
            logger->error("Failed to open CSV file: {}", filepath_);
            return false;
        }

        // Read header if present
        if (options_.has_header) {
            read_header();
        }

        // Infer column types if not explicitly provided
        if (options_.column_types.empty()) {
            infer_column_types();
        }

        logger->info("Successfully connected to CSV file: {}", filepath_);
        return true;

    } catch (const std::exception& e) {
        logger->error("Failed to connect to CSV file {}: {}", filepath_, e.what());
        return false;
    }
}

void CsvExtractor::disconnect() {
    if (file_stream_.is_open()) {
        file_stream_.close();
    }
}

void CsvExtractor::read_header() {
    if (!file_stream_.is_open()) {
        throw common::ExtractionException("File stream is not open");
    }

    std::string header_line;
    if (std::getline(file_stream_, header_line)) {
        column_names_ = parser_->parse_line(header_line);
        current_line_++;
        
        auto logger = common::Logger::get("omop-csv-extractor");
        logger->info("Read CSV header with {} columns", column_names_.size());
    } else {
        throw common::ExtractionException("Failed to read CSV header");
    }
}

void CsvExtractor::infer_column_types(size_t sample_size) {
    auto logger = common::Logger::get("omop-csv-extractor");
    logger->info("Inferring column types from {} sample rows", sample_size);

    // Store current position
    auto current_pos = file_stream_.tellg();
    size_t saved_line = current_line_;

    std::vector<std::vector<std::string>> samples;
    std::string line;
    
    // Collect sample data
    size_t samples_collected = 0;
    while (samples_collected < sample_size && std::getline(file_stream_, line)) {
        if (options_.skip_empty_lines && line.empty()) {
            continue;
        }
        
        auto fields = parser_->parse_line(line);
        samples.push_back(fields);
        samples_collected++;
    }

    // Reset file position
    file_stream_.clear();
    file_stream_.seekg(current_pos);
    current_line_ = saved_line;

    // Infer types for each column
    if (!samples.empty()) {
        size_t num_columns = samples[0].size();
        column_types_.resize(num_columns, "string");

        for (size_t col = 0; col < num_columns; ++col) {
            bool all_integers = true;
            bool all_doubles = true;
            bool all_booleans = true;

            for (const auto& sample : samples) {
                if (col >= sample.size()) continue;
                
                const std::string& field = sample[col];
                if (field.empty()) continue;

                // Test integer
                try {
                    std::stoll(field);
                } catch (...) {
                    all_integers = false;
                }

                // Test double
                try {
                    std::stod(field);
                } catch (...) {
                    all_doubles = false;
                }

                // Test boolean
                std::string lower_field = field;
                std::transform(lower_field.begin(), lower_field.end(), 
                              lower_field.begin(), ::tolower);
                if (lower_field != "true" && lower_field != "false" && 
                    lower_field != "1" && lower_field != "0") {
                    all_booleans = false;
                }
            }

            // Assign type based on inference
            if (all_integers) {
                column_types_[col] = "integer";
            } else if (all_doubles) {
                column_types_[col] = "double";
            } else if (all_booleans) {
                column_types_[col] = "boolean";
            } else {
                column_types_[col] = "string";
            }
        }
    }

    logger->info("Column type inference completed");
}

std::vector<core::Record> CsvExtractor::extractBatchImpl(size_t batch_size) {
    std::vector<core::Record> records;
    records.reserve(batch_size);

    std::string line;
    size_t extracted = 0;

    while (extracted < batch_size && std::getline(file_stream_, line)) {
        current_line_++;

        // Skip empty lines if configured
        if (options_.skip_empty_lines && line.empty()) {
            continue;
        }

        try {
            // Parse line into fields
            auto fields = parser_->parse_line(line);
            
            // Create record
            core::Record record;
            
            // Set source reference in metadata
            auto& metadata = record.getMetadataMutable();
            metadata.source_table = filepath_;
            metadata.source_row_number = current_line_;

            // Convert fields to typed values
            for (size_t i = 0; i < fields.size() && i < column_names_.size(); ++i) {
                std::string type_hint = i < column_types_.size() ? column_types_[i] : "";
                auto value = parser_->convert_field(fields[i], type_hint);
                record.setField(column_names_[i], value);
            }

            records.push_back(std::move(record));
            extracted++;

        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-csv-extractor");
            logger->warn("Error parsing line {}: {}", current_line_, e.what());
            // Continue with next line
        }
    }

    return records;
}

core::Record CsvExtractor::convertToRecord(const std::any& source_data) {
    // This method is called by ExtractorBase, but we handle conversion in extractBatchImpl
    // Return empty record as this shouldn't be called in our implementation
    return core::Record{};
}

bool CsvExtractor::has_more_data() const {
    return file_stream_.is_open() && !file_stream_.eof();
}

core::RecordBatch CsvExtractor::extract_batch(size_t batch_size,
                                              core::ProcessingContext& context) {
    return ExtractorBase::extract_batch(batch_size, context);
}

void CsvExtractor::finalize(core::ProcessingContext& context) {
    ExtractorBase::finalize(context);
    disconnect();
}

std::unordered_map<std::string, std::any> CsvExtractor::get_statistics() const {
    auto base_stats = ExtractorBase::get_statistics();
    
    // Add CSV-specific statistics
    base_stats["filepath"] = filepath_;
    base_stats["current_line"] = current_line_;
    base_stats["total_lines"] = total_lines_;
    base_stats["column_count"] = column_names_.size();
    base_stats["delimiter"] = std::string(1, options_.delimiter);
    base_stats["has_header"] = options_.has_header;
    
    return base_stats;
}

bool CsvExtractor::is_compressed_file(const std::string& filepath) {
    return CompressionUtils::is_compressed_file(filepath);
}

SourceSchema CsvExtractor::getSchema() const {
    SourceSchema schema;
    schema.source_type = "csv";
    schema.source_name = filepath_;
    
    for (size_t i = 0; i < column_names_.size(); ++i) {
        SourceSchema::Column column;
        column.name = column_names_[i];
        column.data_type = i < column_types_.size() ? column_types_[i] : "string";
        column.description = "Column " + std::to_string(i);
        schema.columns.push_back(column);
    }
    
    return schema;
}

omop::common::ValidationResult CsvExtractor::validateSource() {
    omop::common::ValidationResult result;
    
    try {
        // Check file exists
        if (!std::filesystem::exists(filepath_)) {
            result.add_error("filepath", "File does not exist: " + filepath_, "file_existence", "error");
            return result;
        }

        // Check file size
        auto file_size = std::filesystem::file_size(filepath_);
        if (file_size == 0) {
            result.add_warning("filepath", "File is empty: " + filepath_, "file_size", "warning");
        } else {
            result.add_info("filepath", "File size: " + std::to_string(file_size) + " bytes", "file_size", "info");
        }

        // Try to open and read header
        std::ifstream test_stream(filepath_);
        if (!test_stream.is_open()) {
            result.add_error("filepath", "Cannot open file: " + filepath_, "file_access", "error");
            return result;
        }

        std::string first_line;
        if (std::getline(test_stream, first_line)) {
            auto fields = parser_->parse_line(first_line);
            result.add_info("columns", "Detected " + std::to_string(fields.size()) + " columns", "header_parsing", "info");
        }

        result.add_info("validation", "CSV file validation completed successfully", "validation_complete", "info");

    } catch (const std::exception& e) {
        result.add_error("validation", "Validation failed: " + std::string(e.what()), "exception_handling", "error");
    }

    return result;
}

// CsvExtractorFactory implementation

std::unique_ptr<core::IExtractor> CsvExtractorFactory::create(const std::string& type) {
    if (type == "csv") {
        return std::make_unique<CsvExtractor>();
    }
    return nullptr;
}

void CsvExtractorFactory::register_extractors() {
    ExtractorFactoryRegistry::register_type("csv",
        []() { return std::make_unique<CsvExtractor>(); });
}

} // namespace omop::extract