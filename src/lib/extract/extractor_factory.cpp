/**
 * @file extractor_factory_impl.cpp
 * @brief Implementation of extractor factory and registration
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "extractor_factory.h"
#include "extract.h"
#include "csv_extractor.h"
#include "streaming_csv_extractor.h"
#include "pooled_csv_extractor.h"
#include "csv_multi_file_extractors.h"
#include "json_extractor.h"
#include "database_connector.h"
#ifdef OMOP_HAS_POSTGRESQL
#include "postgresql_connector.h"
#endif
#ifdef OMOP_HAS_MYSQL
#include "mysql_connector.h"
#endif
#ifdef OMOP_HAS_ODBC
#include "odbc_connector.h"
#endif
#include "common/logging.h"
#include "common/exceptions.h"
#include "common/configuration.h"
#include "core/interfaces.h"
#include <algorithm>
#include <mutex>
#include <atomic>
#include <unordered_map>
#include <string>
#include <vector>
#include <functional>
#include <memory>
#include <any>
#include <format>

namespace omop::extract {

namespace {
    // Use an atomic flag instead of once_flag so we can reset it
    static std::atomic<bool> init_flag{false};
    static std::mutex init_mutex;
}

// Static member definitions for ExtractorFactoryRegistry
std::unordered_map<std::string,
    std::function<std::unique_ptr<core::IExtractor>()>> ExtractorFactoryRegistry::creators_;
std::mutex ExtractorFactoryRegistry::mutex_;

// Custom creator for plugin support
static std::function<std::unique_ptr<core::IExtractor>(const std::string&, 
                                                      const std::unordered_map<std::string, std::any>&)> custom_creator_;
static std::mutex custom_creator_mutex_;

/**
 * @brief Main extractor factory implementation
 */
class ExtractorFactoryImpl {
public:
    /**
     * @brief Register all built-in extractors
     */
    static void register_all_extractors() {
        auto logger = common::Logger::get("omop-extractor-factory");
        logger->info("Registering built-in extractors");

        // Register CSV extractors
        register_csv_extractors();

        // Register JSON extractors
        register_json_extractors();

        // Register database extractors
        register_database_extractors();

        logger->info("Registered {} extractor types",
                    ExtractorFactoryRegistry::get_registered_types().size());
    }

private:
    /**
     * @brief Register CSV extractor types
     */
    static void register_csv_extractors() {
        ExtractorFactoryRegistry::register_type("csv",
            []() { return std::make_unique<CsvExtractor>(); });

        ExtractorFactoryRegistry::register_type("streaming_csv",
            []() { return std::make_unique<StreamingCsvExtractor>(); });

        ExtractorFactoryRegistry::register_type("pooled_csv",
            []() { return std::make_unique<PooledCsvExtractor>(); });

        ExtractorFactoryRegistry::register_type("multi_csv",
            []() { return std::make_unique<MultiFileCsvExtractor>(); });

        ExtractorFactoryRegistry::register_type("csv_directory",
            []() { return std::make_unique<CsvDirectoryExtractor>(); });

        ExtractorFactoryRegistry::register_type("compressed_csv",
            []() { return std::make_unique<CompressedCsvExtractor>(); });
    }

    /**
     * @brief Register JSON extractor types
     */
    static void register_json_extractors() {
        ExtractorFactoryRegistry::register_type("json",
            []() { return std::make_unique<JsonExtractor>(); });

        ExtractorFactoryRegistry::register_type("jsonl",
            []() { return std::make_unique<JsonLinesExtractor>("jsonl"); });

        ExtractorFactoryRegistry::register_type("json_lines",
            []() { return std::make_unique<JsonLinesExtractor>("json_lines"); });

        ExtractorFactoryRegistry::register_type("streaming_json",
            []() { return std::make_unique<StreamingJsonExtractor>(); });
    }

    /**
     * @brief Register database extractor types
     */
    static void register_database_extractors() {
#ifdef OMOP_HAS_POSTGRESQL
        // Register PostgreSQL
        ExtractorFactoryRegistry::register_type("postgresql",
            []() {
                auto conn = std::make_unique<PostgreSQLConnection>();
                return std::make_unique<PostgreSQLExtractor>(std::move(conn), "postgresql");
            });

        ExtractorFactoryRegistry::register_type("postgres",
            []() {
                auto conn = std::make_unique<PostgreSQLConnection>();
                return std::make_unique<PostgreSQLExtractor>(std::move(conn), "postgres");
            });
#endif

#ifdef OMOP_HAS_MYSQL
        // Register MySQL
        ExtractorFactoryRegistry::register_type("mysql",
            []() {
                auto conn = std::make_unique<MySQLConnection>();
                return std::make_unique<MySQLExtractor>(std::move(conn));
            });

        ExtractorFactoryRegistry::register_type("mariadb",
            []() {
                auto conn = std::make_unique<MySQLConnection>();
                return std::make_unique<MySQLExtractor>(std::move(conn));
            });
#endif

#ifdef OMOP_HAS_ODBC
        // Register ODBC
        ExtractorFactoryRegistry::register_type("odbc",
            []() {
                auto conn = std::make_unique<OdbcDatabaseConnection>();
                return std::make_unique<OdbcExtractor>(std::move(conn));
            });
#endif

#ifdef OMOP_HAS_POSTGRESQL
        // Register generic database extractor (defaults to PostgreSQL)
        ExtractorFactoryRegistry::register_type("database",
            []() {
                // Default to PostgreSQL for generic database
                auto conn = std::make_unique<PostgreSQLConnection>();
                return std::make_unique<DatabaseExtractor>(std::move(conn));
            });
#endif
    }
};

// ExtractorFactoryRegistry implementation

void ExtractorFactoryRegistry::register_type(const std::string& type,
                                           std::function<std::unique_ptr<core::IExtractor>()> creator) {
    std::lock_guard<std::mutex> lock(mutex_);
    creators_[type] = std::move(creator);
}

std::unique_ptr<core::IExtractor> ExtractorFactoryRegistry::create(const std::string& type) {
    std::lock_guard<std::mutex> lock(mutex_);

    auto it = creators_.find(type);
    if (it == creators_.end()) {
        throw common::ConfigurationException(
            "Unknown extractor type: '" + type + "'");
    }

    return it->second();
}

std::vector<std::string> ExtractorFactoryRegistry::get_registered_types() {
    std::lock_guard<std::mutex> lock(mutex_);

    std::vector<std::string> types;
    types.reserve(creators_.size());

    for (const auto& [type, _] : creators_) {
        types.push_back(type);
    }

    std::sort(types.begin(), types.end());
    return types;
}

bool ExtractorFactoryRegistry::is_type_registered(const std::string& type) {
    std::lock_guard<std::mutex> lock(mutex_);
    return creators_.find(type) != creators_.end();
}

void ExtractorFactoryRegistry::clear() {
    std::lock_guard<std::mutex> lock(mutex_);
    creators_.clear();
}

void ExtractorFactoryRegistry::register_custom_creator(
    std::function<std::unique_ptr<core::IExtractor>(const std::string&, 
                                                   const std::unordered_map<std::string, std::any>&)> creator) {
    std::lock_guard<std::mutex> lock(custom_creator_mutex_);
    custom_creator_ = std::move(creator);
}

void reset_initialize_extractors_flag() {
    // Reset the initialization flag
    init_flag.store(false);
}

void initialize_extractors() {
    // Use double-checked locking pattern for thread safety
    if (!init_flag.load()) {
        std::lock_guard<std::mutex> lock(init_mutex);
        if (!init_flag.load()) {
            ExtractorFactoryImpl::register_all_extractors();
            init_flag.store(true);
        }
    }
}

// Extractor creation helper
std::unique_ptr<core::IExtractor> create_extractor(const std::string& type,
                                                  const std::unordered_map<std::string, std::any>& config) {
    // Ensure extractors are registered
    initialize_extractors();

    std::unique_ptr<core::IExtractor> extractor;

    // Try custom creator first (for plugins)
    {
        std::lock_guard<std::mutex> lock(custom_creator_mutex_);
        if (custom_creator_) {
            try {
                extractor = custom_creator_(type, config);
                if (extractor) {
                    return extractor;
                }
            } catch (...) {
                // Fall through to standard factory
            }
        }
    }

    // Create extractor using standard factory
    extractor = ExtractorFactoryRegistry::create(type);

    // Initialize with configuration if provided
    if (!config.empty()) {
        core::ProcessingContext context;
        extractor->initialize(config, context);
    }

    return extractor;
}

std::vector<ExtractorTypeInfo> get_extractor_info() {
    return {
        {
            "csv",
            "Extracts data from a single CSV file",
            {"filepath"},
            {"delimiter", "quote_char", "has_header", "column_names", "column_types", "encoding"},
            R"({"filepath": "data.csv", "delimiter": ",", "has_header": true})"
        },
        {
            "pooled_csv",
            "Memory-optimized CSV extractor using object pooling for high-throughput scenarios",
            {"filepath"},
            {"delimiter", "quote_char", "has_header", "record_pool_size", "field_pool_size", "max_pool_size"},
            R"({"filepath": "data.csv", "delimiter": ",", "has_header": true, "record_pool_size": 50})"
        },
        {
            "multi_csv",
            "Extracts data from multiple CSV files",
            {"files"},
            {"skip_headers_after_first"},
            R"({"files": ["data1.csv", "data2.csv"], "skip_headers_after_first": true})"
        },
        {
            "csv_directory",
            "Extracts data from all CSV files in a directory",
            {"directory"},
            {"pattern", "recursive"},
            R"({"directory": "/data", "pattern": ".*\\.csv$", "recursive": true})"
        },
        {
            "json",
            "Extracts data from a JSON file",
            {"filepath"},
            {"root_path", "flatten_nested", "array_delimiter", "parse_dates"},
            R"({"filepath": "data.json", "root_path": "data.records", "flatten_nested": true})"
        },
        {
            "jsonl",
            "Extracts data from a JSON Lines file",
            {"filepath"},
            {"flatten_nested", "parse_dates"},
            R"({"filepath": "data.jsonl", "flatten_nested": true})"
        },
        {
            "streaming_json",
            "Extracts data from large JSON files using streaming",
            {"filepath"},
            {"root_path", "flatten_nested"},
            R"({"filepath": "large_data.json", "root_path": "records"})"
        },
        {
            "postgresql",
            "Extracts data from PostgreSQL database",
            {"host", "port", "database", "username", "password", "table"},
            {"schema", "columns", "filter", "order_by"},
            R"({"host": "localhost", "port": 5432, "database": "omop", "username": "user", "password": "pass", "table": "person"})"
        },
#ifdef OMOP_HAS_MYSQL
        {
            "mysql",
            "Extracts data from MySQL database",
            {"host", "port", "database", "username", "password", "table"},
            {"schema", "columns", "filter", "order_by"},
            R"({"host": "localhost", "port": 3306, "database": "omop", "username": "user", "password": "pass", "table": "person"})"
        },
#endif
#ifdef OMOP_HAS_ODBC
        {
            "odbc",
            "Extracts data from any ODBC-compliant database",
            {"dsn", "table"},
            {"username", "password", "schema", "columns", "filter", "order_by"},
            R"({"dsn": "MyDataSource", "username": "user", "password": "pass", "table": "person"})"
        }
#endif
    };
}

void print_extractor_info(std::ostream& stream) {
    auto info_list = get_extractor_info();
    
    stream << "Available Extractor Types:\n";
    stream << "========================\n\n";
    
    for (const auto& info : info_list) {
        stream << "Type: " << info.type << "\n";
        stream << "Description: " << info.description << "\n";
        
        if (!info.required_params.empty()) {
            stream << "Required Parameters: ";
            for (size_t i = 0; i < info.required_params.size(); ++i) {
                if (i > 0) stream << ", ";
                stream << info.required_params[i];
            }
            stream << "\n";
        }
        
        if (!info.optional_params.empty()) {
            stream << "Optional Parameters: ";
            for (size_t i = 0; i < info.optional_params.size(); ++i) {
                if (i > 0) stream << ", ";
                stream << info.optional_params[i];
            }
            stream << "\n";
        }
        
        if (!info.example_config.empty()) {
            stream << "Example Config: " << info.example_config << "\n";
        }
        
        stream << "\n";
    }
}

} // namespace omop::extract