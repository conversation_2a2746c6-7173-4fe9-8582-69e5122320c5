#pragma once

#include "core/interfaces.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <regex>
#include <chrono>
#include <memory>

namespace omop::extract {

/**
 * @brief Data type classification for schema discovery
 */
enum class DataType {
    STRING,
    INTEGER,
    LONG,
    DOUBLE,
    BOOLEAN,
    DATE,
    DATETIME,
    TIME,
    UUID,
    EMAIL,
    PHONE,
    URL,
    IP_ADDRESS,
    POSTCODE_UK,
    NHS_NUMBER,
    CURRENCY,
    PERCENTAGE,
    JSON,
    ARRAY,
    NULL_TYPE
};

/**
 * @brief Column statistics for enhanced type inference
 */
struct ColumnStatistics {
    size_t total_values{0};
    size_t null_values{0};
    size_t unique_values{0};
    size_t min_length{SIZE_MAX};
    size_t max_length{0};
    double avg_length{0.0};
    
    // Type-specific counts
    size_t integer_count{0};
    size_t double_count{0};
    size_t boolean_count{0};
    size_t date_count{0};
    size_t datetime_count{0};
    size_t email_count{0};
    size_t phone_count{0};
    size_t url_count{0};
    size_t uuid_count{0};
    size_t postcode_count{0};
    size_t nhs_number_count{0};
    
    // Range information for numeric types
    long long min_integer{LLONG_MAX};
    long long max_integer{LLONG_MIN};
    double min_double{std::numeric_limits<double>::max()};
    double max_double{std::numeric_limits<double>::lowest()};
    
    // Sample values for pattern analysis
    std::vector<std::string> sample_values;
    std::unordered_map<std::string, size_t> value_frequencies;
};

/**
 * @brief Advanced schema discovery configuration
 */
struct SchemaDiscoveryOptions {
    size_t sample_size{1000};                    // Number of rows to sample
    double type_confidence_threshold{0.8};       // Minimum confidence for type assignment
    bool detect_patterns{true};                  // Enable pattern-based detection
    bool detect_uk_specific{true};               // Enable UK-specific data types
    bool detect_healthcare{true};                // Enable healthcare-specific patterns
    bool analyze_statistics{true};               // Collect detailed statistics
    size_t max_unique_values_for_enum{50};       // Max unique values to consider as enum
    double null_tolerance{0.2};                  // Max null ratio to still infer specific type
    std::vector<std::string> date_formats{       // Date formats to try
        "%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y", "%Y/%m/%d",
        "%d-%m-%Y", "%m-%d-%Y", "%Y%m%d"
    };
    std::vector<std::string> datetime_formats{   // DateTime formats to try
        "%Y-%m-%d %H:%M:%S", "%d/%m/%Y %H:%M:%S", "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%d %H:%M", "%d/%m/%Y %H:%M"
    };
};

/**
 * @brief Discovered column schema information
 */
struct ColumnSchema {
    std::string name;
    DataType inferred_type{DataType::STRING};
    double confidence{0.0};
    bool nullable{false};
    size_t max_length{0};
    
    // Type-specific properties
    std::vector<std::string> enum_values;        // For enum-like columns
    std::string date_format;                     // For date/datetime columns
    long long min_value{0};                      // For numeric columns
    long long max_value{0};                      // For numeric columns
    std::string pattern;                         // Regex pattern for validation
    
    // Metadata
    std::string description;                     // Auto-generated description
    ColumnStatistics statistics;                 // Detailed statistics
};

/**
 * @brief Complete schema discovery result
 */
struct DiscoveredSchema {
    std::vector<ColumnSchema> columns;
    std::string delimiter{","};
    bool has_header{true};
    std::string encoding{"UTF-8"};
    size_t estimated_row_count{0};
    double discovery_confidence{0.0};
    
    // Quality metrics
    size_t total_rows_analyzed{0};
    size_t valid_rows{0};
    size_t malformed_rows{0};
    std::vector<std::string> warnings;
    std::vector<std::string> recommendations;
};

/**
 * @brief Advanced schema discovery engine
 */
class SchemaDiscoveryEngine {
public:
    /**
     * @brief Constructor
     * @param options Discovery configuration options
     */
    explicit SchemaDiscoveryEngine(const SchemaDiscoveryOptions& options = {});
    
    /**
     * @brief Discover schema from CSV file
     * @param filepath Path to CSV file
     * @return DiscoveredSchema Discovered schema information
     */
    DiscoveredSchema discover_csv_schema(const std::string& filepath);
    
    /**
     * @brief Discover schema from sample data
     * @param headers Column headers
     * @param sample_data Sample rows of data
     * @return DiscoveredSchema Discovered schema information
     */
    DiscoveredSchema discover_schema_from_sample(
        const std::vector<std::string>& headers,
        const std::vector<std::vector<std::string>>& sample_data);
    
    /**
     * @brief Analyze column type from values
     * @param column_name Name of the column
     * @param values Sample values for the column
     * @return ColumnSchema Discovered column schema
     */
    ColumnSchema analyze_column(const std::string& column_name,
                               const std::vector<std::string>& values);
    
    /**
     * @brief Auto-detect CSV delimiter
     * @param sample_lines Sample lines from CSV file
     * @return char Most likely delimiter
     */
    char detect_delimiter(const std::vector<std::string>& sample_lines);
    
    /**
     * @brief Detect if first row is header
     * @param first_row First row values
     * @param second_row Second row values (if available)
     * @return bool True if first row appears to be header
     */
    bool detect_header(const std::vector<std::string>& first_row,
                      const std::vector<std::string>& second_row = {});
    
    /**
     * @brief Convert DataType enum to string
     * @param type Data type
     * @return std::string String representation
     */
    static std::string data_type_to_string(DataType type);
    
    /**
     * @brief Get type confidence threshold
     * @return double Current confidence threshold
     */
    double get_confidence_threshold() const { return options_.type_confidence_threshold; }

private:
    SchemaDiscoveryOptions options_;
    
    // Pattern matchers
    std::regex email_regex_;
    std::regex phone_uk_regex_;
    std::regex url_regex_;
    std::regex uuid_regex_;
    std::regex postcode_uk_regex_;
    std::regex nhs_number_regex_;
    std::regex ip_address_regex_;
    std::regex currency_regex_;
    
    /**
     * @brief Initialize regex patterns
     */
    void initialize_patterns();
    
    /**
     * @brief Classify single value type
     * @param value String value to classify
     * @return DataType Detected type
     */
    DataType classify_value(const std::string& value);
    
    /**
     * @brief Check if value matches integer pattern
     * @param value String value
     * @return bool True if integer
     */
    bool is_integer(const std::string& value);
    
    /**
     * @brief Check if value matches double pattern
     * @param value String value
     * @return bool True if double
     */
    bool is_double(const std::string& value);
    
    /**
     * @brief Check if value matches boolean pattern
     * @param value String value
     * @return bool True if boolean
     */
    bool is_boolean(const std::string& value);
    
    /**
     * @brief Check if value matches date pattern
     * @param value String value
     * @param format Output detected format
     * @return bool True if date
     */
    bool is_date(const std::string& value, std::string& format);
    
    /**
     * @brief Check if value matches datetime pattern
     * @param value String value
     * @param format Output detected format
     * @return bool True if datetime
     */
    bool is_datetime(const std::string& value, std::string& format);
    
    /**
     * @brief Check if value matches UK NHS number pattern
     * @param value String value
     * @return bool True if valid NHS number
     */
    bool is_nhs_number(const std::string& value);
    
    /**
     * @brief Check if value matches UK postcode pattern
     * @param value String value
     * @return bool True if valid UK postcode
     */
    bool is_uk_postcode(const std::string& value);
    
    /**
     * @brief Determine final column type from statistics
     * @param stats Column statistics
     * @return DataType Final inferred type
     */
    DataType determine_column_type(const ColumnStatistics& stats);
    
    /**
     * @brief Calculate confidence score for type assignment
     * @param stats Column statistics
     * @param assigned_type Assigned data type
     * @return double Confidence score (0.0 to 1.0)
     */
    double calculate_confidence(const ColumnStatistics& stats, DataType assigned_type);
    
    /**
     * @brief Generate recommendations for the discovered schema
     * @param schema Discovered schema
     */
    void generate_recommendations(DiscoveredSchema& schema);
    
    /**
     * @brief Create pattern for column validation
     * @param column Column schema
     * @return std::string Regex pattern for validation
     */
    std::string create_validation_pattern(const ColumnSchema& column);
};

/**
 * @brief Factory for creating schema discovery engines with preset configurations
 */
class SchemaDiscoveryFactory {
public:
    /**
     * @brief Create engine for general CSV files
     * @return std::unique_ptr<SchemaDiscoveryEngine> Engine instance
     */
    static std::unique_ptr<SchemaDiscoveryEngine> create_general();
    
    /**
     * @brief Create engine for UK healthcare data
     * @return std::unique_ptr<SchemaDiscoveryEngine> Engine instance
     */
    static std::unique_ptr<SchemaDiscoveryEngine> create_uk_healthcare();
    
    /**
     * @brief Create engine for financial data
     * @return std::unique_ptr<SchemaDiscoveryEngine> Engine instance
     */
    static std::unique_ptr<SchemaDiscoveryEngine> create_financial();
    
    /**
     * @brief Create engine with custom options
     * @param options Custom discovery options
     * @return std::unique_ptr<SchemaDiscoveryEngine> Engine instance
     */
    static std::unique_ptr<SchemaDiscoveryEngine> create_custom(
        const SchemaDiscoveryOptions& options);
};

} // namespace omop::extract