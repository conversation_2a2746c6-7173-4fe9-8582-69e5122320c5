#pragma once

#include "csv_extractor.h"
#include "memory_pool.h"
#include "core/interfaces.h"

namespace omop::extract {

/**
 * @brief Memory-optimized CSV extractor using object pooling
 * 
 * Extends CsvExtractor with memory pooling capabilities to reduce allocation
 * overhead during high-throughput data extraction operations.
 */
class PooledCsvExtractor : public CsvExtractor {
public:
    /**
     * @brief Constructor
     */
    PooledCsvExtractor();

    /**
     * @brief Initialize with pooling configuration
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                    core::ProcessingContext& context) override;

    /**
     * @brief Extract batch using pooled objects
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Batch of extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                    core::ProcessingContext& context) override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "pooled_csv"; }

    /**
     * @brief Get extraction statistics including pooling info
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Finalize and report pool statistics
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Extract batch implementation using pooled objects
     * @param batch_size Size of batch to extract
     * @return std::vector<core::Record> Vector of records
     */
    std::vector<core::Record> extractBatchImpl(size_t batch_size) override;

    /**
     * @brief Create a pooled record
     * @return PooledObject<core::Record> Pooled record object
     */
    PooledObject<core::Record> create_pooled_record();

    /**
     * @brief Create record using pooled resources
     * @param fields Field values
     * @return core::Record Created record
     */
    core::Record create_optimized_record(const std::vector<std::string>& fields);

private:
    // Object pools for memory optimization
    std::unique_ptr<ObjectPool<core::Record>> record_pool_;
    std::unique_ptr<ObjectPool<std::vector<std::string>>> field_vector_pool_;
    
    // Pool configuration
    size_t record_pool_size_{50};
    size_t field_pool_size_{20};
    size_t max_pool_size_{200};
    
    // Statistics
    size_t pooled_records_created_{0};
    size_t pooled_fields_reused_{0};
    std::chrono::steady_clock::time_point pool_created_time_;
};

/**
 * @brief Batch processor with memory pooling for high-throughput scenarios
 */
class BatchProcessor {
public:
    /**
     * @brief Constructor
     * @param batch_pool_size Size of batch object pool
     * @param record_pool_size Size of record object pool
     */
    explicit BatchProcessor(size_t batch_pool_size = 10, size_t record_pool_size = 100);

    /**
     * @brief Process a batch of CSV lines efficiently
     * @param lines Vector of CSV lines to process
     * @param parser CSV field parser
     * @param column_names Column names for record creation
     * @param column_types Column types for conversion
     * @return core::RecordBatch Processed batch
     */
    core::RecordBatch process_batch(const std::vector<std::string>& lines,
                                   CsvFieldParser& parser,
                                   const std::vector<std::string>& column_names,
                                   const std::vector<std::string>& column_types);

    /**
     * @brief Get processing statistics
     * @return Statistics about batch processing performance
     */
    struct ProcessingStats {
        size_t total_batches_processed{0};
        size_t total_records_processed{0};
        size_t pool_hits{0};
        size_t pool_misses{0};
        std::chrono::milliseconds total_processing_time{0};
        double avg_batch_processing_time_ms{0.0};
    };

    ProcessingStats get_stats() const { return stats_; }

    /**
     * @brief Reset processing statistics
     */
    void reset_stats() { stats_ = ProcessingStats{}; }

private:
    ObjectPool<core::RecordBatch> batch_pool_;
    ObjectPool<core::Record> record_pool_;
    StringBufferPool& string_pool_;
    ProcessingStats stats_;
};

/**
 * @brief Memory-aware CSV parsing utilities
 */
class MemoryAwareCsvUtils {
public:
    /**
     * @brief Parse CSV line with minimal allocations
     * @param line Input line
     * @param delimiter Field delimiter
     * @param fields Output vector (will be reused if provided)
     * @return std::vector<std::string>& Reference to fields vector
     */
    static std::vector<std::string>& parse_line_optimized(
        const std::string& line,
        char delimiter,
        std::vector<std::string>& fields);

    /**
     * @brief Estimate memory usage for CSV processing
     * @param file_size File size in bytes
     * @param avg_line_length Average line length
     * @param batch_size Processing batch size
     * @return size_t Estimated memory usage in bytes
     */
    static size_t estimate_memory_usage(size_t file_size,
                                       size_t avg_line_length,
                                       size_t batch_size);

    /**
     * @brief Configure optimal pool sizes based on data characteristics
     * @param expected_records Expected number of records to process
     * @param avg_fields_per_record Average number of fields per record
     * @param available_memory Available memory in bytes
     * @return Pool configuration recommendations
     */
    struct PoolConfig {
        size_t record_pool_size;
        size_t field_pool_size;
        size_t string_buffer_pool_size;
        size_t max_batch_size;
    };

    static PoolConfig calculate_optimal_pools(size_t expected_records,
                                             size_t avg_fields_per_record,
                                             size_t available_memory);
};

} // namespace omop::extract