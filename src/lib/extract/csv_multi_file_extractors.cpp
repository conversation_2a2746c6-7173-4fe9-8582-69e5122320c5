/**
 * @file csv_multi_file_extractors.cpp
 * @brief Implementation of multi-file CSV extractors
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "csv_multi_file_extractors.h"
#include "compression_utils.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <algorithm>
#include <filesystem>

namespace omop::extract {

// MultiFileCsvExtractor implementation

void MultiFileCsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                      core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-multi-csv-extractor");
    logger->info("Initializing multi-file CSV extractor");

    try {
        // Extract file paths
        if (auto it = config.find("files"); it != config.end()) {
            file_paths_ = std::any_cast<std::vector<std::string>>(it->second);
        } else {
            throw common::ConfigurationException("Multi-file CSV extractor requires 'files' parameter");
        }

        if (file_paths_.empty()) {
            throw common::ConfigurationException("No files specified for multi-file CSV extractor");
        }

        // Check skip headers option
        if (auto it = config.find("skip_headers_after_first"); it != config.end()) {
            skip_headers_after_first_ = std::any_cast<bool>(it->second);
        }

        // Initialize with first file
        current_file_index_ = 0;
        
        // Create a config for the first file
        std::unordered_map<std::string, std::any> first_file_config = config;
        first_file_config["filepath"] = file_paths_[0];
        
        // Initialize base CSV extractor with first file
        CsvExtractor::initialize(first_file_config, context);
        
        logger->info("Multi-file CSV extractor initialized with {} files", file_paths_.size());

    } catch (const std::bad_any_cast& e) {
        throw common::ConfigurationException("Invalid configuration parameter type: " + std::string(e.what()));
    }
}

core::RecordBatch MultiFileCsvExtractor::extract_batch(size_t batch_size,
                                                       core::ProcessingContext& context) {
    core::RecordBatch batch;
    
    while (batch.getRecords().size() < batch_size) {
        // Try to extract from current file
        if (has_more_data()) {
            auto current_batch = CsvExtractor::extract_batch(batch_size - batch.getRecords().size(), context);
            
            // Append records to our batch
            batch.getRecordsMutable().insert(batch.getRecordsMutable().end(), 
                                current_batch.getRecords().begin(), 
                                current_batch.getRecords().end());
            
            // Update metadata
            batch.setTotalSize(batch.getTotalSize() + current_batch.getTotalSize());
            if (batch.getExtractionTime() == std::chrono::milliseconds::zero()) {
                batch.setExtractionTime(current_batch.getExtractionTime());
            }
        } else {
            // Move to next file
            if (!next_file()) {
                break; // No more files
            }
        }
    }
    
    return batch;
}

bool MultiFileCsvExtractor::has_more_data() const {
    // Check if current file has more data or if there are more files
    return CsvExtractor::has_more_data() || (current_file_index_ + 1 < file_paths_.size());
}

bool MultiFileCsvExtractor::next_file() {
    auto logger = common::Logger::get("omop-multi-csv-extractor");
    
    // Close current file
    disconnect();
    
    // Move to next file
    current_file_index_++;
    if (current_file_index_ >= file_paths_.size()) {
        logger->info("Reached end of file list");
        return false;
    }
    
    // Open next file
    filepath_ = file_paths_[current_file_index_];
    logger->info("Moving to next file: {} ({}/{})", 
                filepath_, current_file_index_ + 1, file_paths_.size());
    
    if (!connect()) {
        logger->error("Failed to open next file: {}", filepath_);
        return false;
    }
    
    // Skip header if not the first file and option is set
    if (skip_headers_after_first_ && current_file_index_ > 0 && options_.has_header) {
        std::string header_line;
        if (std::getline(file_stream_, header_line)) {
            current_line_++;
        }
    }
    
    return true;
}

// CsvDirectoryExtractor implementation

void CsvDirectoryExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                      core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-csv-directory-extractor");
    logger->info("Initializing CSV directory extractor");

    try {
        // Extract directory path
        if (auto it = config.find("directory"); it != config.end()) {
            directory_path_ = std::any_cast<std::string>(it->second);
        } else {
            throw common::ConfigurationException("CSV directory extractor requires 'directory' parameter");
        }

        // Extract pattern
        std::string pattern = ".*\\.csv$";
        if (auto it = config.find("pattern"); it != config.end()) {
            pattern = std::any_cast<std::string>(it->second);
        }
        file_pattern_ = std::regex(pattern);

        // Extract recursive option
        if (auto it = config.find("recursive"); it != config.end()) {
            recursive_search_ = std::any_cast<bool>(it->second);
        }

        // Find all CSV files in directory
        auto found_files = find_csv_files(directory_path_, pattern, recursive_search_);
        
        if (found_files.empty()) {
            throw common::ConfigurationException("No CSV files found in directory: " + directory_path_);
        }

        // Sort files for consistent processing order
        std::sort(found_files.begin(), found_files.end());

        // Create modified config with files list
        std::unordered_map<std::string, std::any> multi_file_config = config;
        multi_file_config["files"] = found_files;

        // Initialize multi-file extractor
        MultiFileCsvExtractor::initialize(multi_file_config, context);
        
        logger->info("Found {} CSV files in directory: {}", found_files.size(), directory_path_);

    } catch (const std::bad_any_cast& e) {
        throw common::ConfigurationException("Invalid configuration parameter type: " + std::string(e.what()));
    }
}

std::vector<std::string> CsvDirectoryExtractor::find_csv_files(const std::string& directory,
                                                              const std::string& pattern,
                                                              bool recursive) {
    std::vector<std::string> csv_files;
    auto logger = common::Logger::get("omop-csv-directory-extractor");
    
    try {
        if (!std::filesystem::exists(directory)) {
            logger->warn("Directory does not exist: {}", directory);
            return csv_files;
        }

        std::regex pattern_regex(pattern);
        
        if (recursive) {
            std::filesystem::recursive_directory_iterator iterator(directory);
            for (const auto& entry : iterator) {
            if (entry.is_regular_file()) {
                std::string filename = entry.path().filename().string();
                if (std::regex_match(filename, pattern_regex)) {
                    csv_files.push_back(entry.path().string());
                    logger->debug("Found CSV file: {}", entry.path().string());
                }
            }
        }
        } else {
            std::filesystem::directory_iterator iterator(directory);
            for (const auto& entry : iterator) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    if (std::regex_match(filename, pattern_regex)) {
                        csv_files.push_back(entry.path().string());
                        logger->debug("Found CSV file: {}", entry.path().string());
                    }
                }
            }
        }
        
    } catch (const std::exception& e) {
        logger->error("Error scanning directory {}: {}", directory, e.what());
    }

    return csv_files;
}

// CompressedCsvExtractor implementation

void CompressedCsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                       core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-compressed-csv-extractor");
    logger->info("Initializing compressed CSV extractor");

    try {
        // Get original filepath
        std::string original_filepath;
        if (auto it = config.find("filepath"); it != config.end()) {
            original_filepath = std::any_cast<std::string>(it->second);
        } else {
            throw common::ConfigurationException("Compressed CSV extractor requires 'filepath' parameter");
        }

        // Detect compression format
        compression_format_ = CompressionUtils::detect_compression(original_filepath);
        if (compression_format_ == CompressionFormat::None) {
            throw common::ConfigurationException("File is not compressed: " + original_filepath);
        }

        // Generate temporary file path
        temp_file_path_ = CompressionUtils::generate_temp_path(original_filepath);

        // Decompress file
        if (!CompressionUtils::decompress_file(original_filepath, temp_file_path_, compression_format_)) {
            throw common::ExtractionException("Failed to decompress file: " + original_filepath);
        }

        // Create modified config with decompressed file path
        std::unordered_map<std::string, std::any> csv_config = config;
        csv_config["filepath"] = temp_file_path_;

        // Initialize base CSV extractor with decompressed file
        CsvExtractor::initialize(csv_config, context);
        
        logger->info("Successfully decompressed {} to {}", original_filepath, temp_file_path_);

    } catch (const std::bad_any_cast& e) {
        throw common::ConfigurationException("Invalid configuration parameter type: " + std::string(e.what()));
    }
}

void CompressedCsvExtractor::finalize(core::ProcessingContext& context) {
    // Finalize base extractor first
    CsvExtractor::finalize(context);
    
    // Clean up temporary file
    if (cleanup_temp_file_ && !temp_file_path_.empty() && 
        std::filesystem::exists(temp_file_path_)) {
        try {
            std::filesystem::remove(temp_file_path_);
            auto logger = common::Logger::get("omop-compressed-csv-extractor");
            logger->info("Cleaned up temporary file: {}", temp_file_path_);
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-compressed-csv-extractor");
            logger->warn("Failed to clean up temporary file {}: {}", temp_file_path_, e.what());
        }
    }
}

std::unordered_map<std::string, std::any> CompressedCsvExtractor::get_statistics() const {
    auto stats = CsvExtractor::get_statistics();
    
    // Add compression-specific statistics
    stats["compression_format"] = CompressionUtils::format_to_string(compression_format_);
    stats["temp_file_path"] = temp_file_path_;
    
    return stats;
}

} // namespace omop::extract