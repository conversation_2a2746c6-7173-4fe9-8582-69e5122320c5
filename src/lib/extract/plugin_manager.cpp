#include "plugin_manager.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <algorithm>
#include <dlfcn.h>

#ifdef _WIN32
#include <windows.h>
#else
#include <dlfcn.h>
#endif

namespace omop::extract {

PluginManager::PluginManager(const std::string& plugin_directory)
    : plugin_directory_(plugin_directory) {
    // Ensure plugin directory exists
    std::filesystem::create_directories(plugin_directory_);
    
    auto logger = common::Logger::get("omop-plugin-manager");
    logger->info("Plugin manager initialized with directory: {}", plugin_directory_);
}

PluginManager::~PluginManager() {
    std::lock_guard<std::mutex> lock(plugins_mutex_);
    
    auto logger = common::Logger::get("omop-plugin-manager");
    logger->info("Shutting down plugin manager, unloading {} plugins", loaded_plugins_.size());
    
    // Unload all plugins
    for (auto& [name, plugin] : loaded_plugins_) {
        if (plugin && plugin->status == PluginStatus::LOADED) {
            unload_plugin_library(*plugin);
        }
    }
    loaded_plugins_.clear();
}

size_t PluginManager::scan_for_plugins() {
    auto logger = common::Logger::get("omop-plugin-manager");
    logger->info("Scanning for plugins in directory: {}", plugin_directory_);
    
    if (!std::filesystem::exists(plugin_directory_)) {
        logger->warn("Plugin directory does not exist: {}", plugin_directory_);
        return 0;
    }
    
    size_t found_count = 0;
    std::string extension = get_library_extension();
    
    try {
        for (const auto& entry : std::filesystem::directory_iterator(plugin_directory_)) {
            if (entry.is_regular_file()) {
                std::string filename = entry.path().filename().string();
                if (filename.ends_with(extension)) {
                    logger->debug("Found potential plugin: {}", filename);
                    
                    if (validate_plugin_file(entry.path().string())) {
                        found_count++;
                        update_file_time(entry.path().string());
                    }
                }
            }
        }
    } catch (const std::exception& e) {
        logger->error("Error scanning plugin directory: {}", e.what());
    }
    
    logger->info("Found {} potential plugins", found_count);
    return found_count;
}

bool PluginManager::load_plugin(const std::string& plugin_path) {
    std::lock_guard<std::mutex> lock(plugins_mutex_);
    
    auto logger = common::Logger::get("omop-plugin-manager");
    logger->info("Loading plugin: {}", plugin_path);
    
    // Check if already loaded
    std::string plugin_name = std::filesystem::path(plugin_path).stem().string();
    if (loaded_plugins_.find(plugin_name) != loaded_plugins_.end()) {
        logger->warn("Plugin {} is already loaded", plugin_name);
        return false;
    }
    
    // Load the plugin
    auto loaded_plugin = load_plugin_library(plugin_path);
    if (!loaded_plugin) {
        logger->error("Failed to load plugin: {}", plugin_path);
        return false;
    }
    
    // Store the loaded plugin
    loaded_plugins_[plugin_name] = std::move(loaded_plugin);
    
    logger->info("Successfully loaded plugin: {} ({})", 
                 plugin_name, loaded_plugins_[plugin_name]->metadata.version);
    
    return true;
}

bool PluginManager::unload_plugin(const std::string& plugin_name) {
    std::lock_guard<std::mutex> lock(plugins_mutex_);
    
    auto logger = common::Logger::get("omop-plugin-manager");
    logger->info("Unloading plugin: {}", plugin_name);
    
    auto it = loaded_plugins_.find(plugin_name);
    if (it == loaded_plugins_.end()) {
        logger->warn("Plugin {} is not loaded", plugin_name);
        return false;
    }
    
    auto& plugin = it->second;
    if (plugin->reference_count > 0) {
        logger->warn("Cannot unload plugin {} - still has {} active references", 
                     plugin_name, plugin->reference_count);
        return false;
    }
    
    unload_plugin_library(*plugin);
    loaded_plugins_.erase(it);
    
    logger->info("Successfully unloaded plugin: {}", plugin_name);
    return true;
}

bool PluginManager::reload_plugin(const std::string& plugin_name) {
    auto logger = common::Logger::get("omop-plugin-manager");
    logger->info("Reloading plugin: {}", plugin_name);
    
    std::string plugin_path;
    {
        std::lock_guard<std::mutex> lock(plugins_mutex_);
        auto it = loaded_plugins_.find(plugin_name);
        if (it != loaded_plugins_.end()) {
            plugin_path = it->second->path;
        }
    }
    
    if (plugin_path.empty()) {
        logger->error("Cannot reload plugin {} - not currently loaded", plugin_name);
        return false;
    }
    
    // Unload and reload
    if (!unload_plugin(plugin_name)) {
        return false;
    }
    
    return load_plugin(plugin_path);
}

std::vector<std::string> PluginManager::get_loaded_plugins() const {
    std::lock_guard<std::mutex> lock(plugins_mutex_);
    
    std::vector<std::string> plugin_names;
    plugin_names.reserve(loaded_plugins_.size());
    
    for (const auto& [name, plugin] : loaded_plugins_) {
        if (plugin && plugin->status == PluginStatus::LOADED) {
            plugin_names.push_back(name);
        }
    }
    
    return plugin_names;
}

PluginMetadata PluginManager::get_plugin_metadata(const std::string& plugin_name) const {
    std::lock_guard<std::mutex> lock(plugins_mutex_);
    
    auto it = loaded_plugins_.find(plugin_name);
    if (it != loaded_plugins_.end() && it->second) {
        return it->second->metadata;
    }
    
    return {}; // Empty metadata if not found
}

PluginStatus PluginManager::get_plugin_status(const std::string& plugin_name) const {
    std::lock_guard<std::mutex> lock(plugins_mutex_);
    
    auto it = loaded_plugins_.find(plugin_name);
    if (it != loaded_plugins_.end() && it->second) {
        return it->second->status;
    }
    
    return PluginStatus::NOT_LOADED;
}

std::string PluginManager::find_plugin_for_type(const std::string& extractor_type) const {
    std::lock_guard<std::mutex> lock(plugins_mutex_);
    
    for (const auto& [name, plugin] : loaded_plugins_) {
        if (plugin && plugin->status == PluginStatus::LOADED && plugin->plugin_instance) {
            auto supported_types = plugin->plugin_instance->get_supported_types();
            if (std::find(supported_types.begin(), supported_types.end(), extractor_type) 
                != supported_types.end()) {
                return name;
            }
        }
    }
    
    return {}; // Empty string if not found
}

std::unique_ptr<core::IExtractor> PluginManager::create_extractor(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {
    
    auto logger = common::Logger::get("omop-plugin-manager");
    
    std::string plugin_name = find_plugin_for_type(type);
    if (plugin_name.empty()) {
        logger->debug("No plugin found for extractor type: {}", type);
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(plugins_mutex_);
    auto it = loaded_plugins_.find(plugin_name);
    if (it == loaded_plugins_.end() || !it->second || !it->second->plugin_instance) {
        logger->error("Plugin {} found but instance is invalid", plugin_name);
        return nullptr;
    }
    
    auto& plugin = it->second;
    
    // Validate configuration
    if (!plugin->plugin_instance->validate_config(type, config)) {
        logger->error("Invalid configuration for extractor type {} in plugin {}", type, plugin_name);
        return nullptr;
    }
    
    // Create extractor
    try {
        auto extractor = plugin->plugin_instance->create_extractor(type, config);
        if (extractor) {
            plugin->reference_count++;
            logger->info("Created extractor of type {} using plugin {}", type, plugin_name);
        }
        return extractor;
    } catch (const std::exception& e) {
        logger->error("Failed to create extractor {} from plugin {}: {}", type, plugin_name, e.what());
        return nullptr;
    }
}

void PluginManager::register_with_factory() {
    auto logger = common::Logger::get("omop-plugin-manager");
    logger->info("Registering plugin manager with extractor factory");
    
    // Register a custom creator function that uses plugins
    ExtractorFactoryRegistry::register_custom_creator([this](const std::string& type, 
                                                            const std::unordered_map<std::string, std::any>& config) {
        return this->create_extractor(type, config);
    });
}

void PluginManager::set_plugin_directory(const std::string& directory) {
    std::lock_guard<std::mutex> lock(plugins_mutex_);
    plugin_directory_ = directory;
    std::filesystem::create_directories(plugin_directory_);
}

void PluginManager::enable_hot_reloading(bool enable) {
    hot_reloading_enabled_ = enable;
    
    auto logger = common::Logger::get("omop-plugin-manager");
    logger->info("Hot reloading {}", enable ? "enabled" : "disabled");
}

void PluginManager::check_for_changes() {
    if (!hot_reloading_enabled_) {
        return;
    }
    
    auto logger = common::Logger::get("omop-plugin-manager");
    
    std::vector<std::string> plugins_to_reload;
    
    {
        std::lock_guard<std::mutex> lock(plugins_mutex_);
        for (const auto& [name, plugin] : loaded_plugins_) {
            if (plugin && is_file_modified(plugin->path)) {
                if (plugin->reference_count == 0) {
                    plugins_to_reload.push_back(name);
                } else {
                    logger->warn("Plugin {} has been modified but has active references, skipping reload", name);
                }
            }
        }
    }
    
    // Reload modified plugins (outside the lock to avoid deadlock)
    for (const auto& plugin_name : plugins_to_reload) {
        logger->info("Hot reloading modified plugin: {}", plugin_name);
        reload_plugin(plugin_name);
    }
}

std::vector<LoadedPlugin> PluginManager::get_plugin_details() const {
    std::lock_guard<std::mutex> lock(plugins_mutex_);
    
    std::vector<LoadedPlugin> details;
    details.reserve(loaded_plugins_.size());
    
    for (const auto& [name, plugin] : loaded_plugins_) {
        if (plugin) {
            details.push_back(*plugin); // Copy plugin info
        }
    }
    
    return details;
}

std::unique_ptr<LoadedPlugin> PluginManager::load_plugin_library(const std::string& library_path) {
    auto logger = common::Logger::get("omop-plugin-manager");
    
    auto loaded_plugin = std::make_unique<LoadedPlugin>();
    loaded_plugin->path = library_path;
    loaded_plugin->status = PluginStatus::LOADING;
    loaded_plugin->load_time = std::chrono::system_clock::now();
    
#ifdef _WIN32
    // Windows DLL loading
    HMODULE handle = LoadLibraryA(library_path.c_str());
    if (!handle) {
        DWORD error = GetLastError();
        loaded_plugin->error_message = "Failed to load DLL, error code: " + std::to_string(error);
        loaded_plugin->status = PluginStatus::FAILED;
        return loaded_plugin;
    }
    loaded_plugin->library_handle = handle;
    
    // Get entry point
    typedef IExtractorPlugin* (*CreatePluginFunc)();
    CreatePluginFunc create_plugin = (CreatePluginFunc)GetProcAddress(handle, "create_plugin");
    
    if (!create_plugin) {
        loaded_plugin->error_message = "Entry point 'create_plugin' not found";
        loaded_plugin->status = PluginStatus::FAILED;
        FreeLibrary(handle);
        return loaded_plugin;
    }
    
#else
    // Unix/Linux shared library loading
    void* handle = dlopen(library_path.c_str(), RTLD_LAZY);
    if (!handle) {
        loaded_plugin->error_message = std::string("Failed to load shared library: ") + dlerror();
        loaded_plugin->status = PluginStatus::FAILED;
        return loaded_plugin;
    }
    loaded_plugin->library_handle = handle;
    
    // Clear any existing error
    dlerror();
    
    // Get entry point
    typedef IExtractorPlugin* (*CreatePluginFunc)();
    CreatePluginFunc create_plugin = (CreatePluginFunc)dlsym(handle, "create_plugin");
    
    const char* dlsym_error = dlerror();
    if (dlsym_error) {
        loaded_plugin->error_message = std::string("Entry point 'create_plugin' not found: ") + dlsym_error;
        loaded_plugin->status = PluginStatus::FAILED;
        dlclose(handle);
        return loaded_plugin;
    }
#endif
    
    // Create plugin instance
    try {
        loaded_plugin->plugin_instance.reset(create_plugin());
        if (!loaded_plugin->plugin_instance) {
            loaded_plugin->error_message = "create_plugin returned null";
            loaded_plugin->status = PluginStatus::FAILED;
            return loaded_plugin;
        }
        
        // Get metadata and check API compatibility
        loaded_plugin->metadata = loaded_plugin->plugin_instance->get_metadata();
        loaded_plugin->api_version = loaded_plugin->plugin_instance->get_api_version();
        
        if (!is_api_compatible(loaded_plugin->api_version)) {
            loaded_plugin->error_message = "Incompatible API version: " + loaded_plugin->api_version.to_string();
            loaded_plugin->status = PluginStatus::INCOMPATIBLE;
            return loaded_plugin;
        }
        
        // Initialize plugin
        if (!loaded_plugin->plugin_instance->initialize()) {
            loaded_plugin->error_message = "Plugin initialization failed";
            loaded_plugin->status = PluginStatus::FAILED;
            return loaded_plugin;
        }
        
        loaded_plugin->status = PluginStatus::LOADED;
        logger->info("Successfully loaded plugin: {} v{}", 
                     loaded_plugin->metadata.name, loaded_plugin->metadata.version);
        
    } catch (const std::exception& e) {
        loaded_plugin->error_message = std::string("Exception during plugin creation: ") + e.what();
        loaded_plugin->status = PluginStatus::FAILED;
    }
    
    return loaded_plugin;
}

void PluginManager::unload_plugin_library(LoadedPlugin& plugin) {
    auto logger = common::Logger::get("omop-plugin-manager");
    
    plugin.status = PluginStatus::UNLOADING;
    
    // Shutdown plugin instance
    if (plugin.plugin_instance) {
        try {
            plugin.plugin_instance->shutdown();
        } catch (const std::exception& e) {
            logger->warn("Exception during plugin shutdown: {}", e.what());
        }
        plugin.plugin_instance.reset();
    }
    
    // Unload library
    if (plugin.library_handle) {
#ifdef _WIN32
        FreeLibrary(static_cast<HMODULE>(plugin.library_handle));
#else
        dlclose(plugin.library_handle);
#endif
        plugin.library_handle = nullptr;
    }
    
    plugin.status = PluginStatus::NOT_LOADED;
}

bool PluginManager::is_api_compatible(const PluginApiVersion& plugin_api) const {
    return current_api_version_.is_compatible(plugin_api);
}

std::string PluginManager::get_library_extension() const {
#ifdef _WIN32
    return ".dll";
#elif defined(__APPLE__)
    return ".dylib";
#else
    return ".so";
#endif
}

std::string PluginManager::get_entry_point_symbol() const {
    return "create_plugin";
}

bool PluginManager::validate_plugin_file(const std::string& plugin_path) const {
    if (!std::filesystem::exists(plugin_path)) {
        return false;
    }
    
    if (!std::filesystem::is_regular_file(plugin_path)) {
        return false;
    }
    
    // Check file extension
    std::string extension = get_library_extension();
    if (!plugin_path.ends_with(extension)) {
        return false;
    }
    
    // Additional validation could be added here (file size, permissions, etc.)
    
    return true;
}

void PluginManager::update_file_time(const std::string& plugin_path) {
    try {
        plugin_file_times_[plugin_path] = std::filesystem::last_write_time(plugin_path);
    } catch (const std::exception&) {
        // Ignore errors
    }
}

bool PluginManager::is_file_modified(const std::string& plugin_path) const {
    try {
        auto current_time = std::filesystem::last_write_time(plugin_path);
        auto it = plugin_file_times_.find(plugin_path);
        
        if (it == plugin_file_times_.end()) {
            return true; // File time not tracked, consider modified
        }
        
        return current_time != it->second;
    } catch (const std::exception&) {
        return false; // On error, assume not modified
    }
}

// PluginFactory implementation

PluginFactory& PluginFactory::instance() {
    static PluginFactory instance;
    return instance;
}

std::unique_ptr<PluginManager> PluginFactory::create_plugin_manager(const std::string& plugin_directory) {
    return std::make_unique<PluginManager>(plugin_directory);
}

void PluginFactory::register_plugin_manager(std::shared_ptr<PluginManager> manager) {
    std::lock_guard<std::mutex> lock(factory_mutex_);
    global_plugin_manager_ = std::move(manager);
}

std::shared_ptr<PluginManager> PluginFactory::get_plugin_manager() {
    std::lock_guard<std::mutex> lock(factory_mutex_);
    return global_plugin_manager_;
}

// ExtractorPluginBase implementation

ExtractorPluginBase::ExtractorPluginBase(const PluginMetadata& metadata)
    : metadata_(metadata) {
}

PluginMetadata ExtractorPluginBase::get_metadata() const {
    return metadata_;
}

PluginApiVersion ExtractorPluginBase::get_api_version() const {
    return api_version_;
}

bool ExtractorPluginBase::initialize() {
    initialized_ = true;
    return true;
}

void ExtractorPluginBase::shutdown() {
    initialized_ = false;
}

} // namespace omop::extract