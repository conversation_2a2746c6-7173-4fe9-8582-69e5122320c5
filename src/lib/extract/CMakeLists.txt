# Extract library CMakeLists.txt

# Define all sources and headers upfront
set(EXTRACT_SOURCES
    connection_pool.cpp
    csv_extractor.cpp
    csv_field_parser.cpp
    compression_utils.cpp
    csv_multi_file_extractors.cpp
    streaming_csv_extractor.cpp
    memory_pool.cpp
    pooled_csv_extractor.cpp
    extractor_base.cpp
    extract_utils.cpp
    database_connector.cpp
    extractor_factory.cpp
    json_extractor.cpp
    schema_discovery.cpp
    performance_profiler.cpp
    failover_connection_pool.cpp
    plugin_manager.cpp
)

set(EXTRACT_HEADERS
    csv_extractor.h
    csv_field_parser.h
    compression_utils.h
    csv_multi_file_extractors.h
    streaming_csv_extractor.h
    memory_pool.h
    pooled_csv_extractor.h
    database_connector.h
    extract.h
    extractor_base.h
    extract_utils.h
    extractor_factory.h
    json_extractor.h
    schema_discovery.h
    performance_profiler.h
    failover_connection_pool.h
    plugin_manager.h
)

# Platform-specific sources
if(WIN32)
    list(APPEND EXTRACT_SOURCES platform/windows_utils.cpp)
elseif(UNIX)
    list(APPEND EXTRACT_SOURCES platform/unix_utils.cpp)
endif()

# Database connector support
set(DATABASE_DEPS "")
set(DATABASE_DEFS "")

# PostgreSQL support
find_package(PostgreSQL)
if(PostgreSQL_FOUND)
    list(APPEND EXTRACT_SOURCES postgresql_connector.cpp)
    list(APPEND EXTRACT_HEADERS postgresql_connector.h)
    list(APPEND DATABASE_DEPS PostgreSQL::PostgreSQL)
    list(APPEND DATABASE_DEFS "OMOP_HAS_POSTGRESQL")
endif()

# MySQL support
find_package(MySQL)
if(MySQL_FOUND OR TARGET MySQL::MySQL)
    list(APPEND EXTRACT_SOURCES mysql_connector.cpp)
    list(APPEND EXTRACT_HEADERS mysql_connector.h)
    list(APPEND DATABASE_DEPS MySQL::MySQL)
    list(APPEND DATABASE_DEFS "OMOP_HAS_MYSQL")
endif()

# ODBC support
find_package(ODBC)
if(ODBC_FOUND)
    list(APPEND EXTRACT_SOURCES odbc_connector.cpp)
    list(APPEND EXTRACT_HEADERS odbc_connector.h)
    list(APPEND DATABASE_DEPS ODBC::ODBC)
    list(APPEND DATABASE_DEFS "OMOP_HAS_ODBC")
endif()

# Compression dependencies
set(COMPRESSION_DEPS "")
set(COMPRESSION_DEFS "")

if(HAVE_BZLIB AND TARGET BZip2::BZip2)
    list(APPEND COMPRESSION_DEPS BZip2::BZip2)
    list(APPEND COMPRESSION_DEFS "OMOP_HAS_BZLIB")
endif()

if(HAVE_LZMA AND TARGET LibLZMA::LibLZMA)
    list(APPEND COMPRESSION_DEPS LibLZMA::LibLZMA)
    list(APPEND COMPRESSION_DEFS "OMOP_HAS_LZMA")
endif()

if(HAVE_LIBARCHIVE AND TARGET LibArchive::LibArchive)
    list(APPEND COMPRESSION_DEPS LibArchive::LibArchive)
    list(APPEND COMPRESSION_DEFS "OMOP_HAS_LIBARCHIVE")
endif()

# Create the library
add_library(omop_extract STATIC ${EXTRACT_SOURCES})

# Configure using omop_configure_library function
omop_configure_library(omop_extract
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    PUBLIC_DEPS
        omop_core
        omop_common
    PRIVATE_DEPS
        nlohmann_json::nlohmann_json
        spdlog::spdlog
        ZLIB::ZLIB
        Threads::Threads
        ${DATABASE_DEPS}
        ${COMPRESSION_DEPS}
    HEADERS
        ${EXTRACT_HEADERS}
)

# Add compile definitions for database and compression support
if(DATABASE_DEFS)
    target_compile_definitions(omop_extract PUBLIC ${DATABASE_DEFS})
endif()

if(COMPRESSION_DEFS)
    target_compile_definitions(omop_extract PRIVATE ${COMPRESSION_DEFS})
endif()

# Install platform-specific headers
install(DIRECTORY platform/
    DESTINATION include/omop/extract/platform
    FILES_MATCHING PATTERN "*.h"
)

# Generate export header
include(GenerateExportHeader)
generate_export_header(omop_extract
    EXPORT_FILE_NAME ${CMAKE_CURRENT_BINARY_DIR}/extract_export.h
)

install(FILES ${CMAKE_CURRENT_BINARY_DIR}/extract_export.h
    DESTINATION include/omop/extract
)
