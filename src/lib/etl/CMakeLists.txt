# ETL library CMakeLists.txt

# Validate target naming
omop_validate_target_name("omop_etl" "omop_")

# Create ETL library (header-only interface library)
add_library(omop_etl INTERFACE)

# Set ETL library sources (header-only for now)
set(ETL_HEADERS
    etl_service.h
    etl_service_client.h
)

# Set standard properties for INTERFACE library
set_target_properties(omop_etl PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    OUTPUT_NAME omop_etl
    EXPORT_NAME omop_etl
)

# Set C++20 standard for INTERFACE target
target_compile_features(omop_etl INTERFACE cxx_std_20)

# Set include directories with modern generator expressions
target_include_directories(omop_etl
    INTERFACE
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<INSTALL_INTERFACE:include>
)

# Link dependencies
target_link_libraries(omop_etl
    INTERFACE
        omop_common
        omop_core
)

# Add compile definitions for ETL library
target_compile_definitions(omop_etl 
    INTERFACE
        OMOP_ETL_VERSION="${PROJECT_VERSION}"
)

# Install rules
install(TARGETS omop_etl
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

# Install headers
string(REPLACE "omop_" "" component_name "omop_etl")
install(FILES ${ETL_HEADERS}
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/omop/etl
)

# Export component information
omop_print_component_summary("ETL Library" "omop_etl")

# Add alias for consistent usage
add_library(omop::etl ALIAS omop_etl) 