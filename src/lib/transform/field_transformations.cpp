#include "transform/field_transformations.h"
#include "transform/transformations.h"
#include "transform/transformation_engine.h"
#include "transform/string_transformations.h"
#include "transform/unified_string_transformation.h"
#include "transform/date_transformations.h"
#include "transform/numeric_transformations.h"
#include "transform/conditional_transformations.h"
#include "transform/vocabulary_transformations.h"
#include "transform/custom_transformations.h"
#include <algorithm>
#include <cctype>
#include <sstream>
#include <iomanip>
#include <regex>
#include <string>
#include <optional>
#include <unordered_map>
#include <vector>
#include <mutex>

// Forward declarations for basic transformations
namespace omop::transform {
    class DirectTransformation;
    class DateTransformation;
    class NumericTransformation;
    class StringConcatenationTransformation;
    class ConditionalTransformation;
}

namespace omop::transform {

// All TransformationUtils:: and TransformationRegistry:: method definitions have been removed from this file.
// Only field transformation registration code remains.

// Function to register all transformations
void register_all_transformations() {
    auto& registry = TransformationRegistry::instance();

    // Register basic transformations
    registry.register_transformation("direct",
        []() { return std::make_unique<DirectTransformation>(); });

    registry.register_transformation("Direct",  // Add capitalised version for tests
        []() { return std::make_unique<DirectTransformation>(); });

    registry.register_transformation("date_transform",
        []() { return std::make_unique<DateTransformation>(); });

    registry.register_transformation("numeric_transform",
        []() { return std::make_unique<NumericTransformation>(); });

    // Add basic aliases for simpler transformation names
    registry.register_transformation("numeric",
        []() { return std::make_unique<NumericTransformation>(); });

    registry.register_transformation("string_concatenation",
        []() { return std::make_unique<StringConcatenationTransformation>(); });

    registry.register_transformation("string",
        []() { return std::make_unique<StringManipulationTransformation>(); });

    // Register unified string transformation
    registry.register_transformation("string_transform",
        []() { return std::make_unique<UnifiedStringTransformation>(); });

    registry.register_transformation("conditional",
        []() { return std::make_unique<ConditionalTransformation>(); });

    // Register string transformations
    registry.register_transformation("string_manipulation",
        []() { return std::make_unique<StringManipulationTransformation>(); });

    registry.register_transformation("string_pattern_extraction",
        []() { return std::make_unique<StringPatternExtractionTransformation>(); });

    // Register vocabulary transformations
    registry.register_transformation("vocabulary_mapping",
        []() { return std::make_unique<VocabularyTransformation>(VocabularyServiceManager::instance()); });

    registry.register_transformation("concept_hierarchy",
        []() { return std::make_unique<ConceptHierarchyTransformation>(); });

    registry.register_transformation("domain_mapping",
        []() { return std::make_unique<DomainMappingTransformation>(); });

    registry.register_transformation("concept_relationship",
        []() { return std::make_unique<ConceptRelationshipTransformation>(); });

    // Register custom transformations
    registry.register_transformation("javascript",
        []() { return std::make_unique<JavaScriptTransformation>(); });

    registry.register_transformation("sql",
        []() { return std::make_unique<SQLTransformation>(); });

    registry.register_transformation("python",
        []() { return std::make_unique<PythonTransformation>(); });

    registry.register_transformation("plugin",
        []() { return std::make_unique<PluginTransformation>(); });

    registry.register_transformation("composite",
        []() { return std::make_unique<CompositeTransformation>(); });
}

// Static registration function for basic transformations
void register_basic_transformations() {
    static std::once_flag registration_flag;
    std::call_once(registration_flag, []() {
        register_all_transformations();
    });
}

// Ensure registration happens on first use
struct TransformationRegistrar {
    TransformationRegistrar() { register_basic_transformations(); }
};
static TransformationRegistrar registrar;

} // namespace omop::transform