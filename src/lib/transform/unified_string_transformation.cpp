#include "transform/unified_string_transformation.h"
#include "common/logging.h"
#include <algorithm>
#include <regex>
#include <cctype>
#include <format>

namespace omop::transform {

TransformationResult UnifiedStringTransformation::transform_detailed(const std::any& input,
                                                                    core::ProcessingContext& context) {
    TransformationResult result;

    try {
        if (!validate_input(input)) {
            result.set_error("Invalid input for string transformation");
            return result;
        }

        std::string value = extract_string_value(input);

        switch (operation_) {
            case Operation::Format:
                return format_operation(value);
            case Operation::Validate:
                return validate_operation(value);
            case Operation::Manipulation:
            case Operation::Extract:
                // For now, return the input as-is for these operations
                result.value = value;
                result.success = true;
                break;
        }

        result.metadata["operation"] = "string_transform";
        result.metadata["original_length"] = static_cast<int>(value.length());
        
        return result;
    } catch (const std::exception& e) {
        result.set_error(std::format("String transformation failed: {}", e.what()));
        return result;
    }
}

bool UnifiedStringTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) {
        return false;
    }

    try {
        extract_string_value(input);
        return true;
    } catch (...) {
        return false;
    }
}

void UnifiedStringTransformation::configure(const YAML::Node& params) {
    if (params["operation"]) {
        std::string op_str = params["operation"].as<std::string>();
        if (op_str == "format") {
            operation_ = Operation::Format;
        } else if (op_str == "validate") {
            operation_ = Operation::Validate;
        } else if (op_str == "manipulation") {
            operation_ = Operation::Manipulation;
        } else if (op_str == "extract") {
            operation_ = Operation::Extract;
        }
    }

    if (params["pattern"]) {
        pattern_ = params["pattern"].as<std::string>();
    }

    if (params["validation_type"]) {
        std::string type_str = params["validation_type"].as<std::string>();
        if (type_str == "nhs_number") {
            validation_type_ = ValidationType::NHSNumber;
        } else if (type_str == "uk_postcode") {
            validation_type_ = ValidationType::UKPostcode;
        } else if (type_str == "uk_phone") {
            validation_type_ = ValidationType::UKPhoneNumber;
        } else if (type_str == "pattern") {
            validation_type_ = ValidationType::Pattern;
        }
    }

    if (params["case_sensitive"]) {
        case_sensitive_ = params["case_sensitive"].as<bool>();
    }

    if (params["return_formatted"]) {
        return_formatted_ = params["return_formatted"].as<bool>();
    }

    if (params["format_template"]) {
        format_template_ = params["format_template"].as<std::string>();
    }
}

std::string UnifiedStringTransformation::extract_string_value(const std::any& input) const {
    if (input.type() == typeid(std::string)) {
        return std::any_cast<std::string>(input);
    } else if (input.type() == typeid(const char*)) {
        return std::string(std::any_cast<const char*>(input));
    } else if (input.type() == typeid(int)) {
        return std::to_string(std::any_cast<int>(input));
    } else if (input.type() == typeid(double)) {
        return std::to_string(std::any_cast<double>(input));
    } else if (input.type() == typeid(float)) {
        return std::to_string(std::any_cast<float>(input));
    } else if (input.type() == typeid(long)) {
        return std::to_string(std::any_cast<long>(input));
    } else {
        throw common::TransformationException("Unsupported input type for string transformation", "input_field", "string_transform");
    }
}

TransformationResult UnifiedStringTransformation::format_operation(const std::string& input) {
    TransformationResult result;

    try {
        // If format_template is provided, use it for formatting
        if (!format_template_.empty()) {
            // Simple template replacement - look for {} placeholder
            std::string formatted = format_template_;
            size_t placeholder_pos = formatted.find("{}");
            if (placeholder_pos != std::string::npos) {
                formatted.replace(placeholder_pos, 2, input);
            }
            result.value = formatted;
            result.success = true;
            result.metadata["formatted"] = true;
            return result;
        }

        // Original pattern-based formatting
        if (pattern_.empty()) {
            result.set_error("Pattern or format template is required for format operation");
            return result;
        }

        std::regex pattern_regex(pattern_);
        bool matches = std::regex_match(input, pattern_regex);

        if (matches) {
            result.value = input;
            result.success = true;
            result.metadata["pattern_matched"] = true;
        } else {
            result.value = std::string("");  // Return empty string for non-matching patterns
            result.success = true;
            result.metadata["pattern_matched"] = false;
        }

        return result;
    } catch (const std::regex_error& e) {
        result.set_error(std::format("Invalid regex pattern: {}", e.what()));
        return result;
    }
}

TransformationResult UnifiedStringTransformation::validate_operation(const std::string& input) {
    TransformationResult result;

    bool is_valid = false;
    std::string formatted_value = input;

    switch (validation_type_) {
        case ValidationType::NHSNumber:
            is_valid = common::MedicalUtils::is_valid_nhs_number(input);
            if (is_valid && return_formatted_) {
                // Format NHS number as XXX XXX XXXX
                std::string cleaned = input;
                cleaned.erase(std::remove_if(cleaned.begin(), cleaned.end(), 
                                           [](char c) { return std::isspace(c) || c == '-'; }), 
                            cleaned.end());
                if (cleaned.length() == 10) {
                    formatted_value = cleaned.substr(0, 3) + " " + cleaned.substr(3, 3) + " " + cleaned.substr(6);
                }
            }
            break;
        case ValidationType::UKPostcode:
            is_valid = common::ValidationUtils::is_valid_uk_postcode(input);
            if (is_valid && return_formatted_) {
                // Format UK postcode properly
                std::string cleaned = input;
                // Remove all spaces and non-alphanumeric characters
                cleaned.erase(std::remove_if(cleaned.begin(), cleaned.end(), 
                                           [](char c) { return !std::isalnum(c); }), 
                            cleaned.end());
                
                // Convert to uppercase
                std::transform(cleaned.begin(), cleaned.end(), cleaned.begin(), ::toupper);
                
                // UK postcode format validation and formatting
                if (cleaned.length() >= 5 && cleaned.length() <= 8) {
                    // Handle different postcode lengths
                    if (cleaned.length() == 5) { // e.g., M11AA -> M1 1AA
                        formatted_value = cleaned.substr(0, 2) + " " + cleaned.substr(2);
                    } else if (cleaned.length() == 6) { // e.g., B338TH -> B33 8TH
                        formatted_value = cleaned.substr(0, 3) + " " + cleaned.substr(3);
                    } else if (cleaned.length() == 7) { // e.g., SW1A1AA -> SW1A 1AA, W1A0AX -> W1A 0AX
                        formatted_value = cleaned.substr(0, 4) + " " + cleaned.substr(4);
                    } else if (cleaned.length() == 8) { // e.g., EC1A1BB -> EC1A 1BB
                        formatted_value = cleaned.substr(0, 4) + " " + cleaned.substr(4);
                    }
                } else {
                    formatted_value = cleaned;
                }
            }
            break;
        case ValidationType::UKPhoneNumber:
            is_valid = common::ValidationUtils::is_valid_uk_phone(input);
            if (is_valid && return_formatted_) {
                // Format UK phone number properly
                std::string cleaned = input;
                // Remove all non-digits except +
                std::string clean_phone;
                for (char c : input) {
                    if (std::isdigit(c) || c == '+') {
                        clean_phone += c;
                    }
                }
                
                // UK phone number formatting
                if (clean_phone.length() == 11 && clean_phone[0] == '0') {
                    // Convert 07 to +447 format for mobile
                    if (clean_phone.substr(0, 2) == "07") {
                        formatted_value = "+44" + clean_phone.substr(1);
                    } else if (clean_phone.substr(0, 2) == "02") {
                        // London, Cardiff, etc. (02X XXXX XXXX)
                        formatted_value = "+44" + clean_phone.substr(1);
                    } else if (clean_phone[1] == '1' || clean_phone[1] == '7' || clean_phone[1] == '8' || clean_phone[1] == '9') {
                        // Mobile and special numbers (07XXX XXXXXX)
                        formatted_value = "+44" + clean_phone.substr(1);
                    } else {
                        // Standard geographic (01XXX XXXXXX)
                        formatted_value = "+44" + clean_phone.substr(1);
                    }
                } else if (clean_phone.length() == 10 && clean_phone.substr(0, 2) == "44") {
                    // International format without +
                    formatted_value = "+44" + clean_phone.substr(2);
                } else if (clean_phone.length() == 12 && clean_phone.substr(0, 3) == "+44") {
                    // Already in international format
                    formatted_value = clean_phone;
                } else {
                    formatted_value = clean_phone;
                }
            }
            break;
        case ValidationType::Pattern:
            is_valid = validate_pattern(input);
            break;
        case ValidationType::Custom:
            // Custom validation - for now, accept any non-empty string
            is_valid = !input.empty() && input != "null" && input != "NULL";
            break;
    }

    if (is_valid) {
        result.value = formatted_value;
        result.success = true;
    } else {
        result.value = std::string("");
        result.success = false;
        result.set_error(std::format("Validation failed for input: {}", input));
    }

    result.metadata["validation_type"] = static_cast<int>(validation_type_);
    result.metadata["is_valid"] = is_valid;

    return result;
}



bool UnifiedStringTransformation::validate_pattern(const std::string& input) const {
    if (pattern_.empty()) {
        return false;
    }

    try {
        std::regex pattern_regex(pattern_, case_sensitive_ ? std::regex::ECMAScript : std::regex::icase);
        return std::regex_match(input, pattern_regex);
    } catch (const std::regex_error&) {
        return false;
    }
}



} // namespace omop::transform