#pragma once

/**
 * @file anonymization_transformations.h
 * @brief Anonymization and privacy-preserving transformations for healthcare data
 *
 * This header provides comprehensive anonymization capabilities including:
 * - Irreversible hashing for full anonymization
 * - Date-to-age conversions for temporal anonymization
 * - Geographic region aggregation for location anonymization
 * - Secure pseudonymization for research purposes
 * - NHS-specific anonymization methods
 *
 * All transformations comply with GDPR, NHS Digital guidelines, and UK healthcare
 * data protection requirements.
 */

#include "transformations.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include "common/utils/date_utils.h"
#include "security/auth_manager.h"
#include <openssl/sha.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/hmac.h>
#include <nlohmann/json.hpp>
#include <chrono>
#include <random>
#include <unordered_map>
#include <iomanip>

namespace omop::transform {

/**
 * @brief Anonymization compliance levels
 */
enum class AnonymizationLevel {
    FULLY_ANONYMIZED,        ///< GDPR Recital 26 - cannot be re-identified
    PSEUDONYMIZED,           ///< GDPR Article 4(5) - can be linked but not identified
    MASKED,                  ///< Data is obscured but may contain patterns
    AGGREGATED              ///< Data is grouped to prevent individual identification
};

/**
 * @brief Result of anonymization operation
 */
struct AnonymizationResult {
    std::any anonymized_value;                    ///< The anonymized data
    AnonymizationLevel compliance_level;          ///< Compliance level achieved
    bool contains_personal_data{false};           ///< Whether result contains personal data
    bool is_reversible{false};                    ///< Whether transformation is reversible
    std::string method_used;                      ///< Anonymization method applied
    std::unordered_map<std::string, std::any> metadata; ///< Additional metadata
    std::optional<std::string> audit_log;         ///< Audit log entry
};

/**
 * @brief Base class for all anonymization transformations
 */
class AnonymizationTransformation : public ComplexTransformation {
public:
    explicit AnonymizationTransformation(const std::string& method_name);
    virtual ~AnonymizationTransformation() = default;

    /**
     * @brief Apply anonymization transformation
     * @param input Input value to anonymize
     * @param context Processing context
     * @return TransformationResult Detailed anonymization result
     */
    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    /**
     * @brief Get anonymization compliance level
     * @return AnonymizationLevel Compliance level this transformation achieves
     */
    virtual AnonymizationLevel get_compliance_level() const = 0;

    /**
     * @brief Check if transformation result contains personal data
     * @return bool True if result contains personal data
     */
    virtual bool contains_personal_data() const = 0;

    /**
     * @brief Check if transformation is reversible
     * @return bool True if transformation can be reversed
     */
    virtual bool is_reversible() const = 0;

protected:
    /**
     * @brief Apply specific anonymization logic
     * @param input Input value
     * @param context Processing context
     * @return AnonymizationResult Anonymization result
     */
    virtual AnonymizationResult apply_anonymization(const std::any& input,
                                                   core::ProcessingContext& context) = 0;

    /**
     * @brief Create audit log entry
     * @param input_type Type of input data
     * @param success Whether operation was successful
     * @return std::string Audit log entry
     */
    std::string create_audit_log(const std::string& input_type, bool success) const;

    /**
     * @brief Generate secure random salt
     * @param length Salt length in bytes
     * @return std::string Base64-encoded salt
     */
    std::string generate_secure_salt(size_t length = 32) const;

protected:
    std::string method_name_;
    YAML::Node config_;
    mutable std::mutex transformation_mutex_;
};

/**
 * @brief NHS Number anonymization transformation
 * 
 * Irreversibly anonymizes NHS numbers using cryptographic hashing
 * with secure salts. Compliance level: FULLY_ANONYMIZED
 */
class NHSNumberAnonymizer : public AnonymizationTransformation {
public:
    NHSNumberAnonymizer();
    ~NHSNumberAnonymizer() override = default;

    bool validate_input(const std::any& input) const override;
    std::string get_type() const override { return "nhs_number_anonymizer"; }
    void configure(const YAML::Node& params) override;
    
    AnonymizationLevel get_compliance_level() const override { return AnonymizationLevel::FULLY_ANONYMIZED; }
    bool contains_personal_data() const override { return false; }
    bool is_reversible() const override { return false; }

protected:
    AnonymizationResult apply_anonymization(const std::any& input,
                                           core::ProcessingContext& context) override;

private:
    /**
     * @brief Validate NHS number format
     * @param nhs_number NHS number to validate
     * @return bool True if valid NHS number format
     */
    bool is_valid_nhs_number(const std::string& nhs_number) const;

    /**
     * @brief Generate irreversible hash of NHS number
     * @param nhs_number NHS number to hash
     * @return std::string Secure hash
     */
    std::string generate_secure_hash(const std::string& nhs_number) const;

private:
    std::string global_salt_;
    std::string study_specific_salt_;
    size_t hash_iterations_;
    bool use_time_based_salt_;
};

/**
 * @brief Date to Age anonymization transformation
 * 
 * Converts birth dates and other temporal data to ages in years and months.
 * Removes exact temporal information while preserving analytical value.
 * Compliance level: FULLY_ANONYMIZED
 */
class DateToAgeAnonymizer : public AnonymizationTransformation {
public:
    DateToAgeAnonymizer();
    ~DateToAgeAnonymizer() override = default;

    bool validate_input(const std::any& input) const override;
    std::string get_type() const override { return "date_to_age_anonymizer"; }
    void configure(const YAML::Node& params) override;
    
    AnonymizationLevel get_compliance_level() const override { return AnonymizationLevel::FULLY_ANONYMIZED; }
    bool contains_personal_data() const override { return false; }
    bool is_reversible() const override { return false; }

protected:
    AnonymizationResult apply_anonymization(const std::any& input,
                                           core::ProcessingContext& context) override;

private:

    /**
     * @brief Parse date string with UK formats
     * @param date_str Date string
     * @return std::chrono::system_clock::time_point Parsed date
     */
    std::chrono::system_clock::time_point parse_uk_date(const std::string& date_str) const;

private:
    std::vector<std::string> supported_formats_;
    std::chrono::system_clock::time_point reference_date_;
    bool use_current_date_as_reference_;
    int minimum_age_years_;
    int maximum_age_years_;
};

/**
 * @brief UK Postcode to Region anonymization transformation
 * 
 * Converts specific UK postcodes to broader geographic regions to prevent
 * individual identification while preserving regional analytical value.
 * Compliance level: FULLY_ANONYMIZED
 */
class PostcodeToRegionAnonymizer : public AnonymizationTransformation {
public:
    PostcodeToRegionAnonymizer();
    ~PostcodeToRegionAnonymizer() override = default;

    bool validate_input(const std::any& input) const override;
    std::string get_type() const override { return "postcode_to_region_anonymizer"; }
    void configure(const YAML::Node& params) override;
    
    AnonymizationLevel get_compliance_level() const override { return AnonymizationLevel::FULLY_ANONYMIZED; }
    bool contains_personal_data() const override { return false; }
    bool is_reversible() const override { return false; }

protected:
    AnonymizationResult apply_anonymization(const std::any& input,
                                           core::ProcessingContext& context) override;

private:
    /**
     * @brief UK region information
     */
    struct UKRegion {
        std::string region_code;     ///< Anonymous region code (e.g., "R001")
        std::string region_name;     ///< Region name (e.g., "South West England")
        std::string nhs_region;      ///< NHS administrative region
        size_t population_size;      ///< Approximate population for k-anonymity
    };

    /**
     * @brief Initialize UK postcode to region mapping
     */
    void initialize_region_mapping();

    /**
     * @brief Map postcode to region with k-anonymity
     * @param postcode UK postcode
     * @return UKRegion Anonymized region
     */
    UKRegion map_postcode_to_region(const std::string& postcode) const;

    /**
     * @brief Validate UK postcode format
     * @param postcode Postcode to validate
     * @return bool True if valid UK postcode format
     */
    bool is_valid_uk_postcode(const std::string& postcode) const;

private:
    std::unordered_map<std::string, UKRegion> region_mapping_;
    size_t minimum_population_size_;
    bool use_nhs_regions_;
    std::regex uk_postcode_regex_;
};

/**
 * @brief Generic field anonymization using secure hashing
 * 
 * Provides secure hashing for any field type that needs full anonymization.
 * Uses cryptographically secure methods with salts and iterations.
 * Compliance level: FULLY_ANONYMIZED
 */
class SecureHashAnonymizer : public AnonymizationTransformation {
public:
    SecureHashAnonymizer();
    ~SecureHashAnonymizer() override = default;

    bool validate_input(const std::any& input) const override;
    std::string get_type() const override { return "secure_hash_anonymizer"; }
    void configure(const YAML::Node& params) override;
    
    AnonymizationLevel get_compliance_level() const override { return AnonymizationLevel::FULLY_ANONYMIZED; }
    bool contains_personal_data() const override { return false; }
    bool is_reversible() const override { return false; }

protected:
    AnonymizationResult apply_anonymization(const std::any& input,
                                           core::ProcessingContext& context) override;

private:
    /**
     * @brief Convert any value to string for hashing
     * @param input Input value of any type
     * @return std::string String representation
     */
    std::string convert_to_string(const std::any& input) const;

    /**
     * @brief Generate cryptographically secure hash
     * @param input Input string
     * @return std::string Secure hash (hex encoded)
     */
    std::string generate_pbkdf2_hash(const std::string& input) const;

private:
    std::string algorithm_;        ///< Hash algorithm (SHA256, SHA512)
    std::string global_salt_;      ///< Global salt for all operations
    std::string field_specific_salt_; ///< Field-specific salt
    size_t iterations_;            ///< Number of hash iterations
    size_t output_length_;         ///< Output hash length in bytes
    bool include_timestamp_salt_;  ///< Whether to include timestamp in salt
};

/**
 * @brief Research pseudonymization transformation
 * 
 * Creates consistent pseudonyms for research purposes that can link records
 * for the same individual across datasets without revealing identity.
 * Compliance level: PSEUDONYMIZED
 */
class ResearchPseudonymizer : public AnonymizationTransformation {
public:
    ResearchPseudonymizer();
    ~ResearchPseudonymizer() override = default;

    bool validate_input(const std::any& input) const override;
    std::string get_type() const override { return "research_pseudonymizer"; }
    void configure(const YAML::Node& params) override;
    
    AnonymizationLevel get_compliance_level() const override { return AnonymizationLevel::PSEUDONYMIZED; }
    bool contains_personal_data() const override { return false; }
    bool is_reversible() const override { return false; } // Cannot be reversed without key

protected:
    AnonymizationResult apply_anonymization(const std::any& input,
                                           core::ProcessingContext& context) override;

private:
    /**
     * @brief Generate consistent pseudonym for research
     * @param identifier Source identifier
     * @return std::string Research pseudonym
     */
    std::string generate_research_pseudonym(const std::string& identifier) const;

    /**
     * @brief Generate study-specific pseudonym
     * @param identifier Source identifier
     * @param study_id Study identifier
     * @return std::string Study-specific pseudonym
     */
    std::string generate_study_pseudonym(const std::string& identifier,
                                       const std::string& study_id) const;

private:
    std::string study_id_;
    std::string research_key_;
    std::string pseudonym_format_;
    size_t pseudonym_length_;
    bool time_limited_;
    std::chrono::hours pseudonym_expiry_;
};

/**
 * @brief Composite anonymization transformation
 * 
 * Combines multiple anonymization methods for complex data structures.
 * Allows field-specific anonymization within records.
 */
class CompositeAnonymizer : public AnonymizationTransformation {
public:
    CompositeAnonymizer();
    ~CompositeAnonymizer() override = default;

    bool validate_input(const std::any& input) const override;
    std::string get_type() const override { return "composite_anonymizer"; }
    void configure(const YAML::Node& params) override;
    
    AnonymizationLevel get_compliance_level() const override;
    bool contains_personal_data() const override;
    bool is_reversible() const override;

    /**
     * @brief Add field-specific anonymization rule
     * @param field_name Field name
     * @param anonymizer Anonymization transformation
     */
    void add_field_anonymizer(const std::string& field_name,
                            std::unique_ptr<AnonymizationTransformation> anonymizer);

protected:
    AnonymizationResult apply_anonymization(const std::any& input,
                                           core::ProcessingContext& context) override;

private:
    std::unordered_map<std::string, std::unique_ptr<AnonymizationTransformation>> field_anonymizers_;
    AnonymizationLevel overall_compliance_level_;
    bool overall_contains_personal_data_;
    bool overall_is_reversible_;
};

/**
 * @brief Factory for creating anonymization transformations
 */
class AnonymizationTransformationFactory {
public:
    /**
     * @brief Create anonymization transformation by type
     * @param type Anonymization type
     * @param config Configuration parameters
     * @return std::unique_ptr<AnonymizationTransformation> Created transformation
     */
    static std::unique_ptr<AnonymizationTransformation> create(
        const std::string& type,
        const YAML::Node& config = YAML::Node{});

    /**
     * @brief Register all anonymization transformations with global registry
     */
    static void register_all_transformations();

    /**
     * @brief Get available anonymization transformation types
     * @return std::vector<std::string> Available types
     */
    static std::vector<std::string> get_available_types();
};

} // namespace omop::transform