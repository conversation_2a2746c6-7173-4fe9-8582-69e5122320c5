#pragma once

#include "core/interfaces.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include "transform/transformations.h"
#include "transform/transformation_result.h"
#include "transform/date_transformations.h"
#include "transform/numeric_transformations.h"
#include "transform/conditional_transformations.h"
#include "transform/anonymization_transformations.h"
#include "vocabulary_service.h"
#include <memory>
#include <string>
#include <unordered_map>
#include <functional>
#include <regex>
#include <chrono>
#include <any>
#include <optional>
#include <vector>
#include <yaml-cpp/yaml.h>
#include "common/validation.h"

namespace omop::transform {

// Base classes are now defined in transformations.h

/**
 * @brief Direct field mapping (no transformation)
 */
class DirectTransformation : public FieldTransformation {
public:
    std::any transform(const std::any& input,
                      [[maybe_unused]] core::ProcessingContext& context) override {
        return input;
    }

    bool validate_input(const std::any& input) const override {
        return input.has_value();
    }

    std::string get_type() const override { return "direct"; }

    void configure([[maybe_unused]] const YAML::Node& params) override {
        // No configuration needed for direct mapping
    }
};

/**
 * @brief Date value transformation
 *
 * Handles date parsing, formatting, and validation.
 */
class DateTransformation : public FieldTransformation {
public:
    /**
     * @brief Constructor
     */
    DateTransformation() = default;

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    TransformationResult transform_safe(const std::any& input,
                                       core::ProcessingContext& context) override {
        TransformationResult result;
        try {
            result.value = transform(input, context);
            result.success = true;
        } catch (const std::exception& e) {
            result.set_error(std::format("Date transformation failed: {}", e.what()));
        }
        return result;
    }

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "date_transform"; }

    void configure(const YAML::Node& params) override;

private:
    std::string input_format_{"%Y-%m-%d"};
    std::vector<std::string> formats_{"%Y-%m-%d"};
    std::string output_format_{"%Y-%m-%d"};
    std::string timezone_{"UTC"};
    bool add_time_{false};
    std::string default_time_{"00:00:00"};
};

/**
 * @brief Vocabulary mapping transformation
 *
 * Maps source values to OMOP concept IDs using vocabulary lookups.
 */
class VocabularyTransformation : public FieldTransformation {
public:
    /**
     * @brief Constructor
     * @param vocabulary_service Reference to vocabulary service
     */
    explicit VocabularyTransformation(class VocabularyService& vocabulary_service);

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "vocabulary_mapping"; }

    void configure(const YAML::Node& params) override;

private:
    VocabularyService& vocabulary_service_;
    std::string vocabulary_name_;
    std::string source_vocabulary_;
    std::string target_vocabulary_{"OMOP"};
    int default_concept_id_{0};
    bool case_sensitive_{false};
    bool allow_circular_{true};
};

/**
 * @brief Numeric value transformation
 *
 * Handles numeric conversions, unit conversions, and calculations.
 */
class NumericTransformation : public FieldTransformation {
public:
    enum class Operation {
        None,
        Multiply,
        Divide,
        Add,
        Subtract,
        Round,
        Floor,
        Ceiling,
        Absolute
    };

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    TransformationResult transform_safe(const std::any& input,
                                       core::ProcessingContext& context) override {
        TransformationResult result;
        try {
            result.value = transform(input, context);
            result.success = true;
        } catch (const std::exception& e) {
            result.set_error(std::format("Numeric transformation failed: {}", e.what()));
            context.increment_errors();
        }
        return result;
    }

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "numeric_transform"; }

    void configure(const YAML::Node& params) override;

private:
    Operation operation_{Operation::None};
    double operand_{1.0};
    int precision_{2};
    std::optional<double> min_value_;
    std::optional<double> max_value_;
    std::string unit_conversion_;
};

/**
 * @brief String concatenation transformation
 *
 * Combines multiple fields into a single string value.
 */
class StringConcatenationTransformation : public FieldTransformation {
public:
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "string_concatenation"; }

    void configure(const YAML::Node& params) override;

    /**
     * @brief Set source fields for concatenation
     * @param fields Vector of field names
     */
    void set_source_fields(const std::vector<std::string>& fields) {
        source_fields_ = fields;
    }

    /**
     * @brief Transform multiple values
     * @param values Map of field names to values
     * @param context Processing context
     * @return std::any Concatenated string
     */
    std::any transform_multiple(const std::unordered_map<std::string, std::any>& values,
                               core::ProcessingContext& context);

private:
    std::vector<std::string> source_fields_;
    std::string separator_{" "};
    bool skip_empty_{true};
    bool trim_whitespace_{false};
    std::string prefix_;
    std::string suffix_;
};

/**
 * @brief Conditional transformation based on rules
 *
 * Applies different transformations based on conditions.
 */
class ConditionalTransformation : public FieldTransformation {
public:
    struct Condition {
        std::string field;
        std::string operator_type;
        std::any value;
        std::string then_value;
        std::optional<std::string> else_value;
    };

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "conditional"; }

    void configure(const YAML::Node& params) override;

private:
    std::vector<Condition> conditions_;
    std::string default_value_;

    bool evaluate_condition(const Condition& condition,
                          const std::any& value) const;
};

/**
 * @brief Main transformation engine
 *
 * This class orchestrates the transformation process, managing field
 * transformations and applying business rules to convert source data
 * to OMOP CDM format.
 */
class TransformationEngine : public core::ITransformer {
public:
    /**
     * @brief Constructor
     */
    TransformationEngine();

    /**
     * @brief Initialize the transformer
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Transform a single record
     * @param record Record to transform
     * @param context Processing context
     * @return std::optional<core::Record> Transformed record
     */
    std::optional<core::Record> transform(const core::Record& record,
                                         core::ProcessingContext& context) override;

    /**
     * @brief Transform a batch of records
     * @param batch Batch to transform
     * @param context Processing context
     * @return core::RecordBatch Transformed batch
     */
    core::RecordBatch transform_batch(const core::RecordBatch& batch,
                                     core::ProcessingContext& context) override;

    /**
     * @brief Get transformer type name
     * @return std::string Transformer type identifier
     */
    std::string get_type() const override { return "omop_transformation_engine"; }

    /**
     * @brief Validate a record
     * @param record Record to validate
     * @return omop::common::ValidationResult Validation result
     */
    omop::common::ValidationResult validate(const core::Record& record) const override;

    /**
     * @brief Get transformation statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Register custom transformation
     * @param type Transformation type name
     * @param factory Factory function
     */
    void register_transformation(const std::string& type,
        std::function<std::unique_ptr<FieldTransformation>()> factory);

    /**
     * @brief Enable/disable mandatory anonymization
     * @param enabled True to make anonymization mandatory
     */
    void set_anonymization_mandatory(bool enabled) { anonymization_mandatory_ = enabled; }

    /**
     * @brief Check if anonymization is mandatory
     * @return bool True if anonymization is mandatory
     */
    bool is_anonymization_mandatory() const { return anonymization_mandatory_; }

    /**
     * @brief Set anonymization configuration
     * @param config Anonymization configuration YAML node
     */
    void set_anonymization_config(const YAML::Node& config) { anonymization_config_ = config; }

#ifdef UNIT_TESTING
    /**
     * @brief Set current mapping (for testing only)
     * @param mapping Table mapping to set
     */
    void set_current_mapping_for_test(const common::TableMapping& mapping) {
        current_mapping_ = mapping;
    }
#endif

private:
    /**
     * @brief Apply transformation rules to a record
     * @param record Source record
     * @param rules Transformation rules
     * @param context Processing context
     * @return core::Record Transformed record
     */
    core::Record apply_transformations(const core::Record& record,
                                      const std::vector<common::TransformationRule>& rules,
                                      core::ProcessingContext& context);

    /**
     * @brief Create field transformation from rule
     * @param rule Transformation rule
     * @return std::unique_ptr<FieldTransformation> Field transformation
     */
    std::unique_ptr<FieldTransformation> create_transformation(
        const common::TransformationRule& rule);

    /**
     * @brief Apply filters to record
     * @param record Record to filter
     * @param filters Filter conditions
     * @return bool True if record passes filters
     */
    bool apply_filters(const core::Record& record,
                      const YAML::Node& filters) const;

    /**
     * @brief Apply custom validations from configuration
     * @param record Record to validate
     * @param validations YAML node with validation rules
     * @return omop::common::ValidationResult Validation result
     */
    omop::common::ValidationResult apply_validations(const core::Record& record,
                                                    const YAML::Node& validations) const;

    // Configuration
    common::TableMapping current_mapping_;
    std::unique_ptr<class VocabularyService> vocabulary_service_;

    // Transformation factories
    std::unordered_map<std::string,
        std::function<std::unique_ptr<FieldTransformation>()>> transformation_factories_;

    // Statistics
    mutable std::atomic<size_t> records_transformed_{0};
    mutable std::atomic<size_t> records_filtered_{0};
    mutable std::atomic<size_t> validation_errors_{0};
    mutable std::atomic<size_t> transformation_errors_{0};

    // Performance metrics
    mutable std::chrono::duration<double> total_transform_time_{0};
    mutable std::mutex stats_mutex_;
    
    // Anonymization settings
    bool anonymization_mandatory_{true};  // Default to mandatory for healthcare compliance
    YAML::Node anonymization_config_;
    std::unique_ptr<CompositeAnonymizer> record_anonymizer_;
};

/**
 * @brief Factory for creating transformation engines
 */
class TransformationEngineFactory {
public:
    /**
     * @brief Create transformation engine for table
     * @param table_name OMOP table name
     * @param config Configuration manager
     * @return std::unique_ptr<TransformationEngine> Transformation engine
     */
    static std::unique_ptr<TransformationEngine> create_for_table(
        const std::string& table_name,
        const common::ConfigurationManager& config);

    /**
     * @brief Register custom transformation engine
     * @param table_name Table name
     * @param factory Factory function
     */
    static void register_engine(const std::string& table_name,
        std::function<std::unique_ptr<TransformationEngine>()> factory);

private:
    static std::unordered_map<std::string,
        std::function<std::unique_ptr<TransformationEngine>()>> engine_factories_;
};

} // namespace omop::transform