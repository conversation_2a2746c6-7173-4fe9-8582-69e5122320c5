#pragma once

/**
 * @file validation_engine.h
 * @brief Validation engine interface and implementations
 *
 * Provides a framework for validating transformed data against OMOP CDM
 * requirements and custom business rules.
 */

#include "core/interfaces.h"
#include "common/exceptions.h"
#include "common/validation.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <any>
#include <optional>
#include <yaml-cpp/yaml.h>
#include <regex>

namespace omop::transform {

/**
 * @brief Abstract interface for implementing field-level validation logic.
 */
class IFieldValidator {
public:
    virtual ~IFieldValidator() = default;

    /**
     * @brief Validate a field value
     * @param value Field value to validate
     * @param context Processing context
     * @return omop::common::ValidationResult Result with any errors/warnings
     */
    virtual omop::common::ValidationResult validate(const std::any& value,
                                                   core::ProcessingContext& context) = 0;

    /**
     * @brief Get validator type name
     * @return std::string Type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Configure validator with parameters
     * @param params Configuration parameters
     */
    virtual void configure(const YAML::Node& params) = 0;

    /**
     * @brief Get field name this validator applies to
     * @return std::string Field name
     */
    virtual const std::string& get_field_name() const = 0;

    /**
     * @brief Set field name
     * @param name Field name
     */
    virtual void set_field_name(const std::string& name) = 0;
};

/**
 * @brief Validation engine interface
 *
 * Manages field validators and applies validation rules to records.
 */
class IValidationEngine {
public:
    virtual ~IValidationEngine() = default;

    /**
     * @brief Load validation rules from configuration
     * @param config Validation configuration
     */
    virtual void load_validation_rules(const YAML::Node& config) = 0;

    /**
     * @brief Validate a record
     * @param record Record to validate
     * @param context Processing context
     * @return omop::common::ValidationResult Validation result
     */
    virtual omop::common::ValidationResult validate_record(const core::Record& record,
                                                         core::ProcessingContext& context) = 0;

    /**
     * @brief Validate a single field
     * @param field_name Field name
     * @param value Field value
     * @param context Processing context
     * @return omop::common::ValidationResult Validation result
     */
    virtual omop::common::ValidationResult validate_field(const std::string& field_name,
                                                        const std::any& value,
                                                        core::ProcessingContext& context) = 0;

    /**
     * @brief Register a custom validator
     * @param type Validator type name
     * @param factory Factory function
     */
    virtual void register_validator(const std::string& type,
                                  std::function<std::unique_ptr<IFieldValidator>()> factory) = 0;

    /**
     * @brief Get registered validator types
     * @return std::vector<std::string> List of type names
     */
    virtual std::vector<std::string> get_registered_types() const = 0;

    /**
     * @brief Clear all validation rules
     */
    virtual void clear_rules() = 0;

    /**
     * @brief Load configuration from YAML
     * @param config YAML configuration
     */
    virtual void load_configuration(const YAML::Node& config) = 0;

    /**
     * @brief Validate a batch of records
     * @param records Records to validate
     * @param context Processing context  
     * @return std::vector<omop::common::ValidationResult> Validation results
     */
    virtual std::vector<omop::common::ValidationResult> validate_batch(
        const std::vector<core::Record>& records,
        core::ProcessingContext& context) = 0;
};

/**
 * @brief Factory function for creating validation engine
 * @return std::unique_ptr<IValidationEngine> Validation engine instance
 */
std::unique_ptr<IValidationEngine> create_validation_engine();

/**
 * @brief Required field validator implementation
 */
class RequiredFieldValidator : public IFieldValidator {
public:
    omop::common::ValidationResult validate(const std::any& value, core::ProcessingContext& context) override;
    std::string get_type() const override { return "required"; }
    void configure(const YAML::Node& params) override;
    const std::string& get_field_name() const override { return field_name_; }
    void set_field_name(const std::string& name) override { field_name_ = name; }
private:
    bool allow_empty_string_{false};
    std::string field_name_;
    int min_length_{0};
    int max_length_{0};
    std::string pattern_;
    std::string expected_type_;
};

/**
 * @brief Data type validator implementation
 */
class DataTypeValidator : public IFieldValidator {
public:
    enum class DataType {
        String,
        Integer,
        Float,
        Boolean,
        Date,
        DateTime,
        Time
    };

    omop::common::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override;

    std::string get_type() const override { return "data_type"; }

    void configure(const YAML::Node& params) override;

    const std::string& get_field_name() const override { return field_name_; }

    void set_field_name(const std::string& name) override { field_name_ = name; }

private:
    void configure_type(const std::string& type_str);
    std::string get_type_name(DataType type) const;
    std::string get_actual_type(const std::any& value) const;
    bool validate_date_format(const std::string& date_str);

    DataType expected_type_{DataType::String};
    bool required_{false};
    std::vector<std::string> date_formats_;
    std::string field_name_;
};

/**
 * @brief Range validator for numeric values
 */
class RangeValidator : public IFieldValidator {
public:
    omop::common::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override;

    std::string get_type() const override { return "range"; }

    void configure(const YAML::Node& params) override;

    const std::string& get_field_name() const override { return field_name_; }

    void set_field_name(const std::string& name) override { field_name_ = name; }

private:
    std::optional<double> min_value_;
    std::optional<double> max_value_;
    std::vector<double> allowed_values_;
    std::string field_name_;
};

/**
 * @brief Pattern validator for string values
 */
class PatternValidator : public IFieldValidator {
public:
    omop::common::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override;

    std::string get_type() const override { return "pattern"; }

    void configure(const YAML::Node& params) override;

    const std::string& get_field_name() const override { return field_name_; }

    void set_field_name(const std::string& name) override { field_name_ = name; }

private:
    std::string pattern_;
    bool case_sensitive_{false};
    std::regex regex_pattern_;
    std::string field_name_;
};

/**
 * @brief Length validator for string values
 */
class LengthValidator : public IFieldValidator {
public:
    omop::common::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override;

    std::string get_type() const override { return "length"; }

    void configure(const YAML::Node& params) override;

    const std::string& get_field_name() const override { return field_name_; }

    void set_field_name(const std::string& name) override { field_name_ = name; }

private:
    std::optional<size_t> min_length_;
    std::optional<size_t> max_length_;
    std::optional<size_t> exact_length_;
    std::string field_name_;
};

/**
 * @brief Vocabulary field validator (consolidated with VocabularyValidator)
 */
class VocabularyFieldValidator : public IFieldValidator {
public:
    omop::common::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override;

    std::string get_type() const override { return "vocabulary"; }

    void configure(const YAML::Node& params) override;

    const std::string& get_field_name() const override { return field_name_; }

    void set_field_name(const std::string& name) override { field_name_ = name; }

private:
    std::string vocabulary_name_;
    std::optional<std::string> expected_domain_;
    bool validate_concept_id_{false};
    bool require_standard_{false};
    std::string field_name_;
};

/**
 * @brief Cross-field validator
 */
class CrossFieldValidator : public IFieldValidator {
public:
    omop::common::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override;

    std::string get_type() const override { return "cross_field"; }

    void configure(const YAML::Node& params) override;

    const std::string& get_field_name() const override { return field_name_; }

    void set_field_name(const std::string& name) override { field_name_ = name; }

private:
    std::vector<std::string> related_fields_;
    std::string condition_;
    std::string field_name_;
    
    bool evaluate_condition(const std::string& condition,
                          const core::Record& record,
                          const std::string& current_field,
                          const std::any& current_value) const;
    bool compare_field_values(const std::any& left_value, 
                            const std::any& right_value, 
                            const std::string& op) const;
};

/**
 * @brief Custom validator that uses a transformation function
 */
class CustomValidator : public IFieldValidator {
public:
    using ValidationFunction = std::function<bool(const std::any&, core::ProcessingContext&)>;

    omop::common::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override;

    std::string get_type() const override { return "custom"; }

    void configure(const YAML::Node& params) override;

    const std::string& get_field_name() const override { return field_name_; }

    void set_field_name(const std::string& name) override { field_name_ = name; }

    /**
     * @brief Set validation function
     * @param func Validation function
     */
    void set_validation_function(ValidationFunction func) {
        validation_func_ = std::move(func);
    }

private:
    ValidationFunction validation_func_;
    std::string error_message_;
    std::string field_name_;
};

/**
 * @brief Validation engine implementation
 */
class ValidationEngine : public IValidationEngine {
public:
    ValidationEngine();
    
    void load_validation_rules(const YAML::Node& config) override;

    omop::common::ValidationResult validate_record(const core::Record& record,
                                         core::ProcessingContext& context) override;

    omop::common::ValidationResult validate_field(const std::string& field_name,
                                        const std::any& value,
                                        core::ProcessingContext& context) override;

    void register_validator(const std::string& type,
                          std::function<std::unique_ptr<IFieldValidator>()> factory) override;

    std::vector<std::string> get_registered_types() const override;

    void clear_rules() override;

    void load_configuration(const YAML::Node& config) override;

    std::vector<omop::common::ValidationResult> validate_batch(
        const std::vector<core::Record>& records,
        core::ProcessingContext& context) override;

private:
    std::unique_ptr<IFieldValidator> create_validator(const std::string& type);
    
    std::unordered_map<std::string, std::vector<std::unique_ptr<IFieldValidator>>> field_validators_;
    std::unordered_map<std::string, std::function<std::unique_ptr<IFieldValidator>()>> validator_factories_;
    mutable std::mutex mutex_;
};

} // namespace omop::transform 