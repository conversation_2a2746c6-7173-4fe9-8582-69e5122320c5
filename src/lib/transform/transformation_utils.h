#pragma once

/**
 * @file transformation_utils.h
 * @brief Header for transformation utilities and registry
 *
 * This header provides the TransformationUtils class with common utility functions
 * for transformations, including date parsing, unit conversion, validation, and more.
 */

#include "common/exceptions.h"
#include "common/logging.h"
#include "common/utilities.h"
#include "core/interfaces.h"
#include <yaml-cpp/yaml.h>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <optional>
#include <regex>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <cmath>
#include <mutex>
#include <functional>

namespace omop::transform {

/**
 * @brief Common transformation utilities
 */
class TransformationUtils {
public:
    /**
     * @brief Parse date string with multiple format attempts
     * @param date_str Date string to parse
     * @param formats Vector of format strings to try
     * @return std::optional<std::chrono::system_clock::time_point> Parsed date
     */
    static std::optional<std::chrono::system_clock::time_point> parse_date(
        const std::string& date_str,
        const std::vector<std::string>& formats);

    /**
     * @brief Format date to string
     * @param time_point Time point to format
     * @param format Format string
     * @return std::string Formatted date string
     */
    static std::string format_date(
        const std::chrono::system_clock::time_point& time_point,
        const std::string& format);

    /**
     * @brief Convert numeric value with unit conversion
     * @param value Input value
     * @param from_unit Source unit
     * @param to_unit Target unit
     * @return double Converted value
     */
    static double convert_units(
        double value,
        const std::string& from_unit,
        const std::string& to_unit);

    /**
     * @brief Validate numeric range
     * @param value Value to validate
     * @param min_value Minimum allowed value
     * @param max_value Maximum allowed value
     * @return bool True if within range
     */
    static bool validate_numeric_range(
        double value,
        std::optional<double> min_value,
        std::optional<double> max_value);

    /**
     * @brief Extract numeric value from string
     * @param str String containing numeric value
     * @param default_value Default if extraction fails
     * @return double Extracted value
     */
    static double extract_numeric(
        const std::string& str,
        double default_value = 0.0);

    /**
     * @brief Calculate age from birthdate
     * @param birthdate Birth date
     * @param reference_date Reference date (default: now)
     * @return int Age in years
     */
    static int calculate_age(
        const std::chrono::system_clock::time_point& birthdate,
        const std::chrono::system_clock::time_point& reference_date =
            std::chrono::system_clock::now());

    /**
     * @brief Normalize string with case and trim options
     * @param str String to normalize
     * @param case_sensitive Whether to preserve case
     * @param trim_whitespace Whether to trim whitespace
     * @return std::string Normalized string
     */
    static std::string normalize_string(const std::string& str, bool case_sensitive = true, bool trim_whitespace = true);

    /**
     * @brief Calculate date difference
     * @param start_date Start date
     * @param end_date End date
     * @param unit Unit of difference (days, months, years)
     * @return int Difference in specified unit
     */
    static int calculate_date_difference(
        const std::chrono::system_clock::time_point& start_date,
        const std::chrono::system_clock::time_point& end_date,
        const std::string& unit = "days");

    
    /**
     * @brief Check if unit is a temperature unit
     * @param unit Unit string to check
     * @return bool True if temperature unit
     */
    static bool is_temperature_unit(const std::string& unit);
    
    /**
     * @brief Check if term is a medical term
     * @param term Term to check
     * @return bool True if medical term
     */
    static bool is_medical_term(const std::string& term);
    
    /**
     * @brief Check if concept is in specified domain
     * @param concept_id Concept ID to check
     * @param domain_id Domain ID to check
     * @return bool True if concept is in domain
     */
    static bool is_in_domain(int concept_id, const std::string& domain_id);
    
    /**
     * @brief Validate vocabulary tables exist
     * @return bool True if all required tables exist
     */
    static bool validate_vocabulary_tables();
    
    /**
     * @brief Validate concept ID is valid
     * @param concept_id Concept ID to validate
     * @param expected_domain Optional expected domain
     * @return bool True if valid concept ID
     */
    static bool validate_concept_id(int concept_id, const std::optional<std::string>& expected_domain = std::nullopt);
    
    /**
     * @brief Validate mapping exists for source value
     * @param source_value Source value to check
     * @param vocabulary_name Vocabulary name
     * @return bool True if mapping exists
     */
    static bool validate_mapping_exists(const std::string& source_value, const std::string& vocabulary_name);
    
    /**
     * @brief Validate concept is standard
     * @param concept_id Concept ID to validate
     * @return bool True if standard concept
     */
    static bool validate_standard_concept(int concept_id);
    
    /**
     * @brief Check if auto-learn is enabled
     * @return bool True if auto-learn is enabled
     */
    static bool is_auto_learn_enabled();
    
    /**
     * @brief Check if vocabulary service is initialised
     * @return bool True if initialised
     */
    static bool is_vocabulary_initialised();

    // ===== CONCEPT HIERARCHY FUNCTIONS =====
    
    /**
     * @brief Check if concept is a leaf node (no descendants)
     * @param concept_id Concept ID to check
     * @return bool True if leaf concept
     */
    static bool is_leaf_concept(int concept_id);
    
    /**
     * @brief Check if concept is a root node (no ancestors)
     * @param concept_id Concept ID to check
     * @return bool True if root concept
     */
    static bool is_root_concept(int concept_id);
    
    /**
     * @brief Check if concept is an ancestor of another concept
     * @param ancestor_id Potential ancestor concept ID
     * @param descendant_id Potential descendant concept ID
     * @return bool True if ancestor relationship exists
     */
    static bool is_ancestor_concept(int ancestor_id, int descendant_id);
    
    /**
     * @brief Check if concept is a descendant of another concept
     * @param descendant_id Potential descendant concept ID
     * @param ancestor_id Potential ancestor concept ID
     * @return bool True if descendant relationship exists
     */
    static bool is_descendant_concept(int descendant_id, int ancestor_id);
    
    // ===== FIELD VALIDATION FUNCTIONS =====
    
    /**
     * @brief Check if value is required and present
     * @param value Value to check
     * @param required Whether field is required
     * @return bool True if value meets requirement
     */
    static bool is_required_field_valid(const std::any& value, bool required = true);
    
    /**
     * @brief Check if value is optional and valid
     * @param value Value to check
     * @return bool True if value is valid for optional field
     */
    static bool is_optional_field_valid(const std::any& value);
    
    /**
     * @brief Check if value is mandatory and valid
     * @param value Value to check
     * @return bool True if value is valid for mandatory field
     */
    static bool is_mandatory_field_valid(const std::any& value);
    
    /**
     * @brief Check if value is primary and valid
     * @param value Value to check
     * @return bool True if value is valid for primary field
     */
    static bool is_primary_field_valid(const std::any& value);
    
    /**
     * @brief Check if value is secondary and valid
     * @param value Value to check
     * @return bool True if value is valid for secondary field
     */
    static bool is_secondary_field_valid(const std::any& value);
    
    // ===== STATUS FUNCTIONS =====
    
    /**
     * @brief Check if value represents an active status
     * @param value Value to check
     * @return bool True if active status
     */
    static bool is_active_status(const std::any& value);
    
    /**
     * @brief Check if value represents an inactive status
     * @param value Value to check
     * @return bool True if inactive status
     */
    static bool is_inactive_status(const std::any& value);
    
    /**
     * @brief Check if value represents an enabled status
     * @param value Value to check
     * @return bool True if enabled status
     */
    static bool is_enabled_status(const std::any& value);
    
    /**
     * @brief Check if value represents a disabled status
     * @param value Value to check
     * @return bool True if disabled status
     */
    static bool is_disabled_status(const std::any& value);
    
    /**
     * @brief Check if value represents a visible status
     * @param value Value to check
     * @return bool True if visible status
     */
    static bool is_visible_status(const std::any& value);
    
    /**
     * @brief Check if value represents a hidden status
     * @param value Value to check
     * @return bool True if hidden status
     */
    static bool is_hidden_status(const std::any& value);
    
    /**
     * @brief Check if value represents an editable status
     * @param value Value to check
     * @return bool True if editable status
     */
    static bool is_editable_status(const std::any& value);
    
    /**
     * @brief Check if value represents a readonly status
     * @param value Value to check
     * @return bool True if readonly status
     */
    static bool is_readonly_status(const std::any& value);



private:
    static std::unordered_map<std::string, double> unit_conversion_factors_;
    static std::once_flag unit_init_flag_;

    /**
     * @brief Initialize unit conversion factors (lazy initialization)
     */
    static void initialize_unit_conversion_factors();

    /**
     * @brief Convert temperature with proper offset calculations
     * @param value Temperature value
     * @param from_unit Source temperature unit
     * @param to_unit Target temperature unit
     * @return double Converted temperature value
     */
    static double convert_temperature(double value, const std::string& from_unit, const std::string& to_unit);
};

} // namespace omop::transform 