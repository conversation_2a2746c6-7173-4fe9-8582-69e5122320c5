#pragma once

/**
 * @file transformations.h
 * @brief Common header for all transformation types in the OMOP ETL pipeline
 *
 * This header provides a centralised include point for all transformation
 * classes and utilities used in the transform module.
 */

#include "common/exceptions.h"
#include "common/logging.h"
#include "core/interfaces.h"
#include "transformation_utils.h"
#include <yaml-cpp/yaml.h>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <optional>
#include <regex>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <cmath>
#include <mutex>
#include <functional>
#include "transform/transformation_result.h"

namespace omop::transform {

/**
 * @brief Base class for field transformations
 *
 * This abstract class provides the interface for all field-level transformations
 * in the ETL pipeline. Concrete implementations handle specific transformation types.
 */
class FieldTransformation {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~FieldTransformation() = default;

    /**
     * @brief Apply transformation to a field value
     * @param input Input value
     * @param context Processing context
     * @return TransformationResult Result with value and metadata
     * @throws common::TransformationException on critical errors
     */
    virtual TransformationResult transform_safe(const std::any& input,
                                               core::ProcessingContext& context) {
        // Default implementation calls transform and wraps result
        return TransformationResult{transform(input, context), true, {}, {}, {}};
    }

    /**
     * @brief Apply transformation to a field value
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed value
     */
    virtual std::any transform(const std::any& input,
                              core::ProcessingContext& context) = 0;

    /**
     * @brief Validate input value before transformation
     * @param input Input value
     * @return bool True if valid
     */
    virtual bool validate_input(const std::any& input) const = 0;

    /**
     * @brief Get transformation type name
     * @return std::string Type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Configure transformation with parameters
     * @param params Configuration parameters
     */
    virtual void configure(const YAML::Node& params) = 0;
};

/**
 * @brief Base class for complex transformations
 */
class ComplexTransformation : public FieldTransformation {
public:
    /**
     * @brief Transform with detailed result
     * @param input Input value
     * @param context Processing context
     * @return TransformationResult Detailed result
     */
    virtual TransformationResult transform_detailed(
        const std::any& input,
        core::ProcessingContext& context) = 0;

    /**
     * @brief Safe transform implementation that returns detailed result
     */
    TransformationResult transform_safe(const std::any& input,
                                       core::ProcessingContext& context) override {
        return transform_detailed(input, context);
    }

    /**
     * @brief Standard transform implementation
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override {
        auto result = transform_detailed(input, context);
        if (!result.is_success()) {
            throw common::TransformationException(
                result.error_message.value_or("Unknown transformation error"),
                get_type(), "transform");
        }
        return result.value;
    }
};

/**
 * @brief Transform configuration constants
 */
namespace constants {
    constexpr size_t DEFAULT_BATCH_SIZE = 1000;
    constexpr size_t MAX_VALIDATION_ERRORS = 100;
    constexpr double NUMERIC_EPSILON = 0.0001;
    constexpr char DEFAULT_DATE_FORMAT[] = "%Y-%m-%d";
    constexpr char DEFAULT_DATETIME_FORMAT[] = "%Y-%m-%d %H:%M:%S";
    constexpr char DEFAULT_TIME_FORMAT[] = "%H:%M:%S";
}

/**
 * @brief Registry for custom transformations
 */
class TransformationRegistry {
public:
    /**
     * @brief Get singleton instance
     */
    static TransformationRegistry& instance() {
        static TransformationRegistry instance;
        return instance;
    }

    /**
     * @brief Register transformation factory
     * @param type_name Transformation type name
     * @param factory Factory function
     */
    void register_transformation(
        const std::string& type_name,
        std::function<std::unique_ptr<FieldTransformation>()> factory);

    /**
     * @brief Create transformation by type
     * @param type_name Transformation type name
     * @return std::unique_ptr<FieldTransformation> Created transformation
     */
    std::unique_ptr<FieldTransformation> create_transformation(
        const std::string& type_name);

    /**
     * @brief Check if transformation type is registered
     * @param type_name Transformation type name
     * @return bool True if registered
     */
    bool has_transformation(const std::string& type_name) const;

    /**
     * @brief Get all registered transformation types
     * @return std::vector<std::string> Type names
     */
    std::vector<std::string> get_registered_types() const;

    /**
     * @brief Reset registry (clear all transformations) - mainly for testing
     */
    void reset();

protected:
    TransformationRegistry() = default;
    std::unordered_map<std::string,
        std::function<std::unique_ptr<FieldTransformation>()>> factories_;
    mutable std::mutex registry_mutex_;
};

} // namespace omop::transform