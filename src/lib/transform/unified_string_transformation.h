#pragma once

/**
 * @file unified_string_transformation.h
 * @brief Unified string transformation supporting format, validate, and other operations
 */

#include "transform/transformations.h"
#include "common/utilities.h"
#include <string>
#include <regex>
#include <yaml-cpp/yaml.h>

namespace omop::transform {

/**
 * @brief Unified string transformation supporting multiple operations
 */
class UnifiedStringTransformation : public ComplexTransformation {
public:
    enum class Operation {
        Format,
        Validate,
        Manipulation,
        Extract
    };

    enum class ValidationType {
        NHSNumber,
        UKPostcode,
        UKPhoneNumber,
        Pattern,
        Custom
    };

    UnifiedStringTransformation() = default;

    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "string_transform"; }

    void configure(const YAML::Node& params) override;

private:
    std::string extract_string_value(const std::any& input) const;
    
    // Format operations
    TransformationResult format_operation(const std::string& input);
    
    // Validation operations
    TransformationResult validate_operation(const std::string& input);
    bool validate_pattern(const std::string& input) const;

    Operation operation_{Operation::Format};
    ValidationType validation_type_{ValidationType::Pattern};
    std::string pattern_;
    std::string format_template_;
    bool case_sensitive_{true};
    bool return_formatted_{true};
};

} // namespace omop::transform