#pragma once

/**
 * @file conditional_transformations.h
 * @brief Conditional transformation implementations
 *
 * Provides conditional transformation types including advanced conditional logic
 * and lookup table transformations.
 */

#include "transformations.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <optional>
#include <memory>
#include <yaml-cpp/yaml.h>

namespace omop::transform {

// Forward declaration for the expression parser
class ExpressionParser;

/**
 * @brief Advanced conditional transformation
 *
 * Provides complex conditional logic including multiple conditions,
 * nested conditions, and expression evaluation.
 */
class AdvancedConditionalTransformation : public FieldTransformation {
public:
    struct Condition {
        std::string field;
        std::string operator_type;
        std::any value;
        std::vector<Condition> nested_conditions;
        std::string logical_operator; // AND, OR
        bool negate{false};
    };

    struct Action {
        enum Type {
            SetValue,
            Transform,
            CopyField,
            Calculate
        } type;
        std::any value;
        std::string source_field;
        std::string transformation_name;
        YAML::Node transformation_params;
        std::string expression; // Add this line for calculation expressions
    };

    struct Rule {
        std::vector<Condition> conditions;
        Action then_action;
        std::optional<Action> else_action;
        std::string description;
    };

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "advanced_conditional"; }

    void configure(const YAML::Node& params) override;

private:
    Rule parse_rule(const YAML::Node& node);
    Condition parse_condition(const YAML::Node& node);
    Action parse_action(const YAML::Node& node);
    bool evaluate_conditions(const std::vector<Condition>& conditions,
                           const std::any& input,
                           core::ProcessingContext& context);
    bool evaluate_single_condition(const Condition& condition,
                                 const std::any& input,
                                 core::ProcessingContext& context);
    bool compare_values(const std::any& field_value,
                       const std::any& condition_value,
                       const std::string& operator_type);
    double extract_numeric(const std::any& value);
    TransformationResult apply_action(const Action& action,
                                    const std::any& input,
                                    core::ProcessingContext& context);
    void validate_configuration() const;

    std::vector<Rule> rules_;
    std::optional<Action> default_action_;
    bool continue_on_no_match_{true};
};

/**
 * @brief Lookup table transformation
 *
 * Maps values using a lookup table with optional default values.
 */
class LookupTableTransformation : public FieldTransformation {
public:
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "lookup_table"; }

    void configure(const YAML::Node& params) override;

private:
    std::string extract_key(const std::any& input);
    void load_lookup_from_file(const std::string& filename);

    std::unordered_map<std::string, std::string> lookup_table_;
    bool case_sensitive_{false};
    bool use_default_value_{false};
    std::string default_value_;
    bool pass_through_on_miss_{false};
    bool allow_null_keys_{false};
};

} // namespace omop::transform