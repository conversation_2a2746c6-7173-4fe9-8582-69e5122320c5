#include "transform/vocabulary_transformations.h"
#include "transform/vocabulary_service.h"
#include "common/logging.h"
#include "common/utilities.h"
#include <algorithm>

namespace omop::transform {

/**
 * @brief Concept hierarchy transformation
 *
 * Maps concepts to their ancestors or descendants in the concept hierarchy.
 */
TransformationResult ConceptHierarchyTransformation::transform_detailed(const std::any& input,
                                                                  core::ProcessingContext& context) {
    TransformationResult result;

    try {
        if (!validate_input(input)) {
            result.set_error("Invalid input for concept hierarchy transformation");
            return result;
        }

        // Get vocabulary service
        if (!VocabularyServiceManager::is_initialised()) {
            result.set_error("Vocabulary service not initialised");
            return result;
        }
        auto& vocab_service = VocabularyServiceManager::instance();

        int concept_id = extract_concept_id(input);
        if (concept_id == 0) {
            result.set_error("Invalid concept ID");
            return result;
        }

        // auto logger = common::Logger::get("omop-transform");
        // logger->info("ConceptHierarchy: concept_id={}, direction={}, strategy={}", 
        //            concept_id, (int)direction_, select_strategy_);

        // Perform hierarchy navigation
        int result_concept_id = 0;

        switch (direction_) {
            case Direction::ToAncestor: {
                // Get ancestors from vocabulary service
                std::vector<int> ancestors = vocab_service.get_ancestors(concept_id, ancestor_level_);
                
                if (!ancestors.empty()) {
                    if (select_strategy_ == "first") {
                        result_concept_id = ancestors.front();
                    } else if (select_strategy_ == "last") {
                        result_concept_id = ancestors.back();
                    } else if (select_strategy_ == "all") {
                        // Return all ancestors as a comma-separated string
                        std::vector<std::string> ids;
                        for (int id : ancestors) {
                            ids.push_back(std::to_string(id));
                        }
                        result.value = common::string_utils::join(ids, ",");
                        result.metadata["ancestor_count"] = ancestors.size();
                        return result;
                    }
                }
                break;
            }

            case Direction::ToDescendant: {
                // Get descendants from vocabulary service
                std::vector<int> descendants = vocab_service.get_descendants(concept_id, descendant_level_);
                
                // auto logger = common::Logger::get("omop-transform");
                // logger->info("ToDescendant: concept_id={}, level={}, found {} descendants", 
                //            concept_id, descendant_level_, descendants.size());
                
                if (!descendants.empty()) {
                    if (select_strategy_ == "first") {
                        result_concept_id = descendants.front();
                    } else if (select_strategy_ == "last") {
                        result_concept_id = descendants.back();
                        // logger->info("ToDescendant: selected last descendant {}", result_concept_id);
                    } else if (select_strategy_ == "all") {
                        // Return all descendants
                        std::vector<std::string> ids;
                        for (int id : descendants) {
                            ids.push_back(std::to_string(id));
                        }
                        result.value = common::string_utils::join(ids, ",");
                        result.metadata["descendant_count"] = descendants.size();
                        return result;
                    }
                }
                break;
            }

            case Direction::ToRoot: {
                // Get all ancestors and find the root
                auto ancestors = vocab_service.get_ancestors(concept_id, -1);
                if (!ancestors.empty()) {
                    // The root is typically the last ancestor (highest level)
                    result_concept_id = ancestors.back();
                }
                break;
            }

            case Direction::ToLeaf: {
                // Get descendants and filter for leaf nodes
                auto descendants = vocab_service.get_descendants(concept_id, -1);
                std::vector<int> leaf_nodes;

                for (int desc_id : descendants) {
                    auto desc_descendants = vocab_service.get_descendants(desc_id, 1);
                    if (desc_descendants.empty()) {
                        leaf_nodes.push_back(desc_id);
                    }
                }

                if (!leaf_nodes.empty()) {
                    result_concept_id = leaf_nodes.front();
                    result.metadata["leaf_count"] = leaf_nodes.size();
                }
                break;
            }
        }

        if (result_concept_id == 0) {
            if (use_original_on_fail_) {
                result.value = concept_id;
                result.add_warning("No hierarchy mapping found, using original concept");
            } else {
                result.value = default_concept_id_;
                result.add_warning("No hierarchy mapping found, using default concept");
            }
        } else {
            result.value = result_concept_id;
        }

        // Add concept information to metadata  
        if (result.value.type() == typeid(int)) {
            int final_concept_id = std::any_cast<int>(result.value);
            auto concept_result = vocab_service.get_concept(final_concept_id);
            if (concept_result) {
                result.metadata["concept_name"] = concept_result->concept_name();
                result.metadata["domain_id"] = concept_result->domain_id();
                result.metadata["vocabulary_id"] = concept_result->vocabulary_id();
            }
        }

    } catch (const std::exception& e) {
        result.set_error(std::format("Concept hierarchy transformation failed: {}", e.what()));
        context.increment_errors();
    }

    return result;
}

bool ConceptHierarchyTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return false;

    return input.type() == typeid(int) ||
           input.type() == typeid(int64_t) ||
           input.type() == typeid(std::string);
}

void ConceptHierarchyTransformation::configure(const YAML::Node& params) {
    if (params["direction"]) {
        std::string dir_str = params["direction"].as<std::string>();
        configure_direction(dir_str);
    }

    if (params["ancestor_level"]) {
        ancestor_level_ = params["ancestor_level"].as<int>();
    }

    if (params["descendant_level"]) {
        descendant_level_ = params["descendant_level"].as<int>();
    }

    if (params["select_strategy"]) {
        select_strategy_ = params["select_strategy"].as<std::string>();
    }

    if (params["default_concept_id"]) {
        default_concept_id_ = params["default_concept_id"].as<int>();
    }

    if (params["use_original_on_fail"]) {
        use_original_on_fail_ = params["use_original_on_fail"].as<bool>();
    }
}

int ConceptHierarchyTransformation::extract_concept_id(const std::any& input) {
    if (input.type() == typeid(int)) {
        return std::any_cast<int>(input);
    } else if (input.type() == typeid(int64_t)) {
        return static_cast<int>(std::any_cast<int64_t>(input));
    } else if (input.type() == typeid(std::string)) {
        try {
            return std::stoi(std::any_cast<std::string>(input));
        } catch (...) {
            return 0;
        }
    }
    return 0;
}

void ConceptHierarchyTransformation::configure_direction(const std::string& dir_str) {
    static const std::unordered_map<std::string, Direction> dir_map = {
        {"to_ancestor", Direction::ToAncestor},
        {"ancestor", Direction::ToAncestor},
        {"to_descendant", Direction::ToDescendant},
        {"descendant", Direction::ToDescendant},
        {"to_root", Direction::ToRoot},
        {"root", Direction::ToRoot},
        {"to_leaf", Direction::ToLeaf},
        {"leaf", Direction::ToLeaf}
    };

    auto it = dir_map.find(dir_str);
    if (it != dir_map.end()) {
        direction_ = it->second;
    }
}

/**
 * @brief Domain-specific concept mapping
 *
 * Maps concepts to ensure they belong to the correct domain.
 */
TransformationResult DomainMappingTransformation::transform_detailed(const std::any& input,
                                                                   core::ProcessingContext& context) {
    TransformationResult result;

    try {
        if (!validate_input(input)) {
            result.set_error("Invalid input for domain mapping transformation");
            return result;
        }

        // Check if input is a Record - if so, handle field mappings
        if (input.type() == typeid(core::Record)) {
            return transform_record(std::any_cast<core::Record>(input), context);
        }

        // Get vocabulary service
        if (!VocabularyServiceManager::is_initialised()) {
            result.set_error("Vocabulary service not initialised");
            return result;
        }
        auto& vocab_service = VocabularyServiceManager::instance();

        // Extract source value
        std::string source_value = extract_string_value(input);

        // First try to map to concept using source vocabulary
        int concept_id = vocab_service.map_to_concept_id(
            source_value, source_vocabulary_);
        
        // If auto-learn is enabled and no mapping found, this will be recorded
        if (concept_id == 0 && VocabularyServiceManager::instance().is_auto_learn_enabled()) {
            auto logger = common::Logger::get("omop-transform");
            logger->debug("No mapping found for '{}', recorded for auto-learn", source_value);
        }

        if (concept_id == 0) {
            result.add_warning(std::format(
                "No concept mapping found for '{}' in vocabulary '{}'",
                source_value, source_vocabulary_));
            result.value = get_domain_default(target_domain_);
            return result;
        }

        // Check if concept is in target domain
        bool is_in_domain = TransformationUtils::is_in_domain(concept_id, target_domain_);

        if (is_in_domain) {
            result.value = concept_id;
        } else {
            // Need to find equivalent concept in target domain
            auto concept_result = vocab_service.get_concept(concept_id);
            if (!concept_result) {
                result.value = default_concept_id_;
                result.add_warning("Could not retrieve concept information");
                return result;
            }

            // Try to find standard concept
            int standard_id = vocab_service.get_standard_concept(concept_id);
            if (standard_id != 0 && TransformationUtils::is_in_domain(standard_id, target_domain_)) {
                result.value = standard_id;
                result.metadata["mapped_to_standard"] = true;
            } else {
                // Use domain-specific default
                result.value = get_domain_default(target_domain_);
                result.add_warning(std::format(
                    "Concept {} ({}) not in domain {}, using default",
                    concept_id, concept_result->concept_name(), target_domain_));
            }
        }

        // Add metadata
        auto final_concept = vocab_service.get_concept(
            std::any_cast<int>(result.value));
        if (final_concept) {
            result.metadata["concept_name"] = final_concept->concept_name();
            result.metadata["target_domain"] = final_concept->domain_id();
            // Note: original_domain would need concept_result in scope
        }

    } catch (const std::exception& e) {
        result.set_error(std::format("Domain mapping transformation failed: {}", e.what()));
        context.increment_errors();
    }

    return result;
}

bool DomainMappingTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return false;

    return input.type() == typeid(std::string) ||
           input.type() == typeid(const char*) ||
           input.type() == typeid(int) ||
           input.type() == typeid(int64_t) ||
           input.type() == typeid(core::Record);
}

void DomainMappingTransformation::configure(const YAML::Node& params) {
    if (params["source_vocabulary"]) {
        source_vocabulary_ = params["source_vocabulary"].as<std::string>();
    }

    if (params["target_domain"]) {
        target_domain_ = params["target_domain"].as<std::string>();
    }

    if (params["default_concept_id"]) {
        default_concept_id_ = params["default_concept_id"].as<int>();
    }

    if (params["domain_defaults"]) {
        for (const auto& entry : params["domain_defaults"]) {
            domain_defaults_[entry.first.as<std::string>()] =
                entry.second.as<int>();
        }
    }

    // Handle field mappings configuration
    if (params["mappings"]) {
        field_mappings_.clear();
        for (const auto& mapping : params["mappings"]) {
            FieldMappingConfig config;
            if (mapping["source_field"]) {
                config.source_field = mapping["source_field"].as<std::string>();
            }
            if (mapping["target_field"]) {
                config.target_field = mapping["target_field"].as<std::string>();
            }
            if (mapping["type"]) {
                config.type = mapping["type"].as<std::string>();
            }
            if (mapping["expression"]) {
                config.expression = mapping["expression"].as<std::string>();
            }
            field_mappings_.push_back(config);
        }
    }
}

std::string DomainMappingTransformation::extract_string_value(const std::any& input) {
    if (input.type() == typeid(std::string)) {
        return std::any_cast<std::string>(input);
    } else if (input.type() == typeid(const char*)) {
        return std::string(std::any_cast<const char*>(input));
    } else if (input.type() == typeid(int)) {
        return std::to_string(std::any_cast<int>(input));
    } else if (input.type() == typeid(int64_t)) {
        return std::to_string(std::any_cast<int64_t>(input));
    }
    return "";
}

int DomainMappingTransformation::get_domain_default(const std::string& domain) {
    auto it = domain_defaults_.find(domain);
    if (it != domain_defaults_.end()) {
        return it->second;
    }
    return default_concept_id_;
}

TransformationResult DomainMappingTransformation::transform_record(const core::Record& input, 
                                                                core::ProcessingContext& context) {
    TransformationResult result;
    core::Record output_record;

    try {
        // Process each field mapping
        for (const auto& mapping : field_mappings_) {
            auto source_value = input.getFieldOptional(mapping.source_field);
            
            if (mapping.type == "direct") {
                // Direct field mapping - copy value as is
                if (source_value.has_value()) {
                    output_record.setField(mapping.target_field, *source_value);
                }
            } else if (mapping.type == "computed") {
                // Computed field mapping - evaluate expression
                std::string computed_value = evaluate_expression(mapping.expression, input);
                output_record.setField(mapping.target_field, computed_value);
            }
        }

        result.value = output_record;
        result.metadata["mappings_applied"] = field_mappings_.size();

    } catch (const std::exception& e) {
        result.set_error(std::format("Record transformation failed: {}", e.what()));
        context.increment_errors();
    }

    return result;
}

std::string DomainMappingTransformation::evaluate_expression(const std::string& expression, 
                                                           const core::Record& record) {
    // Simple expression evaluation for string concatenation
    std::string result = expression;
    
    // Handle the specific case: "first_name + ' ' + last_name"
    std::regex concat_pattern(R"((\w+)\s*\+\s*'([^']+)'\s*\+\s*(\w+))");
    std::smatch match;
    
    if (std::regex_search(result, match, concat_pattern)) {
        std::string field1 = match[1].str();
        std::string separator = match[2].str();
        std::string field2 = match[3].str();
        
        // Get field values
        auto value1 = record.getFieldOptional(field1);
        auto value2 = record.getFieldOptional(field2);
        
        std::string str1, str2;
        if (value1.has_value() && value1->type() == typeid(std::string)) {
            str1 = std::any_cast<std::string>(*value1);
        }
        if (value2.has_value() && value2->type() == typeid(std::string)) {
            str2 = std::any_cast<std::string>(*value2);
        }
        
        return str1 + separator + str2;
    }
    
    // Handle simple field replacement
    std::regex field_pattern(R"(\b(\w+)\b)");
    std::sregex_iterator iter(expression.begin(), expression.end(), field_pattern);
    std::sregex_iterator end;
    
    std::map<std::string, std::string> replacements;
    for (; iter != end; ++iter) {
        std::string field_name = iter->str();
        
        // Skip common operators and keywords
        if (field_name == "and" || field_name == "or" || field_name == "not" || 
            field_name == "if" || field_name == "then" || field_name == "else") {
            continue;
        }
        
        auto field_value = record.getFieldOptional(field_name);
        if (field_value.has_value()) {
            std::string value_str;
            if (field_value->type() == typeid(std::string)) {
                value_str = std::any_cast<std::string>(*field_value);
            } else if (field_value->type() == typeid(int)) {
                value_str = std::to_string(std::any_cast<int>(*field_value));
            } else if (field_value->type() == typeid(double)) {
                value_str = std::to_string(std::any_cast<double>(*field_value));
            } else {
                value_str = "";
            }
            replacements[field_name] = value_str;
        }
    }
    
    // Apply replacements only if no concatenation pattern was found
    if (replacements.size() == 1) {
        for (const auto& [field, value] : replacements) {
            // Escape regex metacharacters in field name to prevent invalid regex
            std::string escaped_field = field;
            std::string metacharacters = R"(\^${}[]().*+?|)";
            for (char c : metacharacters) {
                std::string from(1, c);
                std::string to = "\\" + from;
                size_t pos = 0;
                while ((pos = escaped_field.find(from, pos)) != std::string::npos) {
                    escaped_field.replace(pos, 1, to);
                    pos += to.length();
                }
            }
            std::regex field_regex(R"(\b)" + escaped_field + R"(\b)");
            result = std::regex_replace(result, field_regex, value);
        }
    }
    
    return result;
}

TransformationResult ConceptRelationshipTransformation::transform_detailed(const std::any& input,
                                                                         core::ProcessingContext& context) {
    TransformationResult result;

    try {
        if (!validate_input(input)) {
            result.set_error("Invalid input for concept relationship transformation");
            return result;
        }

        // Get vocabulary service
        if (!VocabularyServiceManager::is_initialised()) {
            result.set_error("Vocabulary service not initialised");
            return result;
        }
        auto& vocab_service = VocabularyServiceManager::instance();

        int source_concept_id = extract_concept_id(input);
        if (source_concept_id == 0) {
            result.set_error("Invalid source concept ID");
            return result;
        }

        // Find related concepts
        std::vector<int> related_concepts = find_related_concepts(
            vocab_service, source_concept_id);

        if (related_concepts.empty()) {
            if (use_source_on_no_match_) {
                result.value = source_concept_id;
                result.add_warning(std::format(
                    "No {} relationship found for concept {}", relationship_id_, source_concept_id));
            } else {
                result.value = default_concept_id_;
                result.add_warning("No related concepts found");
            }
        } else {
            // Select concept based on strategy
            int selected_concept = select_concept(related_concepts, vocab_service);
            result.value = selected_concept;

            result.metadata["relationship_id"] = relationship_id_;
            result.metadata["related_count"] = related_concepts.size();

            // Add concept details
            auto concept_result = vocab_service.get_concept(selected_concept);
            if (concept_result) {
                result.metadata["selected_concept_name"] = concept_result->concept_name();
                result.metadata["selected_vocabulary"] = concept_result->vocabulary_id();
            }
        }

    } catch (const std::exception& e) {
        result.set_error(std::format("Concept relationship transformation failed: {}", e.what()));
        context.increment_errors();
    }

    return result;
}

bool ConceptRelationshipTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return false;

    return input.type() == typeid(int) ||
           input.type() == typeid(int64_t) ||
           input.type() == typeid(std::string);
}

void ConceptRelationshipTransformation::configure(const YAML::Node& params) {
    if (params["relationship_id"]) {
        relationship_id_ = params["relationship_id"].as<std::string>();
    }

    if (params["selection_strategy"]) {
        selection_strategy_ = params["selection_strategy"].as<std::string>();
    }

    if (params["filter_vocabulary"]) {
        filter_vocabulary_ = params["filter_vocabulary"].as<std::string>();
    }

    if (params["filter_domain"]) {
        filter_domain_ = params["filter_domain"].as<std::string>();
    }

    // Support filters parameter as well
    if (params["filters"]) {
        if (params["filters"]["vocabulary_id"]) {
            filter_vocabulary_ = params["filters"]["vocabulary_id"].as<std::string>();
        }
        if (params["filters"]["domain_id"]) {
            filter_domain_ = params["filters"]["domain_id"].as<std::string>();
        }
    }

    if (params["prefer_standard"]) {
        prefer_standard_ = params["prefer_standard"].as<bool>();
    }

    if (params["default_concept_id"]) {
        default_concept_id_ = params["default_concept_id"].as<int>();
    }

    if (params["use_source_on_no_match"]) {
        use_source_on_no_match_ = params["use_source_on_no_match"].as<bool>();
    }
    
    if (params["use_original_on_fail"]) {
        use_source_on_no_match_ = params["use_original_on_fail"].as<bool>();
    }
}

int ConceptRelationshipTransformation::extract_concept_id(const std::any& input) {
    if (input.type() == typeid(int)) {
        return std::any_cast<int>(input);
    } else if (input.type() == typeid(int64_t)) {
        return static_cast<int>(std::any_cast<int64_t>(input));
    } else if (input.type() == typeid(std::string)) {
        try {
            return std::stoi(std::any_cast<std::string>(input));
        } catch (...) {
            return 0;
        }
    }
    return 0;
}

std::vector<int> ConceptRelationshipTransformation::find_related_concepts(VocabularyService& vocab_service,
                                                                         int source_concept_id) {
    std::vector<int> related;

    try {
        // Query concept_relationship table for related concepts
        auto connection = vocab_service.get_connection(); // Need to add getter to VocabularyService
        if (!connection) {
            auto logger = common::Logger::get("omop-transform");
            logger->error("No database connection available for concept relationships");
            return related;
        }

        std::string query = R"(
            SELECT DISTINCT concept_id_2
            FROM concept_relationship
            WHERE concept_id_1 = $1
              AND relationship_id = $2
              AND invalid_reason IS NULL
        )";

        auto stmt = connection->prepare_statement(query);
        if (!stmt) {
            return related;
        }

        stmt->bind(1, source_concept_id);
        stmt->bind(2, relationship_id_);
        auto result = stmt->execute_query();

        while (result->next()) {
            related.push_back(std::any_cast<int>(result->get_value(0)));
        }

        // Special handling for "Maps to" relationship
        if (relationship_id_ == "Maps to" && related.empty()) {
            // Try get_standard_concept as fallback, but only for valid concepts
            if (source_concept_id > 0 && source_concept_id < 900000) {  // Reasonable concept ID range
                int standard_id = vocab_service.get_standard_concept(source_concept_id);
                if (standard_id != 0 && standard_id != source_concept_id) {
                    related.push_back(standard_id);
                }
            }
        }

    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-transform");
        logger->error("Failed to find related concepts: {}", e.what());
    }

    return related;
}

int ConceptRelationshipTransformation::select_concept(const std::vector<int>& concepts,
                                                     VocabularyService& vocab_service) {
    if (concepts.empty()) {
        return default_concept_id_;
    }

    // Apply filters
    std::vector<int> filtered = concepts;

    if (!filter_vocabulary_.empty()) {
        filtered.erase(
            std::remove_if(filtered.begin(), filtered.end(),
                [&](int id) {
                    auto concept_result = vocab_service.get_concept(id);
                    return !concept_result || concept_result->vocabulary_id() != filter_vocabulary_;
                }),
            filtered.end());
    }

    if (!filter_domain_.empty()) {
        filtered.erase(
            std::remove_if(filtered.begin(), filtered.end(),
                [&](int id) {
                    auto concept_result = vocab_service.get_concept(id);
                    return !concept_result || concept_result->domain_id() != filter_domain_;
                }),
            filtered.end());
    }

    if (prefer_standard_) {
        // Sort by standard concept preference
        std::sort(filtered.begin(), filtered.end(),
            [&](int a, int b) {
                auto concept_a = vocab_service.get_concept(a);
                auto concept_b = vocab_service.get_concept(b);
                if (!concept_a || !concept_b) return false;
                bool a_is_standard = concept_a->is_standard();
                bool b_is_standard = concept_b->is_standard();
                return a_is_standard && !b_is_standard;
            });
    }

    // Apply selection strategy
    if (selection_strategy_ == "first" && !filtered.empty()) {
        return filtered.front();
    } else if (selection_strategy_ == "last" && !filtered.empty()) {
        return filtered.back();
    }

    return filtered.empty() ? default_concept_id_ : filtered.front();
}

// Register vocabulary transformations
static bool register_vocabulary_transformations() {
    auto& registry = TransformationRegistry::instance();

    registry.register_transformation("concept_hierarchy",
        []() { return std::make_unique<ConceptHierarchyTransformation>(); });

    registry.register_transformation("domain_mapping",
        []() { return std::make_unique<DomainMappingTransformation>(); });

    registry.register_transformation("concept_relationship",
        []() { return std::make_unique<ConceptRelationshipTransformation>(); });

    return true;
}

static bool vocabulary_transformations_registered = register_vocabulary_transformations();

} // namespace omop::transform