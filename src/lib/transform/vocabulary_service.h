#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <optional>
#include <memory>
#include <shared_mutex>
#include <chrono>
#include <mutex>
#include <atomic>
#include <set>
#include <thread>
#include <condition_variable>
#include <queue>
#include <utility>
#include <yaml-cpp/yaml.h>
#include "common/exceptions.h"
#include "ml/medical_term_classifier.h"
#include "transform/transformations.h"
#include "transform/transformation_utils.h"
#include "transformation_utils.h"
#include "extract/database_connector.h"

namespace ml { class MedicalTermClassifier; }
namespace omop::transform {

/**
 * @brief Vocabulary mapping entry
 *
 * Represents a mapping from a source value to an OMOP concept.
 */
struct VocabularyMapping {
    std::string source_value;
    std::string source_vocabulary;
    int target_concept_id;
    std::string target_vocabulary;
    float mapping_confidence{1.0f};
    std::string mapping_type;
    std::optional<std::string> context;
};

// Forward declarations
class VocabularyService;

/**
 * @brief Vocabulary update scheduler
 *
 * Handles scheduled updates of vocabulary data from external sources.
 */
class VocabularyUpdateScheduler {
public:
    /**
     * @brief Constructor
     * @param service Pointer to vocabulary service
     */
    explicit VocabularyUpdateScheduler(VocabularyService* service);

    /**
     * @brief Destructor
     */
    ~VocabularyUpdateScheduler();

    /**
     * @brief Start the scheduler
     */
    void start();

    /**
     * @brief Stop the scheduler
     */
    void stop();

    /**
     * @brief Configure the scheduler
     * @param config Configuration parameters
     */
    void configure(const YAML::Node& config);

private:
    /**
     * @brief Main scheduler thread function
     */
    void scheduler_thread();

    VocabularyService* vocabulary_service_;
    std::thread scheduler_thread_;
    std::atomic<bool> running_{false};
    std::mutex mutex_;
    std::condition_variable cv_;
    std::chrono::hours update_interval_{24};
    std::chrono::hours quiet_hours_start_{2};
    std::chrono::hours quiet_hours_end_{6};
    size_t batch_size_{100};
};

/**
 * @brief Vocabulary version manager
 *
 * Manages versioning of vocabulary data and tracks changes.
 */
class VocabularyVersionManager {
public:
    /**
     * @brief Types of mapping changes for versioning
     */
    enum class MappingChangeType { Added, Modified, Removed, Deprecated, Validated, Merged };
    
    /**
     * @brief Version change metadata
     */
    struct MappingChange {
        MappingChangeType type;
        VocabularyMapping new_mapping;
        VocabularyMapping old_mapping;
        std::string reason;
        std::string description;
        std::string author;
        std::string change_id;
        int mappings_added = 0;
        int mappings_modified = 0;
        int mappings_removed = 0;
        std::chrono::system_clock::time_point timestamp;
        std::vector<std::string> affected_concepts;
        std::vector<std::string> validation_errors;
        bool requires_approval{false};
        std::string approval_status{"pending"};
        std::string approved_by;
        std::chrono::system_clock::time_point approval_timestamp;
    };

    /**
     * @brief Version information
     */
    struct VersionInfo {
        std::string version_id;
        std::string description;
        std::string author;
        std::chrono::system_clock::time_point created;
        std::chrono::system_clock::time_point last_modified;
        size_t total_changes;
        std::vector<std::string> tags;
        bool is_stable{false};
        bool is_approved{false};
        std::string parent_version;
        std::vector<std::string> child_versions;
    };

    /**
     * @brief Begin a version transaction
     * @param description Description of the transaction
     * @param author Author of the changes
     * @param change_id Optional change ID for tracking
     */
    void begin_transaction(const std::string& description, 
                          const std::string& author = "system",
                          const std::string& change_id = "");

    /**
     * @brief Commit the current transaction
     * @param require_approval Whether to require approval before committing
     * @return std::string Version ID of the committed version
     */
    std::string commit_transaction(bool require_approval = false);

    /**
     * @brief Rollback the current transaction
     */
    void rollback_transaction();

    /**
     * @brief Get current version
     * @return std::string Version string
     */
    std::string get_current_version() const;

    /**
     * @brief Get version information
     * @param version_id Version ID
     * @return std::optional<VersionInfo> Version information if found
     */
    std::optional<VersionInfo> get_version_info(const std::string& version_id) const;

    /**
     * @brief Record a mapping change for versioning
     * @param change The mapping change
     */
    void record_change(const MappingChange& change);

    /**
     * @brief Get version history (last N changes)
     * @param max_entries Maximum number of entries to return
     * @return std::vector<MappingChange> List of changes
     */
    std::vector<MappingChange> get_version_history(size_t max_entries = 0) const;

    /**
     * @brief Create a new version branch
     * @param branch_name Branch name
     * @param base_version Base version to branch from
     * @param description Branch description
     * @return std::string New branch version ID
     */
    std::string create_branch(const std::string& branch_name, 
                             const std::string& base_version,
                             const std::string& description = "");

    /**
     * @brief Merge branch into target version
     * @param source_branch Source branch version ID
     * @param target_version Target version ID
     * @param merge_strategy Merge strategy to use
     * @return std::string Merged version ID
     */
    std::string merge_branch(const std::string& source_branch,
                            const std::string& target_version,
                            const std::string& merge_strategy = "auto");

    /**
     * @brief Rollback to a specific version
     * @param target_version Version to rollback to
     * @param create_backup Whether to create a backup of current version
     * @return std::string Backup version ID if created
     */
    std::string rollback_to_version(const std::string& target_version, bool create_backup = true);

    /**
     * @brief Get all available versions
     * @return std::vector<VersionInfo> List of all versions
     */
    std::vector<VersionInfo> get_all_versions() const;

    /**
     * @brief Get version differences
     * @param from_version Source version
     * @param to_version Target version
     * @return std::vector<MappingChange> List of changes between versions
     */
    std::vector<MappingChange> get_version_differences(const std::string& from_version,
                                                      const std::string& to_version) const;

    /**
     * @brief Approve a pending version
     * @param version_id Version ID to approve
     * @param approver Approver name
     * @return bool True if approval successful
     */
    bool approve_version(const std::string& version_id, const std::string& approver);

    /**
     * @brief Reject a pending version
     * @param version_id Version ID to reject
     * @param reason Rejection reason
     * @return bool True if rejection successful
     */
    bool reject_version(const std::string& version_id, const std::string& reason);

    /**
     * @brief Tag a version
     * @param version_id Version ID
     * @param tag Tag to add
     */
    void tag_version(const std::string& version_id, const std::string& tag);

    /**
     * @brief Get versions by tag
     * @param tag Tag to search for
     * @return std::vector<VersionInfo> Versions with the specified tag
     */
    std::vector<VersionInfo> get_versions_by_tag(const std::string& tag) const;

    /**
     * @brief Export version to external format
     * @param version_id Version ID to export
     * @param format Export format (json, xml, csv)
     * @return std::string Exported data
     */
    std::string export_version(const std::string& version_id, const std::string& format = "json") const;

    /**
     * @brief Import version from external format
     * @param version_data Version data to import
     * @param format Import format
     * @param description Description of the imported version
     * @return std::string Imported version ID
     */
    std::string import_version(const std::string& version_data, 
                              const std::string& format = "json",
                              const std::string& description = "Imported version");

private:
    std::optional<int> current_transaction_id_;
    std::vector<std::string> pending_changes_;
    std::string current_version_;
    std::vector<MappingChange> change_history_;
    
    // Enhanced versioning support
    std::unordered_map<std::string, VersionInfo> version_info_;
    std::unordered_map<std::string, std::vector<std::string>> version_changes_;
    std::unordered_map<std::string, std::vector<std::string>> version_tags_;
    std::unordered_map<std::string, std::string> version_branches_;
    
    // Approval workflow
    std::unordered_map<std::string, std::string> pending_approvals_;
    std::unordered_map<std::string, std::string> approval_reasons_;
    
    // Helper methods
    std::string generate_version_id() const;
    std::string generate_change_id() const;
    bool validate_version_id(const std::string& version_id) const;
    std::vector<MappingChange> calculate_changes_between_versions(
        const std::string& from_version, const std::string& to_version) const;
    std::string resolve_merge_conflicts(const std::vector<MappingChange>& source_changes,
                                      const std::vector<MappingChange>& target_changes) const;
};

/**
 * @brief Conflict resolution engine
 *
 * Handles conflicts when multiple mappings exist for the same term.
 */
class ConflictResolutionEngine {
public:
    /**
     * @brief Conflict resolution result
     */
    struct ConflictResolution {
        VocabularyMapping selected_mapping;
        std::vector<VocabularyMapping> alternatives;
        float confidence_score{0.0f};
        bool requires_human_review{false};
        std::string reasoning;
        std::vector<std::string> resolution_factors;
        float semantic_similarity{0.0f};
        float frequency_weight{0.0f};
        float recency_weight{0.0f};
        float source_reliability_weight{0.0f};
    };

    /**
     * @brief Advanced conflict resolution strategies
     */
    enum class ResolutionStrategy {
        ConfidenceBased,      // Use confidence scores
        FrequencyBased,       // Use frequency of usage
        RecencyBased,         // Use recency of updates
        SemanticBased,        // Use semantic similarity
        SourceReliability,    // Use source reliability scores
        Hybrid,               // Combine multiple strategies
        MachineLearning,      // Use ML-based resolution
        ManualReview          // Always require human review
    };

    /**
     * @brief Resolve conflict between multiple mappings
     * @param term Term with conflicting mappings
     * @param candidate_mappings Candidate mappings
     * @param context Context for disambiguation
     * @return ConflictResolution Resolution result
     */
    ConflictResolution resolve_conflict(
        const std::string& term,
        const std::vector<VocabularyMapping>& candidate_mappings,
        const std::string& context = "");

    /**
     * @brief Set resolution strategy
     * @param strategy Strategy name
     */
    void set_resolution_strategy(const std::string& strategy);

    /**
     * @brief Set resolution strategy using enum
     * @param strategy Resolution strategy
     */
    void set_resolution_strategy(ResolutionStrategy strategy);

    /**
     * @brief Configure the engine
     * @param config Configuration parameters
     */
    void configure(const YAML::Node& config);

    /**
     * @brief Enable machine learning-based resolution
     * @param enabled Whether to enable ML resolution
     * @param model_path Path to ML model
     */
    void enable_ml_resolution(bool enabled, const std::string& model_path = "");

    /**
     * @brief Get resolution statistics
     * @return Resolution statistics
     */
    struct ResolutionStats {
        size_t total_conflicts;
        size_t auto_resolved;
        size_t ml_resolved;
        size_t manual_reviews;
        float average_confidence;
        std::chrono::system_clock::time_point last_updated;
    };

    ResolutionStats get_resolution_stats() const;

    /**
     * @brief Train resolution model with historical decisions
     * @param training_data Vector of historical resolution decisions
     */
    void train_resolution_model(const std::vector<std::pair<std::string, ConflictResolution>>& training_data);

private:
    ResolutionStrategy resolution_strategy_{ResolutionStrategy::Hybrid};
    float confidence_threshold_{0.8f};
    bool ml_resolution_enabled_{false};
    std::string ml_model_path_;
    
    // Resolution statistics
    mutable std::mutex stats_mutex_;
    ResolutionStats resolution_stats_;
    
    
    // Resolution strategy implementations
    ConflictResolution resolve_confidence_based(
        const std::string& term,
        const std::vector<VocabularyMapping>& candidate_mappings,
        const std::string& context);
    
    ConflictResolution resolve_frequency_based(
        const std::string& term,
        const std::vector<VocabularyMapping>& candidate_mappings,
        const std::string& context);
    
    ConflictResolution resolve_recency_based(
        const std::string& term,
        const std::vector<VocabularyMapping>& candidate_mappings,
        const std::string& context);
    
    ConflictResolution resolve_semantic_based(
        const std::string& term,
        const std::vector<VocabularyMapping>& candidate_mappings,
        const std::string& context);
    
    ConflictResolution resolve_hybrid(
        const std::string& term,
        const std::vector<VocabularyMapping>& candidate_mappings,
        const std::string& context);
    
    ConflictResolution resolve_ml_based(
        const std::string& term,
        const std::vector<VocabularyMapping>& candidate_mappings,
        const std::string& context);
    
    // Helper methods
    float calculate_semantic_similarity(const std::string& term, const VocabularyMapping& mapping) const;
    float calculate_frequency_score(const VocabularyMapping& mapping) const;
    float calculate_recency_score(const VocabularyMapping& mapping) const;
    float calculate_source_reliability_score(const VocabularyMapping& mapping) const;
    std::vector<std::string> extract_resolution_factors(const ConflictResolution& resolution) const;
};

/**
 * @brief Represents an OMOP concept
 *
 * This class encapsulates the information about a single OMOP concept,
 * including its ID, name, domain, and vocabulary information.
 */
class Concept {
public:
    /**
     * @brief Default constructor
     */
    Concept() = default;

    /**
     * @brief Constructor with all fields
     */
    Concept(int concept_id,
            std::string concept_name,
            std::string domain_id,
            std::string vocabulary_id,
            std::string concept_class_id,
            std::string concept_code)
        : concept_id_(concept_id),
          concept_name_(std::move(concept_name)),
          domain_id_(std::move(domain_id)),
          vocabulary_id_(std::move(vocabulary_id)),
          concept_class_id_(std::move(concept_class_id)),
          concept_code_(std::move(concept_code)) {}

    // Getters
    [[nodiscard]] constexpr int concept_id() const noexcept { return concept_id_; }
    [[nodiscard]] constexpr const std::string& concept_name() const noexcept { return concept_name_; }
    [[nodiscard]] constexpr const std::string& domain_id() const noexcept { return domain_id_; }
    [[nodiscard]] constexpr const std::string& vocabulary_id() const noexcept { return vocabulary_id_; }
    [[nodiscard]] constexpr const std::string& concept_class_id() const noexcept { return concept_class_id_; }
    [[nodiscard]] constexpr const std::string& concept_code() const noexcept { return concept_code_; }
    [[nodiscard]] bool is_standard() const noexcept { return standard_concept_ == "S"; }
    [[nodiscard]] bool is_valid() const {
        // For UK date format testing, check if end date is in future
        if (valid_end_date_.empty()) {
            return true; // No end date means valid
        }
        
        // Get current date for comparison
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::tm* tm = std::localtime(&time_t);
        int current_year = tm->tm_year + 1900;
        int current_month = tm->tm_mon + 1;
        int current_day = tm->tm_mday;
        
        // Check for UK format dates (DD/MM/YYYY)
        if (valid_end_date_.find('/') != std::string::npos) {
            // Parse DD/MM/YYYY format
            size_t pos1 = valid_end_date_.find('/');
            size_t pos2 = valid_end_date_.find('/', pos1 + 1);
            
            if (pos1 != std::string::npos && pos2 != std::string::npos) {
                try {
                    int day = std::stoi(valid_end_date_.substr(0, pos1));
                    int month = std::stoi(valid_end_date_.substr(pos1 + 1, pos2 - pos1 - 1));
                    int year = std::stoi(valid_end_date_.substr(pos2 + 1));
                    
                    // Compare dates
                    if (year > current_year) return true;
                    if (year < current_year) return false;
                    if (month > current_month) return true;
                    if (month < current_month) return false;
                    return day >= current_day;
                } catch (...) {
                    return true; // Assume valid on parse error
                }
            }
        }
        
        // Check for ISO format dates (YYYY-MM-DD)
        if (valid_end_date_.find('-') != std::string::npos) {
            // Parse YYYY-MM-DD format
            size_t pos1 = valid_end_date_.find('-');
            size_t pos2 = valid_end_date_.find('-', pos1 + 1);
            
            if (pos1 != std::string::npos && pos2 != std::string::npos) {
                try {
                    int year = std::stoi(valid_end_date_.substr(0, pos1));
                    int month = std::stoi(valid_end_date_.substr(pos1 + 1, pos2 - pos1 - 1));
                    int day = std::stoi(valid_end_date_.substr(pos2 + 1));
                    
                    // Compare dates
                    if (year > current_year) return true;
                    if (year < current_year) return false;
                    if (month > current_month) return true;
                    if (month < current_month) return false;
                    return day >= current_day;
                } catch (...) {
                    return true; // Assume valid on parse error
                }
            }
        }
        
        return true; // Default to valid
    }

    // Setters
    void set_standard_concept(const std::string& standard) { standard_concept_ = standard; }
    void set_valid_dates(const std::string& start, const std::string& end) {
        valid_start_date_ = start;
        valid_end_date_ = end;
    }

private:
    int concept_id_{0};
    std::string concept_name_;
    std::string domain_id_;
    std::string vocabulary_id_;
    std::string concept_class_id_;
    std::string concept_code_;
    std::string standard_concept_;
    std::string valid_start_date_;
    std::string valid_end_date_;
};

/**
 * @brief Vocabulary service for concept lookups and mappings
 *
 * This service manages vocabulary data, provides concept lookups,
 * and handles source-to-concept mappings for the ETL pipeline.
 */
class VocabularyService {
public:
    /**
     * @brief Constructor
     * @param connection Database connection for vocabulary tables (optional)
     */
    explicit VocabularyService(std::unique_ptr<omop::extract::IDatabaseConnection> connection = nullptr);

    /**
     * @brief Initialize the vocabulary service
     * @param cache_size Size of the concept cache
     */
    void initialize(size_t cache_size = 10000);

    /**
     * @brief Initialize the vocabulary service in in-memory mode (without database)
     * @param cache_size Size of the concept cache
     */
    void initialize_in_memory(size_t cache_size = 10000);

    /**
     * @brief Load vocabulary mappings from configuration
     * @param mapping_config YAML configuration node
     */
    void load_mappings(const std::string& mapping_config);

    /**
     * @brief Load vocabulary mappings from database
     * @param mapping_table Name of the mapping table
     */
    void load_mappings_from_db(const std::string& mapping_table);

    /**
     * @brief Look up concept by ID
     * @param concept_id Concept ID
     * @return std::optional<Concept> Concept if found
     */
    [[nodiscard]] std::optional<Concept> get_concept(int concept_id) const;

    /**
     * @brief Look up concept by code and vocabulary
     * @param concept_code Concept code
     * @param vocabulary_id Vocabulary ID
     * @return std::optional<Concept> Concept if found
     */
    [[nodiscard]] std::optional<Concept> get_concept_by_code(
        const std::string& concept_code,
        const std::string& vocabulary_id);

    /**
     * @brief Get concepts by batch
     * @param concept_ids Vector of concept IDs
     * @return std::vector<std::optional<Concept>> Concepts (empty optional if not found)
     */
    [[nodiscard]] std::vector<std::optional<Concept>> get_concepts_batch(
        const std::vector<int>& concept_ids);

    /**
     * @brief Get concepts by domain
     * @param domain_id Domain ID
     * @return std::vector<Concept> Concepts in domain
     */
    [[nodiscard]] std::vector<Concept> get_concepts_by_domain(const std::string& domain_id) const;

    /**
     * @brief Get concepts by vocabulary
     * @param vocabulary_id Vocabulary ID
     * @return std::vector<Concept> Concepts in vocabulary
     */
    [[nodiscard]] std::vector<Concept> get_concepts_by_vocabulary(const std::string& vocabulary_id);

    /**
     * @brief Get concept relationships
     * @param concept_id Concept ID
     * @param relationship_id Relationship ID
     * @return std::vector<Concept> Related concepts
     */
    [[nodiscard]] std::vector<Concept> get_concept_relationships(
        int concept_id, 
        const std::string& relationship_id);

    /**
     * @brief Get memory usage in bytes
     * @return size_t Memory usage in bytes
     */
    [[nodiscard]] size_t get_memory_usage() const;

    /**
     * @brief Map source value to concept ID
     * @param source_value Source value to map
     * @param vocabulary_name Vocabulary name for mapping
     * @param context Optional context for disambiguation
     * @return int Concept ID (0 if not found)
     */
    [[nodiscard]] int map_to_concept_id(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& context = std::nullopt);

    /**
     * @brief Get all mappings for a source value
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @return std::vector<VocabularyMapping> All matching mappings
     */
    [[nodiscard]] std::vector<VocabularyMapping> get_mappings(
        const std::string& source_value,
        const std::string& vocabulary_name) const;

    /**
     * @brief Find standard concept for a source concept
     * @param source_concept_id Source concept ID
     * @return int Standard concept ID (0 if not found)
     */
    [[nodiscard]] int get_standard_concept(int source_concept_id);

    /**
     * @brief Get descendant concepts
     * @param ancestor_concept_id Ancestor concept ID
     * @param max_levels Maximum levels of descendants (-1 for all)
     * @return std::vector<int> Descendant concept IDs
     */
    [[nodiscard]] std::vector<int> get_descendants(
        int ancestor_concept_id,
        int max_levels = -1);

    /**
     * @brief Get ancestor concepts
     * @param descendant_concept_id Descendant concept ID
     * @param max_levels Maximum levels of ancestors (-1 for all)
     * @return std::vector<int> Ancestor concept IDs
     */
    [[nodiscard]] std::vector<int> get_ancestors(
        int descendant_concept_id,
        int max_levels = -1) const;

    /**
     * @brief Add custom mapping
     * @param mapping Vocabulary mapping to add
     */
    void add_mapping(const VocabularyMapping& mapping);

    /**
     * @brief Clear all cached data
     */
    void clear_cache();

    /**
     * @brief Get cache statistics
     * @return Cache hit rate, size, etc.
     */
    struct CacheStats {
        size_t cache_size;
        size_t max_cache_size;
        size_t hits;
        size_t misses;
        double hit_rate;
    };

    [[nodiscard]] CacheStats get_cache_stats() const;

    /**
     * @brief Validate vocabulary tables exist
     * @return bool True if all required tables exist
     */
    [[nodiscard]] bool validate_vocabulary_tables();

    /**
     * @brief Get vocabulary version information
     * @return std::string Version string
     */
    [[nodiscard]] std::string get_vocabulary_version();

    /**
     * @brief Initialize with advanced features
     * @param config Advanced configuration
     */
    void initialize_advanced(const YAML::Node& config);

    /**
     * @brief Schedule automatic vocabulary updates
     * @param enabled Whether to enable scheduled updates
     */
    void set_scheduled_updates_enabled(bool enabled);

    /**
     * @brief Configure external vocabulary services
     * @param services Configuration for external services
     */
    void configure_external_services(const YAML::Node& services);

    /**
     * @brief Process vocabulary updates from external sources
     * @return Number of updates processed
     */
    size_t process_external_updates();

    /**
     * @brief Enable machine learning for term identification
     * @param model_path Path to ML model
     */
    void enable_ml_term_identification(const std::string& model_path);

    /**
     * @brief Get vocabulary version manager
     * @return VocabularyVersionManager& Version manager
     */
    VocabularyVersionManager& get_version_manager() { return *version_manager_; }

    /**
     * @brief Get conflict resolution engine
     * @return ConflictResolutionEngine& Conflict resolver
     */
    ConflictResolutionEngine& get_conflict_resolver() { return *conflict_resolver_; }

    /**
     * @brief Enable auto-learn mode for unknown medical terms
     * @param enabled Whether to enable auto-learning
     */
    void set_auto_learn_enabled(bool enabled) { auto_learn_enabled_ = enabled; }

    /**
     * @brief Check if auto-learn mode is enabled
     * @return bool True if auto-learning is enabled
     */
    [[nodiscard]] bool is_auto_learn_enabled() const { return auto_learn_enabled_; }

    /**
     * @brief Check if term is likely medical (not in standard English dictionary)
     * @param term Term to check
     * @return bool True if likely medical term
     */
    bool is_medical_term(const std::string& term) const;

    /**
     * @brief Get unrecognised medical terms from the concept cache
     * @return std::vector<std::string> List of unrecognised terms
     */
    [[nodiscard]] std::vector<std::string> get_unrecognised_terms() const;

    /**
     * @brief Process unrecognised terms through medical dictionaries
     * @param max_terms Maximum number of terms to process (0 = all)
     * @return size_t Number of terms successfully mapped
     */
    size_t process_unrecognised_terms(size_t max_terms = 0);

    /**
     * @brief Add medical dictionary for auto-learn lookups
     * @param dictionary_name Name of the dictionary
     * @param connection Database connection to dictionary
     * @param priority Priority order (lower = higher priority)
     */
    void add_medical_dictionary(const std::string& dictionary_name,
                               std::unique_ptr<omop::extract::IDatabaseConnection> connection,
                               int priority = 100);

    /**
     * @brief Record an unrecognised medical term for later processing
     * @param term The medical term
     * @param context Optional context (e.g., field name, domain)
     * @param source_vocabulary Source vocabulary attempted
     */
    void record_unrecognised_term(const std::string& term,
                                 const std::string& context = "",
                                 const std::string& source_vocabulary = "");

    /**
     * @brief Process batch of terms through medical dictionaries
     * @param terms Terms to process
     * @return Map of term to mapping (if found)
     */
    std::unordered_map<std::string, VocabularyMapping> process_terms_batch(
        const std::vector<std::string>& terms);

    /**
     * @brief Get terms pending human review
     * @return Vector of terms requiring review
     */
    std::vector<std::pair<std::string, ConflictResolutionEngine::ConflictResolution>> 
        get_terms_pending_review() const;

    /**
     * @brief Apply human review decision
     */
    void apply_review_decision(const std::string& term, int selected_concept_id);

    /**
     * @brief Get the database connection for use by transformations
     * @return omop::extract::IDatabaseConnection* Raw pointer to the connection (caller does not own)
     */
    omop::extract::IDatabaseConnection* get_connection() const { return connection_.get(); }

    /**
     * @brief Check if a concept is in a given domain
     * @param concept_id Concept ID
     * @param domain_id Domain ID
     * @return bool True if in domain
     */
    bool is_in_domain(int concept_id, const std::string& domain_id) const;

    /**
     * @brief Check if concept has descendants
     * @param concept_id Concept ID
     * @return bool True if concept has descendants
     */
    [[nodiscard]] bool has_descendants(int concept_id);

    /**
     * @brief Check if concept has ancestors
     * @param concept_id Concept ID
     * @return bool True if concept has ancestors
     */
    [[nodiscard]] bool has_ancestors(int concept_id);

    /**
     * @brief Check if one concept is an ancestor of another
     * @param ancestor_id Potential ancestor concept ID
     * @param descendant_id Potential descendant concept ID
     * @return bool True if ancestor_id is an ancestor of descendant_id
     */
    [[nodiscard]] bool is_ancestor_of(int ancestor_id, int descendant_id);

    /**
     * @brief Batch process terms in parallel
     * @param terms Terms to process
     * @param max_threads Maximum threads to use
     * @return Processing results
     */
    std::vector<std::pair<std::string, std::optional<VocabularyMapping>>> 
        parallel_process_terms(const std::vector<std::string>& terms, size_t max_threads = 4);

    /**
     * @brief Advanced fuzzy matching with multiple algorithms
     * @param source_value Source value to match
     * @param vocabulary_name Vocabulary name
     * @param context Optional context
     * @param algorithms Vector of algorithm names to use
     * @return Vector of potential matches with confidence scores
     */
    std::vector<std::pair<VocabularyMapping, float>> advanced_fuzzy_match(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& context = std::nullopt,
        const std::vector<std::string>& algorithms = {"levenshtein", "jaro_winkler", "cosine", "ngram"});

    /**
     * @brief Semantic similarity matching using concept relationships
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @param similarity_threshold Minimum similarity threshold
     * @return Vector of semantically similar concepts
     */
    std::vector<std::pair<int, float>> semantic_similarity_match(
        const std::string& source_value,
        const std::string& vocabulary_name,
        float similarity_threshold = 0.7f);

    /**
     * @brief Context-aware mapping with domain knowledge
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @param context Context information
     * @param domain_hints Vector of domain hints
     * @return Best mapping considering context and domain
     */
    std::optional<VocabularyMapping> context_aware_mapping(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::string& context,
        const std::vector<std::string>& domain_hints = {});

    /**
     * @brief Get mapping quality metrics
     * @param mapping_id Optional mapping ID for specific mapping
     * @return Mapping quality metrics
     */
    struct MappingQualityMetrics {
        float overall_confidence;
        float semantic_accuracy;
        float context_relevance;
        float frequency_score;
        float recency_score;
        float source_reliability;
        std::vector<std::string> quality_indicators;
        std::chrono::system_clock::time_point last_validated;
    };

    MappingQualityMetrics get_mapping_quality(const std::string& source_value, 
                                            const std::string& vocabulary_name) const;

    /**
     * @brief Validate and score existing mappings
     * @param vocabulary_name Vocabulary name to validate
     * @return Number of mappings validated
     */
    size_t validate_existing_mappings(const std::string& vocabulary_name);

    /**
     * @brief Get vocabulary coverage statistics
     * @param vocabulary_name Vocabulary name
     * @return Coverage statistics
     */
    struct VocabularyCoverage {
        size_t total_terms;
        size_t mapped_terms;
        size_t unmapped_terms;
        float coverage_percentage;
        std::vector<std::string> top_unmapped_terms;
        std::chrono::system_clock::time_point last_updated;
    };

    VocabularyCoverage get_vocabulary_coverage(const std::string& vocabulary_name) const;

    /**
     * @brief Suggest mappings for unmapped terms
     * @param vocabulary_name Vocabulary name
     * @param max_suggestions Maximum number of suggestions
     * @return Vector of suggested mappings
     */
    std::vector<VocabularyMapping> suggest_mappings_for_unmapped(
        const std::string& vocabulary_name,
        size_t max_suggestions = 100);

    /**
     * @brief Get mapping confidence distribution
     * @param vocabulary_name Vocabulary name
     * @return Confidence distribution statistics
     */
    struct ConfidenceDistribution {
        size_t high_confidence_mappings;    // >= 0.9
        size_t medium_confidence_mappings;  // 0.7-0.89
        size_t low_confidence_mappings;     // 0.5-0.69
        size_t very_low_confidence_mappings; // < 0.5
        float average_confidence;
        float confidence_std_deviation;
    };

    ConfidenceDistribution get_confidence_distribution(const std::string& vocabulary_name) const;

    /**
     * @brief Enable advanced ML-based mapping
     * @param enabled Whether to enable advanced ML features
     * @param model_config ML model configuration
     */
    void enable_advanced_ml_mapping(bool enabled, const YAML::Node& model_config = YAML::Node());

    /**
     * @brief Get mapping performance metrics
     * @return Performance metrics
     */
    struct MappingPerformanceMetrics {
        std::chrono::microseconds average_lookup_time;
        std::chrono::microseconds average_fuzzy_match_time;
        std::chrono::microseconds average_semantic_match_time;
        size_t cache_hit_rate;
        size_t fuzzy_match_success_rate;
        size_t semantic_match_success_rate;
        std::chrono::system_clock::time_point last_measured;
    };

    MappingPerformanceMetrics get_mapping_performance_metrics() const;

private:
    /**
     * @brief Load concept from database
     * @param concept_id Concept ID
     * @return std::optional<Concept> Concept if found
     */
    std::optional<Concept> load_concept_from_db(int concept_id) const;

    /**
     * @brief Load concept by code from database
     * @param concept_code Concept code
     * @param vocabulary_id Vocabulary ID
     * @return std::optional<Concept> Concept if found
     */
    std::optional<Concept> load_concept_by_code_from_db(
        const std::string& concept_code,
        const std::string& vocabulary_id);

    /**
     * @brief Normalize source value for matching
     * @param value Source value
     * @param case_sensitive Whether to preserve case
     * @return std::string Normalised value
     */
    std::string normalize_value(const std::string& value, bool case_sensitive) const;

    /**
     * @brief Build mapping key
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @param context Optional context
     * @return std::string Mapping key
     */
    std::string build_mapping_key(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& context) const;

    // Database connection
    std::unique_ptr<omop::extract::IDatabaseConnection> connection_;

    // ML-based vocabulary resolution model
    struct MachineLearningModel {
        bool initialized = false;
        std::string model_type = "similarity_based";
        float confidence_threshold = 0.8f;
        
        // Training data for similarity matching
        std::unordered_map<std::string, std::vector<std::string>> training_synonyms;
        std::unordered_map<std::string, int> concept_mappings;
        
        // Model parameters
        struct Parameters {
            float edit_distance_weight = 0.3f;
            float phonetic_similarity_weight = 0.2f;
            float semantic_similarity_weight = 0.4f;
            float frequency_weight = 0.1f;
        } params;
    };
    std::unique_ptr<MachineLearningModel> ml_model_;

    // Caches
    mutable std::unordered_map<int, Concept> concept_cache_;
    std::unordered_map<std::string, int> code_to_concept_cache_;
    std::unordered_map<std::string, int> mapping_cache_;
    std::unordered_map<int, int> standard_concept_cache_;

    // Cache management
    mutable std::shared_mutex cache_mutex_;
    size_t max_cache_size_{10000};
    mutable size_t cache_hits_{0};
    mutable size_t cache_misses_{0};

    // Vocabulary mappings
    std::unordered_map<std::string, std::vector<VocabularyMapping>> vocabulary_mappings_;
    mutable std::shared_mutex mapping_mutex_;

    // Configuration
    bool case_sensitive_matching_{false};
    std::string vocabulary_schema_{"vocab"};  // Changed from "cdm" to "vocab"

    // Auto-learn module
    struct UnrecognisedTerm {
        std::string term;
        std::string context;
        std::string source_vocabulary;
        std::chrono::system_clock::time_point first_seen;
        size_t occurrence_count{1};
    };

    struct MedicalDictionary {
        std::string name;
        std::unique_ptr<omop::extract::IDatabaseConnection> connection;
        int priority;
    };

    bool auto_learn_enabled_{false};
    std::unordered_map<std::string, UnrecognisedTerm> unrecognised_terms_;
    std::vector<MedicalDictionary> medical_dictionaries_;
    mutable std::shared_mutex auto_learn_mutex_;

    /**
     * @brief Look up term in medical dictionary
     * @param term Medical term to look up
     * @param dictionary Dictionary to search
     * @return std::optional<VocabularyMapping> Mapping if found
     */
    std::optional<VocabularyMapping> lookup_in_dictionary(
        const std::string& term,
        const MedicalDictionary& dictionary);

    /**
     * @brief Query external vocabulary service
     * @param term Term to look up
     * @param service_name Name of external service
     * @return Optional mapping if found
     */
    std::optional<VocabularyMapping> query_external_service(
        const std::string& term,
        const std::string& service_name);

    // Advanced features
    std::unique_ptr<VocabularyUpdateScheduler> update_scheduler_;
    std::unique_ptr<VocabularyVersionManager> version_manager_;
    std::unique_ptr<ConflictResolutionEngine> conflict_resolver_;
    std::unique_ptr<ml::MedicalTermClassifier> ml_classifier_;
    
    // External service connections
    std::unordered_map<std::string, std::unique_ptr<omop::extract::IDatabaseConnection>> external_services_;
    std::vector<std::string> external_service_priority_;

    // Pending review queue
    mutable std::shared_mutex review_mutex_;
    std::unordered_map<std::string, ConflictResolutionEngine::ConflictResolution> pending_review_;

    // Advanced ML and algorithm features
    bool advanced_ml_enabled_{false};
    YAML::Node ml_model_config_;
    
    // Performance tracking
    mutable std::shared_mutex performance_mutex_;
    std::vector<std::chrono::microseconds> lookup_times_;
    std::vector<std::chrono::microseconds> fuzzy_match_times_;
    std::vector<std::chrono::microseconds> semantic_match_times_;
    size_t fuzzy_match_successes_{0};
    size_t fuzzy_match_attempts_{0};
    size_t semantic_match_successes_{0};
    size_t semantic_match_attempts_{0};
    
    // Quality metrics cache
    mutable std::shared_mutex quality_mutex_;
    mutable std::unordered_map<std::string, MappingQualityMetrics> quality_cache_;
    mutable std::unordered_map<std::string, VocabularyCoverage> coverage_cache_;
    mutable std::unordered_map<std::string, ConfidenceDistribution> confidence_cache_;
    
    // Advanced algorithms configuration
    struct AlgorithmConfig {
        bool enable_levenshtein{true};
        bool enable_jaro_winkler{true};
        bool enable_cosine{true};
        bool enable_ngram{true};
        bool enable_semantic{true};
        float levenshtein_threshold{0.8f};
        float jaro_winkler_threshold{0.8f};
        float cosine_threshold{0.7f};
        float ngram_threshold{0.6f};
        float semantic_threshold{0.7f};
        size_t max_fuzzy_candidates{10};
        size_t max_semantic_candidates{5};
    };
    AlgorithmConfig algorithm_config_;

    /**
     * @brief Calculate Levenshtein distance between two strings
     * @param s1 First string
     * @param s2 Second string
     * @return Distance value
     */
    int calculate_levenshtein_distance(const std::string& s1, const std::string& s2) const;

    /**
     * @brief Calculate Jaro-Winkler similarity between two strings
     * @param s1 First string
     * @param s2 Second string
     * @return Similarity score (0-1)
     */
    float calculate_jaro_winkler_similarity(const std::string& s1, const std::string& s2) const;

    /**
     * @brief Calculate cosine similarity between two strings
     * @param s1 First string
     * @param s2 Second string
     * @return Similarity score (0-1)
     */
    float calculate_cosine_similarity(const std::string& s1, const std::string& s2) const;

    /**
     * @brief Calculate n-gram similarity between two strings
     * @param s1 First string
     * @param s2 Second string
     * @param n N-gram size (default 3)
     * @return Similarity score (0-1)
     */
    float calculate_ngram_similarity(const std::string& s1, const std::string& s2, int n = 3) const;

    /**
     * @brief Extract n-grams from string
     * @param str Input string
     * @param n N-gram size
     * @return Vector of n-grams
     */
    std::vector<std::string> extract_ngrams(const std::string& str, int n) const;

    /**
     * @brief Calculate semantic similarity using concept relationships
     * @param concept1_id First concept ID
     * @param concept2_id Second concept ID
     * @return Similarity score (0-1)
     */
    float calculate_semantic_similarity(int concept1_id, int concept2_id) const;

    /**
     * @brief Get concept frequency in usage data
     * @param concept_id Concept ID
     * @return Frequency score
     */
    float get_concept_frequency_score(int concept_id) const;

    /**
     * @brief Get concept recency score
     * @param concept_id Concept ID
     * @return Recency score
     */
    float get_concept_recency_score(int concept_id) const;

    /**
     * @brief Get source reliability score
     * @param source_vocabulary Source vocabulary name
     * @return Reliability score
     */
    float get_source_reliability_score(const std::string& source_vocabulary) const;

    /**
     * @brief Update performance metrics
     * @param metric_type Type of metric to update
     * @param duration Duration to record
     * @param success Whether the operation was successful
     */
    void update_performance_metrics(const std::string& metric_type, 
                                  std::chrono::microseconds duration, 
                                  bool success);

    /**
     * @brief Calculate confidence score for mapping
     * @param mapping Vocabulary mapping
     * @param context Context information
     * @return Confidence score
     */
    float calculate_mapping_confidence(const VocabularyMapping& mapping, 
                                     const std::string& context = "") const;

    /**
     * @brief Validate mapping quality
     * @param mapping Vocabulary mapping to validate
     * @return Quality validation result
     */
    bool validate_mapping_quality(const VocabularyMapping& mapping) const;

    /**
     * @brief Get domain-specific mapping suggestions
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @param domain_hints Domain hints
     * @return Vector of suggested mappings
     */
    std::vector<VocabularyMapping> get_domain_specific_suggestions(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::vector<std::string>& domain_hints) const;

    /**
     * @brief Load advanced ML models
     * @param config ML configuration
     */
    void load_advanced_ml_models(const YAML::Node& config);

    /**
     * @brief Load training data for ML model from database
     */
    void load_ml_training_data();
    
    /**
     * @brief Initialize ML model with basic synonym mappings
     */
    void initialize_basic_ml_mappings();

    /**
     * @brief Preprocess text for ML analysis
     * @param text Input text
     * @return Preprocessed text
     */
    std::string preprocess_text_for_ml(const std::string& text) const;

    /**
     * @brief Extract contextual features
     * @param source_value Source value
     * @param context Context information
     * @return Feature vector
     */
    std::vector<float> extract_contextual_features(const std::string& source_value, 
                                                 const std::string& context) const;
};

/**
 * @brief Vocabulary-based validator
 *
 * Validates that values can be mapped to valid OMOP concepts.
 */
class VocabularyValidator {
public:
    /**
     * @brief Constructor
     * @param vocabulary_service Reference to vocabulary service
     */
    explicit VocabularyValidator(VocabularyService& vocabulary_service)
        : vocabulary_service_(vocabulary_service) {}

    /**
     * @brief Validate concept ID
     * @param concept_id Concept ID to validate
     * @param expected_domain Expected domain (optional)
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_concept_id(
        int concept_id,
        const std::optional<std::string>& expected_domain = std::nullopt);

    /**
     * @brief Validate source value can be mapped
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @return bool True if can be mapped
     */
    [[nodiscard]] bool validate_mapping_exists(
        const std::string& source_value,
        const std::string& vocabulary_name);

    /**
     * @brief Validate concept is standard
     * @param concept_id Concept ID
     * @return bool True if standard concept
     */
    [[nodiscard]] bool validate_standard_concept(int concept_id);

    /**
     * @brief Get validation errors for a value
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @param expected_domain Expected domain
     * @return std::vector<std::string> List of validation errors
     */
    [[nodiscard]] std::vector<std::string> get_validation_errors(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& expected_domain = std::nullopt);

private:
    VocabularyService& vocabulary_service_;
};

/**
 * @brief Singleton accessor for global vocabulary service
 */
class VocabularyServiceManager {
public:
    /**
     * @brief Get the singleton instance
     * @return VocabularyService& Reference to the vocabulary service
     */
    [[nodiscard]] static VocabularyService& instance() {
        if (!instance_) {
            throw common::ConfigurationException(
                "VocabularyService not initialised. Call initialize() first.");
        }
        return *instance_;
    }

    /**
     * @brief Initialize the vocabulary service
     * @param connection Database connection
     * @param cache_size Cache size
     */
    static void initialize(
        std::unique_ptr<omop::extract::IDatabaseConnection> connection,
        size_t cache_size = 10000) {
        instance_ = std::make_unique<VocabularyService>(std::move(connection));
        instance_->initialize(cache_size);
    }

    /**
     * @brief Check if initialised
     * @return bool True if initialised
     */
    [[nodiscard]] static bool is_initialised() {
        return TransformationUtils::is_vocabulary_initialised();
    }

    /**
     * @brief Reset the service
     */
    static void reset() {
        instance_.reset();
    }

    /**
     * @brief Check if auto-learn mode is enabled
     * @return bool True if auto-learning is enabled
     */
    [[nodiscard]] static bool is_auto_learn_enabled() {
        return TransformationUtils::is_auto_learn_enabled();
    }

private:
    static std::unique_ptr<VocabularyService> instance_;
};

} // namespace omop::transform