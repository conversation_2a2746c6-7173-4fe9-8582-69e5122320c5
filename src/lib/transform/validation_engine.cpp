#include "transform/validation_engine.h"
#include "transform/transformations.h"
#include "transform/vocabulary_service.h"
#include "common/logging.h"
#include "common/validation.h"
#include <regex>
#include <set>
#include <yaml-cpp/yaml.h>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cmath>

namespace omop::transform {

// RequiredFieldValidator implementation
omop::common::ValidationResult RequiredFieldValidator::validate(const std::any& value, core::ProcessingContext& context) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    omop::common::ValidationResult result;
    
    // Check if field is present
    if (!value.has_value()) {
        result.add_error(field_name_, "Required field is missing", "required_field", "error");
        result.set_quality_score(0.0);
    } else {
        result.set_quality_score(100.0);
    }
    
    // Check data type if expected_type_ is set
    if (!expected_type_.empty() && expected_type_ != "required") {
        bool type_valid = false;
        if (expected_type_ == "string") {
            type_valid = (value.type() == typeid(std::string));
        } else if (expected_type_ == "int" || expected_type_ == "integer") {
            type_valid = (value.type() == typeid(int) || value.type() == typeid(int64_t));
        } else if (expected_type_ == "double" || expected_type_ == "float") {
            type_valid = (value.type() == typeid(double) || value.type() == typeid(float));
        } else if (expected_type_ == "bool" || expected_type_ == "boolean") {
            type_valid = (value.type() == typeid(bool));
        }
        
        if (!type_valid) {
            result.add_error(field_name_, 
                std::format("Expected type '{}' but got different type", expected_type_), 
                "type_validation", "error");
            result.set_quality_score(0.0);
            return result;
        }
    }
    
    // Check if field is empty string
    if (value.type() == typeid(std::string)) {
        std::string str_val = std::any_cast<std::string>(value);
        if (str_val.empty() && !allow_empty_string_) {
            result.add_error(field_name_, "Required field is empty", "required_field", "error");
            result.set_quality_score(0.0);
            return result;
        }
        
        // Validate string length
        if (min_length_ > 0 && str_val.length() < static_cast<size_t>(min_length_)) {
            result.add_error(field_name_, 
                std::format("String too short (minimum {} characters)", min_length_), 
                "length_validation", "error");
            result.set_quality_score(50.0);
        }
        
        if (max_length_ > 0 && str_val.length() > static_cast<size_t>(max_length_)) {
            result.add_error(field_name_, 
                std::format("String too long (maximum {} characters)", max_length_), 
                "length_validation", "error");
            result.set_quality_score(50.0);
        }
        
        // Validate pattern
        if (!pattern_.empty()) {
            try {
                std::regex pattern_regex(pattern_);
                if (!std::regex_match(str_val, pattern_regex)) {
                    result.add_error(field_name_, 
                        std::format("String does not match required pattern"), 
                        "pattern_validation", "error");
                    result.set_quality_score(50.0);
                }
            } catch (const std::regex_error& e) {
                result.add_error(field_name_, 
                    std::format("Invalid regex pattern: {}", e.what()), 
                    "pattern_validation", "error");
                result.set_quality_score(0.0);
            }
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    result.set_validation_duration(duration);
    result.set_validation_timestamp(std::chrono::system_clock::now());
    
    return result;
}

void RequiredFieldValidator::configure(const YAML::Node& params) {
    if (params["allow_empty_string"]) {
        allow_empty_string_ = params["allow_empty_string"].as<bool>();
    }
    if (params["min_length"]) {
        min_length_ = params["min_length"].as<int>();
    }
    if (params["max_length"]) {
        max_length_ = params["max_length"].as<int>();
    }
    if (params["pattern"]) {
        pattern_ = params["pattern"].as<std::string>();
    }
    if (params["type"]) {
        expected_type_ = params["type"].as<std::string>();
    }
    if (params["field_name"]) {
        field_name_ = params["field_name"].as<std::string>();
    }
}

// DataTypeValidator implementation
omop::common::ValidationResult DataTypeValidator::validate(const std::any& value, core::ProcessingContext& context) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    omop::common::ValidationResult result;
    if (!value.has_value()) {
        if (required_) {
            result.add_error(field_name_, "Value is required", "not_null", "error");
            result.set_quality_score(0.0);
        }
        return result;
    }
    
    bool valid = false;
    std::string actual_type = get_actual_type(value);
    switch (expected_type_) {
        case DataType::String:
            valid = value.type() == typeid(std::string) || value.type() == typeid(const char*);
            break;
        case DataType::Integer:
            valid = value.type() == typeid(int) || value.type() == typeid(int64_t);
            if (!valid && value.type() == typeid(std::string)) {
                try { std::stoi(std::any_cast<std::string>(value)); valid = true; } catch (...) {}
            }
            break;
        case DataType::Float:
            valid = value.type() == typeid(double) || value.type() == typeid(float);
            if (!valid && value.type() == typeid(std::string)) {
                try { std::stod(std::any_cast<std::string>(value)); valid = true; } catch (...) {}
            }
            break;
        case DataType::Boolean:
            valid = value.type() == typeid(bool);
            if (!valid && value.type() == typeid(std::string)) {
                std::string str_val = std::any_cast<std::string>(value);
                valid = (str_val == "true" || str_val == "false" || str_val == "1" || str_val == "0");
            }
            break;
        case DataType::Date:
        case DataType::DateTime:
        case DataType::Time:
            if (value.type() == typeid(std::string)) {
                std::string str_val = std::any_cast<std::string>(value);
                valid = validate_date_format(str_val);
            } else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
                valid = true;
            }
            break;
    }
    
    if (!valid) {
        result.add_error(field_name_, "Type mismatch", "type_validation", "error");
        result.set_quality_score(0.0);
    } else {
        result.set_quality_score(100.0);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    result.set_validation_duration(duration);
    result.set_validation_timestamp(std::chrono::system_clock::now());
    
    return result;
}

void DataTypeValidator::configure(const YAML::Node& params) {
    if (params["type"]) {
        configure_type(params["type"].as<std::string>());
    }
    if (params["expected_type"]) {
        configure_type(params["expected_type"].as<std::string>());
    }
    if (params["required"]) {
        required_ = params["required"].as<bool>();
    }
    if (params["date_formats"]) {
        date_formats_ = params["date_formats"].as<std::vector<std::string>>();
    }
}

void DataTypeValidator::configure_type(const std::string& type_str) {
    static const std::unordered_map<std::string, DataType> type_map = {
        {"string", DataType::String}, {"integer", DataType::Integer}, {"int", DataType::Integer},
        {"float", DataType::Float}, {"double", DataType::Float}, {"boolean", DataType::Boolean},
        {"bool", DataType::Boolean}, {"date", DataType::Date}, {"datetime", DataType::DateTime}, {"time", DataType::Time}
    };
    auto it = type_map.find(type_str);
    if (it != type_map.end()) expected_type_ = it->second;
}

std::string DataTypeValidator::get_type_name(DataType type) const {
    switch (type) {
        case DataType::String: return "string";
        case DataType::Integer: return "integer";
        case DataType::Float: return "float";
        case DataType::Boolean: return "boolean";
        case DataType::Date: return "date";
        case DataType::DateTime: return "datetime";
        case DataType::Time: return "time";
        default: return "unknown";
    }
}

std::string DataTypeValidator::get_actual_type(const std::any& value) const {
    if (value.type() == typeid(std::string)) return "string";
    if (value.type() == typeid(int)) return "int";
    if (value.type() == typeid(int64_t)) return "int64";
    if (value.type() == typeid(double)) return "double";
    if (value.type() == typeid(float)) return "float";
    if (value.type() == typeid(bool)) return "bool";
    if (value.type() == typeid(std::chrono::system_clock::time_point)) return "datetime";
    return value.type().name();
}

bool DataTypeValidator::validate_date_format(const std::string& date_str) {
    // Simple date format validation - can be enhanced
    if (date_str.empty()) return false;
    
    // Check if it's a valid date format (basic check)
    std::regex date_pattern(R"(\d{4}-\d{2}-\d{2})");
    return std::regex_match(date_str, date_pattern);
}

// RangeValidator implementation
omop::common::ValidationResult RangeValidator::validate(const std::any& value, core::ProcessingContext& context) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    omop::common::ValidationResult result;
    
    if (!value.has_value()) {
        result.add_error(field_name_, "Value is required for range validation", "range_validation", "error");
        result.set_quality_score(0.0);
        return result;
    }
    
    double numeric_value = 0.0;
    bool is_numeric = false;
    
    // Convert value to numeric
    if (value.type() == typeid(int)) {
        numeric_value = static_cast<double>(std::any_cast<int>(value));
        is_numeric = true;
    } else if (value.type() == typeid(int64_t)) {
        numeric_value = static_cast<double>(std::any_cast<int64_t>(value));
        is_numeric = true;
    } else if (value.type() == typeid(double)) {
        numeric_value = std::any_cast<double>(value);
        is_numeric = true;
    } else if (value.type() == typeid(float)) {
        numeric_value = static_cast<double>(std::any_cast<float>(value));
        is_numeric = true;
    } else if (value.type() == typeid(std::string)) {
        try {
            numeric_value = std::stod(std::any_cast<std::string>(value));
            is_numeric = true;
        } catch (...) {
            // Not a numeric string
        }
    }
    
    if (!is_numeric) {
        result.add_error(field_name_, "Value must be numeric for range validation", "range_validation", "error");
        result.set_quality_score(0.0);
        return result;
    }
    
    // Check allowed values first
    if (!allowed_values_.empty()) {
        bool found = false;
        for (double allowed : allowed_values_) {
            if (std::abs(numeric_value - allowed) < 1e-9) {
                found = true;
                break;
            }
        }
        if (!found) {
            result.add_error(field_name_, 
                std::format("Value {} is not in allowed list", numeric_value), 
                "range_validation", "error");
            result.set_quality_score(0.0);
            return result;
        }
    }
    
    // Check min/max range
    if (min_value_.has_value() && numeric_value < min_value_.value()) {
        result.add_error(field_name_, 
            std::format("Value {} is below minimum {}", numeric_value, min_value_.value()), 
            "range_validation", "error");
        result.set_quality_score(50.0);
    }
    
    if (max_value_.has_value() && numeric_value > max_value_.value()) {
        result.add_error(field_name_, 
            std::format("Value {} is above maximum {}", numeric_value, max_value_.value()), 
            "range_validation", "error");
        result.set_quality_score(50.0);
    }
    
    if (result.is_valid()) {
        result.set_quality_score(100.0);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    result.set_validation_duration(duration);
    result.set_validation_timestamp(std::chrono::system_clock::now());
    
    return result;
}

void RangeValidator::configure(const YAML::Node& params) {
    if (params["min_value"]) {
        min_value_ = params["min_value"].as<double>();
    }
    if (params["max_value"]) {
        max_value_ = params["max_value"].as<double>();
    }
    if (params["allowed_values"]) {
        allowed_values_ = params["allowed_values"].as<std::vector<double>>();
    }
    if (params["field_name"]) {
        field_name_ = params["field_name"].as<std::string>();
    }
}

// PatternValidator implementation
omop::common::ValidationResult PatternValidator::validate(const std::any& value, core::ProcessingContext& context) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    omop::common::ValidationResult result;
    
    if (!value.has_value()) {
        result.add_error(field_name_, "Value is required for pattern validation", "pattern_validation", "error");
        result.set_quality_score(0.0);
        return result;
    }
    
    if (value.type() != typeid(std::string)) {
        result.add_error(field_name_, "Pattern validation only applies to string values", "pattern_validation", "error");
        result.set_quality_score(0.0);
        return result;
    }
    
    std::string str_val = std::any_cast<std::string>(value);
    
    try {
        if (!std::regex_match(str_val, regex_pattern_)) {
            result.add_error(field_name_, 
                std::format("String does not match required pattern: {}", pattern_), 
                "pattern_validation", "error");
            result.set_quality_score(0.0);
        } else {
            result.set_quality_score(100.0);
        }
    } catch (const std::regex_error& e) {
        result.add_error(field_name_, 
            std::format("Invalid regex pattern: {}", e.what()), 
            "pattern_validation", "error");
        result.set_quality_score(0.0);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    result.set_validation_duration(duration);
    result.set_validation_timestamp(std::chrono::system_clock::now());
    
    return result;
}

void PatternValidator::configure(const YAML::Node& params) {
    if (params["pattern"]) {
        pattern_ = params["pattern"].as<std::string>();
        try {
            regex_pattern_ = std::regex(pattern_);
        } catch (const std::regex_error& e) {
            // Log error but don't throw
        }
    }
    if (params["case_sensitive"]) {
        case_sensitive_ = params["case_sensitive"].as<bool>();
    }
    if (params["field_name"]) {
        field_name_ = params["field_name"].as<std::string>();
    }
}

// LengthValidator implementation
omop::common::ValidationResult LengthValidator::validate(const std::any& value, core::ProcessingContext& context) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    omop::common::ValidationResult result;
    
    if (!value.has_value()) {
        result.add_error(field_name_, "Value is required for length validation", "length_validation", "error");
        result.set_quality_score(0.0);
        return result;
    }
    
    if (value.type() != typeid(std::string)) {
        result.add_error(field_name_, "Length validation only applies to string values", "length_validation", "error");
        result.set_quality_score(0.0);
        return result;
    }
    
    std::string str_val = std::any_cast<std::string>(value);
    size_t length = str_val.length();
    
    // Check exact length if specified
    if (exact_length_.has_value() && length != exact_length_.value()) {
        result.add_error(field_name_, 
            std::format("String length must be exactly {} characters, got {}", exact_length_.value(), length), 
            "length_validation", "error");
        result.set_quality_score(0.0);
        return result;
    }
    
    // Check min length
    if (min_length_.has_value() && length < min_length_.value()) {
        result.add_error(field_name_, 
            std::format("String length must be at least {} characters, got {}", min_length_.value(), length), 
            "length_validation", "error");
        result.set_quality_score(50.0);
    }
    
    // Check max length
    if (max_length_.has_value() && length > max_length_.value()) {
        result.add_error(field_name_, 
            std::format("String length must be at most {} characters, got {}", max_length_.value(), length), 
            "length_validation", "error");
        result.set_quality_score(50.0);
    }
    
    if (result.is_valid()) {
        result.set_quality_score(100.0);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    result.set_validation_duration(duration);
    result.set_validation_timestamp(std::chrono::system_clock::now());
    
    return result;
}

void LengthValidator::configure(const YAML::Node& params) {
    if (params["min_length"]) {
        min_length_ = params["min_length"].as<size_t>();
    }
    if (params["max_length"]) {
        max_length_ = params["max_length"].as<size_t>();
    }
    if (params["exact_length"]) {
        exact_length_ = params["exact_length"].as<size_t>();
    }
    if (params["field_name"]) {
        field_name_ = params["field_name"].as<std::string>();
    }
}

// VocabularyFieldValidator implementation
omop::common::ValidationResult VocabularyFieldValidator::validate(const std::any& value, core::ProcessingContext& context) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    omop::common::ValidationResult result;
    
    if (!value.has_value()) {
        if (validate_concept_id_) {
            result.add_error(field_name_, "Concept ID is required", "vocabulary_validation", "error");
            result.set_quality_score(0.0);
        }
        return result;
    }
    
    // Basic vocabulary validation - in a real implementation, this would check against actual vocabulary tables
    if (validate_concept_id_) {
        if (value.type() == typeid(int) || value.type() == typeid(int64_t)) {
            int64_t concept_id = (value.type() == typeid(int)) ? 
                static_cast<int64_t>(std::any_cast<int>(value)) : 
                std::any_cast<int64_t>(value);
            
            if (concept_id <= 0) {
                result.add_error(field_name_, "Concept ID must be positive", "vocabulary_validation", "error");
                result.set_quality_score(0.0);
            } else {
                result.set_quality_score(100.0);
                result.add_quality_indicator("valid_concept_id");
            }
        } else {
            result.add_error(field_name_, "Concept ID must be numeric", "vocabulary_validation", "error");
            result.set_quality_score(0.0);
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    result.set_validation_duration(duration);
    result.set_validation_timestamp(std::chrono::system_clock::now());
    
    return result;
}

void VocabularyFieldValidator::configure(const YAML::Node& params) {
    if (params["vocabulary"]) {
        vocabulary_name_ = params["vocabulary"].as<std::string>();
    }
    if (params["expected_domain"]) {
        expected_domain_ = params["expected_domain"].as<std::string>();
    }
    if (params["validate_concept_id"]) {
        validate_concept_id_ = params["validate_concept_id"].as<bool>();
    }
    if (params["require_standard"]) {
        require_standard_ = params["require_standard"].as<bool>();
    }
    if (params["field_name"]) {
        field_name_ = params["field_name"].as<std::string>();
    }
}

// CrossFieldValidator implementation
omop::common::ValidationResult CrossFieldValidator::validate(const std::any& value, core::ProcessingContext& context) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    omop::common::ValidationResult result;
    
    if (!value.has_value()) {
        result.add_error(field_name_, "Value is required for cross-field validation", "cross_field_validation", "error");
        result.set_quality_score(0.0);
        return result;
    }
    
    // Enhanced cross-field validation with full record context
    if (!condition_.empty()) {
        // Try to get the full record from context for comprehensive cross-field validation
        core::Record context_record{};
        auto record_data = context.get_data("current_record");
        if (record_data && record_data->has_value()) {
            try {
                if (record_data->type() == typeid(core::Record)) {
                    context_record = std::any_cast<core::Record>(*record_data);
                } else if (record_data->type() == typeid(const core::Record)) {
                    context_record = std::any_cast<const core::Record>(*record_data);
                }
            } catch (const std::bad_any_cast& e) {
                // Fallback to empty record if cast fails
                auto logger = common::Logger::get("omop-validation");
                logger->debug("Failed to cast record in cross-field validation: {}", e.what());
            }
        }
        
        // Evaluate condition with full record context
        bool condition_met = evaluate_condition(condition_, context_record, field_name_, value);
        if (!condition_met) {
            result.add_error(field_name_, 
                std::format("Cross-field validation condition failed: {}", condition_), 
                "cross_field_validation", "error");
            result.set_quality_score(0.0);
        } else {
            result.set_quality_score(100.0);
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    result.set_validation_duration(duration);
    result.set_validation_timestamp(std::chrono::system_clock::now());
    
    return result;
}

void CrossFieldValidator::configure(const YAML::Node& params) {
    if (params["related_fields"]) {
        related_fields_ = params["related_fields"].as<std::vector<std::string>>();
    }
    if (params["condition"]) {
        condition_ = params["condition"].as<std::string>();
    }
    if (params["field_name"]) {
        field_name_ = params["field_name"].as<std::string>();
    }
}

bool CrossFieldValidator::evaluate_condition(const std::string& condition,
                                           const core::Record& record,
                                           const std::string& current_field,
                                           const std::any& current_value) const {
    // Simple condition parser for common cases
    // Supports: field1 < field2, field1 == field2, field1 != field2, etc.
    
    std::regex pattern(R"((\w+)\s*(<=|>=|<|>|==|!=)\s*(\w+))");
    std::smatch match;
    
    if (std::regex_match(condition, match, pattern)) {
        std::string left_field = match[1].str();
        std::string op = match[2].str();
        std::string right_field = match[3].str();
        
        // Get field values
        std::any left_value = (left_field == current_field) ? current_value : record.getField(left_field);
        std::any right_value = (right_field == current_field) ? current_value : record.getField(right_field);
        
        // Compare values based on type
        return compare_field_values(left_value, right_value, op);
    }
    
    return true; // If condition cannot be parsed, assume it passes
}

bool CrossFieldValidator::compare_field_values(const std::any& left_value, 
                                             const std::any& right_value, 
                                             const std::string& op) const {
    // Simple comparison implementation
    // In a real implementation, this would handle different data types more robustly
    
    if (!left_value.has_value() || !right_value.has_value()) {
        return false;
    }
    
    // For now, convert both to strings for comparison
    std::string left_str, right_str;
    
    if (left_value.type() == typeid(std::string)) {
        left_str = std::any_cast<std::string>(left_value);
    } else if (left_value.type() == typeid(int)) {
        left_str = std::to_string(std::any_cast<int>(left_value));
    } else if (left_value.type() == typeid(double)) {
        left_str = std::to_string(std::any_cast<double>(left_value));
    } else {
        return false;
    }
    
    if (right_value.type() == typeid(std::string)) {
        right_str = std::any_cast<std::string>(right_value);
    } else if (right_value.type() == typeid(int)) {
        right_str = std::to_string(std::any_cast<int>(right_value));
    } else if (right_value.type() == typeid(double)) {
        right_str = std::to_string(std::any_cast<double>(right_value));
    } else {
        return false;
    }
    
    if (op == "==") {
        return left_str == right_str;
    } else if (op == "!=") {
        return left_str != right_str;
    } else if (op == "<") {
        return left_str < right_str;
    } else if (op == ">") {
        return left_str > right_str;
    } else if (op == "<=") {
        return left_str <= right_str;
    } else if (op == ">=") {
        return left_str >= right_str;
    }
    
    return false;
}

// CustomValidator implementation
omop::common::ValidationResult CustomValidator::validate(const std::any& value, core::ProcessingContext& context) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    omop::common::ValidationResult result;
    
    if (validation_func_) {
        bool valid = validation_func_(value, context);
        if (!valid) {
            result.add_error(field_name_, 
                error_message_.empty() ? "Custom validation failed" : error_message_, 
                "custom", "error");
            result.set_quality_score(0.0);
        } else {
            result.set_quality_score(100.0);
        }
    } else {
        result.add_error(field_name_, "No custom validation function set", "custom", "error");
        result.set_quality_score(0.0);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    result.set_validation_duration(duration);
    result.set_validation_timestamp(std::chrono::system_clock::now());
    
    return result;
}

void CustomValidator::configure(const YAML::Node& params) {
    if (params["error_message"]) {
        error_message_ = params["error_message"].as<std::string>();
    }
    if (params["field_name"]) {
        field_name_ = params["field_name"].as<std::string>();
    }
}

// ValidationEngine implementation
ValidationEngine::ValidationEngine() {
    // Register built-in validators
    register_validator("required", []() { return std::make_unique<RequiredFieldValidator>(); });
    register_validator("not_null", []() { return std::make_unique<RequiredFieldValidator>(); });
    register_validator("datatype", []() { return std::make_unique<DataTypeValidator>(); });
    register_validator("data_type", []() { return std::make_unique<DataTypeValidator>(); });
    register_validator("range", []() { return std::make_unique<RangeValidator>(); });
    register_validator("pattern", []() { return std::make_unique<PatternValidator>(); });
    register_validator("length", []() { return std::make_unique<LengthValidator>(); });
    register_validator("vocabulary", []() { return std::make_unique<VocabularyFieldValidator>(); });
    register_validator("crossfield", []() { return std::make_unique<CrossFieldValidator>(); });
    register_validator("custom", []() { return std::make_unique<CustomValidator>(); });
}

void ValidationEngine::load_validation_rules(const YAML::Node& config) {
    auto logger = common::Logger::get("omop-validation");
    logger->info("Loading validation rules from configuration");
    
    // Handle new format: config["rules"] with validators array
    if (config["rules"]) {
        const auto& rules_node = config["rules"];
        for (const auto& rule_node : rules_node) {
            if (!rule_node["field"] || !rule_node["validators"]) {
                logger->warn("Invalid rule format: missing 'field' or 'validators'");
                continue;
            }
            
            std::string field_name = rule_node["field"].as<std::string>();
            const auto& validators_array = rule_node["validators"];
            
            for (const auto& validator_config : validators_array) {
                if (!validator_config["type"]) {
                    logger->warn("Invalid validator format: missing 'type' for field: {}", field_name);
                    continue;
                }
                
                std::string validator_type = validator_config["type"].as<std::string>();
                auto validator = create_validator(validator_type);
                if (validator) {
                    validator->set_field_name(field_name);
                    validator->configure(validator_config);
                    
                    field_validators_[field_name].push_back(std::move(validator));
                    logger->debug("Added {} validator for field: {}", validator_type, field_name);
                } else {
                    logger->warn("Unknown validator type: {} for field: {}", validator_type, field_name);
                }
            }
        }
    }
    // Handle legacy format: config["validation_rules"] with direct type
    else if (config["validation_rules"]) {
        const auto& rules_node = config["validation_rules"];
        for (const auto& rule_node : rules_node) {
            std::string field_name = rule_node["field"].as<std::string>();
            std::string rule_type = rule_node["type"].as<std::string>();
            
            auto validator = create_validator(rule_type);
            if (validator) {
                validator->set_field_name(field_name);
                validator->configure(rule_node);
                
                field_validators_[field_name].push_back(std::move(validator));
            } else {
                logger->warn("Unknown validator type: {} for field: {}", rule_type, field_name);
            }
        }
    }
}

omop::common::ValidationResult ValidationEngine::validate_record(const core::Record& record, core::ProcessingContext& context) {
    omop::common::ValidationResult result;
    std::lock_guard<std::mutex> lock(mutex_);
    
    for (const auto& [field_name, validators] : field_validators_) {
        auto field_value_opt = record.getFieldOptional(field_name);
        std::any field_value = field_value_opt.value_or(std::any{});
        
        for (const auto& validator : validators) {
            auto val_result = validator->validate(field_value, context);
            result.merge(val_result);
        }
    }
    
    // Set metadata for the overall result
    result.set_validation_timestamp(std::chrono::system_clock::now());
    
    return result;
}

omop::common::ValidationResult ValidationEngine::validate_field(const std::string& field_name, const std::any& value, core::ProcessingContext& context) {
    omop::common::ValidationResult result;
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = field_validators_.find(field_name);
    if (it != field_validators_.end()) {
        for (const auto& validator : it->second) {
            auto val_result = validator->validate(value, context);
            result.merge(val_result);
        }
    }
    
    return result;
}

void ValidationEngine::register_validator(const std::string& type, std::function<std::unique_ptr<IFieldValidator>()> factory) {
    std::lock_guard<std::mutex> lock(mutex_);
    validator_factories_[type] = std::move(factory);
}

std::vector<std::string> ValidationEngine::get_registered_types() const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<std::string> types;
    for (const auto& pair : validator_factories_) types.push_back(pair.first);
    return types;
}

void ValidationEngine::clear_rules() {
    std::lock_guard<std::mutex> lock(mutex_);
    field_validators_.clear();
}

std::unique_ptr<IFieldValidator> ValidationEngine::create_validator(const std::string& type) {
    auto it = validator_factories_.find(type);
    if (it != validator_factories_.end()) return it->second();
    auto logger = common::Logger::get("omop-transform");
    logger->warn("Unknown validator type: {}", type);
    return nullptr;
}

void ValidationEngine::load_configuration(const YAML::Node& config) {
    auto logger = common::Logger::get("omop-validation");
    logger->info("Loading validation configuration");
    
    // Load validation rules from configuration
    if (config["validation_rules"]) {
        load_validation_rules(config["validation_rules"]);
    } else {
        // Fallback to loading entire config as validation rules
        load_validation_rules(config);
    }
    
    // Configure additional validation settings
    if (config["strict_mode"]) {
        bool strict_mode = config["strict_mode"].as<bool>();
        logger->info("Validation strict mode: {}", strict_mode ? "enabled" : "disabled");
    }
    
    if (config["max_errors_per_record"]) {
        int max_errors = config["max_errors_per_record"].as<int>();
        logger->info("Maximum validation errors per record: {}", max_errors);
    }
    
    logger->info("Validation configuration loaded successfully");
}

std::vector<omop::common::ValidationResult> ValidationEngine::validate_batch(
    const std::vector<core::Record>& records,
    core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-validation");
    logger->debug("Validating batch of {} records", records.size());
    
    std::vector<omop::common::ValidationResult> results;
    results.reserve(records.size());
    
    // Track batch-level statistics
    size_t total_errors = 0;
    size_t total_warnings = 0;
    
    // Validate each record in the batch
    for (size_t i = 0; i < records.size(); ++i) {
        const auto& record = records[i];
        
        try {
            auto result = validate_record(record, context);
            
            // Count errors and warnings for batch statistics
            total_errors += result.error_count();
            total_warnings += result.warning_count();
            
            results.push_back(std::move(result));
            
        } catch (const std::exception& e) {
            // Handle validation failures gracefully
            omop::common::ValidationResult error_result;
            error_result.add_error("batch_record_" + std::to_string(i), 
                                 std::format("Validation exception: {}", e.what()), 
                                 "batch_validation", "error");
            
            results.push_back(std::move(error_result));
            total_errors++;
        }
    }
    
    logger->info("Batch validation completed: {} records, {} errors, {} warnings", 
                records.size(), total_errors, total_warnings);
    
    return results;
}

// Factory function
std::unique_ptr<IValidationEngine> create_validation_engine() {
    return std::make_unique<ValidationEngine>();
}

} // namespace omop::transform 