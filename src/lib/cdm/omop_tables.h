/**
 * @file omop_tables.h
 * @brief OMOP CDM table classes for UK healthcare data processing
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include <string>
#include <optional>
#include <chrono>
#include <vector>
#include <unordered_map>
#include <any>
#include <memory>
#include <functional>
#include <mutex>
#include <atomic>

namespace omop::cdm {

/**
 * @brief OMOP CDM version
 */
constexpr const char* CDM_VERSION = "5.4";

/**
 * @brief Validation result containing status and error messages
 */
struct ValidationResult {
    bool is_valid;
    std::vector<std::string> errors;

    /**
     * @brief Implicit conversion to bool for convenience
     */
    operator bool() const { return is_valid; }
};

/**
 * @brief Field visitor interface for efficient field access
 */
class FieldVisitor {
public:
    virtual ~FieldVisitor() = default;

    /**
     * @brief Visit a field with its name and value
     * @param name Field name
     * @param value Field value
     */
    virtual void visit(const std::string& name, const std::any& value) = 0;
};

/**
 * @brief Base class for all OMOP CDM tables
 *
 * Provides common functionality for OMOP table records including
 * field access, validation, and SQL generation. Implements visitor
 * pattern for efficient field iteration.
 */
class OmopTable {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~OmopTable() = default;

    // Delete copy operations for base class
    OmopTable(const OmopTable&) = delete;
    OmopTable& operator=(const OmopTable&) = delete;
    
    // Allow move operations for base class
    OmopTable(OmopTable&&) = default;
    OmopTable& operator=(OmopTable&&) = default;
    
    OmopTable() = default;

    /**
     * @brief Get table name
     * @return std::string Table name
     */
    [[nodiscard]] virtual std::string table_name() const = 0;

    /**
     * @brief Get schema name
     * @return std::string Schema name
     */
    [[nodiscard]] virtual std::string schema_name() const { return "cdm"; }

    /**
     * @brief Convert to INSERT SQL statement
     *
     * Generates a properly escaped SQL INSERT statement for the record.
     * String values are escaped to prevent SQL injection.
     *
     * @param escape_values Whether to escape string values (default: true)
     * @return std::string INSERT SQL
     */
    [[nodiscard]] virtual std::string to_insert_sql(bool escape_values = true) const = 0;

    /**
     * @brief Get field names
     * @return std::vector<std::string> Field names
     */
    [[nodiscard]] virtual std::vector<std::string> field_names() const = 0;

    /**
     * @brief Get field values
     *
     * @deprecated Use visit_fields() for better performance
     * @return std::vector<std::any> Field values
     */
    [[nodiscard]] virtual std::vector<std::any> field_values() const = 0;

    /**
     * @brief Visit fields with a visitor for efficient access
     *
     * Allows iteration over fields without creating intermediate vectors.
     * More efficient than field_names() + field_values() for large tables.
     *
     * @param visitor Field visitor
     */
    virtual void visit_fields(FieldVisitor& visitor) const = 0;

    /**
     * @brief Validate record
     * @return bool True if valid
     */
    [[nodiscard]] virtual bool validate() const = 0;

    /**
     * @brief Get validation errors
     * @return std::vector<std::string> Validation errors
     */
    [[nodiscard]] virtual std::vector<std::string> validation_errors() const = 0;

    /**
     * @brief Validate record with detailed result
     * @return ValidationResult Validation result with errors
     */
    [[nodiscard]] virtual ValidationResult validate_detailed() const {
        ValidationResult result;
        result.is_valid = validate();
        if (!result.is_valid) {
            result.errors = validation_errors();
        }
        return result;
    }

    /**
     * @brief Get UK formatted date string (DD/MM/YYYY)
     * @param time_point Time point to format
     * @return std::string UK formatted date
     */
    [[nodiscard]] static std::string format_uk_date(const std::chrono::system_clock::time_point& time_point);

protected:
    /**
     * @brief Helper to format datetime for SQL
     * @param time_point Time point to format
     * @return std::string Formatted datetime string
     */
    [[nodiscard]] static std::string format_datetime_sql(
        const std::chrono::system_clock::time_point& time_point);

    /**
     * @brief Helper to escape string value for SQL
     * @param value String value to escape
     * @return std::string Escaped string with quotes
     */
    [[nodiscard]] static std::string escape_string_sql(const std::string& value);
};

/**
 * @brief Person table
 *
 * Central table containing demographic information about each person.
 * Includes comprehensive validation for data integrity.
 */
class Person : public OmopTable {
public:
    // Required fields
    int64_t person_id{0};
    int32_t gender_concept_id{0};
    int32_t year_of_birth{0};
    int32_t race_concept_id{0};
    int32_t ethnicity_concept_id{0};

    // Optional fields
    std::optional<int32_t> month_of_birth;
    std::optional<int32_t> day_of_birth;
    std::optional<std::chrono::system_clock::time_point> birth_datetime;
    std::optional<int32_t> location_id;
    std::optional<int32_t> provider_id;
    std::optional<int32_t> care_site_id;
    std::optional<std::string> person_source_value;
    std::optional<std::string> gender_source_value;
    std::optional<int32_t> gender_source_concept_id;
    std::optional<std::string> race_source_value;
    std::optional<int32_t> race_source_concept_id;
    std::optional<std::string> ethnicity_source_value;
    std::optional<int32_t> ethnicity_source_concept_id;

    // Constructors
    Person() = default;
    Person(Person&&) = default;
    Person& operator=(Person&&) = default;

    [[nodiscard]] std::string table_name() const override { return "person"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;

private:
    /**
     * @brief Validate year of birth is reasonable
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_year_of_birth() const;

    /**
     * @brief Validate month of birth if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_month_of_birth() const;

    /**
     * @brief Validate day of birth if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_day_of_birth() const;
};

/**
 * @brief Observation Period table
 *
 * Defines periods of time during which a person is observed.
 * Validates that periods have logical date ranges.
 */
class ObservationPeriod : public OmopTable {
public:
    // Required fields
    int64_t observation_period_id{0};
    int64_t person_id{0};
    std::chrono::system_clock::time_point observation_period_start_date;
    std::chrono::system_clock::time_point observation_period_end_date;
    int32_t period_type_concept_id{0};

    // Custom copy constructor and assignment operator
    ObservationPeriod(const ObservationPeriod& other)
        : observation_period_id(other.observation_period_id),
          person_id(other.person_id),
          observation_period_start_date(other.observation_period_start_date),
          observation_period_end_date(other.observation_period_end_date),
          period_type_concept_id(other.period_type_concept_id) {}
    ObservationPeriod& operator=(const ObservationPeriod& other) {
        if (this != &other) {
            observation_period_id = other.observation_period_id;
            person_id = other.person_id;
            observation_period_start_date = other.observation_period_start_date;
            observation_period_end_date = other.observation_period_end_date;
            period_type_concept_id = other.period_type_concept_id;
        }
        return *this;
    }
    ObservationPeriod() = default;

    [[nodiscard]] std::string table_name() const override { return "observation_period"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;

private:
    /**
     * @brief Validate date range is logical
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_date_range() const;
};

/**
 * @brief Visit Occurrence table
 *
 * Records encounters between a person and healthcare provider.
 * Includes validation for visit dates and relationships.
 */
class VisitOccurrence : public OmopTable {
public:
    // Required fields
    int64_t visit_occurrence_id{0};
    int64_t person_id{0};
    int32_t visit_concept_id{0};
    std::chrono::system_clock::time_point visit_start_date;
    std::chrono::system_clock::time_point visit_end_date;
    int32_t visit_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> visit_start_datetime;
    std::optional<std::chrono::system_clock::time_point> visit_end_datetime;
    std::optional<int32_t> provider_id;
    std::optional<int32_t> care_site_id;
    std::optional<std::string> visit_source_value;
    std::optional<int32_t> visit_source_concept_id;
    std::optional<int32_t> admitted_from_concept_id;
    std::optional<std::string> admitted_from_source_value;
    std::optional<int32_t> discharged_to_concept_id;
    std::optional<std::string> discharged_to_source_value;
    std::optional<int64_t> preceding_visit_occurrence_id;

    // Custom copy constructor and assignment operator
    VisitOccurrence(const VisitOccurrence& other)
        : visit_occurrence_id(other.visit_occurrence_id),
          person_id(other.person_id),
          visit_concept_id(other.visit_concept_id),
          visit_start_date(other.visit_start_date),
          visit_end_date(other.visit_end_date),
          visit_type_concept_id(other.visit_type_concept_id),
          visit_start_datetime(other.visit_start_datetime),
          visit_end_datetime(other.visit_end_datetime),
          provider_id(other.provider_id),
          care_site_id(other.care_site_id),
          visit_source_value(other.visit_source_value),
          visit_source_concept_id(other.visit_source_concept_id),
          admitted_from_concept_id(other.admitted_from_concept_id),
          admitted_from_source_value(other.admitted_from_source_value),
          discharged_to_concept_id(other.discharged_to_concept_id),
          discharged_to_source_value(other.discharged_to_source_value),
          preceding_visit_occurrence_id(other.preceding_visit_occurrence_id) {}
    VisitOccurrence& operator=(const VisitOccurrence& other) {
        if (this != &other) {
            visit_occurrence_id = other.visit_occurrence_id;
            person_id = other.person_id;
            visit_concept_id = other.visit_concept_id;
            visit_start_date = other.visit_start_date;
            visit_end_date = other.visit_end_date;
            visit_type_concept_id = other.visit_type_concept_id;
            visit_start_datetime = other.visit_start_datetime;
            visit_end_datetime = other.visit_end_datetime;
            provider_id = other.provider_id;
            care_site_id = other.care_site_id;
            visit_source_value = other.visit_source_value;
            visit_source_concept_id = other.visit_source_concept_id;
            admitted_from_concept_id = other.admitted_from_concept_id;
            admitted_from_source_value = other.admitted_from_source_value;
            discharged_to_concept_id = other.discharged_to_concept_id;
            discharged_to_source_value = other.discharged_to_source_value;
            preceding_visit_occurrence_id = other.preceding_visit_occurrence_id;
        }
        return *this;
    }
    VisitOccurrence() = default;

    [[nodiscard]] std::string table_name() const override { return "visit_occurrence"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Condition Occurrence table
 *
 * Records conditions (diseases, symptoms, findings) diagnosed or reported.
 */
class ConditionOccurrence : public OmopTable {
public:
    // Required fields
    int64_t condition_occurrence_id{0};
    int64_t person_id{0};
    int32_t condition_concept_id{0};
    std::chrono::system_clock::time_point condition_start_date;
    int32_t condition_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> condition_start_datetime;
    std::optional<std::chrono::system_clock::time_point> condition_end_date;
    std::optional<std::chrono::system_clock::time_point> condition_end_datetime;
    std::optional<int32_t> condition_status_concept_id;
    std::optional<std::string> stop_reason;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> condition_source_value;
    std::optional<int32_t> condition_source_concept_id;
    std::optional<std::string> condition_status_source_value;

    // Custom copy constructor and assignment operator
    ConditionOccurrence(const ConditionOccurrence& other)
        : condition_occurrence_id(other.condition_occurrence_id),
          person_id(other.person_id),
          condition_concept_id(other.condition_concept_id),
          condition_start_date(other.condition_start_date),
          condition_type_concept_id(other.condition_type_concept_id),
          condition_start_datetime(other.condition_start_datetime),
          condition_end_date(other.condition_end_date),
          condition_end_datetime(other.condition_end_datetime),
          condition_status_concept_id(other.condition_status_concept_id),
          stop_reason(other.stop_reason),
          provider_id(other.provider_id),
          visit_occurrence_id(other.visit_occurrence_id),
          visit_detail_id(other.visit_detail_id),
          condition_source_value(other.condition_source_value),
          condition_source_concept_id(other.condition_source_concept_id),
          condition_status_source_value(other.condition_status_source_value) {}
    ConditionOccurrence& operator=(const ConditionOccurrence& other) {
        if (this != &other) {
            condition_occurrence_id = other.condition_occurrence_id;
            person_id = other.person_id;
            condition_concept_id = other.condition_concept_id;
            condition_start_date = other.condition_start_date;
            condition_type_concept_id = other.condition_type_concept_id;
            condition_start_datetime = other.condition_start_datetime;
            condition_end_date = other.condition_end_date;
            condition_end_datetime = other.condition_end_datetime;
            condition_status_concept_id = other.condition_status_concept_id;
            stop_reason = other.stop_reason;
            provider_id = other.provider_id;
            visit_occurrence_id = other.visit_occurrence_id;
            visit_detail_id = other.visit_detail_id;
            condition_source_value = other.condition_source_value;
            condition_source_concept_id = other.condition_source_concept_id;
            condition_status_source_value = other.condition_status_source_value;
        }
        return *this;
    }
    ConditionOccurrence() = default;

    [[nodiscard]] std::string table_name() const override { return "condition_occurrence"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Drug Exposure table
 *
 * Records drug exposures including prescriptions, administrations, and dispensings.
 * Includes validation for drug quantities and date ranges.
 */
class DrugExposure : public OmopTable {
public:
    // Required fields
    int64_t drug_exposure_id{0};
    int64_t person_id{0};
    int32_t drug_concept_id{0};
    std::chrono::system_clock::time_point drug_exposure_start_date;
    std::chrono::system_clock::time_point drug_exposure_end_date;
    int32_t drug_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> drug_exposure_start_datetime;
    std::optional<std::chrono::system_clock::time_point> drug_exposure_end_datetime;
    std::optional<std::chrono::system_clock::time_point> verbatim_end_date;
    std::optional<std::string> stop_reason;
    std::optional<int32_t> refills;
    std::optional<double> quantity;
    std::optional<int32_t> days_supply;
    std::optional<std::string> sig;
    std::optional<int32_t> route_concept_id;
    std::optional<std::string> lot_number;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> drug_source_value;
    std::optional<int32_t> drug_source_concept_id;
    std::optional<std::string> route_source_value;
    std::optional<std::string> dose_unit_source_value;

    // Constructors
    DrugExposure() = default;
    DrugExposure(DrugExposure&&) = default;
    DrugExposure& operator=(DrugExposure&&) = default;

    [[nodiscard]] std::string table_name() const override { return "drug_exposure"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;

private:
    /**
     * @brief Validate drug quantity if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_quantity() const;

    /**
     * @brief Validate days supply if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_days_supply() const;
};

/**
 * @brief Procedure Occurrence table
 *
 * Records procedures performed on a person.
 */
class ProcedureOccurrence : public OmopTable {
public:
    // Required fields
    int64_t procedure_occurrence_id{0};
    int64_t person_id{0};
    int32_t procedure_concept_id{0};
    std::chrono::system_clock::time_point procedure_date;
    int32_t procedure_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> procedure_datetime;
    std::optional<std::chrono::system_clock::time_point> procedure_end_date;
    std::optional<std::chrono::system_clock::time_point> procedure_end_datetime;
    std::optional<int32_t> modifier_concept_id;
    std::optional<int32_t> quantity;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> procedure_source_value;
    std::optional<int32_t> procedure_source_concept_id;
    std::optional<std::string> modifier_source_value;

    // Constructors
    ProcedureOccurrence() = default;
    ProcedureOccurrence(ProcedureOccurrence&&) = default;
    ProcedureOccurrence& operator=(ProcedureOccurrence&&) = default;

    [[nodiscard]] std::string table_name() const override { return "procedure_occurrence"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Measurement table
 *
 * Records measurements including laboratory tests, vital signs, and other clinical observations.
 * Includes validation for measurement values and ranges.
 */
class Measurement : public OmopTable {
public:
    // Required fields
    int64_t measurement_id{0};
    int64_t person_id{0};
    int32_t measurement_concept_id{0};
    std::chrono::system_clock::time_point measurement_date;
    int32_t measurement_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> measurement_datetime;
    std::optional<std::string> measurement_time;
    std::optional<int32_t> operator_concept_id;
    std::optional<double> value_as_number;
    std::optional<int32_t> value_as_concept_id;
    std::optional<int32_t> unit_concept_id;
    std::optional<double> range_low;
    std::optional<double> range_high;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> measurement_source_value;
    std::optional<int32_t> measurement_source_concept_id;
    std::optional<std::string> unit_source_value;
    std::optional<int32_t> unit_source_concept_id;
    std::optional<std::string> value_source_value;
    std::optional<int64_t> measurement_event_id;
    std::optional<int32_t> meas_event_field_concept_id;

    // Constructors
    Measurement() = default;
    Measurement(Measurement&&) = default;
    Measurement& operator=(Measurement&&) = default;

    [[nodiscard]] std::string table_name() const override { return "measurement"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;

private:
    /**
     * @brief Validate measurement ranges if provided
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_ranges() const;
};

/**
 * @brief Observation table
 *
 * Records clinical facts about a person that are not measurements or procedures.
 */
class Observation : public OmopTable {
public:
    // Required fields
    int64_t observation_id{0};
    int64_t person_id{0};
    int32_t observation_concept_id{0};
    std::chrono::system_clock::time_point observation_date;
    int32_t observation_type_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> observation_datetime;
    std::optional<double> value_as_number;
    std::optional<std::string> value_as_string;
    std::optional<int32_t> value_as_concept_id;
    std::optional<int32_t> qualifier_concept_id;
    std::optional<int32_t> unit_concept_id;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> observation_source_value;
    std::optional<int32_t> observation_source_concept_id;
    std::optional<std::string> unit_source_value;
    std::optional<std::string> qualifier_source_value;
    std::optional<std::string> value_source_value;
    std::optional<int64_t> observation_event_id;
    std::optional<int32_t> obs_event_field_concept_id;

    // Constructors
    Observation() = default;
    Observation(Observation&&) = default;
    Observation& operator=(Observation&&) = default;

    [[nodiscard]] std::string table_name() const override { return "observation"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Death table
 *
 * Records death information for a person.
 */
class Death : public OmopTable {
public:
    // Required fields
    int64_t person_id{0};
    std::chrono::system_clock::time_point death_date;

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> death_datetime;
    std::optional<int32_t> death_type_concept_id;
    std::optional<int32_t> cause_concept_id;
    std::optional<std::string> cause_source_value;
    std::optional<int32_t> cause_source_concept_id;

    // Constructors
    Death() = default;
    Death(Death&&) = default;
    Death& operator=(Death&&) = default;

    [[nodiscard]] std::string table_name() const override { return "death"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;

private:
    /**
     * @brief Validate death date is not in the future
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_death_date() const;
};

/**
 * @brief Note table
 *
 * Records unstructured text notes about a person.
 */
class Note : public OmopTable {
public:
    // Required fields
    int64_t note_id{0};
    int64_t person_id{0};
    std::chrono::system_clock::time_point note_date;
    int32_t note_type_concept_id{0};
    int32_t note_class_concept_id{0};
    std::string note_title;
    std::string note_text;
    int32_t encoding_concept_id{0};
    int32_t language_concept_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> note_datetime;
    std::optional<int32_t> provider_id;
    std::optional<int64_t> visit_occurrence_id;
    std::optional<int64_t> visit_detail_id;
    std::optional<std::string> note_source_value;
    std::optional<int64_t> note_event_id;
    std::optional<int32_t> note_event_field_concept_id;

    // Constructors
    Note() = default;
    Note(Note&&) = default;
    Note& operator=(Note&&) = default;

    [[nodiscard]] std::string table_name() const override { return "note"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Concept table
 *
 * Contains standardised vocabularies and concept definitions.
 */
class Concept : public OmopTable {
public:
    // Required fields
    int32_t concept_id{0};
    std::string concept_name;
    std::string domain_id;
    std::string vocabulary_id;
    std::string concept_class_id;
    std::string concept_code;
    std::chrono::system_clock::time_point valid_start_date;
    std::chrono::system_clock::time_point valid_end_date;

    // Optional fields
    std::optional<std::string> standard_concept;
    std::optional<std::string> invalid_reason;

    // Constructors
    Concept() = default;
    Concept(Concept&&) = default;
    Concept& operator=(Concept&&) = default;

    [[nodiscard]] std::string table_name() const override { return "concept"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Location table
 *
 * Contains geographic location information for UK addresses.
 */
class Location : public OmopTable {
public:
    // Required fields
    int32_t location_id{0};

    // Optional fields
    std::optional<std::string> address_1;
    std::optional<std::string> address_2;
    std::optional<std::string> city;
    std::optional<std::string> state;  // County for UK
    std::optional<std::string> zip;    // UK postcode
    std::optional<std::string> county;
    std::optional<std::string> country;
    std::optional<std::string> location_source_value;
    std::optional<double> latitude;
    std::optional<double> longitude;

    // Constructors
    Location() = default;
    Location(Location&&) = default;
    Location& operator=(Location&&) = default;

    [[nodiscard]] std::string table_name() const override { return "location"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;

private:
    /**
     * @brief Validate UK postcode format
     * @return bool True if valid UK postcode
     */
    [[nodiscard]] bool validate_uk_postcode() const;
};

/**
 * @brief Care Site table
 *
 * Contains information about healthcare facilities.
 */
class CareSite : public OmopTable {
public:
    // Required fields
    int32_t care_site_id{0};

    // Optional fields
    std::optional<std::string> care_site_name;
    std::optional<int32_t> place_of_service_concept_id;
    std::optional<int32_t> location_id;
    std::optional<std::string> care_site_source_value;
    std::optional<std::string> place_of_service_source_value;

    // Constructors
    CareSite() = default;
    CareSite(CareSite&&) = default;
    CareSite& operator=(CareSite&&) = default;

    [[nodiscard]] std::string table_name() const override { return "care_site"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Provider table
 *
 * Contains information about healthcare providers.
 */
class Provider : public OmopTable {
public:
    // Required fields
    int32_t provider_id{0};

    // Optional fields
    std::optional<std::string> provider_name;
    std::optional<std::string> npi;  // National Provider Identifier
    std::optional<std::string> dea;  // Drug Enforcement Administration
    std::optional<int32_t> specialty_concept_id;
    std::optional<int32_t> care_site_id;
    std::optional<int32_t> year_of_birth;
    std::optional<int32_t> gender_concept_id;
    std::optional<std::string> provider_source_value;
    std::optional<std::string> specialty_source_value;
    std::optional<int32_t> specialty_source_concept_id;
    std::optional<std::string> gender_source_value;
    std::optional<int32_t> gender_source_concept_id;

    // Constructors
    Provider() = default;
    Provider(Provider&&) = default;
    Provider& operator=(Provider&&) = default;

    [[nodiscard]] std::string table_name() const override { return "provider"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Visit Detail table
 *
 * Provides granular information about components of a visit.
 */
class VisitDetail : public OmopTable {
public:
    // Required fields
    int64_t visit_detail_id{0};
    int64_t person_id{0};
    int32_t visit_detail_concept_id{0};
    std::chrono::system_clock::time_point visit_detail_start_date;
    std::chrono::system_clock::time_point visit_detail_end_date;
    int32_t visit_detail_type_concept_id{0};
    int64_t visit_occurrence_id{0};

    // Optional fields
    std::optional<std::chrono::system_clock::time_point> visit_detail_start_datetime;
    std::optional<std::chrono::system_clock::time_point> visit_detail_end_datetime;
    std::optional<int32_t> provider_id;
    std::optional<int32_t> care_site_id;
    std::optional<std::string> visit_detail_source_value;
    std::optional<int32_t> visit_detail_source_concept_id;
    std::optional<int32_t> admitted_from_concept_id;
    std::optional<std::string> admitted_from_source_value;
    std::optional<int32_t> discharged_to_concept_id;
    std::optional<std::string> discharged_to_source_value;
    std::optional<int64_t> preceding_visit_detail_id;
    std::optional<int32_t> parent_visit_detail_id;

    // Constructors
    VisitDetail() = default;
    VisitDetail(VisitDetail&&) = default;
    VisitDetail& operator=(VisitDetail&&) = default;

    [[nodiscard]] std::string table_name() const override { return "visit_detail"; }
    [[nodiscard]] std::string to_insert_sql(bool escape_values = true) const override;
    [[nodiscard]] std::vector<std::string> field_names() const override;
    [[nodiscard]] std::vector<std::any> field_values() const override;
    void visit_fields(FieldVisitor& visitor) const override;
    [[nodiscard]] bool validate() const override;
    [[nodiscard]] std::vector<std::string> validation_errors() const override;
};

/**
 * @brief Factory for creating OMOP table instances
 */
class OmopTableFactory {
public:
    /**
     * @brief Create table instance by name
     * @param table_name Table name
     * @return std::unique_ptr<OmopTable> Table instance or nullptr if unsupported
     */
    [[nodiscard]] static std::unique_ptr<OmopTable> create(const std::string& table_name);

    /**
     * @brief Get all supported table names
     * @return std::vector<std::string> Table names
     */
    [[nodiscard]] static std::vector<std::string> get_supported_tables();

    /**
     * @brief Check if table is supported
     * @param table_name Table name
     * @return bool True if supported
     */
    [[nodiscard]] static bool is_supported(const std::string& table_name);

    /**
     * @brief Register custom table type
     *
     * Allows extending the factory with custom table implementations.
     *
     * @param table_name Table name
     * @param creator Factory function
     */
    static void register_table(
        const std::string& table_name,
        std::function<std::unique_ptr<OmopTable>()> creator);

    /**
     * @brief Unregister a table type
     *
     * Removes a table type from the factory.
     *
     * @param table_name Table name to unregister
     */
    static void unregister_table(const std::string& table_name);

private:
    static std::unordered_map<std::string, std::function<std::unique_ptr<OmopTable>()>>& get_creators();
};

/**
 * @brief OMOP CDM table schema information
 */
class OmopSchema {
public:
    /**
     * @brief Get CREATE TABLE SQL for a table
     * @param table_name Table name
     * @param schema_name Schema name
     * @return std::string CREATE TABLE SQL
     * @throws std::invalid_argument if table name is not recognised
     */
    [[nodiscard]] static std::string get_create_table_sql(
        const std::string& table_name,
        const std::string& schema_name = "cdm");

    /**
     * @brief Get all CREATE TABLE statements
     * @param schema_name Schema name
     * @return std::vector<std::string> CREATE TABLE statements in dependency order
     */
    [[nodiscard]] static std::vector<std::string> get_all_create_table_sql(
        const std::string& schema_name = "cdm");

    /**
     * @brief Get indexes for a table
     * @param table_name Table name
     * @param schema_name Schema name
     * @return std::vector<std::string> CREATE INDEX statements
     * @throws std::invalid_argument if table name is not recognised
     */
    [[nodiscard]] static std::vector<std::string> get_table_indexes(
        const std::string& table_name,
        const std::string& schema_name = "cdm");

    /**
     * @brief Get foreign key constraints
     * @param table_name Table name
     * @param schema_name Schema name
     * @return std::vector<std::string> ALTER TABLE ADD CONSTRAINT statements
     * @throws std::invalid_argument if table name is not recognised
     */
    [[nodiscard]] static std::vector<std::string> get_foreign_keys(
        const std::string& table_name,
        const std::string& schema_name = "cdm");
};

} // namespace omop::cdm