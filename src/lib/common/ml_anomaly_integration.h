/**
 * @file ml_anomaly_integration.h  
 * @brief Integration wrapper for ML-based anomaly detectors with main anomaly detection system
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#pragma once

#include "anomaly_detector.h"
#include "ml_anomaly_detectors.h"
#include <memory>
#include <vector>
#include <string>
#include <chrono>

namespace omop::common {

/**
 * @brief Wrapper class for integrating ML anomaly detectors with the main system
 */
class MLAnomalyIntegration {
public:
    /**
     * @brief Configuration for ML integration
     */
    struct Config {
        bool enable_isolation_forest{true};     ///< Enable Isolation Forest
        bool enable_lstm_autoencoder{true};     ///< Enable LSTM Autoencoder
        bool enable_advanced_ensemble{true};    ///< Enable advanced ML ensemble
        
        // Training configuration
        bool auto_train{true};                  ///< Enable automatic training
        std::chrono::hours training_interval{24}; ///< Training interval
        size_t min_training_samples{500};       ///< Minimum samples for training
        double training_data_hours{72.0};       ///< Hours of data to use for training
        
        // Performance thresholds
        std::chrono::milliseconds max_training_time{30000};  ///< Max training time
        std::chrono::milliseconds max_detection_time{5000};  ///< Max detection time
        
        // Individual detector configs
        omop::common::ml::IsolationForestDetector::Config isolation_config;
        omop::common::ml::LSTMAutoencoderDetector::Config lstm_config;
        omop::common::ml::AdvancedMLAnomalyDetector::Config ensemble_config;
    };
    
    /**
     * @brief Constructor
     * @param config ML integration configuration
     */
    explicit MLAnomalyIntegration(const Config& config = Config{});
    
    /**
     * @brief Destructor
     */
    ~MLAnomalyIntegration();
    
    /**
     * @brief Initialize ML detectors
     * @return true if initialization successful
     */
    bool initialize();
    
    /**
     * @brief Train ML models with historical data
     * @param series Time series data for training
     * @param force_retrain Force retraining even if models are already trained
     * @return true if training successful
     */
    bool train_models(const TimeSeries& series, bool force_retrain = false);
    
    /**
     * @brief Detect anomalies using ML algorithms
     * @param series Time series to analyze
     * @param analysis_window_hours Hours of data to analyze
     * @param algorithm Specific algorithm to use (or ML ensemble)
     * @return Vector of detected anomalies
     */
    std::vector<Anomaly> detect_anomalies(
        const TimeSeries& series,
        double analysis_window_hours,
        AnomalyDetectionAlgorithm algorithm = AnomalyDetectionAlgorithm::ENSEMBLE);
    
    /**
     * @brief Check if ML models are trained and ready
     * @return true if models are trained
     */
    bool are_models_trained() const;
    
    /**
     * @brief Get ML detector statistics
     */
    struct MLStatistics {
        bool isolation_forest_available{false};
        bool lstm_autoencoder_available{false};
        bool advanced_ensemble_available{false};
        
        size_t total_training_samples{0};
        std::chrono::system_clock::time_point last_training_time;
        std::chrono::milliseconds last_training_duration{0};
        std::chrono::milliseconds average_detection_time{0};
        
        size_t total_detections{0};
        size_t anomalies_detected{0};
        double detection_accuracy{0.0};
        
        // Individual detector stats
        omop::common::ml::IsolationForestDetector::ModelStats isolation_stats;
        omop::common::ml::LSTMAutoencoderDetector::ModelStats lstm_stats;
        omop::common::ml::AdvancedMLAnomalyDetector::EnsembleStats ensemble_stats;
    };
    
    MLStatistics get_ml_statistics() const;
    
    /**
     * @brief Update configuration at runtime
     * @param config New configuration
     */
    void update_config(const Config& config);
    
    /**
     * @brief Check if specific algorithm is supported
     * @param algorithm Algorithm to check
     * @return true if supported
     */
    bool supports_algorithm(AnomalyDetectionAlgorithm algorithm) const;
    
    /**
     * @brief Get list of available ML algorithms
     * @return Vector of available algorithms
     */
    std::vector<AnomalyDetectionAlgorithm> get_available_algorithms() const;
    
    /**
     * @brief Perform health check on ML components
     * @return Health check results
     */
    struct HealthStatus {
        bool overall_healthy{true};
        std::vector<std::string> issues;
        std::vector<std::string> warnings;
        double performance_score{1.0};  // 0-1 scale
    };
    
    HealthStatus check_health() const;
    
    /**
     * @brief Reset ML models (clear training)
     */
    void reset_models();

private:
    Config config_;
    mutable std::mutex mutex_;
    
    // ML Detectors
    std::unique_ptr<omop::common::ml::IsolationForestDetector> isolation_detector_;
    std::unique_ptr<omop::common::ml::LSTMAutoencoderDetector> lstm_detector_;
    std::unique_ptr<omop::common::ml::AdvancedMLAnomalyDetector> ensemble_detector_;
    
    // Training management
    std::chrono::system_clock::time_point last_training_time_;
    std::atomic<bool> training_in_progress_{false};
    std::atomic<bool> models_initialized_{false};
    
    // Performance tracking
    mutable std::mutex stats_mutex_;
    MLStatistics ml_stats_;
    
    /**
     * @brief Initialize individual detectors
     */
    bool initialize_isolation_forest();
    bool initialize_lstm_autoencoder();
    bool initialize_advanced_ensemble();
    
    /**
     * @brief Check if retraining is needed
     * @param series Current time series data
     * @return true if retraining should be performed
     */
    bool should_retrain(const TimeSeries& series) const;
    
    /**
     * @brief Prepare training data from time series
     * @param series Time series data
     * @return Training data points
     */
    std::vector<TimeSeriesPoint> prepare_training_data(const TimeSeries& series) const;
    
    /**
     * @brief Update performance statistics
     * @param detection_time Time taken for detection
     * @param anomalies_found Number of anomalies found
     */
    void update_performance_stats(std::chrono::milliseconds detection_time, 
                                size_t anomalies_found);
    
    /**
     * @brief Validate detection results
     * @param anomalies Detected anomalies
     * @return Validated anomalies (removes invalid ones)
     */
    std::vector<Anomaly> validate_detection_results(const std::vector<Anomaly>& anomalies) const;
};

/**
 * @brief Factory for creating ML anomaly integration instances
 */
class MLAnomalyIntegrationFactory {
public:
    /**
     * @brief Create production ML integration instance
     * @return Configured ML integration for production use
     */
    static std::unique_ptr<MLAnomalyIntegration> create_production_instance();
    
    /**
     * @brief Create development ML integration instance
     * @return Configured ML integration for development use
     */
    static std::unique_ptr<MLAnomalyIntegration> create_development_instance();
    
    /**
     * @brief Create ML integration from environment variables
     * Environment variables:
     * - ML_ANOMALY_ISOLATION_FOREST_ENABLED: Enable Isolation Forest (true/false)
     * - ML_ANOMALY_LSTM_ENABLED: Enable LSTM Autoencoder (true/false)
     * - ML_ANOMALY_ENSEMBLE_ENABLED: Enable Advanced Ensemble (true/false)
     * - ML_ANOMALY_AUTO_TRAIN: Enable automatic training (true/false)
     * - ML_ANOMALY_TRAINING_INTERVAL: Training interval in hours
     * - ML_ANOMALY_MIN_TRAINING_SAMPLES: Minimum training samples
     * @return Configured ML integration from environment
     */
    static std::unique_ptr<MLAnomalyIntegration> create_from_environment();
    
    /**
     * @brief Create high-performance ML integration
     * Optimized for high-throughput scenarios with reduced accuracy for speed
     * @return High-performance ML integration instance
     */
    static std::unique_ptr<MLAnomalyIntegration> create_high_performance_instance();
    
    /**
     * @brief Create high-accuracy ML integration
     * Optimized for maximum accuracy with longer processing times
     * @return High-accuracy ML integration instance
     */
    static std::unique_ptr<MLAnomalyIntegration> create_high_accuracy_instance();
};

} // namespace omop::common