/**
 * @file utilities.h
 * @brief Common utility functions for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file provides a comprehensive collection of utility functions for the OMOP ETL pipeline,
 * including string manipulation, date/time handling, file operations, and validation utilities.
 */

#pragma once

// Include the dedicated util classes
#include "utils/string_utils.h"
#include "utils/date_utils.h"
#include "utils/file_utils.h"
#include "utils/system_utils.h"

#include <string>
#include <vector>
#include <optional>
#include <chrono>
#include <filesystem>
#include <unordered_map>
#include <unordered_set>
#include <sstream>
#include <functional>
#include <iomanip>
#include <algorithm>
#include <regex>
#include <uuid/uuid.h>
#include <any>
#include <typeinfo>
#include <mutex>
#include <atomic>
#include <ctime>
#include <random>

namespace omop::common {

/**
 * @brief Convert std::any to string representation
 * @param value Any value to convert
 * @return String representation
 */
std::string any_to_string(const std::any& value);

/**
 * @brief Convert std::any to double representation
 * @param value Any value to convert
 * @return Double representation
 */
double any_to_double(const std::any& value);


/**
 * @brief Cryptographic utility functions
 */
class CryptoUtils {
public:
    /**
     * @brief Calculate MD5 hash
     * @param data Input data
     * @return MD5 hash (hex string)
     */
    static std::string md5(const std::string& data);

    /**
     * @brief Calculate SHA256 hash
     * @param data Input data
     * @return SHA256 hash (hex string)
     */
    static std::string sha256(const std::string& data);

    /**
     * @brief Base64 encode
     * @param data Binary data
     * @return Base64 encoded string
     */
    static std::string base64_encode(const std::vector<uint8_t>& data);

    /**
     * @brief Base64 decode
     * @param encoded Encoded string
     * @return Decoded binary data
     */
    static std::vector<uint8_t> base64_decode(const std::string& encoded);

    /**
     * @brief Generate UUID
     * @return UUID string
     */
    static std::string generate_uuid();

    /**
     * @brief Generate random bytes
     * @param length Number of bytes
     * @return Random bytes
     */
    static std::vector<uint8_t> random_bytes(size_t length);

private:
    static std::mutex random_mutex;
};

/**
 * @brief Validation utility functions
 */
class ValidationUtils {
public:
    /**
     * @brief Validate email address
     * @param email Email address
     * @return True if valid
     */
    static bool is_valid_email(const std::string& email);

    /**
     * @brief Validate URL
     * @param url URL string
     * @return True if valid
     */
    static bool is_valid_url(const std::string& url);

    /**
     * @brief Validate IP address (v4 or v6)
     * @param ip IP address
     * @return True if valid
     */
    static bool is_valid_ip(const std::string& ip);

    /**
     * @brief Validate phone number
     * @param phone Phone number
     * @param country_code Country code
     * @return True if valid
     */
    static bool is_valid_phone(const std::string& phone,
                              const std::string& country_code = "US");

    /**
     * @brief Validate postal code
     * @param postal_code Postal code
     * @param country_code Country code
     * @return True if valid
     */
    static bool is_valid_postal_code(const std::string& postal_code,
                                    const std::string& country_code = "GB");
    
    /**
     * @brief Validate UK postal code specifically
     * @param postal_code UK postal code
     * @return True if valid UK postcode
     */
    static bool is_valid_uk_postcode(const std::string& postal_code);
    
    /**
     * @brief Generic configuration validation
     * @tparam T Field type
     * @param value Field value
     * @param field_name Field name for error reporting
     * @return bool True if valid
     */
    template<typename T>
    static bool validate_required_field(const T& value, const std::string& field_name);
    
    /**
     * @brief Generic field range validation
     * @tparam T Field type
     * @param value Field value
     * @param min_value Minimum allowed value
     * @param max_value Maximum allowed value
     * @return bool True if within range
     */
    template<typename T>
    static bool validate_field_range(const T& value, const T& min_value, const T& max_value);
    
    /**
     * @brief Validate file path
     * @param path File path to validate
     * @return bool True if valid
     */
    static bool validate_file_path(const std::string& path);
    
    /**
     * @brief Validate database connection string
     * @param connection_string Connection string to validate
     * @return bool True if valid
     */
    static bool validate_database_connection_string(const std::string& connection_string);
    
    /**
     * @brief Validate configuration map
     * @param config Configuration map
     * @param required_fields Required field names
     * @param validators Field validators
     * @return std::vector<std::string> Validation errors
     */
    static std::vector<std::string> validate_config_map(
        const std::unordered_map<std::string, std::any>& config,
        const std::vector<std::string>& required_fields,
        const std::unordered_map<std::string, std::function<bool(const std::any&)>>& validators);
    
    /**
     * @brief Validate UK phone number
     * @param phone UK phone number
     * @return True if valid UK phone number
     */
    static bool is_valid_uk_phone(const std::string& phone);

    /**
     * @brief Validate date format
     * @param date_str Date string
     * @param format Expected format
     * @return True if valid
     */
    static bool is_valid_date_format(const std::string& date_str,
                                    const std::string& format);

    /**
     * @brief Validate JSON string
     * @param json JSON string
     * @return True if valid JSON
     */
    static bool is_valid_json(const std::string& json);

    /**
     * @brief Validate SQL identifier
     * @param identifier SQL identifier
     * @return True if valid
     */
    static bool is_valid_sql_identifier(const std::string& identifier);

    /**
     * @brief Validate UUID format
     * @param uuid UUID string
     * @return True if valid UUID
     */
    static bool is_valid_uuid(const std::string& uuid);

    /**
     * @brief Sanitize string for safe usage
     * @param input Input string
     * @return Sanitised string
     */
    static std::string sanitize_string(const std::string& input);
};

/**
 * @brief Performance monitoring utilities
 */
class PerformanceUtils {
public:
    /**
     * @brief Simple timer class
     */
    class Timer {
    public:
        Timer() : start_(std::chrono::high_resolution_clock::now()) {}

        void reset() {
            start_ = std::chrono::high_resolution_clock::now();
        }

        double elapsed_seconds() const {
            auto end = std::chrono::high_resolution_clock::now();
            return std::chrono::duration<double>(end - start_).count();
        }

        double elapsed_milliseconds() const {
            return elapsed_seconds() * 1000.0;
        }

    private:
        std::chrono::high_resolution_clock::time_point start_;
    };

    /**
     * @brief Memory usage tracker
     */
    class MemoryTracker {
    public:
        MemoryTracker();

        size_t current_usage() const;
        size_t peak_usage() const;
        void reset();
        static size_t global_peak_usage() { return global_peak_usage_.load(); }

    private:
        size_t initial_usage_;
        mutable size_t peak_usage_;
        static std::atomic<size_t> global_peak_usage_;
    };

    /**
     * @brief Format bytes to human readable string
     * @param bytes Number of bytes
     * @return Formatted string (e.g., "1.5 GB")
     */
    static std::string format_bytes(size_t bytes);

    /**
     * @brief Format duration to human readable string
     * @param seconds Duration in seconds
     * @return Formatted string (e.g., "2h 15m 30s")
     */
    static std::string format_duration(double seconds);

    /**
     * @brief Calculate throughput
     * @param items Number of items processed
     * @param seconds Time taken
     * @return Items per second
     */
    static double calculate_throughput(size_t items, double seconds);
};

/**
 * @brief Generic enum conversion utilities
 */
class EnumUtils {
public:
    /**
     * @brief Generic enum to string conversion
     * @tparam T Enum type
     * @param value Enum value
     * @param string_map Map of enum values to strings
     * @return std::string String representation
     */
    template<typename T>
    static std::string enum_to_string(T value, 
                                    const std::unordered_map<T, std::string>& string_map) {
        auto it = string_map.find(value);
        if (it != string_map.end()) {
            return it->second;
        }
        return "Unknown";
    }

    /**
     * @brief Generic string to enum conversion
     * @tparam T Enum type
     * @param str String representation
     * @param string_map Map of strings to enum values
     * @return std::optional<T> Enum value if valid
     */
    template<typename T>
    static std::optional<T> string_to_enum(const std::string& str,
                                          const std::unordered_map<std::string, T>& string_map) {
        auto it = string_map.find(str);
        if (it != string_map.end()) {
            return it->second;
        }
        return std::nullopt;
    }

    /**
     * @brief Get all enum values as strings
     * @tparam T Enum type
     * @param string_map Map of enum values to strings
     * @return std::vector<std::string> All enum string representations
     */
    template<typename T>
    static std::vector<std::string> get_all_enum_strings(
        const std::unordered_map<T, std::string>& string_map) {
        std::vector<std::string> result;
        result.reserve(string_map.size());
        for (const auto& pair : string_map) {
            result.push_back(pair.second);
        }
        return result;
    }

    /**
     * @brief Check if string represents valid enum value
     * @tparam T Enum type
     * @param str String to check
     * @param string_map Map of strings to enum values
     * @return bool True if valid
     */
    template<typename T>
    static bool is_valid_enum_string(const std::string& str,
                                   const std::unordered_map<std::string, T>& string_map) {
        return string_map.find(str) != string_map.end();
    }
};

/**
 * @brief Generic optimization utilities
 */
class OptimizationUtils {
public:
    /**
     * @brief Calculate optimal batch size
     * @param record_size Average record size in bytes
     * @param available_memory Available memory in bytes
     * @param system_type System type identifier
     * @return size_t Optimal batch size
     */
    static size_t calculate_optimal_batch_size(size_t record_size,
                                             size_t available_memory,
                                             const std::string& system_type);

    /**
     * @brief Calculate optimal worker count
     * @param target_throughput Target records per second
     * @param average_batch_time Average batch processing time
     * @param system_cores Number of CPU cores
     * @return size_t Optimal worker count
     */
    static size_t calculate_optimal_workers(size_t target_throughput,
                                          std::chrono::duration<double> average_batch_time,
                                          size_t system_cores);

    /**
     * @brief Optimize configuration based on system constraints
     * @param base_config Base configuration
     * @param system_info System information
     * @param performance_requirements Performance requirements
     * @return std::unordered_map<std::string, std::any> Optimised configuration
     */
    static std::unordered_map<std::string, std::any> optimize_config(
        const std::unordered_map<std::string, std::any>& base_config,
        const std::unordered_map<std::string, std::any>& system_info,
        const std::unordered_map<std::string, std::any>& performance_requirements);

    /**
     * @brief Get system information for optimization
     * @return std::unordered_map<std::string, std::any> System information
     */
    static std::unordered_map<std::string, std::any> get_system_info();

    /**
     * @brief Optimize string operations for high-throughput scenarios
     * @param input Input string
     * @param operations Vector of operations to perform
     * @return std::string Optimized result
     */
    static std::string optimize_string_operations(const std::string& input,
                                                 const std::vector<std::string>& operations);

    /**
     * @brief Batch process multiple string operations for high throughput
     * @param inputs Vector of input strings
     * @param operation_func Function to apply to each string
     * @param num_threads Number of threads to use (0 for auto-detect)
     * @return std::vector<std::string> Processed strings
     */
    static std::vector<std::string> batch_string_processing(
        const std::vector<std::string>& inputs,
        std::function<std::string(const std::string&)> operation_func,
        size_t num_threads = 0);

    /**
     * @brief High-throughput any_to_string conversion using lookup tables
     * @param values Vector of any values to convert
     * @return std::vector<std::string> String representations
     */
    static std::vector<std::string> bulk_any_to_string(const std::vector<std::any>& values);

    /**
     * @brief Optimized validation for high-volume data
     * @param values Vector of values to validate
     * @param validator Validation function
     * @param num_threads Number of threads (0 for auto-detect)
     * @return std::vector<bool> Validation results
     */
    template<typename T>
    static std::vector<bool> bulk_validate(
        const std::vector<T>& values,
        std::function<bool(const T&)> validator,
        size_t num_threads = 0);

    /**
     * @brief Memory pool for high-throughput string operations
     */
    class StringPool {
    public:
        StringPool(size_t initial_capacity = 1000);
        ~StringPool();
        
        std::string* acquire();
        void release(std::string* str);
        void clear_all();
        
    private:
        std::vector<std::unique_ptr<std::string>> pool_;
        std::vector<std::string*> available_;
        std::mutex mutex_;
        size_t capacity_;
    };

    /**
     * @brief Get thread-local string pool
     * @return StringPool& Thread-local string pool instance
     */
    static StringPool& get_string_pool();

private:
    static const std::unordered_map<std::string, double> system_optimization_factors_;
    
    // High-performance string operation implementations
    static std::string fast_replace_all(const std::string& input,
                                       const std::string& from,
                                       const std::string& to);
    
    static std::string fast_to_upper(const std::string& input);
    static std::string fast_to_lower(const std::string& input);
    static std::string fast_trim(const std::string& input);
};

/**
 * @brief Medical and healthcare utilities
 */
class MedicalUtils {
public:
    /**
     * @brief Check if term is a medical term
     * @param term Term to check
     * @return bool True if medical term
     */
    static bool is_medical_term(const std::string& term);
    
    /**
     * @brief Check if term has medical suffix
     * @param term Term to check
     * @return bool True if has medical suffix
     */
    static bool has_medical_suffix(const std::string& term);
    
    /**
     * @brief Check if term has medical prefix
     * @param term Term to check
     * @return bool True if has medical prefix
     */
    static bool has_medical_prefix(const std::string& term);
    
    /**
     * @brief Calculate character entropy for medical term analysis
     * @param term Term to analyze
     * @return float Entropy value
     */
    static float calculate_character_entropy(const std::string& term);
    
    /**
     * @brief Check if term contains measurement pattern
     * @param term Term to check
     * @return bool True if contains measurement pattern
     */
    static bool contains_measurement_pattern(const std::string& term);
    
    /**
     * @brief Count syllables in medical term
     * @param term Term to analyze
     * @return int Syllable count
     */
    static int count_syllables(const std::string& term);
    
    /**
     * @brief Validate NHS number format
     * @param nhs_number NHS number string
     * @return bool True if valid format
     */
    static bool is_valid_nhs_number(const std::string& nhs_number);
    
    /**
     * @brief Validate UK National Insurance number
     * @param ni_number National Insurance number
     * @return bool True if valid format
     */
    static bool is_valid_uk_ni_number(const std::string& ni_number);
    
    /**
     * @brief Validate SNOMED CT code format
     * @param code SNOMED CT code
     * @return bool True if valid format
     */
    static bool is_valid_snomed_code(const std::string& code);
    
    /**
     * @brief Validate Read code format (UK legacy)
     * @param code Read code
     * @return bool True if valid format
     */
    static bool is_valid_read_code(const std::string& code);

private:
    static const std::unordered_set<std::string> medical_suffixes_;
    static const std::unordered_set<std::string> medical_prefixes_;
    static const std::regex measurement_pattern_;
};

/**
 * @brief UK-specific localization utilities
 */
namespace UKLocalization {
    std::string format_uk_currency(double amount);
    double celsius_to_fahrenheit(double celsius);
    double fahrenheit_to_celsius(double fahrenheit);
    std::string format_temperature_celsius(double celsius);
    
    // UK-specific number formatting
    std::string format_uk_number(double number, int decimal_places = 2);
    
    // UK-specific decimal formatting with thousands separator option
    std::string format_uk_decimal(double value, int precision, bool use_thousands_separator = false);
    
    // UK-specific address formatting
    std::string format_uk_address(const std::string& line1, 
                                 const std::string& line2,
                                 const std::string& city,
                                 const std::string& county,
                                 const std::string& postcode);
    
    // UK-specific phone number formatting
    std::string format_uk_phone(int area_code, int number);
}

/**
 * @brief Processing-related utility functions
 */
class ProcessingUtils {
public:
    /**
     * @brief Convert processing stage enum to string
     * @param stage Processing stage enum value
     * @return String representation of the stage
     */
    static std::string stage_name(int stage);
};

} // namespace omop::common