/**
 * @file configuration_environment.cpp
 * @brief Implementation of multi-environment configuration overlay
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#include "configuration_environment.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <regex>
#include <algorithm>
#include <cstdlib>

namespace omop::common::config {

// Helper function to convert environment type to string
std::string to_string(EnvironmentType type) {
    switch (type) {
        case EnvironmentType::Development: return "development";
        case EnvironmentType::Testing: return "testing";
        case EnvironmentType::Staging: return "staging";
        case EnvironmentType::Production: return "production";
        case EnvironmentType::Local: return "local";
        case EnvironmentType::Custom: return "custom";
        default: return "unknown";
    }
}

EnvironmentType environment_type_from_string(const std::string& type_str) {
    std::string lower_type = type_str;
    std::transform(lower_type.begin(), lower_type.end(), lower_type.begin(), ::tolower);
    
    if (lower_type == "development" || lower_type == "dev") return EnvironmentType::Development;
    if (lower_type == "testing" || lower_type == "test") return EnvironmentType::Testing;
    if (lower_type == "staging" || lower_type == "stage") return EnvironmentType::Staging;
    if (lower_type == "production" || lower_type == "prod") return EnvironmentType::Production;
    if (lower_type == "local") return EnvironmentType::Local;
    
    return EnvironmentType::Custom;
}

// ConfigurationEnvironment implementation
ConfigurationEnvironment::ConfigurationEnvironment(const EnvironmentConfig& env_config)
    : config_(env_config) {
    statistics_.last_loaded = std::chrono::system_clock::time_point{};
}

ConfigurationEnvironment::~ConfigurationEnvironment() = default;

bool ConfigurationEnvironment::load_configuration() {
    auto start_time = std::chrono::high_resolution_clock::now();
    std::lock_guard<std::mutex> lock(values_mutex_);
    
    try {
        // Clear existing values
        values_.clear();
        
        // Load from file if specified
        if (!config_.config_file_path.empty()) {
            if (!load_from_file(config_.config_file_path)) {
                return false;
            }
            
            // Update file modification time for change detection
            std::error_code ec;
            last_file_write_time_ = std::filesystem::last_write_time(config_.config_file_path, ec);
        }
        
        // Resolve environment variables
        resolve_environment_variables();
        
        // Update statistics
        auto end_time = std::chrono::high_resolution_clock::now();
        auto load_time = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        
        std::lock_guard<std::mutex> stats_lock(stats_mutex_);
        statistics_.last_loaded = std::chrono::system_clock::now();
        ++statistics_.load_count;
        
        if (statistics_.avg_load_time.count() == 0) {
            statistics_.avg_load_time = load_time;
        } else {
            statistics_.avg_load_time = 
                (statistics_.avg_load_time * (statistics_.load_count - 1) + load_time) / statistics_.load_count;
        }
        
        statistics_.total_keys = values_.size();
        statistics_.sensitive_keys = 0;
        statistics_.required_keys = 0;
        
        for (const auto& [key, value] : values_) {
            if (value.is_sensitive) ++statistics_.sensitive_keys;
            if (value.is_required) ++statistics_.required_keys;
        }
        
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

bool ConfigurationEnvironment::reload_if_changed() {
    if (!config_.auto_reload || config_.config_file_path.empty()) {
        return false;
    }
    
    // Check if file has been modified
    std::error_code ec;
    auto current_write_time = std::filesystem::last_write_time(config_.config_file_path, ec);
    if (ec || current_write_time <= last_file_write_time_) {
        return false;
    }
    
    return load_configuration();
}

std::optional<ConfigValue> ConfigurationEnvironment::get_value(const std::string& key) const {
    std::lock_guard<std::mutex> lock(values_mutex_);
    
    auto it = values_.find(key);
    if (it != values_.end()) {
        return it->second;
    }
    
    return std::nullopt;
}

bool ConfigurationEnvironment::set_value(const std::string& key, const ConfigValue::ValueType& value, bool persist) {
    std::lock_guard<std::mutex> lock(values_mutex_);
    
    ConfigValue config_value;
    config_value.value = value;
    config_value.source_file = config_.config_file_path;
    config_value.source_environment = config_.name;
    config_value.priority = config_.priority;
    config_value.last_modified = std::chrono::system_clock::now();
    config_value.is_sensitive = is_sensitive_key(key);
    
    values_[key] = config_value;
    
    if (persist && !config_.config_file_path.empty()) {
        // In a full implementation, this would update the configuration file
        // For now, we'll just mark it as modified
        config_value.last_modified = std::chrono::system_clock::now();
    }
    
    return true;
}

bool ConfigurationEnvironment::has_key(const std::string& key) const {
    std::lock_guard<std::mutex> lock(values_mutex_);
    return values_.find(key) != values_.end();
}

std::vector<std::string> ConfigurationEnvironment::get_all_keys() const {
    std::lock_guard<std::mutex> lock(values_mutex_);
    
    std::vector<std::string> keys;
    keys.reserve(values_.size());
    
    for (const auto& [key, value] : values_) {
        keys.push_back(key);
    }
    
    return keys;
}

std::vector<std::string> ConfigurationEnvironment::validate() const {
    std::lock_guard<std::mutex> lock(values_mutex_);
    std::vector<std::string> errors;
    
    // Check required keys
    for (const auto& required_key : config_.required_keys) {
        if (values_.find(required_key) == values_.end()) {
            errors.push_back("Required key missing: " + required_key);
        }
    }
    
    // Check for empty values in required keys
    for (const auto& [key, value] : values_) {
        if (value.is_required) {
            if (std::holds_alternative<std::string>(value.value)) {
                const auto& str_value = std::get<std::string>(value.value);
                if (str_value.empty()) {
                    errors.push_back("Required key has empty value: " + key);
                }
            }
        }
    }
    
    return errors;
}

ConfigurationEnvironment::EnvironmentConfig ConfigurationEnvironment::get_environment_config() const {
    return config_;
}

std::string ConfigurationEnvironment::export_json(bool include_sensitive) const {
    std::lock_guard<std::mutex> lock(values_mutex_);
    
    std::ostringstream json;
    json << "{\n";
    json << "  \"environment\": \"" << config_.name << "\",\n";
    json << "  \"type\": \"" << to_string(config_.type) << "\",\n";
    json << "  \"priority\": " << config_.priority << ",\n";
    json << "  \"values\": {\n";
    
    bool first = true;
    for (const auto& [key, value] : values_) {
        if (!include_sensitive && value.is_sensitive) {
            continue;
        }
        
        if (!first) json << ",\n";
        first = false;
        
        json << "    \"" << key << "\": {\n";
        json << "      \"value\": ";
        
        // Output value based on type
        std::visit([&json](const auto& val) {
            using T = std::decay_t<decltype(val)>;
            if constexpr (std::is_same_v<T, std::string>) {
                json << "\"" << val << "\"";
            } else if constexpr (std::is_same_v<T, bool>) {
                json << (val ? "true" : "false");
            } else {
                json << val;
            }
        }, value.value);
        
        json << ",\n";
        json << "      \"source\": \"" << value.source_environment << "\",\n";
        json << "      \"priority\": " << value.priority << ",\n";
        json << "      \"is_sensitive\": " << (value.is_sensitive ? "true" : "false") << ",\n";
        json << "      \"is_required\": " << (value.is_required ? "true" : "false") << "\n";
        json << "    }";
    }
    
    json << "\n  }\n";
    json << "}";
    
    return json.str();
}

ConfigurationEnvironment::Statistics ConfigurationEnvironment::get_statistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return statistics_;
}

bool ConfigurationEnvironment::load_from_file(const std::string& file_path) {
    if (!std::filesystem::exists(file_path)) {
        return false;
    }
    
    std::ifstream file(file_path);
    if (!file.is_open()) {
        return false;
    }
    
    // Simple key-value parsing (in production, use proper YAML/JSON parser)
    std::string line;
    while (std::getline(file, line)) {
        // Skip comments and empty lines
        if (line.empty() || line[0] == '#') {
            continue;
        }
        
        size_t equals_pos = line.find('=');
        if (equals_pos == std::string::npos) {
            continue;
        }
        
        std::string key = line.substr(0, equals_pos);
        std::string value_str = line.substr(equals_pos + 1);
        
        // Trim whitespace
        key.erase(0, key.find_first_not_of(" \t"));
        key.erase(key.find_last_not_of(" \t") + 1);
        value_str.erase(0, value_str.find_first_not_of(" \t"));
        value_str.erase(value_str.find_last_not_of(" \t") + 1);
        
        // Create ConfigValue
        ConfigValue config_value;
        
        // Try to parse as different types
        if (value_str == "true" || value_str == "false") {
            config_value.value = (value_str == "true");
        } else if (std::regex_match(value_str, std::regex("^-?\\d+$"))) {
            config_value.value = std::stoll(value_str);
        } else if (std::regex_match(value_str, std::regex("^-?\\d*\\.\\d+$"))) {
            config_value.value = std::stod(value_str);
        } else {
            config_value.value = value_str;
        }
        
        config_value.source_file = file_path;
        config_value.source_environment = config_.name;
        config_value.priority = config_.priority;
        config_value.last_modified = std::chrono::system_clock::now();
        config_value.is_sensitive = is_sensitive_key(key);
        config_value.is_required = std::find(config_.required_keys.begin(), config_.required_keys.end(), key) 
                                  != config_.required_keys.end();
        
        values_[key] = config_value;
    }
    
    return true;
}

void ConfigurationEnvironment::resolve_environment_variables() {
    for (auto& [key, value] : values_) {
        if (std::holds_alternative<std::string>(value.value)) {
            std::string& str_value = std::get<std::string>(value.value);
            str_value = substitute_variables(str_value);
        }
    }
}

std::string ConfigurationEnvironment::substitute_variables(const std::string& value) const {
    std::string result = value;
    
    // Replace ${VAR} with environment variable value
    std::regex var_regex(R"(\$\{([^}]+)\})");
    std::smatch matches;
    
    while (std::regex_search(result, matches, var_regex)) {
        std::string var_name = matches[1].str();
        const char* env_value = std::getenv(var_name.c_str());
        
        if (env_value) {
            result.replace(matches.position(), matches.length(), env_value);
        } else {
            // Check custom environment variables
            auto it = config_.variables.find(var_name);
            if (it != config_.variables.end()) {
                result.replace(matches.position(), matches.length(), it->second);
            } else {
                // Leave as is if not found
                break;
            }
        }
    }
    
    return result;
}

bool ConfigurationEnvironment::is_sensitive_key(const std::string& key) const {
    // Common sensitive key patterns
    std::vector<std::string> sensitive_patterns = {
        ".*password.*", ".*secret.*", ".*key.*", ".*token.*", ".*credential.*", ".*api.*key.*"
    };
    
    for (const auto& pattern : sensitive_patterns) {
        std::regex regex(pattern, std::regex_constants::icase);
        if (std::regex_match(key, regex)) {
            return true;
        }
    }
    
    return false;
}

// MultiEnvironmentConfiguration implementation
MultiEnvironmentConfiguration::MultiEnvironmentConfiguration(const Config& config)
    : config_(config), current_environment_(config.current_environment) {
    
    if (config_.auto_detect_environment) {
        std::string detected = auto_detect_environment();
        if (!detected.empty()) {
            current_environment_ = detected;
        }
    }
    
    // Load default environments if base directory exists
    if (!config_.base_config_directory.empty() && 
        std::filesystem::exists(config_.base_config_directory)) {
        load_default_environments();
    }
}

MultiEnvironmentConfiguration::~MultiEnvironmentConfiguration() = default;

bool MultiEnvironmentConfiguration::register_environment(const ConfigurationEnvironment::EnvironmentConfig& env_config) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    auto env = std::make_unique<ConfigurationEnvironment>(env_config);
    if (!env->load_configuration()) {
        return false;
    }
    
    environments_[env_config.name] = std::move(env);
    return true;
}

bool MultiEnvironmentConfiguration::set_current_environment(const std::string& environment_name) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    if (environments_.find(environment_name) == environments_.end()) {
        return false;
    }
    
    current_environment_ = environment_name;
    
    // Clear cache when switching environments
    if (config_.cache_enabled) {
        value_cache_.clear();
    }
    
    return true;
}

std::string MultiEnvironmentConfiguration::get_current_environment() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return current_environment_;
}

bool MultiEnvironmentConfiguration::load_all_environments() {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    bool all_loaded = true;
    for (auto& [name, env] : environments_) {
        if (!env->load_configuration()) {
            all_loaded = false;
        }
    }
    
    // Clear cache after reloading
    if (config_.cache_enabled) {
        value_cache_.clear();
    }
    
    return all_loaded;
}

bool MultiEnvironmentConfiguration::has(const std::string& key) const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return resolve_value(key).has_value();
}

std::vector<std::string> MultiEnvironmentConfiguration::get_environments() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    std::vector<std::string> env_names;
    env_names.reserve(environments_.size());
    
    for (const auto& [name, env] : environments_) {
        env_names.push_back(name);
    }
    
    return env_names;
}

std::unordered_map<std::string, std::vector<std::string>> MultiEnvironmentConfiguration::validate_all() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    std::unordered_map<std::string, std::vector<std::string>> all_errors;
    
    for (const auto& [name, env] : environments_) {
        auto errors = env->validate();
        if (!errors.empty()) {
            all_errors[name] = errors;
        }
    }
    
    return all_errors;
}

std::string MultiEnvironmentConfiguration::export_configuration(const std::string& environment_name,
                                                               const std::string& format,
                                                               bool include_sensitive) const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    if (format != "json") {
        return "{}"; // Only JSON supported in this implementation
    }
    
    std::ostringstream output;
    output << "{\n";
    
    if (environment_name.empty()) {
        // Export all environments
        output << "  \"environments\": {\n";
        bool first_env = true;
        
        for (const auto& [name, env] : environments_) {
            if (!first_env) output << ",\n";
            first_env = false;
            
            output << "    \"" << name << "\": ";
            std::string env_json = env->export_json(include_sensitive);
            
            // Remove outer braces and indent
            if (env_json.size() > 2) {
                env_json = env_json.substr(1, env_json.size() - 2); // Remove {}
                std::istringstream iss(env_json);
                std::string line;
                bool first_line = true;
                output << "{\n";
                while (std::getline(iss, line)) {
                    if (!first_line) output << "\n";
                    first_line = false;
                    output << "    " << line;
                }
                output << "\n    }";
            } else {
                output << "{}";
            }
        }
        
        output << "\n  }\n";
    } else {
        // Export specific environment
        auto env = get_environment(environment_name);
        if (env) {
            std::string env_json = env->export_json(include_sensitive);
            output << env_json.substr(1, env_json.size() - 2); // Remove outer braces
        }
    }
    
    output << "}";
    return output.str();
}

std::vector<std::string> MultiEnvironmentConfiguration::reload_if_changed() {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    std::vector<std::string> reloaded_environments;
    
    for (auto& [name, env] : environments_) {
        if (env->reload_if_changed()) {
            reloaded_environments.push_back(name);
        }
    }
    
    // Clear cache if any environment was reloaded
    if (!reloaded_environments.empty() && config_.cache_enabled) {
        value_cache_.clear();
    }
    
    return reloaded_environments;
}

MultiEnvironmentConfiguration::OverallStatistics MultiEnvironmentConfiguration::get_statistics() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    OverallStatistics stats;
    stats.total_environments = environments_.size();
    stats.current_environment = current_environment_;
    stats.cache_hits = cache_hits_;
    stats.cache_misses = cache_misses_;
    
    for (const auto& [name, env] : environments_) {
        auto env_stats = env->get_statistics();
        stats.environment_stats[name] = env_stats;
        stats.total_keys += env_stats.total_keys;
    }
    
    return stats;
}

std::optional<ConfigValue> MultiEnvironmentConfiguration::resolve_value(const std::string& key) const {
    // Collect all values for this key from all environments
    std::vector<std::pair<int, ConfigValue>> candidates;
    
    for (const auto& [env_name, env] : environments_) {
        auto value = env->get_value(key);
        if (value.has_value()) {
            candidates.emplace_back(value->priority, *value);
        }
    }
    
    if (candidates.empty()) {
        ++cache_misses_;
        return std::nullopt;
    }
    
    // Sort by priority (highest first)
    std::sort(candidates.begin(), candidates.end(),
             [](const auto& a, const auto& b) { return a.first > b.first; });
    
    ++cache_hits_;
    return candidates[0].second;
}

ConfigurationEnvironment* MultiEnvironmentConfiguration::get_environment(const std::string& name) const {
    auto it = environments_.find(name);
    return (it != environments_.end()) ? it->second.get() : nullptr;
}

std::string MultiEnvironmentConfiguration::auto_detect_environment() const {
    // Check environment variables
    const char* env_var = std::getenv("OMOP_ENV");
    if (env_var) {
        return std::string(env_var);
    }
    
    env_var = std::getenv("NODE_ENV");
    if (env_var) {
        return std::string(env_var);
    }
    
    env_var = std::getenv("APP_ENV");
    if (env_var) {
        return std::string(env_var);
    }
    
    // Default to development
    return "development";
}

void MultiEnvironmentConfiguration::load_default_environments() {
    // Standard environment configurations
    std::vector<std::pair<std::string, EnvironmentType>> standard_envs = {
        {"development", EnvironmentType::Development},
        {"testing", EnvironmentType::Testing},
        {"staging", EnvironmentType::Staging},
        {"production", EnvironmentType::Production},
        {"local", EnvironmentType::Local}
    };
    
    for (const auto& [env_name, env_type] : standard_envs) {
        std::filesystem::path config_file = 
            std::filesystem::path(config_.base_config_directory) / (env_name + ".conf");
        
        if (std::filesystem::exists(config_file)) {
            ConfigurationEnvironment::EnvironmentConfig env_config;
            env_config.name = env_name;
            env_config.type = env_type;
            env_config.config_file_path = config_file.string();
            env_config.priority = (env_type == EnvironmentType::Production) ? 200 :
                                 (env_type == EnvironmentType::Staging) ? 150 :
                                 (env_type == EnvironmentType::Testing) ? 100 :
                                 (env_type == EnvironmentType::Development) ? 50 : 25;
            
            register_environment(env_config);
        }
    }
}

// ConfigurationFactory implementation
std::unique_ptr<MultiEnvironmentConfiguration> 
ConfigurationFactory::create_for_environment(const std::string& environment, 
                                            const std::string& base_directory) {
    MultiEnvironmentConfiguration::Config config;
    config.base_config_directory = base_directory;
    config.current_environment = environment;
    config.auto_detect_environment = false;
    
    auto multi_env_config = std::make_unique<MultiEnvironmentConfiguration>(config);
    register_standard_environments(*multi_env_config, base_directory);
    
    return multi_env_config;
}

std::unique_ptr<MultiEnvironmentConfiguration>
ConfigurationFactory::create_auto_detect(const std::string& base_directory) {
    MultiEnvironmentConfiguration::Config config;
    config.base_config_directory = base_directory;
    config.auto_detect_environment = true;
    
    auto multi_env_config = std::make_unique<MultiEnvironmentConfiguration>(config);
    register_standard_environments(*multi_env_config, base_directory);
    
    return multi_env_config;
}

void ConfigurationFactory::register_standard_environments(MultiEnvironmentConfiguration& config,
                                                         const std::string& base_directory) {
    std::vector<std::pair<std::string, EnvironmentType>> standard_envs = {
        {"local", EnvironmentType::Local},
        {"development", EnvironmentType::Development},
        {"testing", EnvironmentType::Testing},
        {"staging", EnvironmentType::Staging},
        {"production", EnvironmentType::Production}
    };
    
    for (const auto& [env_name, env_type] : standard_envs) {
        ConfigurationEnvironment::EnvironmentConfig env_config;
        env_config.name = env_name;
        env_config.type = env_type;
        env_config.config_file_path = base_directory + "/" + env_name + ".conf";
        env_config.description = "Standard " + env_name + " environment";
        
        // Set priorities
        env_config.priority = (env_type == EnvironmentType::Production) ? 200 :
                             (env_type == EnvironmentType::Staging) ? 150 :
                             (env_type == EnvironmentType::Testing) ? 100 :
                             (env_type == EnvironmentType::Development) ? 75 :
                             (env_type == EnvironmentType::Local) ? 50 : 25;
        
        config.register_environment(env_config);
    }
}

// Global multi-environment configuration instance
MultiEnvironmentConfiguration& global_multi_env_config() {
    static std::unique_ptr<MultiEnvironmentConfiguration> instance;
    static std::once_flag init_flag;
    
    std::call_once(init_flag, []() {
        instance = ConfigurationFactory::create_auto_detect();
    });
    
    return *instance;
}

} // namespace omop::common::config