/**
 * @file configuration_watcher.h
 * @brief Configuration file watcher component
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <chrono>
#include <thread>
#include <atomic>
#include <unordered_map>
#include <mutex>

namespace omop::common::config {

/**
 * @brief File change event details
 */
struct FileChangeEvent {
    enum class Type {
        Modified,
        Deleted,
        Created,
        MovedFrom,
        MovedTo
    };

    std::string file_path;
    Type event_type;
    std::chrono::system_clock::time_point timestamp;
    size_t file_size = 0;
    std::string checksum;
};

/**
 * @brief Configuration file watcher component
 * 
 * Features:
 * - Hot reloading with < 50ms detection time
 * - Multiple file watching
 * - Cross-platform implementation
 * - Change event filtering
 * - Performance optimized
 */
class ConfigurationWatcher {
public:
    /**
     * @brief Callback function for file changes
     */
    using ChangeCallback = std::function<void(const FileChangeEvent&)>;

    /**
     * @brief Configuration for the watcher
     */
    struct Config {
        std::chrono::milliseconds poll_interval{100};  // Polling interval
        bool enable_checksum_validation = true;        // Validate file contents
        bool recursive_watching = false;               // Watch subdirectories
        std::vector<std::string> ignore_patterns;      // Patterns to ignore
        size_t max_events_per_second = 100;           // Rate limiting
    };

    /**
     * @brief Constructor
     * @param config Watcher configuration
     */
    explicit ConfigurationWatcher(const Config& config = {});

    /**
     * @brief Destructor
     */
    ~ConfigurationWatcher();

    /**
     * @brief Start watching files
     * @return true if successful
     */
    bool start();

    /**
     * @brief Stop watching files
     */
    void stop();

    /**
     * @brief Add file to watch list
     * @param file_path Path to file to watch
     * @param callback Callback for changes to this file
     * @return true if successful
     */
    bool add_watch(const std::string& file_path, ChangeCallback callback);

    /**
     * @brief Remove file from watch list
     * @param file_path Path to file to remove
     * @return true if successful
     */
    bool remove_watch(const std::string& file_path);

    /**
     * @brief Add directory to watch list
     * @param directory_path Directory to watch
     * @param callback Callback for changes in directory
     * @param recursive Watch subdirectories recursively
     * @return true if successful
     */
    bool add_directory_watch(const std::string& directory_path, 
                            ChangeCallback callback,
                            bool recursive = false);

    /**
     * @brief Check if file is being watched
     * @param file_path File path to check
     * @return true if watched
     */
    bool is_watched(const std::string& file_path) const;

    /**
     * @brief Get list of watched files
     * @return Vector of watched file paths
     */
    std::vector<std::string> get_watched_files() const;

    /**
     * @brief Get watcher statistics
     */
    struct Statistics {
        size_t files_watched = 0;
        size_t directories_watched = 0;
        size_t events_processed = 0;
        size_t events_ignored = 0;
        std::chrono::milliseconds total_detection_time{0};
        std::chrono::milliseconds average_detection_time{0};
        std::chrono::system_clock::time_point last_event_time;
    };

    Statistics get_statistics() const;

    /**
     * @brief Force check for file changes
     * @param file_path Specific file to check (empty for all)
     * @return Number of changes detected
     */
    size_t force_check(const std::string& file_path = "");

private:
    /**
     * @brief File metadata for change detection
     */
    struct FileMetadata {
        std::string path;
        std::chrono::system_clock::time_point last_modified;
        size_t file_size = 0;
        std::string checksum;
        ChangeCallback callback;
    };

    /**
     * @brief Watcher thread function
     */
    void watcher_thread();

    /**
     * @brief Check single file for changes
     * @param metadata File metadata
     * @return true if changed
     */
    bool check_file_changes(FileMetadata& metadata);

    /**
     * @brief Calculate file checksum
     * @param file_path File path
     * @return Checksum string
     */
    std::string calculate_checksum(const std::string& file_path) const;

    /**
     * @brief Check if path matches ignore patterns
     * @param path Path to check
     * @return true if should be ignored
     */
    bool should_ignore(const std::string& path) const;

    // Watcher state
    Config config_;
    std::thread watcher_thread_;
    std::atomic<bool> running_{false};
    std::atomic<bool> should_stop_{false};
    
    // File monitoring
    std::vector<FileMetadata> watched_files_;
    std::mutex files_mutex_;
    
    // Statistics
    mutable Statistics stats_;
    mutable std::mutex stats_mutex_;
};

#ifdef __linux__
/**
 * @brief Linux-specific inotify watcher implementation
 */
class InotifyWatcher {
public:
    InotifyWatcher();
    ~InotifyWatcher();

    bool add_watch(const std::string& path, std::function<void()> callback);
    bool remove_watch(const std::string& path);
    void start();
    void stop();

private:
    int inotify_fd_{-1};
    std::unordered_map<int, std::string> watch_descriptors_;
    std::unordered_map<std::string, std::function<void()>> callbacks_;
    std::thread event_thread_;
    std::atomic<bool> running_{false};
    std::mutex callbacks_mutex_;
};
#endif

#ifdef __APPLE__
/**
 * @brief macOS-specific FSEvents watcher implementation
 */
class FSEventsWatcher {
public:
    FSEventsWatcher();
    ~FSEventsWatcher();

    bool add_watch(const std::string& path, std::function<void()> callback);
    bool remove_watch(const std::string& path);
    void start();
    void stop();

private:
    void* fs_event_stream_{nullptr}; // FSEventStreamRef
    std::unordered_map<std::string, std::function<void()>> callbacks_;
    std::atomic<bool> running_{false};
    std::mutex callbacks_mutex_;
};
#endif

#ifdef _WIN32
/**
 * @brief Windows-specific ReadDirectoryChangesW watcher implementation
 */
class WindowsWatcher {
public:
    WindowsWatcher();
    ~WindowsWatcher();

    bool add_watch(const std::string& path, std::function<void()> callback);
    bool remove_watch(const std::string& path);
    void start();
    void stop();

private:
    struct WatchInfo {
        void* directory_handle{nullptr}; // HANDLE
        std::function<void()> callback;
        std::vector<char> buffer;
    };
    std::unordered_map<std::string, WatchInfo> watches_;
    std::atomic<bool> running_{false};
    std::mutex watches_mutex_;
    std::thread event_thread_;
};
#endif

} // namespace omop::common::config