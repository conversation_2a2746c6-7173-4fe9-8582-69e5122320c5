/**
 * @file configuration_audit.cpp
 * @brief Implementation of configuration audit trail system
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#include "configuration_audit.h"
#include <sstream>
#include <iomanip>
#include <fstream>
#include <filesystem>
#include <regex>
#include <thread>
#include <random>
#include <algorithm>
#include <chrono>

#ifdef __unix__
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#endif

namespace omop::common::config {

// Helper function to convert enum to string
std::string to_string(AuditEventType type) {
    switch (type) {
        case AuditEventType::ConfigLoad: return "ConfigLoad";
        case AuditEventType::ConfigReload: return "ConfigReload";
        case AuditEventType::ValueChange: return "ValueChange";
        case AuditEventType::ValueAccess: return "ValueAccess";
        case AuditEventType::BackupCreated: return "BackupCreated";
        case AuditEventType::BackupRestored: return "BackupRestored";
        case AuditEventType::ValidationFailed: return "ValidationFailed";
        case AuditEventType::ValidationPassed: return "ValidationPassed";
        case AuditEventType::CacheHit: return "CacheHit";
        case AuditEventType::CacheMiss: return "CacheMiss";
        case AuditEventType::SecurityViolation: return "SecurityViolation";
        case AuditEventType::PermissionDenied: return "PermissionDenied";
        case AuditEventType::SystemError: return "SystemError";
        case AuditEventType::Custom: return "Custom";
        default: return "Unknown";
    }
}

std::string to_string(AuditSeverity severity) {
    switch (severity) {
        case AuditSeverity::Debug: return "DEBUG";
        case AuditSeverity::Info: return "INFO";
        case AuditSeverity::Warning: return "WARNING";
        case AuditSeverity::Error: return "ERROR";
        case AuditSeverity::Critical: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

// FileAuditStorage implementation
FileAuditStorage::FileAuditStorage(const std::string& log_file_path, 
                                 size_t max_file_size_mb,
                                 size_t max_files)
    : log_file_path_(log_file_path)
    , max_file_size_bytes_(max_file_size_mb * 1024 * 1024)
    , max_files_(max_files) {
    
    // Create directory if it doesn't exist
    std::filesystem::path path(log_file_path_);
    std::filesystem::create_directories(path.parent_path());
    
    // Open log file in append mode
    log_file_.open(log_file_path_, std::ios::app);
    if (!log_file_.is_open()) {
        throw std::runtime_error("Unable to open audit log file: " + log_file_path_);
    }
}

FileAuditStorage::~FileAuditStorage() {
    close();
}

bool FileAuditStorage::store_event(const AuditEvent& event) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (!log_file_.is_open()) {
        return false;
    }
    
    std::string serialized = serialize_event(event);
    log_file_ << serialized << std::endl;
    log_file_.flush();
    
    // Check if rotation is needed
    rotate_log_if_needed();
    
    return log_file_.good();
}

std::vector<AuditEvent> FileAuditStorage::query_events(const AuditQuery& query) const {
    std::lock_guard<std::mutex> lock(file_mutex_);
    std::vector<AuditEvent> results;
    
    std::ifstream file(log_file_path_);
    if (!file.is_open()) {
        return results;
    }
    
    std::string line;
    size_t count = 0;
    size_t skipped = 0;
    
    // For ascending order, we need to read all events first
    std::vector<AuditEvent> all_events;
    
    while (std::getline(file, line) && results.size() < query.limit) {
        auto event = deserialize_event(line);
        if (!event.has_value()) {
            continue;
        }
        
        if (matches_query(*event, query)) {
            if (query.ascending_order) {
                all_events.push_back(*event);
            } else {
                if (skipped < query.offset) {
                    ++skipped;
                    continue;
                }
                results.push_back(*event);
            }
        }
    }
    
    if (query.ascending_order) {
        // Sort by timestamp ascending and apply offset/limit
        std::sort(all_events.begin(), all_events.end(),
                 [](const auto& a, const auto& b) { return a.timestamp < b.timestamp; });
        
        auto start_it = all_events.begin() + std::min(query.offset, all_events.size());
        auto end_it = start_it + std::min(query.limit, static_cast<size_t>(all_events.end() - start_it));
        results.assign(start_it, end_it);
    }
    
    return results;
}

size_t FileAuditStorage::get_total_events() const {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    std::ifstream file(log_file_path_);
    if (!file.is_open()) {
        return 0;
    }
    
    size_t count = 0;
    std::string line;
    while (std::getline(file, line)) {
        ++count;
    }
    
    return count;
}

size_t FileAuditStorage::purge_events(std::chrono::system_clock::time_point older_than) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    std::ifstream input_file(log_file_path_);
    if (!input_file.is_open()) {
        return 0;
    }
    
    std::string temp_file_path = log_file_path_ + ".tmp";
    std::ofstream output_file(temp_file_path);
    if (!output_file.is_open()) {
        return 0;
    }
    
    size_t purged_count = 0;
    std::string line;
    
    while (std::getline(input_file, line)) {
        auto event = deserialize_event(line);
        if (event.has_value() && event->timestamp >= older_than) {
            output_file << line << std::endl;
        } else {
            ++purged_count;
        }
    }
    
    input_file.close();
    output_file.close();
    log_file_.close();
    
    // Replace original file with filtered content
    std::filesystem::rename(temp_file_path, log_file_path_);
    
    // Reopen log file
    log_file_.open(log_file_path_, std::ios::app);
    
    return purged_count;
}

void FileAuditStorage::close() {
    std::lock_guard<std::mutex> lock(file_mutex_);
    if (log_file_.is_open()) {
        log_file_.close();
    }
}

void FileAuditStorage::rotate_log_if_needed() {
    if (!std::filesystem::exists(log_file_path_)) {
        return;
    }
    
    std::error_code ec;
    auto file_size = std::filesystem::file_size(log_file_path_, ec);
    if (ec || file_size < max_file_size_bytes_) {
        return;
    }
    
    // Close current log
    log_file_.close();
    
    // Rotate existing log files
    for (size_t i = max_files_ - 1; i > 0; --i) {
        std::string old_file = log_file_path_ + "." + std::to_string(i);
        std::string new_file = log_file_path_ + "." + std::to_string(i + 1);
        
        if (std::filesystem::exists(old_file)) {
            if (i == max_files_ - 1) {
                std::filesystem::remove(new_file); // Remove oldest
            }
            std::filesystem::rename(old_file, new_file);
        }
    }
    
    // Move current log to .1
    std::string rotated_file = log_file_path_ + ".1";
    std::filesystem::rename(log_file_path_, rotated_file);
    
    // Reopen log file
    log_file_.open(log_file_path_, std::ios::app);
}

std::string FileAuditStorage::serialize_event(const AuditEvent& event) const {
    std::ostringstream oss;
    
    // Timestamp in ISO format
    auto time_t = std::chrono::system_clock::to_time_t(event.timestamp);
    oss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%S");
    
    // Microseconds
    auto ms = std::chrono::duration_cast<std::chrono::microseconds>(
        event.timestamp.time_since_epoch()) % 1000000;
    oss << "." << std::setfill('0') << std::setw(6) << ms.count() << "Z";
    
    // JSON-like format for easy parsing
    oss << " {";
    oss << "\"id\":\"" << event.event_id << "\",";
    oss << "\"type\":\"" << to_string(event.event_type) << "\",";
    oss << "\"severity\":\"" << to_string(event.severity) << "\",";
    oss << "\"config_path\":\"" << event.config_path << "\",";
    oss << "\"config_key\":\"" << event.config_key << "\",";
    oss << "\"old_value\":\"" << event.old_value << "\",";
    oss << "\"new_value\":\"" << event.new_value << "\",";
    oss << "\"user_id\":\"" << event.user_id << "\",";
    oss << "\"session_id\":\"" << event.session_id << "\",";
    oss << "\"client_ip\":\"" << event.client_ip << "\",";
    oss << "\"description\":\"" << event.description << "\",";
    oss << "\"duration_us\":" << event.duration.count() << ",";
    oss << "\"success\":" << (event.success ? "true" : "false") << ",";
    oss << "\"error_message\":\"" << event.error_message << "\",";
    oss << "\"process_id\":\"" << event.process_id << "\",";
    oss << "\"thread_id\":\"" << event.thread_id << "\"";
    oss << "}";
    
    return oss.str();
}

std::optional<AuditEvent> FileAuditStorage::deserialize_event(const std::string& line) const {
    // Simple parsing for the JSON-like format
    // In a full implementation, use a proper JSON parser
    
    size_t brace_pos = line.find('{');
    if (brace_pos == std::string::npos) {
        return std::nullopt;
    }
    
    AuditEvent event;
    
    // Extract timestamp
    std::string timestamp_str = line.substr(0, brace_pos - 1);
    // Parse ISO timestamp (simplified)
    std::tm tm = {};
    std::istringstream ss(timestamp_str);
    ss >> std::get_time(&tm, "%Y-%m-%dT%H:%M:%S");
    event.timestamp = std::chrono::system_clock::from_time_t(std::mktime(&tm));
    
    // Extract JSON content (simplified parsing)
    std::string json_content = line.substr(brace_pos);
    
    // Extract event ID
    size_t id_pos = json_content.find("\"id\":\"");
    if (id_pos != std::string::npos) {
        size_t start = id_pos + 6;
        size_t end = json_content.find("\"", start);
        if (end != std::string::npos) {
            event.event_id = json_content.substr(start, end - start);
        }
    }
    
    // Set default values for demonstration
    event.event_type = AuditEventType::Custom;
    event.severity = AuditSeverity::Info;
    event.success = true;
    
    return event;
}

bool FileAuditStorage::matches_query(const AuditEvent& event, const AuditQuery& query) const {
    // Check time range
    if (query.start_time.has_value() && event.timestamp < *query.start_time) {
        return false;
    }
    if (query.end_time.has_value() && event.timestamp > *query.end_time) {
        return false;
    }
    
    // Check event types
    if (!query.event_types.empty()) {
        if (std::find(query.event_types.begin(), query.event_types.end(), event.event_type) == query.event_types.end()) {
            return false;
        }
    }
    
    // Check severities
    if (!query.severities.empty()) {
        if (std::find(query.severities.begin(), query.severities.end(), event.severity) == query.severities.end()) {
            return false;
        }
    }
    
    // Check config path
    if (query.config_path.has_value() && event.config_path != *query.config_path) {
        return false;
    }
    
    // Check config key
    if (query.config_key.has_value() && event.config_key != *query.config_key) {
        return false;
    }
    
    // Check user ID
    if (query.user_id.has_value() && event.user_id != *query.user_id) {
        return false;
    }
    
    // Check session ID
    if (query.session_id.has_value() && event.session_id != *query.session_id) {
        return false;
    }
    
    // Check success filter
    if (query.success_filter.has_value() && event.success != *query.success_filter) {
        return false;
    }
    
    return true;
}

// ConfigurationAudit implementation
ConfigurationAudit::ConfigurationAudit(std::unique_ptr<IAuditStorage> storage, const Config& config)
    : storage_(std::move(storage)), config_(config) {
    enabled_.store(config_.enabled);
}

ConfigurationAudit::~ConfigurationAudit() = default;

void ConfigurationAudit::log_event(AuditEventType event_type,
                                  AuditSeverity severity,
                                  const std::string& config_path,
                                  const std::string& description,
                                  const std::unordered_map<std::string, std::string>& metadata) {
    if (!enabled_.load() || severity < config_.min_severity) {
        return;
    }
    
    AuditEvent event;
    event.event_id = generate_event_id();
    event.event_type = event_type;
    event.severity = severity;
    event.timestamp = std::chrono::system_clock::now();
    event.config_path = config_path;
    event.description = description;
    event.metadata = metadata;
    event.user_id = get_current_user_id();
    event.session_id = get_current_session_id();
    event.process_id = get_current_process_id();
    event.thread_id = get_current_thread_id();
    event.success = true;
    
    if (config_.include_call_stack) {
        event.call_stack = get_call_stack();
    }
    
    // Store event
    if (storage_) {
        storage_->store_event(event);
    }
    
    // Add to memory cache
    add_to_memory_cache(event);
    
    // Check alert conditions
    check_alert_conditions(event);
}

void ConfigurationAudit::log_value_change(const std::string& config_path,
                                         const std::string& config_key,
                                         const std::string& old_value,
                                         const std::string& new_value,
                                         const std::string& user_id,
                                         const std::string& session_id) {
    if (!should_audit_key(config_key)) {
        return;
    }
    
    std::unordered_map<std::string, std::string> metadata;
    metadata["change_type"] = "value_modification";
    
    AuditEvent event;
    event.event_id = generate_event_id();
    event.event_type = AuditEventType::ValueChange;
    event.severity = AuditSeverity::Info;
    event.timestamp = std::chrono::system_clock::now();
    event.config_path = config_path;
    event.config_key = config_key;
    event.old_value = sanitize_value(old_value, config_key);
    event.new_value = sanitize_value(new_value, config_key);
    event.user_id = user_id.empty() ? get_current_user_id() : user_id;
    event.session_id = session_id.empty() ? get_current_session_id() : session_id;
    event.process_id = get_current_process_id();
    event.thread_id = get_current_thread_id();
    event.description = "Configuration value changed: " + config_key;
    event.metadata = metadata;
    event.success = true;
    
    if (config_.include_call_stack) {
        event.call_stack = get_call_stack();
    }
    
    // Store event
    if (storage_) {
        storage_->store_event(event);
    }
    
    // Add to memory cache
    add_to_memory_cache(event);
    
    // Check alert conditions
    check_alert_conditions(event);
}

void ConfigurationAudit::log_value_access(const std::string& config_path,
                                         const std::string& config_key,
                                         const std::string& value,
                                         const std::string& user_id,
                                         const std::string& session_id) {
    if (!config_.audit_value_access || !should_audit_key(config_key)) {
        return;
    }
    
    log_event(AuditEventType::ValueAccess, AuditSeverity::Debug, config_path,
             "Configuration value accessed: " + config_key,
             {{"accessed_key", config_key}});
}

void ConfigurationAudit::log_config_load(const std::string& config_path,
                                        bool success,
                                        std::chrono::microseconds duration,
                                        const std::string& error_message) {
    AuditEvent event;
    event.event_id = generate_event_id();
    event.event_type = AuditEventType::ConfigLoad;
    event.severity = success ? AuditSeverity::Info : AuditSeverity::Error;
    event.timestamp = std::chrono::system_clock::now();
    event.config_path = config_path;
    event.description = success ? "Configuration loaded successfully" : "Configuration load failed";
    event.duration = duration;
    event.success = success;
    event.error_message = error_message;
    event.user_id = get_current_user_id();
    event.session_id = get_current_session_id();
    event.process_id = get_current_process_id();
    event.thread_id = get_current_thread_id();
    
    if (config_.include_call_stack) {
        event.call_stack = get_call_stack();
    }
    
    // Store event
    if (storage_) {
        storage_->store_event(event);
    }
    
    // Add to memory cache
    add_to_memory_cache(event);
    
    // Check alert conditions
    check_alert_conditions(event);
}

void ConfigurationAudit::log_security_violation(const std::string& violation_type,
                                               const std::string& config_path,
                                               const std::string& config_key,
                                               const std::string& description,
                                               const std::string& user_id,
                                               const std::string& client_ip) {
    AuditEvent event;
    event.event_id = generate_event_id();
    event.event_type = AuditEventType::SecurityViolation;
    event.severity = AuditSeverity::Critical;
    event.timestamp = std::chrono::system_clock::now();
    event.config_path = config_path;
    event.config_key = config_key;
    event.description = description;
    event.user_id = user_id;
    event.client_ip = client_ip;
    event.process_id = get_current_process_id();
    event.thread_id = get_current_thread_id();
    event.metadata["violation_type"] = violation_type;
    event.success = false;
    
    if (config_.include_call_stack) {
        event.call_stack = get_call_stack();
    }
    
    // Store event
    if (storage_) {
        storage_->store_event(event);
    }
    
    // Add to memory cache
    add_to_memory_cache(event);
    
    // Always trigger alerts for security violations
    if (alert_callback_) {
        alert_callback_(event);
    }
}

std::vector<AuditEvent> ConfigurationAudit::query_events(const AuditQuery& query) const {
    if (storage_) {
        return storage_->query_events(query);
    }
    return {};
}

ConfigurationAudit::Statistics ConfigurationAudit::get_statistics() const {
    Statistics stats;
    
    if (!storage_) {
        return stats;
    }
    
    stats.total_events = storage_->get_total_events();
    
    // Get recent events for analysis
    AuditQuery query;
    query.limit = 10000; // Analyze last 10k events
    auto events = storage_->query_events(query);
    
    for (const auto& event : events) {
        // Count by type
        int type_index = static_cast<int>(event.event_type);
        if (type_index >= 0 && type_index <= static_cast<int>(AuditEventType::Custom)) {
            ++stats.events_by_type[type_index];
        }
        
        // Count by severity
        int severity_index = static_cast<int>(event.severity);
        if (severity_index >= 0 && severity_index <= static_cast<int>(AuditSeverity::Critical)) {
            ++stats.events_by_severity[severity_index];
        }
        
        // Track success/failure
        if (event.success) {
            ++stats.successful_operations;
        } else {
            ++stats.failed_operations;
        }
        
        // Track top users
        if (!event.user_id.empty()) {
            ++stats.top_users[event.user_id];
        }
        
        // Track top config paths
        if (!event.config_path.empty()) {
            ++stats.top_config_paths[event.config_path];
        }
        
        // Track timestamp range
        if (stats.oldest_event == std::chrono::system_clock::time_point{} || event.timestamp < stats.oldest_event) {
            stats.oldest_event = event.timestamp;
        }
        if (stats.newest_event == std::chrono::system_clock::time_point{} || event.timestamp > stats.newest_event) {
            stats.newest_event = event.timestamp;
        }
    }
    
    return stats;
}

std::string ConfigurationAudit::generate_compliance_report(std::chrono::system_clock::time_point start_time,
                                                          std::chrono::system_clock::time_point end_time,
                                                          const std::string& format) const {
    AuditQuery query;
    query.start_time = start_time;
    query.end_time = end_time;
    query.limit = 100000; // Large limit for comprehensive report
    
    auto events = query_events(query);
    
    std::ostringstream report;
    
    if (format == "json") {
        report << "{\n";
        report << "  \"report_period\": {\n";
        report << "    \"start\": \"" << std::chrono::duration_cast<std::chrono::seconds>(start_time.time_since_epoch()).count() << "\",\n";
        report << "    \"end\": \"" << std::chrono::duration_cast<std::chrono::seconds>(end_time.time_since_epoch()).count() << "\"\n";
        report << "  },\n";
        report << "  \"summary\": {\n";
        report << "    \"total_events\": " << events.size() << ",\n";
        
        size_t security_violations = 0;
        size_t failed_operations = 0;
        for (const auto& event : events) {
            if (event.event_type == AuditEventType::SecurityViolation) {
                ++security_violations;
            }
            if (!event.success) {
                ++failed_operations;
            }
        }
        
        report << "    \"security_violations\": " << security_violations << ",\n";
        report << "    \"failed_operations\": " << failed_operations << "\n";
        report << "  }\n";
        report << "}";
    } else if (format == "csv") {
        report << "Timestamp,Event Type,Severity,Config Path,Config Key,User ID,Description,Success\n";
        for (const auto& event : events) {
            auto time_t = std::chrono::system_clock::to_time_t(event.timestamp);
            report << std::put_time(std::gmtime(&time_t), "%Y-%m-%d %H:%M:%S") << ",";
            report << to_string(event.event_type) << ",";
            report << to_string(event.severity) << ",";
            report << event.config_path << ",";
            report << event.config_key << ",";
            report << event.user_id << ",";
            report << event.description << ",";
            report << (event.success ? "true" : "false") << "\n";
        }
    }
    
    return report.str();
}

// Helper method implementations
std::string ConfigurationAudit::generate_event_id() const {
    static std::atomic<uint64_t> counter{0};
    
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    uint64_t count = counter.fetch_add(1);
    
    std::ostringstream oss;
    oss << "audit_" << timestamp << "_" << count;
    return oss.str();
}

bool ConfigurationAudit::should_audit_key(const std::string& config_key) const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    // Check excluded patterns
    for (const auto& pattern : config_.excluded_key_patterns) {
        std::regex regex(pattern);
        if (std::regex_match(config_key, regex)) {
            return false;
        }
    }
    
    return true;
}

bool ConfigurationAudit::is_sensitive_key(const std::string& config_key) const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    for (const auto& pattern : config_.sensitive_key_patterns) {
        std::regex regex(pattern);
        if (std::regex_match(config_key, regex)) {
            return true;
        }
    }
    
    // Common sensitive key patterns
    std::vector<std::string> default_patterns = {
        ".*password.*", ".*secret.*", ".*key.*", ".*token.*", ".*credential.*"
    };
    
    for (const auto& pattern : default_patterns) {
        std::regex regex(pattern, std::regex_constants::icase);
        if (std::regex_match(config_key, regex)) {
            return true;
        }
    }
    
    return false;
}

std::string ConfigurationAudit::sanitize_value(const std::string& value, const std::string& config_key) const {
    if (!config_.include_sensitive_data && is_sensitive_key(config_key)) {
        return "[REDACTED]";
    }
    return value;
}

void ConfigurationAudit::add_to_memory_cache(const AuditEvent& event) const {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    memory_cache_.push_back(event);
    
    // Keep only the most recent events
    if (memory_cache_.size() > config_.max_events_memory) {
        memory_cache_.erase(memory_cache_.begin(), 
                           memory_cache_.begin() + (memory_cache_.size() - config_.max_events_memory));
    }
}

void ConfigurationAudit::check_alert_conditions(const AuditEvent& event) {
    if (!alert_callback_) {
        return;
    }
    
    // Trigger alerts for critical events
    if (event.severity >= AuditSeverity::Critical ||
        event.event_type == AuditEventType::SecurityViolation ||
        event.event_type == AuditEventType::PermissionDenied) {
        alert_callback_(event);
    }
}

std::vector<std::string> ConfigurationAudit::get_call_stack() const {
    // Simplified call stack - in production use proper stack walking
    return {"ConfigurationAudit::get_call_stack"};
}

std::string ConfigurationAudit::get_current_user_id() const {
#ifdef __unix__
    struct passwd* pw = getpwuid(getuid());
    if (pw) {
        return std::string(pw->pw_name);
    }
#endif
    return "unknown";
}

std::string ConfigurationAudit::get_current_session_id() const {
    // Generate or retrieve session ID
    static std::string session_id = "session_" + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count());
    return session_id;
}

std::string ConfigurationAudit::get_current_process_id() const {
#ifdef __unix__
    return std::to_string(getpid());
#else
    return "unknown";
#endif
}

std::string ConfigurationAudit::get_current_thread_id() const {
    std::ostringstream oss;
    oss << std::this_thread::get_id();
    return oss.str();
}

size_t ConfigurationAudit::purge_old_events() {
    if (!storage_) {
        return 0;
    }
    
    auto cutoff_time = std::chrono::system_clock::now() - config_.retention_period;
    return storage_->purge_events(cutoff_time);
}

void ConfigurationAudit::set_enabled(bool enabled) {
    enabled_.store(enabled);
}

void ConfigurationAudit::update_config(const Config& config) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    config_ = config;
    enabled_.store(config_.enabled);
}

ConfigurationAudit::Config ConfigurationAudit::get_config() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return config_;
}

void ConfigurationAudit::set_alert_callback(std::function<void(const AuditEvent&)> callback) {
    alert_callback_ = std::move(callback);
}

// AuditScope implementation
AuditScope::AuditScope(ConfigurationAudit& audit,
                      AuditEventType event_type,
                      const std::string& config_path,
                      const std::string& description)
    : audit_(audit), event_type_(event_type), config_path_(config_path), 
      description_(description), start_time_(std::chrono::high_resolution_clock::now()) {
}

AuditScope::~AuditScope() {
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time_);
    
    std::unordered_map<std::string, std::string> final_metadata = metadata_;
    final_metadata["duration_us"] = std::to_string(duration.count());
    
    if (success_) {
        audit_.log_event(event_type_, AuditSeverity::Info, config_path_, 
                        description_ + " completed successfully", final_metadata);
    } else {
        audit_.log_event(event_type_, AuditSeverity::Error, config_path_, 
                        description_ + " failed: " + error_message_, final_metadata);
    }
}

void AuditScope::set_failed(const std::string& error_message) {
    success_ = false;
    error_message_ = error_message;
}

void AuditScope::add_metadata(const std::string& key, const std::string& value) {
    metadata_[key] = value;
}

// Global audit instance
ConfigurationAudit& global_audit() {
    static std::unique_ptr<ConfigurationAudit> instance;
    static std::once_flag init_flag;
    
    std::call_once(init_flag, []() {
        auto storage = std::make_unique<FileAuditStorage>("/tmp/omop_config_audit.log");
        instance = std::make_unique<ConfigurationAudit>(std::move(storage));
    });
    
    return *instance;
}

} // namespace omop::common::config