/**
 * @file configuration_validator.h
 * @brief Configuration validation component
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <functional>
#include <regex>
#include <mutex>
#include <shared_mutex>
#include <chrono>
#include <yaml-cpp/yaml.h>

namespace omop::common::config {

/**
 * @brief Validation error details
 */
struct ValidationError {
    enum class Severity {
        Warning,
        Error,
        Critical
    };

    std::string path;        // Configuration path
    std::string message;     // Error message
    Severity severity;       // Error severity
    std::string rule_name;   // Name of failed validation rule
    std::any actual_value;   // Actual value that failed
    std::any expected_value; // Expected value or pattern
};

/**
 * @brief Validation result
 */
struct ValidationResult {
    bool is_valid = true;
    std::vector<ValidationError> errors;
    std::vector<ValidationError> warnings;
    std::chrono::milliseconds validation_time{0};
    size_t rules_evaluated = 0;

    bool has_critical_errors() const {
        return std::any_of(errors.begin(), errors.end(),
            [](const auto& error) {
                return error.severity == ValidationError::Severity::Critical;
            });
    }
};

/**
 * @brief Base validation rule
 */
class ValidationRule {
public:
    virtual ~ValidationRule() = default;
    
    /**
     * @brief Validate configuration path
     * @param path Configuration path
     * @param value Configuration value
     * @param config Full configuration for context
     * @return Validation errors (empty if valid)
     */
    virtual std::vector<ValidationError> validate(
        const std::string& path,
        const YAML::Node& value,
        const YAML::Node& config) const = 0;

    /**
     * @brief Get rule name
     * @return Rule name for error reporting
     */
    virtual std::string get_name() const = 0;

    /**
     * @brief Get rule description
     * @return Human-readable description
     */
    virtual std::string get_description() const = 0;
};

/**
 * @brief Configuration validation component
 * 
 * Features:
 * - Schema-based validation
 * - Custom validation rules
 * - Performance optimization with rule compilation
 * - Detailed error reporting
 * - Severity levels
 */
class ConfigurationValidator {
public:
    /**
     * @brief Constructor
     */
    ConfigurationValidator();

    /**
     * @brief Destructor
     */
    ~ConfigurationValidator();

    /**
     * @brief Add validation schema from file
     * @param schema_path Path to schema file
     * @return true if successful
     */
    bool add_schema(const std::string& schema_path);

    /**
     * @brief Add validation schema from YAML
     * @param schema Schema as YAML node
     * @param schema_name Name for the schema
     * @return true if successful
     */
    bool add_schema(const YAML::Node& schema, const std::string& schema_name);

    /**
     * @brief Add custom validation rule
     * @param rule Custom validation rule
     */
    void add_rule(std::unique_ptr<ValidationRule> rule);

    /**
     * @brief Validate configuration
     * @param config Configuration to validate
     * @return Validation result
     */
    ValidationResult validate(const YAML::Node& config) const;

    /**
     * @brief Validate specific path
     * @param config Configuration
     * @param path Path to validate
     * @return Validation result for specific path
     */
    ValidationResult validate_path(const YAML::Node& config, 
                                  const std::string& path) const;

    /**
     * @brief Compile validation rules for better performance
     * @return true if successful
     */
    bool compile_rules();

    /**
     * @brief Get validator statistics
     */
    struct Statistics {
        size_t schemas_loaded = 0;
        size_t custom_rules = 0;
        size_t validations_performed = 0;
        size_t total_errors_found = 0;
        std::chrono::milliseconds total_validation_time{0};
        std::chrono::milliseconds average_validation_time{0};
    };

    Statistics get_statistics() const;

private:
    // Validation rules storage
    std::vector<std::unique_ptr<ValidationRule>> rules_;
    mutable std::shared_mutex rules_mutex_;
    
    // Statistics tracking
    mutable Statistics stats_;
    mutable std::mutex stats_mutex_;
    
    // Configuration
    bool strict_mode_{true};
    bool enable_warnings_{true};
    
    // Helper methods
    void update_statistics(const ValidationResult& result) const;
    bool is_required_field_missing(const YAML::Node& node, const std::string& field_path) const;
    bool is_type_valid(const YAML::Node& node, const std::string& expected_type) const;
};

// Built-in validation rules

/**
 * @brief Required field validation rule
 */
class RequiredFieldRule : public ValidationRule {
public:
    explicit RequiredFieldRule(std::vector<std::string> required_paths)
        : required_paths_(std::move(required_paths)) {}

    std::vector<ValidationError> validate(
        const std::string& path,
        const YAML::Node& value,
        const YAML::Node& config) const override;

    std::string get_name() const override { return "RequiredField"; }
    std::string get_description() const override { return "Validates required configuration fields"; }

private:
    std::vector<std::string> required_paths_;
};

/**
 * @brief Type validation rule
 */
class TypeValidationRule : public ValidationRule {
public:
    enum class Type {
        String, Integer, Double, Boolean, Array, Object
    };

    TypeValidationRule(std::string path_pattern, Type expected_type)
        : path_pattern_(std::move(path_pattern)), expected_type_(expected_type) {}

    std::vector<ValidationError> validate(
        const std::string& path,
        const YAML::Node& value,
        const YAML::Node& config) const override;

    std::string get_name() const override { return "TypeValidation"; }
    std::string get_description() const override { return "Validates configuration value types"; }

private:
    std::string path_pattern_;
    Type expected_type_;
};

/**
 * @brief Range validation rule
 */
class RangeValidationRule : public ValidationRule {
public:
    RangeValidationRule(std::string path_pattern, double min_value, double max_value)
        : path_pattern_(std::move(path_pattern)), min_value_(min_value), max_value_(max_value) {}

    std::vector<ValidationError> validate(
        const std::string& path,
        const YAML::Node& value,
        const YAML::Node& config) const override;

    std::string get_name() const override { return "RangeValidation"; }
    std::string get_description() const override { return "Validates numeric ranges"; }

private:
    std::string path_pattern_;
    double min_value_;
    double max_value_;
};

/**
 * @brief Regular expression validation rule
 */
class RegexValidationRule : public ValidationRule {
public:
    RegexValidationRule(std::string path_pattern, std::string regex_pattern)
        : path_pattern_(std::move(path_pattern))
        , regex_(std::move(regex_pattern)) {}

    std::vector<ValidationError> validate(
        const std::string& path,
        const YAML::Node& value,
        const YAML::Node& config) const override;

    std::string get_name() const override { return "RegexValidation"; }
    std::string get_description() const override { return "Validates string patterns"; }

private:
    std::string path_pattern_;
    std::regex regex_;
};

/**
 * @brief Database connection validation rule
 */
class DatabaseConnectionRule : public ValidationRule {
public:
    DatabaseConnectionRule() = default;

    std::vector<ValidationError> validate(
        const std::string& path,
        const YAML::Node& value,
        const YAML::Node& config) const override;

    std::string get_name() const override { return "DatabaseConnection"; }
    std::string get_description() const override { return "Validates database connection parameters"; }
};

/**
 * @brief File existence validation rule
 */
class FileExistenceRule : public ValidationRule {
public:
    explicit FileExistenceRule(std::vector<std::string> file_paths)
        : file_paths_(std::move(file_paths)) {}

    std::vector<ValidationError> validate(
        const std::string& path,
        const YAML::Node& value,
        const YAML::Node& config) const override;

    std::string get_name() const override { return "FileExistence"; }
    std::string get_description() const override { return "Validates that required files exist"; }

private:
    std::vector<std::string> file_paths_;
};

} // namespace omop::common::config