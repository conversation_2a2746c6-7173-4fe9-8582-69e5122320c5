/**
 * @file configuration_backup.cpp
 * @brief Implementation of configuration backup and restore
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#include "configuration_backup.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <random>
#include <iomanip>
#include <filesystem>
#include <openssl/sha.h>
#include <openssl/evp.h>
#include <openssl/aes.h>
#include <zlib.h>

namespace omop::common::config {

// ConfigurationBackup implementation
ConfigurationBackup::ConfigurationBackup(const std::string& backup_directory)
    : backup_directory_(backup_directory) {
    
    // Create backup directory if it doesn't exist
    std::filesystem::create_directories(backup_directory_);
    
    // Set metadata file path
    metadata_file_ = backup_directory_ / "backup_metadata.json";
    
    // Load existing metadata
    load_metadata();
}

ConfigurationBackup::~ConfigurationBackup() = default;

BackupResult ConfigurationBackup::create_backup(const std::string& config_path, const BackupOptions& options) {
    std::lock_guard<std::mutex> lock(backup_mutex_);
    
    BackupResult result;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        // Validate configuration file before backup if requested
        if (options.validate_before_backup && !validate_configuration_file(config_path)) {
            result.error_message = "Configuration validation failed before backup";
            return result;
        }
        
        // Generate backup ID
        std::string backup_id = generate_backup_id();
        
        // Create backup metadata
        BackupMetadata metadata;
        metadata.backup_id = backup_id;
        metadata.original_config_path = config_path;
        metadata.timestamp = std::chrono::system_clock::now();
        metadata.description = options.description.empty() ? "Manual backup" : options.description;
        metadata.created_by = "ConfigurationBackup";
        metadata.config_version = "2.0";
        
        // Determine backup file path
        std::filesystem::path source_path(config_path);
        std::string backup_filename = backup_id;
        if (options.compress) {
            backup_filename += ".gz";
            metadata.is_compressed = true;
            metadata.compression_algorithm = "gzip";
        }
        if (options.encrypt) {
            backup_filename += ".enc";
            metadata.is_encrypted = true;
            metadata.encryption_algorithm = "AES-256";
        }
        backup_filename += ".bak";
        
        metadata.backup_path = backup_directory_ / backup_filename;
        
        // Get original file size
        std::error_code ec;
        metadata.file_size_bytes = std::filesystem::file_size(source_path, ec);
        if (ec) {
            result.error_message = "Unable to determine file size: " + ec.message();
            return result;
        }
        
        // Calculate checksum of original file
        metadata.checksum = calculate_file_checksum(source_path);
        if (metadata.checksum.empty()) {
            result.error_message = "Unable to calculate file checksum";
            return result;
        }
        
        // Create backup file
        std::filesystem::path temp_backup = metadata.backup_path;
        temp_backup += ".tmp";
        
        if (options.compress && options.encrypt) {
            // Compress then encrypt
            std::filesystem::path compressed_temp = temp_backup;
            compressed_temp += ".gz";
            
            if (!compress_file(source_path, compressed_temp)) {
                result.error_message = "Failed to compress configuration file";
                return result;
            }
            
            if (!encrypt_file(compressed_temp, temp_backup)) {
                result.error_message = "Failed to encrypt configuration file";
                std::filesystem::remove(compressed_temp);
                return result;
            }
            
            std::filesystem::remove(compressed_temp);
        } else if (options.compress) {
            if (!compress_file(source_path, temp_backup)) {
                result.error_message = "Failed to compress configuration file";
                return result;
            }
        } else if (options.encrypt) {
            if (!encrypt_file(source_path, temp_backup)) {
                result.error_message = "Failed to encrypt configuration file";
                return result;
            }
        } else {
            // Simple copy
            std::filesystem::copy_file(source_path, temp_backup, ec);
            if (ec) {
                result.error_message = "Failed to copy configuration file: " + ec.message();
                return result;
            }
        }
        
        // Move temp file to final location
        std::filesystem::rename(temp_backup, metadata.backup_path, ec);
        if (ec) {
            result.error_message = "Failed to finalize backup: " + ec.message();
            std::filesystem::remove(temp_backup);
            return result;
        }
        
        // Save metadata
        metadata_entries_.push_back(metadata);
        save_metadata();
        
        // Cleanup old backups if needed
        if (options.max_backups > 0) {
            auto config_backups = list_backups(config_path);
            if (config_backups.size() > options.max_backups) {
                // Sort by timestamp (oldest first)
                std::sort(config_backups.begin(), config_backups.end(),
                         [](const auto& a, const auto& b) { return a.timestamp < b.timestamp; });
                
                // Delete excess backups
                size_t to_delete = config_backups.size() - options.max_backups;
                for (size_t i = 0; i < to_delete; ++i) {
                    delete_backup(config_backups[i].backup_id);
                }
            }
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        result.backup_duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        result.metadata = metadata;
        result.success = true;
        
        // Update statistics
        update_statistics(result);
        
        // Call callback if set
        if (backup_callback_) {
            backup_callback_(result);
        }
        
    } catch (const std::exception& e) {
        result.error_message = std::string("Exception during backup: ") + e.what();
        auto end_time = std::chrono::high_resolution_clock::now();
        result.backup_duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        update_statistics(result);
    }
    
    return result;
}

BackupResult ConfigurationBackup::create_automatic_backup(const std::string& config_path, const std::string& description) {
    BackupOptions options;
    options.description = description.empty() ? "Automatic backup" : description;
    options.compress = true;
    options.max_backups = max_auto_backups_.load();
    return create_backup(config_path, options);
}

RestoreResult ConfigurationBackup::restore_backup(const std::string& backup_id, const RestoreOptions& options) {
    std::lock_guard<std::mutex> lock(backup_mutex_);
    
    RestoreResult result;
    result.restore_time = std::chrono::system_clock::now();
    
    try {
        // Find backup metadata
        auto metadata_it = std::find_if(metadata_entries_.begin(), metadata_entries_.end(),
                                       [&backup_id](const auto& meta) { return meta.backup_id == backup_id; });
        
        if (metadata_it == metadata_entries_.end()) {
            result.error_message = "Backup not found: " + backup_id;
            return result;
        }
        
        const auto& metadata = *metadata_it;
        
        // Verify backup exists and is valid
        if (!std::filesystem::exists(metadata.backup_path)) {
            result.error_message = "Backup file not found: " + metadata.backup_path.string();
            return result;
        }
        
        if (!verify_backup(backup_id)) {
            if (!options.force_restore) {
                result.error_message = "Backup verification failed. Use force_restore=true to override.";
                return result;
            }
            result.validation_warnings.push_back("Backup verification failed but restore was forced");
        }
        
        // Determine target path
        std::string target_path = options.target_config_path.empty() ? 
                                 metadata.original_config_path : options.target_config_path;
        
        // Create backup of current config if requested
        if (options.create_backup_before_restore && std::filesystem::exists(target_path)) {
            BackupOptions backup_opts;
            backup_opts.description = "Pre-restore backup";
            auto backup_result = create_backup(target_path, backup_opts);
            if (!backup_result.success) {
                result.error_message = "Failed to create pre-restore backup: " + backup_result.error_message;
                return result;
            }
        }
        
        // Create temporary file for restoration
        std::filesystem::path temp_restore = std::filesystem::path(target_path);
        temp_restore += ".restore.tmp";
        
        // Restore file based on compression/encryption
        if (metadata.is_compressed && metadata.is_encrypted) {
            // Decrypt then decompress
            std::filesystem::path decrypted_temp = temp_restore;
            decrypted_temp += ".dec";
            
            if (!decrypt_file(metadata.backup_path, decrypted_temp)) {
                result.error_message = "Failed to decrypt backup file";
                return result;
            }
            
            if (!decompress_file(decrypted_temp, temp_restore)) {
                result.error_message = "Failed to decompress backup file";
                std::filesystem::remove(decrypted_temp);
                return result;
            }
            
            std::filesystem::remove(decrypted_temp);
        } else if (metadata.is_compressed) {
            if (!decompress_file(metadata.backup_path, temp_restore)) {
                result.error_message = "Failed to decompress backup file";
                return result;
            }
        } else if (metadata.is_encrypted) {
            if (!decrypt_file(metadata.backup_path, temp_restore)) {
                result.error_message = "Failed to decrypt backup file";
                return result;
            }
        } else {
            // Simple copy
            std::error_code ec;
            std::filesystem::copy_file(metadata.backup_path, temp_restore, ec);
            if (ec) {
                result.error_message = "Failed to copy backup file: " + ec.message();
                return result;
            }
        }
        
        // Validate restored configuration if requested
        if (options.validate_after_restore) {
            if (validate_configuration_file(temp_restore)) {
                result.validation_passed = true;
            } else {
                result.validation_passed = false;
                result.validation_warnings.push_back("Restored configuration failed validation");
                if (!options.force_restore) {
                    result.error_message = "Restored configuration validation failed";
                    std::filesystem::remove(temp_restore);
                    return result;
                }
            }
        }
        
        // Move restored file to final location
        std::error_code ec;
        std::filesystem::rename(temp_restore, target_path, ec);
        if (ec) {
            result.error_message = "Failed to finalize restore: " + ec.message();
            std::filesystem::remove(temp_restore);
            return result;
        }
        
        result.success = true;
        result.restored_config_path = target_path;
        
        // Update statistics
        update_statistics(result);
        
        // Call callback if set
        if (restore_callback_) {
            restore_callback_(result);
        }
        
    } catch (const std::exception& e) {
        result.error_message = std::string("Exception during restore: ") + e.what();
        update_statistics(result);
    }
    
    return result;
}

RestoreResult ConfigurationBackup::restore_latest_backup(const std::string& original_config_path, const RestoreOptions& options) {
    auto backups = list_backups(original_config_path);
    if (backups.empty()) {
        RestoreResult result;
        result.error_message = "No backups found for configuration: " + original_config_path;
        return result;
    }
    
    // Sort by timestamp (newest first)
    std::sort(backups.begin(), backups.end(),
             [](const auto& a, const auto& b) { return a.timestamp > b.timestamp; });
    
    return restore_backup(backups[0].backup_id, options);
}

std::vector<BackupMetadata> ConfigurationBackup::list_backups(const std::string& config_path) const {
    std::lock_guard<std::mutex> lock(backup_mutex_);
    
    if (config_path.empty()) {
        return metadata_entries_;
    }
    
    std::vector<BackupMetadata> filtered_backups;
    std::copy_if(metadata_entries_.begin(), metadata_entries_.end(),
                std::back_inserter(filtered_backups),
                [&config_path](const auto& meta) { return meta.original_config_path == config_path; });
    
    return filtered_backups;
}

std::optional<BackupMetadata> ConfigurationBackup::get_backup_metadata(const std::string& backup_id) const {
    std::lock_guard<std::mutex> lock(backup_mutex_);
    
    auto it = std::find_if(metadata_entries_.begin(), metadata_entries_.end(),
                          [&backup_id](const auto& meta) { return meta.backup_id == backup_id; });
    
    if (it != metadata_entries_.end()) {
        return *it;
    }
    
    return std::nullopt;
}

bool ConfigurationBackup::delete_backup(const std::string& backup_id) {
    std::lock_guard<std::mutex> lock(backup_mutex_);
    
    auto it = std::find_if(metadata_entries_.begin(), metadata_entries_.end(),
                          [&backup_id](const auto& meta) { return meta.backup_id == backup_id; });
    
    if (it == metadata_entries_.end()) {
        return false;
    }
    
    // Delete backup file
    std::error_code ec;
    std::filesystem::remove(it->backup_path, ec);
    if (ec) {
        return false;
    }
    
    // Remove from metadata
    metadata_entries_.erase(it);
    save_metadata();
    
    return true;
}

size_t ConfigurationBackup::cleanup_old_backups(const std::string& config_path) {
    std::lock_guard<std::mutex> lock(backup_mutex_);
    
    auto now = std::chrono::system_clock::now();
    size_t cleaned_count = 0;
    
    auto it = metadata_entries_.begin();
    while (it != metadata_entries_.end()) {
        bool should_delete = false;
        
        // Check if it matches the config path filter (if provided)
        if (!config_path.empty() && it->original_config_path != config_path) {
            ++it;
            continue;
        }
        
        // Check retention period (30 days default)
        auto age = now - it->timestamp;
        if (age > std::chrono::hours(24 * 30)) {
            should_delete = true;
        }
        
        if (should_delete) {
            // Delete backup file
            std::error_code ec;
            std::filesystem::remove(it->backup_path, ec);
            if (!ec) {
                it = metadata_entries_.erase(it);
                ++cleaned_count;
            } else {
                ++it;
            }
        } else {
            ++it;
        }
    }
    
    if (cleaned_count > 0) {
        save_metadata();
    }
    
    return cleaned_count;
}

bool ConfigurationBackup::verify_backup(const std::string& backup_id) const {
    auto metadata = get_backup_metadata(backup_id);
    if (!metadata) {
        return false;
    }
    
    // Check if backup file exists
    if (!std::filesystem::exists(metadata->backup_path)) {
        return false;
    }
    
    // For now, just check file existence
    // In a full implementation, we would:
    // 1. Verify checksums
    // 2. Test decompression/decryption if applicable
    // 3. Validate configuration syntax
    
    return true;
}

// Helper method implementations
std::string ConfigurationBackup::generate_backup_id() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << "backup_" << std::put_time(std::gmtime(&time_t), "%Y%m%d_%H%M%S");
    
    // Add random suffix for uniqueness
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(1000, 9999);
    oss << "_" << dis(gen);
    
    return oss.str();
}

std::string ConfigurationBackup::calculate_file_checksum(const std::filesystem::path& file_path) const {
    std::ifstream file(file_path, std::ios::binary);
    if (!file.is_open()) {
        return "";
    }
    
    // Simple SHA-256 checksum
    std::ostringstream buffer;
    buffer << file.rdbuf();
    std::string content = buffer.str();
    
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, content.c_str(), content.length());
    SHA256_Final(hash, &sha256);
    
    std::ostringstream checksum;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        checksum << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(hash[i]);
    }
    
    return checksum.str();
}

bool ConfigurationBackup::compress_file(const std::filesystem::path& source, const std::filesystem::path& dest) const {
    std::ifstream src(source, std::ios::binary);
    if (!src.is_open()) {
        return false;
    }
    
    gzFile dst = gzopen(dest.string().c_str(), "wb");
    if (!dst) {
        return false;
    }
    
    char buffer[8192];
    while (src.read(buffer, sizeof(buffer)) || src.gcount() > 0) {
        if (gzwrite(dst, buffer, src.gcount()) != src.gcount()) {
            gzclose(dst);
            return false;
        }
    }
    
    gzclose(dst);
    return true;
}

bool ConfigurationBackup::decompress_file(const std::filesystem::path& source, const std::filesystem::path& dest) const {
    gzFile src = gzopen(source.string().c_str(), "rb");
    if (!src) {
        return false;
    }
    
    std::ofstream dst(dest, std::ios::binary);
    if (!dst.is_open()) {
        gzclose(src);
        return false;
    }
    
    char buffer[8192];
    int bytes_read;
    while ((bytes_read = gzread(src, buffer, sizeof(buffer))) > 0) {
        dst.write(buffer, bytes_read);
        if (!dst.good()) {
            gzclose(src);
            return false;
        }
    }
    
    gzclose(src);
    return bytes_read == 0; // Success if we reached end of file
}

bool ConfigurationBackup::encrypt_file(const std::filesystem::path& source, const std::filesystem::path& dest) const {
    // Simplified encryption - in production use proper AES implementation
    std::ifstream src(source, std::ios::binary);
    std::ofstream dst(dest, std::ios::binary);
    
    if (!src.is_open() || !dst.is_open()) {
        return false;
    }
    
    // For demonstration, just copy the file
    // In real implementation, use OpenSSL for AES encryption
    dst << src.rdbuf();
    
    return src.good() && dst.good();
}

bool ConfigurationBackup::decrypt_file(const std::filesystem::path& source, const std::filesystem::path& dest) const {
    // Simplified decryption - in production use proper AES implementation
    std::ifstream src(source, std::ios::binary);
    std::ofstream dst(dest, std::ios::binary);
    
    if (!src.is_open() || !dst.is_open()) {
        return false;
    }
    
    // For demonstration, just copy the file
    // In real implementation, use OpenSSL for AES decryption
    dst << src.rdbuf();
    
    return src.good() && dst.good();
}

bool ConfigurationBackup::validate_configuration_file(const std::filesystem::path& config_path) const {
    // Basic validation - check if file exists and is readable
    std::ifstream file(config_path);
    if (!file.is_open()) {
        return false;
    }
    
    // In a full implementation, this would:
    // 1. Parse YAML syntax
    // 2. Validate schema
    // 3. Check required fields
    
    return true;
}

void ConfigurationBackup::load_metadata() {
    if (!std::filesystem::exists(metadata_file_)) {
        return; // No metadata file yet
    }
    
    std::ifstream file(metadata_file_);
    if (!file.is_open()) {
        return;
    }
    
    // In a full implementation, this would parse JSON metadata
    // For now, we'll keep metadata in memory
}

void ConfigurationBackup::save_metadata() const {
    std::ofstream file(metadata_file_);
    if (!file.is_open()) {
        return;
    }
    
    // In a full implementation, this would serialize metadata to JSON
    file << "# Backup metadata placeholder\n";
}

void ConfigurationBackup::update_statistics(const BackupResult& result) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    if (result.success) {
        ++statistics_.successful_backups;
        if (statistics_.avg_backup_time.count() == 0) {
            statistics_.avg_backup_time = result.backup_duration;
        } else {
            auto total_successful = statistics_.successful_backups;
            statistics_.avg_backup_time = 
                (statistics_.avg_backup_time * (total_successful - 1) + result.backup_duration) / total_successful;
        }
    } else {
        ++statistics_.failed_backups;
    }
    
    ++statistics_.total_backups;
}

void ConfigurationBackup::update_statistics(const RestoreResult& result) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    if (result.success) {
        ++statistics_.successful_restores;
    } else {
        ++statistics_.failed_restores;
    }
}

ConfigurationBackup::BackupStatistics ConfigurationBackup::get_statistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    statistics_.total_backups = metadata_entries_.size();
    
    if (!metadata_entries_.empty()) {
        auto min_max = std::minmax_element(metadata_entries_.begin(), metadata_entries_.end(),
                                          [](const auto& a, const auto& b) { return a.timestamp < b.timestamp; });
        statistics_.oldest_backup = min_max.first->timestamp;
        statistics_.newest_backup = min_max.second->timestamp;
        
        statistics_.total_size_bytes = 0;
        for (const auto& metadata : metadata_entries_) {
            std::error_code ec;
            auto size = std::filesystem::file_size(metadata.backup_path, ec);
            if (!ec) {
                statistics_.total_size_bytes += size;
            }
        }
    }
    
    return statistics_;
}

} // namespace omop::common::config