/**
 * @file configuration_audit.h
 * @brief Comprehensive configuration change audit trail system
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#pragma once

#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <unordered_map>
#include <functional>
#include <atomic>
#include <mutex>
#include <fstream>
#include <optional>
#include <variant>

namespace omop::common::config {

/**
 * @brief Types of configuration events that can be audited
 */
enum class AuditEventType {
    ConfigLoad,          // Configuration loaded from file
    ConfigReload,        // Configuration reloaded (hot reload)
    ValueChange,         // Configuration value changed
    ValueAccess,         // Configuration value accessed
    BackupCreated,       // Backup created
    BackupRestored,      // Backup restored
    ValidationFailed,    // Configuration validation failed
    ValidationPassed,    // Configuration validation passed
    CacheHit,           // Cache hit occurred
    CacheMiss,          // Cache miss occurred
    SecurityViolation,   // Security-related event
    PermissionDenied,   // Access denied
    SystemError,        // System error occurred
    Custom              // Custom event type
};

/**
 * @brief Severity levels for audit events
 */
enum class AuditSeverity {
    Debug = 0,
    Info = 1,
    Warning = 2,
    Error = 3,
    Critical = 4
};

/**
 * @brief Audit event data
 */
struct AuditEvent {
    std::string event_id;                           // Unique event ID
    AuditEventType event_type;                      // Type of event
    AuditSeverity severity;                         // Event severity
    std::chrono::system_clock::time_point timestamp; // When event occurred
    std::string config_path;                        // Configuration file path
    std::string config_key;                         // Configuration key affected
    std::string old_value;                          // Previous value (for changes)
    std::string new_value;                          // New value (for changes)
    std::string user_id;                            // User who triggered event
    std::string session_id;                         // Session identifier
    std::string client_ip;                          // Client IP address
    std::string description;                        // Human-readable description
    std::unordered_map<std::string, std::string> metadata; // Additional metadata
    std::chrono::microseconds duration{0};         // Operation duration
    bool success = true;                            // Whether operation succeeded
    std::string error_message;                      // Error message if failed
    
    // Security context
    std::string process_id;                         // Process ID
    std::string thread_id;                          // Thread ID
    std::vector<std::string> call_stack;           // Call stack information
    
    // Compliance and regulatory fields
    std::string data_classification;               // Data sensitivity level
    std::vector<std::string> regulatory_tags;     // Regulatory compliance tags
    std::string retention_policy;                  // Data retention policy
};

/**
 * @brief Audit query filter
 */
struct AuditQuery {
    std::optional<std::chrono::system_clock::time_point> start_time;
    std::optional<std::chrono::system_clock::time_point> end_time;
    std::vector<AuditEventType> event_types;
    std::vector<AuditSeverity> severities;
    std::optional<std::string> config_path;
    std::optional<std::string> config_key;
    std::optional<std::string> user_id;
    std::optional<std::string> session_id;
    std::optional<bool> success_filter;
    size_t limit = 1000;
    size_t offset = 0;
    bool ascending_order = false; // Default: newest first
};

/**
 * @brief Audit storage interface
 */
class IAuditStorage {
public:
    virtual ~IAuditStorage() = default;
    
    /**
     * @brief Store audit event
     * @param event Event to store
     * @return bool True if successfully stored
     */
    virtual bool store_event(const AuditEvent& event) = 0;
    
    /**
     * @brief Query audit events
     * @param query Query filter
     * @return std::vector<AuditEvent> Matching events
     */
    virtual std::vector<AuditEvent> query_events(const AuditQuery& query) const = 0;
    
    /**
     * @brief Get total event count
     * @return size_t Total number of events
     */
    virtual size_t get_total_events() const = 0;
    
    /**
     * @brief Purge old events based on retention policy
     * @param older_than Purge events older than this time
     * @return size_t Number of events purged
     */
    virtual size_t purge_events(std::chrono::system_clock::time_point older_than) = 0;
    
    /**
     * @brief Close storage and cleanup resources
     */
    virtual void close() = 0;
};

/**
 * @brief File-based audit storage implementation
 */
class FileAuditStorage : public IAuditStorage {
public:
    /**
     * @brief Constructor
     * @param log_file_path Path to audit log file
     * @param max_file_size_mb Maximum file size in MB before rotation
     * @param max_files Maximum number of rotated files to keep
     */
    explicit FileAuditStorage(const std::string& log_file_path, 
                             size_t max_file_size_mb = 100,
                             size_t max_files = 10);
    
    /**
     * @brief Destructor
     */
    ~FileAuditStorage() override;
    
    // IAuditStorage implementation
    bool store_event(const AuditEvent& event) override;
    std::vector<AuditEvent> query_events(const AuditQuery& query) const override;
    size_t get_total_events() const override;
    size_t purge_events(std::chrono::system_clock::time_point older_than) override;
    void close() override;

private:
    std::string log_file_path_;
    size_t max_file_size_bytes_;
    size_t max_files_;
    mutable std::mutex file_mutex_;
    std::ofstream log_file_;
    
    void rotate_log_if_needed();
    std::string serialize_event(const AuditEvent& event) const;
    std::optional<AuditEvent> deserialize_event(const std::string& line) const;
    bool matches_query(const AuditEvent& event, const AuditQuery& query) const;
};

/**
 * @brief Configuration audit manager
 */
class ConfigurationAudit {
public:
    /**
     * @brief Audit configuration
     */
    struct Config {
        bool enabled = true;
        AuditSeverity min_severity = AuditSeverity::Info;
        bool audit_value_access = false;        // Don't log every value access by default
        bool audit_cache_operations = false;   // Don't log cache hits/misses by default
        bool include_call_stack = false;       // Don't include call stack by default
        bool include_sensitive_data = false;   // Don't log sensitive values
        std::chrono::days retention_period{90}; // Keep logs for 90 days
        size_t max_events_memory = 10000;      // Keep last 10k events in memory
        std::vector<std::string> sensitive_key_patterns; // Patterns for sensitive keys
        std::vector<std::string> excluded_key_patterns;  // Keys to exclude from audit
    };

    /**
     * @brief Constructor
     * @param storage Audit storage implementation
     * @param config Audit configuration
     */
    explicit ConfigurationAudit(std::unique_ptr<IAuditStorage> storage, 
                               const Config& config = {});

    /**
     * @brief Destructor
     */
    ~ConfigurationAudit();

    /**
     * @brief Log configuration event
     * @param event_type Type of event
     * @param severity Event severity
     * @param config_path Configuration file path
     * @param description Event description
     * @param metadata Additional metadata
     */
    void log_event(AuditEventType event_type,
                  AuditSeverity severity,
                  const std::string& config_path,
                  const std::string& description,
                  const std::unordered_map<std::string, std::string>& metadata = {});

    /**
     * @brief Log configuration value change
     * @param config_path Configuration file path
     * @param config_key Configuration key
     * @param old_value Previous value
     * @param new_value New value
     * @param user_id User who made the change
     * @param session_id Session identifier
     */
    void log_value_change(const std::string& config_path,
                         const std::string& config_key,
                         const std::string& old_value,
                         const std::string& new_value,
                         const std::string& user_id = "",
                         const std::string& session_id = "");

    /**
     * @brief Log configuration value access
     * @param config_path Configuration file path
     * @param config_key Configuration key
     * @param value Accessed value
     * @param user_id User who accessed value
     * @param session_id Session identifier
     */
    void log_value_access(const std::string& config_path,
                         const std::string& config_key,
                         const std::string& value,
                         const std::string& user_id = "",
                         const std::string& session_id = "");

    /**
     * @brief Log configuration load event
     * @param config_path Configuration file path
     * @param success Whether load succeeded
     * @param duration Load duration
     * @param error_message Error message if failed
     */
    void log_config_load(const std::string& config_path,
                        bool success,
                        std::chrono::microseconds duration,
                        const std::string& error_message = "");

    /**
     * @brief Log security violation
     * @param violation_type Type of violation
     * @param config_path Configuration file path
     * @param config_key Configuration key
     * @param description Violation description
     * @param user_id User involved
     * @param client_ip Client IP address
     */
    void log_security_violation(const std::string& violation_type,
                               const std::string& config_path,
                               const std::string& config_key,
                               const std::string& description,
                               const std::string& user_id,
                               const std::string& client_ip);

    /**
     * @brief Query audit events
     * @param query Query filter
     * @return std::vector<AuditEvent> Matching events
     */
    std::vector<AuditEvent> query_events(const AuditQuery& query) const;

    /**
     * @brief Get audit statistics
     */
    struct Statistics {
        size_t total_events = 0;
        size_t events_by_type[static_cast<int>(AuditEventType::Custom) + 1] = {0};
        size_t events_by_severity[static_cast<int>(AuditSeverity::Critical) + 1] = {0};
        std::chrono::system_clock::time_point oldest_event;
        std::chrono::system_clock::time_point newest_event;
        size_t successful_operations = 0;
        size_t failed_operations = 0;
        std::unordered_map<std::string, size_t> top_users;
        std::unordered_map<std::string, size_t> top_config_paths;
    };

    Statistics get_statistics() const;

    /**
     * @brief Generate compliance report
     * @param start_time Report start time
     * @param end_time Report end time
     * @param format Report format ("json", "csv", "html")
     * @return std::string Generated report
     */
    std::string generate_compliance_report(std::chrono::system_clock::time_point start_time,
                                          std::chrono::system_clock::time_point end_time,
                                          const std::string& format = "json") const;

    /**
     * @brief Export audit events
     * @param query Query filter
     * @param format Export format ("json", "csv")
     * @param output_path Output file path
     * @return bool True if successfully exported
     */
    bool export_events(const AuditQuery& query,
                      const std::string& format,
                      const std::string& output_path) const;

    /**
     * @brief Set audit alert callback
     * @param callback Callback for audit alerts
     */
    void set_alert_callback(std::function<void(const AuditEvent&)> callback);

    /**
     * @brief Enable/disable audit logging
     * @param enabled Enable audit logging
     */
    void set_enabled(bool enabled);

    /**
     * @brief Update audit configuration
     * @param config New configuration
     */
    void update_config(const Config& config);

    /**
     * @brief Purge old audit events
     * @return size_t Number of events purged
     */
    size_t purge_old_events();

    /**
     * @brief Get current audit configuration
     * @return Config Current configuration
     */
    Config get_config() const;

private:
    std::unique_ptr<IAuditStorage> storage_;
    Config config_;
    mutable std::mutex config_mutex_;
    
    std::atomic<bool> enabled_{true};
    std::atomic<size_t> event_counter_{0};
    
    // In-memory event cache for fast access
    mutable std::vector<AuditEvent> memory_cache_;
    mutable std::mutex cache_mutex_;
    
    // Alert callback
    std::function<void(const AuditEvent&)> alert_callback_;
    
    // Helper methods
    std::string generate_event_id() const;
    bool should_audit_key(const std::string& config_key) const;
    bool is_sensitive_key(const std::string& config_key) const;
    std::string sanitize_value(const std::string& value, const std::string& config_key) const;
    void add_to_memory_cache(const AuditEvent& event) const;
    void check_alert_conditions(const AuditEvent& event);
    std::vector<std::string> get_call_stack() const;
    
    // Context information
    std::string get_current_user_id() const;
    std::string get_current_session_id() const;
    std::string get_current_process_id() const;
    std::string get_current_thread_id() const;
};

/**
 * @brief RAII audit scope for automatic event logging
 */
class AuditScope {
public:
    /**
     * @brief Constructor
     * @param audit Audit manager
     * @param event_type Event type
     * @param config_path Configuration path
     * @param description Event description
     */
    AuditScope(ConfigurationAudit& audit,
              AuditEventType event_type,
              const std::string& config_path,
              const std::string& description);

    /**
     * @brief Destructor - logs completion event
     */
    ~AuditScope();

    /**
     * @brief Mark operation as failed
     * @param error_message Error message
     */
    void set_failed(const std::string& error_message);

    /**
     * @brief Add metadata to the audit event
     * @param key Metadata key
     * @param value Metadata value
     */
    void add_metadata(const std::string& key, const std::string& value);

private:
    ConfigurationAudit& audit_;
    AuditEventType event_type_;
    std::string config_path_;
    std::string description_;
    std::chrono::high_resolution_clock::time_point start_time_;
    std::unordered_map<std::string, std::string> metadata_;
    bool success_ = true;
    std::string error_message_;
};

/**
 * @brief Macro for automatic audit scope creation
 */
#define OMOP_CONFIG_AUDIT_SCOPE(audit, type, path, desc) \
    AuditScope _audit_scope(audit, type, path, desc)

/**
 * @brief Global configuration audit instance
 * @return ConfigurationAudit& Global audit instance
 */
ConfigurationAudit& global_audit();

} // namespace omop::common::config