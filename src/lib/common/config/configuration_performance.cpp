/**
 * @file configuration_performance.cpp
 * @brief Implementation of configuration performance monitoring
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#include "configuration_performance.h"
#include <algorithm>
#include <numeric>
#include <sstream>
#include <iomanip>
#include <fstream>
#include <filesystem>
#include <cmath>

namespace omop::common::config {

// ConfigurationPerformance::Timer implementation
ConfigurationPerformance::Timer::Timer(ConfigurationPerformance* perf, const std::string& operation_name)
    : performance_(perf), operation_name_(operation_name), 
      start_time_(std::chrono::high_resolution_clock::now()) {
}

ConfigurationPerformance::Timer::~Timer() {
    if (!stopped_) {
        stop();
    }
}

void ConfigurationPerformance::Timer::stop() {
    if (!stopped_) {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time_);
        performance_->record_timing(operation_name_, duration);
        stopped_ = true;
    }
}

// ConfigurationPerformance implementation
ConfigurationPerformance::ConfigurationPerformance(const SLAThresholds& sla_thresholds)
    : sla_thresholds_(sla_thresholds) {
    current_metrics_.last_measurement = std::chrono::system_clock::now();
}

ConfigurationPerformance::~ConfigurationPerformance() = default;

std::unique_ptr<ConfigurationPerformance::Timer> ConfigurationPerformance::start_timer(const std::string& operation_name) {
    return std::make_unique<Timer>(this, operation_name);
}

void ConfigurationPerformance::record_timing(const std::string& operation_name, std::chrono::microseconds duration) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    update_operation_stats(operation_name, duration);
    
    // Update specific metrics based on operation name
    if (operation_name == "config_load") {
        current_metrics_.load_time = duration;
    } else if (operation_name == "config_reload") {
        current_metrics_.reload_time = duration;
        ++current_metrics_.reload_count;
    } else if (operation_name == "config_validation") {
        current_metrics_.validation_time = duration;
        ++current_metrics_.validation_count;
    } else if (operation_name == "cache_lookup") {
        current_metrics_.cache_lookup_time = duration;
    } else if (operation_name == "config_backup") {
        current_metrics_.backup_time = duration;
    } else if (operation_name == "config_restore") {
        current_metrics_.restore_time = duration;
    }
    
    current_metrics_.last_measurement = std::chrono::system_clock::now();
    
    // Check for SLA violations
    check_sla_violation(operation_name, duration);
}

void ConfigurationPerformance::record_load(const std::string& config_path, 
                                         std::chrono::microseconds load_time,
                                         size_t config_size_bytes) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    current_metrics_.config_file_path = config_path;
    current_metrics_.config_size_bytes = config_size_bytes;
    current_metrics_.load_time = load_time;
    current_metrics_.last_measurement = std::chrono::system_clock::now();
    
    update_operation_stats("config_load", load_time);
    check_sla_violation("config_load", load_time);
}

void ConfigurationPerformance::record_reload(std::chrono::microseconds reload_time) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    current_metrics_.reload_time = reload_time;
    ++current_metrics_.reload_count;
    current_metrics_.last_measurement = std::chrono::system_clock::now();
    
    update_operation_stats("config_reload", reload_time);
    check_sla_violation("config_reload", reload_time);
}

void ConfigurationPerformance::record_cache_access(bool hit, std::chrono::microseconds lookup_time) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    if (hit) {
        ++current_metrics_.cache_hit_count;
    } else {
        ++current_metrics_.cache_miss_count;
    }
    
    current_metrics_.cache_lookup_time = lookup_time;
    current_metrics_.last_measurement = std::chrono::system_clock::now();
    
    update_operation_stats("cache_lookup", lookup_time);
    check_sla_violation("cache_lookup", lookup_time);
}

PerformanceMetrics ConfigurationPerformance::get_metrics() const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    return current_metrics_;
}

BenchmarkResults ConfigurationPerformance::run_benchmark(const std::string& config_path, size_t iterations) {
    BenchmarkResults results;
    results.benchmark_time = std::chrono::system_clock::now();
    results.benchmark_version = "1.0";
    results.total_samples = 0;
    
    // Benchmark configuration loading
    {
        std::vector<std::chrono::microseconds> load_times;
        load_times.reserve(iterations);
        
        for (size_t i = 0; i < iterations; ++i) {
            auto start = std::chrono::high_resolution_clock::now();
            
            // Simulate config loading - in real implementation this would load actual config
            std::ifstream file(config_path);
            if (file.is_open()) {
                std::string content((std::istreambuf_iterator<char>(file)),
                                   std::istreambuf_iterator<char>());
                file.close();
            }
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            load_times.push_back(duration);
        }
        
        BenchmarkResults::Operation load_op;
        load_op.name = "config_load";
        load_op.sample_count = load_times.size();
        load_op.min_time = *std::min_element(load_times.begin(), load_times.end());
        load_op.max_time = *std::max_element(load_times.begin(), load_times.end());
        load_op.avg_time = std::accumulate(load_times.begin(), load_times.end(), std::chrono::microseconds{0}) / load_times.size();
        load_op.p50_time = calculate_percentile(load_times, 50.0);
        load_op.p90_time = calculate_percentile(load_times, 90.0);
        load_op.p95_time = calculate_percentile(load_times, 95.0);
        load_op.p99_time = calculate_percentile(load_times, 99.0);
        load_op.sla_threshold = sla_thresholds_.config_load;
        load_op.meets_sla = load_op.p95_time <= sla_thresholds_.config_load;
        
        results.operations.push_back(load_op);
        results.total_samples += load_times.size();
    }
    
    // Benchmark cache lookup performance
    {
        std::vector<std::chrono::microseconds> lookup_times;
        lookup_times.reserve(iterations);
        
        // Simulate cache operations
        std::unordered_map<std::string, std::string> test_cache;
        test_cache["test.key.1"] = "test_value_1";
        test_cache["test.key.2"] = "test_value_2";
        test_cache["database.host"] = "localhost";
        test_cache["database.port"] = "5432";
        
        for (size_t i = 0; i < iterations; ++i) {
            auto start = std::chrono::high_resolution_clock::now();
            
            // Simulate cache lookup
            std::string key = "test.key." + std::to_string((i % 2) + 1);
            auto it = test_cache.find(key);
            volatile bool found = (it != test_cache.end());
            (void)found; // Suppress unused variable warning
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            lookup_times.push_back(duration);
        }
        
        BenchmarkResults::Operation lookup_op;
        lookup_op.name = "cache_lookup";
        lookup_op.sample_count = lookup_times.size();
        lookup_op.min_time = *std::min_element(lookup_times.begin(), lookup_times.end());
        lookup_op.max_time = *std::max_element(lookup_times.begin(), lookup_times.end());
        lookup_op.avg_time = std::accumulate(lookup_times.begin(), lookup_times.end(), std::chrono::microseconds{0}) / lookup_times.size();
        lookup_op.p50_time = calculate_percentile(lookup_times, 50.0);
        lookup_op.p90_time = calculate_percentile(lookup_times, 90.0);
        lookup_op.p95_time = calculate_percentile(lookup_times, 95.0);
        lookup_op.p99_time = calculate_percentile(lookup_times, 99.0);
        lookup_op.sla_threshold = sla_thresholds_.cache_lookup;
        lookup_op.meets_sla = lookup_op.p95_time <= sla_thresholds_.cache_lookup;
        
        results.operations.push_back(lookup_op);
        results.total_samples += lookup_times.size();
    }
    
    // Benchmark configuration validation
    {
        std::vector<std::chrono::microseconds> validation_times;
        validation_times.reserve(iterations);
        
        for (size_t i = 0; i < iterations; ++i) {
            auto start = std::chrono::high_resolution_clock::now();
            
            // Simulate config validation - basic checks
            bool valid = true;
            std::vector<std::string> required_keys = {"database.host", "database.port", "database.name"};
            std::unordered_map<std::string, std::string> test_config = {
                {"database.host", "localhost"},
                {"database.port", "5432"},
                {"database.name", "test_db"}
            };
            
            for (const auto& key : required_keys) {
                if (test_config.find(key) == test_config.end()) {
                    valid = false;
                    break;
                }
            }
            (void)valid; // Suppress unused variable warning
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            validation_times.push_back(duration);
        }
        
        BenchmarkResults::Operation validation_op;
        validation_op.name = "config_validation";
        validation_op.sample_count = validation_times.size();
        validation_op.min_time = *std::min_element(validation_times.begin(), validation_times.end());
        validation_op.max_time = *std::max_element(validation_times.begin(), validation_times.end());
        validation_op.avg_time = std::accumulate(validation_times.begin(), validation_times.end(), std::chrono::microseconds{0}) / validation_times.size();
        validation_op.p50_time = calculate_percentile(validation_times, 50.0);
        validation_op.p90_time = calculate_percentile(validation_times, 90.0);
        validation_op.p95_time = calculate_percentile(validation_times, 95.0);
        validation_op.p99_time = calculate_percentile(validation_times, 99.0);
        validation_op.sla_threshold = sla_thresholds_.validation;
        validation_op.meets_sla = validation_op.p95_time <= sla_thresholds_.validation;
        
        results.operations.push_back(validation_op);
        results.total_samples += validation_times.size();
    }
    
    // Calculate overall SLA compliance
    results.overall_sla_compliance = std::all_of(results.operations.begin(), results.operations.end(),
                                                [](const auto& op) { return op.meets_sla; });
    
    return results;
}

bool ConfigurationPerformance::meets_sla() const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    return (current_metrics_.load_time <= sla_thresholds_.config_load) &&
           (current_metrics_.reload_time <= sla_thresholds_.config_hot_reload) &&
           (current_metrics_.cache_lookup_time <= sla_thresholds_.cache_lookup) &&
           (current_metrics_.validation_time <= sla_thresholds_.validation) &&
           (current_metrics_.backup_time <= sla_thresholds_.backup) &&
           (current_metrics_.restore_time <= sla_thresholds_.restore);
}

std::vector<std::string> ConfigurationPerformance::get_sla_violations() const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    std::vector<std::string> violations;
    
    if (current_metrics_.load_time > sla_thresholds_.config_load) {
        violations.push_back("Config load time (" + std::to_string(current_metrics_.load_time.count()) + 
                           "μs) exceeds SLA (" + std::to_string(sla_thresholds_.config_load.count()) + "ms)");
    }
    
    if (current_metrics_.reload_time > sla_thresholds_.config_hot_reload) {
        violations.push_back("Config reload time (" + std::to_string(current_metrics_.reload_time.count()) + 
                           "μs) exceeds SLA (" + std::to_string(sla_thresholds_.config_hot_reload.count()) + "ms)");
    }
    
    if (current_metrics_.cache_lookup_time > sla_thresholds_.cache_lookup) {
        violations.push_back("Cache lookup time (" + std::to_string(current_metrics_.cache_lookup_time.count()) + 
                           "μs) exceeds SLA (" + std::to_string(sla_thresholds_.cache_lookup.count()) + "ms)");
    }
    
    if (current_metrics_.validation_time > sla_thresholds_.validation) {
        violations.push_back("Validation time (" + std::to_string(current_metrics_.validation_time.count()) + 
                           "μs) exceeds SLA (" + std::to_string(sla_thresholds_.validation.count()) + "ms)");
    }
    
    if (current_metrics_.backup_time > sla_thresholds_.backup) {
        violations.push_back("Backup time (" + std::to_string(current_metrics_.backup_time.count()) + 
                           "μs) exceeds SLA (" + std::to_string(sla_thresholds_.backup.count()) + "ms)");
    }
    
    if (current_metrics_.restore_time > sla_thresholds_.restore) {
        violations.push_back("Restore time (" + std::to_string(current_metrics_.restore_time.count()) + 
                           "μs) exceeds SLA (" + std::to_string(sla_thresholds_.restore.count()) + "ms)");
    }
    
    return violations;
}

void ConfigurationPerformance::reset_statistics() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    current_metrics_ = PerformanceMetrics{};
    operation_stats_.clear();
    current_metrics_.last_measurement = std::chrono::system_clock::now();
}

void ConfigurationPerformance::enable_detailed_timing(bool enabled) {
    detailed_timing_enabled_.store(enabled);
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    for (auto& [name, stats] : operation_stats_) {
        stats.detailed_timing_enabled = enabled;
    }
}

void ConfigurationPerformance::set_alert_callback(std::function<void(const std::string&, std::chrono::microseconds)> callback) {
    alert_callback_ = std::move(callback);
}

std::string ConfigurationPerformance::export_json() const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    std::ostringstream json;
    json << "{\n";
    json << "  \"timestamp\": \"" << std::chrono::duration_cast<std::chrono::seconds>(
        current_metrics_.last_measurement.time_since_epoch()).count() << "\",\n";
    json << "  \"config_file\": \"" << current_metrics_.config_file_path << "\",\n";
    json << "  \"metrics\": {\n";
    json << "    \"load_time_us\": " << current_metrics_.load_time.count() << ",\n";
    json << "    \"reload_time_us\": " << current_metrics_.reload_time.count() << ",\n";
    json << "    \"validation_time_us\": " << current_metrics_.validation_time.count() << ",\n";
    json << "    \"cache_lookup_time_us\": " << current_metrics_.cache_lookup_time.count() << ",\n";
    json << "    \"backup_time_us\": " << current_metrics_.backup_time.count() << ",\n";
    json << "    \"restore_time_us\": " << current_metrics_.restore_time.count() << ",\n";
    json << "    \"config_size_bytes\": " << current_metrics_.config_size_bytes << ",\n";
    json << "    \"cache_hit_count\": " << current_metrics_.cache_hit_count << ",\n";
    json << "    \"cache_miss_count\": " << current_metrics_.cache_miss_count << ",\n";
    json << "    \"reload_count\": " << current_metrics_.reload_count << ",\n";
    json << "    \"validation_count\": " << current_metrics_.validation_count << "\n";
    json << "  },\n";
    json << "  \"sla_compliance\": " << (meets_sla() ? "true" : "false") << "\n";
    json << "}";
    
    return json.str();
}

std::string ConfigurationPerformance::export_benchmark_json(const BenchmarkResults& results) const {
    std::ostringstream json;
    json << "{\n";
    json << "  \"benchmark_time\": \"" << std::chrono::duration_cast<std::chrono::seconds>(
        results.benchmark_time.time_since_epoch()).count() << "\",\n";
    json << "  \"version\": \"" << results.benchmark_version << "\",\n";
    json << "  \"total_samples\": " << results.total_samples << ",\n";
    json << "  \"overall_sla_compliance\": " << (results.overall_sla_compliance ? "true" : "false") << ",\n";
    json << "  \"operations\": [\n";
    
    for (size_t i = 0; i < results.operations.size(); ++i) {
        const auto& op = results.operations[i];
        json << "    {\n";
        json << "      \"name\": \"" << op.name << "\",\n";
        json << "      \"sample_count\": " << op.sample_count << ",\n";
        json << "      \"min_time_us\": " << op.min_time.count() << ",\n";
        json << "      \"max_time_us\": " << op.max_time.count() << ",\n";
        json << "      \"avg_time_us\": " << op.avg_time.count() << ",\n";
        json << "      \"p50_time_us\": " << op.p50_time.count() << ",\n";
        json << "      \"p90_time_us\": " << op.p90_time.count() << ",\n";
        json << "      \"p95_time_us\": " << op.p95_time.count() << ",\n";
        json << "      \"p99_time_us\": " << op.p99_time.count() << ",\n";
        json << "      \"sla_threshold_ms\": " << std::chrono::duration_cast<std::chrono::milliseconds>(op.sla_threshold).count() << ",\n";
        json << "      \"meets_sla\": " << (op.meets_sla ? "true" : "false") << "\n";
        json << "    }";
        if (i < results.operations.size() - 1) json << ",";
        json << "\n";
    }
    
    json << "  ]\n";
    json << "}";
    
    return json.str();
}

void ConfigurationPerformance::check_sla_violation(const std::string& operation_name, std::chrono::microseconds duration) {
    std::chrono::milliseconds threshold{0};
    
    if (operation_name == "config_load") {
        threshold = sla_thresholds_.config_load;
    } else if (operation_name == "config_reload") {
        threshold = sla_thresholds_.config_hot_reload;
    } else if (operation_name == "cache_lookup") {
        threshold = sla_thresholds_.cache_lookup;
    } else if (operation_name == "config_validation") {
        threshold = sla_thresholds_.validation;
    } else if (operation_name == "config_backup") {
        threshold = sla_thresholds_.backup;
    } else if (operation_name == "config_restore") {
        threshold = sla_thresholds_.restore;
    }
    
    if (threshold.count() > 0 && duration > threshold && alert_callback_) {
        alert_callback_(operation_name, duration);
    }
}

std::chrono::microseconds ConfigurationPerformance::calculate_percentile(
    const std::vector<std::chrono::microseconds>& timings, double percentile) const {
    
    if (timings.empty()) {
        return std::chrono::microseconds{0};
    }
    
    std::vector<std::chrono::microseconds> sorted_timings = timings;
    std::sort(sorted_timings.begin(), sorted_timings.end());
    
    size_t index = static_cast<size_t>(std::ceil(percentile / 100.0 * sorted_timings.size())) - 1;
    index = std::min(index, sorted_timings.size() - 1);
    
    return sorted_timings[index];
}

void ConfigurationPerformance::update_operation_stats(const std::string& operation_name, std::chrono::microseconds duration) {
    auto& stats = operation_stats_[operation_name];
    stats.detailed_timing_enabled = detailed_timing_enabled_.load();
    stats.add_timing(duration);
}

// Global performance monitor
ConfigurationPerformance& global_performance_monitor() {
    static ConfigurationPerformance instance;
    return instance;
}

} // namespace omop::common::config