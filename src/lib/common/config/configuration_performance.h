/**
 * @file configuration_performance.h
 * @brief Configuration performance monitoring and benchmarking
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#pragma once

#include <chrono>
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <atomic>
#include <mutex>
#include <functional>
#include <optional>

namespace omop::common::config {

/**
 * @brief Performance metrics for configuration operations
 */
struct PerformanceMetrics {
    std::chrono::microseconds load_time{0};
    std::chrono::microseconds reload_time{0};
    std::chrono::microseconds validation_time{0};
    std::chrono::microseconds cache_lookup_time{0};
    std::chrono::microseconds backup_time{0};
    std::chrono::microseconds restore_time{0};
    
    size_t config_size_bytes = 0;
    size_t cache_hit_count = 0;
    size_t cache_miss_count = 0;
    size_t reload_count = 0;
    size_t validation_count = 0;
    
    std::chrono::system_clock::time_point last_measurement;
    std::string config_file_path;
};

/**
 * @brief Performance benchmarking results
 */
struct BenchmarkResults {
    struct Operation {
        std::string name;
        std::chrono::microseconds min_time{0};
        std::chrono::microseconds max_time{0};
        std::chrono::microseconds avg_time{0};
        std::chrono::microseconds p50_time{0};
        std::chrono::microseconds p90_time{0};
        std::chrono::microseconds p95_time{0};
        std::chrono::microseconds p99_time{0};
        size_t sample_count = 0;
        bool meets_sla = false;
        std::chrono::microseconds sla_threshold{0};
    };
    
    std::vector<Operation> operations;
    std::chrono::system_clock::time_point benchmark_time;
    std::string benchmark_version;
    size_t total_samples = 0;
    bool overall_sla_compliance = false;
};

/**
 * @brief Configuration performance monitor and benchmarking system
 */
class ConfigurationPerformance {
public:
    /**
     * @brief Performance SLA thresholds (as specified in requirements)
     */
    struct SLAThresholds {
        std::chrono::milliseconds config_load{100};        // <100ms load
        std::chrono::milliseconds config_hot_reload{50};   // <50ms hot-reload
        std::chrono::milliseconds cache_lookup{1};         // <1ms cache lookup
        std::chrono::milliseconds validation{10};          // <10ms validation
        std::chrono::milliseconds backup{500};             // <500ms backup
        std::chrono::milliseconds restore{1000};           // <1000ms restore
    };

    /**
     * @brief Constructor
     * @param sla_thresholds SLA thresholds for performance monitoring
     */
    explicit ConfigurationPerformance(const SLAThresholds& sla_thresholds = SLAThresholds());

    /**
     * @brief Destructor
     */
    ~ConfigurationPerformance();

    /**
     * @brief Start timing an operation
     * @param operation_name Name of operation being timed
     * @return Timer handle for stopping the measurement
     */
    class Timer {
    public:
        Timer(ConfigurationPerformance* perf, const std::string& operation_name);
        ~Timer();
        void stop();
    private:
        ConfigurationPerformance* performance_;
        std::string operation_name_;
        std::chrono::high_resolution_clock::time_point start_time_;
        bool stopped_{false};
    };
    
    std::unique_ptr<Timer> start_timer(const std::string& operation_name);

    /**
     * @brief Record operation timing manually
     * @param operation_name Operation name
     * @param duration Operation duration
     */
    void record_timing(const std::string& operation_name, std::chrono::microseconds duration);

    /**
     * @brief Record configuration load event
     * @param config_path Configuration file path
     * @param load_time Time taken to load
     * @param config_size_bytes Size of configuration in bytes
     */
    void record_load(const std::string& config_path, 
                    std::chrono::microseconds load_time,
                    size_t config_size_bytes);

    /**
     * @brief Record configuration reload event
     * @param reload_time Time taken to reload
     */
    void record_reload(std::chrono::microseconds reload_time);

    /**
     * @brief Record cache hit/miss
     * @param hit True if cache hit, false if miss
     * @param lookup_time Time taken for lookup
     */
    void record_cache_access(bool hit, std::chrono::microseconds lookup_time);

    /**
     * @brief Get current performance metrics
     * @return PerformanceMetrics Current metrics
     */
    PerformanceMetrics get_metrics() const;

    /**
     * @brief Run comprehensive performance benchmark
     * @param config_path Configuration file path for benchmarking
     * @param iterations Number of iterations per operation
     * @return BenchmarkResults Benchmark results
     */
    BenchmarkResults run_benchmark(const std::string& config_path, size_t iterations = 100);

    /**
     * @brief Check if current performance meets SLA requirements
     * @return true if all SLA requirements are met
     */
    bool meets_sla() const;

    /**
     * @brief Get SLA violations
     * @return Vector of SLA violation descriptions
     */
    std::vector<std::string> get_sla_violations() const;

    /**
     * @brief Reset performance statistics
     */
    void reset_statistics();

    /**
     * @brief Enable/disable detailed timing collection
     * @param enabled Enable detailed timing
     */
    void enable_detailed_timing(bool enabled);

    /**
     * @brief Set performance alert callback
     * @param callback Callback for performance alerts
     */
    void set_alert_callback(std::function<void(const std::string&, std::chrono::microseconds)> callback);

    /**
     * @brief Export performance data as JSON
     * @return std::string Performance data in JSON format
     */
    std::string export_json() const;

    /**
     * @brief Export benchmark results as JSON
     * @param results Benchmark results to export
     * @return std::string Benchmark results in JSON format
     */
    std::string export_benchmark_json(const BenchmarkResults& results) const;

private:
    struct OperationStats {
        std::vector<std::chrono::microseconds> timings;
        std::chrono::microseconds total_time{0};
        size_t count = 0;
        std::chrono::microseconds min_time{std::chrono::microseconds::max()};
        std::chrono::microseconds max_time{0};
        
        void add_timing(std::chrono::microseconds timing) {
            if (detailed_timing_enabled) {
                timings.push_back(timing);
            }
            total_time += timing;
            ++count;
            min_time = std::min(min_time, timing);
            max_time = std::max(max_time, timing);
        }
        
        std::chrono::microseconds avg_time() const {
            return count > 0 ? std::chrono::microseconds(total_time / count) : std::chrono::microseconds{0};
        }
        
        bool detailed_timing_enabled = false;
    };

    SLAThresholds sla_thresholds_;
    mutable std::mutex metrics_mutex_;
    PerformanceMetrics current_metrics_;
    
    std::unordered_map<std::string, OperationStats> operation_stats_;
    std::atomic<bool> detailed_timing_enabled_{false};
    
    std::function<void(const std::string&, std::chrono::microseconds)> alert_callback_;
    
    void check_sla_violation(const std::string& operation_name, std::chrono::microseconds duration);
    std::chrono::microseconds calculate_percentile(const std::vector<std::chrono::microseconds>& timings, double percentile) const;
    void update_operation_stats(const std::string& operation_name, std::chrono::microseconds duration);
};

/**
 * @brief RAII helper for automatic performance timing
 */
#define OMOP_CONFIG_TIMER(perf, operation) \
    auto _timer = (perf)->start_timer(operation)

/**
 * @brief Global performance monitor instance
 * @return ConfigurationPerformance& Global performance monitor
 */
ConfigurationPerformance& global_performance_monitor();

} // namespace omop::common::config