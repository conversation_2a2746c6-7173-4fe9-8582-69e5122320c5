/**
 * @file configuration_backup.h
 * @brief Configuration backup and restore mechanisms
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#pragma once

#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <filesystem>
#include <functional>
#include <atomic>
#include <mutex>
#include <optional>
#include <thread>

namespace omop::common::config {

/**
 * @brief Backup metadata information
 */
struct BackupMetadata {
    std::string backup_id;
    std::string original_config_path;
    std::filesystem::path backup_path;
    std::chrono::system_clock::time_point timestamp;
    size_t file_size_bytes = 0;
    std::string checksum;
    std::string description;
    std::string created_by;
    std::string config_version;
    bool is_valid = true;
    
    // Compression and encryption info
    bool is_compressed = false;
    bool is_encrypted = false;
    std::string compression_algorithm;
    std::string encryption_algorithm;
};

/**
 * @brief Backup restoration result
 */
struct RestoreResult {
    bool success = false;
    std::string error_message;
    std::string restored_config_path;
    std::chrono::system_clock::time_point restore_time;
    bool validation_passed = false;
    std::vector<std::string> validation_warnings;
};

/**
 * @brief Backup creation result
 */
struct BackupResult {
    bool success = false;
    std::string error_message;
    BackupMetadata metadata;
    std::chrono::microseconds backup_duration{0};
};

/**
 * @brief Configuration backup and restore manager
 */
class ConfigurationBackup {
public:
    /**
     * @brief Backup configuration options
     */
    struct BackupOptions {
        bool compress = true;
        bool encrypt = false;
        std::string description;
        std::string encryption_key_path;
        size_t max_backups = 10;
        std::chrono::hours retention_period{24 * 30}; // 30 days
        bool include_metadata = true;
        bool validate_before_backup = true;
    };

    /**
     * @brief Restore options
     */
    struct RestoreOptions {
        bool validate_after_restore = true;
        bool create_backup_before_restore = true;
        bool force_restore = false; // Restore even if validation fails
        std::string target_config_path; // If empty, restore to original location
    };

    /**
     * @brief Constructor
     * @param backup_directory Directory to store backups
     */
    explicit ConfigurationBackup(const std::string& backup_directory);

    /**
     * @brief Destructor
     */
    ~ConfigurationBackup();

    /**
     * @brief Create backup of configuration file
     * @param config_path Path to configuration file
     * @param options Backup options
     * @return BackupResult Result of backup operation
     */
    BackupResult create_backup(const std::string& config_path, const BackupOptions& options = BackupOptions());

    /**
     * @brief Create automatic backup with timestamp
     * @param config_path Path to configuration file
     * @param description Optional description
     * @return BackupResult Result of backup operation
     */
    BackupResult create_automatic_backup(const std::string& config_path, const std::string& description = "");

    /**
     * @brief Restore configuration from backup
     * @param backup_id Backup ID to restore
     * @param options Restore options
     * @return RestoreResult Result of restore operation
     */
    RestoreResult restore_backup(const std::string& backup_id, const RestoreOptions& options = RestoreOptions());

    /**
     * @brief Restore from most recent backup
     * @param original_config_path Original configuration file path
     * @param options Restore options
     * @return RestoreResult Result of restore operation
     */
    RestoreResult restore_latest_backup(const std::string& original_config_path, const RestoreOptions& options = {});

    /**
     * @brief List all available backups
     * @param config_path Optional filter by original config path
     * @return std::vector<BackupMetadata> List of backup metadata
     */
    std::vector<BackupMetadata> list_backups(const std::string& config_path = "") const;

    /**
     * @brief Get backup metadata by ID
     * @param backup_id Backup ID
     * @return std::optional<BackupMetadata> Backup metadata if found
     */
    std::optional<BackupMetadata> get_backup_metadata(const std::string& backup_id) const;

    /**
     * @brief Delete backup by ID
     * @param backup_id Backup ID to delete
     * @return bool True if successfully deleted
     */
    bool delete_backup(const std::string& backup_id);

    /**
     * @brief Cleanup old backups based on retention policy
     * @param config_path Optional filter by original config path
     * @return size_t Number of backups cleaned up
     */
    size_t cleanup_old_backups(const std::string& config_path = "");

    /**
     * @brief Verify backup integrity
     * @param backup_id Backup ID to verify
     * @return bool True if backup is valid and intact
     */
    bool verify_backup(const std::string& backup_id) const;

    /**
     * @brief Export backup to external location
     * @param backup_id Backup ID
     * @param export_path Path to export to
     * @param include_metadata Include metadata file
     * @return bool True if successfully exported
     */
    bool export_backup(const std::string& backup_id, const std::string& export_path, bool include_metadata = true) const;

    /**
     * @brief Import backup from external location
     * @param import_path Path to backup file or directory
     * @param description Optional description for imported backup
     * @return std::optional<std::string> Backup ID if successfully imported
     */
    std::optional<std::string> import_backup(const std::string& import_path, const std::string& description = "");

    /**
     * @brief Set encryption key for backup encryption/decryption
     * @param key_path Path to encryption key file
     */
    void set_encryption_key(const std::string& key_path);

    /**
     * @brief Enable/disable automatic backups before configuration changes
     * @param enabled Enable automatic backups
     * @param max_auto_backups Maximum number of automatic backups to keep
     */
    void enable_automatic_backups(bool enabled, size_t max_auto_backups = 5);

    /**
     * @brief Set backup completion callback
     * @param callback Callback function called after backup completion
     */
    void set_backup_callback(std::function<void(const BackupResult&)> callback);

    /**
     * @brief Set restore completion callback
     * @param callback Callback function called after restore completion
     */
    void set_restore_callback(std::function<void(const RestoreResult&)> callback);

    /**
     * @brief Get backup directory path
     * @return std::string Backup directory path
     */
    std::string get_backup_directory() const { return backup_directory_.string(); }

    /**
     * @brief Get backup statistics
     */
    struct BackupStatistics {
        size_t total_backups = 0;
        size_t total_size_bytes = 0;
        size_t successful_backups = 0;
        size_t failed_backups = 0;
        size_t successful_restores = 0;
        size_t failed_restores = 0;
        std::chrono::system_clock::time_point oldest_backup;
        std::chrono::system_clock::time_point newest_backup;
        std::chrono::microseconds avg_backup_time{0};
        std::chrono::microseconds avg_restore_time{0};
    };

    BackupStatistics get_statistics() const;

private:
    std::filesystem::path backup_directory_;
    std::filesystem::path metadata_file_;
    mutable std::mutex backup_mutex_;
    
    std::string encryption_key_path_;
    std::atomic<bool> automatic_backups_enabled_{false};
    std::atomic<size_t> max_auto_backups_{5};
    
    // Statistics
    mutable BackupStatistics statistics_;
    mutable std::mutex stats_mutex_;
    
    // Callbacks
    std::function<void(const BackupResult&)> backup_callback_;
    std::function<void(const RestoreResult&)> restore_callback_;
    
    // Helper methods
    std::string generate_backup_id() const;
    std::string calculate_file_checksum(const std::filesystem::path& file_path) const;
    bool compress_file(const std::filesystem::path& source, const std::filesystem::path& dest) const;
    bool decompress_file(const std::filesystem::path& source, const std::filesystem::path& dest) const;
    bool encrypt_file(const std::filesystem::path& source, const std::filesystem::path& dest) const;
    bool decrypt_file(const std::filesystem::path& source, const std::filesystem::path& dest) const;
    bool validate_configuration_file(const std::filesystem::path& config_path) const;
    
    void load_metadata();
    void save_metadata() const;
    std::vector<BackupMetadata> metadata_entries_;
    
    void update_statistics(const BackupResult& result);
    void update_statistics(const RestoreResult& result);
    
    std::filesystem::path get_backup_file_path(const std::string& backup_id) const;
    std::filesystem::path get_metadata_file_path(const std::string& backup_id) const;
};

/**
 * @brief Automatic backup scheduler
 * 
 * Provides scheduled automatic backups based on configuration file changes
 * or time-based intervals.
 */
class AutomaticBackupScheduler {
public:
    /**
     * @brief Schedule configuration
     */
    struct ScheduleConfig {
        bool enabled = false;
        std::chrono::minutes interval{60}; // Backup every hour
        size_t max_scheduled_backups = 24; // Keep 24 hourly backups
        std::vector<std::string> watched_config_paths;
        bool backup_on_change = true;
        bool backup_on_schedule = true;
    };

    /**
     * @brief Constructor
     * @param backup_manager Backup manager to use for operations
     * @param config Schedule configuration
     */
    explicit AutomaticBackupScheduler(std::shared_ptr<ConfigurationBackup> backup_manager, 
                                    const ScheduleConfig& config = ScheduleConfig());

    /**
     * @brief Destructor
     */
    ~AutomaticBackupScheduler();

    /**
     * @brief Start automatic backup scheduling
     */
    void start();

    /**
     * @brief Stop automatic backup scheduling
     */
    void stop();

    /**
     * @brief Add configuration path to watch
     * @param config_path Configuration file path to watch
     */
    void add_watched_path(const std::string& config_path);

    /**
     * @brief Remove configuration path from watching
     * @param config_path Configuration file path to stop watching
     */
    void remove_watched_path(const std::string& config_path);

    /**
     * @brief Update schedule configuration
     * @param config New schedule configuration
     */
    void update_schedule(const ScheduleConfig& config);

    /**
     * @brief Get current schedule configuration
     * @return ScheduleConfig Current configuration
     */
    ScheduleConfig get_schedule() const;

    /**
     * @brief Force immediate backup of all watched configurations
     * @return size_t Number of backups created
     */
    size_t force_backup_all();

private:
    std::shared_ptr<ConfigurationBackup> backup_manager_;
    ScheduleConfig config_;
    mutable std::mutex config_mutex_;
    
    std::atomic<bool> running_{false};
    std::thread scheduler_thread_;
    std::vector<std::thread> watcher_threads_;
    std::atomic<bool> should_stop_{false};
    
    // File watching state
    std::unordered_map<std::string, std::filesystem::file_time_type> last_write_times_;
    std::mutex watch_state_mutex_;
    
    void scheduler_worker();
    void watch_config_changes();
    void perform_scheduled_backup();
    void handle_config_change(const std::string& config_path);
    void cleanup_old_scheduled_backups();
};

} // namespace omop::common::config