/**
 * @file schema_validator.cpp
 * @brief Implementation of formal schema validation for OMOP ETL configuration files
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#include "schema_validator.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cctype>
#include "../exceptions.h"

namespace omop::common::config {

// SchemaValidationResult implementation
std::string SchemaValidationResult::get_error_summary() const {
    if (is_valid) {
        return "Validation passed successfully";
    }
    
    std::ostringstream oss;
    oss << "Validation failed for schema version " << schema_version << "\n";
    
    if (!errors.empty()) {
        oss << "Errors (" << errors.size() << "):\n";
        for (const auto& error : errors) {
            oss << "  - " << error << "\n";
        }
    }
    
    if (!warnings.empty()) {
        oss << "Warnings (" << warnings.size() << "):\n";
        for (const auto& warning : warnings) {
            oss << "  - " << warning << "\n";
        }
    }
    
    return oss.str();
}

// SchemaField implementation
SchemaValidationResult SchemaField::validate_value(const YAML::Node& value) const {
    SchemaValidationResult result;
    result.is_valid = true;
    
    // Check if value is defined when required
    if (required && (!value || value.IsNull())) {
        result.is_valid = false;
        result.errors.push_back("Field '" + name + "' is required but not provided");
        return result;
    }
    
    // Skip validation if value is not provided and not required
    if (!value || value.IsNull()) {
        return result;
    }
    
    // Type validation
    switch (type) {
        case Type::String:
            if (!value.IsScalar() && !value.IsNull()) {
                result.is_valid = false;
                result.errors.push_back("Field '" + name + "' must be a string");
            }
            break;
        case Type::Integer:
            try {
                [[maybe_unused]] int int_value = value.as<int>();
            } catch (...) {
                result.is_valid = false;
                result.errors.push_back("Field '" + name + "' must be an integer");
            }
            break;
        case Type::Double:
            try {
                [[maybe_unused]] double double_value = value.as<double>();
            } catch (...) {
                result.is_valid = false;
                result.errors.push_back("Field '" + name + "' must be a number");
            }
            break;
        case Type::Boolean:
            try {
                [[maybe_unused]] bool bool_value = value.as<bool>();
            } catch (...) {
                result.is_valid = false;
                result.errors.push_back("Field '" + name + "' must be a boolean");
            }
            break;
        case Type::Array:
            if (!value.IsSequence()) {
                result.is_valid = false;
                result.errors.push_back("Field '" + name + "' must be an array");
            }
            break;
        case Type::Object:
            if (!value.IsMap()) {
                result.is_valid = false;
                result.errors.push_back("Field '" + name + "' must be an object");
            }
            break;
        case Type::Any:
            // Any type is acceptable
            break;
    }
    
    // Pattern validation for strings
    if (pattern && value.IsScalar() && result.is_valid) {
        std::string str_value = value.as<std::string>();
        if (!std::regex_match(str_value, *pattern)) {
            result.is_valid = false;
            result.errors.push_back("Field '" + name + "' does not match required pattern");
        }
    }
    
    // Range validation for numbers
    if (min_value || max_value) {
        try {
            double num_value = value.as<double>();
            if (min_value && num_value < *min_value) {
                result.is_valid = false;
                result.errors.push_back("Field '" + name + "' is below minimum value " + std::to_string(*min_value));
            }
            if (max_value && num_value > *max_value) {
                result.is_valid = false;
                result.errors.push_back("Field '" + name + "' exceeds maximum value " + std::to_string(*max_value));
            }
        } catch (...) {
            // Range validation only applies to numeric values
        }
    }
    
    // Allowed values validation
    if (!allowed_values.empty() && value.IsScalar()) {
        std::string str_value = value.as<std::string>();
        auto it = std::find(allowed_values.begin(), allowed_values.end(), str_value);
        if (it == allowed_values.end()) {
            result.is_valid = false;
            result.errors.push_back("Field '" + name + "' has invalid value. Allowed values: " +
                                    [&]() {
                                        std::ostringstream oss;
                                        for (size_t i = 0; i < allowed_values.size(); ++i) {
                                            if (i > 0) oss << ", ";
                                            oss << allowed_values[i];
                                        }
                                        return oss.str();
                                    }());
        }
    }
    
    // Deprecation warning
    if (deprecated) {
        result.warnings.push_back("Field '" + name + "' is deprecated" + 
                                  (description ? ": " + *description : ""));
    }
    
    return result;
}

// ConfigurationSchema implementation
ConfigurationSchema::ConfigurationSchema(const std::string& version)
    : version_(version) {}

void ConfigurationSchema::load_from_file(const std::string& schema_path) {
    std::ifstream file(schema_path);
    if (!file.is_open()) {
        throw omop::common::ConfigurationException(
            "Cannot open schema file: " + schema_path);
    }
    
    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    load_from_string(content);
}

void ConfigurationSchema::load_from_string(const std::string& schema_yaml) {
    try {
        YAML::Node schema_node = YAML::Load(schema_yaml);
        
        // Parse version if provided
        if (schema_node["version"]) {
            version_ = schema_node["version"].as<std::string>();
        }
        
        // Parse fields
        if (schema_node["fields"]) {
            for (const auto& field_entry : schema_node["fields"]) {
                std::string field_name = field_entry.first.as<std::string>();
                SchemaField field = parse_field_definition(field_entry.second);
                field.name = field_name;
                fields_[field_name] = field;
            }
        }
    } catch (const YAML::Exception& e) {
        throw omop::common::ConfigurationException(
            "Failed to parse schema YAML: " + std::string(e.what()));
    }
}

void ConfigurationSchema::add_field(const std::string& field_path, const SchemaField& field) {
    fields_[field_path] = field;
}

SchemaValidationResult ConfigurationSchema::validate(const YAML::Node& config) const {
    SchemaValidationResult result;
    result.is_valid = true;
    result.schema_version = version_;
    
    return validate_object(config, fields_);
}

bool ConfigurationSchema::has_field(const std::string& field_path) const {
    return fields_.find(field_path) != fields_.end();
}

std::optional<SchemaField> ConfigurationSchema::get_field(const std::string& field_path) const {
    auto it = fields_.find(field_path);
    if (it != fields_.end()) {
        return it->second;
    }
    return std::nullopt;
}

SchemaField ConfigurationSchema::parse_field_definition(const YAML::Node& field_node) const {
    SchemaField field;
    
    // Type
    if (field_node["type"]) {
        std::string type_str = field_node["type"].as<std::string>();
        std::transform(type_str.begin(), type_str.end(), type_str.begin(), ::tolower);
        
        if (type_str == "string") {
            field.type = SchemaField::Type::String;
        } else if (type_str == "integer" || type_str == "int") {
            field.type = SchemaField::Type::Integer;
        } else if (type_str == "double" || type_str == "number" || type_str == "float") {
            field.type = SchemaField::Type::Double;
        } else if (type_str == "boolean" || type_str == "bool") {
            field.type = SchemaField::Type::Boolean;
        } else if (type_str == "array" || type_str == "sequence") {
            field.type = SchemaField::Type::Array;
        } else if (type_str == "object" || type_str == "map") {
            field.type = SchemaField::Type::Object;
        } else {
            field.type = SchemaField::Type::Any;
        }
    }
    
    // Required
    if (field_node["required"]) {
        field.required = field_node["required"].as<bool>();
    }
    
    // Deprecated
    if (field_node["deprecated"]) {
        field.deprecated = field_node["deprecated"].as<bool>();
    }
    
    // Default value
    if (field_node["default"]) {
        field.default_value = field_node["default"].as<std::string>();
    }
    
    // Description
    if (field_node["description"]) {
        field.description = field_node["description"].as<std::string>();
    }
    
    // Pattern
    if (field_node["pattern"]) {
        field.pattern = std::regex(field_node["pattern"].as<std::string>());
    }
    
    // Min/Max values
    if (field_node["min"]) {
        field.min_value = field_node["min"].as<double>();
    }
    if (field_node["max"]) {
        field.max_value = field_node["max"].as<double>();
    }
    
    // Allowed values
    if (field_node["enum"]) {
        for (const auto& value : field_node["enum"]) {
            field.allowed_values.push_back(value.as<std::string>());
        }
    }
    
    // Nested fields for objects
    if (field_node["fields"]) {
        for (const auto& nested_field : field_node["fields"]) {
            std::string nested_name = nested_field.first.as<std::string>();
            SchemaField nested_schema = parse_field_definition(nested_field.second);
            nested_schema.name = nested_name;
            field.nested_fields[nested_name] = nested_schema;
        }
    }
    
    return field;
}

SchemaValidationResult ConfigurationSchema::validate_object(
    const YAML::Node& config,
    const std::unordered_map<std::string, SchemaField>& schema_fields,
    const std::string& path_prefix) const {
    
    SchemaValidationResult result;
    result.is_valid = true;
    result.schema_version = version_;
    
    // Validate each field in schema
    for (const auto& [field_name, field_schema] : schema_fields) {
        std::string full_path = path_prefix.empty() ? field_name : path_prefix + "." + field_name;
        
        YAML::Node field_value;
        if (config.IsMap() && config[field_name]) {
            field_value = config[field_name];
        }
        
        SchemaValidationResult field_result = field_schema.validate_value(field_value);
        
        // Merge results
        if (!field_result.is_valid) {
            result.is_valid = false;
        }
        
        // Add path prefix to error messages
        for (const auto& error : field_result.errors) {
            result.errors.push_back(full_path + ": " + error);
        }
        
        for (const auto& warning : field_result.warnings) {
            result.warnings.push_back(full_path + ": " + warning);
        }
        
        // Validate nested objects
        if (field_schema.type == SchemaField::Type::Object && field_value && field_value.IsMap()) {
            SchemaValidationResult nested_result = validate_object(
                field_value, field_schema.nested_fields, full_path);
            
            if (!nested_result.is_valid) {
                result.is_valid = false;
            }
            
            result.errors.insert(result.errors.end(),
                                nested_result.errors.begin(), nested_result.errors.end());
            result.warnings.insert(result.warnings.end(),
                                  nested_result.warnings.begin(), nested_result.warnings.end());
        }
    }
    
    return result;
}

std::vector<std::string> ConfigurationSchema::split_path(const std::string& path) const {
    std::vector<std::string> parts;
    std::string current;
    
    for (char c : path) {
        if (c == '.') {
            if (!current.empty()) {
                parts.push_back(current);
                current.clear();
            }
        } else {
            current += c;
        }
    }
    
    if (!current.empty()) {
        parts.push_back(current);
    }
    
    return parts;
}

// OMOPConfigurationSchemaFactory implementation
std::unique_ptr<ConfigurationSchema> OMOPConfigurationSchemaFactory::create_standard_schema(
    const std::string& version) {
    
    auto schema = std::make_unique<ConfigurationSchema>(version);
    
    add_database_schema_fields(*schema);
    add_etl_schema_fields(*schema);
    add_logging_schema_fields(*schema);
    add_monitoring_schema_fields(*schema);
    
    return schema;
}

std::unique_ptr<ConfigurationSchema> OMOPConfigurationSchemaFactory::create_uk_healthcare_schema(
    const std::string& version) {
    
    auto schema = create_standard_schema(version);
    add_uk_healthcare_fields(*schema);
    
    return schema;
}

std::unique_ptr<ConfigurationSchema> OMOPConfigurationSchemaFactory::create_minimal_schema(
    const std::string& version) {
    
    auto schema = std::make_unique<ConfigurationSchema>(version);
    
    // Add only essential fields
    SchemaField version_field;
    version_field.name = "version";
    version_field.type = SchemaField::Type::String;
    version_field.required = true;
    version_field.description = "Configuration version";
    schema->add_field("version", version_field);
    
    return schema;
}

void OMOPConfigurationSchemaFactory::add_database_schema_fields(ConfigurationSchema& schema) {
    // Database configuration
    SchemaField database_field;
    database_field.name = "database";
    database_field.type = SchemaField::Type::Object;
    database_field.required = true;
    database_field.description = "Database configuration";
    
    // Database host
    SchemaField host_field;
    host_field.name = "host";
    host_field.type = SchemaField::Type::String;
    host_field.required = true;
    host_field.description = "Database host";
    database_field.nested_fields["host"] = host_field;
    
    // Database port
    SchemaField port_field;
    port_field.name = "port";
    port_field.type = SchemaField::Type::Integer;
    port_field.required = true;
    port_field.min_value = 1;
    port_field.max_value = 65535;
    port_field.description = "Database port";
    database_field.nested_fields["port"] = port_field;
    
    // Database name
    SchemaField dbname_field;
    dbname_field.name = "dbname";
    dbname_field.type = SchemaField::Type::String;
    dbname_field.required = true;
    dbname_field.description = "Database name";
    database_field.nested_fields["dbname"] = dbname_field;
    
    // Database user
    SchemaField user_field;
    user_field.name = "user";
    user_field.type = SchemaField::Type::String;
    user_field.required = true;
    user_field.description = "Database user";
    database_field.nested_fields["user"] = user_field;
    
    // Database password
    SchemaField password_field;
    password_field.name = "password";
    password_field.type = SchemaField::Type::String;
    password_field.required = true;
    password_field.description = "Database password";
    database_field.nested_fields["password"] = password_field;
    
    schema.add_field("database", database_field);
}

void OMOPConfigurationSchemaFactory::add_etl_schema_fields(ConfigurationSchema& schema) {
    // ETL configuration
    SchemaField etl_field;
    etl_field.name = "etl";
    etl_field.type = SchemaField::Type::Object;
    etl_field.required = true;
    etl_field.description = "ETL pipeline configuration";
    
    // Batch size
    SchemaField batch_size_field;
    batch_size_field.name = "batch_size";
    batch_size_field.type = SchemaField::Type::Integer;
    batch_size_field.required = false;
    batch_size_field.min_value = 1;
    batch_size_field.max_value = 100000;
    batch_size_field.default_value = "1000";
    batch_size_field.description = "ETL batch size";
    etl_field.nested_fields["batch_size"] = batch_size_field;
    
    // Parallel processing
    SchemaField parallel_field;
    parallel_field.name = "parallel_workers";
    parallel_field.type = SchemaField::Type::Integer;
    parallel_field.required = false;
    parallel_field.min_value = 1;
    parallel_field.max_value = 32;
    parallel_field.default_value = "4";
    parallel_field.description = "Number of parallel workers";
    etl_field.nested_fields["parallel_workers"] = parallel_field;
    
    schema.add_field("etl", etl_field);
}

void OMOPConfigurationSchemaFactory::add_uk_healthcare_fields(ConfigurationSchema& schema) {
    // UK healthcare specific configuration
    SchemaField uk_config_field;
    uk_config_field.name = "uk_healthcare";
    uk_config_field.type = SchemaField::Type::Object;
    uk_config_field.required = false;
    uk_config_field.description = "UK healthcare specific settings";
    
    // NHS number validation
    SchemaField nhs_validation_field;
    nhs_validation_field.name = "validate_nhs_numbers";
    nhs_validation_field.type = SchemaField::Type::Boolean;
    nhs_validation_field.required = false;
    nhs_validation_field.default_value = "true";
    nhs_validation_field.description = "Enable NHS number validation";
    uk_config_field.nested_fields["validate_nhs_numbers"] = nhs_validation_field;
    
    // Postcode validation
    SchemaField postcode_validation_field;
    postcode_validation_field.name = "validate_postcodes";
    postcode_validation_field.type = SchemaField::Type::Boolean;
    postcode_validation_field.required = false;
    postcode_validation_field.default_value = "true";
    postcode_validation_field.description = "Enable UK postcode validation";
    uk_config_field.nested_fields["validate_postcodes"] = postcode_validation_field;
    
    // Date format
    SchemaField date_format_field;
    date_format_field.name = "date_format";
    date_format_field.type = SchemaField::Type::String;
    date_format_field.required = false;
    date_format_field.allowed_values = {"DD/MM/YYYY", "DD-MM-YYYY", "YYYY-MM-DD"};
    date_format_field.default_value = "DD/MM/YYYY";
    date_format_field.description = "UK date format preference";
    uk_config_field.nested_fields["date_format"] = date_format_field;
    
    schema.add_field("uk_healthcare", uk_config_field);
}

void OMOPConfigurationSchemaFactory::add_logging_schema_fields(ConfigurationSchema& schema) {
    // Logging configuration
    SchemaField logging_field;
    logging_field.name = "logging";
    logging_field.type = SchemaField::Type::Object;
    logging_field.required = false;
    logging_field.description = "Logging configuration";
    
    // Log level
    SchemaField level_field;
    level_field.name = "level";
    level_field.type = SchemaField::Type::String;
    level_field.required = false;
    level_field.allowed_values = {"DEBUG", "INFO", "WARN", "ERROR", "CRITICAL"};
    level_field.default_value = "INFO";
    level_field.description = "Logging level";
    logging_field.nested_fields["level"] = level_field;
    
    // Log file path
    SchemaField file_field;
    file_field.name = "file";
    file_field.type = SchemaField::Type::String;
    file_field.required = false;
    file_field.description = "Log file path";
    logging_field.nested_fields["file"] = file_field;
    
    schema.add_field("logging", logging_field);
}

void OMOPConfigurationSchemaFactory::add_monitoring_schema_fields(ConfigurationSchema& schema) {
    // Monitoring configuration
    SchemaField monitoring_field;
    monitoring_field.name = "monitoring";
    monitoring_field.type = SchemaField::Type::Object;
    monitoring_field.required = false;
    monitoring_field.description = "Monitoring configuration";
    
    // Enable metrics
    SchemaField metrics_field;
    metrics_field.name = "enable_metrics";
    metrics_field.type = SchemaField::Type::Boolean;
    metrics_field.required = false;
    metrics_field.default_value = "true";
    metrics_field.description = "Enable metrics collection";
    monitoring_field.nested_fields["enable_metrics"] = metrics_field;
    
    // Metrics port
    SchemaField metrics_port_field;
    metrics_port_field.name = "metrics_port";
    metrics_port_field.type = SchemaField::Type::Integer;
    metrics_port_field.required = false;
    metrics_port_field.min_value = 1024;
    metrics_port_field.max_value = 65535;
    metrics_port_field.default_value = "9090";
    metrics_port_field.description = "Metrics HTTP port";
    monitoring_field.nested_fields["metrics_port"] = metrics_port_field;
    
    schema.add_field("monitoring", monitoring_field);
}

// SchemaConfigurationValidator implementation
SchemaConfigurationValidator::SchemaConfigurationValidator(std::unique_ptr<ConfigurationSchema> schema)
    : schema_(std::move(schema)) {}

SchemaValidationResult SchemaConfigurationValidator::validate_file(const std::string& config_path) const {
    try {
        YAML::Node config = YAML::LoadFile(config_path);
        return validate_node(config);
    } catch (const YAML::Exception& e) {
        SchemaValidationResult result;
        result.is_valid = false;
        result.errors.push_back("Failed to load configuration file '" + config_path + "': " + e.what());
        return result;
    }
}

SchemaValidationResult SchemaConfigurationValidator::validate_yaml(const std::string& config_yaml) const {
    try {
        YAML::Node config = YAML::Load(config_yaml);
        return validate_node(config);
    } catch (const YAML::Exception& e) {
        SchemaValidationResult result;
        result.is_valid = false;
        result.errors.push_back("Failed to parse configuration YAML: " + std::string(e.what()));
        return result;
    }
}

SchemaValidationResult SchemaConfigurationValidator::validate_node(const YAML::Node& config) const {
    SchemaValidationResult result = schema_->validate(config);
    
    // In strict mode, warnings are treated as errors
    if (strict_mode_ && !result.warnings.empty()) {
        result.is_valid = false;
        result.errors.insert(result.errors.end(), 
                            result.warnings.begin(), result.warnings.end());
        result.warnings.clear();
    }
    
    return result;
}

} // namespace omop::common::config