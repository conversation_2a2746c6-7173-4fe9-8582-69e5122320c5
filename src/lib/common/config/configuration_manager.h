/**
 * @file configuration_manager.h
 * @brief Refactored configuration management system with focused components
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include <memory>
#include <string>
#include <chrono>
#include <functional>
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <unordered_map>
#include <vector>
#include <thread>
#include <filesystem>
#include <type_traits>

namespace omop::common::config {

// Forward declarations
class ConfigurationLoader;
class ConfigurationValidator;
class ConfigurationWatcher;
class ConfigurationCache;
class ConfigurationEncryption;
class ConfigurationAudit;

/**
 * @brief Main configuration manager with focused responsibilities
 * 
 * This is the main facade that coordinates between specialized components.
 * Each component handles a specific aspect of configuration management.
 */
class ConfigurationManager {
public:
    /**
     * @brief Configuration for the manager itself
     */
    struct Config {
        bool enable_hot_reload = true;
        bool enable_caching = true;
        bool enable_encryption = false;
        bool enable_audit = true;
        std::chrono::seconds reload_interval{30};
        size_t cache_size_mb = 50;
        std::string encryption_key_path;
        std::string audit_log_path;
    };

    /**
     * @brief Constructor
     * @param config Manager configuration
     */
    explicit ConfigurationManager(const Config& config = {});

    /**
     * @brief Destructor
     */
    ~ConfigurationManager();

    /**
     * @brief Load configuration from file
     * @param config_path Path to configuration file
     * @return true if successful
     */
    bool load_config(const std::string& config_path);

    /**
     * @brief Reload configuration if changed
     * @return true if configuration was reloaded
     */
    bool reload_if_changed();

    /**
     * @brief Get configuration value by path
     * @tparam T Value type
     * @param path Configuration path (e.g., "database.host")
     * @param default_value Default value if not found
     * @return T Configuration value
     */
    template<typename T>
    T get(const std::string& path, const T& default_value = T{}) const {
        std::shared_lock<std::shared_mutex> lock(config_mutex_);
        
        // Check cache first if enabled
        if (caching_enabled_.load()) {
            std::lock_guard<std::mutex> cache_lock(cache_mutex_);
            auto cache_it = cache_.find(path);
            if (cache_it != cache_.end()) {
                auto now = std::chrono::system_clock::now();
                if (now - cache_it->second.timestamp < cache_ttl_) {
                    update_statistics(path, true);
                    ++cache_it->second.access_count;
                    return convert_from_string<T>(cache_it->second.value, default_value);
                } else {
                    cache_.erase(cache_it);  // Expired entry
                }
            }
        }
        
        // Find in configuration data
        auto it = configuration_data_.find(path);
        if (it != configuration_data_.end()) {
            update_statistics(path, false);
            
            // Cache the result if caching is enabled
            if (caching_enabled_.load()) {
                std::lock_guard<std::mutex> cache_lock(cache_mutex_);
                cache_[path] = {it->second, std::chrono::system_clock::now(), 1};
            }
            
            return convert_from_string<T>(it->second, default_value);
        }
        
        return default_value;
    }

    /**
     * @brief Set configuration value
     * @tparam T Value type
     * @param path Configuration path
     * @param value Value to set
     * @param persist Whether to persist to file
     * @return true if successful
     */
    template<typename T>
    bool set(const std::string& path, const T& value, bool persist = false) {
        std::unique_lock<std::shared_mutex> lock(config_mutex_);
        
        std::string old_value;
        auto it = configuration_data_.find(path);
        if (it != configuration_data_.end()) {
            old_value = it->second;
        }
        
        std::string new_value = convert_to_string<T>(value);
        configuration_data_[path] = new_value;
        
        // Invalidate cache entry
        if (caching_enabled_.load()) {
            std::lock_guard<std::mutex> cache_lock(cache_mutex_);
            cache_.erase(path);
        }
        
        lock.unlock();
        
        // Notify callbacks
        if (old_value != new_value) {
            notify_callbacks(path, old_value, new_value);
        }
        
        if (persist) {
            // TODO: Implement persistence to file
            // This would involve rewriting the configuration file
        }
        
        return true;
    }

    /**
     * @brief Check if configuration path exists
     * @param path Configuration path
     * @return true if exists
     */
    bool has(const std::string& path) const;

    /**
     * @brief Register configuration change callback
     * @param path Configuration path to watch
     * @param callback Callback function
     */
    void register_change_callback(
        const std::string& path,
        std::function<void(const std::string&, const std::string&)> callback);

    /**
     * @brief Validate current configuration
     * @return Validation errors (empty if valid)
     */
    std::vector<std::string> validate() const;

    /**
     * @brief Get configuration statistics
     */
    struct Statistics {
        size_t total_keys = 0;
        size_t cache_hits = 0;
        size_t cache_misses = 0;
        size_t reload_count = 0;
        std::chrono::system_clock::time_point last_reload;
        size_t memory_usage_bytes = 0;
    };

    Statistics get_statistics() const;

    /**
     * @brief Create backup of current configuration
     * @param backup_path Path for backup file
     * @return true if successful
     */
    bool create_backup(const std::string& backup_path) const;

    /**
     * @brief Restore configuration from backup
     * @param backup_path Path to backup file
     * @return true if successful
     */
    bool restore_backup(const std::string& backup_path);

    /**
     * @brief Enable configuration caching
     * @param enabled Enable/disable caching
     */
    void enable_caching(bool enabled);

    /**
     * @brief Set cache TTL (Time To Live)
     * @param ttl Cache time to live
     */
    void set_cache_ttl(std::chrono::seconds ttl);

    /**
     * @brief Enable hot reload
     * @param enabled Enable/disable hot reload
     */
    void enable_hot_reload(bool enabled);

    /**
     * @brief Set reload interval for hot reload
     * @param interval Reload check interval
     */
    void set_reload_interval(std::chrono::seconds interval);

private:
    // Configuration management state
    Config config_;
    std::string current_config_path_;
    std::unordered_map<std::string, std::string> configuration_data_;
    mutable std::shared_mutex config_mutex_;
    
    // Hot reload and caching state
    std::atomic<bool> hot_reload_enabled_{false};
    std::atomic<bool> caching_enabled_{false};
    std::chrono::seconds cache_ttl_{300};
    std::chrono::seconds reload_interval_{30};
    std::filesystem::file_time_type last_write_time_;
    std::thread reload_thread_;
    std::atomic<bool> should_stop_reload_{false};
    
    // Statistics tracking
    mutable Statistics stats_;
    mutable std::mutex stats_mutex_;
    
    // Change callbacks
    std::unordered_map<std::string, std::function<void(const std::string&, const std::string&)>> callbacks_;
    std::mutex callbacks_mutex_;
    
    // Cache management
    struct CacheEntry {
        std::string value;
        std::chrono::system_clock::time_point timestamp;
        size_t access_count = 0;
    };
    mutable std::unordered_map<std::string, CacheEntry> cache_;
    mutable std::mutex cache_mutex_;
    
    // Helper methods
    void start_reload_thread();
    void stop_reload_thread();
    void reload_worker();
    bool needs_reload() const;
    void update_statistics(const std::string& key, bool cache_hit) const;
    void notify_callbacks(const std::string& path, const std::string& old_value, const std::string& new_value);
    std::string resolve_path(const std::string& path) const;
    
    // Template helper methods for type conversion
    template<typename T>
    T convert_from_string(const std::string& str, const T& default_value) const {
        if constexpr (std::is_same_v<T, std::string>) {
            return str;
        } else if constexpr (std::is_same_v<T, int>) {
            try { return std::stoi(str); } catch (...) { return default_value; }
        } else if constexpr (std::is_same_v<T, long>) {
            try { return std::stol(str); } catch (...) { return default_value; }
        } else if constexpr (std::is_same_v<T, long long>) {
            try { return std::stoll(str); } catch (...) { return default_value; }
        } else if constexpr (std::is_same_v<T, double>) {
            try { return std::stod(str); } catch (...) { return default_value; }
        } else if constexpr (std::is_same_v<T, float>) {
            try { return std::stof(str); } catch (...) { return default_value; }
        } else if constexpr (std::is_same_v<T, bool>) {
            if (str == "true" || str == "1" || str == "yes" || str == "on") return true;
            if (str == "false" || str == "0" || str == "no" || str == "off") return false;
            return default_value;
        } else {
            return default_value;  // Unsupported type
        }
    }
    
    template<typename T>
    std::string convert_to_string(const T& value) const {
        if constexpr (std::is_same_v<T, std::string>) {
            return value;
        } else if constexpr (std::is_arithmetic_v<T>) {
            return std::to_string(value);
        } else if constexpr (std::is_same_v<T, bool>) {
            return value ? "true" : "false";
        } else {
            return "";  // Unsupported type
        }
    }
};

} // namespace omop::common::config