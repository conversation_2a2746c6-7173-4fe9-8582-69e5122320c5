/**
 * @file configuration_loader.h
 * @brief Configuration loading component
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include <string>
#include <memory>
#include <chrono>
#include <unordered_map>
#include <any>
#include <mutex>
#include <yaml-cpp/yaml.h>

namespace omop::common::config {

/**
 * @brief Configuration loading results
 */
struct LoadResult {
    bool success = false;
    std::string error_message;
    YAML::Node config_data;
    std::chrono::system_clock::time_point load_time;
    size_t file_size_bytes = 0;
    std::chrono::milliseconds load_duration{0};
};

/**
 * @brief Focused component for loading configuration files
 * 
 * Handles:
 * - YAML file loading and parsing
 * - Environment variable substitution
 * - Include file processing
 * - Error handling and reporting
 * - Performance tracking
 */
class ConfigurationLoader {
public:
    /**
     * @brief Configuration for the loader
     */
    struct Config {
        bool enable_env_substitution = true;
        bool enable_includes = true;
        bool enable_caching = true;
        std::string include_base_path = ".";
        std::chrono::seconds cache_ttl{300}; // 5 minutes
    };

    /**
     * @brief Constructor
     * @param config Loader configuration
     */
    explicit ConfigurationLoader(const Config& config = {});

    /**
     * @brief Load configuration from file
     * @param file_path Path to configuration file
     * @return LoadResult Load result with data or error
     */
    LoadResult load_file(const std::string& file_path);

    /**
     * @brief Load configuration from string
     * @param yaml_content YAML content as string
     * @param source_name Name for error reporting
     * @return LoadResult Load result with data or error
     */
    LoadResult load_string(const std::string& yaml_content, 
                          const std::string& source_name = "string");

    /**
     * @brief Check if file has been modified
     * @param file_path File path to check
     * @return true if file has been modified since last load
     */
    bool is_file_modified(const std::string& file_path) const;

    /**
     * @brief Get file modification time
     * @param file_path File path
     * @return Modification time
     */
    std::chrono::system_clock::time_point get_modification_time(
        const std::string& file_path) const;

    /**
     * @brief Clear cached data
     */
    void clear_cache();

    /**
     * @brief Get loader statistics
     */
    struct Statistics {
        size_t files_loaded = 0;
        size_t cache_hits = 0;
        size_t cache_misses = 0;
        size_t env_substitutions = 0;
        size_t includes_processed = 0;
        std::chrono::milliseconds total_load_time{0};
        std::chrono::milliseconds average_load_time{0};
    };

    Statistics get_statistics() const;

private:
    /**
     * @brief Process environment variable substitutions
     * @param node YAML node to process
     */
    void process_env_substitutions(YAML::Node& node);

    /**
     * @brief Process include directives
     * @param node YAML node to process
     * @param base_path Base path for relative includes
     */
    void process_includes(YAML::Node& node, const std::string& base_path);

    /**
     * @brief Substitute environment variables in string
     * @param text Text with potential ${VAR} patterns
     * @return String with substitutions applied
     */
    std::string substitute_env_vars(const std::string& text);

    /**
     * @brief Merge included configuration
     * @param target Target node to merge into
     * @param source Source node to merge from
     */
    void merge_nodes(YAML::Node& target, const YAML::Node& source);

    // Configuration loader state
    Config config_;
    
    // Cache management
    struct CacheEntry {
        YAML::Node data;
        std::chrono::system_clock::time_point load_time;
        std::chrono::system_clock::time_point file_time;
        std::string file_path;
    };
    mutable std::unordered_map<std::string, CacheEntry> cache_;
    mutable std::mutex cache_mutex_;
    
    // Statistics tracking
    mutable Statistics stats_;
    mutable std::mutex stats_mutex_;
    
    // File modification tracking
    mutable std::unordered_map<std::string, std::chrono::system_clock::time_point> file_times_;
    mutable std::mutex file_times_mutex_;
};

} // namespace omop::common::config