/**
 * @file alert_manager.h
 * @brief Alert management system for OMOP ETL monitoring and notifications
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <chrono>
#include <atomic>
#include <mutex>
#include <thread>
#include <condition_variable>
#include <queue>
#include "health_monitor.h"

namespace omop::common {

/**
 * @brief Alert severity levels
 */
enum class AlertSeverity {
    INFO,       ///< Informational alert
    WARNING,    ///< Warning alert
    ERROR,      ///< Error alert  
    CRITICAL    ///< Critical alert requiring immediate attention
};

/**
 * @brief Alert status enumeration
 */
enum class AlertStatus {
    ACTIVE,     ///< Alert is currently active
    RESOLVED,   ///< Alert has been resolved
    SILENCED,   ///< Alert is silenced
    PENDING     ///< Alert is pending evaluation
};

/**
 * @brief Alert information structure
 */
struct <PERSON><PERSON> {
    std::string id;
    std::string component_name;
    std::string alert_name;
    AlertSeverity severity{AlertSeverity::INFO};
    AlertStatus status{AlertStatus::PENDING};
    std::string message;
    std::string description;
    std::chrono::system_clock::time_point created_at;
    std::chrono::system_clock::time_point updated_at;
    std::chrono::system_clock::time_point resolved_at;
    std::unordered_map<std::string, std::string> labels;
    std::unordered_map<std::string, std::string> annotations;
    double value{0.0};
    double threshold{0.0};
    
    /**
     * @brief Check if alert is active
     */
    bool is_active() const {
        return status == AlertStatus::ACTIVE;
    }
    
    /**
     * @brief Get alert age in seconds
     */
    std::chrono::seconds get_age() const {
        auto now = std::chrono::system_clock::now();
        return std::chrono::duration_cast<std::chrono::seconds>(now - created_at);
    }
};

/**
 * @brief Alert rule for evaluating conditions
 */
struct AlertRule {
    std::string rule_name;
    std::string component_name;
    std::string metric_name;
    AlertSeverity severity{AlertSeverity::WARNING};
    std::string condition;              ///< e.g., "greater_than", "less_than", "equals"
    double threshold{0.0};
    std::chrono::seconds duration{0};   ///< How long condition must be true
    std::chrono::seconds interval{60};  ///< Check interval
    bool enabled{true};
    std::unordered_map<std::string, std::string> labels;
    std::string message_template;
    std::function<bool(double, double)> evaluator; ///< Custom evaluation function
};

/**
 * @brief Alert channel interface for sending notifications
 */
class IAlertChannel {
public:
    virtual ~IAlertChannel() = default;
    
    /**
     * @brief Send alert notification
     * @param alert Alert to send
     * @return bool True if sent successfully
     */
    virtual bool send_alert(const Alert& alert) = 0;
    
    /**
     * @brief Get channel name
     * @return Channel name
     */
    virtual std::string get_channel_name() const = 0;
    
    /**
     * @brief Test channel connectivity
     * @return bool True if channel is reachable
     */
    virtual bool test_connection() = 0;
};

/**
 * @brief Console alert channel (for development/debugging)
 */
class ConsoleAlertChannel : public IAlertChannel {
public:
    bool send_alert(const Alert& alert) override;
    std::string get_channel_name() const override { return "console"; }
    bool test_connection() override { return true; }
};

/**
 * @brief Email alert channel
 */
class EmailAlertChannel : public IAlertChannel {
public:
    struct Config {
        std::string smtp_server;
        int smtp_port{587};
        std::string username;
        std::string password;
        std::string from_address;
        std::vector<std::string> to_addresses;
        bool use_tls{true};
        std::chrono::seconds timeout{30};
    };
    
    explicit EmailAlertChannel(const Config& config);
    
    bool send_alert(const Alert& alert) override;
    std::string get_channel_name() const override { return "email"; }
    bool test_connection() override;

private:
    Config config_;
    std::string format_email_content(const Alert& alert) const;
};

/**
 * @brief Slack alert channel
 */
class SlackAlertChannel : public IAlertChannel {
public:
    struct Config {
        std::string webhook_url;
        std::string channel;
        std::string username{"OMOP-ETL-Bot"};
        std::string icon_emoji{":warning:"};
        std::chrono::seconds timeout{10};
    };
    
    explicit SlackAlertChannel(const Config& config);
    
    bool send_alert(const Alert& alert) override;
    std::string get_channel_name() const override { return "slack"; }
    bool test_connection() override;

private:
    Config config_;
    std::string format_slack_payload(const Alert& alert) const;
};

/**
 * @brief Webhook alert channel
 */
class WebhookAlertChannel : public IAlertChannel {
public:
    struct Config {
        std::string url;
        std::string method{"POST"};
        std::unordered_map<std::string, std::string> headers;
        std::string content_type{"application/json"};
        std::chrono::seconds timeout{10};
        bool verify_ssl{true};
    };
    
    explicit WebhookAlertChannel(const std::string& name, const Config& config);
    
    bool send_alert(const Alert& alert) override;
    std::string get_channel_name() const override { return channel_name_; }
    bool test_connection() override;

private:
    std::string channel_name_;
    Config config_;
    std::string format_webhook_payload(const Alert& alert) const;
};

/**
 * @brief Alert manager for centralized alert processing and notifications
 */
class AlertManager {
public:
    /**
     * @brief Alert manager configuration
     */
    struct Config {
        bool enabled{true};
        std::chrono::seconds evaluation_interval{30};
        std::chrono::seconds cleanup_interval{3600};
        std::chrono::hours alert_retention_period{168}; // 7 days
        size_t max_alerts{10000};
        bool enable_grouping{true};
        std::chrono::seconds grouping_interval{300}; // 5 minutes
        bool enable_inhibition{true};
        std::string alert_store_path{"./alerts.json"};
        bool persist_alerts{true};
    };
    
    /**
     * @brief Constructor
     * @param config Alert manager configuration
     */
    AlertManager();
    explicit AlertManager(const Config& config);
    
    /**
     * @brief Destructor
     */
    ~AlertManager();
    
    /**
     * @brief Start alert manager
     */
    void start();
    
    /**
     * @brief Stop alert manager
     */
    void stop();
    
    /**
     * @brief Add alert rule
     * @param rule Alert rule to add
     */
    void add_alert_rule(const AlertRule& rule);
    
    /**
     * @brief Remove alert rule
     * @param rule_name Rule name to remove
     */
    void remove_alert_rule(const std::string& rule_name);
    
    /**
     * @brief Add alert channel
     * @param channel Alert channel
     */
    void add_alert_channel(std::unique_ptr<IAlertChannel> channel);
    
    /**
     * @brief Remove alert channel
     * @param channel_name Channel name to remove
     */
    void remove_alert_channel(const std::string& channel_name);
    
    /**
     * @brief Fire alert manually
     * @param alert Alert to fire
     */
    void fire_alert(const Alert& alert);
    
    /**
     * @brief Resolve alert
     * @param alert_id Alert ID to resolve
     */
    void resolve_alert(const std::string& alert_id);
    
    /**
     * @brief Silence alert
     * @param alert_id Alert ID to silence
     * @param duration Silence duration
     */
    void silence_alert(const std::string& alert_id, std::chrono::seconds duration);
    
    /**
     * @brief Get active alerts
     * @param severity_filter Optional severity filter
     * @return Vector of active alerts
     */
    std::vector<Alert> get_active_alerts(std::optional<AlertSeverity> severity_filter = {}) const;
    
    /**
     * @brief Get alert by ID
     * @param alert_id Alert ID
     * @return Optional alert
     */
    std::optional<Alert> get_alert(const std::string& alert_id) const;
    
    /**
     * @brief Get alert history
     * @param component_name Optional component filter
     * @param limit Maximum number of alerts to return
     * @return Vector of historical alerts
     */
    std::vector<Alert> get_alert_history(const std::string& component_name = "", size_t limit = 100) const;
    
    /**
     * @brief Set health monitor for automatic health check alerts
     * @param health_monitor Health monitor instance
     */
    void set_health_monitor(std::shared_ptr<HealthMonitor> health_monitor);
    
    /**
     * @brief Process health check results and generate alerts
     * @param results Health check results
     */
    void process_health_results(const std::unordered_map<std::string, HealthCheckResult>& results);
    
    /**
     * @brief Get alert statistics
     */
    struct Statistics {
        size_t total_alerts{0};
        size_t active_alerts{0};
        size_t resolved_alerts{0};
        size_t silenced_alerts{0};
        size_t notifications_sent{0};
        size_t notification_failures{0};
        std::chrono::system_clock::time_point last_alert_time;
        std::chrono::system_clock::time_point last_evaluation_time;
    };
    
    Statistics get_statistics() const;
    
    /**
     * @brief Export alerts in Prometheus format
     * @return Prometheus formatted metrics
     */
    std::string export_prometheus_metrics() const;

private:
    Config config_;
    mutable std::mutex alerts_mutex_;
    std::unordered_map<std::string, Alert> alerts_;
    std::unordered_map<std::string, AlertRule> alert_rules_;
    std::vector<std::unique_ptr<IAlertChannel>> alert_channels_;
    
    // Processing thread
    std::atomic<bool> running_{false};
    std::unique_ptr<std::thread> evaluation_thread_;
    std::unique_ptr<std::thread> cleanup_thread_;
    std::condition_variable evaluation_cv_;
    std::mutex evaluation_mutex_;
    
    // Alert queue for processing
    std::queue<Alert> pending_alerts_;
    std::mutex pending_alerts_mutex_;
    std::condition_variable pending_alerts_cv_;
    std::unique_ptr<std::thread> notification_thread_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    Statistics stats_;
    
    // Health monitor integration
    std::shared_ptr<HealthMonitor> health_monitor_;
    
    /**
     * @brief Evaluation loop for checking alert rules
     */
    void evaluation_loop();
    
    /**
     * @brief Cleanup loop for removing old alerts
     */
    void cleanup_loop();
    
    /**
     * @brief Notification loop for sending alerts
     */
    void notification_loop();
    
    /**
     * @brief Evaluate single alert rule
     * @param rule Alert rule to evaluate
     */
    void evaluate_rule(const AlertRule& rule);
    
    /**
     * @brief Send alert through all channels
     * @param alert Alert to send
     */
    void send_alert_notifications(const Alert& alert);
    
    /**
     * @brief Generate unique alert ID
     * @return Unique alert ID
     */
    std::string generate_alert_id() const;
    
    /**
     * @brief Load alerts from persistent storage
     */
    void load_alerts();
    
    /**
     * @brief Save alerts to persistent storage
     */
    void save_alerts() const;
    
    /**
     * @brief Convert alert severity to string
     * @param severity Alert severity
     * @return String representation
     */
    std::string alert_severity_to_string(AlertSeverity severity) const;
    
    /**
     * @brief Convert alert status to string
     * @param status Alert status
     * @return String representation
     */
    std::string alert_status_to_string(AlertStatus status) const;
};

} // namespace omop::common