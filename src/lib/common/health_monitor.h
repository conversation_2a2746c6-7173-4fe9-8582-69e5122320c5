/**
 * @file health_monitor.h
 * @brief Centralized health monitoring for OMOP ETL components
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <chrono>
#include <atomic>
#include <mutex>
#include <thread>
#include <condition_variable>
#include "metrics_collector.h"

namespace omop::common {

/**
 * @brief Health status enumeration
 */
enum class HealthStatus {
    HEALTHY,        ///< Component is healthy
    DEGRADED,       ///< Component is degraded but functional
    UNHEALTHY,      ///< Component is unhealthy
    UNKNOWN         ///< Health status unknown
};

/**
 * @brief Health check result
 */
struct HealthCheckResult {
    HealthStatus status{HealthStatus::UNKNOWN};
    std::string component_name;
    std::string message;
    std::chrono::system_clock::time_point timestamp;
    std::unordered_map<std::string, std::string> details;
    std::chrono::milliseconds response_time{0};
    
    /**
     * @brief Check if health check passed
     */
    bool is_healthy() const {
        return status == HealthStatus::HEALTHY || status == HealthStatus::DEGRADED;
    }
};

/**
 * @brief Health check interface
 */
class IHealthCheck {
public:
    virtual ~IHealthCheck() = default;
    
    /**
     * @brief Perform health check
     * @return HealthCheckResult Result of the health check
     */
    virtual HealthCheckResult check_health() = 0;
    
    /**
     * @brief Get component name
     * @return Component name
     */
    virtual std::string get_component_name() const = 0;
    
    /**
     * @brief Get check interval
     * @return Check interval in milliseconds
     */
    virtual std::chrono::milliseconds get_check_interval() const = 0;
};

/**
 * @brief Configuration health check
 */
class ConfigurationHealthCheck : public IHealthCheck {
public:
    explicit ConfigurationHealthCheck(const std::string& config_path);
    
    HealthCheckResult check_health() override;
    std::string get_component_name() const override { return "Configuration"; }
    std::chrono::milliseconds get_check_interval() const override { return std::chrono::milliseconds(30000); }

private:
    std::string config_path_;
};

/**
 * @brief Database health check
 */
class DatabaseHealthCheck : public IHealthCheck {
public:
    struct DatabaseConfig {
        std::string host;
        int port{5432};
        std::string database;
        std::string username;
        std::string password;
        std::chrono::milliseconds timeout{5000};
    };
    
    explicit DatabaseHealthCheck(const std::string& name, const DatabaseConfig& config);
    
    HealthCheckResult check_health() override;
    std::string get_component_name() const override { return component_name_; }
    std::chrono::milliseconds get_check_interval() const override { return std::chrono::milliseconds(15000); }

private:
    std::string component_name_;
    DatabaseConfig config_;
};

/**
 * @brief Memory health check
 */
class MemoryHealthCheck : public IHealthCheck {
public:
    struct MemoryConfig {
        size_t max_memory_mb{1024};      ///< Maximum allowed memory in MB
        double warning_threshold{0.8};   ///< Warning threshold (80%)
        double critical_threshold{0.95}; ///< Critical threshold (95%)
    };
    
    MemoryHealthCheck();
    explicit MemoryHealthCheck(const MemoryConfig& config);
    
    HealthCheckResult check_health() override;
    std::string get_component_name() const override { return "Memory"; }
    std::chrono::milliseconds get_check_interval() const override { return std::chrono::milliseconds(10000); }

private:
    MemoryConfig config_;
    size_t get_memory_usage_mb() const;
};

/**
 * @brief Disk space health check
 */
class DiskSpaceHealthCheck : public IHealthCheck {
public:
    struct DiskConfig {
        std::string path;                 ///< Path to monitor
        size_t min_free_space_mb{1024};  ///< Minimum free space in MB
        double warning_threshold{0.2};   ///< Warning threshold (20% free)
        double critical_threshold{0.1};  ///< Critical threshold (10% free)
    };
    
    explicit DiskSpaceHealthCheck(const DiskConfig& config);
    
    HealthCheckResult check_health() override;
    std::string get_component_name() const override { return "DiskSpace_" + config_.path; }
    std::chrono::milliseconds get_check_interval() const override { return std::chrono::milliseconds(60000); }

private:
    DiskConfig config_;
    std::tuple<size_t, size_t> get_disk_usage(const std::string& path) const;
};

/**
 * @brief Central health monitoring manager
 */
class HealthMonitor {
public:
    /**
     * @brief Health monitor configuration
     */
    struct Config {
        bool enabled{true};
        std::chrono::milliseconds check_interval{5000};
        std::chrono::milliseconds check_timeout{30000};
        bool export_metrics{true};
        std::string metrics_prefix{"omop_health"};
        bool enable_alerts{false};
        std::vector<std::string> alert_endpoints;
    };
    
    /**
     * @brief Constructor
     * @param config Health monitor configuration
     */
    HealthMonitor();
    explicit HealthMonitor(const Config& config);
    
    /**
     * @brief Destructor
     */
    ~HealthMonitor();
    
    /**
     * @brief Start health monitoring
     */
    void start();
    
    /**
     * @brief Stop health monitoring
     */
    void stop();
    
    /**
     * @brief Register health check
     * @param health_check Health check implementation
     */
    void register_health_check(std::shared_ptr<IHealthCheck> health_check);
    
    /**
     * @brief Unregister health check
     * @param component_name Component name to unregister
     */
    void unregister_health_check(const std::string& component_name);
    
    /**
     * @brief Get current health status
     * @return Overall health status
     */
    HealthStatus get_overall_health() const;
    
    /**
     * @brief Get health check results
     * @return Map of component name to health check result
     */
    std::unordered_map<std::string, HealthCheckResult> get_health_results() const;
    
    /**
     * @brief Get health check result for specific component
     * @param component_name Component name
     * @return Optional health check result
     */
    std::optional<HealthCheckResult> get_component_health(const std::string& component_name) const;
    
    /**
     * @brief Perform manual health check for all components
     * @return Map of component name to health check result
     */
    std::unordered_map<std::string, HealthCheckResult> perform_health_check();
    
    /**
     * @brief Set metrics collector for exporting health metrics
     * @param metrics_collector Metrics collector instance
     */
    void set_metrics_collector(std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector);
    
    /**
     * @brief Export health metrics in Prometheus format
     * @return Prometheus formatted metrics
     */
    std::string export_prometheus_metrics() const;
    
    /**
     * @brief Get statistics
     */
    struct Statistics {
        size_t total_checks_performed{0};
        size_t healthy_components{0};
        size_t degraded_components{0};
        size_t unhealthy_components{0};
        size_t unknown_components{0};
        std::chrono::system_clock::time_point last_check_time;
        std::chrono::milliseconds avg_check_duration{0};
    };
    
    Statistics get_statistics() const;

private:
    Config config_;
    mutable std::mutex health_checks_mutex_;
    std::unordered_map<std::string, std::shared_ptr<IHealthCheck>> health_checks_;
    mutable std::mutex results_mutex_;
    std::unordered_map<std::string, HealthCheckResult> last_results_;
    
    // Monitoring thread
    std::atomic<bool> running_{false};
    std::unique_ptr<std::thread> monitor_thread_;
    std::condition_variable monitor_cv_;
    std::mutex monitor_mutex_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    Statistics stats_;
    
    // Metrics integration
    std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector_;
    
    /**
     * @brief Monitor thread function
     */
    void monitor_loop();
    
    /**
     * @brief Perform single health check
     * @param health_check Health check to perform
     * @return Health check result
     */
    HealthCheckResult perform_single_check(std::shared_ptr<IHealthCheck> health_check);
    
    /**
     * @brief Update health metrics
     * @param results Health check results
     */
    void update_health_metrics(const std::unordered_map<std::string, HealthCheckResult>& results);
    
    /**
     * @brief Send health alerts if needed
     * @param results Health check results
     */
    void process_health_alerts(const std::unordered_map<std::string, HealthCheckResult>& results);
    
    /**
     * @brief Convert health status to string
     * @param status Health status
     * @return String representation
     */
    std::string health_status_to_string(HealthStatus status) const;
};

} // namespace omop::common