#include "metrics_collector.h"
#include "logging.h"
#include <mutex>
#include <unordered_map>
#include <vector>
#include <thread>
#include <condition_variable>
#include <random>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <regex>
#include <numeric>
#include <limits>

namespace omop::monitoring {

// Constructor and destructor are defaulted in header

bool MetricsCollector::initialize(const MetricsConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    config_ = config;
    
    if (config_.enabled) {
        start_collection_thread();
    }
    
    auto logger = omop::common::Logger::get("omop-metrics");
    logger->info("MetricsCollector initialised with config: enabled={}, interval={}s, retention={}s",
                config_.enabled, config_.collection_interval.count(), config_.retention_period.count());
    
    return true;
}

bool MetricsCollector::register_metric(const MetricDefinition& definition) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (metrics_.find(definition.name) != metrics_.end()) {
        return false; // Already exists
    }
    
    Metric metric;
    metric.definition = definition;
    metric.last_updated = std::chrono::system_clock::now();
    
    // Initialize histogram data if needed
    if (definition.type == MetricType::Histogram) {
        HistogramData hist_data;
        hist_data.total_count = 0;
        hist_data.sum = 0.0;
        
        for (double bound : definition.histogram_buckets) {
            hist_data.buckets.push_back({bound, 0});
        }
        // Add +Inf bucket
        hist_data.buckets.push_back({std::numeric_limits<double>::infinity(), 0});
        
        metric.histogram_data = hist_data;
    }
    
    // Initialize summary data if needed
    if (definition.type == MetricType::Summary) {
        SummaryData summary_data;
        summary_data.count = 0;
        summary_data.sum = 0.0;
        
        for (double quantile : definition.summary_quantiles) {
            summary_data.quantiles.push_back({quantile, 0.0});
        }
        
        metric.summary_data = summary_data;
    }
    
    metrics_[definition.name] = metric;
    
    auto logger = omop::common::Logger::get("omop-metrics");
    logger->debug("Registered metric: {} (type: {})", definition.name, 
                 metric_type_to_string(definition.type));
    
    return true;
}

bool MetricsCollector::increment_counter(const std::string& name, double value, 
                                      const std::unordered_map<std::string, std::string>& labels) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = metrics_.find(name);
    if (it == metrics_.end() || it->second.definition.type != MetricType::Counter) {
        return false;
    }
    
    MetricValue metric_value;
    metric_value.value = value;
    metric_value.timestamp = std::chrono::system_clock::now();
    metric_value.labels = labels;
    
    it->second.values.push_back(metric_value);
    it->second.last_updated = metric_value.timestamp;
    
    // Cleanup old values if retention period is exceeded
    cleanup_old_values(it->second);
    
    return true;
}

bool MetricsCollector::set_gauge(const std::string& name, double value, 
                               const std::unordered_map<std::string, std::string>& labels) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = metrics_.find(name);
    if (it == metrics_.end() || it->second.definition.type != MetricType::Gauge) {
        return false;
    }
    
    MetricValue metric_value;
    metric_value.value = value;
    metric_value.timestamp = std::chrono::system_clock::now();
    metric_value.labels = labels;
    
    // For gauges, replace existing value with same labels
    auto& values = it->second.values;
    auto existing = std::find_if(values.begin(), values.end(),
        [&labels](const MetricValue& mv) {
            return mv.labels == labels;
        });
    
    if (existing != values.end()) {
        *existing = metric_value;
    } else {
        values.push_back(metric_value);
    }
    
    it->second.last_updated = metric_value.timestamp;
    
    // Cleanup old values if retention period is exceeded
    cleanup_old_values(it->second);
    
    return true;
}

bool MetricsCollector::observe_histogram(const std::string& name, double value, 
                                       const std::unordered_map<std::string, std::string>& labels) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = metrics_.find(name);
    if (it == metrics_.end() || it->second.definition.type != MetricType::Histogram) {
        return false;
    }
    
    // Safety check: ensure histogram data exists
    if (!it->second.histogram_data.has_value()) {
        return false;
    }
    
    // Update histogram data
    it->second.histogram_data->total_count++;
    it->second.histogram_data->sum += value;
    
    // Update buckets
    bool bucket_updated = false;
    for (size_t i = 0; i < it->second.histogram_data->buckets.size(); ++i) {
        auto& bucket = it->second.histogram_data->buckets[i];
        if (value <= bucket.upper_bound) {
            bucket.count++;
            bucket_updated = true;
            break;
        }
    }
    
    // If no bucket was updated, this means the value is greater than all bucket bounds
    // In this case, it should go to the +Inf bucket (last bucket)
    if (!bucket_updated && !it->second.histogram_data->buckets.empty()) {
        auto& last_bucket = it->second.histogram_data->buckets.back();
        last_bucket.count++;
    }
    
    // Add value to values list for retention
    MetricValue metric_value;
    metric_value.value = value;
    metric_value.timestamp = std::chrono::system_clock::now();
    metric_value.labels = labels;
    
    it->second.values.push_back(metric_value);
    it->second.last_updated = metric_value.timestamp;
    
    // Cleanup old values if retention period is exceeded
    cleanup_old_values(it->second);
    
    return true;
}

// Private version of observe_histogram that doesn't acquire the mutex
// Used internally to avoid deadlock when called from methods that already hold the mutex
bool MetricsCollector::observe_histogram_internal(const std::string& name, double value,
                                                const std::unordered_map<std::string, std::string>& labels) {
    auto it = metrics_.find(name);
    if (it == metrics_.end() || it->second.definition.type != MetricType::Histogram) {
        return false;
    }
    
    // Safety check: ensure histogram data exists
    if (!it->second.histogram_data.has_value()) {
        return false;
    }
    
    // Update histogram data
    it->second.histogram_data->total_count++;
    it->second.histogram_data->sum += value;
    
    // Update buckets - ensure we don't hang by using a more robust approach
    bool bucket_updated = false;
    for (size_t i = 0; i < it->second.histogram_data->buckets.size(); ++i) {
        auto& bucket = it->second.histogram_data->buckets[i];
        if (value <= bucket.upper_bound) {
            bucket.count++;
            bucket_updated = true;
            break;
        }
    }
    
    // If no bucket was updated, this means the value is greater than all bucket bounds
    // In this case, it should go to the +Inf bucket (last bucket)
    if (!bucket_updated && !it->second.histogram_data->buckets.empty()) {
        auto& last_bucket = it->second.histogram_data->buckets.back();
        last_bucket.count++;
    }
    
    // Add value to values list for retention
    MetricValue metric_value;
    metric_value.value = value;
    metric_value.timestamp = std::chrono::system_clock::now();
    metric_value.labels = labels;
    
    it->second.values.push_back(metric_value);
    it->second.last_updated = metric_value.timestamp;
    
    // Cleanup old values if retention period is exceeded
    cleanup_old_values(it->second);
    
    return true;
}

bool MetricsCollector::observe_summary(const std::string& name, double value, 
                                     const std::unordered_map<std::string, std::string>& labels) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = metrics_.find(name);
    if (it == metrics_.end() || it->second.definition.type != MetricType::Summary) {
        return false;
    }
    
    // Update summary data
    it->second.summary_data->count++;
    it->second.summary_data->sum += value;
    
    // Store value for quantile calculation
    summary_values_[name].push_back(value);
    
    // Calculate quantiles if we have enough data
    if (it->second.summary_data->count % 100 == 0) {
        calculate_quantiles(name, *it->second.summary_data);
    }
    
    // Add value to values list for retention
    MetricValue metric_value;
    metric_value.value = value;
    metric_value.timestamp = std::chrono::system_clock::now();
    metric_value.labels = labels;
    
    it->second.values.push_back(metric_value);
    it->second.last_updated = metric_value.timestamp;
    
    // Cleanup old values if retention period is exceeded
    cleanup_old_values(it->second);
    
    return true;
}

std::string MetricsCollector::start_timer(const std::string& name, 
                                        const std::unordered_map<std::string, std::string>& labels) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Validate that the metric exists and is of the correct type
    auto metric_it = metrics_.find(name);
    if (metric_it == metrics_.end()) {
        // Return empty string to indicate failure
        return "";
    }
    
    // Check if the metric is a histogram (timers use histogram data)
    if (metric_it->second.definition.type != MetricType::Histogram) {
        // Return empty string to indicate failure
        return "";
    }
    
    std::string timer_id = "timer_" + std::to_string(next_timer_id_++);
    
    TimerInfo timer_info;
    timer_info.metric_name = name;
    timer_info.labels = labels;
    timer_info.start_time = std::chrono::high_resolution_clock::now();
    
    active_timers_[timer_id] = timer_info;
    
    return timer_id;
}

std::optional<double> MetricsCollector::stop_timer(const std::string& timer_id) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = active_timers_.find(timer_id);
    if (it == active_timers_.end()) {
        return std::nullopt;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double>(end_time - it->second.start_time);
    
    // Record the duration as a histogram observation
    // Use the private version that doesn't acquire the mutex to avoid deadlock
    observe_histogram_internal(it->second.metric_name, duration.count(), it->second.labels);
    
    // Remove the timer
    active_timers_.erase(it);
    
    return duration.count();
}

std::optional<Metric> MetricsCollector::get_metric(const std::string& name) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = metrics_.find(name);
    if (it == metrics_.end()) {
        return std::nullopt;
    }
    
    return it->second;
}

std::vector<Metric> MetricsCollector::query_metrics(const MetricsQuery& query) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::vector<Metric> results;
    
    for (const auto& [name, metric] : metrics_) {
        // Check if metric matches query criteria
        if (!query.metric_names.empty()) {
            if (std::find(query.metric_names.begin(), query.metric_names.end(), name) == query.metric_names.end()) {
                continue;
            }
        }
        
        // Check metric type filter
        if (!query.metric_types.empty()) {
            if (std::find(query.metric_types.begin(), query.metric_types.end(), metric.definition.type) == query.metric_types.end()) {
                continue;
            }
        } else if (query.type != MetricType::Counter && 
                   (query.metric_names.empty() && query.name_pattern.empty() && 
                    !query.start_time.has_value() && !query.end_time.has_value() && 
                    query.label_filters.empty() && query.limit == 100 && query.offset == 0)) {
            // Only apply type filter if it's explicitly set to something other than default AND no other criteria are set
            if (metric.definition.type != query.type) {
                continue;
            }
        }
        
        // Check label filters
        if (!query.label_filters.empty()) {
            bool match_labels = true;
            for (const auto& value : metric.values) {
                bool value_matches = true;
                for (const auto& [filter_key, filter_value] : query.label_filters) {
                    auto label_it = value.labels.find(filter_key);
                    if (label_it == value.labels.end() || label_it->second != filter_value) {
                        value_matches = false;
                        break;
                    }
                }
                if (!value_matches) {
                    match_labels = false;
                    break;
                }
            }
            if (!match_labels) {
                continue;
            }
        }
        
        // Check time range
        if (query.start_time.has_value() || query.end_time.has_value()) {
            auto last_updated = metric.last_updated;
            if (query.start_time.has_value() && last_updated < query.start_time.value()) {
                continue;
            }
            if (query.end_time.has_value() && last_updated > query.end_time.value()) {
                continue;
            }
        }
        
        results.push_back(metric);
    }
    
    // Apply limit and offset
    if (query.offset < results.size()) {
        results.erase(results.begin(), results.begin() + query.offset);
    } else {
        results.clear();
    }
    
    if (query.limit < results.size()) {
        results.resize(query.limit);
    }
    
    return results;
}

std::vector<std::string> MetricsCollector::get_metric_names() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::vector<std::string> names;
    names.reserve(metrics_.size());
    
    for (const auto& [name, metric] : metrics_) {
        names.push_back(name);
    }
    
    return names;
}

std::string MetricsCollector::export_metrics(const std::string& format) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (format == "prometheus") {
        return export_prometheus_format();
    } else if (format == "json") {
        return export_json_format();
    } else {
        return "Unsupported format: " + format;
    }
}

bool MetricsCollector::clear_metrics(const std::string& name) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (name.empty()) {
        // Clear all metrics
        metrics_.clear();
        active_timers_.clear();
        summary_values_.clear();
        return true;
    } else {
        // Clear specific metric
        auto it = metrics_.find(name);
        if (it == metrics_.end()) {
            return false;
        }
        
        metrics_.erase(it);
        summary_values_.erase(name);
        
        // Remove any active timers for this metric
        for (auto it = active_timers_.begin(); it != active_timers_.end();) {
            if (it->second.metric_name == name) {
                it = active_timers_.erase(it);
            } else {
                ++it;
            }
        }
        
        return true;
    }
}

size_t MetricsCollector::delete_old_data(const std::chrono::system_clock::time_point& older_than) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    size_t total_deleted = 0;
    
    for (auto& [name, metric] : metrics_) {
        size_t original_size = metric.values.size();
        
        metric.values.erase(
            std::remove_if(metric.values.begin(), metric.values.end(),
                          [&older_than](const MetricValue& value) {
                              return value.timestamp < older_than;
                          }),
            metric.values.end()
        );
        
        total_deleted += original_size - metric.values.size();
    }
    
    return total_deleted;
}

size_t MetricsCollector::delete_old_data_internal(const std::chrono::system_clock::time_point& older_than) {
    // Private version that doesn't acquire the mutex - assumes caller already holds it
    size_t total_deleted = 0;
    
    for (auto& [name, metric] : metrics_) {
        size_t original_size = metric.values.size();
        
        metric.values.erase(
            std::remove_if(metric.values.begin(), metric.values.end(),
                          [&older_than](const MetricValue& value) {
                              return value.timestamp < older_than;
                          }),
            metric.values.end()
        );
        
        total_deleted += original_size - metric.values.size();
    }
    
    return total_deleted;
}

std::unordered_map<std::string, std::any> MetricsCollector::get_statistics() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::unordered_map<std::string, std::any> stats;
    
    stats["total_metrics"] = metrics_.size();
    stats["active_timers"] = active_timers_.size();
    stats["config_enabled"] = config_.enabled;
    stats["collection_interval_seconds"] = static_cast<int64_t>(config_.collection_interval.count());
    stats["retention_period_seconds"] = static_cast<int64_t>(config_.retention_period.count());
    
    size_t total_values = std::accumulate(metrics_.begin(), metrics_.end(), size_t(0),
                                          [](size_t sum, const auto& pair) {
                                              return sum + pair.second.values.size();
                                          });
    stats["total_metric_values"] = total_values;
    
    return stats;
}

MetricsConfig MetricsCollector::get_config() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return config_;
}

bool MetricsCollector::update_config(const MetricsConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    bool was_enabled = config_.enabled;
    config_ = config;
    
    if (config_.enabled && !was_enabled) {
        start_collection_thread();
    } else if (!config_.enabled && was_enabled) {
        stop_collection_thread();
    }
    
    return true;
}

// Private helper methods
void MetricsCollector::cleanup_old_values(Metric& metric) {
    if (config_.retention_period.count() <= 0) {
        return; // No retention limit
    }
    
    auto cutoff_time = std::chrono::system_clock::now() - config_.retention_period;
    
    metric.values.erase(
        std::remove_if(metric.values.begin(), metric.values.end(),
                      [&cutoff_time](const MetricValue& value) {
                          return value.timestamp < cutoff_time;
                      }),
        metric.values.end()
    );
}

void MetricsCollector::calculate_quantiles(const std::string& name, SummaryData& summary_data) {
    auto& values = summary_values_[name];
    if (values.empty()) {
        return;
    }
    
    std::sort(values.begin(), values.end());
    
    for (auto& quantile : summary_data.quantiles) {
        size_t index = static_cast<size_t>(quantile.quantile * (values.size() - 1));
        if (index < values.size()) {
            quantile.value = values[index];
        }
    }
}

void MetricsCollector::start_collection_thread() {
    if (collection_thread_running_) {
        return;
    }
    
    collection_thread_running_ = true;
    collection_thread_ = std::thread([this]() {
        while (collection_thread_running_) {
            {
                std::unique_lock<std::mutex> lock(mutex_);
                collection_cv_.wait_for(lock, config_.collection_interval, [this]() {
                    return !collection_thread_running_;
                });
            }
            
            if (!collection_thread_running_) {
                break;
            }
            
            // Perform periodic cleanup
            auto cutoff_time = std::chrono::system_clock::now() - config_.retention_period;
            delete_old_data_internal(cutoff_time);
            
            // Recalculate quantiles for summary metrics
            for (auto& [name, metric] : metrics_) {
                if (metric.definition.type == MetricType::Summary && metric.summary_data.has_value()) {
                    calculate_quantiles(name, *metric.summary_data);
                }
            }
        }
    });
}

void MetricsCollector::stop_collection_thread() {
    if (!collection_thread_running_) {
        return;
    }
    
    collection_thread_running_ = false;
    collection_cv_.notify_all();
    
    if (collection_thread_.joinable()) {
        collection_thread_.join();
    }
}

std::string MetricsCollector::export_prometheus_format() {
    std::ostringstream oss;
    
    for (const auto& [name, metric] : metrics_) {
        const auto& definition = metric.definition;
        
        // Add metric help and type comments
        if (!definition.description.empty()) {
            oss << "# HELP " << name << " " << definition.description << "\n";
        } else {
            oss << "# HELP " << name << " " << name << "\n";
        }
        oss << "# TYPE " << name << " " << metric_type_to_string(definition.type) << "\n";
        
        // Export values
        for (const auto& value : metric.values) {
            oss << name;
            
            // Add labels
            if (!value.labels.empty()) {
                oss << "{";
                bool first = true;
                for (const auto& [label_name, label_value] : value.labels) {
                    if (!first) oss << ",";
                    oss << label_name << "=\"" << label_value << "\"";
                    first = false;
                }
                oss << "}";
            }
            
            oss << " " << std::fixed << std::setprecision(6) << value.value << "\n";
        }
        
        // Export histogram buckets if applicable
        if (definition.type == MetricType::Histogram && metric.histogram_data.has_value()) {
            const auto& hist_data = *metric.histogram_data;
            
            for (const auto& bucket : hist_data.buckets) {
                oss << name << "_bucket{le=\"" << bucket.upper_bound << "\"} " << bucket.count << "\n";
            }
            oss << name << "_sum " << hist_data.sum << "\n";
            oss << name << "_count " << hist_data.total_count << "\n";
        }
        
        // Export summary quantiles if applicable
        if (definition.type == MetricType::Summary && metric.summary_data.has_value()) {
            const auto& summary_data = *metric.summary_data;
            
            for (const auto& quantile : summary_data.quantiles) {
                oss << name << "{quantile=\"" << quantile.quantile << "\"} " << quantile.value << "\n";
            }
            oss << name << "_sum " << summary_data.sum << "\n";
            oss << name << "_count " << summary_data.count << "\n";
        }
    }
    
    return oss.str();
}

std::string MetricsCollector::export_json_format() {
    std::ostringstream oss;
    oss << "{\n";
    oss << "  \"metrics\": [\n";
    
    bool first_metric = true;
    for (const auto& [name, metric] : metrics_) {
        if (!first_metric) oss << ",\n";
        first_metric = false;
        
        oss << "    {\n";
        oss << "      \"name\": \"" << name << "\",\n";
        oss << "      \"type\": \"" << metric_type_to_string(metric.definition.type) << "\",\n";
        oss << "      \"description\": \"" << metric.definition.description << "\",\n";
        oss << "      \"last_updated\": \"" << std::chrono::duration_cast<std::chrono::seconds>(
            metric.last_updated.time_since_epoch()).count() << "\",\n";
        oss << "      \"values\": [\n";
        
        bool first_value = true;
        for (const auto& value : metric.values) {
            if (!first_value) oss << ",\n";
            first_value = false;
            
            oss << "        {\n";
            oss << "          \"value\": " << value.value << ",\n";
            oss << "          \"timestamp\": \"" << std::chrono::duration_cast<std::chrono::seconds>(
                value.timestamp.time_since_epoch()).count() << "\",\n";
            oss << "          \"labels\": {\n";
            
            bool first_label = true;
            for (const auto& [label_name, label_value] : value.labels) {
                if (!first_label) oss << ",\n";
                first_label = false;
                oss << "            \"" << label_name << "\": \"" << label_value << "\"";
            }
            oss << "\n          }\n";
            oss << "        }";
        }
        oss << "\n      ]\n";
        oss << "    }";
    }
    
    oss << "\n  ]\n";
    oss << "}\n";
    
    return oss.str();
}

// Factory functions
std::unique_ptr<IMetricsCollector> create_metrics_collector() {
    return std::make_unique<MetricsCollector>();
}

MetricsConfig get_default_metrics_config() {
    MetricsConfig config;
    config.enabled = true;
    config.collection_interval = std::chrono::seconds(30);
    config.retention_period = std::chrono::seconds(86400);
    config.max_metrics = 10000;
    config.max_values_per_metric = 1000;
    config.export_format = "prometheus";
    config.export_endpoint = "/metrics";
    config.storage_backend = "memory";
    config.enable_compression = false;
    return config;
}

std::string metric_type_to_string(MetricType type) {
    switch (type) {
        case MetricType::Counter: return "counter";
        case MetricType::Gauge: return "gauge";
        case MetricType::Histogram: return "histogram";
        case MetricType::Summary: return "summary";
        case MetricType::Timer: return "timer";
        default: return "unknown";
    }
}

MetricType string_to_metric_type(const std::string& type_str) {
    if (type_str == "counter") return MetricType::Counter;
    if (type_str == "gauge") return MetricType::Gauge;
    if (type_str == "histogram") return MetricType::Histogram;
    if (type_str == "summary") return MetricType::Summary;
    if (type_str == "timer") return MetricType::Timer;
    return MetricType::Counter;
}

bool create_standard_etl_metrics(IMetricsCollector& collector) {
    // Create standard ETL metrics
    std::vector<MetricDefinition> definitions = {
        {"etl_records_processed_total", "Total number of records processed", MetricType::Counter, {}, {}, {}, {}},
        {"etl_processing_duration_seconds", "Time taken to process records", MetricType::Histogram, {}, {}, {}, {}},
        {"etl_active_connections", "Number of active database connections", MetricType::Gauge, {}, {}, {}, {}},
        {"etl_queue_size", "Size of processing queue", MetricType::Gauge, {}, {}, {}, {}},
        {"etl_errors_total", "Total number of errors encountered", MetricType::Counter, {}, {}, {}, {}},
        {"etl_memory_usage_bytes", "Memory usage during processing in bytes", MetricType::Gauge, {}, {}, {}, {}}
    };
    
    for (const auto& def : definitions) {
        if (!collector.register_metric(def)) {
            return false;
        }
    }
    
    return true;
}

// Timer implementation
Timer::Timer(IMetricsCollector& collector, const std::string& metric_name,
            const std::unordered_map<std::string, std::string>& labels)
    : collector_(collector), stopped_(false) {
    timer_id_ = collector_.start_timer(metric_name, labels);
}

Timer::~Timer() {
    if (!stopped_) {
        stop();
    }
}

std::optional<double> Timer::stop() {
    if (stopped_) {
        return std::nullopt;
    }
    
    stopped_ = true;
    return collector_.stop_timer(timer_id_);
}

// Factory function implementations
MetricDefinition create_counter_metric(const std::string& name, const std::string& description) {
    MetricDefinition def;
    def.name = name;
    def.description = description;
    def.type = MetricType::Counter;
    return def;
}

// Add similar factory functions for other metric types as needed

} // namespace omop::monitoring