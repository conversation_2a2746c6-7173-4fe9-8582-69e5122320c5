/**
 * @file health_endpoints.cpp
 * @brief Implementation of comprehensive health check endpoints
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#include "health_endpoints.h"
#include "logging.h"
#include "utils/string_utils.h"
#include <nlohmann/json.hpp>
#include <sstream>
#include <iomanip>
#include <thread>
#include <regex>
#include <cstdlib>
#include <sys/stat.h>
#include <unistd.h>

#if defined(__APPLE__)
#include <sys/sysctl.h>
#include <mach/mach.h>
#elif defined(__linux__)
#include <sys/statvfs.h>
#include <fstream>
#endif

using json = nlohmann::json;
using namespace omop::common::string_utils;

namespace omop::common {

// HealthEndpointHandler implementation
HealthEndpointHandler::HealthEndpointHandler(
    std::shared_ptr<HealthMonitor> health_monitor,
    const Config& config)
    : config_(config), health_monitor_(health_monitor) {
}

std::string HealthEndpointHandler::handle_request(
    const std::string& path,
    const std::unordered_map<std::string, std::string>& query_params,
    const std::unordered_map<std::string, std::string>& headers) {
    
    if (!config_.enabled) {
        return generate_response(503, "Health endpoints are disabled", {});
    }
    
    // Check authentication if required
    if (config_.require_authentication && !check_authentication(headers)) {
        return generate_response(401, "Authentication required", {});
    }
    
    // Check rate limits
    std::string client_ip = extract_client_ip(headers);
    if (!check_rate_limits(client_ip)) {
        return generate_response(429, "Rate limit exceeded", {});
    }
    
    // Parse response format
    HealthResponseFormat format = parse_response_format(query_params);
    
    // Generate cache key
    std::string cache_key = path + "_" + to_string(static_cast<int>(format));
    
    // Check cache
    auto cached_response = get_cached_response(cache_key);
    if (cached_response) {
        return *cached_response;
    }
    
    try {
        std::string response_content;
        
        if (path == config_.base_path || path == config_.base_path + "/") {
            // Main health endpoint
            auto detailed_response = generate_detailed_response();
            response_content = format_response(detailed_response, format, config_.pretty_print);
            
        } else if (path == config_.base_path + "/live" || path == config_.base_path + "/liveness") {
            // Kubernetes liveness probe
            json simple_response;
            simple_response["status"] = "alive";
            simple_response["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
            response_content = simple_response.dump(config_.pretty_print ? 2 : -1);
            
        } else if (path == config_.base_path + "/ready" || path == config_.base_path + "/readiness") {
            // Kubernetes readiness probe
            auto health_results = health_monitor_->check_all();
            bool all_healthy = std::all_of(health_results.begin(), health_results.end(),
                [](const auto& result) { return result.status == HealthStatus::HEALTHY; });
            
            json simple_response;
            simple_response["status"] = all_healthy ? "ready" : "not_ready";
            simple_response["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
            response_content = simple_response.dump(config_.pretty_print ? 2 : -1);
            
        } else if (path == config_.base_path + "/startup") {
            // Kubernetes startup probe
            json simple_response;
            simple_response["status"] = "started";
            simple_response["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
            response_content = simple_response.dump(config_.pretty_print ? 2 : -1);
            
        } else {
            return generate_response(404, "Endpoint not found", {});
        }
        
        // Cache the response
        cache_response(cache_key, response_content, format);
        
        // Determine content type
        std::string content_type;
        switch (format) {
            case HealthResponseFormat::JSON:
                content_type = "application/json";
                break;
            case HealthResponseFormat::PROMETHEUS:
                content_type = "text/plain";
                break;
            case HealthResponseFormat::HTML:
                content_type = "text/html";
                break;
            case HealthResponseFormat::PLAIN_TEXT:
            default:
                content_type = "text/plain";
                break;
        }
        
        std::unordered_map<std::string, std::string> response_headers;
        response_headers["Content-Type"] = content_type;
        response_headers["Cache-Control"] = "max-age=" + to_string(config_.cache_duration.count());
        
        return generate_response(200, response_content, response_headers);
        
    } catch (const std::exception& e) {
        OMOP_LOG_ERROR("Health endpoint error: {}", e.what());
        
        json error_response;
        error_response["error"] = "Internal server error";
        if (config_.include_detailed_errors) {
            error_response["details"] = e.what();
        }
        error_response["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        return generate_response(500, error_response.dump(), 
                                {{"Content-Type", "application/json"}});
    }
}

std::vector<std::string> HealthEndpointHandler::get_supported_paths() const {
    return {
        config_.base_path,
        config_.base_path + "/live",
        config_.base_path + "/liveness",
        config_.base_path + "/ready",
        config_.base_path + "/readiness",
        config_.base_path + "/startup"
    };
}

void HealthEndpointHandler::set_metrics_collector(std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector) {
    metrics_collector_ = metrics_collector;
}

void HealthEndpointHandler::register_dependency_check(
    const std::string& name,
    std::function<DetailedHealthResponse::DependencyStatus()> check_function) {
    
    std::lock_guard<std::mutex> lock(dependency_checks_mutex_);
    dependency_checks_[name] = check_function;
}

void HealthEndpointHandler::set_custom_data_provider(
    std::function<std::unordered_map<std::string, std::any>()> provider) {
    
    custom_data_provider_ = provider;
}

void HealthEndpointHandler::set_feature_flag_provider(
    std::function<std::unordered_map<std::string, bool>()> provider) {
    
    feature_flag_provider_ = provider;
}

DetailedHealthResponse HealthEndpointHandler::generate_detailed_response() {
    DetailedHealthResponse response;
    response.check_time = std::chrono::system_clock::now();
    
    auto start_time = std::chrono::steady_clock::now();
    
    // Get basic health check results
    auto health_results = health_monitor_->check_all();
    response.component_results.reserve(health_results.size());
    
    HealthStatus overall_status = HealthStatus::HEALTHY;
    std::vector<std::string> issues;
    
    for (const auto& result : health_results) {
        response.component_results[result.check_name] = result;
        
        if (result.status == HealthStatus::UNHEALTHY) {
            overall_status = HealthStatus::UNHEALTHY;
            issues.push_back(result.check_name + ": " + result.message);
        } else if (result.status == HealthStatus::DEGRADED && overall_status == HealthStatus::HEALTHY) {
            overall_status = HealthStatus::DEGRADED;
            issues.push_back(result.check_name + ": " + result.message);
        }
    }
    
    response.overall_status = overall_status;
    response.overall_message = issues.empty() ? "All systems healthy" : join(issues, "; ");
    
    // System information
    if (config_.include_system_info) {
        response.system_info = get_system_info();
    }
    
    // Dependencies
    if (config_.include_dependencies) {
        response.dependencies = check_dependencies();
    }
    
    // Performance metrics
    if (config_.include_performance_metrics) {
        response.performance_metrics = get_performance_metrics();
    }
    
    // Configuration status
    if (config_.include_configuration_status) {
        response.configuration_status = get_configuration_status();
    }
    
    // Feature flags
    if (feature_flag_provider_) {
        response.feature_flags = feature_flag_provider_();
    }
    
    // Custom data
    if (config_.include_custom_data && custom_data_provider_) {
        response.custom_data = custom_data_provider_();
    }
    
    auto end_time = std::chrono::steady_clock::now();
    response.total_check_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - start_time);
    
    return response;
}

DetailedHealthResponse::SystemInfo HealthEndpointHandler::get_system_info() {
    DetailedHealthResponse::SystemInfo info;
    
    // Hostname
    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        info.hostname = hostname;
    }
    
    // Version (could be from environment or config)
    if (const char* version = std::getenv("APP_VERSION")) {
        info.version = version;
    } else {
        info.version = "unknown";
    }
    
    // Startup time (approximation)
    info.startup_time = std::chrono::system_clock::now() - std::chrono::hours(1);
    info.uptime = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now() - info.startup_time);
    
    // Memory usage
    info.memory_usage_mb = get_memory_usage_mb();
    
    // CPU usage (approximation)
    info.cpu_usage_percent = get_cpu_usage_percent();
    
    // Disk usage
    auto [used, free] = get_disk_usage_mb();
    info.disk_usage_mb = used;
    info.disk_free_mb = free;
    
    // Active connections (approximation)
    info.active_connections = 0;
    
    // Environment info
    if (const char* env = std::getenv("ENVIRONMENT")) {
        info.environment_info["environment"] = env;
    }
    if (const char* region = std::getenv("REGION")) {
        info.environment_info["region"] = region;
    }
    
    return info;
}

std::vector<DetailedHealthResponse::DependencyStatus> HealthEndpointHandler::check_dependencies() {
    std::vector<DetailedHealthResponse::DependencyStatus> dependencies;
    
    std::lock_guard<std::mutex> lock(dependency_checks_mutex_);
    
    for (const auto& [name, check_function] : dependency_checks_) {
        try {
            auto status = check_function();
            dependencies.push_back(status);
        } catch (const std::exception& e) {
            DetailedHealthResponse::DependencyStatus failed_status;
            failed_status.name = name;
            failed_status.status = HealthStatus::UNHEALTHY;
            failed_status.error_message = e.what();
            dependencies.push_back(failed_status);
        }
    }
    
    return dependencies;
}

DetailedHealthResponse::PerformanceMetrics HealthEndpointHandler::get_performance_metrics() {
    DetailedHealthResponse::PerformanceMetrics metrics;
    
    if (metrics_collector_) {
        // Get metrics from collector
        auto stats = metrics_collector_->get_statistics();
        
        metrics.total_requests = stats.metrics_collected;
        // Other metrics would be extracted from the metrics collector
    }
    
    return metrics;
}

DetailedHealthResponse::ConfigurationStatus HealthEndpointHandler::get_configuration_status() {
    DetailedHealthResponse::ConfigurationStatus status;
    
    status.is_valid = true;
    status.last_reload_time = std::chrono::system_clock::now() - std::chrono::hours(1);
    status.config_source = "file";
    
    // Key settings (could be populated from actual config)
    status.key_settings["health_endpoints_enabled"] = config_.enabled ? "true" : "false";
    status.key_settings["authentication_required"] = config_.require_authentication ? "true" : "false";
    status.key_settings["cache_duration"] = to_string(config_.cache_duration.count());
    
    return status;
}

std::string HealthEndpointHandler::format_response(
    const DetailedHealthResponse& response,
    HealthResponseFormat format,
    bool pretty_print) {
    
    switch (format) {
        case HealthResponseFormat::JSON:
            return format_json_response(response, pretty_print);
        case HealthResponseFormat::PROMETHEUS:
            return format_prometheus_response(response);
        case HealthResponseFormat::HTML:
            return format_html_response(response);
        case HealthResponseFormat::PLAIN_TEXT:
        default:
            return format_plain_text_response(response);
    }
}

std::string HealthEndpointHandler::format_json_response(
    const DetailedHealthResponse& response,
    bool pretty_print) {
    
    json j;
    
    // Basic status
    j["status"] = static_cast<int>(response.overall_status);
    j["message"] = response.overall_message;
    j["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
        response.check_time.time_since_epoch()).count();
    j["check_duration_ms"] = response.total_check_duration.count();
    
    // Component results
    if (!response.component_results.empty()) {
        json components;
        for (const auto& [name, result] : response.component_results) {
            json component;
            component["status"] = static_cast<int>(result.status);
            component["message"] = result.message;
            component["duration_ms"] = result.duration.count();
            components[name] = component;
        }
        j["components"] = components;
    }
    
    // System info
    if (response.system_info.hostname != "") {
        json system;
        system["hostname"] = response.system_info.hostname;
        system["version"] = response.system_info.version;
        system["uptime_seconds"] = response.system_info.uptime.count();
        system["memory_usage_mb"] = response.system_info.memory_usage_mb;
        system["cpu_usage_percent"] = response.system_info.cpu_usage_percent;
        system["disk_usage_mb"] = response.system_info.disk_usage_mb;
        system["disk_free_mb"] = response.system_info.disk_free_mb;
        system["active_connections"] = response.system_info.active_connections;
        system["environment"] = response.system_info.environment_info;
        j["system"] = system;
    }
    
    // Dependencies
    if (!response.dependencies.empty()) {
        json dependencies = json::array();
        for (const auto& dep : response.dependencies) {
            json dependency;
            dependency["name"] = dep.name;
            dependency["status"] = static_cast<int>(dep.status);
            dependency["endpoint"] = dep.endpoint;
            dependency["response_time_ms"] = dep.response_time.count();
            dependency["version"] = dep.version;
            if (!dep.error_message.empty()) {
                dependency["error"] = dep.error_message;
            }
            dependencies.push_back(dependency);
        }
        j["dependencies"] = dependencies;
    }
    
    // Performance metrics
    if (response.performance_metrics.total_requests > 0) {
        json performance;
        performance["requests_per_second"] = response.performance_metrics.requests_per_second;
        performance["avg_response_time_ms"] = response.performance_metrics.avg_response_time.count();
        performance["active_requests"] = response.performance_metrics.active_requests;
        performance["total_requests"] = response.performance_metrics.total_requests;
        performance["error_count"] = response.performance_metrics.error_count;
        performance["error_rate"] = response.performance_metrics.error_rate;
        performance["custom_metrics"] = response.performance_metrics.custom_metrics;
        j["performance"] = performance;
    }
    
    // Configuration status
    if (!response.configuration_status.config_source.empty()) {
        json config;
        config["is_valid"] = response.configuration_status.is_valid;
        config["validation_errors"] = response.configuration_status.validation_errors;
        config["validation_warnings"] = response.configuration_status.validation_warnings;
        config["last_reload"] = std::chrono::duration_cast<std::chrono::seconds>(
            response.configuration_status.last_reload_time.time_since_epoch()).count();
        config["source"] = response.configuration_status.config_source;
        config["key_settings"] = response.configuration_status.key_settings;
        j["configuration"] = config;
    }
    
    // Feature flags
    if (!response.feature_flags.empty()) {
        j["feature_flags"] = response.feature_flags;
    }
    
    return j.dump(pretty_print ? 2 : -1);
}

std::string HealthEndpointHandler::format_prometheus_response(const DetailedHealthResponse& response) {
    std::stringstream ss;
    
    // Overall health status
    ss << "# HELP health_status Overall system health status (0=unhealthy, 1=degraded, 2=healthy)\n";
    ss << "# TYPE health_status gauge\n";
    ss << "health_status " << static_cast<int>(response.overall_status) << "\n";
    
    // Check duration
    ss << "# HELP health_check_duration_ms Duration of health check in milliseconds\n";
    ss << "# TYPE health_check_duration_ms gauge\n";
    ss << "health_check_duration_ms " << response.total_check_duration.count() << "\n";
    
    // Component statuses
    for (const auto& [name, result] : response.component_results) {
        ss << "# HELP health_component_status{component=\"" << name << "\"} Component health status\n";
        ss << "# TYPE health_component_status gauge\n";
        ss << "health_component_status{component=\"" << name << "\"} " << static_cast<int>(result.status) << "\n";
        
        ss << "# HELP health_component_duration_ms{component=\"" << name << "\"} Component check duration\n";
        ss << "# TYPE health_component_duration_ms gauge\n";
        ss << "health_component_duration_ms{component=\"" << name << "\"} " << result.duration.count() << "\n";
    }
    
    // System metrics
    if (response.system_info.memory_usage_mb > 0) {
        ss << "# HELP system_memory_usage_mb Memory usage in megabytes\n";
        ss << "# TYPE system_memory_usage_mb gauge\n";
        ss << "system_memory_usage_mb " << response.system_info.memory_usage_mb << "\n";
        
        ss << "# HELP system_cpu_usage_percent CPU usage percentage\n";
        ss << "# TYPE system_cpu_usage_percent gauge\n";
        ss << "system_cpu_usage_percent " << response.system_info.cpu_usage_percent << "\n";
        
        ss << "# HELP system_disk_usage_mb Disk usage in megabytes\n";
        ss << "# TYPE system_disk_usage_mb gauge\n";
        ss << "system_disk_usage_mb " << response.system_info.disk_usage_mb << "\n";
    }
    
    // Performance metrics
    if (response.performance_metrics.total_requests > 0) {
        ss << "# HELP requests_per_second Current requests per second\n";
        ss << "# TYPE requests_per_second gauge\n";
        ss << "requests_per_second " << response.performance_metrics.requests_per_second << "\n";
        
        ss << "# HELP total_requests Total number of requests processed\n";
        ss << "# TYPE total_requests counter\n";
        ss << "total_requests " << response.performance_metrics.total_requests << "\n";
        
        ss << "# HELP error_rate Current error rate\n";
        ss << "# TYPE error_rate gauge\n";
        ss << "error_rate " << response.performance_metrics.error_rate << "\n";
    }
    
    return ss.str();
}

std::string HealthEndpointHandler::format_plain_text_response(const DetailedHealthResponse& response) {
    std::stringstream ss;
    
    ss << "Health Status: ";
    switch (response.overall_status) {
        case HealthStatus::HEALTHY: ss << "HEALTHY"; break;
        case HealthStatus::DEGRADED: ss << "DEGRADED"; break;
        case HealthStatus::UNHEALTHY: ss << "UNHEALTHY"; break;
        default: ss << "UNKNOWN"; break;
    }
    ss << "\n";
    
    if (!response.overall_message.empty()) {
        ss << "Message: " << response.overall_message << "\n";
    }
    
    ss << "Check Duration: " << response.total_check_duration.count() << "ms\n";
    ss << "Timestamp: " << std::chrono::duration_cast<std::chrono::seconds>(
        response.check_time.time_since_epoch()).count() << "\n\n";
    
    // Components
    if (!response.component_results.empty()) {
        ss << "Components:\n";
        for (const auto& [name, result] : response.component_results) {
            ss << "  " << name << ": ";
            switch (result.status) {
                case HealthStatus::HEALTHY: ss << "HEALTHY"; break;
                case HealthStatus::DEGRADED: ss << "DEGRADED"; break;
                case HealthStatus::UNHEALTHY: ss << "UNHEALTHY"; break;
                default: ss << "UNKNOWN"; break;
            }
            if (!result.message.empty()) {
                ss << " - " << result.message;
            }
            ss << " (" << result.duration.count() << "ms)\n";
        }
        ss << "\n";
    }
    
    // System info
    if (!response.system_info.hostname.empty()) {
        ss << "System Information:\n";
        ss << "  Hostname: " << response.system_info.hostname << "\n";
        ss << "  Version: " << response.system_info.version << "\n";
        ss << "  Uptime: " << response.system_info.uptime.count() << "s\n";
        if (response.system_info.memory_usage_mb > 0) {
            ss << "  Memory: " << response.system_info.memory_usage_mb << "MB\n";
            ss << "  CPU: " << response.system_info.cpu_usage_percent << "%\n";
            ss << "  Disk: " << response.system_info.disk_usage_mb << "MB used, " 
               << response.system_info.disk_free_mb << "MB free\n";
        }
        ss << "\n";
    }
    
    return ss.str();
}

std::string HealthEndpointHandler::format_html_response(const DetailedHealthResponse& response) {
    std::stringstream ss;
    
    ss << "<!DOCTYPE html>\n<html>\n<head>\n";
    ss << "<title>Health Check</title>\n";
    ss << "<meta charset=\"utf-8\">\n";
    ss << "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n";
    ss << "<style>\n";
    ss << "body { font-family: Arial, sans-serif; margin: 20px; }\n";
    ss << ".status { padding: 10px; margin: 10px 0; border-radius: 5px; }\n";
    ss << ".healthy { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }\n";
    ss << ".degraded { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }\n";
    ss << ".unhealthy { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }\n";
    ss << "table { border-collapse: collapse; width: 100%; margin: 10px 0; }\n";
    ss << "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n";
    ss << "th { background-color: #f2f2f2; }\n";
    ss << "</style>\n";
    ss << "</head>\n<body>\n";
    
    ss << "<h1>System Health Status</h1>\n";
    
    // Overall status
    std::string status_class = (response.overall_status == HealthStatus::HEALTHY) ? "healthy" :
                              (response.overall_status == HealthStatus::DEGRADED) ? "degraded" : "unhealthy";
    
    ss << "<div class=\"status " << status_class << "\">\n";
    ss << "<h2>Overall Status: ";
    switch (response.overall_status) {
        case HealthStatus::HEALTHY: ss << "HEALTHY"; break;
        case HealthStatus::DEGRADED: ss << "DEGRADED"; break;
        case HealthStatus::UNHEALTHY: ss << "UNHEALTHY"; break;
        default: ss << "UNKNOWN"; break;
    }
    ss << "</h2>\n";
    if (!response.overall_message.empty()) {
        ss << "<p>" << response.overall_message << "</p>\n";
    }
    ss << "<p>Check completed in " << response.total_check_duration.count() << "ms</p>\n";
    ss << "</div>\n";
    
    // Components table
    if (!response.component_results.empty()) {
        ss << "<h3>Component Health</h3>\n";
        ss << "<table>\n";
        ss << "<tr><th>Component</th><th>Status</th><th>Message</th><th>Duration</th></tr>\n";
        
        for (const auto& [name, result] : response.component_results) {
            ss << "<tr>\n";
            ss << "<td>" << name << "</td>\n";
            ss << "<td>";
            switch (result.status) {
                case HealthStatus::HEALTHY: ss << "✅ HEALTHY"; break;
                case HealthStatus::DEGRADED: ss << "⚠️ DEGRADED"; break;
                case HealthStatus::UNHEALTHY: ss << "❌ UNHEALTHY"; break;
                default: ss << "❓ UNKNOWN"; break;
            }
            ss << "</td>\n";
            ss << "<td>" << result.message << "</td>\n";
            ss << "<td>" << result.duration.count() << "ms</td>\n";
            ss << "</tr>\n";
        }
        ss << "</table>\n";
    }
    
    // System information
    if (!response.system_info.hostname.empty()) {
        ss << "<h3>System Information</h3>\n";
        ss << "<table>\n";
        ss << "<tr><th>Property</th><th>Value</th></tr>\n";
        ss << "<tr><td>Hostname</td><td>" << response.system_info.hostname << "</td></tr>\n";
        ss << "<tr><td>Version</td><td>" << response.system_info.version << "</td></tr>\n";
        ss << "<tr><td>Uptime</td><td>" << response.system_info.uptime.count() << " seconds</td></tr>\n";
        if (response.system_info.memory_usage_mb > 0) {
            ss << "<tr><td>Memory Usage</td><td>" << response.system_info.memory_usage_mb << " MB</td></tr>\n";
            ss << "<tr><td>CPU Usage</td><td>" << response.system_info.cpu_usage_percent << "%</td></tr>\n";
            ss << "<tr><td>Disk Usage</td><td>" << response.system_info.disk_usage_mb << " MB used, " 
               << response.system_info.disk_free_mb << " MB free</td></tr>\n";
        }
        ss << "</table>\n";
    }
    
    ss << "<hr>\n";
    ss << "<p><small>Generated at " << std::chrono::duration_cast<std::chrono::seconds>(
        response.check_time.time_since_epoch()).count() << "</small></p>\n";
    ss << "</body>\n</html>\n";
    
    return ss.str();
}

bool HealthEndpointHandler::check_authentication(const std::unordered_map<std::string, std::string>& headers) {
    if (config_.allowed_api_keys.empty()) {
        return true; // No API keys configured
    }
    
    auto it = headers.find(config_.api_key_header);
    if (it == headers.end()) {
        return false;
    }
    
    const std::string& provided_key = it->second;
    return std::find(config_.allowed_api_keys.begin(), config_.allowed_api_keys.end(), provided_key) != 
           config_.allowed_api_keys.end();
}

bool HealthEndpointHandler::check_rate_limits(const std::string& client_ip) {
    if (config_.max_requests_per_minute == 0) {
        return true; // No rate limiting
    }
    
    std::lock_guard<std::mutex> lock(rate_limit_mutex_);
    auto& state = rate_limit_states_[client_ip];
    
    auto now = std::chrono::system_clock::now();
    
    // Clean old requests
    while (!state.request_times.empty() && 
           (now - state.request_times.front()) > config_.rate_limit_window) {
        state.request_times.pop();
    }
    
    // Check limit
    if (state.request_times.size() >= config_.max_requests_per_minute) {
        return false;
    }
    
    // Add current request
    state.request_times.push(now);
    return true;
}

std::optional<std::string> HealthEndpointHandler::get_cached_response(const std::string& cache_key) {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    auto it = response_cache_.find(cache_key);
    if (it == response_cache_.end()) {
        return std::nullopt;
    }
    
    auto& cached = it->second;
    auto now = std::chrono::system_clock::now();
    
    if ((now - cached.created_time) > config_.cache_duration) {
        response_cache_.erase(it);
        return std::nullopt;
    }
    
    return cached.content;
}

void HealthEndpointHandler::cache_response(const std::string& cache_key, 
                                          const std::string& response, 
                                          HealthResponseFormat format) {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    
    CachedResponse cached;
    cached.content = response;
    cached.created_time = std::chrono::system_clock::now();
    cached.format = format;
    
    response_cache_[cache_key] = cached;
    
    // Cleanup old entries
    cleanup_cache();
}

void HealthEndpointHandler::cleanup_cache() {
    auto now = std::chrono::system_clock::now();
    
    for (auto it = response_cache_.begin(); it != response_cache_.end();) {
        if ((now - it->second.created_time) > config_.cache_duration) {
            it = response_cache_.erase(it);
        } else {
            ++it;
        }
    }
}

std::string HealthEndpointHandler::extract_client_ip(const std::unordered_map<std::string, std::string>& headers) {
    // Try various headers for client IP
    std::vector<std::string> ip_headers = {
        "X-Forwarded-For",
        "X-Real-IP",
        "X-Client-IP",
        "CF-Connecting-IP"
    };
    
    for (const auto& header : ip_headers) {
        auto it = headers.find(header);
        if (it != headers.end() && !it->second.empty()) {
            // Extract first IP from comma-separated list
            std::string ip = it->second;
            auto comma_pos = ip.find(',');
            if (comma_pos != std::string::npos) {
                ip = ip.substr(0, comma_pos);
            }
            return trim(ip);
        }
    }
    
    return "unknown";
}

HealthResponseFormat HealthEndpointHandler::parse_response_format(
    const std::unordered_map<std::string, std::string>& query_params) {
    
    auto it = query_params.find("format");
    if (it == query_params.end()) {
        return config_.default_format;
    }
    
    std::string format = to_lower(it->second);
    
    if (format == "json") return HealthResponseFormat::JSON;
    if (format == "prometheus") return HealthResponseFormat::PROMETHEUS;
    if (format == "html") return HealthResponseFormat::HTML;
    if (format == "text" || format == "plain") return HealthResponseFormat::PLAIN_TEXT;
    
    return config_.default_format;
}

std::string HealthEndpointHandler::generate_response(int status_code, 
                                                    const std::string& content,
                                                    const std::unordered_map<std::string, std::string>& headers) {
    std::stringstream ss;
    ss << "HTTP/1.1 " << status_code << " ";
    
    switch (status_code) {
        case 200: ss << "OK"; break;
        case 401: ss << "Unauthorized"; break;
        case 404: ss << "Not Found"; break;
        case 429: ss << "Too Many Requests"; break;
        case 500: ss << "Internal Server Error"; break;
        case 503: ss << "Service Unavailable"; break;
        default: ss << "Unknown"; break;
    }
    
    ss << "\r\n";
    
    for (const auto& [key, value] : headers) {
        ss << key << ": " << value << "\r\n";
    }
    
    ss << "Content-Length: " << content.length() << "\r\n";
    ss << "Connection: close\r\n";
    ss << "\r\n";
    ss << content;
    
    return ss.str();
}

// Helper functions for system metrics
size_t HealthEndpointHandler::get_memory_usage_mb() {
#if defined(__APPLE__)
    mach_task_basic_info info;
    mach_msg_type_number_t info_count = MACH_TASK_BASIC_INFO_COUNT;
    
    if (task_info(mach_task_self(), MACH_TASK_BASIC_INFO, 
                  (task_info_t)&info, &info_count) == KERN_SUCCESS) {
        return info.resident_size / (1024 * 1024);
    }
#elif defined(__linux__)
    std::ifstream statm("/proc/self/statm");
    if (statm) {
        size_t size, resident;
        statm >> size >> resident;
        return (resident * getpagesize()) / (1024 * 1024);
    }
#endif
    return 0;
}

double HealthEndpointHandler::get_cpu_usage_percent() {
    // This is a simplified implementation
    // In practice, you'd want to track CPU time over intervals
    return 0.0;
}

std::pair<size_t, size_t> HealthEndpointHandler::get_disk_usage_mb() {
#if defined(__linux__)
    struct statvfs stat;
    if (statvfs(".", &stat) == 0) {
        size_t total = (stat.f_blocks * stat.f_frsize) / (1024 * 1024);
        size_t free = (stat.f_avail * stat.f_frsize) / (1024 * 1024);
        return {total - free, free};
    }
#endif
    return {0, 0};
}

// HealthEndpointServer implementation
HealthEndpointServer::HealthEndpointServer(
    std::shared_ptr<IHealthEndpointHandler> handler,
    const Config& config)
    : config_(config), handler_(handler) {
    
    stats_.start_time = std::chrono::system_clock::now();
}

HealthEndpointServer::~HealthEndpointServer() {
    stop();
}

bool HealthEndpointServer::start() {
    if (running_.exchange(true)) {
        return false; // Already running
    }
    
    try {
        server_thread_ = std::make_unique<std::thread>(&HealthEndpointServer::server_loop, this);
        return true;
    } catch (const std::exception& e) {
        running_.store(false);
        return false;
    }
}

void HealthEndpointServer::stop() {
    if (!running_.exchange(false)) {
        return; // Already stopped
    }
    
    if (server_thread_ && server_thread_->joinable()) {
        server_thread_->join();
    }
}

HealthEndpointServer::Statistics HealthEndpointServer::get_statistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void HealthEndpointServer::server_loop() {
    // This is a simplified HTTP server implementation
    // In practice, you'd use a proper HTTP library like libhttpserver or similar
    
    while (running_.load()) {
        // Sleep to simulate server operations
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // In a real implementation, this would:
        // 1. Listen on the configured port
        // 2. Accept incoming connections
        // 3. Parse HTTP requests
        // 4. Call handle_http_request
        // 5. Send responses back to clients
    }
}

std::string HealthEndpointServer::handle_http_request(const std::string& request) {
    auto parsed = parse_request(request);
    
    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.total_requests++;
        stats_.endpoint_request_counts[parsed.path]++;
    }
    
    try {
        auto start_time = std::chrono::steady_clock::now();
        
        std::string response = handler_->handle_request(parsed.path, parsed.query_params, parsed.headers);
        
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        {
            std::lock_guard<std::mutex> lock(stats_mutex_);
            stats_.successful_requests++;
            
            // Update average response time
            if (stats_.successful_requests > 0) {
                auto total_time = stats_.avg_response_time.count() * (stats_.successful_requests - 1) + duration.count();
                stats_.avg_response_time = std::chrono::milliseconds(total_time / stats_.successful_requests);
            } else {
                stats_.avg_response_time = duration;
            }
        }
        
        return response;
        
    } catch (const std::exception& e) {
        {
            std::lock_guard<std::mutex> lock(stats_mutex_);
            stats_.failed_requests++;
        }
        
        return generate_response(500, "Internal Server Error", {{"Content-Type", "text/plain"}});
    }
}

HealthEndpointServer::HttpRequest HealthEndpointServer::parse_request(const std::string& request) {
    HttpRequest parsed;
    
    // This is a very simplified HTTP request parser
    std::istringstream iss(request);
    std::string line;
    
    // Parse request line
    if (std::getline(iss, line)) {
        std::istringstream request_line(line);
        request_line >> parsed.method >> parsed.path;
        
        // Extract query parameters
        auto query_pos = parsed.path.find('?');
        if (query_pos != std::string::npos) {
            std::string query_string = parsed.path.substr(query_pos + 1);
            parsed.path = parsed.path.substr(0, query_pos);
            
            // Parse query parameters (simplified)
            std::regex param_regex(R"(([^&=]+)=([^&]*))");
            std::sregex_iterator iter(query_string.begin(), query_string.end(), param_regex);
            std::sregex_iterator end;
            
            for (; iter != end; ++iter) {
                std::smatch match = *iter;
                parsed.query_params[match[1].str()] = match[2].str();
            }
        }
    }
    
    // Parse headers
    while (std::getline(iss, line) && !line.empty() && line != "\r") {
        auto colon_pos = line.find(':');
        if (colon_pos != std::string::npos) {
            std::string key = line.substr(0, colon_pos);
            std::string value = line.substr(colon_pos + 1);
            parsed.headers[trim(key)] = trim(value);
        }
    }
    
    // Parse body (if present)
    std::string body_line;
    while (std::getline(iss, body_line)) {
        parsed.body += body_line + "\n";
    }
    
    return parsed;
}

std::string HealthEndpointServer::generate_response(int status_code, 
                                                   const std::string& content,
                                                   const std::unordered_map<std::string, std::string>& headers) {
    std::stringstream ss;
    ss << "HTTP/1.1 " << status_code << " ";
    
    switch (status_code) {
        case 200: ss << "OK"; break;
        case 400: ss << "Bad Request"; break;
        case 401: ss << "Unauthorized"; break;
        case 404: ss << "Not Found"; break;
        case 500: ss << "Internal Server Error"; break;
        default: ss << "Unknown"; break;
    }
    
    ss << "\r\n";
    
    // Add CORS headers if enabled
    if (config_.enable_cors) {
        ss << "Access-Control-Allow-Origin: *\r\n";
        ss << "Access-Control-Allow-Methods: GET, POST, OPTIONS\r\n";
        ss << "Access-Control-Allow-Headers: Content-Type, Authorization\r\n";
    }
    
    for (const auto& [key, value] : headers) {
        ss << key << ": " << value << "\r\n";
    }
    
    ss << "Content-Length: " << content.length() << "\r\n";
    ss << "Connection: close\r\n";
    ss << "\r\n";
    ss << content;
    
    return ss.str();
}

// HealthEndpointsFactory implementation
std::unique_ptr<HealthEndpointServer> HealthEndpointsFactory::create_production_endpoints(
    std::shared_ptr<HealthMonitor> health_monitor,
    int port) {
    
    HealthEndpointHandler::Config handler_config;
    handler_config.enabled = true;
    handler_config.require_authentication = true;
    handler_config.include_detailed_errors = false;
    handler_config.cache_duration = std::chrono::seconds(10);
    handler_config.max_requests_per_minute = 60;
    
    auto handler = std::make_shared<HealthEndpointHandler>(health_monitor, handler_config);
    
    HealthEndpointServer::Config server_config;
    server_config.port = port;
    server_config.enable_cors = false;
    server_config.log_requests = true;
    server_config.log_responses = false;
    
    return std::make_unique<HealthEndpointServer>(handler, server_config);
}

std::unique_ptr<HealthEndpointServer> HealthEndpointsFactory::create_development_endpoints(
    std::shared_ptr<HealthMonitor> health_monitor,
    int port) {
    
    HealthEndpointHandler::Config handler_config;
    handler_config.enabled = true;
    handler_config.require_authentication = false;
    handler_config.include_detailed_errors = true;
    handler_config.pretty_print = true;
    handler_config.cache_duration = std::chrono::seconds(1);
    handler_config.max_requests_per_minute = 0; // No rate limiting
    
    auto handler = std::make_shared<HealthEndpointHandler>(health_monitor, handler_config);
    
    HealthEndpointServer::Config server_config;
    server_config.port = port;
    server_config.enable_cors = true;
    server_config.log_requests = true;
    server_config.log_responses = true;
    
    return std::make_unique<HealthEndpointServer>(handler, server_config);
}

std::unique_ptr<HealthEndpointServer> HealthEndpointsFactory::create_kubernetes_endpoints(
    std::shared_ptr<HealthMonitor> health_monitor,
    int port) {
    
    HealthEndpointHandler::Config handler_config;
    handler_config.enabled = true;
    handler_config.base_path = "/health";
    handler_config.require_authentication = false;
    handler_config.include_system_info = true;
    handler_config.include_dependencies = true;
    handler_config.cache_duration = std::chrono::seconds(5);
    handler_config.max_requests_per_minute = 1000; // High limit for K8s probes
    
    auto handler = std::make_shared<HealthEndpointHandler>(health_monitor, handler_config);
    
    HealthEndpointServer::Config server_config;
    server_config.port = port;
    server_config.enable_cors = false;
    server_config.log_requests = false; // Reduce noise from probes
    
    return std::make_unique<HealthEndpointServer>(handler, server_config);
}

} // namespace omop::common