/**
 * @file tracing_manager.cpp
 * @brief Implementation of distributed tracing manager with OpenTelemetry integration
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#include "tracing_manager.h"
#include "http_client.h"
#include <sstream>
#include <iomanip>
#include <random>
#include <iostream>
#include <ctime>
#include <nlohmann/json.hpp>

namespace omop::common {

// TraceContext implementation
std::string TraceContext::serialize() const {
    // Simple serialization format: trace_id|span_id|parent_span_id|sampled
    std::ostringstream oss;
    oss << trace_id << "|" << span_id << "|" << parent_span_id << "|" << (sampled ? "1" : "0");
    return oss.str();
}

std::optional<TraceContext> TraceContext::deserialize(const std::string& serialized) {
    std::istringstream iss(serialized);
    std::string token;
    std::vector<std::string> tokens;
    
    while (std::getline(iss, token, '|')) {
        tokens.push_back(token);
    }
    
    if (tokens.size() < 4) {
        return std::nullopt;
    }
    
    TraceContext context;
    context.trace_id = tokens[0];
    context.span_id = tokens[1];
    context.parent_span_id = tokens[2];
    context.sampled = (tokens[3] == "1");
    
    if (!context.is_valid()) {
        return std::nullopt;
    }
    
    return context;
}

// SpanAttributes implementation
void SpanAttributes::set_string(const std::string& key, const std::string& value) {
    std::lock_guard<std::mutex> lock(mutex_);
    attributes_[key] = value;
}

void SpanAttributes::set_int(const std::string& key, int64_t value) {
    std::lock_guard<std::mutex> lock(mutex_);
    attributes_[key] = std::to_string(value);
}

void SpanAttributes::set_double(const std::string& key, double value) {
    std::lock_guard<std::mutex> lock(mutex_);
    attributes_[key] = std::to_string(value);
}

void SpanAttributes::set_bool(const std::string& key, bool value) {
    std::lock_guard<std::mutex> lock(mutex_);
    attributes_[key] = value ? "true" : "false";
}

const std::unordered_map<std::string, std::string>& SpanAttributes::get_all() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return attributes_;
}

void SpanAttributes::clear() {
    std::lock_guard<std::mutex> lock(mutex_);
    attributes_.clear();
}

// DefaultSpan implementation
DefaultSpan::DefaultSpan(const std::string& name, SpanKind kind, const TraceContext& parent_context)
    : name_(name), kind_(kind), start_time_(std::chrono::system_clock::now()) {
    
    // Generate trace context
    if (parent_context.is_valid()) {
        context_.trace_id = parent_context.trace_id;
        context_.parent_span_id = parent_context.span_id;
        context_.sampled = parent_context.sampled;
    } else {
        // Generate new trace ID if this is a root span
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<uint64_t> dis;
        
        std::ostringstream trace_oss;
        trace_oss << std::hex << dis(gen) << dis(gen);
        context_.trace_id = trace_oss.str();
        context_.sampled = true;
    }
    
    context_.span_id = generate_span_id();
    
    // Set default attributes
    attributes_.set_string("span.kind", [kind]() {
        switch (kind) {
            case SpanKind::INTERNAL: return "internal";
            case SpanKind::SERVER: return "server";
            case SpanKind::CLIENT: return "client";
            case SpanKind::PRODUCER: return "producer";
            case SpanKind::CONSUMER: return "consumer";
        }
        return "internal";
    }());
}

DefaultSpan::~DefaultSpan() {
    if (!ended_.load()) {
        end();
    }
}

void DefaultSpan::set_name(const std::string& name) {
    std::lock_guard<std::mutex> lock(mutex_);
    name_ = name;
}

void DefaultSpan::set_status(SpanStatus status, const std::string& description) {
    std::lock_guard<std::mutex> lock(mutex_);
    status_ = status;
    status_description_ = description;
}

void DefaultSpan::set_attribute(const std::string& key, const std::string& value) {
    attributes_.set_string(key, value);
}

void DefaultSpan::set_attribute(const std::string& key, int64_t value) {
    attributes_.set_int(key, value);
}

void DefaultSpan::set_attribute(const std::string& key, double value) {
    attributes_.set_double(key, value);
}

void DefaultSpan::set_attribute(const std::string& key, bool value) {
    attributes_.set_bool(key, value);
}

void DefaultSpan::add_event(const SpanEvent& event) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!ended_.load()) {
        events_.push_back(event);
    }
}

void DefaultSpan::add_event(const std::string& name, const SpanAttributes& attributes) {
    SpanEvent event(name);
    event.attributes = attributes;
    add_event(event);
}

void DefaultSpan::record_exception(const std::exception& exception) {
    SpanAttributes attrs;
    attrs.set_string("exception.type", typeid(exception).name());
    attrs.set_string("exception.message", exception.what());
    
    add_event("exception", attrs);
    set_status(SpanStatus::ERROR, "Exception occurred: " + std::string(exception.what()));
}

void DefaultSpan::end() {
    bool expected = false;
    if (ended_.compare_exchange_strong(expected, true)) {
        end_time_ = std::chrono::system_clock::now();
        
        // Calculate duration and add it as an attribute
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time_ - start_time_);
        attributes_.set_int("duration_us", duration.count());
    }
}

TraceContext DefaultSpan::get_context() const {
    return context_;
}

bool DefaultSpan::is_recording() const {
    return !ended_.load() && context_.sampled;
}

std::string DefaultSpan::generate_span_id() const {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint64_t> dis;
    
    std::ostringstream oss;
    oss << std::hex << dis(gen);
    return oss.str();
}

// DefaultTracer implementation
thread_local ISpan* DefaultTracer::active_span_ = nullptr;

DefaultTracer::DefaultTracer(const std::string& service_name)
    : service_name_(service_name) {}

std::unique_ptr<ISpan> DefaultTracer::start_span(
    const std::string& name, 
    SpanKind kind,
    const TraceContext& parent_context) {
    
    TraceContext effective_parent = parent_context;
    
    // If no parent context provided, use active span's context
    if (!effective_parent.is_valid() && active_span_) {
        effective_parent = active_span_->get_context();
    }
    
    auto span = std::make_unique<DefaultSpan>(name, kind, effective_parent);
    
    // Set service name attribute
    span->set_attribute("service.name", service_name_);
    
    return span;
}

ISpan* DefaultTracer::get_active_span() {
    return active_span_;
}

void DefaultTracer::set_active_span(ISpan* span) {
    active_span_ = span;
}

std::string DefaultTracer::generate_trace_id() const {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint64_t> dis;
    
    std::ostringstream oss;
    oss << std::hex << dis(gen) << dis(gen);
    return oss.str();
}

// ConsoleSpanExporter implementation
bool ConsoleSpanExporter::export_spans(const std::vector<std::unique_ptr<ISpan>>& spans) {
    for (const auto& span : spans) {
        if (!span || !span->is_recording()) {
            continue;
        }
        
        auto context = span->get_context();
        std::cout << "[TRACE] Span: " << context.span_id 
                  << " Trace: " << context.trace_id;
        
        if (!context.parent_span_id.empty()) {
            std::cout << " Parent: " << context.parent_span_id;
        }
        
        std::cout << std::endl;
    }
    
    return true;
}

void ConsoleSpanExporter::shutdown() {
    // Nothing to do for console exporter
}

// OTLPSpanExporter implementation
OTLPSpanExporter::OTLPSpanExporter(const Config& config)
    : config_(config) {}

bool OTLPSpanExporter::export_spans(const std::vector<std::unique_ptr<ISpan>>& spans) {
    try {
        if (spans.empty()) {
            return true;
        }

        // Create HTTP client for OTLP export using factory
        auto client = HttpClientFactory::create_client();
        if (!client) {
            std::cerr << "[OTLP] Failed to create HTTP client" << std::endl;
            return false;
        }
        
        // Prepare OTLP batch export payload
        nlohmann::json batch_payload;
        batch_payload["resourceSpans"] = nlohmann::json::array();
        
        for (const auto& span : spans) {
            auto context = span->get_context();
            nlohmann::json span_data;
            span_data["traceId"] = context.trace_id;
            span_data["spanId"] = context.span_id;
            span_data["parentSpanId"] = context.parent_span_id;
            
            // Note: We can't get name, kind, start_time, end_time from ISpan interface
            // This would need to be extended in the interface or we'd need to cast to DefaultSpan
            // For now, we'll export what we can
            span_data["attributes"] = nlohmann::json::object();
            
            batch_payload["resourceSpans"].push_back(span_data);
        }
        
        // Send to OTLP endpoint
        HttpClient::Request request;
        request.method = HttpClient::Method::POST;
        request.url = config_.endpoint;
        request.body = batch_payload.dump();
        request.headers["Content-Type"] = "application/json";
        request.timeout_seconds = 30;
        
        auto response = client->make_request(request);
        return response.status_code >= 200 && response.status_code < 300;
        
    } catch (const std::exception& e) {
        std::cerr << "[OTLP] Export failed: " << e.what() << std::endl;
        return false;
    }
}

void OTLPSpanExporter::shutdown() {
    // Flush any remaining spans before shutdown
    if (!pending_spans_.empty()) {
        export_spans(std::move(pending_spans_));
        pending_spans_.clear();
    }
}

// JaegerSpanExporter implementation
JaegerSpanExporter::JaegerSpanExporter(const Config& config)
    : config_(config) {}

bool JaegerSpanExporter::export_spans(const std::vector<std::unique_ptr<ISpan>>& spans) {
    try {
        if (spans.empty()) {
            return true;
        }

        // Create HTTP client for Jaeger export using factory
        auto client = HttpClientFactory::create_client();
        if (!client) {
            std::cerr << "[Jaeger] Failed to create HTTP client" << std::endl;
            return false;
        }
        
        // Prepare Jaeger batch export payload
        nlohmann::json batch_payload;
        batch_payload["spans"] = nlohmann::json::array();
        
        for (const auto& span : spans) {
            auto context = span->get_context();
            nlohmann::json span_data;
            span_data["traceId"] = context.trace_id;
            span_data["spanId"] = context.span_id;
            span_data["parentSpanId"] = context.parent_span_id;
            
            // Note: We can't get name, kind, start_time, end_time from ISpan interface
            // This would need to be extended in the interface or we'd need to cast to DefaultSpan
            // For now, we'll export what we can
            span_data["tags"] = nlohmann::json::array();
            
            batch_payload["spans"].push_back(span_data);
        }
        
        // Send to Jaeger collector endpoint
        std::string endpoint = config_.collector_endpoint;
        if (endpoint.empty()) {
            endpoint = "http://" + config_.agent_host + ":" + std::to_string(config_.agent_port) + "/api/traces";
        }
        
        HttpClient::Request request;
        request.method = HttpClient::Method::POST;
        request.url = endpoint;
        request.body = batch_payload.dump();
        request.headers["Content-Type"] = "application/json";
        request.timeout_seconds = 30;
        
        auto response = client->make_request(request);
        return response.status_code >= 200 && response.status_code < 300;
        
    } catch (const std::exception& e) {
        std::cerr << "[Jaeger] Export failed: " << e.what() << std::endl;
        return false;
    }
}

void JaegerSpanExporter::shutdown() {
    // Flush any remaining spans before shutdown
    if (!pending_spans_.empty()) {
        export_spans(std::move(pending_spans_));
        pending_spans_.clear();
    }
}

// TracingManager implementation
TracingManager::TracingManager() : config_{} {}

TracingManager::TracingManager(const Config& config)
    : config_(config) {}

TracingManager::~TracingManager() {
    shutdown();
}

void TracingManager::initialize() {
    if (initialized_.load() || !config_.enabled) {
        return;
    }
    
    tracer_ = std::make_shared<DefaultTracer>(config_.service_name);
    setup_exporters();
    
    initialized_ = true;
}

void TracingManager::shutdown() {
    if (!initialized_.load()) {
        return;
    }
    
    shutdown_requested_ = true;
    
    // Shutdown all exporters
    for (auto& exporter : exporters_) {
        exporter->shutdown();
    }
    
    initialized_ = false;
}

std::shared_ptr<ITracer> TracingManager::get_tracer(const std::string& name) {
    if (!initialized_.load()) {
        initialize();
    }
    
    // For now, return the default tracer
    // In a full implementation, this could maintain multiple named tracers
    return tracer_;
}

TracingManager& TracingManager::instance() {
    static TracingManager instance;
    return instance;
}

TracingManager& TracingManager::instance(const Config& config) {
    static TracingManager instance(config);
    return instance;
}

void TracingManager::add_exporter(std::unique_ptr<ISpanExporter> exporter) {
    exporters_.push_back(std::move(exporter));
}

std::optional<TraceContext> TracingManager::extract_trace_context(
    const std::unordered_map<std::string, std::string>& carrier) const {
    
    // Look for standard tracing headers
    auto it = carrier.find("traceparent");
    if (it != carrier.end()) {
        // W3C Trace Context format: version-trace_id-parent_id-trace_flags
        return TraceContext::deserialize(it->second);
    }
    
    // Look for custom format
    it = carrier.find("x-trace-context");
    if (it != carrier.end()) {
        return TraceContext::deserialize(it->second);
    }
    
    return std::nullopt;
}

void TracingManager::inject_trace_context(
    const TraceContext& context,
    std::unordered_map<std::string, std::string>& carrier) const {
    
    if (!context.is_valid()) {
        return;
    }
    
    // Inject as custom header
    carrier["x-trace-context"] = context.serialize();
    
    // Inject as W3C Trace Context format
    carrier["traceparent"] = "00-" + context.trace_id + "-" + context.span_id + "-" + 
                              (context.sampled ? "01" : "00");
    
    if (!context.baggage.empty()) {
        std::string tracestate;
        for (const auto& [key, value] : context.baggage) {
            if (!tracestate.empty()) {
                tracestate += ",";
            }
            tracestate += key + "=" + value;
        }
        carrier["tracestate"] = tracestate;
    }
}

TracingManager::Statistics TracingManager::get_statistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void TracingManager::setup_exporters() {
    switch (config_.exporter_type) {
        case Config::ExporterType::CONSOLE:
            exporters_.push_back(std::make_unique<ConsoleSpanExporter>());
            break;
        case Config::ExporterType::OTLP: {
            OTLPSpanExporter::Config otlp_config;
            otlp_config.endpoint = config_.otlp_endpoint;
            exporters_.push_back(std::make_unique<OTLPSpanExporter>(otlp_config));
            break;
        }
        case Config::ExporterType::JAEGER: {
            JaegerSpanExporter::Config jaeger_config;
            jaeger_config.agent_host = config_.jaeger_agent_host;
            jaeger_config.agent_port = config_.jaeger_agent_port;
            exporters_.push_back(std::make_unique<JaegerSpanExporter>(jaeger_config));
            break;
        }
    }
}

bool TracingManager::should_sample() const {
    static thread_local std::random_device rd;
    static thread_local std::mt19937 gen(rd());
    static thread_local std::uniform_real_distribution<> dis(0.0, 1.0);
    
    return dis(gen) <= config_.sampling_rate;
}

// ScopedSpan implementation
ScopedSpan::ScopedSpan(std::shared_ptr<ITracer> tracer, 
                       const std::string& name,
                       SpanKind kind,
                       const TraceContext& parent_context)
    : tracer_(tracer) {
    
    if (tracer_) {
        span_ = tracer_->start_span(name, kind, parent_context);
        
        if (span_) {
            previous_active_span_ = tracer_->get_active_span();
            tracer_->set_active_span(span_.get());
        }
    }
}

ScopedSpan::~ScopedSpan() {
    end();
}

ScopedSpan::ScopedSpan(ScopedSpan&& other) noexcept
    : tracer_(std::move(other.tracer_))
    , span_(std::move(other.span_))
    , previous_active_span_(other.previous_active_span_)
    , ended_(other.ended_) {
    
    other.previous_active_span_ = nullptr;
    other.ended_ = true;
}

ScopedSpan& ScopedSpan::operator=(ScopedSpan&& other) noexcept {
    if (this != &other) {
        end();
        
        tracer_ = std::move(other.tracer_);
        span_ = std::move(other.span_);
        previous_active_span_ = other.previous_active_span_;
        ended_ = other.ended_;
        
        other.previous_active_span_ = nullptr;
        other.ended_ = true;
    }
    
    return *this;
}

void ScopedSpan::end() {
    if (!ended_ && span_) {
        span_->end();
        
        if (tracer_) {
            tracer_->set_active_span(previous_active_span_);
        }
        
        ended_ = true;
    }
}

} // namespace omop::common