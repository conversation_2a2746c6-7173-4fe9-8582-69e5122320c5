/**
 * @file error_recovery.cpp
 * @brief Implementation of enhanced error recovery mechanisms with retry and fallback strategies
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#include "error_recovery.h"
#include "logging.h"
#include <random>
#include <algorithm>
#include <future>
#include <thread>
#include <regex>
#include <any>

// Create a logger for error recovery
static auto logger = omop::common::Logger::get("error_recovery");

namespace omop::common {

// ErrorRecoveryEngine implementation
ErrorRecoveryEngine::ErrorRecoveryEngine() : config_{} {
    // Set default error classifier
    error_classifier_ = [this](const std::exception& error, const RecoveryContext& context) -> ErrorType {
        return classify_error(error, context);
    };
    
    LOG_INFO(logger, "ErrorRecoveryEngine initialized with default config");
}

ErrorRecoveryEngine::ErrorRecoveryEngine(const Config& config) : config_(config) {
    // Set default error classifier
    error_classifier_ = [this](const std::exception& error, const RecoveryContext& context) -> ErrorType {
        return classify_error(error, context);
    };
    
    LOG_INFO(logger, "ErrorRecoveryEngine initialized with config: retry={}, fallback={}, circuit_breaker={}", 
             config_.enable_retry, config_.enable_fallback, config_.enable_circuit_breaker);
}

ErrorRecoveryEngine::~ErrorRecoveryEngine() {
    // Clean up active recoveries
    std::lock_guard<std::mutex> lock(recoveries_mutex_);
    active_recoveries_.clear();
    
    auto stats = get_statistics();
    LOG_INFO(logger, "ErrorRecoveryEngine shutdown. Final statistics: success_rate={:.2f}%, recovery_rate={:.2f}%",
             stats.success_rate() * 100.0, stats.recovery_rate() * 100.0);
}

template<typename T>
std::variant<T, std::exception_ptr> ErrorRecoveryEngine::execute_with_recovery(
    const std::string& operation_id,
    std::function<T()> operation,
    const RetryPolicy& retry_policy,
    std::optional<std::function<T()>> fallback_operation) {
    
    internal_stats_.total_operations++;
    
    // Create recovery context
    RecoveryContext context;
    context.operation_id = operation_id;
    context.component_name = "ErrorRecoveryEngine";
    context.start_time = std::chrono::system_clock::now();
    
    // Store active recovery
    {
        std::lock_guard<std::mutex> lock(recoveries_mutex_);
        active_recoveries_[operation_id] = std::make_shared<RecoveryContext>(context);
    }
    
    try {
        // First attempt - direct execution
        try {
            auto result = operation();
            internal_stats_.successful_operations++;
            
            // Remove from active recoveries
            {
                std::lock_guard<std::mutex> lock(recoveries_mutex_);
                active_recoveries_.erase(operation_id);
            }
            
            LOG_DEBUG(logger, "Operation {} succeeded on first attempt", operation_id);
            return result;
        } catch (const std::exception& e) {
            LOG_DEBUG(logger, "Operation {} failed on first attempt: {}", operation_id, e.what());
            
            // Classify error to determine recovery strategy
            ErrorType error_type = error_classifier_(e, context);
            
            // Try retry strategy if enabled and error is transient
            if (config_.enable_retry && (error_type == ErrorType::Transient || error_type == ErrorType::Resource)) {
                auto retry_result = execute_retry<T>(operation, retry_policy, context);
                if (std::holds_alternative<T>(retry_result)) {
                    internal_stats_.successful_operations++;
                    internal_stats_.recovered_operations++;
                    
                    // Remove from active recoveries
                    {
                        std::lock_guard<std::mutex> lock(recoveries_mutex_);
                        active_recoveries_.erase(operation_id);
                    }
                    
                    LOG_INFO(logger, "Operation {} succeeded after {} retry attempts", operation_id, context.total_attempts());
                    return std::get<T>(retry_result);
                }
            }
            
            // Try fallback if available and enabled
            if (config_.enable_fallback && fallback_operation.has_value()) {
                LOG_INFO(logger, "Attempting fallback for operation {}", operation_id);
                
                auto fallback_result = execute_fallback<T>(fallback_operation.value(), context);
                if (std::holds_alternative<T>(fallback_result)) {
                    internal_stats_.successful_operations++;
                    internal_stats_.recovered_operations++;
                    internal_stats_.fallback_used++;
                    
                    // Remove from active recoveries
                    {
                        std::lock_guard<std::mutex> lock(recoveries_mutex_);
                        active_recoveries_.erase(operation_id);
                    }
                    
                    LOG_INFO(logger, "Operation {} succeeded using fallback", operation_id);
                    return std::get<T>(fallback_result);
                }
            }
            
            // All recovery attempts failed
            internal_stats_.failed_operations++;
            
            // Remove from active recoveries
            {
                std::lock_guard<std::mutex> lock(recoveries_mutex_);
                active_recoveries_.erase(operation_id);
            }
            
            LOG_ERROR(logger, "All recovery attempts failed for operation {}", operation_id);
            return std::current_exception();
        }
    } catch (...) {
        // Unexpected error
        internal_stats_.failed_operations++;
        
        // Remove from active recoveries
        {
            std::lock_guard<std::mutex> lock(recoveries_mutex_);
            active_recoveries_.erase(operation_id);
        }
        
        LOG_ERROR(logger, "Unexpected error during recovery for operation {}", operation_id);
        return std::current_exception();
    }
}

void ErrorRecoveryEngine::register_fallback(
    const std::string& operation_type,
    std::function<std::function<std::any()>(const std::any&)> fallback_factory) {
    
    std::lock_guard<std::mutex> lock(strategies_mutex_);
    fallback_strategies_[operation_type] = std::move(fallback_factory);
    
    LOG_DEBUG(logger, "Registered fallback strategy for operation type: {}", operation_type);
}

void ErrorRecoveryEngine::register_compensation(
    const std::string& operation_type,
    std::function<std::function<void()>(const std::any&)> compensation_factory) {
    
    std::lock_guard<std::mutex> lock(strategies_mutex_);
    compensation_strategies_[operation_type] = std::move(compensation_factory);
    
    LOG_DEBUG(logger, "Registered compensation strategy for operation type: {}", operation_type);
}

std::shared_ptr<CircuitBreaker> ErrorRecoveryEngine::get_circuit_breaker(const std::string& service_name) {
    std::lock_guard<std::mutex> lock(circuit_breakers_mutex_);
    
    auto it = circuit_breakers_.find(service_name);
    if (it == circuit_breakers_.end()) {
        // Create new circuit breaker with default config
        CircuitBreaker::Config cb_config;
        cb_config.failure_threshold = 5;
        cb_config.timeout = std::chrono::seconds(60);
        cb_config.success_threshold = 3;
        
        circuit_breakers_[service_name] = std::make_shared<CircuitBreaker>(cb_config);
        LOG_DEBUG(logger, "Created new circuit breaker for service: {}", service_name);
    }
    
    return circuit_breakers_[service_name];
}

ErrorType ErrorRecoveryEngine::classify_error(const std::exception& error, const RecoveryContext& context) const {
    std::string error_msg = error.what();
    std::string error_type = typeid(error).name();
    
    // Convert to lowercase for case-insensitive matching
    std::transform(error_msg.begin(), error_msg.end(), error_msg.begin(), ::tolower);
    
    // Classify based on error message patterns
    if (error_msg.find("timeout") != std::string::npos ||
        error_msg.find("connection") != std::string::npos ||
        error_msg.find("network") != std::string::npos ||
        error_msg.find("temporary") != std::string::npos ||
        error_msg.find("retry") != std::string::npos) {
        return ErrorType::Transient;
    }
    
    if (error_msg.find("memory") != std::string::npos ||
        error_msg.find("disk") != std::string::npos ||
        error_msg.find("resource") != std::string::npos ||
        error_msg.find("quota") != std::string::npos ||
        error_msg.find("limit") != std::string::npos) {
        return ErrorType::Resource;
    }
    
    if (error_msg.find("config") != std::string::npos ||
        error_msg.find("setting") != std::string::npos ||
        error_msg.find("parameter") != std::string::npos) {
        return ErrorType::Configuration;
    }
    
    if (error_msg.find("permission") != std::string::npos ||
        error_msg.find("access") != std::string::npos ||
        error_msg.find("auth") != std::string::npos ||
        error_msg.find("credential") != std::string::npos) {
        return ErrorType::Security;
    }
    
    if (error_msg.find("invalid") != std::string::npos ||
        error_msg.find("not found") != std::string::npos ||
        error_msg.find("missing") != std::string::npos ||
        error_msg.find("corrupt") != std::string::npos) {
        return ErrorType::Persistent;
    }
    
    // Classify based on exception type
    if (error_type.find("NetworkException") != std::string::npos ||
        error_type.find("TimeoutException") != std::string::npos) {
        return ErrorType::Transient;
    }
    
    if (error_type.find("ConfigurationException") != std::string::npos) {
        return ErrorType::Configuration;
    }
    
    if (error_type.find("SecurityException") != std::string::npos) {
        return ErrorType::Security;
    }
    
    return ErrorType::Unknown;
}

template<typename T>
std::variant<T, std::exception_ptr> ErrorRecoveryEngine::execute_retry(
    std::function<T()> operation,
    const RetryPolicy& policy,
    RecoveryContext& context) {
    
    for (size_t attempt = 1; attempt <= policy.max_attempts; ++attempt) {
        if (attempt > 1) {
            // Calculate delay with exponential backoff and jitter
            auto delay = policy.calculate_delay(attempt);
            
            LOG_DEBUG(logger, "Retry attempt {} for operation {}, waiting {}ms", 
                     attempt, context.operation_id, delay.count());
            
            std::this_thread::sleep_for(delay);
        }
        
        RecoveryAttempt recovery_attempt;
        recovery_attempt.attempt_number = attempt;
        recovery_attempt.strategy = RecoveryStrategy::Retry;
        recovery_attempt.timestamp = std::chrono::system_clock::now();
        
        try {
            auto start_time = std::chrono::high_resolution_clock::now();
            auto result = operation();
            auto end_time = std::chrono::high_resolution_clock::now();
            
            recovery_attempt.duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            recovery_attempt.success = true;
            
            context.attempts.push_back(recovery_attempt);
            internal_stats_.retry_attempts++;
            
            LOG_INFO(logger, "Retry attempt {} succeeded for operation {} in {}ms", 
                    attempt, context.operation_id, recovery_attempt.duration.count());
            
            return result;
        } catch (const std::exception& e) {
            auto end_time = std::chrono::high_resolution_clock::now();
            recovery_attempt.duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                end_time - std::chrono::high_resolution_clock::now());
            recovery_attempt.success = false;
            recovery_attempt.error_message = e.what();
            
            context.attempts.push_back(recovery_attempt);
            internal_stats_.retry_attempts++;
            
            // Check if we should continue retrying
            if (!policy.should_retry(e, attempt)) {
                LOG_WARN(logger, "Retry policy indicates no further retries for operation {} after attempt {}: {}", 
                        context.operation_id, attempt, e.what());
                return std::current_exception();
            }
            
            LOG_DEBUG(logger, "Retry attempt {} failed for operation {}: {}", 
                     attempt, context.operation_id, e.what());
            
            if (attempt == policy.max_attempts) {
                LOG_ERROR(logger, "Max retry attempts ({}) reached for operation {}", 
                         policy.max_attempts, context.operation_id);
                return std::current_exception();
            }
        }
    }
    
    return std::current_exception();
}

template<typename T>
std::variant<T, std::exception_ptr> ErrorRecoveryEngine::execute_fallback(
    std::function<T()> fallback_operation,
    RecoveryContext& context) {
    
    RecoveryAttempt recovery_attempt;
    recovery_attempt.attempt_number = context.total_attempts() + 1;
    recovery_attempt.strategy = RecoveryStrategy::Fallback;
    recovery_attempt.timestamp = std::chrono::system_clock::now();
    
    try {
        auto start_time = std::chrono::high_resolution_clock::now();
        auto result = fallback_operation();
        auto end_time = std::chrono::high_resolution_clock::now();
        
        recovery_attempt.duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        recovery_attempt.success = true;
        
        context.attempts.push_back(recovery_attempt);
        
        LOG_INFO(logger, "Fallback succeeded for operation {} in {}ms", 
                context.operation_id, recovery_attempt.duration.count());
        
        return result;
    } catch (const std::exception& e) {
        auto end_time = std::chrono::high_resolution_clock::now();
        recovery_attempt.duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - std::chrono::high_resolution_clock::now());
        recovery_attempt.success = false;
        recovery_attempt.error_message = e.what();
        
        context.attempts.push_back(recovery_attempt);
        
        LOG_ERROR(logger, "Fallback failed for operation {}: {}", context.operation_id, e.what());
        return std::current_exception();
    }
}

void ErrorRecoveryEngine::execute_compensation(
    const std::string& operation_type,
    const std::any& operation_data,
    RecoveryContext& context) {
    
    std::lock_guard<std::mutex> lock(strategies_mutex_);
    
    auto it = compensation_strategies_.find(operation_type);
    if (it == compensation_strategies_.end()) {
        LOG_WARN(logger, "No compensation strategy registered for operation type: {}", operation_type);
        return;
    }
    
    try {
        auto compensation_op = it->second(operation_data);
        compensation_op();
        
        internal_stats_.compensations++;
        LOG_INFO(logger, "Compensation executed successfully for operation {} of type {}", 
                context.operation_id, operation_type);
    } catch (const std::exception& e) {
        LOG_ERROR(logger, "Compensation failed for operation {} of type {}: {}", 
                 context.operation_id, operation_type, e.what());
    }
}

void ErrorRecoveryEngine::reset_statistics() {
    internal_stats_.total_operations = 0;
    internal_stats_.successful_operations = 0;
    internal_stats_.failed_operations = 0;
    internal_stats_.recovered_operations = 0;
    internal_stats_.retry_attempts = 0;
    internal_stats_.fallback_used = 0;
    internal_stats_.circuit_breaks = 0;
    internal_stats_.compensations = 0;
    
    LOG_INFO(logger, "Error recovery statistics reset");
}

// ResourceBulkhead implementation  
template<typename T>
T ResourceBulkhead::execute(std::function<T()> operation, int priority) {
    std::unique_lock<std::mutex> lock(resource_mutex_);
    
    // Wait for available resources
    auto start_wait = std::chrono::steady_clock::now();
    bool acquired = resource_available_.wait_for(
        lock, 
        config_.queue_timeout,
        [this] { return current_operations_.load() < config_.max_concurrent_operations; }
    );
    
    if (!acquired) {
        throw std::runtime_error("Resource bulkhead timeout: no resources available");
    }
    
    // Acquire resource
    current_operations_++;
    lock.unlock();
    
    try {
        // Execute operation
        auto result = operation();
        
        // Release resource
        current_operations_--;
        resource_available_.notify_one();
        
        return result;
    } catch (...) {
        // Release resource on exception
        current_operations_--;
        resource_available_.notify_one();
        throw;
    }
}

// Explicit template instantiations for common types
template std::variant<int, std::exception_ptr> ErrorRecoveryEngine::execute_with_recovery<int>(
    const std::string&, std::function<int()>, const RetryPolicy&, std::optional<std::function<int()>>);
    
template std::variant<std::string, std::exception_ptr> ErrorRecoveryEngine::execute_with_recovery<std::string>(
    const std::string&, std::function<std::string()>, const RetryPolicy&, std::optional<std::function<std::string()>>);
    
template std::variant<bool, std::exception_ptr> ErrorRecoveryEngine::execute_with_recovery<bool>(
    const std::string&, std::function<bool()>, const RetryPolicy&, std::optional<std::function<bool()>>);

template std::variant<void*, std::exception_ptr> ErrorRecoveryEngine::execute_with_recovery<void*>(
    const std::string&, std::function<void*()>, const RetryPolicy&, std::optional<std::function<void*()>>);

template int ResourceBulkhead::execute<int>(std::function<int()>, int);
template std::string ResourceBulkhead::execute<std::string>(std::function<std::string()>, int);
template bool ResourceBulkhead::execute<bool>(std::function<bool()>, int);

} // namespace omop::common