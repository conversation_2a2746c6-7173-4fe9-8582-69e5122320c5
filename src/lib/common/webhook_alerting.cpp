/**
 * @file webhook_alerting.cpp
 * @brief Implementation of real-time webhook alerting system
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#include "webhook_alerting.h"
#include "http_client.h"
#include "utils/string_utils.h"
#include <nlohmann/json.hpp>
#include <chrono>
#include <thread>
#include <sstream>
#include <iomanip>
#include <regex>
#include <cstdlib>

using json = nlohmann::json;
using namespace omop::common::string_utils;

namespace omop::common {

// WebhookPayloadTemplate implementation
std::string WebhookPayloadTemplate::generate_payload(
    const Alert& alert,
    const WebhookConfig& config,
    const std::unordered_map<std::string, std::any>& context) {
    
    if (config.template_format == "slack") {
        return generate_slack_payload(alert, context);
    } else if (config.template_format == "teams") {
        return generate_teams_payload(alert, context);
    } else if (config.template_format == "discord") {
        return generate_discord_payload(alert, context);
    } else if (config.template_format == "pagerduty") {
        return generate_pagerduty_payload(alert, config, context);
    } else if (config.template_format == "custom" && !config.custom_template.empty()) {
        std::unordered_map<std::string, std::string> variables;
        variables["alert_id"] = alert.get_id();
        variables["alert_title"] = alert.get_title();
        variables["alert_message"] = alert.get_message();
        variables["alert_severity"] = to_string(static_cast<int>(alert.get_severity()));
        variables["alert_component"] = alert.get_component();
        variables["alert_timestamp"] = format_timestamp(alert.get_timestamp());
        
        return apply_custom_template(config.custom_template, variables);
    } else {
        // Default JSON format
        json payload;
        payload["alert_id"] = alert.get_id();
        payload["title"] = alert.get_title();
        payload["message"] = alert.get_message();
        payload["severity"] = static_cast<int>(alert.get_severity());
        payload["component"] = alert.get_component();
        payload["timestamp"] = format_timestamp(alert.get_timestamp());
        
        if (config.include_context) {
            json context_json;
            for (const auto& [key, value] : context) {
                // Basic type handling for std::any
                context_json[key] = "context_value";
            }
            payload["context"] = context_json;
        }
        
        return payload.dump();
    }
}

std::string WebhookPayloadTemplate::generate_slack_payload(
    const Alert& alert,
    const std::unordered_map<std::string, std::any>& context) {
    
    json payload;
    
    // Basic message structure
    json attachment;
    attachment["color"] = severity_to_color(alert.get_severity());
    attachment["title"] = alert.get_title();
    attachment["text"] = alert.get_message();
    attachment["ts"] = std::chrono::duration_cast<std::chrono::seconds>(
        alert.get_timestamp().time_since_epoch()).count();
    
    // Add fields
    json fields = json::array();
    
    json severity_field;
    severity_field["title"] = "Severity";
    severity_field["value"] = severity_to_emoji(alert.get_severity()) + " " + 
                             to_string(static_cast<int>(alert.get_severity()));
    severity_field["short"] = true;
    fields.push_back(severity_field);
    
    json component_field;
    component_field["title"] = "Component";
    component_field["value"] = alert.get_component();
    component_field["short"] = true;
    fields.push_back(component_field);
    
    attachment["fields"] = fields;
    attachment["footer"] = "OMOP ETL Monitoring";
    
    payload["attachments"] = json::array({attachment});
    
    return payload.dump();
}

std::string WebhookPayloadTemplate::generate_teams_payload(
    const Alert& alert,
    const std::unordered_map<std::string, std::any>& context) {
    
    json payload;
    payload["@type"] = "MessageCard";
    payload["@context"] = "https://schema.org/extensions";
    
    payload["summary"] = alert.get_title();
    payload["themeColor"] = severity_to_color(alert.get_severity()).substr(1); // Remove #
    
    json sections = json::array();
    json section;
    section["activityTitle"] = alert.get_title();
    section["activitySubtitle"] = "Component: " + alert.get_component();
    section["text"] = alert.get_message();
    
    json facts = json::array();
    
    json severity_fact;
    severity_fact["name"] = "Severity";
    severity_fact["value"] = severity_to_emoji(alert.get_severity()) + " " +
                            to_string(static_cast<int>(alert.get_severity()));
    facts.push_back(severity_fact);
    
    json timestamp_fact;
    timestamp_fact["name"] = "Timestamp";
    timestamp_fact["value"] = format_timestamp(alert.get_timestamp());
    facts.push_back(timestamp_fact);
    
    section["facts"] = facts;
    sections.push_back(section);
    
    payload["sections"] = sections;
    
    return payload.dump();
}

std::string WebhookPayloadTemplate::generate_discord_payload(
    const Alert& alert,
    const std::unordered_map<std::string, std::any>& context) {
    
    json payload;
    
    json embeds = json::array();
    json embed;
    
    embed["title"] = alert.get_title();
    embed["description"] = alert.get_message();
    embed["color"] = std::stoi(severity_to_color(alert.get_severity()).substr(1), nullptr, 16);
    embed["timestamp"] = format_timestamp(alert.get_timestamp());
    
    json fields = json::array();
    
    json severity_field;
    severity_field["name"] = "Severity";
    severity_field["value"] = severity_to_emoji(alert.get_severity()) + " " +
                             to_string(static_cast<int>(alert.get_severity()));
    severity_field["inline"] = true;
    fields.push_back(severity_field);
    
    json component_field;
    component_field["name"] = "Component";
    component_field["value"] = alert.get_component();
    component_field["inline"] = true;
    fields.push_back(component_field);
    
    embed["fields"] = fields;
    
    json footer;
    footer["text"] = "OMOP ETL Monitoring";
    embed["footer"] = footer;
    
    embeds.push_back(embed);
    payload["embeds"] = embeds;
    
    return payload.dump();
}

std::string WebhookPayloadTemplate::generate_pagerduty_payload(
    const Alert& alert,
    const WebhookConfig& config,
    const std::unordered_map<std::string, std::any>& context) {
    
    json payload;
    
    // PagerDuty requires routing_key in config headers or tags
    auto routing_key_it = config.headers.find("routing_key");
    if (routing_key_it != config.headers.end()) {
        payload["routing_key"] = routing_key_it->second;
    }
    
    payload["event_action"] = "trigger";
    payload["dedup_key"] = alert.get_id();
    
    json event_payload;
    event_payload["summary"] = alert.get_title();
    event_payload["source"] = alert.get_component();
    event_payload["severity"] = (alert.get_severity() == AlertSeverity::CRITICAL) ? "critical" : 
                               (alert.get_severity() == AlertSeverity::ERROR) ? "error" :
                               (alert.get_severity() == AlertSeverity::WARNING) ? "warning" : "info";
    event_payload["component"] = alert.get_component();
    event_payload["group"] = "omop-etl";
    event_payload["class"] = "monitoring";
    
    json custom_details;
    custom_details["message"] = alert.get_message();
    custom_details["timestamp"] = format_timestamp(alert.get_timestamp());
    event_payload["custom_details"] = custom_details;
    
    payload["payload"] = event_payload;
    
    return payload.dump();
}

std::string WebhookPayloadTemplate::apply_custom_template(
    const std::string& template_str,
    const std::unordered_map<std::string, std::string>& variables) {
    
    std::string result = template_str;
    
    // Simple variable substitution with {{variable}} syntax
    std::regex var_regex(R"(\{\{(\w+)\}\})");
    std::smatch matches;
    
    while (std::regex_search(result, matches, var_regex)) {
        std::string var_name = matches[1].str();
        auto it = variables.find(var_name);
        
        if (it != variables.end()) {
            result = std::regex_replace(result, std::regex("\\{\\{" + var_name + "\\}\\}"), it->second);
        } else {
            result = std::regex_replace(result, std::regex("\\{\\{" + var_name + "\\}\\}"), "");
        }
    }
    
    return result;
}

std::string WebhookPayloadTemplate::escape_json_string(const std::string& str) {
    std::string result;
    result.reserve(str.length());
    
    for (char c : str) {
        switch (c) {
            case '"': result += "\\\""; break;
            case '\\': result += "\\\\"; break;
            case '\b': result += "\\b"; break;
            case '\f': result += "\\f"; break;
            case '\n': result += "\\n"; break;
            case '\r': result += "\\r"; break;
            case '\t': result += "\\t"; break;
            default: result += c; break;
        }
    }
    
    return result;
}

std::string WebhookPayloadTemplate::format_timestamp(const std::chrono::system_clock::time_point& tp) {
    auto time_t = std::chrono::system_clock::to_time_t(tp);
    std::stringstream ss;
    ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");
    return ss.str();
}

std::string WebhookPayloadTemplate::severity_to_emoji(AlertSeverity severity) {
    switch (severity) {
        case AlertSeverity::CRITICAL: return "🔴";
        case AlertSeverity::ERROR: return "❌";
        case AlertSeverity::WARNING: return "⚠️";
        case AlertSeverity::INFO: return "ℹ️";
        case AlertSeverity::DEBUG: return "🔍";
        default: return "❓";
    }
}

std::string WebhookPayloadTemplate::severity_to_color(AlertSeverity severity) {
    switch (severity) {
        case AlertSeverity::CRITICAL: return "#FF0000";
        case AlertSeverity::ERROR: return "#FF4500";
        case AlertSeverity::WARNING: return "#FFA500";
        case AlertSeverity::INFO: return "#00BFFF";
        case AlertSeverity::DEBUG: return "#808080";
        default: return "#000000";
    }
}

// WebhookAlerting implementation
WebhookAlerting::WebhookAlerting(const Config& config) 
    : config_(config), http_client_(std::make_unique<omop::common::HttpClient>()) {
    
    if (config_.enabled) {
        start();
    }
}

WebhookAlerting::~WebhookAlerting() {
    stop();
}

void WebhookAlerting::start() {
    if (running_.exchange(true)) {
        return; // Already running
    }
    
    // Start worker threads
    worker_threads_.reserve(config_.worker_threads);
    for (size_t i = 0; i < config_.worker_threads; ++i) {
        worker_threads_.emplace_back(
            std::make_unique<std::thread>(&WebhookAlerting::worker_thread, this, static_cast<int>(i))
        );
    }
}

void WebhookAlerting::stop() {
    if (!running_.exchange(false)) {
        return; // Already stopped
    }
    
    // Notify all workers
    queue_cv_.notify_all();
    
    // Join worker threads
    for (auto& worker : worker_threads_) {
        if (worker && worker->joinable()) {
            worker->join();
        }
    }
    worker_threads_.clear();
}

bool WebhookAlerting::register_webhook(const WebhookConfig& config) {
    std::lock_guard<std::mutex> lock(webhooks_mutex_);
    
    if (webhooks_.find(config.name) != webhooks_.end()) {
        return false; // Already exists
    }
    
    webhooks_[config.name] = config;
    
    // Initialize rate limiting state
    {
        std::lock_guard<std::mutex> rate_lock(rate_limit_mutex_);
        rate_limit_states_[config.name] = RateLimitState{};
    }
    
    // Initialize circuit breaker state
    {
        std::lock_guard<std::mutex> cb_lock(circuit_breaker_mutex_);
        circuit_breaker_states_[config.name] = CircuitBreakerState{};
    }
    
    return true;
}

bool WebhookAlerting::unregister_webhook(const std::string& name) {
    std::lock_guard<std::mutex> lock(webhooks_mutex_);
    return webhooks_.erase(name) > 0;
}

size_t WebhookAlerting::send_alert(const Alert& alert, 
                                  const std::unordered_map<std::string, std::any>& context) {
    
    if (!running_.load() || !config_.enabled) {
        return 0;
    }
    
    // Check global rate limits
    auto now = std::chrono::system_clock::now();
    {
        std::lock_guard<std::mutex> lock(rate_limit_mutex_);
        // Simple global rate limiting implementation
        stats_.total_alerts_sent.fetch_add(1);
    }
    
    // Queue alert for processing
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        
        if (alert_queue_.size() >= config_.max_queue_size) {
            stats_.rate_limited_alerts.fetch_add(1);
            return 0;
        }
        
        alert_queue_.emplace(alert, context);
        stats_.queued_deliveries.fetch_add(1);
    }
    
    queue_cv_.notify_one();
    
    std::lock_guard<std::mutex> lock(webhooks_mutex_);
    return webhooks_.size();
}

bool WebhookAlerting::test_webhook(const std::string& name) {
    std::lock_guard<std::mutex> lock(webhooks_mutex_);
    
    auto it = webhooks_.find(name);
    if (it == webhooks_.end()) {
        return false;
    }
    
    const auto& config = it->second;
    
    try {
        // Create test alert
        Alert test_alert("test", "Webhook Test", "This is a test message", AlertSeverity::INFO, "webhook-test");
        
        std::string payload = WebhookPayloadTemplate::generate_payload(test_alert, config);
        
        // Configure HTTP client
        omop::common::HttpClient::Config http_config;
        http_config.timeout_ms = static_cast<int>(config.timeout.count());
        http_client_->configure(http_config);
        
        // Prepare headers
        auto headers = config.headers;
        headers["Content-Type"] = config.content_type;
        
        // Make request
        auto response = http_client_->request(config.method, config.url, payload, headers);
        
        return response && response->status_code >= 200 && response->status_code < 300;
        
    } catch (const std::exception& e) {
        return false;
    }
}

WebhookAlerting::Statistics WebhookAlerting::get_statistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

std::vector<WebhookDeliveryAttempt> WebhookAlerting::get_delivery_history(size_t limit) const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    std::vector<WebhookDeliveryAttempt> result;
    size_t count = std::min(limit, delivery_history_.size());
    
    if (count > 0) {
        auto start_it = delivery_history_.end() - static_cast<long>(count);
        result.assign(start_it, delivery_history_.end());
    }
    
    return result;
}

std::vector<WebhookConfig> WebhookAlerting::get_webhooks() const {
    std::lock_guard<std::mutex> lock(webhooks_mutex_);
    
    std::vector<WebhookConfig> result;
    result.reserve(webhooks_.size());
    
    for (const auto& [name, config] : webhooks_) {
        result.push_back(config);
    }
    
    return result;
}

void WebhookAlerting::set_metrics_collector(std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector) {
    metrics_collector_ = metrics_collector;
}

void WebhookAlerting::worker_thread(int worker_id) {
    while (running_.load()) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        
        queue_cv_.wait(lock, [this] {
            return !alert_queue_.empty() || !running_.load();
        });
        
        if (!running_.load() && alert_queue_.empty()) {
            break;
        }
        
        if (alert_queue_.empty()) {
            continue;
        }
        
        auto [alert, context] = std::move(alert_queue_.front());
        alert_queue_.pop();
        lock.unlock();
        
        process_alert(alert, context);
    }
}

void WebhookAlerting::process_alert(const Alert& alert, 
                                   const std::unordered_map<std::string, std::any>& context) {
    
    std::lock_guard<std::mutex> lock(webhooks_mutex_);
    
    for (const auto& [name, config] : webhooks_) {
        if (!should_send_alert(alert, config)) {
            stats_.filtered_alerts.fetch_add(1);
            continue;
        }
        
        if (!check_rate_limits(name) || !check_circuit_breaker(name)) {
            stats_.rate_limited_alerts.fetch_add(1);
            continue;
        }
        
        auto attempt = deliver_to_webhook(alert, config, context);
        
        // Update statistics
        {
            std::lock_guard<std::mutex> stats_lock(stats_mutex_);
            delivery_history_.push_back(attempt);
            
            // Keep only recent history
            if (delivery_history_.size() > 1000) {
                delivery_history_.erase(delivery_history_.begin(), delivery_history_.begin() + 100);
            }
            
            if (attempt.status == WebhookDeliveryStatus::SUCCESS) {
                stats_.successful_deliveries.fetch_add(1);
                stats_.webhook_success_counts[name]++;
                stats_.last_delivery_time = attempt.delivery_time;
            } else {
                stats_.failed_deliveries.fetch_add(1);
                stats_.webhook_failure_counts[name]++;
            }
        }
        
        // Update circuit breaker
        update_circuit_breaker(name, attempt.status == WebhookDeliveryStatus::SUCCESS);
        
        // Update metrics if available
        if (metrics_collector_) {
            update_webhook_metrics(name, attempt);
        }
    }
}

WebhookDeliveryAttempt WebhookAlerting::deliver_to_webhook(
    const Alert& alert,
    const WebhookConfig& config,
    const std::unordered_map<std::string, std::any>& context) {
    
    WebhookDeliveryAttempt attempt;
    attempt.webhook_name = config.name;
    attempt.alert_id = alert.get_id();
    attempt.scheduled_time = std::chrono::system_clock::now();
    attempt.status = WebhookDeliveryStatus::PENDING;
    
    try {
        std::string payload = WebhookPayloadTemplate::generate_payload(alert, config, context);
        
        // Configure HTTP client
        omop::common::HttpClient::Config http_config;
        http_config.timeout_ms = static_cast<int>(config.timeout.count());
        http_client_->configure(http_config);
        
        // Prepare headers
        auto headers = config.headers;
        headers["Content-Type"] = config.content_type;
        
        auto start_time = std::chrono::steady_clock::now();
        
        // Make request with retry logic
        for (int retry = 0; retry <= config.max_retries; ++retry) {
            attempt.attempt_number = retry + 1;
            
            auto response = http_client_->request(config.method, config.url, payload, headers);
            
            attempt.delivery_time = std::chrono::system_clock::now();
            attempt.response_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - start_time);
            
            if (response) {
                attempt.response_code = response->status_code;
                attempt.response_body = response->body.substr(0, 1000); // Limit body size
                
                if (response->status_code >= 200 && response->status_code < 300) {
                    attempt.status = WebhookDeliveryStatus::SUCCESS;
                    break;
                } else if (response->status_code >= 400 && response->status_code < 500) {
                    // Client error - don't retry
                    attempt.status = WebhookDeliveryStatus::FAILED;
                    attempt.error_message = "HTTP " + to_string(response->status_code);
                    break;
                }
            }
            
            if (retry < config.max_retries) {
                attempt.status = WebhookDeliveryStatus::RETRYING;
                auto delay = std::chrono::duration_cast<std::chrono::milliseconds>(
                    config.retry_delay * std::pow(config.retry_backoff_multiplier, retry));
                std::this_thread::sleep_for(delay);
            } else {
                attempt.status = WebhookDeliveryStatus::FAILED;
                attempt.error_message = "Max retries exceeded";
            }
        }
        
    } catch (const std::exception& e) {
        attempt.status = WebhookDeliveryStatus::FAILED;
        attempt.error_message = e.what();
        attempt.delivery_time = std::chrono::system_clock::now();
    }
    
    return attempt;
}

bool WebhookAlerting::should_send_alert(const Alert& alert, const WebhookConfig& config) const {
    // Check severity filter
    if (!config.severity_filter.empty()) {
        bool found = false;
        for (auto severity : config.severity_filter) {
            if (alert.get_severity() == severity) {
                found = true;
                break;
            }
        }
        if (!found) return false;
    }
    
    // Check component filter
    if (!config.component_filter.empty()) {
        bool found = false;
        for (const auto& component : config.component_filter) {
            if (alert.get_component() == component) {
                found = true;
                break;
            }
        }
        if (!found) return false;
    }
    
    return true;
}

bool WebhookAlerting::check_rate_limits(const std::string& webhook_name) {
    std::lock_guard<std::mutex> lock(rate_limit_mutex_);
    
    auto& state = rate_limit_states_[webhook_name];
    std::lock_guard<std::mutex> state_lock(state.mutex);
    
    auto now = std::chrono::system_clock::now();
    
    // Find webhook config for rate limits
    auto webhook_it = webhooks_.find(webhook_name);
    if (webhook_it == webhooks_.end()) {
        return false;
    }
    
    const auto& config = webhook_it->second;
    
    // Clean old entries
    while (!state.delivery_times.empty() && 
           (now - state.delivery_times.front()) > config.rate_limit_window) {
        state.delivery_times.pop();
    }
    
    // Check rate limit
    if (state.delivery_times.size() >= config.max_alerts_per_minute) {
        return false;
    }
    
    // Add current delivery
    state.delivery_times.push(now);
    return true;
}

bool WebhookAlerting::check_circuit_breaker(const std::string& webhook_name) {
    if (!config_.enable_circuit_breaker) {
        return true;
    }
    
    std::lock_guard<std::mutex> lock(circuit_breaker_mutex_);
    auto& state = circuit_breaker_states_[webhook_name];
    std::lock_guard<std::mutex> state_lock(state.mutex);
    
    if (state.is_open.load()) {
        auto now = std::chrono::system_clock::now();
        if ((now - state.last_failure_time) > config_.circuit_breaker_timeout) {
            // Try to close circuit breaker
            state.is_open.store(false);
            state.failure_count.store(0);
            return true;
        }
        return false;
    }
    
    return true;
}

void WebhookAlerting::update_circuit_breaker(const std::string& webhook_name, bool success) {
    if (!config_.enable_circuit_breaker) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(circuit_breaker_mutex_);
    auto& state = circuit_breaker_states_[webhook_name];
    std::lock_guard<std::mutex> state_lock(state.mutex);
    
    if (success) {
        state.failure_count.store(0);
    } else {
        auto failures = state.failure_count.fetch_add(1) + 1;
        if (failures >= config_.circuit_breaker_failure_threshold) {
            state.is_open.store(true);
            state.last_failure_time = std::chrono::system_clock::now();
        }
    }
}

void WebhookAlerting::update_webhook_metrics(const std::string& webhook_name, 
                                            const WebhookDeliveryAttempt& attempt) {
    if (!metrics_collector_) {
        return;
    }
    
    std::string prefix = config_.metrics_prefix + "_";
    
    // Delivery counter
    std::unordered_map<std::string, std::string> labels;
    labels["webhook"] = webhook_name;
    labels["status"] = (attempt.status == WebhookDeliveryStatus::SUCCESS) ? "success" : "failure";
    
    metrics_collector_->increment_counter(prefix + "deliveries_total", labels);
    
    // Response time histogram
    if (attempt.response_time.count() > 0) {
        metrics_collector_->observe_histogram(prefix + "response_time_ms", 
                                            static_cast<double>(attempt.response_time.count()), labels);
    }
    
    // HTTP status code counter
    if (attempt.response_code > 0) {
        labels["status_code"] = to_string(attempt.response_code);
        metrics_collector_->increment_counter(prefix + "http_responses_total", labels);
    }
}

void WebhookAlerting::cleanup_delivery_history() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    auto cutoff_time = std::chrono::system_clock::now() - config_.failed_delivery_retention;
    
    auto it = std::remove_if(delivery_history_.begin(), delivery_history_.end(),
        [cutoff_time](const WebhookDeliveryAttempt& attempt) {
            return attempt.delivery_time < cutoff_time;
        });
    
    delivery_history_.erase(it, delivery_history_.end());
}

// WebhookAlertingFactory implementation
std::unique_ptr<WebhookAlerting> WebhookAlertingFactory::create_slack_alerting(
    const std::string& slack_webhook_url,
    const std::string& channel) {
    
    auto alerting = std::make_unique<WebhookAlerting>();
    
    WebhookConfig config;
    config.name = "slack";
    config.url = slack_webhook_url;
    config.template_format = "slack";
    config.content_type = "application/json";
    
    if (!channel.empty()) {
        config.headers["channel"] = channel;
    }
    
    alerting->register_webhook(config);
    
    return alerting;
}

std::unique_ptr<WebhookAlerting> WebhookAlertingFactory::create_teams_alerting(
    const std::string& teams_webhook_url) {
    
    auto alerting = std::make_unique<WebhookAlerting>();
    
    WebhookConfig config;
    config.name = "teams";
    config.url = teams_webhook_url;
    config.template_format = "teams";
    config.content_type = "application/json";
    
    alerting->register_webhook(config);
    
    return alerting;
}

std::unique_ptr<WebhookAlerting> WebhookAlertingFactory::create_pagerduty_alerting(
    const std::string& routing_key) {
    
    auto alerting = std::make_unique<WebhookAlerting>();
    
    WebhookConfig config;
    config.name = "pagerduty";
    config.url = "https://events.pagerduty.com/v2/enqueue";
    config.template_format = "pagerduty";
    config.content_type = "application/json";
    config.headers["routing_key"] = routing_key;
    
    alerting->register_webhook(config);
    
    return alerting;
}

std::unique_ptr<WebhookAlerting> WebhookAlertingFactory::create_from_environment() {
    auto alerting = std::make_unique<WebhookAlerting>();
    
    // Slack webhook
    if (const char* slack_url = std::getenv("WEBHOOK_SLACK_URL")) {
        WebhookConfig config;
        config.name = "slack";
        config.url = slack_url;
        config.template_format = "slack";
        
        if (const char* channel = std::getenv("WEBHOOK_SLACK_CHANNEL")) {
            config.headers["channel"] = channel;
        }
        
        alerting->register_webhook(config);
    }
    
    // Teams webhook
    if (const char* teams_url = std::getenv("WEBHOOK_TEAMS_URL")) {
        WebhookConfig config;
        config.name = "teams";
        config.url = teams_url;
        config.template_format = "teams";
        
        alerting->register_webhook(config);
    }
    
    // Discord webhook
    if (const char* discord_url = std::getenv("WEBHOOK_DISCORD_URL")) {
        WebhookConfig config;
        config.name = "discord";
        config.url = discord_url;
        config.template_format = "discord";
        
        alerting->register_webhook(config);
    }
    
    // PagerDuty webhook
    if (const char* pd_key = std::getenv("WEBHOOK_PAGERDUTY_KEY")) {
        WebhookConfig config;
        config.name = "pagerduty";
        config.url = "https://events.pagerduty.com/v2/enqueue";
        config.template_format = "pagerduty";
        config.headers["routing_key"] = pd_key;
        
        alerting->register_webhook(config);
    }
    
    return alerting;
}

} // namespace omop::common