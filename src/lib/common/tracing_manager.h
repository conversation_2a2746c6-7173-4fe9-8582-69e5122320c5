/**
 * @file tracing_manager.h
 * @brief Distributed tracing manager with OpenTelemetry integration for OMOP ETL
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <chrono>
#include <atomic>
#include <mutex>
#include <optional>

namespace omop::common {

/**
 * @brief Span status enumeration
 */
enum class SpanStatus {
    UNSET,      ///< Status not set
    OK,         ///< Operation completed successfully
    ERROR       ///< Operation completed with error
};

/**
 * @brief Span kind enumeration
 */
enum class SpanKind {
    INTERNAL,   ///< Internal span within application
    SERVER,     ///< Server-side handling of request
    CLIENT,     ///< Client-side request
    PRODUCER,   ///< Producer span (e.g., message publishing)
    CONSUMER    ///< Consumer span (e.g., message consumption)
};

/**
 * @brief Trace context for distributed tracing
 */
struct TraceContext {
    std::string trace_id;           ///< Unique trace identifier
    std::string span_id;            ///< Current span identifier
    std::string parent_span_id;     ///< Parent span identifier (optional)
    bool sampled{true};             ///< Whether this trace is sampled
    std::unordered_map<std::string, std::string> baggage; ///< Trace baggage
    
    /**
     * @brief Check if trace context is valid
     */
    bool is_valid() const {
        return !trace_id.empty() && !span_id.empty();
    }
    
    /**
     * @brief Serialize trace context to string (for HTTP headers, etc.)
     */
    std::string serialize() const;
    
    /**
     * @brief Deserialize trace context from string
     * @param serialized Serialized trace context
     * @return Optional trace context if valid
     */
    static std::optional<TraceContext> deserialize(const std::string& serialized);
};

/**
 * @brief Span attributes container
 */
class SpanAttributes {
public:
    SpanAttributes() = default;
    
    // Copy constructor
    SpanAttributes(const SpanAttributes& other) {
        std::lock_guard<std::mutex> other_lock(other.mutex_);
        attributes_ = other.attributes_;
    }
    
    // Copy assignment
    SpanAttributes& operator=(const SpanAttributes& other) {
        if (this != &other) {
            std::lock(mutex_, other.mutex_);
            std::lock_guard<std::mutex> lhs_lock(mutex_, std::adopt_lock);
            std::lock_guard<std::mutex> rhs_lock(other.mutex_, std::adopt_lock);
            attributes_ = other.attributes_;
        }
        return *this;
    }
    
    // Move constructor
    SpanAttributes(SpanAttributes&& other) noexcept {
        std::lock_guard<std::mutex> other_lock(other.mutex_);
        attributes_ = std::move(other.attributes_);
    }
    
    // Move assignment
    SpanAttributes& operator=(SpanAttributes&& other) noexcept {
        if (this != &other) {
            std::lock(mutex_, other.mutex_);
            std::lock_guard<std::mutex> lhs_lock(mutex_, std::adopt_lock);
            std::lock_guard<std::mutex> rhs_lock(other.mutex_, std::adopt_lock);
            attributes_ = std::move(other.attributes_);
        }
        return *this;
    }
    /**
     * @brief Set string attribute
     * @param key Attribute key
     * @param value String value
     */
    void set_string(const std::string& key, const std::string& value);
    
    /**
     * @brief Set integer attribute
     * @param key Attribute key
     * @param value Integer value
     */
    void set_int(const std::string& key, int64_t value);
    
    /**
     * @brief Set double attribute
     * @param key Attribute key
     * @param value Double value
     */
    void set_double(const std::string& key, double value);
    
    /**
     * @brief Set boolean attribute
     * @param key Attribute key
     * @param value Boolean value
     */
    void set_bool(const std::string& key, bool value);
    
    /**
     * @brief Get all attributes
     * @return Map of attribute key-value pairs
     */
    const std::unordered_map<std::string, std::string>& get_all() const;
    
    /**
     * @brief Clear all attributes
     */
    void clear();

private:
    std::unordered_map<std::string, std::string> attributes_;
    mutable std::mutex mutex_;
};

/**
 * @brief Span event for adding timestamped events to spans
 */
struct SpanEvent {
    std::string name;
    std::chrono::system_clock::time_point timestamp;
    SpanAttributes attributes;
    
    SpanEvent(const std::string& event_name) 
        : name(event_name), timestamp(std::chrono::system_clock::now()) {}
};

/**
 * @brief Tracing span interface
 */
class ISpan {
public:
    virtual ~ISpan() = default;
    
    /**
     * @brief Set span name
     * @param name Span name
     */
    virtual void set_name(const std::string& name) = 0;
    
    /**
     * @brief Set span status
     * @param status Span status
     * @param description Optional description
     */
    virtual void set_status(SpanStatus status, const std::string& description = "") = 0;
    
    /**
     * @brief Add attribute to span
     * @param key Attribute key
     * @param value Attribute value
     */
    virtual void set_attribute(const std::string& key, const std::string& value) = 0;
    virtual void set_attribute(const std::string& key, int64_t value) = 0;
    virtual void set_attribute(const std::string& key, double value) = 0;
    virtual void set_attribute(const std::string& key, bool value) = 0;
    
    /**
     * @brief Add event to span
     * @param event Span event
     */
    virtual void add_event(const SpanEvent& event) = 0;
    
    /**
     * @brief Add event to span
     * @param name Event name
     * @param attributes Optional attributes
     */
    virtual void add_event(const std::string& name, const SpanAttributes& attributes = {}) = 0;
    
    /**
     * @brief Record exception
     * @param exception Exception to record
     */
    virtual void record_exception(const std::exception& exception) = 0;
    
    /**
     * @brief End the span
     */
    virtual void end() = 0;
    
    /**
     * @brief Get trace context
     * @return Current trace context
     */
    virtual TraceContext get_context() const = 0;
    
    /**
     * @brief Check if span is recording
     * @return true if span is recording
     */
    virtual bool is_recording() const = 0;
};

/**
 * @brief Default span implementation
 */
class DefaultSpan : public ISpan {
public:
    DefaultSpan(const std::string& name, SpanKind kind, const TraceContext& parent_context = {});
    ~DefaultSpan() override;
    
    void set_name(const std::string& name) override;
    void set_status(SpanStatus status, const std::string& description = "") override;
    void set_attribute(const std::string& key, const std::string& value) override;
    void set_attribute(const std::string& key, int64_t value) override;
    void set_attribute(const std::string& key, double value) override;
    void set_attribute(const std::string& key, bool value) override;
    void add_event(const SpanEvent& event) override;
    void add_event(const std::string& name, const SpanAttributes& attributes = {}) override;
    void record_exception(const std::exception& exception) override;
    void end() override;
    TraceContext get_context() const override;
    bool is_recording() const override;

private:
    std::string name_;
    SpanKind kind_;
    TraceContext context_;
    SpanAttributes attributes_;
    std::vector<SpanEvent> events_;
    SpanStatus status_{SpanStatus::UNSET};
    std::string status_description_;
    std::chrono::system_clock::time_point start_time_;
    std::chrono::system_clock::time_point end_time_;
    std::atomic<bool> ended_{false};
    mutable std::mutex mutex_;
    
    std::string generate_span_id() const;
};

/**
 * @brief Tracer interface for creating spans
 */
class ITracer {
public:
    virtual ~ITracer() = default;
    
    /**
     * @brief Start a new span
     * @param name Span name
     * @param kind Span kind
     * @param parent_context Parent trace context (optional)
     * @return Unique pointer to span
     */
    virtual std::unique_ptr<ISpan> start_span(
        const std::string& name, 
        SpanKind kind = SpanKind::INTERNAL,
        const TraceContext& parent_context = {}) = 0;
    
    /**
     * @brief Get current active span
     * @return Pointer to active span or nullptr
     */
    virtual ISpan* get_active_span() = 0;
    
    /**
     * @brief Set active span
     * @param span Span to make active
     */
    virtual void set_active_span(ISpan* span) = 0;
};

/**
 * @brief Default tracer implementation
 */
class DefaultTracer : public ITracer {
public:
    explicit DefaultTracer(const std::string& service_name);
    
    std::unique_ptr<ISpan> start_span(
        const std::string& name, 
        SpanKind kind = SpanKind::INTERNAL,
        const TraceContext& parent_context = {}) override;
    
    ISpan* get_active_span() override;
    void set_active_span(ISpan* span) override;

private:
    std::string service_name_;
    thread_local static ISpan* active_span_;
    std::string generate_trace_id() const;
};

/**
 * @brief Span exporter interface
 */
class ISpanExporter {
public:
    virtual ~ISpanExporter() = default;
    
    /**
     * @brief Export spans
     * @param spans Spans to export
     * @return true if export successful
     */
    virtual bool export_spans(const std::vector<std::unique_ptr<ISpan>>& spans) = 0;
    
    /**
     * @brief Shutdown exporter
     */
    virtual void shutdown() = 0;
};

/**
 * @brief Console span exporter (for development/debugging)
 */
class ConsoleSpanExporter : public ISpanExporter {
public:
    bool export_spans(const std::vector<std::unique_ptr<ISpan>>& spans) override;
    void shutdown() override;
};

/**
 * @brief OTLP (OpenTelemetry Protocol) span exporter
 */
class OTLPSpanExporter : public ISpanExporter {
public:
    struct Config {
        std::string endpoint{"http://localhost:4317"};
        std::unordered_map<std::string, std::string> headers;
        std::chrono::milliseconds timeout{10000};
        bool use_ssl{false};
    };
    
    explicit OTLPSpanExporter(const Config& config);
    
    bool export_spans(const std::vector<std::unique_ptr<ISpan>>& spans) override;
    void shutdown() override;

private:
    Config config_;
    std::vector<std::unique_ptr<ISpan>> pending_spans_;
};

/**
 * @brief Jaeger span exporter
 */
class JaegerSpanExporter : public ISpanExporter {
public:
    struct Config {
        std::string agent_host{"localhost"};
        int agent_port{6831};
        std::string collector_endpoint;
        std::unordered_map<std::string, std::string> tags;
    };
    
    explicit JaegerSpanExporter(const Config& config);
    
    bool export_spans(const std::vector<std::unique_ptr<ISpan>>& spans) override;
    void shutdown() override;

private:
    Config config_;
    std::vector<std::unique_ptr<ISpan>> pending_spans_;
};

/**
 * @brief Tracing manager for centralized trace management
 */
class TracingManager {
public:
    /**
     * @brief Tracing configuration
     */
    struct Config {
        bool enabled{true};
        std::string service_name{"omop-etl"};
        std::string service_version{"1.0.0"};
        double sampling_rate{1.0};           ///< Sampling rate (0.0 to 1.0)
        size_t max_spans_per_batch{512};     ///< Maximum spans per export batch
        std::chrono::milliseconds export_interval{5000}; ///< Export interval
        std::chrono::milliseconds export_timeout{30000}; ///< Export timeout
        
        // Resource attributes
        std::unordered_map<std::string, std::string> resource_attributes;
        
        // Exporter configuration
        enum class ExporterType {
            CONSOLE,
            OTLP,
            JAEGER
        };
        ExporterType exporter_type{ExporterType::CONSOLE};
        std::string otlp_endpoint{"http://localhost:4317"};
        std::string jaeger_agent_host{"localhost"};
        int jaeger_agent_port{6831};
    };
    
    /**
     * @brief Constructor
     * @param config Tracing configuration
     */
    TracingManager();
    explicit TracingManager(const Config& config);
    
    /**
     * @brief Destructor
     */
    ~TracingManager();
    
    /**
     * @brief Initialize tracing system
     */
    void initialize();
    
    /**
     * @brief Shutdown tracing system
     */
    void shutdown();
    
    /**
     * @brief Get tracer instance
     * @param name Tracer name (optional)
     * @return Shared pointer to tracer
     */
    std::shared_ptr<ITracer> get_tracer(const std::string& name = "");
    
    /**
     * @brief Get or create the global tracing manager instance
     * @param config Configuration (used only for first call)
     * @return Reference to global instance
     */
    static TracingManager& instance();
    static TracingManager& instance(const Config& config);
    
    /**
     * @brief Add span exporter
     * @param exporter Span exporter
     */
    void add_exporter(std::unique_ptr<ISpanExporter> exporter);
    
    /**
     * @brief Extract trace context from carrier (e.g., HTTP headers)
     * @param carrier Key-value carrier
     * @return Optional trace context
     */
    std::optional<TraceContext> extract_trace_context(
        const std::unordered_map<std::string, std::string>& carrier) const;
    
    /**
     * @brief Inject trace context into carrier
     * @param context Trace context to inject
     * @param carrier Key-value carrier to inject into
     */
    void inject_trace_context(
        const TraceContext& context,
        std::unordered_map<std::string, std::string>& carrier) const;
    
    /**
     * @brief Get tracing statistics
     */
    struct Statistics {
        size_t spans_created{0};
        size_t spans_exported{0};
        size_t export_failures{0};
        size_t active_spans{0};
        std::chrono::system_clock::time_point last_export_time;
    };
    
    Statistics get_statistics() const;

private:
    Config config_;
    std::atomic<bool> initialized_{false};
    std::shared_ptr<DefaultTracer> tracer_;
    std::vector<std::unique_ptr<ISpanExporter>> exporters_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    Statistics stats_;
    
    // Export management
    std::atomic<bool> shutdown_requested_{false};
    
    void setup_exporters();
    bool should_sample() const;
};

/**
 * @brief RAII span wrapper for automatic span lifecycle management
 */
class ScopedSpan {
public:
    /**
     * @brief Constructor - starts a new span
     * @param tracer Tracer to use
     * @param name Span name
     * @param kind Span kind
     * @param parent_context Parent context (optional)
     */
    ScopedSpan(std::shared_ptr<ITracer> tracer, 
               const std::string& name,
               SpanKind kind = SpanKind::INTERNAL,
               const TraceContext& parent_context = {});
    
    /**
     * @brief Destructor - ends the span
     */
    ~ScopedSpan();
    
    // Delete copy constructor and assignment
    ScopedSpan(const ScopedSpan&) = delete;
    ScopedSpan& operator=(const ScopedSpan&) = delete;
    
    // Allow move
    ScopedSpan(ScopedSpan&& other) noexcept;
    ScopedSpan& operator=(ScopedSpan&& other) noexcept;
    
    /**
     * @brief Get the underlying span
     * @return Pointer to span
     */
    ISpan* get() const { return span_.get(); }
    
    /**
     * @brief Operator -> for direct span access
     */
    ISpan* operator->() const { return span_.get(); }
    
    /**
     * @brief End the span explicitly
     */
    void end();

private:
    std::shared_ptr<ITracer> tracer_;
    std::unique_ptr<ISpan> span_;
    ISpan* previous_active_span_{nullptr};
    bool ended_{false};
};

// Convenience macros for tracing
#define TRACE_SPAN(tracer, name) \
    omop::common::ScopedSpan _trace_span(tracer, name, omop::common::SpanKind::INTERNAL)

#define TRACE_SPAN_WITH_KIND(tracer, name, kind) \
    omop::common::ScopedSpan _trace_span(tracer, name, kind)

#define TRACE_SET_ATTRIBUTE(key, value) \
    if (_trace_span.get()) { _trace_span->set_attribute(key, value); }

#define TRACE_ADD_EVENT(name) \
    if (_trace_span.get()) { _trace_span->add_event(name); }

#define TRACE_RECORD_EXCEPTION(ex) \
    if (_trace_span.get()) { _trace_span->record_exception(ex); }

} // namespace omop::common