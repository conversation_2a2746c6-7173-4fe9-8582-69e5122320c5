/**
 * @file webhook_alerting.h
 * @brief Real-time webhook alerting system for OMOP ETL monitoring
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#pragma once

#include "alert_manager.h"
#include "http_client.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <atomic>
#include <chrono>
#include <functional>

namespace omop::common {

/**
 * @brief Webhook delivery status
 */
enum class WebhookDeliveryStatus {
    PENDING,    ///< Webhook delivery pending
    SUCCESS,    ///< Webhook delivered successfully  
    FAILED,     ///< Webhook delivery failed
    RETRYING    ///< Webhook delivery being retried
};

/**
 * @brief Webhook configuration
 */
struct WebhookConfig {
    std::string name;                               ///< Webhook name/identifier
    std::string url;                               ///< Webhook endpoint URL
    std::string method{"POST"};                    ///< HTTP method
    std::unordered_map<std::string, std::string> headers; ///< Additional headers
    std::string content_type{"application/json"}; ///< Content type
    std::chrono::milliseconds timeout{30000};     ///< Request timeout
    
    // Retry configuration
    int max_retries{3};
    std::chrono::milliseconds retry_delay{1000};
    double retry_backoff_multiplier{2.0};
    
    // Filtering
    std::vector<AlertSeverity> severity_filter;   ///< Only send alerts with these severities
    std::vector<std::string> component_filter;    ///< Only send alerts from these components
    std::unordered_map<std::string, std::string> tag_filters; ///< Tag-based filters
    
    // Rate limiting
    size_t max_alerts_per_minute{10};
    std::chrono::minutes rate_limit_window{5};
    
    // Payload configuration
    std::string template_format{"json"};          ///< Payload template format (json, slack, teams, custom)
    std::string custom_template;                  ///< Custom payload template
    bool include_context{true};                   ///< Include additional context data
    bool include_metrics{true};                   ///< Include related metrics
};

/**
 * @brief Webhook delivery attempt
 */
struct WebhookDeliveryAttempt {
    std::string webhook_name;
    std::string alert_id;
    WebhookDeliveryStatus status{WebhookDeliveryStatus::PENDING};
    int attempt_number{0};
    std::chrono::system_clock::time_point scheduled_time;
    std::chrono::system_clock::time_point delivery_time;
    std::chrono::milliseconds response_time{0};
    int response_code{0};
    std::string response_body;
    std::string error_message;
};

/**
 * @brief Webhook payload template engine
 */
class WebhookPayloadTemplate {
public:
    /**
     * @brief Generate payload from alert and configuration
     * @param alert Alert to send
     * @param config Webhook configuration
     * @param context Additional context data
     * @return Generated payload string
     */
    static std::string generate_payload(
        const Alert& alert,
        const WebhookConfig& config,
        const std::unordered_map<std::string, std::any>& context = {});
    
    /**
     * @brief Generate Slack-compatible payload
     * @param alert Alert to format
     * @param context Additional context
     * @return Slack JSON payload
     */
    static std::string generate_slack_payload(
        const Alert& alert,
        const std::unordered_map<std::string, std::any>& context = {});
    
    /**
     * @brief Generate Microsoft Teams-compatible payload
     * @param alert Alert to format
     * @param context Additional context
     * @return Teams JSON payload
     */
    static std::string generate_teams_payload(
        const Alert& alert,
        const std::unordered_map<std::string, std::any>& context = {});
    
    /**
     * @brief Generate Discord-compatible payload
     * @param alert Alert to format
     * @param context Additional context
     * @return Discord JSON payload
     */
    static std::string generate_discord_payload(
        const Alert& alert,
        const std::unordered_map<std::string, std::any>& context = {});
    
    /**
     * @brief Generate PagerDuty-compatible payload
     * @param alert Alert to format
     * @param config Webhook configuration (must include routing_key)
     * @param context Additional context
     * @return PagerDuty JSON payload
     */
    static std::string generate_pagerduty_payload(
        const Alert& alert,
        const WebhookConfig& config,
        const std::unordered_map<std::string, std::any>& context = {});
    
    /**
     * @brief Apply custom template with variable substitution
     * @param template_str Template string with {{variable}} placeholders
     * @param variables Variable values for substitution
     * @return Rendered template
     */
    static std::string apply_custom_template(
        const std::string& template_str,
        const std::unordered_map<std::string, std::string>& variables);

private:
    static std::string escape_json_string(const std::string& str);
    static std::string format_timestamp(const std::chrono::system_clock::time_point& tp);
    static std::string severity_to_emoji(AlertSeverity severity);
    static std::string severity_to_color(AlertSeverity severity);
};

/**
 * @brief Real-time webhook alerting system
 */
class WebhookAlerting {
public:
    /**
     * @brief Configuration for webhook alerting system
     */
    struct Config {
        bool enabled{true};
        size_t max_queue_size{1000};
        size_t worker_threads{2};
        std::chrono::milliseconds delivery_timeout{60000};
        std::chrono::hours failed_delivery_retention{24};
        
        // Global rate limiting
        size_t global_rate_limit{100}; // alerts per minute
        std::chrono::minutes global_rate_window{1};
        
        // Circuit breaker settings
        bool enable_circuit_breaker{true};
        size_t circuit_breaker_failure_threshold{5};
        std::chrono::minutes circuit_breaker_timeout{5};
        
        // Metrics integration
        bool export_metrics{true};
        std::string metrics_prefix{"omop_webhook_alerting"};
    };
    
    /**
     * @brief Constructor
     * @param config Webhook alerting configuration
     */
    explicit WebhookAlerting(const Config& config = Config{});
    
    /**
     * @brief Destructor
     */
    ~WebhookAlerting();
    
    /**
     * @brief Start webhook alerting system
     */
    void start();
    
    /**
     * @brief Stop webhook alerting system
     */
    void stop();
    
    /**
     * @brief Register webhook endpoint
     * @param config Webhook configuration
     * @return true if registered successfully
     */
    bool register_webhook(const WebhookConfig& config);
    
    /**
     * @brief Unregister webhook endpoint
     * @param name Webhook name
     * @return true if unregistered successfully
     */
    bool unregister_webhook(const std::string& name);
    
    /**
     * @brief Send alert to registered webhooks
     * @param alert Alert to send
     * @param context Additional context data
     * @return Number of webhooks the alert was queued for
     */
    size_t send_alert(const Alert& alert, 
                     const std::unordered_map<std::string, std::any>& context = {});
    
    /**
     * @brief Test webhook connectivity
     * @param name Webhook name
     * @return true if webhook is reachable
     */
    bool test_webhook(const std::string& name);
    
    /**
     * @brief Get webhook statistics
     */
    struct Statistics {
        size_t total_alerts_sent{0};
        size_t successful_deliveries{0};
        size_t failed_deliveries{0};
        size_t queued_deliveries{0};
        size_t rate_limited_alerts{0};
        size_t filtered_alerts{0};
        std::unordered_map<std::string, size_t> webhook_success_counts;
        std::unordered_map<std::string, size_t> webhook_failure_counts;
        std::chrono::system_clock::time_point last_delivery_time;
    };
    
    Statistics get_statistics() const;
    
    /**
     * @brief Get delivery history for debugging
     * @param limit Maximum number of recent deliveries to return
     * @return Vector of recent delivery attempts
     */
    std::vector<WebhookDeliveryAttempt> get_delivery_history(size_t limit = 100) const;
    
    /**
     * @brief Get configured webhooks
     * @return List of webhook configurations
     */
    std::vector<WebhookConfig> get_webhooks() const;
    
    /**
     * @brief Set metrics collector for exporting webhook metrics
     * @param metrics_collector Metrics collector instance
     */
    void set_metrics_collector(std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector);

private:
    Config config_;
    std::atomic<bool> running_{false};
    
    // Webhook configurations
    mutable std::mutex webhooks_mutex_;
    std::unordered_map<std::string, WebhookConfig> webhooks_;
    
    // Delivery queue and workers
    std::queue<std::pair<Alert, std::unordered_map<std::string, std::any>>> alert_queue_;
    mutable std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::vector<std::unique_ptr<std::thread>> worker_threads_;
    
    // Rate limiting
    struct RateLimitState {
        std::queue<std::chrono::system_clock::time_point> delivery_times;
        mutable std::mutex mutex;
    };
    std::unordered_map<std::string, RateLimitState> rate_limit_states_;
    mutable std::mutex rate_limit_mutex_;
    
    // Circuit breaker state
    struct CircuitBreakerState {
        std::atomic<size_t> failure_count{0};
        std::atomic<bool> is_open{false};
        std::chrono::system_clock::time_point last_failure_time;
        mutable std::mutex mutex;
    };
    std::unordered_map<std::string, CircuitBreakerState> circuit_breaker_states_;
    mutable std::mutex circuit_breaker_mutex_;
    
    // Statistics and history
    mutable std::mutex stats_mutex_;
    Statistics stats_;
    std::vector<WebhookDeliveryAttempt> delivery_history_;
    
    // HTTP client for webhook delivery
    std::unique_ptr<omop::common::HttpClient> http_client_;
    
    // Metrics integration
    std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector_;
    
    /**
     * @brief Worker thread function
     * @param worker_id Worker thread identifier
     */
    void worker_thread(int worker_id);
    
    /**
     * @brief Process a single alert
     * @param alert Alert to process
     * @param context Alert context
     */
    void process_alert(const Alert& alert, 
                      const std::unordered_map<std::string, std::any>& context);
    
    /**
     * @brief Deliver alert to specific webhook
     * @param alert Alert to deliver
     * @param config Webhook configuration
     * @param context Alert context
     * @return Delivery attempt result
     */
    WebhookDeliveryAttempt deliver_to_webhook(
        const Alert& alert,
        const WebhookConfig& config,
        const std::unordered_map<std::string, std::any>& context);
    
    /**
     * @brief Check if alert should be sent to webhook (filtering)
     * @param alert Alert to check
     * @param config Webhook configuration
     * @return true if alert should be sent
     */
    bool should_send_alert(const Alert& alert, const WebhookConfig& config) const;
    
    /**
     * @brief Check rate limits for webhook
     * @param webhook_name Webhook name
     * @return true if within rate limits
     */
    bool check_rate_limits(const std::string& webhook_name);
    
    /**
     * @brief Check circuit breaker state for webhook
     * @param webhook_name Webhook name
     * @return true if circuit is closed (webhook available)
     */
    bool check_circuit_breaker(const std::string& webhook_name);
    
    /**
     * @brief Update circuit breaker state after delivery attempt
     * @param webhook_name Webhook name
     * @param success Whether delivery was successful
     */
    void update_circuit_breaker(const std::string& webhook_name, bool success);
    
    /**
     * @brief Update webhook metrics
     * @param webhook_name Webhook name
     * @param attempt Delivery attempt
     */
    void update_webhook_metrics(const std::string& webhook_name, 
                               const WebhookDeliveryAttempt& attempt);
    
    /**
     * @brief Clean up old delivery history
     */
    void cleanup_delivery_history();
};

/**
 * @brief Webhook alerting factory for common configurations
 */
class WebhookAlertingFactory {
public:
    /**
     * @brief Create webhook alerting with Slack configuration
     * @param slack_webhook_url Slack webhook URL
     * @param channel Channel to send alerts to
     * @return Configured webhook alerting instance
     */
    static std::unique_ptr<WebhookAlerting> create_slack_alerting(
        const std::string& slack_webhook_url,
        const std::string& channel = "#alerts");
    
    /**
     * @brief Create webhook alerting with Microsoft Teams configuration  
     * @param teams_webhook_url Teams webhook URL
     * @return Configured webhook alerting instance
     */
    static std::unique_ptr<WebhookAlerting> create_teams_alerting(
        const std::string& teams_webhook_url);
    
    /**
     * @brief Create webhook alerting with PagerDuty configuration
     * @param routing_key PagerDuty routing key
     * @return Configured webhook alerting instance
     */
    static std::unique_ptr<WebhookAlerting> create_pagerduty_alerting(
        const std::string& routing_key);
    
    /**
     * @brief Create webhook alerting from environment variables
     * Environment variables:
     * - WEBHOOK_SLACK_URL: Slack webhook URL
     * - WEBHOOK_TEAMS_URL: Teams webhook URL  
     * - WEBHOOK_DISCORD_URL: Discord webhook URL
     * - WEBHOOK_PAGERDUTY_KEY: PagerDuty routing key
     * @return Configured webhook alerting instance with all available webhooks
     */
    static std::unique_ptr<WebhookAlerting> create_from_environment();
};

} // namespace omop::common