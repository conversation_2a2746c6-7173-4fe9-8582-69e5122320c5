/**
 * @file health_monitor.cpp
 * @brief Implementation of centralized health monitoring for OMOP ETL components
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#include "health_monitor.h"
#include "exceptions.h"
#include "http_client.h"
#include <filesystem>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <sys/stat.h>
#include <sys/statvfs.h>
#include <unistd.h>
#include <future>
#include <iostream>
#include <nlohmann/json.hpp>

#if defined(__linux__)
#include <sys/sysinfo.h>
#elif defined(__APPLE__)
#include <mach/mach.h>
#include <mach/vm_statistics.h>
#include <mach/mach_host.h>
#endif

namespace omop::common {

// ConfigurationHealthCheck implementation
ConfigurationHealthCheck::ConfigurationHealthCheck(const std::string& config_path)
    : config_path_(config_path) {}

HealthCheckResult ConfigurationHealthCheck::check_health() {
    HealthCheckResult result;
    result.component_name = get_component_name();
    result.timestamp = std::chrono::system_clock::now();
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        if (!std::filesystem::exists(config_path_)) {
            result.status = HealthStatus::UNHEALTHY;
            result.message = "Configuration file not found: " + config_path_;
            result.details["path"] = config_path_;
            result.details["exists"] = "false";
        } else {
            // Check if file is readable
            std::ifstream file(config_path_);
            if (!file.is_open()) {
                result.status = HealthStatus::DEGRADED;
                result.message = "Configuration file exists but cannot be opened: " + config_path_;
                result.details["path"] = config_path_;
                result.details["readable"] = "false";
            } else {
                result.status = HealthStatus::HEALTHY;
                result.message = "Configuration file is accessible";
                result.details["path"] = config_path_;
                result.details["readable"] = "true";
                
                // Get file size and modification time
                auto file_size = std::filesystem::file_size(config_path_);
                auto last_write = std::filesystem::last_write_time(config_path_);
                result.details["size_bytes"] = std::to_string(file_size);
            }
        }
    } catch (const std::exception& e) {
        result.status = HealthStatus::UNHEALTHY;
        result.message = "Error checking configuration: " + std::string(e.what());
        result.details["error"] = e.what();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    result.response_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    return result;
}

// DatabaseHealthCheck implementation
DatabaseHealthCheck::DatabaseHealthCheck(const std::string& name, const DatabaseConfig& config)
    : component_name_(name), config_(config) {}

HealthCheckResult DatabaseHealthCheck::check_health() {
    HealthCheckResult result;
    result.component_name = get_component_name();
    result.timestamp = std::chrono::system_clock::now();
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        // For now, simulate database connectivity check
        // In a real implementation, this would use actual database connection
        result.status = HealthStatus::HEALTHY;
        result.message = "Database connection simulation successful";
        result.details["host"] = config_.host;
        result.details["port"] = std::to_string(config_.port);
        result.details["database"] = config_.database;
        result.details["connection_status"] = "simulated_ok";
        
        // Simulate some response time variability
        std::this_thread::sleep_for(std::chrono::milliseconds(10 + (rand() % 50)));
        
    } catch (const std::exception& e) {
        result.status = HealthStatus::UNHEALTHY;
        result.message = "Database connection failed: " + std::string(e.what());
        result.details["error"] = e.what();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    result.response_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    return result;
}

// MemoryHealthCheck implementation
MemoryHealthCheck::MemoryHealthCheck()
    : config_{} {}

MemoryHealthCheck::MemoryHealthCheck(const MemoryConfig& config)
    : config_(config) {}

size_t MemoryHealthCheck::get_memory_usage_mb() const {
#if defined(__linux__)
    // Read from /proc/self/status
    std::ifstream status_file("/proc/self/status");
    std::string line;
    while (std::getline(status_file, line)) {
        if (line.substr(0, 6) == "VmRSS:") {
            std::istringstream iss(line);
            std::string label;
            size_t value_kb;
            iss >> label >> value_kb;
            return value_kb / 1024; // Convert to MB
        }
    }
    return 0;
#elif defined(__APPLE__)
    struct task_basic_info info;
    mach_msg_type_number_t info_count = TASK_BASIC_INFO_COUNT;
    if (task_info(mach_task_self(), TASK_BASIC_INFO, (task_info_t)&info, &info_count) == KERN_SUCCESS) {
        return info.resident_size / (1024 * 1024); // Convert to MB
    }
    return 0;
#else
    // Fallback - return a simulated value
    return 128; // Simulate 128MB usage
#endif
}

HealthCheckResult MemoryHealthCheck::check_health() {
    HealthCheckResult result;
    result.component_name = get_component_name();
    result.timestamp = std::chrono::system_clock::now();
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        size_t current_memory_mb = get_memory_usage_mb();
        double memory_ratio = static_cast<double>(current_memory_mb) / config_.max_memory_mb;
        
        result.details["current_memory_mb"] = std::to_string(current_memory_mb);
        result.details["max_memory_mb"] = std::to_string(config_.max_memory_mb);
        result.details["memory_ratio"] = std::to_string(memory_ratio);
        
        if (memory_ratio >= config_.critical_threshold) {
            result.status = HealthStatus::UNHEALTHY;
            result.message = "Memory usage critical: " + std::to_string(current_memory_mb) + 
                           "MB (" + std::to_string(memory_ratio * 100) + "%)";
        } else if (memory_ratio >= config_.warning_threshold) {
            result.status = HealthStatus::DEGRADED;
            result.message = "Memory usage high: " + std::to_string(current_memory_mb) + 
                           "MB (" + std::to_string(memory_ratio * 100) + "%)";
        } else {
            result.status = HealthStatus::HEALTHY;
            result.message = "Memory usage normal: " + std::to_string(current_memory_mb) + 
                           "MB (" + std::to_string(memory_ratio * 100) + "%)";
        }
        
    } catch (const std::exception& e) {
        result.status = HealthStatus::UNKNOWN;
        result.message = "Failed to check memory usage: " + std::string(e.what());
        result.details["error"] = e.what();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    result.response_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    return result;
}

// DiskSpaceHealthCheck implementation
DiskSpaceHealthCheck::DiskSpaceHealthCheck(const DiskConfig& config)
    : config_(config) {}

std::tuple<size_t, size_t> DiskSpaceHealthCheck::get_disk_usage(const std::string& path) const {
    struct statvfs stat;
    if (statvfs(path.c_str(), &stat) != 0) {
        throw std::runtime_error("Failed to get disk usage for path: " + path);
    }
    
    size_t total_mb = (stat.f_blocks * stat.f_frsize) / (1024 * 1024);
    size_t free_mb = (stat.f_bavail * stat.f_frsize) / (1024 * 1024);
    
    return std::make_tuple(total_mb, free_mb);
}

HealthCheckResult DiskSpaceHealthCheck::check_health() {
    HealthCheckResult result;
    result.component_name = get_component_name();
    result.timestamp = std::chrono::system_clock::now();
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        if (!std::filesystem::exists(config_.path)) {
            result.status = HealthStatus::UNHEALTHY;
            result.message = "Path does not exist: " + config_.path;
            result.details["path"] = config_.path;
            result.details["exists"] = "false";
        } else {
            auto [total_mb, free_mb] = get_disk_usage(config_.path);
            double free_ratio = static_cast<double>(free_mb) / total_mb;
            
            result.details["path"] = config_.path;
            result.details["total_mb"] = std::to_string(total_mb);
            result.details["free_mb"] = std::to_string(free_mb);
            result.details["free_ratio"] = std::to_string(free_ratio);
            result.details["min_free_mb"] = std::to_string(config_.min_free_space_mb);
            
            if (free_mb < config_.min_free_space_mb || free_ratio < config_.critical_threshold) {
                result.status = HealthStatus::UNHEALTHY;
                result.message = "Disk space critical: " + std::to_string(free_mb) + 
                               "MB free (" + std::to_string(free_ratio * 100) + "%)";
            } else if (free_ratio < config_.warning_threshold) {
                result.status = HealthStatus::DEGRADED;
                result.message = "Disk space low: " + std::to_string(free_mb) + 
                               "MB free (" + std::to_string(free_ratio * 100) + "%)";
            } else {
                result.status = HealthStatus::HEALTHY;
                result.message = "Disk space sufficient: " + std::to_string(free_mb) + 
                               "MB free (" + std::to_string(free_ratio * 100) + "%)";
            }
        }
        
    } catch (const std::exception& e) {
        result.status = HealthStatus::UNKNOWN;
        result.message = "Failed to check disk space: " + std::string(e.what());
        result.details["error"] = e.what();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    result.response_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    return result;
}

// HealthMonitor implementation
HealthMonitor::HealthMonitor()
    : config_{} {}

HealthMonitor::HealthMonitor(const Config& config)
    : config_(config) {}

HealthMonitor::~HealthMonitor() {
    stop();
}

void HealthMonitor::start() {
    if (running_.load() || !config_.enabled) {
        return;
    }
    
    running_ = true;
    monitor_thread_ = std::make_unique<std::thread>(&HealthMonitor::monitor_loop, this);
}

void HealthMonitor::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_ = false;
    monitor_cv_.notify_all();
    
    if (monitor_thread_ && monitor_thread_->joinable()) {
        monitor_thread_->join();
    }
}

void HealthMonitor::register_health_check(std::shared_ptr<IHealthCheck> health_check) {
    std::lock_guard<std::mutex> lock(health_checks_mutex_);
    health_checks_[health_check->get_component_name()] = health_check;
}

void HealthMonitor::unregister_health_check(const std::string& component_name) {
    std::lock_guard<std::mutex> lock(health_checks_mutex_);
    health_checks_.erase(component_name);
}

HealthStatus HealthMonitor::get_overall_health() const {
    std::lock_guard<std::mutex> lock(results_mutex_);
    
    if (last_results_.empty()) {
        return HealthStatus::UNKNOWN;
    }
    
    bool has_unhealthy = false;
    bool has_degraded = false;
    
    for (const auto& [name, result] : last_results_) {
        switch (result.status) {
            case HealthStatus::UNHEALTHY:
                has_unhealthy = true;
                break;
            case HealthStatus::DEGRADED:
                has_degraded = true;
                break;
            case HealthStatus::UNKNOWN:
                return HealthStatus::UNKNOWN;
            case HealthStatus::HEALTHY:
                break;
        }
    }
    
    if (has_unhealthy) {
        return HealthStatus::UNHEALTHY;
    } else if (has_degraded) {
        return HealthStatus::DEGRADED;
    } else {
        return HealthStatus::HEALTHY;
    }
}

std::unordered_map<std::string, HealthCheckResult> HealthMonitor::get_health_results() const {
    std::lock_guard<std::mutex> lock(results_mutex_);
    return last_results_;
}

std::optional<HealthCheckResult> HealthMonitor::get_component_health(const std::string& component_name) const {
    std::lock_guard<std::mutex> lock(results_mutex_);
    auto it = last_results_.find(component_name);
    if (it != last_results_.end()) {
        return it->second;
    }
    return std::nullopt;
}

std::unordered_map<std::string, HealthCheckResult> HealthMonitor::perform_health_check() {
    std::unordered_map<std::string, HealthCheckResult> results;
    
    // Copy health checks to avoid holding lock during checks
    std::unordered_map<std::string, std::shared_ptr<IHealthCheck>> checks;
    {
        std::lock_guard<std::mutex> lock(health_checks_mutex_);
        checks = health_checks_;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (const auto& [name, health_check] : checks) {
        try {
            results[name] = perform_single_check(health_check);
        } catch (const std::exception& e) {
            HealthCheckResult error_result;
            error_result.component_name = name;
            error_result.status = HealthStatus::UNKNOWN;
            error_result.message = "Health check failed: " + std::string(e.what());
            error_result.timestamp = std::chrono::system_clock::now();
            error_result.details["error"] = e.what();
            results[name] = error_result;
        }
    }
    
    // Update stored results
    {
        std::lock_guard<std::mutex> lock(results_mutex_);
        last_results_ = results;
    }
    
    // Update statistics
    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        stats_.total_checks_performed += results.size();
        stats_.last_check_time = std::chrono::system_clock::now();
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto check_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        if (stats_.total_checks_performed > 0) {
            stats_.avg_check_duration = std::chrono::milliseconds(
                (stats_.avg_check_duration.count() + check_duration.count()) / 2);
        } else {
            stats_.avg_check_duration = check_duration;
        }
        
        // Count component statuses
        stats_.healthy_components = 0;
        stats_.degraded_components = 0;
        stats_.unhealthy_components = 0;
        stats_.unknown_components = 0;
        
        for (const auto& [name, result] : results) {
            switch (result.status) {
                case HealthStatus::HEALTHY:
                    stats_.healthy_components++;
                    break;
                case HealthStatus::DEGRADED:
                    stats_.degraded_components++;
                    break;
                case HealthStatus::UNHEALTHY:
                    stats_.unhealthy_components++;
                    break;
                case HealthStatus::UNKNOWN:
                    stats_.unknown_components++;
                    break;
            }
        }
    }
    
    // Export metrics if configured
    if (config_.export_metrics) {
        update_health_metrics(results);
    }
    
    // Process alerts if configured
    if (config_.enable_alerts) {
        process_health_alerts(results);
    }
    
    return results;
}

void HealthMonitor::set_metrics_collector(std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector) {
    metrics_collector_ = metrics_collector;
}

std::string HealthMonitor::export_prometheus_metrics() const {
    std::ostringstream oss;
    
    auto results = get_health_results();
    auto stats = get_statistics();
    
    // Health status metrics
    oss << "# HELP " << config_.metrics_prefix << "_component_status Component health status (0=unknown, 1=healthy, 2=degraded, 3=unhealthy)\n";
    oss << "# TYPE " << config_.metrics_prefix << "_component_status gauge\n";
    
    for (const auto& [name, result] : results) {
        int status_value = static_cast<int>(result.status);
        oss << config_.metrics_prefix << "_component_status{component=\"" << name << "\"} " << status_value << "\n";
    }
    
    // Response time metrics
    oss << "# HELP " << config_.metrics_prefix << "_check_duration_ms Health check duration in milliseconds\n";
    oss << "# TYPE " << config_.metrics_prefix << "_check_duration_ms gauge\n";
    
    for (const auto& [name, result] : results) {
        oss << config_.metrics_prefix << "_check_duration_ms{component=\"" << name << "\"} " << result.response_time.count() << "\n";
    }
    
    // Overall statistics
    oss << "# HELP " << config_.metrics_prefix << "_total_checks_total Total number of health checks performed\n";
    oss << "# TYPE " << config_.metrics_prefix << "_total_checks_total counter\n";
    oss << config_.metrics_prefix << "_total_checks_total " << stats.total_checks_performed << "\n";
    
    oss << "# HELP " << config_.metrics_prefix << "_components_by_status Number of components by status\n";
    oss << "# TYPE " << config_.metrics_prefix << "_components_by_status gauge\n";
    oss << config_.metrics_prefix << "_components_by_status{status=\"healthy\"} " << stats.healthy_components << "\n";
    oss << config_.metrics_prefix << "_components_by_status{status=\"degraded\"} " << stats.degraded_components << "\n";
    oss << config_.metrics_prefix << "_components_by_status{status=\"unhealthy\"} " << stats.unhealthy_components << "\n";
    oss << config_.metrics_prefix << "_components_by_status{status=\"unknown\"} " << stats.unknown_components << "\n";
    
    return oss.str();
}

HealthMonitor::Statistics HealthMonitor::get_statistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void HealthMonitor::monitor_loop() {
    while (running_.load()) {
        try {
            perform_health_check();
        } catch (const std::exception& e) {
            // Log error but continue monitoring
        }
        
        std::unique_lock<std::mutex> lock(monitor_mutex_);
        monitor_cv_.wait_for(lock, config_.check_interval, [this] { return !running_.load(); });
    }
}

HealthCheckResult HealthMonitor::perform_single_check(std::shared_ptr<IHealthCheck> health_check) {
    try {
        // Set up timeout for health check
        std::future<HealthCheckResult> future_result = std::async(std::launch::async, [health_check]() {
            return health_check->check_health();
        });
        
        // Wait for result with timeout
        auto status = future_result.wait_for(config_.check_timeout);
        if (status == std::future_status::timeout) {
            HealthCheckResult timeout_result;
            timeout_result.status = HealthStatus::UNHEALTHY;
            timeout_result.message = "Health check timed out after " + 
                std::to_string(config_.check_timeout.count()) + "ms";
            timeout_result.response_time = config_.check_timeout;
            return timeout_result;
        }
        
        return future_result.get();
        
    } catch (const std::exception& e) {
        HealthCheckResult error_result;
        error_result.status = HealthStatus::UNHEALTHY;
        error_result.message = "Health check failed with exception: " + std::string(e.what());
        error_result.response_time = std::chrono::milliseconds(0);
        return error_result;
    }
}

void HealthMonitor::update_health_metrics(const std::unordered_map<std::string, HealthCheckResult>& results) {
    if (!metrics_collector_) {
        return;
    }
    
    for (const auto& [name, result] : results) {
        // Update health status metric
        std::unordered_map<std::string, std::string> labels = {{"component", name}};
        double status_value = static_cast<double>(result.status);
        metrics_collector_->set_gauge(config_.metrics_prefix + "_component_status", status_value, labels);
        
        // Update response time metric
        metrics_collector_->set_gauge(config_.metrics_prefix + "_check_duration_ms", 
                                     result.response_time.count(), labels);
    }
}

void HealthMonitor::process_health_alerts(const std::unordered_map<std::string, HealthCheckResult>& results) {
    try {
        // Check if we have any alert endpoints configured
        if (config_.alert_endpoints.empty()) {
            return;
        }
        
        // Process each health check result for alerting
        for (const auto& [component_name, result] : results) {
            // Only alert on unhealthy or degraded status
            if (result.status == HealthStatus::HEALTHY) {
                continue;
            }
            
            // Create alert message
            std::string alert_message = "Component " + component_name + " is " + 
                health_status_to_string(result.status) + ": " + result.message;
            
            // Send alert to all configured endpoints
            for (const auto& endpoint : config_.alert_endpoints) {
                try {
                    // Create HTTP client for alerting
                    auto client = HttpClientFactory::create_client();
                    if (!client) {
                        continue;
                    }
                    
                    // Prepare alert payload
                    nlohmann::json alert_payload;
                    alert_payload["component"] = component_name;
                    alert_payload["status"] = health_status_to_string(result.status);
                    alert_payload["message"] = alert_message;
                    alert_payload["response_time_ms"] = result.response_time.count();
                    alert_payload["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
                        std::chrono::system_clock::now().time_since_epoch()).count();
                    
                    // Send alert
                    HttpClient::Request request;
                    request.method = HttpClient::Method::POST;
                    request.url = endpoint;
                    request.body = alert_payload.dump();
                    request.headers["Content-Type"] = "application/json";
                    request.timeout_seconds = 10;
                    
                    auto response = client->make_request(request);
                    if (response.status_code < 200 || response.status_code >= 300) {
                        // Log failed alert delivery
                        std::cerr << "[HEALTH] Failed to send alert to " << endpoint 
                                  << ": HTTP " << response.status_code << std::endl;
                    }
                    
                } catch (const std::exception& e) {
                    std::cerr << "[HEALTH] Exception sending alert to " << endpoint 
                              << ": " << e.what() << std::endl;
                }
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[HEALTH] Exception in process_health_alerts: " << e.what() << std::endl;
    }
}

std::string HealthMonitor::health_status_to_string(HealthStatus status) const {
    switch (status) {
        case HealthStatus::HEALTHY: return "healthy";
        case HealthStatus::DEGRADED: return "degraded";
        case HealthStatus::UNHEALTHY: return "unhealthy";
        case HealthStatus::UNKNOWN: return "unknown";
    }
    return "unknown";
}

} // namespace omop::common