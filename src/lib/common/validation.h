/**
 * @file validation.h
 * @brief Data validation framework for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains the validation framework for ensuring data quality
 * and integrity throughout the ETL process.
 */

#pragma once

#include "exceptions.h"
#include "logging.h"

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>
#include <variant>
#include <optional>
#include <chrono>
#include <regex>
#include <any>
#include <format>

namespace omop::common {

/**
 * @brief Enumeration of validation rule types
 */
enum class ValidationType {
    NOT_NULL,           ///< Field must not be null
    NOT_EMPTY,          ///< Field must not be empty
    UNIQUE,             ///< Field must be unique
    IN_LIST,            ///< Field must be in specified list
    REGEX,              ///< Field must match regex pattern
    DATE_RANGE,         ///< Date must be within range
    NUMERIC_RANGE,      ///< Number must be within range
    GREATER_THAN,       ///< Number must be greater than value
    LESS_THAN,          ///< Number must be less than value
    BETWEEN,            ///< Value must be between two values
    BEFORE,             ///< Date must be before another date
    AFTER,              ///< Date must be after another date
    LENGTH,             ///< String length validation
    CUSTOM,             ///< Custom validation function
    NOT_ZERO,           ///< Number must not be zero
    NOT_FUTURE_DATE,    ///< Date must not be in the future
    FOREIGN_KEY,        ///< Foreign key constraint
    COMPOSITE_KEY,      ///< Composite key validation
    CONDITIONAL         ///< Conditional validation based on other fields
};

class ValidationResult {
public:
    struct ValidationError {
        std::string field_name;
        std::string error_message;
        std::string rule_name;
        std::string severity{"error"};  // "error", "warning", "info"
        std::chrono::system_clock::time_point timestamp{std::chrono::system_clock::now()};
    };

    struct ValidationWarning {
        std::string field_name;
        std::string warning_message;
        std::string rule_name;
        std::string severity{"warning"};  // "warning", "info"
        std::chrono::system_clock::time_point timestamp{std::chrono::system_clock::now()};
    };

    ValidationResult() = default;

    void add_error(ValidationError error) {
        errors_.push_back(std::move(error));
        is_valid_ = false;
    }

    void add_error(const std::string& field_name,
                  const std::string& error_message,
                  const std::string& rule_name,
                  const std::string& severity = "error") {
        errors_.push_back({field_name, error_message, rule_name, severity, std::chrono::system_clock::now()});
        is_valid_ = false;
    }

    void add_warning(ValidationWarning warning) {
        warnings_.push_back(std::move(warning));
    }

    void add_warning(const std::string& field_name,
                    const std::string& warning_message,
                    const std::string& rule_name,
                    const std::string& severity = "warning") {
        warnings_.push_back({field_name, warning_message, rule_name, severity, std::chrono::system_clock::now()});
    }

    /**
     * @brief Add informational message
     * @param field_name Field name (or general context if not field-specific)
     * @param info_message Informational message
     * @param rule_name Rule or context name
     * @param severity Severity level (default: "info")
     */
    void add_info(const std::string& field_name,
                  const std::string& info_message,
                  const std::string& rule_name,
                  const std::string& severity = "info") {
        // Store info messages as warnings with info severity for now
        // In a full implementation, we might want a separate info_ vector
        warnings_.push_back({field_name, info_message, rule_name, severity, std::chrono::system_clock::now()});
    }

    [[nodiscard]] bool is_valid() const noexcept { return is_valid_; }

    [[nodiscard]] const std::vector<ValidationError>& errors() const noexcept {
        return errors_;
    }

    [[nodiscard]] const std::vector<ValidationWarning>& warnings() const noexcept {
        return warnings_;
    }

    [[nodiscard]] size_t error_count() const noexcept { return errors_.size(); }

    [[nodiscard]] size_t warning_count() const noexcept { return warnings_.size(); }

    [[nodiscard]] std::string error_messages() const {
        if (errors_.empty()) {
            return "";
        }
        std::string result;
        for (const auto& error : errors_) {
            result += std::format("Field '{}': {} (rule: {}, severity: {})\n",
                                 error.field_name,
                                 error.error_message,
                                 error.rule_name,
                                 error.severity);
        }
        return result;
    }

    [[nodiscard]] std::string warning_messages() const {
        if (warnings_.empty()) {
            return "";
        }
        std::string result;
        for (const auto& warning : warnings_) {
            result += std::format("Field '{}': {} (rule: {}, severity: {})\n",
                                 warning.field_name,
                                 warning.warning_message,
                                 warning.rule_name,
                                 warning.severity);
        }
        return result;
    }

    void merge(const ValidationResult& other) {
        if (!other.is_valid()) {
            is_valid_ = false;
            errors_.insert(errors_.end(), other.errors().begin(), other.errors().end());
        }
        warnings_.insert(warnings_.end(), other.warnings().begin(), other.warnings().end());
    }

    // Enhanced functionality for performance tracking
    void set_validation_timestamp(std::chrono::system_clock::time_point timestamp) {
        validation_timestamp_ = timestamp;
    }

    [[nodiscard]] std::chrono::system_clock::time_point get_validation_timestamp() const noexcept {
        return validation_timestamp_;
    }

    void set_validation_duration(std::chrono::microseconds duration) {
        validation_duration_ = duration;
    }

    [[nodiscard]] std::chrono::microseconds get_validation_duration() const noexcept {
        return validation_duration_;
    }

    // Enhanced functionality for caching
    void set_cache_metadata(bool from_cache, const std::string& cache_key) {
        from_cache_ = from_cache;
        cache_key_ = cache_key;
    }

    [[nodiscard]] bool is_from_cache() const noexcept { return from_cache_; }
    [[nodiscard]] const std::string& get_cache_key() const noexcept { return cache_key_; }

    // Enhanced functionality for quality scoring
    void set_quality_score(double score) { quality_score_ = score; }
    [[nodiscard]] double get_quality_score() const noexcept { return quality_score_; }

    void add_quality_indicator(const std::string& indicator) {
        quality_indicators_.push_back(indicator);
    }

    [[nodiscard]] const std::vector<std::string>& get_quality_indicators() const noexcept {
        return quality_indicators_;
    }

private:
    bool is_valid_{true};
    std::vector<ValidationError> errors_;
    std::vector<ValidationWarning> warnings_;
    
    // Enhanced metadata
    std::chrono::system_clock::time_point validation_timestamp_{std::chrono::system_clock::now()};
    std::chrono::microseconds validation_duration_{0};
    bool from_cache_{false};
    std::string cache_key_;
    double quality_score_{100.0};
    std::vector<std::string> quality_indicators_;
};

/**
 * @brief Base class for validation rules
 */
class ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param type Type of validation
     * @param error_message Custom error message
     */
    ValidationRule(const std::string& field_name,
                  ValidationType type,
                  const std::string& error_message = "");

    /**
     * @brief Virtual destructor
     */
    virtual ~ValidationRule() = default;

    /**
     * @brief Validate a value
     * @param value The value to validate
     * @param record The entire record (for context)
     * @return true if validation passes, false otherwise
     */
    virtual bool validate(const std::any& value,
                         const std::unordered_map<std::string, std::any>& record) const = 0;

    /**
     * @brief Get the error message for validation failure
     * @return Error message string
     */
    virtual std::string getErrorMessage() const;

    /**
     * @brief Get the field name
     * @return Field name string
     */
    const std::string& getFieldName() const { return field_name_; }

    /**
     * @brief Get the validation type
     * @return Validation type enum
     */
    ValidationType getType() const { return type_; }

protected:
    std::string field_name_;      ///< Name of the field to validate
    ValidationType type_;         ///< Type of validation
    std::string error_message_;   ///< Custom error message
};

/**
 * @brief Not null validation rule
 */
class NotNullRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     */
    explicit NotNullRule(const std::string& field_name);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;
};

/**
 * @brief In list validation rule
 */
template<typename T>
class InListRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param allowed_values List of allowed values
     */
    InListRule(const std::string& field_name, const std::vector<T>& allowed_values);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::vector<T> allowed_values_;  ///< List of allowed values
};

/**
 * @brief Date range validation rule
 */
class DateRangeRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param min_date Minimum allowed date (optional)
     * @param max_date Maximum allowed date (optional)
     */
    DateRangeRule(const std::string& field_name,
                  const std::optional<std::chrono::system_clock::time_point>& min_date,
                  const std::optional<std::chrono::system_clock::time_point>& max_date);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::optional<std::chrono::system_clock::time_point> min_date_;  ///< Minimum date
    std::optional<std::chrono::system_clock::time_point> max_date_;  ///< Maximum date
};

/**
 * @brief Numeric range validation rule
 */
template<typename T>
class NumericRangeRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param min_value Minimum allowed value (optional)
     * @param max_value Maximum allowed value (optional)
     */
    NumericRangeRule(const std::string& field_name,
                     const std::optional<T>& min_value,
                     const std::optional<T>& max_value);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::optional<T> min_value_;  ///< Minimum value
    std::optional<T> max_value_;  ///< Maximum value
};

/**
 * @brief Regular expression validation rule
 */
class RegexRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param pattern Regular expression pattern
     */
    RegexRule(const std::string& field_name, const std::string& pattern);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::regex pattern_;  ///< Regular expression pattern
};

/**
 * @brief Custom validation rule with user-defined function
 */
class CustomRule : public ValidationRule {
public:
    using ValidationFunction = std::function<bool(const std::any&, const std::unordered_map<std::string, std::any>&)>;

    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param validator Custom validation function
     * @param error_message Custom error message
     */
    CustomRule(const std::string& field_name,
               ValidationFunction validator,
               const std::string& error_message = "");

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    ValidationFunction validator_;  ///< Custom validation function
};

/**
 * @brief Basic validation engine for applying validation rules
 */
class BasicValidationEngine {
public:
    /**
     * @brief Constructor
     * @param logger Logger instance
     */
    explicit BasicValidationEngine(std::shared_ptr<Logger> logger = nullptr);

    /**
     * @brief Add a validation rule
     * @param rule Validation rule to add
     */
    void addRule(std::unique_ptr<ValidationRule> rule);

    /**
     * @brief Validate a single record
     * @param record Record to validate
     * @return Validation result
     */
    ValidationResult validateRecord(const std::unordered_map<std::string, std::any>& record);

    /**
     * @brief Validate a batch of records
     * @param records Records to validate
     * @param stop_on_error Stop validation on first error
     * @return Validation result
     */
    ValidationResult validateBatch(const std::vector<std::unordered_map<std::string, std::any>>& records,
                                  bool stop_on_error = false);

    /**
     * @brief Clear all validation rules
     */
    void clearRules();

    /**
     * @brief Get the number of rules
     * @return Number of validation rules
     */
    size_t getRuleCount() const { return rules_.size(); }

    /**
     * @brief Enable or disable specific validation types
     * @param type Validation type to enable/disable
     * @param enabled true to enable, false to disable
     */
    void setValidationTypeEnabled(ValidationType type, bool enabled);

    /**
     * @brief Enable validation result caching
     * @param enabled Enable/disable caching
     */
    void enable_caching(bool enabled);

    /**
     * @brief Set cache size for validation results
     * @param cache_size Maximum number of cached results
     */
    void set_cache_size(size_t cache_size);

    /**
     * @brief Compile validation rules for performance
     */
    void compile_rules();

    /**
     * @brief Validate a batch of records (alias for validateBatch)
     * @param batch_data Records to validate
     * @return Validation result
     */
    ValidationResult validate_batch(const std::vector<std::unordered_map<std::string, std::any>>& batch_data) {
        return validateBatch(batch_data);
    }

private:
    std::vector<std::unique_ptr<ValidationRule>> rules_;  ///< List of validation rules
    std::unordered_map<ValidationType, bool> enabled_types_;  ///< Enabled validation types
    std::shared_ptr<Logger> logger_;  ///< Logger instance
    
    // Caching and optimization members
    bool caching_enabled_{false};
    size_t cache_size_{1000};
    bool rules_compiled_{false};
};

/**
 * @brief Factory class for creating validation rules from configuration
 */
class ValidationRuleFactory {
public:
    /**
     * @brief Create a validation rule from configuration
     * @param config Configuration map
     * @return Unique pointer to validation rule
     */
    static std::unique_ptr<ValidationRule> createRule(const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Create validation engine from YAML configuration
     * @param yaml_config YAML configuration string
     * @return Unique pointer to validation engine
     */
    static std::unique_ptr<BasicValidationEngine> createEngineFromYaml(const std::string& yaml_config);
};

} // namespace omop::common
