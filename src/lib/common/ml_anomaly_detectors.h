/**
 * @file ml_anomaly_detectors.h
 * @brief Advanced ML-based anomaly detection algorithms (LSTM, Isolation Forest)
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#pragma once

#include "anomaly_detector.h"
#include <memory>
#include <vector>
#include <random>
#include <queue>
#include <unordered_set>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <functional>

namespace omop::common::ml {

/**
 * @brief Isolation Forest anomaly detector
 * 
 * Implementation of the Isolation Forest algorithm for unsupervised anomaly detection.
 * The algorithm isolates anomalies by randomly selecting features and split values,
 * with anomalies requiring fewer splits to isolate.
 */
class IsolationForestDetector {
public:
    /**
     * @brief Configuration for Isolation Forest
     */
    struct Config {
        size_t n_estimators{100};           ///< Number of isolation trees
        size_t max_samples{256};            ///< Number of samples to use for each tree
        double contamination{0.1};          ///< Expected proportion of outliers
        size_t max_features{1};             ///< Number of features to use per split
        int random_state{42};               ///< Random seed for reproducibility
        double threshold_percentile{95.0};  ///< Percentile for anomaly threshold
        size_t min_data_points{50};         ///< Minimum points needed for training
        double confidence_threshold{0.6};   ///< Minimum confidence for anomaly detection
    };

    /**
     * @brief Constructor
     * @param config Configuration parameters
     */
    explicit IsolationForestDetector(const Config& config = Config{});

    /**
     * @brief Detect anomalies in time series using Isolation Forest
     * @param series Time series to analyze
     * @param analysis_window_hours Hours of data to analyze
     * @return Vector of detected anomalies
     */
    std::vector<Anomaly> detect_anomalies(
        const TimeSeries& series,
        double analysis_window_hours = 1.0);

    /**
     * @brief Train model on historical data
     * @param training_data Historical time series data
     * @return true if training successful
     */
    bool train_model(const std::vector<std::vector<double>>& training_data);

    /**
     * @brief Check if model is trained
     * @return true if model is ready for prediction
     */
    bool is_trained() const { return model_trained_; }

    /**
     * @brief Get model statistics
     */
    struct ModelStats {
        size_t n_trees{0};
        size_t training_samples{0};
        double contamination_rate{0.0};
        double decision_threshold{0.0};
        std::chrono::system_clock::time_point last_training_time;
    };

    ModelStats get_model_stats() const;

private:
    /**
     * @brief Isolation Tree node
     */
    struct IsolationTreeNode {
        bool is_leaf{false};
        size_t feature_index{0};
        double split_value{0.0};
        std::unique_ptr<IsolationTreeNode> left;
        std::unique_ptr<IsolationTreeNode> right;
        size_t size{0};  // Number of samples in this node
        double path_length{0.0};
    };

    /**
     * @brief Isolation Tree
     */
    class IsolationTree {
    public:
        IsolationTree(size_t max_samples, size_t max_features, int random_state);
        
        void fit(const std::vector<std::vector<double>>& data);
        double path_length(const std::vector<double>& sample) const;
        
    private:
        size_t max_samples_;
        size_t max_features_;
        std::mt19937 random_gen_;
        std::unique_ptr<IsolationTreeNode> root_;
        
        std::unique_ptr<IsolationTreeNode> build_tree(
            const std::vector<std::vector<double>>& data,
            const std::vector<size_t>& indices,
            size_t current_depth,
            size_t max_depth);
        
        double calculate_path_length(
            const std::vector<double>& sample,
            const IsolationTreeNode* node,
            double current_length) const;
        
        double average_path_length(size_t n) const;
    };

    Config config_;
    std::vector<std::unique_ptr<IsolationTree>> trees_;
    double decision_threshold_;
    bool model_trained_{false};
    std::mt19937 random_gen_;
    
    // Model statistics
    mutable std::mutex stats_mutex_;
    ModelStats model_stats_;

    /**
     * @brief Extract features from time series points
     * @param points Time series points
     * @return Feature matrix
     */
    std::vector<std::vector<double>> extract_features(
        const std::vector<TimeSeriesPoint>& points) const;

    /**
     * @brief Calculate anomaly score for a sample
     * @param sample Feature vector
     * @return Anomaly score (higher = more anomalous)
     */
    double calculate_anomaly_score(const std::vector<double>& sample) const;

    /**
     * @brief Convert anomaly score to confidence
     * @param score Anomaly score
     * @return Confidence value (0-1)
     */
    double score_to_confidence(double score) const;

    /**
     * @brief Determine anomaly severity based on score
     * @param score Anomaly score
     * @return Severity level
     */
    AnomalySeverity determine_severity(double score) const;
};

/**
 * @brief LSTM Autoencoder anomaly detector
 * 
 * Implementation of LSTM-based autoencoder for time series anomaly detection.
 * Uses reconstruction error as anomaly score - normal patterns reconstruct well,
 * anomalies have high reconstruction error.
 */
class LSTMAutoencoderDetector {
public:
    /**
     * @brief Configuration for LSTM Autoencoder
     */
    struct Config {
        size_t sequence_length{50};         ///< Input sequence length
        size_t encoding_dim{32};            ///< Encoding dimension (bottleneck)
        size_t lstm_units{64};              ///< LSTM hidden units
        double learning_rate{0.001};        ///< Learning rate
        size_t epochs{50};                  ///< Training epochs
        size_t batch_size{32};              ///< Batch size for training
        double validation_split{0.2};       ///< Validation data split
        double reconstruction_threshold{2.0}; ///< Reconstruction error threshold (std devs)
        size_t min_training_samples{1000};  ///< Minimum samples for training
        double confidence_threshold{0.7};   ///< Minimum confidence for detection
        bool normalize_data{true};          ///< Normalize input data
        double early_stopping_patience{10}; ///< Early stopping patience
    };

    /**
     * @brief Constructor
     * @param config Configuration parameters
     */
    explicit LSTMAutoencoderDetector(const Config& config = Config{});

    /**
     * @brief Detect anomalies using LSTM autoencoder
     * @param series Time series to analyze
     * @param analysis_window_hours Hours of data to analyze
     * @return Vector of detected anomalies
     */
    std::vector<Anomaly> detect_anomalies(
        const TimeSeries& series,
        double analysis_window_hours = 1.0);

    /**
     * @brief Train LSTM autoencoder model
     * @param training_data Historical time series data
     * @return true if training successful
     */
    bool train_model(const std::vector<double>& training_data);

    /**
     * @brief Check if model is trained
     * @return true if model is ready for prediction
     */
    bool is_trained() const { return model_trained_; }

    /**
     * @brief Get model statistics
     */
    struct ModelStats {
        size_t sequence_length{0};
        size_t encoding_dim{0};
        size_t training_samples{0};
        double final_training_loss{0.0};
        double reconstruction_threshold{0.0};
        std::vector<double> training_history;
        std::chrono::system_clock::time_point last_training_time;
        size_t total_parameters{0};
    };

    ModelStats get_model_stats() const;

private:
    /**
     * @brief Simple LSTM cell implementation
     */
    class LSTMCell {
    public:
        LSTMCell(size_t input_size, size_t hidden_size);
        
        std::pair<std::vector<double>, std::vector<double>> forward(
            const std::vector<double>& input,
            const std::vector<double>& hidden,
            const std::vector<double>& cell) const;
        
        void update_weights(const std::vector<std::vector<double>>& gradients, double learning_rate);
        
        size_t get_parameter_count() const;
        
    private:
        size_t input_size_;
        size_t hidden_size_;
        
        // LSTM weights (forget, input, candidate, output gates)
        std::vector<std::vector<double>> Wf_, Wi_, Wc_, Wo_;  // Input weights
        std::vector<std::vector<double>> Uf_, Ui_, Uc_, Uo_;  // Hidden weights  
        std::vector<double> bf_, bi_, bc_, bo_;               // Biases
        
        std::vector<double> sigmoid(const std::vector<double>& x) const;
        std::vector<double> tanh(const std::vector<double>& x) const;
        std::vector<double> add_vectors(const std::vector<double>& a, const std::vector<double>& b) const;
        std::vector<double> multiply_vectors(const std::vector<double>& a, const std::vector<double>& b) const;
        std::vector<double> matrix_vector_multiply(const std::vector<std::vector<double>>& matrix, 
                                                  const std::vector<double>& vector) const;
    };

    /**
     * @brief Simple dense layer implementation
     */
    class DenseLayer {
    public:
        DenseLayer(size_t input_size, size_t output_size);
        
        std::vector<double> forward(const std::vector<double>& input) const;
        void update_weights(const std::vector<std::vector<double>>& gradients, double learning_rate);
        
        size_t get_parameter_count() const;
        
    private:
        size_t input_size_;
        size_t output_size_;
        std::vector<std::vector<double>> weights_;
        std::vector<double> biases_;
        
        std::vector<double> matrix_vector_multiply(const std::vector<std::vector<double>>& matrix,
                                                  const std::vector<double>& vector) const;
    };

    Config config_;
    bool model_trained_{false};
    
    // Model components
    std::unique_ptr<LSTMCell> encoder_lstm_;
    std::unique_ptr<DenseLayer> encoder_dense_;
    std::unique_ptr<LSTMCell> decoder_lstm_;
    std::unique_ptr<DenseLayer> decoder_dense_;
    
    // Training statistics
    double reconstruction_threshold_;
    std::vector<double> training_losses_;
    
    // Data normalization
    double data_mean_{0.0};
    double data_std_{1.0};
    
    // Model statistics
    mutable std::mutex stats_mutex_;
    ModelStats model_stats_;
    
    /**
     * @brief Create training sequences from time series data
     * @param data Raw time series values
     * @return Vector of input sequences
     */
    std::vector<std::vector<double>> create_sequences(const std::vector<double>& data) const;
    
    /**
     * @brief Normalize data for training
     * @param data Raw data
     * @return Normalized data
     */
    std::vector<double> normalize_data(const std::vector<double>& data);
    
    /**
     * @brief Denormalize data after prediction
     * @param normalized_data Normalized data
     * @return Original scale data
     */
    std::vector<double> denormalize_data(const std::vector<double>& normalized_data) const;
    
    /**
     * @brief Forward pass through autoencoder
     * @param input Input sequence
     * @return Reconstructed sequence
     */
    std::vector<double> predict(const std::vector<double>& input) const;
    
    /**
     * @brief Calculate reconstruction error
     * @param original Original sequence
     * @param reconstructed Reconstructed sequence
     * @return Reconstruction error (MSE)
     */
    double calculate_reconstruction_error(const std::vector<double>& original,
                                        const std::vector<double>& reconstructed) const;
    
    /**
     * @brief Convert reconstruction error to confidence
     * @param error Reconstruction error
     * @return Confidence value (0-1)
     */
    double error_to_confidence(double error) const;
    
    /**
     * @brief Determine anomaly severity based on error
     * @param error Reconstruction error
     * @return Severity level
     */
    AnomalySeverity determine_severity(double error) const;
    
    /**
     * @brief Simple training loop implementation
     * @param sequences Training sequences
     * @return Training success
     */
    bool train_autoencoder(const std::vector<std::vector<double>>& sequences);
    
    /**
     * @brief Initialize model weights
     */
    void initialize_weights();
};

/**
 * @brief Advanced ML anomaly detector ensemble
 * 
 * Combines Isolation Forest and LSTM Autoencoder for comprehensive anomaly detection.
 */
class AdvancedMLAnomalyDetector {
public:
    /**
     * @brief Configuration for advanced ML ensemble
     */
    struct Config {
        bool enable_isolation_forest{true};     ///< Enable Isolation Forest
        bool enable_lstm_autoencoder{true};     ///< Enable LSTM Autoencoder
        double ensemble_weight_isolation{0.6};  ///< Weight for Isolation Forest
        double ensemble_weight_lstm{0.4};       ///< Weight for LSTM
        double consensus_threshold{0.5};        ///< Minimum consensus for anomaly
        bool auto_retrain{true};                ///< Enable automatic retraining
        std::chrono::hours retrain_interval{24}; ///< Retraining interval
        size_t min_retrain_samples{1000};       ///< Minimum samples for retraining
        
        // Individual detector configs
        IsolationForestDetector::Config isolation_config;
        LSTMAutoencoderDetector::Config lstm_config;
    };

    /**
     * @brief Constructor
     * @param config Configuration parameters
     */
    explicit AdvancedMLAnomalyDetector(const Config& config = Config{});

    /**
     * @brief Detect anomalies using ML ensemble
     * @param series Time series to analyze
     * @param analysis_window_hours Hours of data to analyze
     * @return Vector of detected anomalies
     */
    std::vector<Anomaly> detect_anomalies(
        const TimeSeries& series,
        double analysis_window_hours = 1.0);

    /**
     * @brief Train both ML models
     * @param training_data Historical time series data
     * @return true if training successful
     */
    bool train_models(const std::vector<TimeSeriesPoint>& training_data);

    /**
     * @brief Check if models are trained and ready
     * @return true if both models are ready
     */
    bool is_trained() const;

    /**
     * @brief Get ensemble statistics
     */
    struct EnsembleStats {
        bool isolation_forest_trained{false};
        bool lstm_autoencoder_trained{false};
        IsolationForestDetector::ModelStats isolation_stats;
        LSTMAutoencoderDetector::ModelStats lstm_stats;
        size_t total_parameters{0};
        std::chrono::system_clock::time_point last_training_time;
        std::vector<double> ensemble_weights;
    };

    EnsembleStats get_ensemble_stats() const;

    /**
     * @brief Force model retraining
     * @param training_data New training data
     * @return true if retraining successful
     */
    bool retrain_models(const std::vector<TimeSeriesPoint>& training_data);

private:
    Config config_;
    std::unique_ptr<IsolationForestDetector> isolation_detector_;
    std::unique_ptr<LSTMAutoencoderDetector> lstm_detector_;
    
    std::chrono::system_clock::time_point last_training_time_;
    
    /**
     * @brief Combine predictions from both detectors
     * @param isolation_anomalies Anomalies from Isolation Forest
     * @param lstm_anomalies Anomalies from LSTM Autoencoder
     * @return Combined ensemble anomalies
     */
    std::vector<Anomaly> combine_predictions(
        const std::vector<Anomaly>& isolation_anomalies,
        const std::vector<Anomaly>& lstm_anomalies) const;

    /**
     * @brief Check if retraining is needed
     * @param current_data Current data for analysis
     * @return true if models should be retrained
     */
    bool should_retrain(const std::vector<TimeSeriesPoint>& current_data) const;

    /**
     * @brief Extract time series values from points
     * @param points Time series points
     * @return Vector of values
     */
    std::vector<double> extract_values(const std::vector<TimeSeriesPoint>& points) const;

    /**
     * @brief Create feature matrix for Isolation Forest
     * @param points Time series points
     * @return Feature matrix
     */
    std::vector<std::vector<double>> create_feature_matrix(
        const std::vector<TimeSeriesPoint>& points) const;
};

} // namespace omop::common::ml