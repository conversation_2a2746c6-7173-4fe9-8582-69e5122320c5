/**
 * @file math_utils.h
 * @brief Mathematical utility functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#pragma once

#include <vector>
#include <array>
#include <cmath>
#include <numeric>
#include <algorithm>
#include <random>
#include <functional>
#include <optional>
#include <chrono>

namespace omop::common::math_utils {

/**
 * @brief Statistical calculations
 */
namespace statistics {

/**
 * @brief Calculate mean of values
 * @tparam Iterator Iterator type
 * @param begin Begin iterator
 * @param end End iterator
 * @return Mean value
 */
template<typename Iterator>
double mean(Iterator begin, Iterator end) {
    if (begin == end) return 0.0;
    auto sum = std::accumulate(begin, end, 0.0);
    return sum / std::distance(begin, end);
}

/**
 * @brief Calculate median of values
 * @tparam T Value type
 * @param values Vector of values
 * @return Median value
 */
template<typename T>
double median(std::vector<T> values) {
    if (values.empty()) return 0.0;
    
    std::sort(values.begin(), values.end());
    size_t n = values.size();
    
    if (n % 2 == 0) {
        return (values[n/2 - 1] + values[n/2]) / 2.0;
    } else {
        return values[n/2];
    }
}

/**
 * @brief Calculate standard deviation
 * @tparam Iterator Iterator type
 * @param begin Begin iterator
 * @param end End iterator
 * @param sample True for sample std dev, false for population
 * @return Standard deviation
 */
template<typename Iterator>
double standard_deviation(Iterator begin, Iterator end, bool sample = true) {
    if (begin == end) return 0.0;
    
    double m = mean(begin, end);
    double sum_sq_diff = 0.0;
    
    for (auto it = begin; it != end; ++it) {
        double diff = *it - m;
        sum_sq_diff += diff * diff;
    }
    
    size_t n = std::distance(begin, end);
    size_t divisor = sample ? (n - 1) : n;
    
    return std::sqrt(sum_sq_diff / divisor);
}

/**
 * @brief Calculate variance
 * @tparam Iterator Iterator type
 * @param begin Begin iterator
 * @param end End iterator
 * @param sample True for sample variance, false for population
 * @return Variance
 */
template<typename Iterator>
double variance(Iterator begin, Iterator end, bool sample = true) {
    double std_dev = standard_deviation(begin, end, sample);
    return std_dev * std_dev;
}

/**
 * @brief Calculate percentile
 * @tparam T Value type
 * @param values Vector of values
 * @param percentile Percentile (0.0 - 1.0)
 * @return Percentile value
 */
template<typename T>
double percentile(std::vector<T> values, double percentile) {
    if (values.empty()) return 0.0;
    if (percentile <= 0.0) return values.front();
    if (percentile >= 1.0) return values.back();
    
    std::sort(values.begin(), values.end());
    
    double index = percentile * (values.size() - 1);
    size_t lower = static_cast<size_t>(std::floor(index));
    size_t upper = static_cast<size_t>(std::ceil(index));
    
    if (lower == upper) {
        return values[lower];
    }
    
    double weight = index - lower;
    return values[lower] * (1.0 - weight) + values[upper] * weight;
}

/**
 * @brief Calculate quartiles
 * @tparam T Value type
 * @param values Vector of values
 * @return Array of [Q1, Q2, Q3]
 */
template<typename T>
std::array<double, 3> quartiles(const std::vector<T>& values) {
    return {
        percentile(values, 0.25),
        percentile(values, 0.50),
        percentile(values, 0.75)
    };
}

/**
 * @brief Calculate correlation coefficient
 * @tparam Iterator Iterator type
 * @param x_begin X values begin
 * @param x_end X values end
 * @param y_begin Y values begin
 * @return Correlation coefficient (-1.0 to 1.0)
 */
template<typename Iterator>
double correlation(Iterator x_begin, Iterator x_end, Iterator y_begin) {
    size_t n = std::distance(x_begin, x_end);
    if (n < 2) return 0.0;
    
    double x_mean = mean(x_begin, x_end);
    double y_mean = mean(y_begin, y_begin + n);
    
    double numerator = 0.0;
    double x_sum_sq = 0.0;
    double y_sum_sq = 0.0;
    
    auto x_it = x_begin;
    auto y_it = y_begin;
    
    for (size_t i = 0; i < n; ++i, ++x_it, ++y_it) {
        double x_diff = *x_it - x_mean;
        double y_diff = *y_it - y_mean;
        
        numerator += x_diff * y_diff;
        x_sum_sq += x_diff * x_diff;
        y_sum_sq += y_diff * y_diff;
    }
    
    double denominator = std::sqrt(x_sum_sq * y_sum_sq);
    return (denominator == 0.0) ? 0.0 : numerator / denominator;
}

/**
 * @brief Simple linear regression
 * @tparam Iterator Iterator type
 * @param x_begin X values begin
 * @param x_end X values end
 * @param y_begin Y values begin
 * @return Pair of (slope, intercept)
 */
template<typename Iterator>
std::pair<double, double> linear_regression(Iterator x_begin, Iterator x_end, Iterator y_begin) {
    size_t n = std::distance(x_begin, x_end);
    if (n < 2) return {0.0, 0.0};
    
    double x_mean = mean(x_begin, x_end);
    double y_mean = mean(y_begin, y_begin + n);
    
    double numerator = 0.0;
    double denominator = 0.0;
    
    auto x_it = x_begin;
    auto y_it = y_begin;
    
    for (size_t i = 0; i < n; ++i, ++x_it, ++y_it) {
        double x_diff = *x_it - x_mean;
        numerator += x_diff * (*y_it - y_mean);
        denominator += x_diff * x_diff;
    }
    
    if (denominator == 0.0) return {0.0, y_mean};
    
    double slope = numerator / denominator;
    double intercept = y_mean - slope * x_mean;
    
    return {slope, intercept};
}

} // namespace statistics

/**
 * @brief Numerical utilities
 */
namespace numerical {

/**
 * @brief Check if two floating point numbers are approximately equal
 * @param a First number
 * @param b Second number
 * @param epsilon Tolerance
 * @return True if approximately equal
 */
template<typename T>
bool almost_equal(T a, T b, T epsilon = std::numeric_limits<T>::epsilon()) {
    return std::abs(a - b) < epsilon;
}

/**
 * @brief Clamp value between min and max
 * @tparam T Value type
 * @param value Value to clamp
 * @param min_val Minimum value
 * @param max_val Maximum value
 * @return Clamped value
 */
template<typename T>
T clamp(T value, T min_val, T max_val) {
    return std::min(std::max(value, min_val), max_val);
}

/**
 * @brief Linear interpolation between two values
 * @tparam T Value type
 * @param a Start value
 * @param b End value
 * @param t Interpolation factor (0.0 to 1.0)
 * @return Interpolated value
 */
template<typename T>
T lerp(T a, T b, double t) {
    return a + t * (b - a);
}

/**
 * @brief Map value from one range to another
 * @tparam T Value type
 * @param value Value to map
 * @param from_min Source range minimum
 * @param from_max Source range maximum
 * @param to_min Target range minimum
 * @param to_max Target range maximum
 * @return Mapped value
 */
template<typename T>
T map_range(T value, T from_min, T from_max, T to_min, T to_max) {
    T ratio = (value - from_min) / (from_max - from_min);
    return to_min + ratio * (to_max - to_min);
}

/**
 * @brief Calculate greatest common divisor
 * @param a First number
 * @param b Second number
 * @return GCD
 */
template<typename T>
T gcd(T a, T b) {
    return b == 0 ? a : gcd(b, a % b);
}

/**
 * @brief Calculate least common multiple
 * @param a First number
 * @param b Second number
 * @return LCM
 */
template<typename T>
T lcm(T a, T b) {
    return (a * b) / gcd(a, b);
}

/**
 * @brief Check if number is prime
 * @param n Number to check
 * @return True if prime
 */
bool is_prime(uint64_t n);

/**
 * @brief Generate primes up to n using Sieve of Eratosthenes
 * @param n Upper limit
 * @return Vector of prime numbers
 */
std::vector<uint64_t> generate_primes(uint64_t n);

/**
 * @brief Calculate factorial
 * @param n Number
 * @return Factorial of n
 */
uint64_t factorial(uint64_t n);

/**
 * @brief Calculate binomial coefficient (n choose k)
 * @param n Total items
 * @param k Items to choose
 * @return Binomial coefficient
 */
uint64_t binomial_coefficient(uint64_t n, uint64_t k);

/**
 * @brief Fast integer power
 * @tparam T Number type
 * @param base Base value
 * @param exp Exponent
 * @return base^exp
 */
template<typename T>
T fast_power(T base, uint64_t exp) {
    T result = 1;
    while (exp > 0) {
        if (exp % 2 == 1) {
            result *= base;
        }
        base *= base;
        exp /= 2;
    }
    return result;
}

} // namespace numerical

/**
 * @brief Random number generation
 */
namespace random {

/**
 * @brief Thread-safe random number generator
 */
class RandomGenerator {
public:
    /**
     * @brief Constructor
     * @param seed Random seed (0 for time-based seed)
     */
    explicit RandomGenerator(uint64_t seed = 0);
    
    /**
     * @brief Generate random integer in range [min, max]
     * @param min Minimum value
     * @param max Maximum value
     * @return Random integer
     */
    int random_int(int min, int max);
    
    /**
     * @brief Generate random double in range [min, max)
     * @param min Minimum value
     * @param max Maximum value
     * @return Random double
     */
    double random_double(double min = 0.0, double max = 1.0);
    
    /**
     * @brief Generate random boolean
     * @param probability Probability of true (0.0 to 1.0)
     * @return Random boolean
     */
    bool random_bool(double probability = 0.5);
    
    /**
     * @brief Generate random from normal distribution
     * @param mean Mean value
     * @param std_dev Standard deviation
     * @return Random value from normal distribution
     */
    double random_normal(double mean = 0.0, double std_dev = 1.0);
    
    /**
     * @brief Generate random from exponential distribution
     * @param lambda Rate parameter
     * @return Random value from exponential distribution
     */
    double random_exponential(double lambda = 1.0);
    
    /**
     * @brief Shuffle container randomly
     * @tparam Container Container type
     * @param container Container to shuffle
     */
    template<typename Container>
    void shuffle(Container& container) {
        std::shuffle(container.begin(), container.end(), generator_);
    }
    
    /**
     * @brief Select random sample from container
     * @tparam Container Container type
     * @param container Source container
     * @param sample_size Number of elements to sample
     * @return Vector of sampled elements
     */
    template<typename Container>
    std::vector<typename Container::value_type> sample(const Container& container, size_t sample_size) {
        std::vector<typename Container::value_type> result(container.begin(), container.end());
        shuffle(result);
        
        if (sample_size < result.size()) {
            result.resize(sample_size);
        }
        
        return result;
    }
    
private:
    std::mt19937_64 generator_;
    std::mutex mutex_;
};

/**
 * @brief Get thread-local random generator
 * @return Reference to thread-local generator
 */
RandomGenerator& thread_local_generator();

/**
 * @brief Generate random UUID
 * @return UUID string
 */
std::string generate_uuid();

/**
 * @brief Generate random string
 * @param length String length
 * @param charset Character set to use
 * @return Random string
 */
std::string generate_random_string(size_t length, const std::string& charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789");

} // namespace random

/**
 * @brief Geometry utilities
 */
namespace geometry {

/**
 * @brief 2D point
 */
struct Point2D {
    double x, y;
    
    Point2D() : x(0), y(0) {}
    Point2D(double x_, double y_) : x(x_), y(y_) {}
    
    Point2D operator+(const Point2D& other) const { return {x + other.x, y + other.y}; }
    Point2D operator-(const Point2D& other) const { return {x - other.x, y - other.y}; }
    Point2D operator*(double scalar) const { return {x * scalar, y * scalar}; }
    
    double distance_to(const Point2D& other) const {
        double dx = x - other.x;
        double dy = y - other.y;
        return std::sqrt(dx * dx + dy * dy);
    }
};

/**
 * @brief 3D point
 */
struct Point3D {
    double x, y, z;
    
    Point3D() : x(0), y(0), z(0) {}
    Point3D(double x_, double y_, double z_) : x(x_), y(y_), z(z_) {}
    
    Point3D operator+(const Point3D& other) const { return {x + other.x, y + other.y, z + other.z}; }
    Point3D operator-(const Point3D& other) const { return {x - other.x, y - other.y, z - other.z}; }
    Point3D operator*(double scalar) const { return {x * scalar, y * scalar, z * scalar}; }
    
    double distance_to(const Point3D& other) const {
        double dx = x - other.x;
        double dy = y - other.y;
        double dz = z - other.z;
        return std::sqrt(dx * dx + dy * dy + dz * dz);
    }
};

/**
 * @brief Calculate distance between two points
 * @param p1 First point
 * @param p2 Second point
 * @return Distance
 */
double distance(const Point2D& p1, const Point2D& p2);
double distance(const Point3D& p1, const Point3D& p2);

/**
 * @brief Check if point is inside rectangle
 * @param point Point to check
 * @param rect_min Rectangle minimum corner
 * @param rect_max Rectangle maximum corner
 * @return True if inside
 */
bool point_in_rectangle(const Point2D& point, const Point2D& rect_min, const Point2D& rect_max);

/**
 * @brief Check if point is inside circle
 * @param point Point to check
 * @param center Circle center
 * @param radius Circle radius
 * @return True if inside
 */
bool point_in_circle(const Point2D& point, const Point2D& center, double radius);

} // namespace geometry

/**
 * @brief Medical/healthcare specific mathematics
 */
namespace medical {

/**
 * @brief Calculate Body Mass Index
 * @param weight_kg Weight in kilograms
 * @param height_m Height in meters
 * @return BMI value
 */
double calculate_bmi(double weight_kg, double height_m);

/**
 * @brief Classify BMI category
 * @param bmi BMI value
 * @return BMI category string
 */
std::string classify_bmi(double bmi);

/**
 * @brief Calculate Creatinine Clearance (Cockcroft-Gault equation)
 * @param age_years Age in years
 * @param weight_kg Weight in kilograms
 * @param serum_creatinine_mg_dl Serum creatinine in mg/dL
 * @param is_female True if female
 * @return Creatinine clearance in mL/min
 */
double calculate_creatinine_clearance(double age_years, double weight_kg, 
                                    double serum_creatinine_mg_dl, bool is_female);

/**
 * @brief Calculate estimated Glomerular Filtration Rate (eGFR) using CKD-EPI equation
 * @param age_years Age in years
 * @param serum_creatinine_mg_dl Serum creatinine in mg/dL
 * @param is_female True if female
 * @param is_black True if African American
 * @return eGFR in mL/min/1.73m²
 */
double calculate_egfr_ckd_epi(double age_years, double serum_creatinine_mg_dl, 
                             bool is_female, bool is_black = false);

/**
 * @brief Convert temperature units
 * @param temp Temperature value
 * @param from_unit Source unit ('C', 'F', 'K')
 * @param to_unit Target unit ('C', 'F', 'K')
 * @return Converted temperature
 */
double convert_temperature(double temp, char from_unit, char to_unit);

/**
 * @brief Convert weight units
 * @param weight Weight value
 * @param from_unit Source unit ("kg", "lbs", "g")
 * @param to_unit Target unit ("kg", "lbs", "g")
 * @return Converted weight
 */
double convert_weight(double weight, const std::string& from_unit, const std::string& to_unit);

/**
 * @brief Convert height units
 * @param height Height value
 * @param from_unit Source unit ("m", "cm", "ft", "in")
 * @param to_unit Target unit ("m", "cm", "ft", "in")
 * @return Converted height
 */
double convert_height(double height, const std::string& from_unit, const std::string& to_unit);

/**
 * @brief Calculate Z-score for pediatric growth charts
 * @param value Measured value
 * @param mean Reference mean
 * @param std_dev Reference standard deviation
 * @return Z-score
 */
double calculate_z_score(double value, double mean, double std_dev);

/**
 * @brief Calculate percentile from Z-score
 * @param z_score Z-score
 * @return Percentile (0.0 to 100.0)
 */
double z_score_to_percentile(double z_score);

} // namespace medical

/**
 * @brief Mathematical constants
 */
namespace constants {

constexpr double PI = 3.14159265358979323846;
constexpr double E = 2.71828182845904523536;
constexpr double GOLDEN_RATIO = 1.61803398874989484820;
constexpr double SQRT_2 = 1.41421356237309504880;
constexpr double SQRT_3 = 1.73205080756887729352;

// Medical constants
constexpr double NORMAL_BODY_TEMP_C = 37.0;
constexpr double NORMAL_BODY_TEMP_F = 98.6;

} // namespace constants

} // namespace omop::common::math_utils