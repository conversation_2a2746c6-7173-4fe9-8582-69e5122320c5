/**
 * @file date_utils.h
 * @brief Optimized date and time utility functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include <string>
#include <string_view>
#include <chrono>
#include <optional>
#include <vector>
#include <nlohmann/json.hpp>

namespace omop::common::date_utils {

// Type aliases for convenience
using duration_type = std::chrono::milliseconds;

/**
 * @brief Parse date from string with automatic format detection
 * @param date_str Date string
 * @return Optional system_clock time_point
 */
std::optional<std::chrono::system_clock::time_point> parse_date(const std::string& date_str,
 const std::string& format = "%Y-%m-%d");

/**
 * @brief Parse date with specific format
 * @param date_str Date string
 * @param format Format string (strptime style)
 * @return Optional system_clock time_point
 */
std::optional<std::chrono::system_clock::time_point> parse_date_format(
    std::string_view date_str, std::string_view format);

/**
 * @brief Parse date with multiple format attempts
 * @param date_str Date string
 * @param formats Vector of format strings to try
 * @return Optional system_clock time_point
 */
std::optional<time_point> parse_date_multiple_formats(const std::string& date_str,
    const std::vector<std::string>& formats);

/**
 * @brief Parse ISO 8601 date
 * @param iso_str ISO 8601 date string
 * @return Optional system_clock time_point
 */
std::optional<std::chrono::system_clock::time_point> parse_iso8601(std::string_view iso_str);

/**
 * @brief Parse Unix timestamp
 * @param timestamp_str Unix timestamp string
 * @param is_milliseconds Whether timestamp is in milliseconds
 * @return Optional system_clock time_point
 */
std::optional<std::chrono::system_clock::time_point> parse_unix_timestamp(
    std::string_view timestamp_str, bool is_milliseconds = false);

/**
 * @brief Parse RFC 3339 date
 * @param rfc_str RFC 3339 date string
 * @return Optional system_clock time_point
 */
std::optional<std::chrono::system_clock::time_point> parse_rfc3339(std::string_view rfc_str);

/**
 * @brief Parse date with multiple format attempts
 * @param date_str Date string
 * @param formats Vector of format strings to try
 * @return Optional system_clock time_point
 */
std::optional<std::chrono::system_clock::time_point> parse_with_formats(
    std::string_view date_str, const std::vector<std::string>& formats);

/**
 * @brief Format date as ISO 8601 string
 * @param time_point Time point to format
 * @param include_microseconds Include microseconds
 * @return ISO 8601 formatted string
 */
std::string to_iso8601(const std::chrono::system_clock::time_point& time_point,
                       bool include_microseconds = false);

/**
 * @brief Format date with custom format
 * @param time_point Time point to format
 * @param format Format string (strftime style)
 * @return Formatted date string
 */
std::string format_date(const std::chrono::system_clock::time_point& time_point,
                        const std::string& format = "%Y-%m-%d");

/**
 * @brief Get current timestamp as string
 * @param format Format string (strftime style)
 * @return Current timestamp string
 */
std::string current_timestamp(const std::string& format = "%Y-%m-%d %H:%M:%S");

/**
 * @brief Format as Unix timestamp
 * @param time_point Time point to format
 * @param include_milliseconds Include milliseconds
 * @return Unix timestamp string
 */
std::string to_unix_timestamp(const std::chrono::system_clock::time_point& time_point,
                              bool include_milliseconds = false);

/**
 * @brief Format as RFC 3339 string
 * @param time_point Time point to format
 * @return RFC 3339 formatted string
 */
std::string to_rfc3339(const std::chrono::system_clock::time_point& time_point);

/**
 * @brief Format duration in human-readable format
 * @param duration Duration to format
 * @return Human-readable duration string
 */
std::string format_duration(const std::chrono::duration<double>& duration);

/**
 * @brief Format age from birth date
 * @param birth_date Birth date
 * @param reference_date Reference date (default: now)
 * @return Age string (e.g., "25 years", "6 months")
 */
std::string format_age(const std::chrono::system_clock::time_point& birth_date,
                       const std::chrono::system_clock::time_point& reference_date =
                       std::chrono::system_clock::now());

/**
 * @brief Get ISO week number
 * @param date Time point
 * @return ISO week number
 */
int get_iso_week(const std::chrono::system_clock::time_point& date);

/**
 * @brief Get duration between two time points
 * @param start Start time point
 * @param end End time point
 * @return Duration between start and end
 */
duration_type duration_between(const std::chrono::system_clock::time_point& start,
                               const std::chrono::system_clock::time_point& end);

/**
 * @brief Format duration in human-readable format
 * @param duration Duration to format
 * @return Human-readable duration string
 */
std::string format_duration_human(const duration_type& duration);

/**
 * @brief Get start of day
 * @param date Input date
 * @return Start of day
 */
std::chrono::system_clock::time_point start_of_day(const std::chrono::system_clock::time_point& date);

/**
 * @brief Get end of day
 * @param date Input date
 * @return End of day
 */
std::chrono::system_clock::time_point end_of_day(const std::chrono::system_clock::time_point& date);

/**
 * @brief UK-specific formatting functions
 * @param date Input date
 * @return UK-formatted date string
 */
std::string format_uk_date(const std::chrono::system_clock::time_point& date);

/**
 * @brief UK-specific formatting functions
 * @param date Input date
 * @return UK-formatted datetime string
 */
std::string format_uk_datetime(const std::chrono::system_clock::time_point& date);

/**
 * @brief ISO-specific formatting functions
 * @param date Input date
 * @return ISO-formatted date string
 */
std::string format_iso_date(const std::chrono::system_clock::time_point& date);

/**
 * @brief ISO-specific formatting functions
 * @param date Input date
 * @return ISO-formatted datetime string
 */
std::string format_iso_datetime(const std::chrono::system_clock::time_point& date);

/**
 * @brief Add years to date
 * @param date Base date
 * @param years Years to add
 * @return New date
 */
std::chrono::system_clock::time_point add_years(
    const std::chrono::system_clock::time_point& date, int years);

/**
 * @brief Add months to date
 * @param date Base date
 * @param months Months to add
 * @return New date
 */
std::chrono::system_clock::time_point add_months(
    const std::chrono::system_clock::time_point& date, int months);

/**
 * @brief Add days to date
 * @param date Base date
 * @param days Days to add
 * @return New date
 */
std::chrono::system_clock::time_point add_days(
    const std::chrono::system_clock::time_point& date, int days);

/**
 * @brief Add duration to time point
 * @param date Time point to add to
 * @param days Days to add
 * @param hours Hours to add
 * @param minutes Minutes to add
 * @return New time point
 */
std::chrono::system_clock::time_point add_duration(const std::chrono::system_clock::time_point& date, int days = 0, int hours = 0, int minutes = 0);

/**
 * @brief Age structure for years and months
 */
struct AgeYearsMonths {
    int years;
    int months;

    nlohmann::json to_json() const {
        return nlohmann::json{{"years", years}, {"months", months}};
    }
};

/**
 * @brief Calculate age in years and months
 * @param birth_date Birth date
 * @param reference_date Reference date (default: current date)
 * @return AgeYearsMonths Age in years and months
 */
AgeYearsMonths calculate_age(const std::chrono::system_clock::time_point& birth_date,
                           const std::chrono::system_clock::time_point& reference_date =
                           std::chrono::system_clock::now());

/**
  * @brief Convert timezone
  * @param date Date to convert
  * @param from_tz Source timezone
  * @param to_tz Target timezone
  * @return Converted date
  */
std::chrono::system_clock::time_point convert_timezone(const std::chrono::system_clock::time_point& date, const std::string& from_tz, const std::string& to_tz);

/**
 * @brief Calculate difference in days
 * @param date1 First date
 * @param date2 Second date
 * @return Difference in days
 */
int days_difference(const std::chrono::system_clock::time_point& date1,
                   const std::chrono::system_clock::time_point& date2);

/**
 * @brief Get start of day
 * @param date Input date
 * @return Start of day (00:00:00)
 */
std::chrono::system_clock::time_point start_of_day(
    const std::chrono::system_clock::time_point& date);

/**
 * @brief Get end of day
 * @param date Input date
 * @return End of day (23:59:59.999)
 */
std::chrono::system_clock::time_point end_of_day(
    const std::chrono::system_clock::time_point& date);

/**
 * @brief Get start of month
 * @param date Input date
 * @return Start of month
 */
std::chrono::system_clock::time_point start_of_month(
    const std::chrono::system_clock::time_point& date);

/**
 * @brief Get end of month
 * @param date Input date
 * @return End of month
 */
std::chrono::system_clock::time_point end_of_month(
    const std::chrono::system_clock::time_point& date);

/**
 * @brief Get start of year
 * @param date Input date
 * @return Start of year
 */
std::chrono::system_clock::time_point start_of_year(
    const std::chrono::system_clock::time_point& date);

/**
 * @brief Get end of year
 * @param date Input date
 * @return End of year
 */
std::chrono::system_clock::time_point end_of_year(
    const std::chrono::system_clock::time_point& date);

/**
 * @brief Check if date is valid
 * @param year Year
 * @param month Month (1-12)
 * @param day Day (1-31)
 * @return true if valid date
 */
bool is_valid_date(int year, int month, int day);

/**
 * @brief Check if date is a weekend
 * @param date Date to check
 * @return true if weekend
 */
bool is_weekend(const std::chrono::system_clock::time_point& date);

/**
 * @brief Check if year is leap year
 * @param year Year to check
 * @return true if leap year
 */
bool is_leap_year(int year);

/**
 * @brief Check if date is in the future
 * @param date Date to check
 * @param reference_date Reference date (default: now)
 * @return true if in future
 */
bool is_future_date(const std::chrono::system_clock::time_point& date,
                   const std::chrono::system_clock::time_point& reference_date =
                       std::chrono::system_clock::now());

/**
 * @brief Check if date is reasonable for birth date
 * @param birth_date Birth date to check
 * @param max_age_years Maximum reasonable age
 * @return true if reasonable
 */
bool is_reasonable_birth_date(const std::chrono::system_clock::time_point& birth_date,
                             int max_age_years = 150);

/**
 * @brief Validate date range
 * @param date Date to check
 * @param min_date Minimum allowed date
 * @param max_date Maximum allowed date
 * @return true if in range
 */
bool is_date_in_range(const std::chrono::system_clock::time_point& date,
                     const std::chrono::system_clock::time_point& min_date,
                     const std::chrono::system_clock::time_point& max_date);

/**
 * @brief Calculate gestational age from conception date
 * @param conception_date Date of conception
 * @param reference_date Reference date (default: now)
 * @return Gestational age in weeks
 */
double calculate_gestational_age_weeks(
    const std::chrono::system_clock::time_point& conception_date,
    const std::chrono::system_clock::time_point& reference_date =
        std::chrono::system_clock::now());

/**
 * @brief Calculate expected due date from last menstrual period
 * @param lmp_date Last menstrual period date
 * @return Expected due date
 */
std::chrono::system_clock::time_point calculate_due_date(
    const std::chrono::system_clock::time_point& lmp_date);

/**
 * @brief Determine pediatric age group
 * @param birth_date Birth date
 * @param reference_date Reference date (default: now)
 * @return Age group string
 */
std::string determine_pediatric_age_group(
    const std::chrono::system_clock::time_point& birth_date,
    const std::chrono::system_clock::time_point& reference_date =
        std::chrono::system_clock::now());

/**
 * @brief Calculate age in months for pediatric patients
 * @param birth_date Birth date
 * @param reference_date Reference date (default: now)
 * @return Age in months
 */
int calculate_age_months(const std::chrono::system_clock::time_point& birth_date,
                        const std::chrono::system_clock::time_point& reference_date =
                            std::chrono::system_clock::now());

// Common date formats for parsing
inline const std::vector<std::string> COMMON_DATE_FORMATS = {
    "%Y-%m-%d",           // 2023-12-25
    "%Y/%m/%d",           // 2023/12/25
    "%d/%m/%Y",           // 25/12/2023
    "%m/%d/%Y",           // 12/25/2023
    "%d-%m-%Y",           // 25-12-2023
    "%m-%d-%Y",           // 12-25-2023
    "%Y%m%d",             // 20231225
    "%d %b %Y",           // 25 Dec 2023
    "%b %d, %Y",          // Dec 25, 2023
    "%A, %B %d, %Y"       // Monday, December 25, 2023
};

// Common datetime formats
inline const std::vector<std::string> COMMON_DATETIME_FORMATS = {
    "%Y-%m-%d %H:%M:%S",      // 2023-12-25 14:30:00
    "%Y-%m-%dT%H:%M:%S",      // 2023-12-25T14:30:00
    "%Y-%m-%d %H:%M:%S.%f",   // 2023-12-25 14:30:00.123
    "%Y-%m-%dT%H:%M:%S.%fZ",  // 2023-12-25T14:30:00.123Z
    "%d/%m/%Y %H:%M:%S",      // 25/12/2023 14:30:00
    "%m/%d/%Y %H:%M:%S"       // 12/25/2023 14:30:00
};

// Medical date constants
constexpr int PREGNANCY_DURATION_DAYS = 280;  // 40 weeks
constexpr int MAX_REASONABLE_AGE_YEARS = 150;

} // namespace omop::common::date_utils