/**
 * @file memory_utils.h
 * @brief Memory management and monitoring utilities
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#pragma once

#include <memory>
#include <cstdint>
#include <functional>
#include <string>
#include <chrono>
#include <atomic>
#include <mutex>
#include <unordered_map>
#include <vector>
#include <optional>

namespace omop::common::memory_utils {

/**
 * @brief Memory allocation utilities
 */
namespace allocation {

/**
 * @brief Allocate aligned memory
 * @param size Size in bytes
 * @param alignment Alignment in bytes (must be power of 2)
 * @return Aligned memory pointer
 */
void* aligned_alloc(size_t size, size_t alignment);

/**
 * @brief Free aligned memory
 * @param ptr Pointer to aligned memory
 */
void aligned_free(void* ptr);

/**
 * @brief Create unique_ptr with aligned memory
 * @tparam T Type to allocate
 * @param count Number of elements
 * @param alignment Alignment in bytes
 * @return Unique pointer with custom deleter
 */
template<typename T>
std::unique_ptr<T[], std::function<void(T*)>> make_aligned_unique(size_t count, size_t alignment = alignof(T)) {
    auto ptr = static_cast<T*>(aligned_alloc(count * sizeof(T), alignment));
    if (!ptr) return nullptr;
    
    return std::unique_ptr<T[], std::function<void(T*)>>(
        ptr, [](T* p) { aligned_free(p); });
}

/**
 * @brief Memory pool for fixed-size allocations
 * @tparam T Type to allocate
 */
template<typename T>
class MemoryPool {
public:
    /**
     * @brief Constructor
     * @param chunk_size Number of objects per chunk
     */
    explicit MemoryPool(size_t chunk_size = 1024);
    
    /**
     * @brief Destructor
     */
    ~MemoryPool();
    
    /**
     * @brief Allocate object
     * @return Pointer to allocated object
     */
    T* allocate();
    
    /**
     * @brief Deallocate object
     * @param ptr Pointer to deallocate
     */
    void deallocate(T* ptr);
    
    /**
     * @brief Get total allocated objects
     * @return Number of allocated objects
     */
    size_t allocated_count() const { return allocated_count_.load(); }
    
    /**
     * @brief Get total memory usage
     * @return Memory usage in bytes
     */
    size_t memory_usage() const { return chunks_.size() * chunk_size_ * sizeof(T); }
    
private:
    struct Chunk {
        std::unique_ptr<T[]> data;
        std::vector<bool> used;
        size_t free_count;
        
        explicit Chunk(size_t size) 
            : data(std::make_unique<T[]>(size))
            , used(size, false)
            , free_count(size) {}
    };
    
    size_t chunk_size_;
    std::vector<std::unique_ptr<Chunk>> chunks_;
    std::atomic<size_t> allocated_count_{0};
    mutable std::mutex mutex_;
    
    T* allocate_from_chunk(Chunk& chunk);
    bool deallocate_from_chunk(Chunk& chunk, T* ptr);
};

} // namespace allocation

/**
 * @brief Memory monitoring utilities
 */
namespace monitoring {

/**
 * @brief Get current process memory usage
 * @return Memory usage in bytes
 */
std::optional<size_t> get_process_memory_usage();

/**
 * @brief Get peak process memory usage
 * @return Peak memory usage in bytes
 */
std::optional<size_t> get_peak_memory_usage();

/**
 * @brief Get available system memory
 * @return Available memory in bytes
 */
std::optional<size_t> get_available_memory();

/**
 * @brief Get total system memory
 * @return Total memory in bytes
 */
std::optional<size_t> get_total_memory();

/**
 * @brief Format memory size in human-readable format
 * @param bytes Memory size in bytes
 * @param use_binary Use binary units (1024) vs decimal (1000)
 * @return Formatted string (e.g., "1.5 GB", "768 MB")
 */
std::string format_memory_size(size_t bytes, bool use_binary = true);

/**
 * @brief Memory usage tracker
 */
class MemoryTracker {
public:
    /**
     * @brief Constructor
     * @param name Tracker name
     */
    explicit MemoryTracker(const std::string& name = "default");
    
    /**
     * @brief Destructor
     */
    ~MemoryTracker();
    
    /**
     * @brief Record memory allocation
     * @param size Allocated size
     * @param location Source location (file:line)
     */
    void record_allocation(size_t size, const std::string& location = "");
    
    /**
     * @brief Record memory deallocation
     * @param size Deallocated size
     */
    void record_deallocation(size_t size);
    
    /**
     * @brief Get current allocated memory
     * @return Current allocation in bytes
     */
    size_t current_allocation() const { return current_allocation_.load(); }
    
    /**
     * @brief Get peak allocated memory
     * @return Peak allocation in bytes
     */
    size_t peak_allocation() const { return peak_allocation_.load(); }
    
    /**
     * @brief Get total allocations count
     * @return Number of allocations
     */
    size_t allocation_count() const { return allocation_count_.load(); }
    
    /**
     * @brief Get statistics
     * @return Statistics map
     */
    std::unordered_map<std::string, size_t> get_statistics() const;
    
    /**
     * @brief Reset statistics
     */
    void reset();
    
private:
    std::string name_;
    std::atomic<size_t> current_allocation_{0};
    std::atomic<size_t> peak_allocation_{0};
    std::atomic<size_t> allocation_count_{0};
    std::atomic<size_t> deallocation_count_{0};
    
    // Location tracking (for debugging)
    mutable std::mutex locations_mutex_;
    std::unordered_map<std::string, size_t> allocation_locations_;
};

/**
 * @brief Global memory tracker
 * @return Reference to global tracker
 */
MemoryTracker& global_tracker();

} // namespace monitoring

/**
 * @brief Memory optimization utilities
 */
namespace optimization {

/**
 * @brief Calculate optimal batch size for memory usage
 * @param item_size Size of each item in bytes
 * @param available_memory Available memory in bytes
 * @param memory_overhead_factor Overhead factor (default: 1.5)
 * @return Optimal batch size
 */
size_t calculate_optimal_batch_size(size_t item_size, 
                                  size_t available_memory, 
                                  double memory_overhead_factor = 1.5);

/**
 * @brief Memory-efficient vector
 * @tparam T Element type
 * @tparam ChunkSize Size of each memory chunk
 */
template<typename T, size_t ChunkSize = 1024>
class ChunkedVector {
public:
    /**
     * @brief Constructor
     */
    ChunkedVector() = default;
    
    /**
     * @brief Add element
     * @param value Element to add
     */
    void push_back(const T& value);
    void push_back(T&& value);
    
    /**
     * @brief Get element
     * @param index Element index
     * @return Reference to element
     */
    T& at(size_t index);
    const T& at(size_t index) const;
    
    /**
     * @brief Get size
     * @return Number of elements
     */
    size_t size() const { return size_; }
    
    /**
     * @brief Check if empty
     * @return True if empty
     */
    bool empty() const { return size_ == 0; }
    
    /**
     * @brief Clear all elements
     */
    void clear();
    
    /**
     * @brief Reserve capacity
     * @param capacity New capacity
     */
    void reserve(size_t capacity);
    
    /**
     * @brief Get memory usage
     * @return Memory usage in bytes
     */
    size_t memory_usage() const { return chunks_.size() * ChunkSize * sizeof(T); }
    
private:
    using Chunk = std::unique_ptr<T[]>;
    std::vector<Chunk> chunks_;
    size_t size_{0};
    size_t capacity_{0};
    
    void ensure_capacity(size_t needed);
};

/**
 * @brief Memory-mapped file reader
 */
class MemoryMappedFile {
public:
    /**
     * @brief Constructor
     */
    MemoryMappedFile() = default;
    
    /**
     * @brief Destructor
     */
    ~MemoryMappedFile();
    
    /**
     * @brief Open file for memory mapping
     * @param filename File to open
     * @param read_only Open read-only
     * @return True if successful
     */
    bool open(const std::string& filename, bool read_only = true);
    
    /**
     * @brief Close file
     */
    void close();
    
    /**
     * @brief Get data pointer
     * @return Pointer to mapped data
     */
    const void* data() const { return data_; }
    void* data() { return data_; }
    
    /**
     * @brief Get file size
     * @return Size in bytes
     */
    size_t size() const { return size_; }
    
    /**
     * @brief Check if file is open
     * @return True if open
     */
    bool is_open() const { return data_ != nullptr; }
    
private:
    void* data_{nullptr};
    size_t size_{0};
    int fd_{-1};
    bool read_only_{true};
};

} // namespace optimization

/**
 * @brief Smart pointer utilities
 */
namespace smart_pointers {

/**
 * @brief Create shared_ptr with custom allocator
 * @tparam T Type to allocate
 * @tparam Allocator Allocator type
 * @param allocator Allocator instance
 * @param args Constructor arguments
 * @return Shared pointer
 */
template<typename T, typename Allocator, typename... Args>
std::shared_ptr<T> allocate_shared_custom(const Allocator& allocator, Args&&... args) {
    return std::allocate_shared<T>(allocator, std::forward<Args>(args)...);
}

/**
 * @brief Weak pointer observer
 * @tparam T Type being observed
 */
template<typename T>
class WeakObserver {
public:
    /**
     * @brief Constructor
     * @param ptr Shared pointer to observe
     */
    explicit WeakObserver(std::shared_ptr<T> ptr) : weak_ptr_(ptr) {}
    
    /**
     * @brief Get locked pointer
     * @return Shared pointer if still valid
     */
    std::shared_ptr<T> lock() const { return weak_ptr_.lock(); }
    
    /**
     * @brief Check if object still exists
     * @return True if object exists
     */
    bool expired() const { return weak_ptr_.expired(); }
    
    /**
     * @brief Use count
     * @return Reference count
     */
    long use_count() const { return weak_ptr_.use_count(); }
    
private:
    std::weak_ptr<T> weak_ptr_;
};

/**
 * @brief Unique pointer with deleter
 * @tparam T Type
 * @tparam Deleter Deleter type
 * @param ptr Pointer to manage
 * @param deleter Deleter function
 * @return Unique pointer with custom deleter
 */
template<typename T, typename Deleter>
std::unique_ptr<T, Deleter> make_unique_with_deleter(T* ptr, Deleter deleter) {
    return std::unique_ptr<T, Deleter>(ptr, deleter);
}

} // namespace smart_pointers

/**
 * @brief Memory safety utilities
 */
namespace safety {

/**
 * @brief Check if pointer is valid
 * @param ptr Pointer to check
 * @param size Expected accessible size
 * @return True if pointer appears valid
 */
bool is_valid_pointer(const void* ptr, size_t size = sizeof(void*));

/**
 * @brief Safe memory copy
 * @param dest Destination buffer
 * @param dest_size Destination buffer size
 * @param src Source buffer
 * @param src_size Source buffer size
 * @return True if copy was safe and successful
 */
bool safe_memcpy(void* dest, size_t dest_size, const void* src, size_t src_size);

/**
 * @brief Safe memory set
 * @param dest Destination buffer
 * @param dest_size Destination buffer size
 * @param value Value to set
 * @param count Number of bytes to set
 * @return True if operation was safe and successful
 */
bool safe_memset(void* dest, size_t dest_size, int value, size_t count);

/**
 * @brief Secure zero memory
 * @param ptr Memory to zero
 * @param size Size in bytes
 */
void secure_zero(void* ptr, size_t size);

/**
 * @brief RAII memory guard
 */
class MemoryGuard {
public:
    /**
     * @brief Constructor
     * @param ptr Pointer to guard
     * @param size Size of memory
     */
    MemoryGuard(void* ptr, size_t size);
    
    /**
     * @brief Destructor - securely zeros memory
     */
    ~MemoryGuard();
    
    /**
     * @brief Get guarded pointer
     * @return Pointer
     */
    void* get() const { return ptr_; }
    
    /**
     * @brief Get size
     * @return Size in bytes
     */
    size_t size() const { return size_; }
    
private:
    void* ptr_;
    size_t size_;
};

} // namespace safety

/**
 * @brief Memory debugging utilities
 */
namespace debugging {

/**
 * @brief Memory leak detector
 */
class LeakDetector {
public:
    /**
     * @brief Constructor
     */
    LeakDetector();
    
    /**
     * @brief Destructor
     */
    ~LeakDetector();
    
    /**
     * @brief Enable leak detection
     */
    void enable();
    
    /**
     * @brief Disable leak detection
     */
    void disable();
    
    /**
     * @brief Record allocation
     * @param ptr Allocated pointer
     * @param size Allocation size
     * @param location Source location
     */
    void record_allocation(void* ptr, size_t size, const std::string& location);
    
    /**
     * @brief Record deallocation
     * @param ptr Deallocated pointer
     */
    void record_deallocation(void* ptr);
    
    /**
     * @brief Check for leaks
     * @return Number of leaked allocations
     */
    size_t check_leaks() const;
    
    /**
     * @brief Print leak report
     * @param stream Output stream
     */
    void print_leak_report(std::ostream& stream) const;
    
private:
    struct AllocationInfo {
        size_t size;
        std::string location;
        std::chrono::system_clock::time_point timestamp;
    };
    
    mutable std::mutex mutex_;
    std::unordered_map<void*, AllocationInfo> allocations_;
    bool enabled_{false};
};

/**
 * @brief Get global leak detector
 * @return Reference to global leak detector
 */
LeakDetector& global_leak_detector();

} // namespace debugging

// Memory tracking macros
#ifdef OMOP_MEMORY_DEBUGGING
#define OMOP_TRACK_ALLOCATION(ptr, size) \
    omop::common::memory_utils::debugging::global_leak_detector().record_allocation(ptr, size, __FILE__ ":" + std::to_string(__LINE__))
#define OMOP_TRACK_DEALLOCATION(ptr) \
    omop::common::memory_utils::debugging::global_leak_detector().record_deallocation(ptr)
#else
#define OMOP_TRACK_ALLOCATION(ptr, size) do {} while(0)
#define OMOP_TRACK_DEALLOCATION(ptr) do {} while(0)
#endif

} // namespace omop::common::memory_utils