/**
 * @file string_utils.h
 * @brief Comprehensive string utility functions for the OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * 
 * This header provides a complete set of string manipulation, validation,
 * and transformation utilities optimized for medical data processing.
 */

#pragma once

#include <string>
#include <string_view>
#include <vector>
#include <regex>
#include <optional>
#include <unordered_map>
#include <chrono>
#include <functional>
#include <locale>
#include <algorithm>
#include <cstdint>
#include <sstream>
#include <iomanip>

namespace omop::common::string_utils {

/**
 * @brief Configuration parameters for string operations
 */
struct StringConfig {
    bool case_sensitive;          ///< Whether string comparisons are case-sensitive
    bool trim_whitespace;         ///< Whether to automatically trim whitespace
    bool normalize_unicode;       ///< Whether to normalize Unicode characters
    std::string default_locale;   ///< Default locale for string operations
    size_t max_string_length;     ///< Maximum allowed string length
    bool enable_regex_cache;      ///< Whether to cache compiled regex patterns
    size_t regex_cache_size;      ///< Maximum number of cached regex patterns
    
    /**
     * @brief Default constructor with sensible defaults
     */
    StringConfig() : case_sensitive(false), trim_whitespace(true), normalize_unicode(false),
                    default_locale("en_US.UTF-8"), max_string_length(1000000),
                    enable_regex_cache(true), regex_cache_size(1000) {}
};

/**
 * @brief Result of string validation operations
 */
struct ValidationResult {
    bool is_valid{true};              ///< Whether the string passed validation
    std::string error_message;        ///< Description of validation failure
    std::vector<std::string> warnings; ///< Non-fatal validation warnings
    std::string sanitized_value;      ///< Cleaned version of the input
    size_t original_length{0};        ///< Length of original string
    size_t sanitized_length{0};       ///< Length of sanitized string
};

// ========================================================================
// Basic String Manipulation
// ========================================================================

/**
 * @brief Remove leading and trailing whitespace from a string
 * @param str Input string to trim
 * @return String with whitespace removed from both ends
 * @note Whitespace includes spaces, tabs, newlines, and other spacing characters
 */
std::string trim(const std::string& str);

/**
 * @brief Remove leading whitespace from a string
 * @param str Input string to trim
 * @return String with whitespace removed from the beginning
 */
std::string left_trim(const std::string& str);

/**
 * @brief Remove trailing whitespace from a string
 * @param str Input string to trim
 * @return String with whitespace removed from the end
 */
std::string right_trim(const std::string& str);

/**
 * @brief Convert string to lowercase
 * @param str Input string to convert
 * @return Lowercase version of the input string
 * @note Uses the default locale for character conversion
 */
std::string to_lower(const std::string& str);

/**
 * @brief Convert string to uppercase
 * @param str Input string to convert
 * @return Uppercase version of the input string
 * @note Uses the default locale for character conversion
 */
std::string to_upper(const std::string& str);

/**
 * @brief Capitalize the first character of a string
 * @param str Input string to modify
 * @return String with first character capitalized
 * @note Returns empty string if input is empty
 */
std::string capitalize_first(const std::string& str);

/**
 * @brief Convert first character to lowercase
 * @param str Input string to modify
 * @return String with first character in lowercase
 * @note Returns empty string if input is empty
 */
std::string uncapitalize_first(const std::string& str);

/**
 * @brief Convert string to title case (capitalize first letter of each word)
 * @param str Input string to convert
 * @return String with each word capitalized
 * @note Words are delimited by whitespace characters
 */
std::string to_title_case(const std::string& str);

// ========================================================================
// String Splitting and Joining
// ========================================================================

/**
 * @brief Split a string by a single character delimiter
 * @param str String to split
 * @param delimiter Character to split on
 * @return Vector of substrings between delimiters
 * @note Empty substrings are excluded from the result
 */
std::vector<std::string> split(const std::string& str, char delimiter);

/**
 * @brief Split a string by a string delimiter
 * @param str String to split
 * @param delimiter String delimiter to split on
 * @return Vector of substrings between delimiters
 * @note Empty delimiters return the original string as a single element
 */
std::vector<std::string> split(const std::string& str, const std::string& delimiter);

/**
 * @brief Split a string using a regular expression pattern
 * @param str String to split
 * @param pattern Regex pattern defining the delimiter
 * @return Vector of substrings that don't match the pattern
 * @throws std::regex_error if pattern is invalid
 */
std::vector<std::string> split_regex(const std::string& str, const std::string& pattern);

/**
 * @brief Split a string using a compiled regular expression
 * @param str String to split
 * @param pattern Compiled regex pattern
 * @param max_splits Maximum number of splits (0 = unlimited)
 * @return Vector of substrings
 */
std::vector<std::string> split_regex(const std::string& str, const std::regex& pattern, size_t max_splits = 0);

/**
 * @brief Split a string into lines
 * @param str String to split into lines
 * @param keep_empty If true, keep empty lines in result
 * @return Vector of lines
 * @note Handles both Unix (LF) and Windows (CRLF) line endings
 */
std::vector<std::string> split_lines(const std::string& str, bool keep_empty = false);

/**
 * @brief Join a vector of strings with a delimiter
 * @param strings Vector of strings to join
 * @param delimiter String to insert between elements
 * @return Joined string
 * @note Empty vector returns empty string
 */
std::string join(const std::vector<std::string>& strings, const std::string& delimiter);

/**
 * @brief Join any container of strings with a delimiter
 * @tparam Container Type of container (must support iteration)
 * @param container Container of strings to join
 * @param delimiter String to insert between elements
 * @return Joined string
 * @note Container must have begin() and end() methods
 */
template<typename Container>
std::string join(const Container& container, const std::string& delimiter) {
    if (container.empty()) return "";
    
    std::ostringstream oss;
    auto it = container.begin();
    oss << *it;
    ++it;
    
    for (; it != container.end(); ++it) {
        oss << delimiter << *it;
    }
    
    return oss.str();
}

// Declare remaining functions (implementation continues with all other functions from the class)
// For brevity, I'll include key function declarations here...

// String Replacement and Modification
std::string replace_all(const std::string& str, const std::string& from, const std::string& to);
std::string replace_first(const std::string& str, const std::string& from, const std::string& to);
std::string replace_pattern(const std::string& text, const std::string& pattern, const std::string& replacement);
std::string remove_all(const std::string& str, const std::string& to_remove);
std::string remove_chars(const std::string& str, const std::string& chars);
std::string remove_non_alphanumeric(const std::string& str, bool preserve_spaces = true);
std::string normalize_whitespace(const std::string& str);
std::string extract_substring(const std::string& str, int start_pos, int end_pos);

// String Searching and Checking
bool starts_with(const std::string& str, const std::string& prefix, bool case_sensitive = true);
bool ends_with(const std::string& str, const std::string& suffix, bool case_sensitive = true);
bool contains(const std::string& str, const std::string& substr, bool case_sensitive = true);
size_t find_first(const std::string& str, const std::string& substr);
size_t find_last(const std::string& str, const std::string& substr);
size_t count_occurrences(const std::string& str, const std::string& substr);

// String Case Conversion
std::string to_snake_case(const std::string& str);
std::string to_camel_case(const std::string& str);
std::string to_pascal_case(const std::string& str);
std::string to_kebab_case(const std::string& str);

// String Validation
bool is_empty_or_whitespace(const std::string& str);
bool is_blank(const std::string& str);
bool is_numeric(const std::string& str, bool allow_decimal = true, bool allow_negative = true);
bool is_alphabetic(const std::string& str);
bool is_alphanumeric(const std::string& str);
bool is_email(const std::string& str);
bool is_url(const std::string& str);
bool is_uuid(const std::string& str);
bool is_ipv4(const std::string& str);
bool is_ipv6(const std::string& str);
bool validate_length(const std::string& str, size_t min_length, size_t max_length);
bool validate_characters(const std::string& str, const std::string& allowed_chars);

// Pattern Matching and Regular Expressions
bool matches_pattern(const std::string& value, const std::string& pattern);
bool matches_regex(const std::string& value, const std::regex& pattern);
std::vector<std::string> find_all_matches(const std::string& text, const std::string& pattern);
std::vector<std::vector<std::string>> extract_capture_groups(const std::string& text, const std::string& pattern);

// String Type Conversion
std::optional<int> to_int(const std::string& str, int base = 10);
std::optional<long> to_long(const std::string& str, int base = 10);
std::optional<double> to_double(const std::string& str);
std::optional<bool> to_bool(const std::string& str);

// String Sanitization and Escaping
std::string sanitize_string(const std::string& input);
std::string escape_characters(const std::string& str, const std::string& chars_to_escape, char escape_char = '\\');
std::string unescape_characters(const std::string& str, char escape_char = '\\');
std::string escape_sql(const std::string& str);
std::string sql_escape(const std::string& str);
std::string escape_html(const std::string& str);
std::string unescape_html(const std::string& str);
std::string escape_xml(const std::string& str);
std::string xml_escape(const std::string& str);
std::string json_escape(const std::string& str);
std::string json_unescape(const std::string& str);

// String Generation and Randomization
std::string random_string(size_t length, const std::string& charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789");
std::string generate_uuid();
std::string generate_hash(const std::string& str, const std::string& algorithm = "sha256");

// String Transformation and Formatting
template<typename... Args>
std::string format(const std::string& format, Args&&... args) {
    size_t size = std::snprintf(nullptr, 0, format.c_str(), args...) + 1;
    if (size <= 0) return "";
    
    std::unique_ptr<char[]> buf(new char[size]);
    std::snprintf(buf.get(), size, format.c_str(), args...);
    return std::string(buf.get(), buf.get() + size - 1);
}

std::string pad(const std::string& str, size_t length, char pad_char = ' ', bool left_pad = false);
std::string pad_string(const std::string& str, size_t length, char pad_char = ' ', bool pad_left = true);
std::string truncate_string(const std::string& str, size_t length, const std::string& suffix = "...");
std::string normalize_string(const std::string& value, bool case_sensitive = false, bool trim_whitespace = true);
std::string reverse(const std::string& str);
std::string repeat(const std::string& str, size_t count);

// Binary and Encoding Operations
std::string to_hex(const unsigned char* data, size_t size, bool uppercase = false);
std::optional<std::vector<unsigned char>> from_hex(const std::string& hex_str);
std::string base64_encode(const unsigned char* data, size_t size);
std::optional<std::vector<unsigned char>> base64_decode(const std::string& encoded);
std::string detect_encoding(const std::string& data);
std::optional<std::string> convert_encoding(const std::string& str, const std::string& from_encoding, const std::string& to_encoding);
std::optional<std::string> to_utf8(const std::string& str, const std::string& from_encoding);
std::optional<std::string> from_utf8(const std::string& str, const std::string& to_encoding);

// Hashing and Checksums
uint32_t crc32(const std::string& str);
std::string md5(const std::string& str);
std::string sha1(const std::string& str);
std::string sha256(const std::string& str);
size_t fast_hash(const std::string& str);

// String Comparison and Metrics
bool iequals(const std::string& str1, const std::string& str2, const std::locale& locale = std::locale::classic());
size_t levenshtein_distance(const std::string& str1, const std::string& str2);
double similarity(const std::string& str1, const std::string& str2);

// Formatting Functions
std::string format_bytes(size_t bytes, bool binary = true);
std::string format_duration(std::chrono::milliseconds milliseconds);
std::string format_number(int64_t number, char separator = ',');
std::string format_float(double number, int precision = 2, char separator = ',');
std::string format_percentage(double value, int precision = 1);

} // namespace omop::common::string_utils