# Utils subdirectory CMakeLists.txt

add_library(omop_common_utils STATIC)

set(UTILS_SOURCES
    string_utils.cpp
    date_utils.cpp
    file_utils.cpp
    system_utils.cpp
)

set(UTILS_HEADERS
    string_utils.h
    date_utils.h
    file_utils.h
    memory_utils.h
    system_utils.h
    math_utils.h
)

target_sources(omop_common_utils PRIVATE ${UTILS_SOURCES})

omop_configure_library(omop_common_utils
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    PUBLIC_DEPS
    PRIVATE_DEPS
        OpenSSL::SSL
        OpenSSL::Crypto
        Threads::Threads
    HEADERS
        ${UTILS_HEADERS}
)

# Explicitly link filesystem library for C++20 filesystem support
# This is needed on some platforms/compilers where filesystem is not automatically linked
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" AND CMAKE_CXX_COMPILER_VERSION VERSION_LESS "9.0")
    target_link_libraries(omop_common_utils PRIVATE stdc++fs)
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang" AND CMAKE_CXX_COMPILER_VERSION VERSION_LESS "9.0")
    target_link_libraries(omop_common_utils PRIVATE c++fs)
endif()

# Ensure C++20 standard is properly set for filesystem support
target_compile_features(omop_common_utils PUBLIC cxx_std_20)
