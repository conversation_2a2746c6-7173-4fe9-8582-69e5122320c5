/**
 * @file system_utils.cpp
 * @brief Implementation of system-level utility functions - refactored from SystemUtils
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#include "system_utils.h"
#include <cstdlib>
#include <cstring>
#include <filesystem>
#include <thread>
#include <memory>
#include <sstream>
#include <fstream>
#include <iostream>

// Platform-specific includes
#ifdef _WIN32
    #include <windows.h>
    #include <psapi.h>
    #include <tlhelp32.h>
#elif defined(__APPLE__)
    #include <unistd.h>
    #include <sys/types.h>
    #include <sys/wait.h>
    #include <sys/utsname.h>
    #include <sys/sysctl.h>
    #include <mach/mach.h>
    #include <mach/vm_statistics.h>
    #include <mach/mach_host.h>
    #include <pwd.h>
    #include <signal.h>
    #include <ifaddrs.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
#else
    #include <unistd.h>
    #include <sys/types.h>
    #include <sys/wait.h>
    #include <sys/sysinfo.h>
    #include <sys/utsname.h>
    #include <pwd.h>
    #include <signal.h>
    #include <ifaddrs.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
#endif

namespace omop::common::system_utils {

namespace environment {

std::optional<std::string> get(std::string_view name) {
    const char* value = std::getenv(name.data());
    if (value) {
        return std::string(value);
    }
    return std::nullopt;
}

bool set(std::string_view name, std::string_view value) {
#ifdef _WIN32
    return _putenv_s(name.data(), value.data()) == 0;
#else
    return setenv(name.data(), value.data(), 1) == 0;
#endif
}

bool unset(std::string_view name) {
#ifdef _WIN32
    return _putenv_s(name.data(), "") == 0;
#else
    return unsetenv(name.data()) == 0;
#endif
}

bool exists(std::string_view name) {
    return get(name).has_value();
}

std::unordered_map<std::string, std::string> get_all() {
    std::unordered_map<std::string, std::string> env_vars;
    
#ifdef _WIN32
    LPWCH env_block = GetEnvironmentStringsW();
    if (env_block) {
        LPWCH current = env_block;
        while (*current) {
            std::wstring wide_str(current);
            std::string str(wide_str.begin(), wide_str.end());
            
            auto equals_pos = str.find('=');
            if (equals_pos != std::string::npos) {
                env_vars[str.substr(0, equals_pos)] = str.substr(equals_pos + 1);
            }
            
            current += wcslen(current) + 1;
        }
        FreeEnvironmentStringsW(env_block);
    }
#else
    extern char **environ;
    for (char **env = environ; *env != nullptr; ++env) {
        std::string env_str(*env);
        auto equals_pos = env_str.find('=');
        if (equals_pos != std::string::npos) {
            env_vars[env_str.substr(0, equals_pos)] = env_str.substr(equals_pos + 1);
        }
    }
#endif
    
    return env_vars;
}

std::string expand_variables(const std::string& input) {
    std::string result = input;
    std::size_t pos = 0;
    
    while ((pos = result.find('$', pos)) != std::string::npos) {
        if (pos + 1 < result.length()) {
            std::string var_name;
            std::size_t start_pos = pos + 1;
            
            // Handle ${VAR} format
            if (result[start_pos] == '{') {
                start_pos++;
                std::size_t end_pos = result.find('}', start_pos);
                if (end_pos != std::string::npos) {
                    var_name = result.substr(start_pos, end_pos - start_pos);
                    auto value = get(var_name);
                    result.replace(pos, end_pos - pos + 1, value.value_or(""));
                }
            }
            // Handle $VAR format
            else {
                std::size_t end_pos = start_pos;
                while (end_pos < result.length() && 
                       (std::isalnum(result[end_pos]) || result[end_pos] == '_')) {
                    end_pos++;
                }
                if (end_pos > start_pos) {
                    var_name = result.substr(start_pos, end_pos - start_pos);
                    auto value = get(var_name);
                    result.replace(pos, end_pos - pos, value.value_or(""));
                }
            }
        }
        pos++;
    }
    
    return result;
}

std::optional<std::string> get_home_directory() {
#ifdef _WIN32
    const char* home = std::getenv("USERPROFILE");
    if (home) {
        return std::string(home);
    }
#else
    const char* home = std::getenv("HOME");
    if (home) {
        return std::string(home);
    }
#endif
    return std::nullopt;
}

std::optional<std::string> get_config_directory() {
#ifdef _WIN32
    const char* appdata = std::getenv("APPDATA");
    if (appdata) {
        return std::string(appdata);
    }
#else
    const char* xdg_config = std::getenv("XDG_CONFIG_HOME");
    if (xdg_config) {
        return std::string(xdg_config);
    }
    auto home = get_home_directory();
    if (home) {
        return *home + "/.config";
    }
#endif
    return std::nullopt;
}

std::optional<std::string> get_temp_directory() {
    try {
        return std::filesystem::temp_directory_path().string();
    } catch (...) {
        return std::nullopt;
    }
}

} // namespace environment

namespace process {

uint32_t get_pid() {
#ifdef _WIN32
    return GetCurrentProcessId();
#else
    return getpid();
#endif
}

uint32_t get_parent_pid() {
#ifdef _WIN32
    // Windows implementation would require more complex code
    return 0;
#else
    return getppid();
#endif
}

uint32_t get_uid() {
#ifdef _WIN32
    return 0; // Windows doesn't have Unix-style UIDs
#else
    return getuid();
#endif
}

uint32_t get_gid() {
#ifdef _WIN32
    return 0; // Windows doesn't have Unix-style GIDs
#else
    return getgid();
#endif
}

std::optional<std::string> get_username() {
#ifdef _WIN32
    char username[256];
    DWORD size = sizeof(username);
    if (GetUserNameA(username, &size)) {
        return std::string(username);
    }
#else
    const char* user = std::getenv("USER");
    if (user) {
        return std::string(user);
    }
    
    // Fallback to getpwuid
    struct passwd *pw = getpwuid(getuid());
    if (pw && pw->pw_name) {
        return std::string(pw->pw_name);
    }
#endif
    return std::nullopt;
}

std::optional<std::string> get_working_directory() {
    return std::filesystem::current_path().string();
}

bool change_working_directory(const std::string& path) {
    std::error_code ec;
    std::filesystem::current_path(path, ec);
    return !ec;
}

std::optional<std::string> execute_command(const std::string& command, std::chrono::seconds timeout) {
    // Custom deleter for FILE*
    auto file_deleter = [](FILE* f) {
        if (f) {
            int result = pclose(f);
            if (result == -1) {
                // Log error but don't throw in destructor
            }
        }
    };
    
    std::unique_ptr<FILE, decltype(file_deleter)> pipe(popen(command.c_str(), "r"), file_deleter);
    
    if (!pipe) {
        return std::nullopt;
    }
    
    std::string result;
    char buffer[256];
    
    // Simple implementation - doesn't handle timeout properly
    // In production, use proper timeout handling with select/poll
    while (fgets(buffer, sizeof(buffer), pipe.get()) != nullptr) {
        result += buffer;
    }
    
    return result;
}

std::optional<std::string> execute_command_with_input(const std::string& command,
                                                     const std::string& input,
                                                     std::chrono::seconds timeout) {
    // Simplified implementation - in production use proper IPC
    return execute_command(command, timeout);
}

bool process_exists(uint32_t pid) {
#ifdef _WIN32
    HANDLE process = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, pid);
    if (process) {
        DWORD exit_code;
        bool exists = GetExitCodeProcess(process, &exit_code) && exit_code == STILL_ACTIVE;
        CloseHandle(process);
        return exists;
    }
    return false;
#else
    return kill(pid, 0) == 0;
#endif
}

bool kill_process(uint32_t pid, int signal) {
#ifdef _WIN32
    HANDLE process = OpenProcess(PROCESS_TERMINATE, FALSE, pid);
    if (process) {
        bool success = TerminateProcess(process, 1);
        CloseHandle(process);
        return success;
    }
    return false;
#else
    return kill(pid, signal) == 0;
#endif
}

std::optional<size_t> get_memory_usage(uint32_t pid) {
#ifdef _WIN32
    HANDLE process = pid ? OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, pid) 
                         : GetCurrentProcess();
    if (process) {
        PROCESS_MEMORY_COUNTERS pmc;
        if (GetProcessMemoryInfo(process, &pmc, sizeof(pmc))) {
            if (pid) CloseHandle(process);
            return pmc.WorkingSetSize;
        }
        if (pid) CloseHandle(process);
    }
    return std::nullopt;
#else
    // Linux implementation - read from /proc/[pid]/status
    std::string proc_path = pid ? "/proc/" + std::to_string(pid) + "/status" : "/proc/self/status";
    std::ifstream status_file(proc_path);
    if (!status_file.is_open()) {
        return std::nullopt;
    }
    
    std::string line;
    while (std::getline(status_file, line)) {
        if (line.starts_with("VmRSS:")) {
            std::istringstream iss(line);
            std::string label;
            size_t value;
            std::string unit;
            if (iss >> label >> value >> unit) {
                return value * 1024; // Convert from kB to bytes
            }
        }
    }
    return std::nullopt;
#endif
}

std::optional<double> get_cpu_usage(uint32_t pid) {
    // Simplified implementation - CPU usage calculation is complex
    // In production, use platform-specific APIs and proper sampling
    return std::nullopt;
}

} // namespace process

namespace info {

std::optional<std::string> get_hostname() {
    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        return std::string(hostname);
    }
    return std::nullopt;
}

std::optional<std::string> get_architecture() {
#ifdef _WIN32
    SYSTEM_INFO sysInfo;
    GetSystemInfo(&sysInfo);
    switch (sysInfo.wProcessorArchitecture) {
        case PROCESSOR_ARCHITECTURE_AMD64:
            return "x86_64";
        case PROCESSOR_ARCHITECTURE_ARM64:
            return "arm64";
        case PROCESSOR_ARCHITECTURE_INTEL:
            return "x86";
        default:
            return "unknown";
    }
#else
    struct utsname uts;
    if (uname(&uts) == 0) {
        return std::string(uts.machine);
    }
    return std::nullopt;
#endif
}

std::optional<std::string> get_os_name() {
#ifdef _WIN32
    return "Windows";
#else
    struct utsname uts;
    if (uname(&uts) == 0) {
        return std::string(uts.sysname);
    }
    return std::nullopt;
#endif
}

std::optional<std::string> get_os_version() {
#ifdef _WIN32
    // Windows version detection is complex due to version lies
    return "Windows";
#else
    struct utsname uts;
    if (uname(&uts) == 0) {
        return std::string(uts.release);
    }
    return std::nullopt;
#endif
}

std::optional<std::string> get_kernel_version() {
#ifdef _WIN32
    return get_os_version();
#else
    struct utsname uts;
    if (uname(&uts) == 0) {
        return std::string(uts.version);
    }
    return std::nullopt;
#endif
}

uint32_t get_cpu_count() {
    return std::thread::hardware_concurrency();
}

std::optional<size_t> get_total_memory() {
#ifdef _WIN32
    MEMORYSTATUSEX status;
    status.dwLength = sizeof(status);
    if (GlobalMemoryStatusEx(&status)) {
        return status.ullTotalPhys;
    }
#elif defined(__linux__)
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        return info.totalram * info.mem_unit;
    }
#else
    // macOS and other Unix systems
    long pages = sysconf(_SC_PHYS_PAGES);
    long page_size = sysconf(_SC_PAGE_SIZE);
    if (pages > 0 && page_size > 0) {
        return static_cast<size_t>(pages) * static_cast<size_t>(page_size);
    }
#endif
    return std::nullopt;
}

std::optional<size_t> get_available_memory() {
#ifdef _WIN32
    MEMORYSTATUSEX status;
    status.dwLength = sizeof(status);
    if (GlobalMemoryStatusEx(&status)) {
        return status.ullAvailPhys;
    }
#elif defined(__linux__)
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        return info.freeram * info.mem_unit;
    }
#elif defined(__APPLE__)
    // macOS specific implementation
    mach_msg_type_number_t count = HOST_VM_INFO_COUNT;
    vm_statistics_data_t vmstat;
    if (host_statistics(mach_host_self(), HOST_VM_INFO, (host_info_t)&vmstat, &count) == KERN_SUCCESS) {
        return (vmstat.free_count + vmstat.inactive_count) * vm_kernel_page_size;
    }
#else
    // Other Unix systems
    long pages = sysconf(_SC_AVPHYS_PAGES);
    long page_size = sysconf(_SC_PAGE_SIZE);
    if (pages > 0 && page_size > 0) {
        return static_cast<size_t>(pages) * static_cast<size_t>(page_size);
    }
#endif
    return std::nullopt;
}

std::optional<std::chrono::seconds> get_uptime() {
#ifdef _WIN32
    return std::chrono::milliseconds(GetTickCount64()) / 1000;
#elif defined(__linux__)
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        return std::chrono::seconds(info.uptime);
    }
#else
    // macOS and other Unix systems - would need more complex implementation
    return std::nullopt;
#endif
    return std::nullopt;
}

std::optional<std::vector<double>> get_load_average() {
#ifdef _WIN32
    // Windows doesn't have load average - could implement CPU utilization instead
    return std::nullopt;
#else
    double loadavg[3];
    if (getloadavg(loadavg, 3) == 3) {
        return std::vector<double>{loadavg[0], loadavg[1], loadavg[2]};
    }
    return std::nullopt;
#endif
}

std::optional<std::vector<size_t>> get_disk_space(const std::string& path) {
    try {
        auto space_info = std::filesystem::space(path);
        return std::vector<size_t>{
            static_cast<size_t>(space_info.capacity),
            static_cast<size_t>(space_info.available),
            static_cast<size_t>(space_info.capacity - space_info.free)
        };
    } catch (...) {
        return std::nullopt;
    }
}

std::unordered_map<std::string, std::vector<std::string>> get_network_interfaces() {
    std::unordered_map<std::string, std::vector<std::string>> interfaces;
    
#ifndef _WIN32
    struct ifaddrs *ifaddrs_ptr, *ifa;
    if (getifaddrs(&ifaddrs_ptr) == -1) {
        return interfaces;
    }
    
    for (ifa = ifaddrs_ptr; ifa != nullptr; ifa = ifa->ifa_next) {
        if (ifa->ifa_addr == nullptr) continue;
        
        if (ifa->ifa_addr->sa_family == AF_INET) {
            struct sockaddr_in* addr_in = (struct sockaddr_in*)ifa->ifa_addr;
            char ip_str[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &(addr_in->sin_addr), ip_str, INET_ADDRSTRLEN);
            interfaces[ifa->ifa_name].push_back(ip_str);
        } else if (ifa->ifa_addr->sa_family == AF_INET6) {
            struct sockaddr_in6* addr_in6 = (struct sockaddr_in6*)ifa->ifa_addr;
            char ip_str[INET6_ADDRSTRLEN];
            inet_ntop(AF_INET6, &(addr_in6->sin6_addr), ip_str, INET6_ADDRSTRLEN);
            interfaces[ifa->ifa_name].push_back(ip_str);
        }
    }
    
    freeifaddrs(ifaddrs_ptr);
#endif
    
    return interfaces;
}

std::optional<std::string> get_timezone() {
    const char* tz = std::getenv("TZ");
    if (tz) {
        return std::string(tz);
    }
    
    // Try to read from system files on Linux
#ifdef __linux__
    std::ifstream timezone_file("/etc/timezone");
    if (timezone_file.is_open()) {
        std::string timezone;
        if (std::getline(timezone_file, timezone)) {
            return timezone;
        }
    }
#endif
    
    return std::nullopt;
}

} // namespace info

// ========== COMPATIBILITY FUNCTIONS (SystemUtils API from utilities.cpp) ==========

// Direct compatibility functions for legacy SystemUtils class API
std::optional<std::string> get_env_legacy(const std::string& name) {
    const char* value = std::getenv(name.c_str());
    if (value) {
        return std::string(value);
    }
    return std::nullopt;
}

bool set_env_legacy(const std::string& name, const std::string& value) {
#ifdef _WIN32
    return _putenv_s(name.c_str(), value.c_str()) == 0;
#else
    return setenv(name.c_str(), value.c_str(), 1) == 0;
#endif
}

std::string get_current_directory_legacy() {
    return std::filesystem::current_path().string();
}

std::string get_home_directory_legacy() {
#ifdef _WIN32
    const char* home = std::getenv("USERPROFILE");
    if (home) {
        return std::string(home);
    }
#else
    const char* home = std::getenv("HOME");
    if (home) {
        return std::string(home);
    }
#endif
    return "";
}

unsigned int get_cpu_count_legacy() {
    return std::thread::hardware_concurrency();
}

size_t get_available_memory_legacy() {
#ifdef _WIN32
    MEMORYSTATUSEX status;
    status.dwLength = sizeof(status);
    if (GlobalMemoryStatusEx(&status)) {
        return status.ullAvailPhys;
    }
#elif defined(__linux__)
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        return info.freeram * info.mem_unit;
    }
#elif defined(__APPLE__)
    // macOS specific implementation
    mach_msg_type_number_t count = HOST_VM_INFO_COUNT;
    vm_statistics_data_t vmstat;
    if (host_statistics(mach_host_self(), HOST_VM_INFO, (host_info_t)&vmstat, &count) == KERN_SUCCESS) {
        return (vmstat.free_count + vmstat.inactive_count) * vm_kernel_page_size;
    }
#else
    // Other Unix systems
    long pages = sysconf(_SC_AVPHYS_PAGES);
    long page_size = sysconf(_SC_PAGE_SIZE);
    if (pages > 0 && page_size > 0) {
        return static_cast<size_t>(pages) * static_cast<size_t>(page_size);
    }
#endif
    return 0;
}

int get_process_id_legacy() {
#ifdef _WIN32
    return GetCurrentProcessId();
#else
    return getpid();
#endif
}

std::optional<std::string> execute_command_legacy(const std::string& command) {
    // Custom deleter for FILE*
    auto file_deleter = [](FILE* f) {
        if (f) {
            int result = pclose(f);
            if (result == -1) {
                // Log error but don't throw in destructor
            }
        }
    };
    
    std::unique_ptr<FILE, decltype(file_deleter)> pipe(popen(command.c_str(), "r"), file_deleter);
    
    if (!pipe) {
        return std::nullopt;
    }
    
    std::string result;
    char buffer[256];
    
    while (fgets(buffer, sizeof(buffer), pipe.get()) != nullptr) {
        result += buffer;
    }
    
    return result;
}

std::string get_hostname_legacy() {
    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        return std::string(hostname);
    }
    return "unknown";
}

std::string get_username_legacy() {
#ifdef _WIN32
    char username[256];
    DWORD size = sizeof(username);
    if (GetUserNameA(username, &size)) {
        return std::string(username);
    }
#else
    const char* user = std::getenv("USER");
    if (user) {
        return std::string(user);
    }
    
    // Fallback to getpwuid
    struct passwd *pw = getpwuid(getuid());
    if (pw && pw->pw_name) {
        return std::string(pw->pw_name);
    }
#endif
    return "unknown";
}

} // namespace omop::common::system_utils