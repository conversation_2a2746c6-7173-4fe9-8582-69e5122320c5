/**
 * @file file_utils.h
 * @brief Optimized file system utility functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include <string>
#include <string_view>
#include <vector>
#include <optional>
#include <chrono>
#include <filesystem>
#include <fstream>
#include <functional>
#include <thread>
#include <atomic>

namespace omop::common::file_utils {

/**
 * @brief Check if file exists
 * @param path File path
 * @return true if file exists
 */
bool exists(const std::filesystem::path& filepath);

/**
 * @brief Check if path is a directory
 * @param path Directory path
 * @return true if directory exists
 */
bool is_directory(const std::filesystem::path& filepath);

/**
 * @brief Check if path is a regular file
 * @param path File path
 * @return true if regular file
 */
bool is_file(const std::filesystem::path& filepath);

/**
 * @brief Get file size
 * @param path File path
 * @return Optional file size in bytes
 */
std::optional<size_t> file_size(const std::filesystem::path& filepath);

/**
 * @brief Get file modification time
 * @param path File path
 * @return Optional modification time
 */
std::optional<std::chrono::system_clock::time_point> modification_time(
    const std::filesystem::path& filepath);

/**
 * @brief Create directory (including parent directories)
 * @param path Directory path
 * @return true if successful
 */
bool create_directories(const std::filesystem::path& filepath);

/**
 * @brief Remove file or directory
 * @param path Path to remove
 * @param recursive Remove recursively if directory
 * @return true if successful
 */
bool remove(const std::filesystem::path& filepath, bool recursive = false);

/**
 * @brief Copy file or directory
 * @param source Source path
 * @param destination Destination path
 * @param overwrite Overwrite if destination exists
 * @return true if successful
 */
bool copy(const std::filesystem::path& source,
          const std::filesystem::path& destination,
          bool overwrite = false);

/**
 * @brief Move/rename file or directory
 * @param source Source path
 * @param destination Destination path
 * @return true if successful
 */
bool move(const std::filesystem::path& source,
          const std::filesystem::path& destination);

/**
 * @brief Watch file for changes
 * @param path File path
 * @return Bool indicating if file exists
 */
bool file_exists(const std::string& filepath);

/**
 * @brief Get file size
 * @param filepath File path
 * @return Optional file size in bytes
 */
std::optional<size_t> file_size(const std::string& filepath);

/**
 * @brief Get file modification time
 * @param filepath File path
 * @return Optional modification time
 */
std::string get_extension(const std::string& filepath);

/**
 * @brief Get file basename (without extension)
 * @param filepath File path
 * @return Basename
 */
std::string get_basename(const std::string& filepath);

/**
 * @brief Get file directory
 * @param filepath File path
 * @return Directory path
 */
std::string get_directory(const std::string& filepath);

/**
 * @brief Create directory (including parent directories)
 * @param path Directory path
 * @return true if successful
 */
bool create_directory(const std::string& path);

/**
 * @brief List files in directory
 * @param directory Directory path
 * @param pattern Filename pattern (regex)
 * @param recursive List recursively
 * @return Vector of file paths
 */
std::vector<std::string> list_files(const std::string& directory, const std::string& pattern = ".*", bool recursive = false);

/**
 * @brief Copy file
 * @param source Source path
 * @param destination Destination path
 * @return true if successful
 */
bool copy_file(const std::string& source, const std::string& destination);

/**
 * @brief Move file
 * @param source Source path
 * @param destination Destination path
 * @return true if successful
 */
bool move_file(const std::string& source, const std::string& destination);

/**
 * @brief Delete file
 * @param filepath File path
 * @return true if successful
 */
bool delete_file(const std::string& filepath);

/**
 * @brief Get temporary directory path
 * @return Temporary directory path
 */
std::string get_temp_directory();

/**
 * @brief Create temporary file
 * @param prefix File name prefix
 * @param extension File extension
 * @return Temporary file path
 */
std::string create_temp_file(const std::string& prefix = "tmp", const std::string& extension = ".tmp");

/**
 * @brief Read entire file as string
 * @param filepath File path
 * @return Optional file content
 */
std::optional<std::string> read_file(const std::filesystem::path& filepath);

/**
 * @brief Read entire file as string
 * @param path File path
 * @return Optional file content
 */
std::optional<std::string> read_text_file(const std::filesystem::path& filepath);

/**
 * @brief Read file as binary data
 * @param path File path
 * @return Optional binary data
 */
std::optional<std::vector<unsigned char>> read_binary_file(const std::filesystem::path& filepath);

/**
 * @brief Read file lines
 * @param path File path
 * @param max_lines Maximum lines to read (0 = all)
 * @return Optional vector of lines
 */
std::optional<std::vector<std::string>> read_lines(
    const std::filesystem::path& filepath, size_t max_lines = 0);

/**
 * @brief Read file with callback for each line
 * @param path File path
 * @param callback Callback function for each line
 * @param max_lines Maximum lines to read
 * @return true if successful
 */
bool read_lines_callback(const std::filesystem::path& filepath,
                        std::function<bool(const std::string&, size_t)> callback,
                        size_t max_lines = 0);

/**
 * @brief Read first N bytes of file
 * @param path File path
 * @param num_bytes Number of bytes to read
 * @return Optional data
 */
std::optional<std::vector<unsigned char>> read_first_bytes(
    const std::filesystem::path& filepath, size_t num_bytes);

/**
 * @brief Read last N bytes of file
 * @param path File path
 * @param num_bytes Number of bytes to read
 * @return Optional data
 */
std::optional<std::vector<unsigned char>> read_last_bytes(
    const std::filesystem::path& filepath, size_t num_bytes);

/**
 * @brief Write string to file
 * @param filepath File path
 * @param content Content to write
 * @param append Append to file if true
 * @return true if successful
 */
bool write_file(const std::string& filepath, const std::string& content, bool append = false);

/**
 * @brief Write string to file
 * @param path File path
 * @param content Content to write
 * @param append Append to file if true
 * @return true if successful
 */
bool write_text_file(const std::filesystem::path& filepath,
                    std::string_view content,
                    bool append = false);

/**
 * @brief Write binary data to file
 * @param path File path
 * @param data Binary data
 * @param append Append to file if true
 * @return true if successful
 */
bool write_binary_file(const std::filesystem::path& filepath,
                      const std::vector<unsigned char>& data,
                      bool append = false);

/**
 * @brief Write lines to file
 * @param path File path
 * @param lines Lines to write
 * @param line_ending Line ending to use
 * @param append Append to file if true
 * @return true if successful
 */
bool write_lines(const std::filesystem::path& filepath,
                const std::vector<std::string>& lines,
                std::string_view line_ending = "\n",
                bool append = false);

/**
 * @brief Create empty file
 * @param path File path
 * @return true if successful
 */
bool touch(const std::filesystem::path& filepath);

/**
 * @brief Get file extension
 * @param path File path
 * @return File extension (including dot)
 */
std::string extension(const std::filesystem::path& filepath);

/**
 * @brief Get filename without extension
 * @param path File path
 * @return Filename without extension
 */
std::string stem(const std::filesystem::path& filepath);

/**
 * @brief Get filename with extension
 * @param path File path
 * @return Filename with extension
 */
std::string filename(const std::filesystem::path& filepath);

/**
 * @brief Get parent directory
 * @param path File path
 * @return Parent directory path
 */
std::filesystem::path parent_path(const std::filesystem::path& filepath);

/**
 * @brief Join path components
 * @param components Path components
 * @return Joined path
 */
std::filesystem::path join(const std::vector<std::string>& components);

/**
 * @brief Make path absolute
 * @param path Relative path
 * @param base Base directory (default: current directory)
 * @return Absolute path
 */
std::filesystem::path absolute(const std::filesystem::path& filepath,
                               const std::filesystem::path& base = std::filesystem::current_path());

/**
 * @brief Make path relative to base
 * @param path Absolute path
 * @param base Base directory
 * @return Relative path
 */
std::optional<std::filesystem::path> relative(const std::filesystem::path& filepath,
                                              const std::filesystem::path& base);

/**
 * @brief Normalize path (resolve . and ..)
 * @param path Path to normalize
 * @return Normalized path
 */
std::filesystem::path normalize(const std::filesystem::path& filepath);

/**
 * @brief List directory contents
 * @param path Directory path
 * @param recursive List recursively
 * @param include_hidden Include hidden files/directories
 * @return Optional vector of paths
 */
std::optional<std::vector<std::filesystem::path>> list_directory(
    const std::filesystem::path& filepath,
    bool recursive = false,
    bool include_hidden = false);

/**
 * @brief Find files matching pattern
 * @param directory Directory to search
 * @param pattern Glob pattern (e.g., "*.txt")
 * @param recursive Search recursively
 * @return Vector of matching paths
 */
std::vector<std::filesystem::path> find_files(
    const std::filesystem::path& directory,
    std::string_view pattern,
    bool recursive = false);

/**
 * @brief Get directory size
 * @param path Directory path
 * @param recursive Include subdirectories
 * @return Optional total size in bytes
 */
std::optional<size_t> directory_size(const std::filesystem::path& filepath, bool recursive = true);

/**
 * @brief Create temporary directory
 * @param prefix Directory name prefix
 * @return Optional temporary directory path
 */
std::optional<std::filesystem::path> create_temp_directory(std::string_view prefix = "omop_");

/**
 * @brief Get current working directory
 * @return Current directory path
 */
std::filesystem::path current_directory();

/**
 * @brief Change current working directory
 * @param path New working directory
 * @return true if successful
 */
bool change_directory(const std::filesystem::path& filepath);

/**
 * @brief Check if file is readable
 * @param path File path
 * @return true if readable
 */
bool is_readable(const std::filesystem::path& filepath);

/**
 * @brief Check if file is writable
 * @param path File path
 * @return true if writable
 */
bool is_writable(const std::filesystem::path& filepath);

/**
 * @brief Check if file is executable
 * @param path File path
 * @return true if executable
 */
bool is_executable(const std::filesystem::path& filepath);

/**
 * @brief Set file permissions
 * @param path File path
 * @param perms Permissions to set
 * @return true if successful
 */
bool set_permissions(const std::filesystem::path& filepath,
                     std::filesystem::perms perms);

/**
 * @brief Get file permissions
 * @param path File path
 * @return Optional permissions
 */
std::optional<std::filesystem::perms> get_permissions(const std::filesystem::path& filepath);

/**
 * @brief File change event
 */
enum class FileEvent {
    Created,
    Modified,
    Deleted,
    Moved
};

/**
 * @brief File watcher callback
 */
using FileWatchCallback = std::function<void(const std::filesystem::path&, FileEvent)>;

/**
 * @brief Simple file watcher
 */
class FileWatcher {
public:
    /**
     * @brief Constructor
     * @param path Path to watch
     * @param callback Callback for file events
     * @param poll_interval Polling interval
     */
    FileWatcher(const std::filesystem::path& filepath,
               FileWatchCallback callback,
               std::chrono::milliseconds poll_interval = std::chrono::milliseconds(1000));

    /**
     * @brief Destructor
     */
    ~FileWatcher();

    /**
     * @brief Start watching
     */
    void start();

    /**
     * @brief Stop watching
     */
    void stop();

private:
    std::filesystem::path watch_path_;
    FileWatchCallback callback_;
    std::chrono::milliseconds poll_interval_;

    std::thread watch_thread_;
    std::atomic<bool> running_{false};
    std::atomic<bool> should_stop_{false};

    std::filesystem::file_time_type last_write_time_;

    void watch_loop();
};

/**
 * @brief Create backup of file
 * @param source_path Source file path
 * @param backup_suffix Backup file suffix (default: ".backup")
 * @param timestamp_suffix Add timestamp to suffix
 * @return Optional backup file path
 */
std::optional<std::filesystem::path> create_backup(
    const std::filesystem::path& source_path,
    std::string_view backup_suffix = ".backup",
    bool timestamp_suffix = false);

/**
 * @brief Restore file from backup
 * @param backup_path Backup file path
 * @param target_path Target file path (optional, inferred if not provided)
 * @return true if successful
 */
bool restore_backup(const std::filesystem::path& backup_path,
                   const std::optional<std::filesystem::path>& target_path = std::nullopt);

/**
 * @brief Find backup files for a given file
 * @param file_path Original file path
 * @param backup_suffix Backup suffix to look for
 * @return Vector of backup file paths
 */
std::vector<std::filesystem::path> find_backups(
    const std::filesystem::path& file_path,
    std::string_view backup_suffix = ".backup");

/**
 * @brief Clean old backup files
 * @param file_path Original file path
 * @param max_backups Maximum number of backups to keep
 * @param max_age Maximum age of backups to keep
 * @return Number of backups removed
 */
size_t cleanup_backups(const std::filesystem::path& file_path,
                      size_t max_backups = 5,
                      std::chrono::hours max_age = std::chrono::hours(24 * 30));

/**
 * @brief Calculate file checksum
 * @param path File path
 * @param algorithm Hash algorithm ("md5", "sha1", "sha256", "crc32")
 * @return Optional checksum string
 */
std::optional<std::string> calculate_checksum(const std::filesystem::path& filepath,
                                             std::string_view algorithm = "sha256");

/**
 * @brief Verify file checksum
 * @param path File path
 * @param expected_checksum Expected checksum
 * @param algorithm Hash algorithm
 * @return true if checksum matches
 */
bool verify_checksum(const std::filesystem::path& filepath,
                    std::string_view expected_checksum,
                    std::string_view algorithm = "sha256");

/**
 * @brief Check if file is text file
 * @param path File path
 * @param sample_size Number of bytes to sample
 * @return true if appears to be text
 */
bool is_text_file(const std::filesystem::path& filepath, size_t sample_size = 1024);

/**
 * @brief Detect file encoding
 * @param path File path
 * @param sample_size Number of bytes to sample
 * @return Detected encoding name
 */
std::string detect_encoding(const std::filesystem::path& filepath, size_t sample_size = 1024);

} // namespace omop::common::file_utils