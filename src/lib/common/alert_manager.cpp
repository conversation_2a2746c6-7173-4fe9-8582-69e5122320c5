/**
 * @file alert_manager.cpp
 * @brief Implementation of alert management system for OMOP ETL monitoring
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#include "alert_manager.h"
#include "http_client.h"
#include <sstream>
#include <iomanip>
#include <random>
#include <iostream>
#include <fstream>
#include <ctime>
#include <algorithm>
#include <nlohmann/json.hpp>

namespace omop::common {

// Helper functions for converting enums to strings
std::string alert_severity_to_string(AlertSeverity severity) {
    switch (severity) {
        case AlertSeverity::INFO: return "INFO";
        case AlertSeverity::WARNING: return "WARNING";
        case AlertSeverity::ERROR: return "ERROR";
        case AlertSeverity::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

std::string alert_status_to_string(AlertStatus status) {
    switch (status) {
        case AlertStatus::ACTIVE: return "ACTIVE";
        case AlertStatus::RESOLVED: return "RESOLVED";
        case AlertStatus::SILENCED: return "SILENCED";
        case AlertStatus::PENDING: return "PENDING";
        default: return "UNKNOWN";
    }
}

// ConsoleAlertChannel implementation
bool ConsoleAlertChannel::send_alert(const Alert& alert) {
    auto now = std::chrono::system_clock::to_time_t(alert.created_at);
    
    std::cout << "\n[ALERT] " << alert_severity_to_string(alert.severity) 
              << " - " << alert.component_name << "/" << alert.alert_name << std::endl;
    std::cout << "Message: " << alert.message << std::endl;
    std::cout << "Time: " << std::put_time(std::localtime(&now), "%Y-%m-%d %H:%M:%S") << std::endl;
    
    if (!alert.description.empty()) {
        std::cout << "Description: " << alert.description << std::endl;
    }
    
    if (alert.value != 0.0 || alert.threshold != 0.0) {
        std::cout << "Value: " << alert.value << ", Threshold: " << alert.threshold << std::endl;
    }
    
    if (!alert.labels.empty()) {
        std::cout << "Labels: ";
        for (const auto& [key, value] : alert.labels) {
            std::cout << key << "=" << value << " ";
        }
        std::cout << std::endl;
    }
    
    std::cout << "Status: " << alert_status_to_string(alert.status) << std::endl;
    std::cout << "---" << std::endl;
    
    return true;
}


// EmailAlertChannel implementation
EmailAlertChannel::EmailAlertChannel(const Config& config) : config_(config) {}

bool EmailAlertChannel::send_alert(const Alert& alert) {
    try {
        // Create HTTP client for SMTP over HTTP (using a service like SendGrid, Mailgun, etc.)
        auto client = HttpClientFactory::create_client();
        if (!client) {
            std::cerr << "[EMAIL] Failed to create HTTP client" << std::endl;
            return false;
        }
        
        // Prepare email payload for HTTP-based email service
        nlohmann::json email_payload;
        email_payload["from"] = config_.from_address;
        email_payload["to"] = config_.to_addresses;
        email_payload["subject"] = "[" + alert_severity_to_string(alert.severity) + "] " 
                                   + alert.component_name + " - " + alert.alert_name;
        email_payload["text"] = format_email_content(alert);
        
        // Send via HTTP-based email service
        HttpClient::Request request;
        request.method = HttpClient::Method::POST;
        request.url = config_.smtp_server; // This would be the email service endpoint
        request.body = email_payload.dump();
        request.headers["Content-Type"] = "application/json";
        request.headers["X-API-Key"] = config_.username; // Use username as API key
        request.timeout_seconds = 30;
        
        auto response = client->make_request(request);
        return response.status_code >= 200 && response.status_code < 300;
        
    } catch (const std::exception& e) {
        std::cerr << "[EMAIL] Send failed: " << e.what() << std::endl;
        return false;
    }
}

bool EmailAlertChannel::test_connection() {
    // TODO: Implement SMTP connection test
    std::cout << "[EMAIL] Testing connection to " << config_.smtp_server 
              << ":" << config_.smtp_port << std::endl;
    return true;
}

std::string EmailAlertChannel::format_email_content(const Alert& alert) const {
    std::ostringstream oss;
    oss << "Alert Details:\n";
    oss << "Component: " << alert.component_name << "\n";
    oss << "Alert: " << alert.alert_name << "\n";
    oss << "Severity: " << alert_severity_to_string(alert.severity) << "\n";
    oss << "Status: " << alert_status_to_string(alert.status) << "\n";
    oss << "Message: " << alert.message << "\n";
    
    if (!alert.description.empty()) {
        oss << "Description: " << alert.description << "\n";
    }
    
    if (alert.value != 0.0 || alert.threshold != 0.0) {
        oss << "Value: " << alert.value << "\n";
        oss << "Threshold: " << alert.threshold << "\n";
    }
    
    auto now = std::chrono::system_clock::to_time_t(alert.created_at);
    oss << "Time: " << std::put_time(std::localtime(&now), "%Y-%m-%d %H:%M:%S") << "\n";
    
    return oss.str();
}


// SlackAlertChannel implementation
SlackAlertChannel::SlackAlertChannel(const Config& config) : config_(config) {}

bool SlackAlertChannel::send_alert(const Alert& alert) {
    try {
        // Create HTTP client for Slack webhook
        auto client = HttpClientFactory::create_client();
        if (!client) {
            std::cerr << "[SLACK] Failed to create HTTP client" << std::endl;
            return false;
        }
        
        // Prepare Slack webhook payload
        nlohmann::json slack_payload;
        slack_payload["channel"] = config_.channel;
        slack_payload["username"] = config_.username;
        slack_payload["icon_emoji"] = config_.icon_emoji;
        slack_payload["text"] = "Alert: " + alert.component_name + "/" + alert.alert_name;
        
        // Create attachments for better formatting
        nlohmann::json attachment;
        attachment["color"] = (alert.severity == AlertSeverity::CRITICAL ? "danger" : 
                               alert.severity == AlertSeverity::ERROR ? "warning" : "good");
        
        nlohmann::json fields = nlohmann::json::array();
        fields.push_back({{"title", "Severity"}, {"value", alert_severity_to_string(alert.severity)}, {"short", true}});
        fields.push_back({{"title", "Status"}, {"value", alert_status_to_string(alert.status)}, {"short", true}});
        fields.push_back({{"title", "Message"}, {"value", alert.message}, {"short", false}});
        
        if (alert.value != 0.0 || alert.threshold != 0.0) {
            fields.push_back({{"title", "Value"}, {"value", std::to_string(alert.value)}, {"short", true}});
            fields.push_back({{"title", "Threshold"}, {"value", std::to_string(alert.threshold)}, {"short", true}});
        }
        
        attachment["fields"] = fields;
        slack_payload["attachments"] = nlohmann::json::array({attachment});
        
        // Send to Slack webhook
        HttpClient::Request request;
        request.method = HttpClient::Method::POST;
        request.url = config_.webhook_url;
        request.body = slack_payload.dump();
        request.headers["Content-Type"] = "application/json";
        request.timeout_seconds = config_.timeout.count();
        
        auto response = client->make_request(request);
        return response.status_code >= 200 && response.status_code < 300;
        
    } catch (const std::exception& e) {
        std::cerr << "[SLACK] Send failed: " << e.what() << std::endl;
        return false;
    }
}

bool SlackAlertChannel::test_connection() {
    // TODO: Implement Slack webhook connectivity test
    std::cout << "[SLACK] Testing webhook: " << config_.webhook_url << std::endl;
    return true;
}

std::string SlackAlertChannel::format_slack_payload(const Alert& alert) const {
    std::ostringstream oss;
    oss << "{\n";
    oss << "  \"channel\": \"" << config_.channel << "\",\n";
    oss << "  \"username\": \"" << config_.username << "\",\n";
    oss << "  \"icon_emoji\": \"" << config_.icon_emoji << "\",\n";
    oss << "  \"text\": \"Alert: " << alert.component_name << "/" << alert.alert_name << "\",\n";
    oss << "  \"attachments\": [{\n";
    oss << "    \"color\": \"" << (alert.severity == AlertSeverity::CRITICAL ? "danger" : 
                                   alert.severity == AlertSeverity::ERROR ? "warning" : "good") << "\",\n";
    oss << "    \"fields\": [\n";
    oss << "      {\"title\": \"Severity\", \"value\": \"" << alert_severity_to_string(alert.severity) << "\", \"short\": true},\n";
    oss << "      {\"title\": \"Status\", \"value\": \"" << alert_status_to_string(alert.status) << "\", \"short\": true},\n";
    oss << "      {\"title\": \"Message\", \"value\": \"" << alert.message << "\", \"short\": false}\n";
    
    if (alert.value != 0.0 || alert.threshold != 0.0) {
        oss << "      ,{\"title\": \"Value\", \"value\": \"" << alert.value << "\", \"short\": true},\n";
        oss << "      {\"title\": \"Threshold\", \"value\": \"" << alert.threshold << "\", \"short\": true}\n";
    }
    
    oss << "    ]\n";
    oss << "  }]\n";
    oss << "}";
    
    return oss.str();
}


// WebhookAlertChannel implementation
WebhookAlertChannel::WebhookAlertChannel(const std::string& name, const Config& config) 
    : channel_name_(name), config_(config) {}

bool WebhookAlertChannel::send_alert(const Alert& alert) {
    try {
        // Create HTTP client for webhook
        auto client = HttpClientFactory::create_client();
        if (!client) {
            std::cerr << "[WEBHOOK:" << channel_name_ << "] Failed to create HTTP client" << std::endl;
            return false;
        }
        
        // Prepare webhook payload
        nlohmann::json webhook_payload;
        webhook_payload["alert_id"] = alert.id;
        webhook_payload["component_name"] = alert.component_name;
        webhook_payload["alert_name"] = alert.alert_name;
        webhook_payload["severity"] = alert_severity_to_string(alert.severity);
        webhook_payload["status"] = alert_status_to_string(alert.status);
        webhook_payload["message"] = alert.message;
        webhook_payload["description"] = alert.description;
        webhook_payload["created_at"] = std::chrono::duration_cast<std::chrono::seconds>(
            alert.created_at.time_since_epoch()).count();
        webhook_payload["value"] = alert.value;
        webhook_payload["threshold"] = alert.threshold;
        webhook_payload["labels"] = alert.labels;
        webhook_payload["annotations"] = alert.annotations;
        
        // Determine HTTP method
        HttpClient::Method method = HttpClient::Method::POST;
        if (config_.method == "GET") method = HttpClient::Method::GET;
        else if (config_.method == "PUT") method = HttpClient::Method::PUT;
        else if (config_.method == "DELETE") method = HttpClient::Method::DELETE;
        else if (config_.method == "PATCH") method = HttpClient::Method::PATCH;
        
        // Send webhook
        HttpClient::Request request;
        request.method = method;
        request.url = config_.url;
        request.body = webhook_payload.dump();
        request.headers = config_.headers;
        request.headers["Content-Type"] = config_.content_type;
        request.timeout_seconds = config_.timeout.count();
        
        auto response = client->make_request(request);
        return response.status_code >= 200 && response.status_code < 300;
        
    } catch (const std::exception& e) {
        std::cerr << "[WEBHOOK:" << channel_name_ << "] Send failed: " << e.what() << std::endl;
        return false;
    }
}

bool WebhookAlertChannel::test_connection() {
    // TODO: Implement webhook connectivity test
    std::cout << "[WEBHOOK:" << channel_name_ << "] Testing: " << config_.url << std::endl;
    return true;
}

std::string WebhookAlertChannel::format_webhook_payload(const Alert& alert) const {
    std::ostringstream oss;
    oss << "{\n";
    oss << "  \"alert_id\": \"" << alert.id << "\",\n";
    oss << "  \"component_name\": \"" << alert.component_name << "\",\n";
    oss << "  \"alert_name\": \"" << alert.alert_name << "\",\n";
    oss << "  \"severity\": \"" << alert_severity_to_string(alert.severity) << "\",\n";
    oss << "  \"status\": \"" << alert_status_to_string(alert.status) << "\",\n";
    oss << "  \"message\": \"" << alert.message << "\",\n";
    oss << "  \"description\": \"" << alert.description << "\",\n";
    oss << "  \"value\": " << alert.value << ",\n";
    oss << "  \"threshold\": " << alert.threshold << ",\n";
    oss << "  \"created_at\": \"" << std::chrono::duration_cast<std::chrono::seconds>(alert.created_at.time_since_epoch()).count() << "\",\n";
    oss << "  \"labels\": {\n";
    
    bool first = true;
    for (const auto& [key, value] : alert.labels) {
        if (!first) oss << ",\n";
        oss << "    \"" << key << "\": \"" << value << "\"";
        first = false;
    }
    
    oss << "\n  }\n";
    oss << "}";
    
    return oss.str();
}


// AlertManager implementation
AlertManager::AlertManager() : config_{} {}

AlertManager::AlertManager(const Config& config) : config_(config) {
    if (config_.persist_alerts) {
        load_alerts();
    }
}

AlertManager::~AlertManager() {
    stop();
    if (config_.persist_alerts) {
        save_alerts();
    }
}

void AlertManager::start() {
    if (running_.load() || !config_.enabled) {
        return;
    }
    
    running_ = true;
    
    // Start evaluation thread
    evaluation_thread_ = std::make_unique<std::thread>(&AlertManager::evaluation_loop, this);
    
    // Start cleanup thread
    cleanup_thread_ = std::make_unique<std::thread>(&AlertManager::cleanup_loop, this);
    
    // Start notification thread
    notification_thread_ = std::make_unique<std::thread>(&AlertManager::notification_loop, this);
    
    std::cout << "[AlertManager] Started with " << alert_rules_.size() << " rules and " 
              << alert_channels_.size() << " channels" << std::endl;
}

void AlertManager::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_ = false;
    
    // Wake up all threads
    evaluation_cv_.notify_all();
    pending_alerts_cv_.notify_all();
    
    // Join threads
    if (evaluation_thread_ && evaluation_thread_->joinable()) {
        evaluation_thread_->join();
    }
    
    if (cleanup_thread_ && cleanup_thread_->joinable()) {
        cleanup_thread_->join();
    }
    
    if (notification_thread_ && notification_thread_->joinable()) {
        notification_thread_->join();
    }
    
    std::cout << "[AlertManager] Stopped" << std::endl;
}

void AlertManager::add_alert_rule(const AlertRule& rule) {
    std::lock_guard<std::mutex> lock(alerts_mutex_);
    alert_rules_[rule.rule_name] = rule;
    
    // Set up default evaluator if not provided
    if (!alert_rules_[rule.rule_name].evaluator) {
        auto& rule_ref = alert_rules_[rule.rule_name];
        
        if (rule.condition == "greater_than") {
            rule_ref.evaluator = [](double value, double threshold) { return value > threshold; };
        } else if (rule.condition == "less_than") {
            rule_ref.evaluator = [](double value, double threshold) { return value < threshold; };
        } else if (rule.condition == "equals") {
            rule_ref.evaluator = [](double value, double threshold) { return std::abs(value - threshold) < 1e-9; };
        } else if (rule.condition == "not_equals") {
            rule_ref.evaluator = [](double value, double threshold) { return std::abs(value - threshold) >= 1e-9; };
        } else {
            // Default to greater_than
            rule_ref.evaluator = [](double value, double threshold) { return value > threshold; };
        }
    }
    
    std::cout << "[AlertManager] Added alert rule: " << rule.rule_name << std::endl;
}

void AlertManager::remove_alert_rule(const std::string& rule_name) {
    std::lock_guard<std::mutex> lock(alerts_mutex_);
    alert_rules_.erase(rule_name);
    std::cout << "[AlertManager] Removed alert rule: " << rule_name << std::endl;
}

void AlertManager::add_alert_channel(std::unique_ptr<IAlertChannel> channel) {
    if (!channel) return;
    
    std::string channel_name = channel->get_channel_name();
    alert_channels_.push_back(std::move(channel));
    
    std::cout << "[AlertManager] Added alert channel: " << channel_name << std::endl;
}

void AlertManager::remove_alert_channel(const std::string& channel_name) {
    alert_channels_.erase(
        std::remove_if(alert_channels_.begin(), alert_channels_.end(),
                      [&channel_name](const std::unique_ptr<IAlertChannel>& channel) {
                          return channel && channel->get_channel_name() == channel_name;
                      }),
        alert_channels_.end());
    
    std::cout << "[AlertManager] Removed alert channel: " << channel_name << std::endl;
}

void AlertManager::fire_alert(const Alert& alert) {
    {
        std::lock_guard<std::mutex> lock(pending_alerts_mutex_);
        pending_alerts_.push(alert);
    }
    pending_alerts_cv_.notify_one();
}

void AlertManager::resolve_alert(const std::string& alert_id) {
    std::lock_guard<std::mutex> lock(alerts_mutex_);
    
    auto it = alerts_.find(alert_id);
    if (it != alerts_.end() && it->second.status == AlertStatus::ACTIVE) {
        it->second.status = AlertStatus::RESOLVED;
        it->second.resolved_at = std::chrono::system_clock::now();
        it->second.updated_at = it->second.resolved_at;
        
        std::cout << "[AlertManager] Resolved alert: " << alert_id << std::endl;
    }
}

void AlertManager::silence_alert(const std::string& alert_id, std::chrono::seconds duration) {
    std::lock_guard<std::mutex> lock(alerts_mutex_);
    
    auto it = alerts_.find(alert_id);
    if (it != alerts_.end()) {
        it->second.status = AlertStatus::SILENCED;
        it->second.updated_at = std::chrono::system_clock::now();
        
        // TODO: Implement silence expiration
        std::cout << "[AlertManager] Silenced alert " << alert_id 
                  << " for " << duration.count() << " seconds" << std::endl;
    }
}

std::vector<Alert> AlertManager::get_active_alerts(std::optional<AlertSeverity> severity_filter) const {
    std::lock_guard<std::mutex> lock(alerts_mutex_);
    
    std::vector<Alert> active_alerts;
    for (const auto& [id, alert] : alerts_) {
        if (alert.status == AlertStatus::ACTIVE) {
            if (!severity_filter || alert.severity == *severity_filter) {
                active_alerts.push_back(alert);
            }
        }
    }
    
    // Sort by creation time (newest first)
    std::sort(active_alerts.begin(), active_alerts.end(),
              [](const Alert& a, const Alert& b) {
                  return a.created_at > b.created_at;
              });
    
    return active_alerts;
}

std::optional<Alert> AlertManager::get_alert(const std::string& alert_id) const {
    std::lock_guard<std::mutex> lock(alerts_mutex_);
    
    auto it = alerts_.find(alert_id);
    if (it != alerts_.end()) {
        return it->second;
    }
    
    return std::nullopt;
}

std::vector<Alert> AlertManager::get_alert_history(const std::string& component_name, size_t limit) const {
    std::lock_guard<std::mutex> lock(alerts_mutex_);
    
    std::vector<Alert> history;
    for (const auto& [id, alert] : alerts_) {
        if (component_name.empty() || alert.component_name == component_name) {
            history.push_back(alert);
        }
    }
    
    // Sort by creation time (newest first)
    std::sort(history.begin(), history.end(),
              [](const Alert& a, const Alert& b) {
                  return a.created_at > b.created_at;
              });
    
    // Limit results
    if (history.size() > limit) {
        history.resize(limit);
    }
    
    return history;
}

void AlertManager::set_health_monitor(std::shared_ptr<HealthMonitor> health_monitor) {
    health_monitor_ = health_monitor;
}

void AlertManager::process_health_results(const std::unordered_map<std::string, HealthCheckResult>& results) {
    for (const auto& [component_name, result] : results) {
        if (!result.is_healthy()) {
            Alert alert;
            alert.id = generate_alert_id();
            alert.component_name = component_name;
            alert.alert_name = "HealthCheck";
            alert.severity = (result.status == HealthStatus::UNHEALTHY) ? 
                           AlertSeverity::ERROR : AlertSeverity::WARNING;
            alert.status = AlertStatus::ACTIVE;
            alert.message = result.message;
            alert.description = "Health check failed for component: " + component_name;
            alert.created_at = result.timestamp;
            alert.updated_at = alert.created_at;
            alert.labels["component"] = component_name;
            alert.labels["health_status"] = (result.status == HealthStatus::UNHEALTHY) ? "unhealthy" : "degraded";
            
            fire_alert(alert);
        }
    }
}

AlertManager::Statistics AlertManager::get_statistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

std::string AlertManager::export_prometheus_metrics() const {
    std::lock_guard<std::mutex> lock(alerts_mutex_);
    std::lock_guard<std::mutex> stats_lock(stats_mutex_);
    
    std::ostringstream oss;
    
    // Alert counts by status
    size_t active_count = 0, resolved_count = 0, silenced_count = 0;
    std::unordered_map<std::string, size_t> severity_counts;
    
    for (const auto& [id, alert] : alerts_) {
        switch (alert.status) {
            case AlertStatus::ACTIVE: active_count++; break;
            case AlertStatus::RESOLVED: resolved_count++; break;
            case AlertStatus::SILENCED: silenced_count++; break;
            default: break;
        }
        
        std::string severity_str = alert_severity_to_string(alert.severity);
        severity_counts[severity_str]++;
    }
    
    oss << "# HELP omop_alerts_total Total number of alerts by status\n";
    oss << "# TYPE omop_alerts_total gauge\n";
    oss << "omop_alerts_total{status=\"active\"} " << active_count << "\n";
    oss << "omop_alerts_total{status=\"resolved\"} " << resolved_count << "\n";
    oss << "omop_alerts_total{status=\"silenced\"} " << silenced_count << "\n";
    
    oss << "# HELP omop_alerts_by_severity Total number of alerts by severity\n";
    oss << "# TYPE omop_alerts_by_severity gauge\n";
    for (const auto& [severity, count] : severity_counts) {
        oss << "omop_alerts_by_severity{severity=\"" << severity << "\"} " << count << "\n";
    }
    
    oss << "# HELP omop_alert_notifications_sent_total Total notifications sent\n";
    oss << "# TYPE omop_alert_notifications_sent_total counter\n";
    oss << "omop_alert_notifications_sent_total " << stats_.notifications_sent << "\n";
    
    oss << "# HELP omop_alert_notification_failures_total Total notification failures\n";
    oss << "# TYPE omop_alert_notification_failures_total counter\n";
    oss << "omop_alert_notification_failures_total " << stats_.notification_failures << "\n";
    
    oss << "# HELP omop_alert_rules_total Total number of alert rules\n";
    oss << "# TYPE omop_alert_rules_total gauge\n";
    oss << "omop_alert_rules_total " << alert_rules_.size() << "\n";
    
    oss << "# HELP omop_alert_channels_total Total number of alert channels\n";
    oss << "# TYPE omop_alert_channels_total gauge\n";
    oss << "omop_alert_channels_total " << alert_channels_.size() << "\n";
    
    return oss.str();
}

void AlertManager::evaluation_loop() {
    while (running_.load()) {
        try {
            auto start_time = std::chrono::system_clock::now();
            
            // Evaluate all alert rules
            {
                std::lock_guard<std::mutex> lock(alerts_mutex_);
                for (const auto& [rule_name, rule] : alert_rules_) {
                    if (rule.enabled) {
                        evaluate_rule(rule);
                    }
                }
            }
            
            {
                std::lock_guard<std::mutex> lock(stats_mutex_);
                stats_.last_evaluation_time = start_time;
            }
            
            // Wait for next evaluation interval
            std::unique_lock<std::mutex> lock(evaluation_mutex_);
            evaluation_cv_.wait_for(lock, config_.evaluation_interval, [this] { return !running_.load(); });
            
        } catch (const std::exception& e) {
            std::cerr << "[AlertManager] Error in evaluation loop: " << e.what() << std::endl;
        }
    }
}

void AlertManager::cleanup_loop() {
    while (running_.load()) {
        try {
            auto now = std::chrono::system_clock::now();
            auto cutoff_time = now - config_.alert_retention_period;
            
            {
                std::lock_guard<std::mutex> lock(alerts_mutex_);
                auto it = alerts_.begin();
                while (it != alerts_.end()) {
                    if (it->second.status == AlertStatus::RESOLVED && 
                        it->second.resolved_at < cutoff_time) {
                        it = alerts_.erase(it);
                    } else {
                        ++it;
                    }
                }
                
                // Also limit total number of alerts
                if (alerts_.size() > config_.max_alerts) {
                    // Convert to vector and sort by creation time
                    std::vector<std::pair<std::string, Alert>> alert_pairs(alerts_.begin(), alerts_.end());
                    std::sort(alert_pairs.begin(), alert_pairs.end(),
                             [](const auto& a, const auto& b) {
                                 return a.second.created_at < b.second.created_at;
                             });
                    
                    // Remove oldest alerts
                    size_t to_remove = alerts_.size() - config_.max_alerts;
                    for (size_t i = 0; i < to_remove; ++i) {
                        alerts_.erase(alert_pairs[i].first);
                    }
                }
            }
            
            // Wait for next cleanup interval
            std::this_thread::sleep_for(config_.cleanup_interval);
            
        } catch (const std::exception& e) {
            std::cerr << "[AlertManager] Error in cleanup loop: " << e.what() << std::endl;
        }
    }
}

void AlertManager::notification_loop() {
    while (running_.load()) {
        try {
            std::unique_lock<std::mutex> lock(pending_alerts_mutex_);
            pending_alerts_cv_.wait(lock, [this] { return !pending_alerts_.empty() || !running_.load(); });
            
            while (!pending_alerts_.empty()) {
                Alert alert = pending_alerts_.front();
                pending_alerts_.pop();
                lock.unlock();
                
                // Store alert
                {
                    std::lock_guard<std::mutex> alerts_lock(alerts_mutex_);
                    alerts_[alert.id] = alert;
                    
                    std::lock_guard<std::mutex> stats_lock(stats_mutex_);
                    stats_.total_alerts++;
                    stats_.active_alerts++;
                    stats_.last_alert_time = alert.created_at;
                }
                
                // Send notifications
                send_alert_notifications(alert);
                
                lock.lock();
            }
            
        } catch (const std::exception& e) {
            std::cerr << "[AlertManager] Error in notification loop: " << e.what() << std::endl;
        }
    }
}

void AlertManager::evaluate_rule(const AlertRule& rule) {
    // TODO: Implement metric collection and rule evaluation
    // This would integrate with metrics collector to get current values
    // For now, this is a placeholder that demonstrates the structure
    
    // Simulated metric value (would come from metrics collector)
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_real_distribution<> dis(0.0, 100.0);
    
    double metric_value = dis(gen);
    
    if (rule.evaluator && rule.evaluator(metric_value, rule.threshold)) {
        // Check if alert already exists for this rule
        std::string alert_key = rule.component_name + ":" + rule.rule_name;
        bool alert_exists = false;
        
        for (const auto& [id, alert] : alerts_) {
            if (alert.component_name == rule.component_name && 
                alert.alert_name == rule.rule_name && 
                alert.status == AlertStatus::ACTIVE) {
                alert_exists = true;
                break;
            }
        }
        
        if (!alert_exists) {
            Alert alert;
            alert.id = generate_alert_id();
            alert.component_name = rule.component_name;
            alert.alert_name = rule.rule_name;
            alert.severity = rule.severity;
            alert.status = AlertStatus::ACTIVE;
            alert.message = rule.message_template.empty() ? 
                          ("Threshold exceeded for " + rule.metric_name) : rule.message_template;
            alert.description = "Alert rule '" + rule.rule_name + "' triggered";
            alert.created_at = std::chrono::system_clock::now();
            alert.updated_at = alert.created_at;
            alert.labels = rule.labels;
            alert.labels["metric"] = rule.metric_name;
            alert.labels["condition"] = rule.condition;
            alert.value = metric_value;
            alert.threshold = rule.threshold;
            
            fire_alert(alert);
        }
    }
}

void AlertManager::send_alert_notifications(const Alert& alert) {
    bool any_success = false;
    
    for (auto& channel : alert_channels_) {
        try {
            if (channel && channel->send_alert(alert)) {
                any_success = true;
            }
        } catch (const std::exception& e) {
            std::cerr << "[AlertManager] Failed to send alert via " 
                      << channel->get_channel_name() << ": " << e.what() << std::endl;
        }
    }
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    if (any_success) {
        stats_.notifications_sent++;
    } else {
        stats_.notification_failures++;
    }
}

std::string AlertManager::generate_alert_id() const {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<uint64_t> dis;
    
    auto now = std::chrono::system_clock::now().time_since_epoch();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now).count();
    
    std::ostringstream oss;
    oss << timestamp << "_" << std::hex << dis(gen);
    return oss.str();
}

void AlertManager::load_alerts() {
    try {
        std::ifstream file(config_.alert_store_path);
        if (!file.is_open()) {
            return; // File doesn't exist yet, which is fine
        }
        
        // Load alerts from JSON file
        nlohmann::json alerts_json;
        file >> alerts_json;
        
        if (alerts_json.contains("alerts") && alerts_json["alerts"].is_array()) {
            for (const auto& alert_json : alerts_json["alerts"]) {
                try {
                    Alert alert;
                    alert.id = alert_json.value("id", "");
                    alert.component_name = alert_json.value("component_name", "");
                    alert.alert_name = alert_json.value("alert_name", "");
                    
                    std::string severity_str = alert_json.value("severity", "INFO");
                    if (severity_str == "INFO") alert.severity = AlertSeverity::INFO;
                    else if (severity_str == "WARNING") alert.severity = AlertSeverity::WARNING;
                    else if (severity_str == "ERROR") alert.severity = AlertSeverity::ERROR;
                    else if (severity_str == "CRITICAL") alert.severity = AlertSeverity::CRITICAL;
                    
                    std::string status_str = alert_json.value("status", "PENDING");
                    if (status_str == "ACTIVE") alert.status = AlertStatus::ACTIVE;
                    else if (status_str == "RESOLVED") alert.status = AlertStatus::RESOLVED;
                    else if (status_str == "SILENCED") alert.status = AlertStatus::SILENCED;
                    else if (status_str == "PENDING") alert.status = AlertStatus::PENDING;
                    
                    alert.message = alert_json.value("message", "");
                    alert.description = alert_json.value("description", "");
                    alert.value = alert_json.value("value", 0.0);
                    alert.threshold = alert_json.value("threshold", 0.0);
                    
                    // Parse labels and annotations
                    if (alert_json.contains("labels") && alert_json["labels"].is_object()) {
                        for (const auto& [key, value] : alert_json["labels"].items()) {
                            alert.labels[key] = value.get<std::string>();
                        }
                    }
                    
                    if (alert_json.contains("annotations") && alert_json["annotations"].is_object()) {
                        for (const auto& [key, value] : alert_json["annotations"].items()) {
                            alert.annotations[key] = value.get<std::string>();
                        }
                    }
                    
                    // Parse timestamps
                    if (alert_json.contains("created_at")) {
                        auto timestamp = alert_json["created_at"].get<int64_t>();
                        alert.created_at = std::chrono::system_clock::from_time_t(timestamp);
                    }
                    
                    if (alert_json.contains("updated_at")) {
                        auto timestamp = alert_json["updated_at"].get<int64_t>();
                        alert.updated_at = std::chrono::system_clock::from_time_t(timestamp);
                    }
                    
                    // Add to alerts map
                    alerts_[alert.id] = alert;
                    
                } catch (const std::exception& e) {
                    std::cerr << "[AlertManager] Failed to parse alert: " << e.what() << std::endl;
                }
            }
            
            std::cout << "[AlertManager] Loaded " << alerts_.size() << " alerts from " 
                      << config_.alert_store_path << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[AlertManager] Failed to load alerts: " << e.what() << std::endl;
    }
}

void AlertManager::save_alerts() const {
    try {
        std::ofstream file(config_.alert_store_path);
        if (!file.is_open()) {
            std::cerr << "[AlertManager] Failed to open alert store for writing" << std::endl;
            return;
        }
        
        // Save alerts to JSON file
        nlohmann::json alerts_json;
        alerts_json["alerts"] = nlohmann::json::array();
        
        for (const auto& [id, alert] : alerts_) {
            nlohmann::json alert_json;
            alert_json["id"] = alert.id;
            alert_json["component_name"] = alert.component_name;
            alert_json["alert_name"] = alert.alert_name;
            alert_json["severity"] = alert_severity_to_string(alert.severity);
            alert_json["status"] = alert_status_to_string(alert.status);
            alert_json["message"] = alert.message;
            alert_json["description"] = alert.description;
            alert_json["value"] = alert.value;
            alert_json["threshold"] = alert.threshold;
            alert_json["labels"] = alert.labels;
            alert_json["annotations"] = alert.annotations;
            alert_json["created_at"] = std::chrono::duration_cast<std::chrono::seconds>(
                alert.created_at.time_since_epoch()).count();
            alert_json["updated_at"] = std::chrono::duration_cast<std::chrono::seconds>(
                alert.updated_at.time_since_epoch()).count();
            
            alerts_json["alerts"].push_back(alert_json);
        }
        
        file << alerts_json.dump(2); // Pretty print with 2-space indentation
        std::cout << "[AlertManager] Saved " << alerts_.size() << " alerts to " 
                  << config_.alert_store_path << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[AlertManager] Failed to save alerts: " << e.what() << std::endl;
    }
}

std::string AlertManager::alert_severity_to_string(AlertSeverity severity) const {
    switch (severity) {
        case AlertSeverity::INFO: return "INFO";
        case AlertSeverity::WARNING: return "WARNING";
        case AlertSeverity::ERROR: return "ERROR";
        case AlertSeverity::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

std::string AlertManager::alert_status_to_string(AlertStatus status) const {
    switch (status) {
        case AlertStatus::ACTIVE: return "ACTIVE";
        case AlertStatus::RESOLVED: return "RESOLVED";
        case AlertStatus::SILENCED: return "SILENCED";
        case AlertStatus::PENDING: return "PENDING";
        default: return "UNKNOWN";
    }
}

} // namespace omop::common