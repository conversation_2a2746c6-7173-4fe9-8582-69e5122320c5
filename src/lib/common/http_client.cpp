#include "http_client.h"
#include "logging.h"
#include <sstream>
#include <regex>
#include <algorithm>
#include <memory>
#include <stdexcept>
#include <sys/socket.h>
#include <netdb.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <cstring>
#ifdef OMOP_HAVE_CURL
#include <curl/curl.h>
#endif // OMOP_HAVE_CURL
#include <thread>
#include <chrono>

namespace omop::common {

// Simple socket wrapper for HTTP requests
class SocketWrapper {
public:
    SocketWrapper() : socket_fd_(-1) {}
    
    ~SocketWrapper() {
        if (socket_fd_ >= 0) {
            close(socket_fd_);
        }
    }
    
    bool connect(const std::string& host, int port) {
        socket_fd_ = socket(AF_INET, SOCK_STREAM, 0);
        if (socket_fd_ < 0) {
            return false;
        }
        
        // Set socket timeout
        struct timeval timeout;
        timeout.tv_sec = 30;
        timeout.tv_usec = 0;
        setsockopt(socket_fd_, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
        setsockopt(socket_fd_, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));
        
        struct hostent* server = gethostbyname(host.c_str());
        if (!server) {
            return false;
        }
        
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(port);
        memcpy(&server_addr.sin_addr.s_addr, server->h_addr, server->h_length);
        
        return ::connect(socket_fd_, reinterpret_cast<struct sockaddr*>(&server_addr), sizeof(server_addr)) >= 0;
    }
    
    bool send_data(const std::string& data) {
        if (socket_fd_ < 0) return false;
        
        size_t total_sent = 0;
        while (total_sent < data.length()) {
            ssize_t sent = send(socket_fd_, data.c_str() + total_sent, data.length() - total_sent, 0);
            if (sent < 0) {
                return false;
            }
            total_sent += sent;
        }
        return true;
    }
    
    std::string receive_data() {
        if (socket_fd_ < 0) return "";
        
        std::string response;
        char buffer[4096];
        
        while (true) {
            ssize_t bytes_received = recv(socket_fd_, buffer, sizeof(buffer) - 1, 0);
            if (bytes_received <= 0) {
                break;
            }
            buffer[bytes_received] = '\0';
            response += buffer;
            
            // Check if we've received the complete response
            if (response.find("\r\n\r\n") != std::string::npos) {
                // For simplicity, we'll assume the response is complete
                // In a real implementation, you'd parse Content-Length header
                break;
            }
        }
        
        return response;
    }
    
private:
    int socket_fd_;
};

omop::common::HttpClient::Response SimpleHttpClient::make_request(const Request& request) {
    auto logger = common::Logger::get("omop-http-client");
    logger->debug("Making HTTP {} request to: {}", 
                 request.method == Method::GET ? "GET" : 
                 request.method == Method::POST ? "POST" : 
                 request.method == Method::PUT ? "PUT" : 
                 request.method == Method::DELETE ? "DELETE" : 
                 request.method == Method::PATCH ? "PATCH" : "UNKNOWN", 
                 request.url);
    
    Response response;
    
    try {
        // Parse URL with enhanced validation
        auto url_parts = parse_url(request.url);
        if (!url_parts) {
            response.success = false;
            response.error_message = "Invalid URL format";
            response.status_code = 0;
            logger->error("Invalid URL format: {}", request.url);
            return response;
        }
        
        std::string host = url_parts->host;
        std::string path = url_parts->path;
        int port = url_parts->port;
        
#ifdef OMOP_HAVE_CURL
        // Check if this is HTTPS - if so, redirect to CurlHttpClient for proper SSL support
        bool is_https = (request.url.substr(0, 5) == "https");
        if (is_https) {
            logger->warn("HTTPS request detected, redirecting to CurlHttpClient for proper SSL support");
            // Create a temporary CurlHttpClient for this request
            try {
                CurlHttpClient curl_client;
                return curl_client.make_request(request);
            } catch (const std::exception& e) {
                response.success = false;
                response.error_message = "HTTPS not supported in SimpleHttpClient: " + std::string(e.what());
                response.status_code = 0;
                logger->error("Failed to create CurlHttpClient for HTTPS request: {}", e.what());
                return response;
            }
        }
#else
        // Check if this is HTTPS - if so, return error since CURL is not available
        bool is_https = (request.url.substr(0, 5) == "https");
        if (is_https) {
            response.success = false;
            response.error_message = "HTTPS not supported - CURL library not available";
            response.status_code = 0;
            logger->error("HTTPS request detected but CURL library not available");
            return response;
        }
#endif // OMOP_HAVE_CURL
        
        // Create socket connection with enhanced error handling
        SocketWrapper socket;
        if (!socket.connect(host, port)) {
            response.success = false;
            response.error_message = "Failed to connect to server";
            response.status_code = 0;
            logger->error("Failed to connect to {}:{}", host, port);
            return response;
        }
        
        // Build HTTP request with enhanced headers
        std::ostringstream request_stream;
        
        // Request line
        std::string method_str;
        switch (request.method) {
            case Method::GET:    method_str = "GET"; break;
            case Method::POST:   method_str = "POST"; break;
            case Method::PUT:    method_str = "PUT"; break;
            case Method::DELETE: method_str = "DELETE"; break;
            case Method::PATCH:  method_str = "PATCH"; break;
        }
        
        request_stream << method_str << " " << path << " HTTP/1.1\r\n";
        
        // Enhanced headers with proper defaults
        request_stream << "Host: " << host << "\r\n";
        request_stream << "User-Agent: OMOP-ETL/1.0\r\n";
        request_stream << "Connection: close\r\n";
        request_stream << "Accept: */*\r\n";
        request_stream << "Accept-Encoding: gzip, deflate\r\n";
        
        // Add default headers
        for (const auto& [key, value] : default_headers_) {
            request_stream << key << ": " << value << "\r\n";
        }
        
        // Add request-specific headers
        for (const auto& [key, value] : request.headers) {
            request_stream << key << ": " << value << "\r\n";
        }
        
        // Content-Length for POST/PUT/PATCH
        if (!request.body.empty()) {
            request_stream << "Content-Length: " << request.body.length() << "\r\n";
        }
        
        // End headers
        request_stream << "\r\n";
        
        // Add body if present
        if (!request.body.empty()) {
            request_stream << request.body;
        }
        
        // Send request with retry logic
        std::string request_data = request_stream.str();
        bool send_success = false;
        int retry_count = 0;
        const int max_retries = 3;
        
        while (!send_success && retry_count < max_retries) {
            if (socket.send_data(request_data)) {
                send_success = true;
            } else {
                retry_count++;
                if (retry_count < max_retries) {
                    logger->warn("Failed to send request, retrying {} of {}", retry_count, max_retries);
                    std::this_thread::sleep_for(std::chrono::milliseconds(100 * retry_count));
                }
            }
        }
        
        if (!send_success) {
            response.success = false;
            response.error_message = "Failed to send request after " + std::to_string(max_retries) + " attempts";
            response.status_code = 0;
            logger->error("Failed to send request data after {} retries", max_retries);
            return response;
        }
        
        // Receive response with timeout handling
        std::string response_data = socket.receive_data();
        if (response_data.empty()) {
            response.success = false;
            response.error_message = "No response received";
            response.status_code = 0;
            logger->error("No response received from server");
            return response;
        }
        
        // Enhanced response parsing with better error handling
        auto header_end = response_data.find("\r\n\r\n");
        if (header_end == std::string::npos) {
            response.success = false;
            response.error_message = "Invalid response format - missing header separator";
            response.status_code = 0;
            logger->error("Invalid response format - missing header separator");
            return response;
        }
        
        std::string headers_part = response_data.substr(0, header_end);
        std::string body_part = response_data.substr(header_end + 4);
        
        // Parse status line with validation
        auto first_line_end = headers_part.find("\r\n");
        if (first_line_end != std::string::npos) {
            std::string status_line = headers_part.substr(0, first_line_end);
            std::istringstream status_stream(status_line);
            std::string http_version;
            status_stream >> http_version >> response.status_code;
            
            // Validate HTTP version
            if (http_version.find("HTTP/") != 0) {
                logger->warn("Unexpected HTTP version: {}", http_version);
            }
        } else {
            response.success = false;
            response.error_message = "Invalid response format - missing status line";
            response.status_code = 0;
            logger->error("Invalid response format - missing status line");
            return response;
        }
        
        // Enhanced header parsing with case-insensitive keys
        std::istringstream headers_stream(headers_part);
        std::string line;
        std::getline(headers_stream, line); // Skip status line
        
        while (std::getline(headers_stream, line)) {
            if (line.empty() || line == "\r") break;
            
            auto colon_pos = line.find(':');
            if (colon_pos != std::string::npos) {
                std::string key = line.substr(0, colon_pos);
                std::string value = line.substr(colon_pos + 1);
                
                // Trim whitespace and normalize
                key.erase(0, key.find_first_not_of(" \t"));
                key.erase(key.find_last_not_of(" \t\r\n") + 1);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t\r\n") + 1);
                
                // Convert key to lowercase for consistency
                std::transform(key.begin(), key.end(), key.begin(), ::tolower);
                
                response.headers[key] = value;
            }
        }
        
        response.body = body_part;
        response.success = (response.status_code >= 200 && response.status_code < 300);
        
        if (!response.success) {
            response.error_message = "HTTP " + std::to_string(response.status_code);
            // Add more descriptive error messages for common status codes
            switch (response.status_code) {
                case 400: response.error_message += " Bad Request"; break;
                case 401: response.error_message += " Unauthorized"; break;
                case 403: response.error_message += " Forbidden"; break;
                case 404: response.error_message += " Not Found"; break;
                case 500: response.error_message += " Internal Server Error"; break;
                case 502: response.error_message += " Bad Gateway"; break;
                case 503: response.error_message += " Service Unavailable"; break;
            }
        }
        
        logger->debug("HTTP request completed with status: {} ({} bytes)", response.status_code, response.body.length());
        
    } catch (const std::exception& e) {
        logger->error("HTTP request failed with exception: {}", e.what());
        response.success = false;
        response.error_message = e.what();
        response.status_code = 0;
    }
    
    return response;
}

std::optional<SimpleHttpClient::UrlParts> SimpleHttpClient::parse_url(const std::string& url) {
    // Enhanced URL parsing - extract host, path, and port
    std::regex url_regex(R"(https?://([^:/]+)(?::(\d+))?(/.*)?)");
    std::smatch match;
    
    if (std::regex_match(url, match, url_regex)) {
        std::string host = match[1].str();
        std::string port_str = match[2].str();
        std::string path = match[3].str();
        
        // Set default port based on protocol
        int port = 80; // Default HTTP port
        if (url.substr(0, 5) == "https") {
            port = 443; // Default HTTPS port
        }
        
        // Override with explicit port if specified
        if (!port_str.empty()) {
            try {
                port = std::stoi(port_str);
            } catch (const std::exception& e) {
                // Invalid port number, use default
            }
        }
        
        if (path.empty()) {
            path = "/";
        }
        
        return std::optional<SimpleHttpClient::UrlParts>{SimpleHttpClient::UrlParts{host, path, port}};
    }
    
    return std::nullopt;
}

std::unique_ptr<HttpClient> HttpClientFactory::create_client() {
#ifdef OMOP_HAVE_CURL
    // Default to CurlHttpClient for full HTTPS support
    try {
        auto client = std::make_unique<CurlHttpClient>();
        auto logger = common::Logger::get("omop-http-client");
        logger->info("Created CurlHttpClient with full SSL/TLS support");
        return client;
    } catch (const std::exception& e) {
        // Fallback to SimpleHttpClient if curl is not available
        auto logger = common::Logger::get("omop-http-client");
        logger->warn("CurlHttpClient creation failed: {}, falling back to SimpleHttpClient", e.what());
        logger->warn("SimpleHttpClient has limited functionality - HTTPS requests will be redirected to CurlHttpClient");
        return std::make_unique<SimpleHttpClient>();
    }
#else
    // CURL not available, use SimpleHttpClient
    auto logger = common::Logger::get("omop-http-client");
    logger->info("CURL not available, using SimpleHttpClient");
    return std::make_unique<SimpleHttpClient>();
#endif // OMOP_HAVE_CURL
}

std::unique_ptr<HttpClient> HttpClientFactory::create_client(
    const std::unordered_map<std::string, std::string>& config) {
    
    auto logger = common::Logger::get("omop-http-client");
    
    // Determine client type based on configuration
    std::string client_type = "auto";
    if (config.find("client_type") != config.end()) {
        client_type = config.at("client_type");
    }
    
    std::unique_ptr<HttpClient> client;
    
#ifdef OMOP_HAVE_CURL
    if (client_type == "curl" || client_type == "auto") {
        try {
            client = std::make_unique<CurlHttpClient>();
            logger->info("Created CurlHttpClient based on configuration");
        } catch (const std::exception& e) {
            logger->warn("CurlHttpClient creation failed: {}, falling back to SimpleHttpClient", e.what());
            client = std::make_unique<SimpleHttpClient>();
        }
    } else
#endif // OMOP_HAVE_CURL
    if (client_type == "simple") {
        client = std::make_unique<SimpleHttpClient>();
        logger->info("Created SimpleHttpClient based on configuration");
    } else {
        logger->warn("Unknown client type: {}, using auto selection", client_type);
        return create_client(); // Use default logic
    }
    
    // Apply configuration
    if (config.find("timeout") != config.end()) {
        try {
            int timeout = std::stoi(config.at("timeout"));
            client->set_timeout(timeout);
            logger->debug("Set timeout to {} seconds", timeout);
        } catch (const std::exception& e) {
            logger->warn("Invalid timeout value: {}", config.at("timeout"));
        }
    }
    
    // Set default headers
    std::unordered_map<std::string, std::string> headers;
    for (const auto& [key, value] : config) {
        if (key.find("header_") == 0) {
            std::string header_name = key.substr(7); // Remove "header_" prefix
            headers[header_name] = value;
        }
    }
    
    if (!headers.empty()) {
        client->set_default_headers(headers);
        logger->debug("Set {} default headers", headers.size());
    }
    
#ifdef OMOP_HAVE_CURL
    // Apply SSL configuration for CurlHttpClient
    if (auto curl_client = dynamic_cast<CurlHttpClient*>(client.get())) {
        if (config.find("ssl_verify_peer") != config.end()) {
            bool verify_peer = (config.at("ssl_verify_peer") == "true");
            curl_client->set_ssl_verification(verify_peer, true);
            logger->debug("Set SSL peer verification to: {}", verify_peer);
        }
        if (config.find("ssl_verify_host") != config.end()) {
            bool verify_host = (config.at("ssl_verify_host") == "true");
            curl_client->set_ssl_verification(true, verify_host);
            logger->debug("Set SSL host verification to: {}", verify_host);
        }
    }
#endif // OMOP_HAVE_CURL
    
    return client;
}

std::unique_ptr<HttpClient> HttpClientFactory::create_thread_safe_client() {
#ifdef OMOP_HAVE_CURL
    return std::make_unique<CurlHttpClient>();
#else
    return std::make_unique<SimpleHttpClient>();
#endif // OMOP_HAVE_CURL
}

#ifdef OMOP_HAVE_CURL
// CurlHttpClient implementation
CurlHttpClient::CurlHttpClient() : curl_handle_(nullptr), verify_peer_(true), verify_host_(true) {
    initialize_curl();
    set_default_options();
}

CurlHttpClient::~CurlHttpClient() {
    if (curl_handle_) {
        curl_easy_cleanup(curl_handle_);
    }
}

void CurlHttpClient::initialize_curl() {
    // Initialize libcurl globally (thread-safe)
    static std::once_flag init_flag;
    std::call_once(init_flag, []() {
        curl_global_init(CURL_GLOBAL_ALL);
    });
    
    curl_handle_ = curl_easy_init();
    if (!curl_handle_) {
        throw std::runtime_error("Failed to initialize CURL handle");
    }
}

void CurlHttpClient::set_default_options() {
    // Set default options
    curl_easy_setopt(curl_handle_, CURLOPT_FOLLOWLOCATION, 1L);
    curl_easy_setopt(curl_handle_, CURLOPT_MAXREDIRS, 10L);
    curl_easy_setopt(curl_handle_, CURLOPT_USERAGENT, "OMOP-ETL/1.0");
    curl_easy_setopt(curl_handle_, CURLOPT_SSL_VERIFYPEER, 1L);
    curl_easy_setopt(curl_handle_, CURLOPT_SSL_VERIFYHOST, 2L);
}

size_t CurlHttpClient::write_callback(void* contents, size_t size, size_t nmemb, void* userp) {
    auto* str = reinterpret_cast<std::string*>(userp);
    str->append(reinterpret_cast<char*>(contents), size * nmemb);
    return size * nmemb;
}

size_t CurlHttpClient::header_callback(void* contents, size_t size, size_t nmemb, void* userp) {
    auto* headers = reinterpret_cast<std::unordered_map<std::string, std::string>*>(userp);
    std::string header(reinterpret_cast<char*>(contents), size * nmemb);
    
    // Parse header line
    auto colon_pos = header.find(':');
    if (colon_pos != std::string::npos) {
        std::string key = header.substr(0, colon_pos);
        std::string value = header.substr(colon_pos + 1);
        
        // Trim whitespace
        key.erase(0, key.find_first_not_of(" \t"));
        key.erase(key.find_last_not_of(" \t\r\n") + 1);
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t\r\n") + 1);
        
        (*headers)[key] = value;
    }
    
    return size * nmemb;
}

bool CurlHttpClient::is_retryable_error(CURLcode curl_code) {
    switch (curl_code) {
        case CURLE_COULDNT_CONNECT:
        case CURLE_COULDNT_RESOLVE_HOST:
        case CURLE_OPERATION_TIMEDOUT:
        case CURLE_PARTIAL_FILE:
        case CURLE_SEND_ERROR:
        case CURLE_RECV_ERROR:
            return true;
        default:
            return false;
    }
}

std::string CurlHttpClient::get_http_error_message(int status_code) {
    switch (status_code) {
        case 400: return "Bad Request";
        case 401: return "Unauthorized";
        case 403: return "Forbidden";
        case 404: return "Not Found";
        case 500: return "Internal Server Error";
        case 502: return "Bad Gateway";
        case 503: return "Service Unavailable";
        default: return "HTTP " + std::to_string(status_code);
    }
}

HttpClient::Response CurlHttpClient::perform_request(const Request& request) {
    HttpClient::Response response;
    
    // Set URL
    curl_easy_setopt(curl_handle_, CURLOPT_URL, request.url.c_str());
    
    // Set HTTP method
    switch (request.method) {
        case HttpClient::Method::GET:
            curl_easy_setopt(curl_handle_, CURLOPT_HTTPGET, 1L);
            break;
        case HttpClient::Method::POST:
            curl_easy_setopt(curl_handle_, CURLOPT_POST, 1L);
            break;
        case HttpClient::Method::PUT:
            curl_easy_setopt(curl_handle_, CURLOPT_CUSTOMREQUEST, "PUT");
            break;
        case HttpClient::Method::DELETE:
            curl_easy_setopt(curl_handle_, CURLOPT_CUSTOMREQUEST, "DELETE");
            break;
        case HttpClient::Method::PATCH:
            curl_easy_setopt(curl_handle_, CURLOPT_CUSTOMREQUEST, "PATCH");
            break;
    }
    
    // Set request body
    if (!request.body.empty()) {
        curl_easy_setopt(curl_handle_, CURLOPT_POSTFIELDS, request.body.c_str());
        curl_easy_setopt(curl_handle_, CURLOPT_POSTFIELDSIZE, request.body.length());
    }
    
    // Set headers with enhanced defaults
    struct curl_slist* headers = nullptr;
    
    // Add default headers if not already present
    std::unordered_map<std::string, std::string> all_headers = request.headers;
    if (all_headers.find("Accept") == all_headers.end()) {
        all_headers["Accept"] = "*/*";
    }
    if (all_headers.find("User-Agent") == all_headers.end()) {
        all_headers["User-Agent"] = "OMOP-ETL/1.0";
    }
    
    for (const auto& [key, value] : all_headers) {
        std::string header = key + ": " + value;
        headers = curl_slist_append(headers, header.c_str());
    }
    if (headers) {
        curl_easy_setopt(curl_handle_, CURLOPT_HTTPHEADER, headers);
    }
    
    // Set timeout
    curl_easy_setopt(curl_handle_, CURLOPT_TIMEOUT, request.timeout_seconds);
    
    // Set callbacks
    std::string response_body;
    curl_easy_setopt(curl_handle_, CURLOPT_WRITEFUNCTION, write_callback);
    curl_easy_setopt(curl_handle_, CURLOPT_WRITEDATA, &response_body);
    curl_easy_setopt(curl_handle_, CURLOPT_HEADERFUNCTION, header_callback);
    curl_easy_setopt(curl_handle_, CURLOPT_HEADERDATA, &response.headers);
    
    // Enhanced SSL/TLS options
    curl_easy_setopt(curl_handle_, CURLOPT_SSL_VERIFYPEER, verify_peer_ ? 1L : 0L);
    curl_easy_setopt(curl_handle_, CURLOPT_SSL_VERIFYHOST, verify_host_ ? 2L : 0L);
    
    // Set connection timeout
    curl_easy_setopt(curl_handle_, CURLOPT_CONNECTTIMEOUT, 10L);
    
    // Enable compression
    curl_easy_setopt(curl_handle_, CURLOPT_ACCEPT_ENCODING, "gzip, deflate");
    
    // Perform request with retry logic
    CURLcode res = CURLE_OK;
    int retry_count = 0;
    const int max_retries = 3;
    
    while (retry_count < max_retries) {
        res = curl_easy_perform(curl_handle_);
        
        if (res == CURLE_OK) {
            break; // Success
        }
        
        // Check if retryable error
        if (is_retryable_error(res) && retry_count < max_retries - 1) {
            retry_count++;
            // Exponential backoff
            std::this_thread::sleep_for(std::chrono::milliseconds(100 * (1 << retry_count)));
            continue;
        }
        
        break;
    }
    
    // Clean up headers
    if (headers) {
        curl_slist_free_all(headers);
    }
    
    if (res != CURLE_OK) {
        response.success = false;
        response.error_message = curl_easy_strerror(res);
        if (retry_count > 0) {
            response.error_message += " (after " + std::to_string(retry_count) + " retries)";
        }
        response.status_code = 0;
    } else {
        long http_code = 0;
        curl_easy_getinfo(curl_handle_, CURLINFO_RESPONSE_CODE, &http_code);
        response.status_code = static_cast<int>(http_code);
        response.body = response_body;
        response.success = (http_code >= 200 && http_code < 300);
        
        // Enhanced error messages for HTTP status codes
        if (!response.success) {
            response.error_message = get_http_error_message(http_code);
        }
    }
    
    return response;
}

HttpClient::Response CurlHttpClient::make_request(const Request& request) {
    auto logger = common::Logger::get("omop-http-client");
    logger->debug("Making HTTP {} request to: {}", 
                 request.method == Method::GET ? "GET" : 
                 request.method == Method::POST ? "POST" : 
                 request.method == Method::PUT ? "PUT" : 
                 request.method == Method::DELETE ? "DELETE" : 
                 request.method == Method::PATCH ? "PATCH" : "UNKNOWN", 
                 request.url);
    
    return perform_request(request);
}

void CurlHttpClient::set_ssl_verification(bool verify_peer, bool verify_host) {
    verify_peer_ = verify_peer;
    verify_host_ = verify_host;
    curl_easy_setopt(curl_handle_, CURLOPT_SSL_VERIFYPEER, verify_peer ? 1L : 0L);
    curl_easy_setopt(curl_handle_, CURLOPT_SSL_VERIFYHOST, verify_host ? 2L : 0L);
}

#endif // OMOP_HAVE_CURL

} // namespace omop::common 