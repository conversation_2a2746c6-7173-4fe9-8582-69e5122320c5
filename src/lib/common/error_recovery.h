/**
 * @file error_recovery.h
 * @brief Enhanced error recovery mechanisms with retry and fallback strategies
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include "exceptions.h"
#include <string>
#include <memory>
#include <functional>
#include <chrono>
#include <vector>
#include <atomic>
#include <mutex>
#include <optional>
#include <variant>
#include <any>
#include <future>
#include <thread>
#include <cmath>
#include <unordered_map>
#include <condition_variable>

namespace omop::common {

/**
 * @brief Error recovery strategy types
 */
enum class RecoveryStrategy {
    Retry,              // Retry the operation
    Fallback,           // Use fallback method
    Skip,               // Skip the operation
    Circuit,            // Circuit breaker pattern
    Compensate,         // Compensating transaction
    Escalate            // Escalate to manual intervention
};

/**
 * @brief Error classification for recovery decisions
 */
enum class ErrorType {
    Transient,          // Temporary error (network, lock timeout)
    Persistent,         // Permanent error (invalid data, missing resource)
    Resource,           // Resource exhaustion (memory, disk space)
    Configuration,      // Configuration error
    Security,           // Security/permission error
    Unknown             // Unclassified error
};

/**
 * @brief Recovery attempt result
 */
struct RecoveryAttempt {
    size_t attempt_number = 0;
    RecoveryStrategy strategy;
    std::chrono::system_clock::time_point timestamp;
    std::chrono::milliseconds duration{0};
    bool success = false;
    std::string error_message;
    std::any result_data;
};

/**
 * @brief Recovery context with operation details
 */
struct RecoveryContext {
    std::string operation_id;
    std::string component_name;
    std::any operation_data;
    std::unordered_map<std::string, std::string> metadata;
    std::vector<RecoveryAttempt> attempts;
    std::chrono::system_clock::time_point start_time;
    
    size_t total_attempts() const { return attempts.size(); }
    std::chrono::milliseconds total_duration() const {
        if (attempts.empty()) return std::chrono::milliseconds{0};
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            attempts.back().timestamp - start_time);
    }
};

/**
 * @brief Retry policy configuration
 */
struct RetryPolicy {
    size_t max_attempts = 3;
    std::chrono::milliseconds base_delay{1000};
    std::chrono::milliseconds max_delay{30000};
    double backoff_multiplier = 2.0;
    double jitter_factor = 0.1;  // Add randomness to prevent thundering herd
    
    // Condition for retry
    std::function<bool(const std::exception&, size_t)> should_retry = 
        [](const std::exception&, size_t attempt) { return attempt < 3; };
    
    // Calculate delay for attempt
    std::chrono::milliseconds calculate_delay(size_t attempt) const {
        auto delay = base_delay * std::pow(backoff_multiplier, attempt - 1);
        
        // Add jitter
        if (jitter_factor > 0.0) {
            auto jitter = delay * jitter_factor * (2.0 * (rand() / double(RAND_MAX)) - 1.0);
            delay += std::chrono::duration_cast<std::chrono::milliseconds>(jitter);
        }
        
        return std::min(std::chrono::duration_cast<std::chrono::milliseconds>(delay), max_delay);
    }
};

/**
 * @brief Circuit breaker for preventing cascade failures
 */
class CircuitBreaker {
public:
    enum class State {
        Closed,     // Normal operation
        Open,       // Failing, reject requests
        HalfOpen    // Testing if service recovered
    };

    struct Config {
        size_t failure_threshold = 5;              // Failures to open circuit
        std::chrono::seconds timeout{60};          // Time before trying again
        size_t success_threshold = 3;              // Successes to close circuit
        std::chrono::seconds reset_timeout{300};   // Full reset time
    };

    CircuitBreaker() : config_{} {
        reset_time_ = std::chrono::steady_clock::now();
    }
    
    explicit CircuitBreaker(const Config& config) : config_(config) {
        reset_time_ = std::chrono::steady_clock::now();
    }

    /**
     * @brief Check if operation should be allowed
     * @return true if operation should proceed
     */
    bool allow_request() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        switch (state_) {
            case State::Closed:
                return true;
                
            case State::Open: {
                auto now = std::chrono::steady_clock::now();
                if (now - last_failure_time_ >= config_.timeout) {
                    state_ = State::HalfOpen;
                    consecutive_successes_ = 0;
                    return true;
                }
                return false;
            }
            
            case State::HalfOpen:
                return true;
        }
        return false;
    }

    /**
     * @brief Record successful operation
     */
    void record_success() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        failure_count_ = 0;
        consecutive_successes_++;
        
        if (state_ == State::HalfOpen && 
            consecutive_successes_ >= config_.success_threshold) {
            state_ = State::Closed;
            reset_time_ = std::chrono::steady_clock::now();
        }
    }

    /**
     * @brief Record failed operation
     */
    void record_failure() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        failure_count_++;
        consecutive_successes_ = 0;
        last_failure_time_ = std::chrono::steady_clock::now();
        
        if (state_ == State::Closed && failure_count_ >= config_.failure_threshold) {
            state_ = State::Open;
        } else if (state_ == State::HalfOpen) {
            state_ = State::Open;
        }
    }

    State get_state() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return state_;
    }

private:
    Config config_;
    mutable std::mutex mutex_;
    State state_ = State::Closed;
    size_t failure_count_ = 0;
    size_t consecutive_successes_ = 0;
    std::chrono::steady_clock::time_point last_failure_time_;
    std::chrono::steady_clock::time_point reset_time_;
};

/**
 * @brief Advanced error recovery engine
 * 
 * Features:
 * - Multiple recovery strategies
 * - Automatic error classification
 * - Circuit breaker pattern
 * - Compensation patterns
 * - Recovery statistics and monitoring
 */
class ErrorRecoveryEngine {
public:
    /**
     * @brief Recovery configuration
     */
    struct Config {
        bool enable_retry = true;
        bool enable_fallback = true;
        bool enable_circuit_breaker = true;
        bool enable_compensation = false;
        size_t max_concurrent_recoveries = 10;
        std::chrono::seconds recovery_timeout{300};
        bool collect_statistics = true;
    };

    /**
     * @brief Constructor
     * @param config Recovery configuration
     */
    ErrorRecoveryEngine();
    explicit ErrorRecoveryEngine(const Config& config);

    /**
     * @brief Destructor
     */
    ~ErrorRecoveryEngine();

    /**
     * @brief Execute operation with recovery
     * @tparam T Result type
     * @param operation_id Unique operation identifier
     * @param operation Operation to execute
     * @param retry_policy Retry policy
     * @param fallback_operation Optional fallback operation
     * @return Operation result or error
     */
    template<typename T>
    std::variant<T, std::exception_ptr> execute_with_recovery(
        const std::string& operation_id,
        std::function<T()> operation,
        const RetryPolicy& retry_policy = {},
        std::optional<std::function<T()>> fallback_operation = std::nullopt);

    /**
     * @brief Register fallback strategy for operation type
     * @param operation_type Operation type identifier
     * @param fallback_factory Fallback operation factory
     */
    void register_fallback(
        const std::string& operation_type,
        std::function<std::function<std::any()>(const std::any&)> fallback_factory);

    /**
     * @brief Register compensation strategy for operation type
     * @param operation_type Operation type identifier
     * @param compensation_factory Compensation operation factory
     */
    void register_compensation(
        const std::string& operation_type,
        std::function<std::function<void()>(const std::any&)> compensation_factory);

    /**
     * @brief Get circuit breaker for service
     * @param service_name Service name
     * @return Circuit breaker instance
     */
    std::shared_ptr<CircuitBreaker> get_circuit_breaker(const std::string& service_name);

    /**
     * @brief Classify error type for recovery decisions
     * @param error Exception to classify
     * @param context Operation context
     * @return Error type classification
     */
    ErrorType classify_error(const std::exception& error, const RecoveryContext& context) const;

    /**
     * @brief Get recovery statistics  
     */
    struct Statistics {
        size_t total_operations = 0;
        size_t successful_operations = 0;
        size_t failed_operations = 0;
        size_t recovered_operations = 0;
        size_t retry_attempts = 0;
        size_t fallback_used = 0;
        size_t circuit_breaks = 0;
        size_t compensations = 0;
        
        double success_rate() const {
            return total_operations > 0 ? static_cast<double>(successful_operations) / total_operations : 0.0;
        }
        
        double recovery_rate() const {
            return failed_operations > 0 ? static_cast<double>(recovered_operations) / failed_operations : 0.0;
        }
    };

    Statistics get_statistics() const {
        Statistics stats;
        stats.total_operations = internal_stats_.total_operations.load();
        stats.successful_operations = internal_stats_.successful_operations.load();
        stats.failed_operations = internal_stats_.failed_operations.load();
        stats.recovered_operations = internal_stats_.recovered_operations.load();
        stats.retry_attempts = internal_stats_.retry_attempts.load();
        stats.fallback_used = internal_stats_.fallback_used.load();
        stats.circuit_breaks = internal_stats_.circuit_breaks.load();
        stats.compensations = internal_stats_.compensations.load();
        return stats;
    }

    /**
     * @brief Reset statistics
     */
    void reset_statistics();

    /**
     * @brief Set error classifier function
     * @param classifier Custom error classifier
     */
    void set_error_classifier(
        std::function<ErrorType(const std::exception&, const RecoveryContext&)> classifier) {
        error_classifier_ = std::move(classifier);
    }

private:
    /**
     * @brief Internal atomic statistics for thread-safe updates
     */
    struct InternalStatistics {
        std::atomic<size_t> total_operations{0};
        std::atomic<size_t> successful_operations{0};
        std::atomic<size_t> failed_operations{0};
        std::atomic<size_t> recovered_operations{0};
        std::atomic<size_t> retry_attempts{0};
        std::atomic<size_t> fallback_used{0};
        std::atomic<size_t> circuit_breaks{0};
        std::atomic<size_t> compensations{0};
    };
    /**
     * @brief Execute retry strategy
     * @tparam T Result type
     * @param operation Operation to retry
     * @param policy Retry policy
     * @param context Recovery context
     * @return Result or exception
     */
    template<typename T>
    std::variant<T, std::exception_ptr> execute_retry(
        std::function<T()> operation,
        const RetryPolicy& policy,
        RecoveryContext& context);

    /**
     * @brief Execute fallback strategy
     * @tparam T Result type
     * @param fallback_operation Fallback operation
     * @param context Recovery context
     * @return Result or exception
     */
    template<typename T>
    std::variant<T, std::exception_ptr> execute_fallback(
        std::function<T()> fallback_operation,
        RecoveryContext& context);

    /**
     * @brief Execute compensation for failed operation
     * @param operation_type Operation type
     * @param operation_data Original operation data
     * @param context Recovery context
     */
    void execute_compensation(
        const std::string& operation_type,
        const std::any& operation_data,
        RecoveryContext& context);

    Config config_;
    mutable InternalStatistics internal_stats_;
    
    // Circuit breakers by service name
    std::unordered_map<std::string, std::shared_ptr<CircuitBreaker>> circuit_breakers_;
    std::mutex circuit_breakers_mutex_;
    
    // Recovery strategies
    std::unordered_map<std::string, std::function<std::function<std::any()>(const std::any&)>> 
        fallback_strategies_;
    std::unordered_map<std::string, std::function<std::function<void()>(const std::any&)>>
        compensation_strategies_;
    std::mutex strategies_mutex_;
    
    // Active recovery contexts
    std::unordered_map<std::string, std::shared_ptr<RecoveryContext>> active_recoveries_;
    std::mutex recoveries_mutex_;
    
    // Error classifier
    std::function<ErrorType(const std::exception&, const RecoveryContext&)> error_classifier_;
};

/**
 * @brief Bulkhead pattern for resource isolation
 */
class ResourceBulkhead {
public:
    struct Config {
        size_t max_concurrent_operations = 10;
        std::chrono::seconds queue_timeout{30};
        bool enable_priority_queue = false;
    };

    ResourceBulkhead() : config_{} {}
    explicit ResourceBulkhead(const Config& config) : config_(config) {}

    /**
     * @brief Execute operation with resource isolation
     * @tparam T Result type
     * @param operation Operation to execute
     * @param priority Operation priority (if priority queue enabled)
     * @return Operation result
     */
    template<typename T>
    T execute(std::function<T()> operation, int priority = 0);

    /**
     * @brief Get current resource usage
     * @return Current concurrent operations count
     */
    size_t get_current_load() const {
        return current_operations_.load();
    }

private:
    Config config_;
    std::atomic<size_t> current_operations_{0};
    std::condition_variable resource_available_;
    std::mutex resource_mutex_;
};

/**
 * @brief Timeout decorator for operations
 */
template<typename T>
class TimeoutDecorator {
public:
    explicit TimeoutDecorator(std::chrono::milliseconds timeout)
        : timeout_(timeout) {}

    std::variant<T, std::exception_ptr> execute(std::function<T()> operation) {
        std::promise<std::variant<T, std::exception_ptr>> promise;
        auto future = promise.get_future();
        
        std::thread worker([&promise, operation = std::move(operation)]() {
            try {
                promise.set_value(operation());
            } catch (...) {
                promise.set_value(std::current_exception());
            }
        });
        
        if (future.wait_for(timeout_) == std::future_status::timeout) {
            worker.detach();  // Let it finish in background
            return std::make_exception_ptr(
                std::runtime_error("Operation timed out"));
        }
        
        worker.join();
        return future.get();
    }

private:
    std::chrono::milliseconds timeout_;
};

} // namespace omop::common