/**
 * @file async_logger.cpp
 * @brief Implementation of asynchronous logging system
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "async_logger.h"
#include <algorithm>
#include <thread>
#include <shared_mutex>

namespace omop::common {

// Static member definitions
std::unordered_map<std::string, std::shared_ptr<AsyncLogger>> AsyncLoggerFactory::loggers_;
std::mutex AsyncLoggerFactory::loggers_mutex_;

AsyncLogger::AsyncLogger(const std::string& name)
    : Logger(name), config_() {
    
    // Validate configuration
    if ((config_.queue_size & (config_.queue_size - 1)) != 0) {
        throw std::invalid_argument("Queue size must be power of 2");
    }
    
    queue_ = std::make_unique<LockFreeQueue<AsyncLogMessage, 65536>>();
    stats_.start_time = std::chrono::steady_clock::now();
}

AsyncLogger::AsyncLogger(const std::string& name, const Config& config)
    : Lo<PERSON>(name), config_(config) {
    
    // Validate configuration
    if ((config_.queue_size & (config_.queue_size - 1)) != 0) {
        throw std::invalid_argument("Queue size must be power of 2");
    }
    
    queue_ = std::make_unique<LockFreeQueue<AsyncLogMessage, 65536>>();
    stats_.start_time = std::chrono::steady_clock::now();
}

AsyncLogger::~AsyncLogger() {
    stop(true);
}

void AsyncLogger::start() {
    if (running_.load()) return;
    
    running_ = true;
    workers_.reserve(config_.worker_threads);
    
    for (size_t i = 0; i < config_.worker_threads; ++i) {
        workers_.emplace_back(&AsyncLogger::worker_thread, this);
    }
}

void AsyncLogger::stop(bool wait_for_flush) {
    if (!running_.load()) return;
    
    if (wait_for_flush) {
        flush(std::chrono::milliseconds(5000));
    }
    
    running_ = false;
    
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    workers_.clear();
}

bool AsyncLogger::flush(std::chrono::milliseconds timeout) {
    if (!running_.load()) return true;
    
    flush_requested_ = true;
    
    auto start_time = std::chrono::steady_clock::now();
    std::unique_lock<std::mutex> lock(flush_mutex_);
    
    return flush_cv_.wait_for(lock, timeout, [this] {
        return queue_->empty() || !running_.load();
    });
}

void AsyncLogger::write_entry(const LogEntry& entry) {
    AsyncLogMessage msg;
    msg.entry = entry;
    msg.enqueue_time = std::chrono::steady_clock::now();
    
    stats_.messages_queued.fetch_add(1, std::memory_order_relaxed);
    
    if (!queue_->try_enqueue(std::move(msg))) {
        // Handle overflow
        stats_.messages_dropped.fetch_add(1, std::memory_order_relaxed);
        
        if (config_.enable_overflow_policy) {
            handle_overflow(entry);
        }
    }
}

void AsyncLogger::worker_thread() {
    std::vector<AsyncLogMessage> batch;
    batch.reserve(config_.batch_size);
    
    auto last_flush = std::chrono::steady_clock::now();
    
    while (running_.load() || !queue_->empty()) {
        batch.clear();
        
        // Collect batch of messages
        AsyncLogMessage msg;
        size_t collected = 0;
        
        // Try to collect a full batch or timeout
        auto batch_start = std::chrono::steady_clock::now();
        while (collected < config_.batch_size && 
               (std::chrono::steady_clock::now() - batch_start) < config_.flush_interval) {
            
            if (queue_->try_dequeue(msg)) {
                batch.push_back(std::move(msg));
                ++collected;
            } else {
                // No messages available, short sleep to avoid busy waiting
                std::this_thread::sleep_for(std::chrono::microseconds(100));
            }
        }
        
        if (!batch.empty()) {
            process_batch(batch);
        }
        
        // Check if we need to flush
        auto now = std::chrono::steady_clock::now();
        if (flush_requested_.load() || 
            (now - last_flush) >= config_.flush_interval) {
            
            // Flush all sinks
            std::shared_lock<std::shared_mutex> lock(sinks_mutex_);
            for (auto& sink : sinks_) {
                sink->flush();
            }
            
            last_flush = now;
            stats_.flush_count.fetch_add(1, std::memory_order_relaxed);
            
            if (flush_requested_.load()) {
                flush_requested_ = false;
                flush_cv_.notify_all();
            }
        }
    }
}

void AsyncLogger::process_batch(std::vector<AsyncLogMessage>& messages) {
    auto process_start = std::chrono::steady_clock::now();
    
    std::shared_lock<std::shared_mutex> lock(sinks_mutex_);
    
    for (const auto& msg : messages) {
        // Calculate latency
        auto latency = std::chrono::duration_cast<std::chrono::nanoseconds>(
            process_start - msg.enqueue_time).count();
        stats_.total_latency_ns.fetch_add(latency, std::memory_order_relaxed);
        
        // Write to all sinks
        for (auto& sink : sinks_) {
            sink->write(msg.entry);
        }
        
        stats_.messages_processed.fetch_add(1, std::memory_order_relaxed);
    }
}

void AsyncLogger::handle_overflow(const LogEntry& entry) {
    // For critical messages, try to write directly (blocking)
    if (entry.level >= LogLevel::Error) {
        std::shared_lock<std::shared_mutex> lock(sinks_mutex_);
        for (auto& sink : sinks_) {
            sink->write(entry);
        }
    }
}

// AsyncLoggerFactory implementation

std::shared_ptr<AsyncLogger> AsyncLoggerFactory::create(const std::string& name) {
    std::lock_guard<std::mutex> lock(loggers_mutex_);
    
    auto it = loggers_.find(name);
    if (it != loggers_.end()) {
        return it->second;
    }
    
    auto logger = std::make_shared<AsyncLogger>(name);
    loggers_[name] = logger;
    return logger;
}

std::shared_ptr<AsyncLogger> AsyncLoggerFactory::create(
    const std::string& name, 
    const AsyncLogger::Config& config) {
    
    std::lock_guard<std::mutex> lock(loggers_mutex_);
    
    auto it = loggers_.find(name);
    if (it != loggers_.end()) {
        return it->second;
    }
    
    auto logger = std::make_shared<AsyncLogger>(name, config);
    logger->start();
    loggers_[name] = logger;
    
    return logger;
}

std::shared_ptr<AsyncLogger> AsyncLoggerFactory::get(const std::string& name) {
    std::lock_guard<std::mutex> lock(loggers_mutex_);
    
    auto it = loggers_.find(name);
    return (it != loggers_.end()) ? it->second : nullptr;
}

void AsyncLoggerFactory::shutdown_all(bool wait_for_flush) {
    std::lock_guard<std::mutex> lock(loggers_mutex_);
    
    for (auto& [name, logger] : loggers_) {
        logger->stop(wait_for_flush);
    }
    
    loggers_.clear();
}

std::unordered_map<std::string, AsyncLogger::Statistics> 
AsyncLoggerFactory::get_all_statistics() {
    std::lock_guard<std::mutex> lock(loggers_mutex_);
    
    std::unordered_map<std::string, AsyncLogger::Statistics> stats;
    for (const auto& [name, logger] : loggers_) {
        stats[name] = logger->get_statistics();
    }
    
    return stats;
}

// AsyncLogSink implementation

AsyncLogSink::AsyncLogSink(std::shared_ptr<ILogSink> target_sink, size_t buffer_size)
    : target_sink_(target_sink), buffer_size_(buffer_size) {
    
    worker_ = std::thread(&AsyncLogSink::worker_thread, this);
}

AsyncLogSink::~AsyncLogSink() {
    flush();
    running_ = false;
    buffer_cv_.notify_all();
    
    if (worker_.joinable()) {
        worker_.join();
    }
}

void AsyncLogSink::write(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    buffer_.push(entry);
    
    if (buffer_.size() >= buffer_size_) {
        buffer_cv_.notify_one();
    }
}

void AsyncLogSink::flush() {
    flush_requested_ = true;
    buffer_cv_.notify_all();
    
    // Wait for buffer to be processed
    std::unique_lock<std::mutex> lock(buffer_mutex_);
    buffer_cv_.wait(lock, [this] { return buffer_.empty() || !running_; });
    
    flush_requested_ = false;
}

void AsyncLogSink::worker_thread() {
    while (running_) {
        std::unique_lock<std::mutex> lock(buffer_mutex_);
        
        buffer_cv_.wait(lock, [this] {
            return !buffer_.empty() || flush_requested_ || !running_;
        });
        
        if (!buffer_.empty()) {
            process_buffer();
        }
    }
    
    // Process remaining entries
    if (!buffer_.empty()) {
        process_buffer();
    }
}

void AsyncLogSink::process_buffer() {
    while (!buffer_.empty()) {
        const auto& entry = buffer_.front();
        target_sink_->write(entry);
        buffer_.pop();
    }
    
    target_sink_->flush();
}

} // namespace omop::common