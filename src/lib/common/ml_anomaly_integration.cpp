/**
 * @file ml_anomaly_integration.cpp
 * @brief Implementation of ML anomaly integration wrapper
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#include "ml_anomaly_integration.h"
#include "logging.h"
#include <algorithm>
#include <numeric>
#include <cstdlib>
#include <cstring>

namespace omop::common {

MLAnomalyIntegration::MLAnomalyIntegration(const Config& config)
    : config_(config) {
    last_training_time_ = std::chrono::system_clock::time_point::min();
    
    // Initialize statistics
    ml_stats_.last_training_time = last_training_time_;
}

MLAnomalyIntegration::~MLAnomalyIntegration() = default;

bool MLAnomalyIntegration::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (models_initialized_.load()) {
        OMOP_LOG_DEBUG("ML anomaly detectors already initialized");
        return true;
    }
    
    OMOP_LOG_INFO("Initializing ML anomaly detection systems");
    
    bool success = true;
    
    // Initialize individual detectors
    if (config_.enable_isolation_forest) {
        success &= initialize_isolation_forest();
    }
    
    if (config_.enable_lstm_autoencoder) {
        success &= initialize_lstm_autoencoder();
    }
    
    if (config_.enable_advanced_ensemble) {
        success &= initialize_advanced_ensemble();
    }
    
    if (success) {
        models_initialized_.store(true);
        OMOP_LOG_INFO("ML anomaly detection systems initialized successfully");
    } else {
        OMOP_LOG_ERROR("Failed to initialize ML anomaly detection systems");
    }
    
    return success;
}

bool MLAnomalyIntegration::initialize_isolation_forest() {
    try {
        isolation_detector_ = std::make_unique<omop::common::ml::IsolationForestDetector>(
            config_.isolation_config);
        
        std::lock_guard<std::mutex> stats_lock(stats_mutex_);
        ml_stats_.isolation_forest_available = true;
        
        OMOP_LOG_INFO("Isolation Forest detector initialized");
        return true;
    } catch (const std::exception& e) {
        OMOP_LOG_ERROR("Failed to initialize Isolation Forest: {}", e.what());
        return false;
    }
}

bool MLAnomalyIntegration::initialize_lstm_autoencoder() {
    try {
        lstm_detector_ = std::make_unique<omop::common::ml::LSTMAutoencoderDetector>(
            config_.lstm_config);
        
        std::lock_guard<std::mutex> stats_lock(stats_mutex_);
        ml_stats_.lstm_autoencoder_available = true;
        
        OMOP_LOG_INFO("LSTM Autoencoder detector initialized");
        return true;
    } catch (const std::exception& e) {
        OMOP_LOG_ERROR("Failed to initialize LSTM Autoencoder: {}", e.what());
        return false;
    }
}

bool MLAnomalyIntegration::initialize_advanced_ensemble() {
    try {
        ensemble_detector_ = std::make_unique<omop::common::ml::AdvancedMLAnomalyDetector>(
            config_.ensemble_config);
        
        std::lock_guard<std::mutex> stats_lock(stats_mutex_);
        ml_stats_.advanced_ensemble_available = true;
        
        OMOP_LOG_INFO("Advanced ML Ensemble detector initialized");
        return true;
    } catch (const std::exception& e) {
        OMOP_LOG_ERROR("Failed to initialize Advanced ML Ensemble: {}", e.what());
        return false;
    }
}

bool MLAnomalyIntegration::train_models(const TimeSeries& series, bool force_retrain) {
    if (training_in_progress_.load()) {
        OMOP_LOG_WARN("ML training already in progress, skipping");
        return false;
    }
    
    if (!force_retrain && !should_retrain(series)) {
        OMOP_LOG_DEBUG("ML retraining not needed");
        return true;
    }
    
    training_in_progress_.store(true);
    std::lock_guard<std::mutex> lock(mutex_);
    
    OMOP_LOG_INFO("Starting ML anomaly detector training");
    auto training_start = std::chrono::high_resolution_clock::now();
    
    // Prepare training data
    auto training_data = prepare_training_data(series);
    if (training_data.size() < config_.min_training_samples) {
        OMOP_LOG_ERROR("Insufficient training data: {} < {}", 
                      training_data.size(), config_.min_training_samples);
        training_in_progress_.store(false);
        return false;
    }
    
    bool overall_success = true;
    
    // Train Isolation Forest
    if (config_.enable_isolation_forest && isolation_detector_) {
        try {
            // Extract features for Isolation Forest
            std::vector<std::vector<double>> features;
            for (size_t i = 0; i < training_data.size(); ++i) {
                std::vector<double> feature_vector;
                feature_vector.push_back(training_data[i].value);
                
                if (i > 0) {
                    feature_vector.push_back(training_data[i].value - training_data[i-1].value);
                } else {
                    feature_vector.push_back(0.0);
                }
                
                features.push_back(feature_vector);
            }
            
            bool iso_success = isolation_detector_->train_model(features);
            if (iso_success) {
                OMOP_LOG_INFO("Isolation Forest training completed successfully");
                std::lock_guard<std::mutex> stats_lock(stats_mutex_);
                ml_stats_.isolation_stats = isolation_detector_->get_model_stats();
            } else {
                OMOP_LOG_ERROR("Isolation Forest training failed");
                overall_success = false;
            }
        } catch (const std::exception& e) {
            OMOP_LOG_ERROR("Exception during Isolation Forest training: {}", e.what());
            overall_success = false;
        }
    }
    
    // Train LSTM Autoencoder
    if (config_.enable_lstm_autoencoder && lstm_detector_) {
        try {
            // Extract values for LSTM
            std::vector<double> values;
            values.reserve(training_data.size());
            for (const auto& point : training_data) {
                values.push_back(point.value);
            }
            
            bool lstm_success = lstm_detector_->train_model(values);
            if (lstm_success) {
                OMOP_LOG_INFO("LSTM Autoencoder training completed successfully");
                std::lock_guard<std::mutex> stats_lock(stats_mutex_);
                ml_stats_.lstm_stats = lstm_detector_->get_model_stats();
            } else {
                OMOP_LOG_ERROR("LSTM Autoencoder training failed");
                overall_success = false;
            }
        } catch (const std::exception& e) {
            OMOP_LOG_ERROR("Exception during LSTM Autoencoder training: {}", e.what());
            overall_success = false;
        }
    }
    
    // Train Advanced Ensemble
    if (config_.enable_advanced_ensemble && ensemble_detector_) {
        try {
            bool ensemble_success = ensemble_detector_->train_models(training_data);
            if (ensemble_success) {
                OMOP_LOG_INFO("Advanced ML Ensemble training completed successfully");
                std::lock_guard<std::mutex> stats_lock(stats_mutex_);
                ml_stats_.ensemble_stats = ensemble_detector_->get_ensemble_stats();
            } else {
                OMOP_LOG_ERROR("Advanced ML Ensemble training failed");
                overall_success = false;
            }
        } catch (const std::exception& e) {
            OMOP_LOG_ERROR("Exception during Advanced ML Ensemble training: {}", e.what());
            overall_success = false;
        }
    }
    
    auto training_end = std::chrono::high_resolution_clock::now();
    auto training_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        training_end - training_start);
    
    // Update statistics
    {
        std::lock_guard<std::mutex> stats_lock(stats_mutex_);
        ml_stats_.total_training_samples = training_data.size();
        ml_stats_.last_training_time = std::chrono::system_clock::now();
        ml_stats_.last_training_duration = training_duration;
    }
    
    last_training_time_ = std::chrono::system_clock::now();
    training_in_progress_.store(false);
    
    OMOP_LOG_INFO("ML training completed in {} ms. Success: {}", 
                  training_duration.count(), overall_success);
    
    return overall_success;
}

std::vector<Anomaly> MLAnomalyIntegration::detect_anomalies(
    const TimeSeries& series,
    double analysis_window_hours,
    AnomalyDetectionAlgorithm algorithm) {
    
    auto detection_start = std::chrono::high_resolution_clock::now();
    std::vector<Anomaly> all_anomalies;
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    try {
        switch (algorithm) {
            case AnomalyDetectionAlgorithm::ISOLATION_FOREST:
                if (isolation_detector_ && isolation_detector_->is_trained()) {
                    all_anomalies = isolation_detector_->detect_anomalies(series, analysis_window_hours);
                }
                break;
                
            case AnomalyDetectionAlgorithm::LSTM_AUTOENCODER:
                if (lstm_detector_ && lstm_detector_->is_trained()) {
                    all_anomalies = lstm_detector_->detect_anomalies(series, analysis_window_hours);
                }
                break;
                
            case AnomalyDetectionAlgorithm::ENSEMBLE:
            default:
                if (ensemble_detector_ && ensemble_detector_->is_trained()) {
                    all_anomalies = ensemble_detector_->detect_anomalies(series, analysis_window_hours);
                } else {
                    // Fallback: combine individual detectors
                    if (isolation_detector_ && isolation_detector_->is_trained()) {
                        auto iso_anomalies = isolation_detector_->detect_anomalies(series, analysis_window_hours);
                        all_anomalies.insert(all_anomalies.end(), iso_anomalies.begin(), iso_anomalies.end());
                    }
                    
                    if (lstm_detector_ && lstm_detector_->is_trained()) {
                        auto lstm_anomalies = lstm_detector_->detect_anomalies(series, analysis_window_hours);
                        all_anomalies.insert(all_anomalies.end(), lstm_anomalies.begin(), lstm_anomalies.end());
                    }
                }
                break;
        }
    } catch (const std::exception& e) {
        OMOP_LOG_ERROR("Exception during ML anomaly detection: {}", e.what());
    }
    
    // Validate and filter results
    auto validated_anomalies = validate_detection_results(all_anomalies);
    
    auto detection_end = std::chrono::high_resolution_clock::now();
    auto detection_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        detection_end - detection_start);
    
    // Update performance statistics
    update_performance_stats(detection_duration, validated_anomalies.size());
    
    OMOP_LOG_DEBUG("ML anomaly detection completed in {} ms, found {} anomalies",
                   detection_duration.count(), validated_anomalies.size());
    
    return validated_anomalies;
}

bool MLAnomalyIntegration::are_models_trained() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    bool isolation_trained = !config_.enable_isolation_forest || 
                           (isolation_detector_ && isolation_detector_->is_trained());
    
    bool lstm_trained = !config_.enable_lstm_autoencoder || 
                       (lstm_detector_ && lstm_detector_->is_trained());
    
    bool ensemble_trained = !config_.enable_advanced_ensemble || 
                           (ensemble_detector_ && ensemble_detector_->is_trained());
    
    return isolation_trained && lstm_trained && ensemble_trained;
}

MLAnomalyIntegration::MLStatistics MLAnomalyIntegration::get_ml_statistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return ml_stats_;
}

bool MLAnomalyIntegration::supports_algorithm(AnomalyDetectionAlgorithm algorithm) const {
    switch (algorithm) {
        case AnomalyDetectionAlgorithm::ISOLATION_FOREST:
            return config_.enable_isolation_forest;
        case AnomalyDetectionAlgorithm::LSTM_AUTOENCODER:
            return config_.enable_lstm_autoencoder;
        case AnomalyDetectionAlgorithm::ENSEMBLE:
            return config_.enable_advanced_ensemble;
        default:
            return false;
    }
}

std::vector<AnomalyDetectionAlgorithm> MLAnomalyIntegration::get_available_algorithms() const {
    std::vector<AnomalyDetectionAlgorithm> algorithms;
    
    if (config_.enable_isolation_forest) {
        algorithms.push_back(AnomalyDetectionAlgorithm::ISOLATION_FOREST);
    }
    
    if (config_.enable_lstm_autoencoder) {
        algorithms.push_back(AnomalyDetectionAlgorithm::LSTM_AUTOENCODER);
    }
    
    if (config_.enable_advanced_ensemble) {
        algorithms.push_back(AnomalyDetectionAlgorithm::ENSEMBLE);
    }
    
    return algorithms;
}

MLAnomalyIntegration::HealthStatus MLAnomalyIntegration::check_health() const {
    HealthStatus status;
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Check initialization
    if (!models_initialized_.load()) {
        status.overall_healthy = false;
        status.issues.push_back("ML models not initialized");
    }
    
    // Check individual detectors
    if (config_.enable_isolation_forest) {
        if (!isolation_detector_) {
            status.overall_healthy = false;
            status.issues.push_back("Isolation Forest detector not created");
        } else if (!isolation_detector_->is_trained()) {
            status.warnings.push_back("Isolation Forest not trained");
        }
    }
    
    if (config_.enable_lstm_autoencoder) {
        if (!lstm_detector_) {
            status.overall_healthy = false;
            status.issues.push_back("LSTM Autoencoder detector not created");
        } else if (!lstm_detector_->is_trained()) {
            status.warnings.push_back("LSTM Autoencoder not trained");
        }
    }
    
    if (config_.enable_advanced_ensemble) {
        if (!ensemble_detector_) {
            status.overall_healthy = false;
            status.issues.push_back("Advanced ML Ensemble detector not created");
        } else if (!ensemble_detector_->is_trained()) {
            status.warnings.push_back("Advanced ML Ensemble not trained");
        }
    }
    
    // Check training recency
    auto now = std::chrono::system_clock::now();
    auto time_since_training = now - last_training_time_;
    
    if (time_since_training > config_.training_interval * 2) {
        status.warnings.push_back("Models may need retraining (last trained " + 
                                 std::to_string(std::chrono::duration_cast<std::chrono::hours>(
                                     time_since_training).count()) + " hours ago)");
    }
    
    // Calculate performance score
    {
        std::lock_guard<std::mutex> stats_lock(stats_mutex_);
        
        double detection_score = 1.0;
        if (ml_stats_.average_detection_time > config_.max_detection_time) {
            detection_score = static_cast<double>(config_.max_detection_time.count()) / 
                            ml_stats_.average_detection_time.count();
        }
        
        double accuracy_score = ml_stats_.detection_accuracy;
        
        status.performance_score = (detection_score + accuracy_score) / 2.0;
    }
    
    return status;
}

void MLAnomalyIntegration::reset_models() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    OMOP_LOG_INFO("Resetting ML anomaly detection models");
    
    // Reset individual detectors
    if (isolation_detector_) {
        isolation_detector_.reset();
    }
    
    if (lstm_detector_) {
        lstm_detector_.reset();
    }
    
    if (ensemble_detector_) {
        ensemble_detector_.reset();
    }
    
    // Reset state
    models_initialized_.store(false);
    last_training_time_ = std::chrono::system_clock::time_point::min();
    
    // Reset statistics
    {
        std::lock_guard<std::mutex> stats_lock(stats_mutex_);
        ml_stats_ = MLStatistics{};
    }
}

bool MLAnomalyIntegration::should_retrain(const TimeSeries& series) const {
    if (!config_.auto_train) {
        return false;
    }
    
    auto now = std::chrono::system_clock::now();
    auto time_since_training = now - last_training_time_;
    
    // Check if enough time has passed
    if (time_since_training < config_.training_interval) {
        return false;
    }
    
    // Check if we have enough new data
    auto recent_points = series.get_points_in_range(
        last_training_time_, now);
    
    return recent_points.size() >= config_.min_training_samples;
}

std::vector<TimeSeriesPoint> MLAnomalyIntegration::prepare_training_data(
    const TimeSeries& series) const {
    
    auto now = std::chrono::system_clock::now();
    auto training_start = now - std::chrono::duration_cast<std::chrono::system_clock::duration>(
        std::chrono::duration<double, std::ratio<3600>>(config_.training_data_hours));
    
    return series.get_points_in_range(training_start, now);
}

void MLAnomalyIntegration::update_performance_stats(
    std::chrono::milliseconds detection_time, size_t anomalies_found) {
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    ml_stats_.total_detections++;
    ml_stats_.anomalies_detected += anomalies_found;
    
    // Update average detection time
    if (ml_stats_.total_detections == 1) {
        ml_stats_.average_detection_time = detection_time;
    } else {
        auto total_time = ml_stats_.average_detection_time.count() * (ml_stats_.total_detections - 1) +
                         detection_time.count();
        ml_stats_.average_detection_time = std::chrono::milliseconds(total_time / ml_stats_.total_detections);
    }
    
    // Simple accuracy estimation (placeholder)
    if (ml_stats_.total_detections > 0) {
        ml_stats_.detection_accuracy = std::min(1.0, 
            static_cast<double>(ml_stats_.anomalies_detected) / ml_stats_.total_detections * 0.1);
    }
}

std::vector<Anomaly> MLAnomalyIntegration::validate_detection_results(
    const std::vector<Anomaly>& anomalies) const {
    
    std::vector<Anomaly> validated;
    validated.reserve(anomalies.size());
    
    for (const auto& anomaly : anomalies) {
        // Basic validation
        if (anomaly.id.empty() || anomaly.metric_name.empty()) {
            OMOP_LOG_WARN("Invalid anomaly detected (empty ID or metric name), skipping");
            continue;
        }
        
        if (anomaly.confidence_score < 0.0 || anomaly.confidence_score > 1.0) {
            OMOP_LOG_WARN("Invalid confidence score: {}, adjusting", anomaly.confidence_score);
            Anomaly adjusted = anomaly;
            adjusted.confidence_score = std::max(0.0, std::min(1.0, anomaly.confidence_score));
            validated.push_back(adjusted);
        } else {
            validated.push_back(anomaly);
        }
    }
    
    return validated;
}

// Factory implementations

std::unique_ptr<MLAnomalyIntegration> MLAnomalyIntegrationFactory::create_production_instance() {
    MLAnomalyIntegration::Config config;
    
    // Production settings
    config.enable_isolation_forest = true;
    config.enable_lstm_autoencoder = true;
    config.enable_advanced_ensemble = true;
    config.auto_train = true;
    config.training_interval = std::chrono::hours(24);
    config.min_training_samples = 1000;
    config.training_data_hours = 72.0;
    config.max_training_time = std::chrono::milliseconds(60000);  // 1 minute
    config.max_detection_time = std::chrono::milliseconds(10000); // 10 seconds
    
    // Configure individual detectors for production
    config.isolation_config.n_estimators = 100;
    config.isolation_config.contamination = 0.05;
    config.isolation_config.confidence_threshold = 0.6;
    
    config.lstm_config.sequence_length = 50;
    config.lstm_config.encoding_dim = 32;
    config.lstm_config.epochs = 50;
    config.lstm_config.confidence_threshold = 0.7;
    
    return std::make_unique<MLAnomalyIntegration>(config);
}

std::unique_ptr<MLAnomalyIntegration> MLAnomalyIntegrationFactory::create_development_instance() {
    MLAnomalyIntegration::Config config;
    
    // Development settings (faster, less accurate)
    config.enable_isolation_forest = true;
    config.enable_lstm_autoencoder = false;  // Disabled for faster development
    config.enable_advanced_ensemble = false;
    config.auto_train = true;
    config.training_interval = std::chrono::hours(1);
    config.min_training_samples = 200;
    config.training_data_hours = 24.0;
    config.max_training_time = std::chrono::milliseconds(10000);  // 10 seconds
    config.max_detection_time = std::chrono::milliseconds(2000);  // 2 seconds
    
    // Configure for development
    config.isolation_config.n_estimators = 20;
    config.isolation_config.contamination = 0.1;
    config.isolation_config.confidence_threshold = 0.5;
    
    return std::make_unique<MLAnomalyIntegration>(config);
}

std::unique_ptr<MLAnomalyIntegration> MLAnomalyIntegrationFactory::create_from_environment() {
    MLAnomalyIntegration::Config config;
    
    // Parse environment variables
    if (const char* val = std::getenv("ML_ANOMALY_ISOLATION_FOREST_ENABLED")) {
        config.enable_isolation_forest = (std::strcmp(val, "true") == 0);
    }
    
    if (const char* val = std::getenv("ML_ANOMALY_LSTM_ENABLED")) {
        config.enable_lstm_autoencoder = (std::strcmp(val, "true") == 0);
    }
    
    if (const char* val = std::getenv("ML_ANOMALY_ENSEMBLE_ENABLED")) {
        config.enable_advanced_ensemble = (std::strcmp(val, "true") == 0);
    }
    
    if (const char* val = std::getenv("ML_ANOMALY_AUTO_TRAIN")) {
        config.auto_train = (std::strcmp(val, "true") == 0);
    }
    
    if (const char* val = std::getenv("ML_ANOMALY_TRAINING_INTERVAL")) {
        try {
            int hours = std::stoi(val);
            config.training_interval = std::chrono::hours(hours);
        } catch (...) {
            OMOP_LOG_WARN("Invalid ML_ANOMALY_TRAINING_INTERVAL value: {}", val);
        }
    }
    
    if (const char* val = std::getenv("ML_ANOMALY_MIN_TRAINING_SAMPLES")) {
        try {
            config.min_training_samples = std::stoul(val);
        } catch (...) {
            OMOP_LOG_WARN("Invalid ML_ANOMALY_MIN_TRAINING_SAMPLES value: {}", val);
        }
    }
    
    return std::make_unique<MLAnomalyIntegration>(config);
}

std::unique_ptr<MLAnomalyIntegration> MLAnomalyIntegrationFactory::create_high_performance_instance() {
    MLAnomalyIntegration::Config config;
    
    // High performance settings (speed over accuracy)
    config.enable_isolation_forest = true;
    config.enable_lstm_autoencoder = false;  // Disabled for speed
    config.enable_advanced_ensemble = false;
    config.max_training_time = std::chrono::milliseconds(5000);   // 5 seconds
    config.max_detection_time = std::chrono::milliseconds(1000);  // 1 second
    
    // Fast Isolation Forest
    config.isolation_config.n_estimators = 20;
    config.isolation_config.max_samples = 128;
    config.isolation_config.confidence_threshold = 0.4;  // Lower threshold for speed
    
    return std::make_unique<MLAnomalyIntegration>(config);
}

std::unique_ptr<MLAnomalyIntegration> MLAnomalyIntegrationFactory::create_high_accuracy_instance() {
    MLAnomalyIntegration::Config config;
    
    // High accuracy settings (accuracy over speed)
    config.enable_isolation_forest = true;
    config.enable_lstm_autoencoder = true;
    config.enable_advanced_ensemble = true;
    config.max_training_time = std::chrono::milliseconds(300000);  // 5 minutes
    config.max_detection_time = std::chrono::milliseconds(30000);  // 30 seconds
    
    // High-accuracy Isolation Forest
    config.isolation_config.n_estimators = 200;
    config.isolation_config.max_samples = 512;
    config.isolation_config.confidence_threshold = 0.8;  // High threshold
    
    // High-accuracy LSTM
    config.lstm_config.sequence_length = 100;
    config.lstm_config.encoding_dim = 64;
    config.lstm_config.epochs = 100;
    config.lstm_config.confidence_threshold = 0.8;
    
    return std::make_unique<MLAnomalyIntegration>(config);
}

} // namespace omop::common