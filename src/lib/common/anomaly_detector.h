/**
 * @file anomaly_detector.h
 * @brief Anomaly detection system for OMOP ETL health and performance monitoring
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#pragma once

#include "metrics_collector.h"
#include "alert_manager.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <queue>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <cmath>

namespace omop::common {

/**
 * @brief Anomaly detection algorithm types
 */
enum class AnomalyDetectionAlgorithm {
    STATISTICAL_OUTLIER,     ///< Statistical outlier detection (Z-score, IQR)
    MOVING_AVERAGE,          ///< Moving average based detection
    EXPONENTIAL_SMOOTHING,   ///< Exponential smoothing
    SEASONAL_DECOMPOSITION,  ///< Seasonal decomposition for time series
    ISOLATION_FOREST,        ///< Isolation forest algorithm
    ONE_CLASS_SVM,           ///< One-class Support Vector Machine
    LSTM_AUTOENCODER,        ///< LSTM autoencoder (if ML libraries available)
    ENSEMBLE                 ///< Ensemble of multiple algorithms
};

/**
 * @brief Anomaly severity levels
 */
enum class AnomalySeverity {
    LOW,        ///< Minor deviation from normal
    MEDIUM,     ///< Moderate anomaly
    HIGH,       ///< Significant anomaly
    CRITICAL    ///< Severe anomaly requiring immediate attention
};

/**
 * @brief Detected anomaly information
 */
struct Anomaly {
    std::string id;                                    ///< Unique anomaly ID
    std::string metric_name;                          ///< Metric that triggered anomaly
    double observed_value;                            ///< Observed metric value
    double expected_value;                            ///< Expected/predicted value
    double deviation;                                 ///< Absolute deviation
    double confidence_score;                          ///< Detection confidence (0-1)
    AnomalySeverity severity;                         ///< Anomaly severity
    AnomalyDetectionAlgorithm detection_algorithm;    ///< Algorithm that detected it
    std::chrono::system_clock::time_point timestamp;  ///< When anomaly was detected
    std::chrono::system_clock::time_point window_start; ///< Analysis window start
    std::chrono::system_clock::time_point window_end;   ///< Analysis window end
    std::unordered_map<std::string, std::string> metadata; ///< Additional context
    
    /**
     * @brief Generate human-readable description
     * @return Anomaly description
     */
    std::string get_description() const;
    
    /**
     * @brief Convert to Alert for alerting system
     * @param component Component name
     * @return Alert instance
     */
    Alert to_alert(const std::string& component = "anomaly-detector") const;
};

/**
 * @brief Time series data point
 */
struct TimeSeriesPoint {
    std::chrono::system_clock::time_point timestamp;
    double value;
    std::unordered_map<std::string, std::string> labels;
    
    TimeSeriesPoint(std::chrono::system_clock::time_point ts, double val)
        : timestamp(ts), value(val) {}
    
    TimeSeriesPoint(std::chrono::system_clock::time_point ts, double val,
                   const std::unordered_map<std::string, std::string>& lbls)
        : timestamp(ts), value(val), labels(lbls) {}
};

/**
 * @brief Time series data container
 */
class TimeSeries {
public:
    /**
     * @brief Constructor
     * @param name Time series name
     * @param max_points Maximum points to retain
     */
    TimeSeries(const std::string& name, size_t max_points = 1000);
    
    /**
     * @brief Add data point
     * @param point Time series point
     */
    void add_point(const TimeSeriesPoint& point);
    
    /**
     * @brief Get all points
     * @return Vector of time series points
     */
    const std::vector<TimeSeriesPoint>& get_points() const;
    
    /**
     * @brief Get points within time range
     * @param start Start time
     * @param end End time
     * @return Vector of points in range
     */
    std::vector<TimeSeriesPoint> get_points_in_range(
        std::chrono::system_clock::time_point start,
        std::chrono::system_clock::time_point end) const;
    
    /**
     * @brief Get latest N points
     * @param count Number of points to retrieve
     * @return Vector of latest points
     */
    std::vector<TimeSeriesPoint> get_latest_points(size_t count) const;
    
    /**
     * @brief Get basic statistics
     */
    struct Statistics {
        double mean{0.0};
        double stddev{0.0};
        double min{0.0};
        double max{0.0};
        double median{0.0};
        size_t count{0};
    };
    
    Statistics get_statistics() const;
    Statistics get_statistics_in_range(
        std::chrono::system_clock::time_point start,
        std::chrono::system_clock::time_point end) const;
    
    /**
     * @brief Clear all points
     */
    void clear();
    
    /**
     * @brief Get series name
     * @return Series name
     */
    const std::string& get_name() const { return name_; }

private:
    std::string name_;
    size_t max_points_;
    mutable std::mutex mutex_;
    std::vector<TimeSeriesPoint> points_;
    
    /**
     * @brief Maintain size limit by removing oldest points
     */
    void maintain_size_limit();
};

/**
 * @brief Statistical anomaly detector
 */
class StatisticalAnomalyDetector {
public:
    /**
     * @brief Configuration for statistical detection
     */
    struct Config {
        double z_score_threshold{3.0};          ///< Z-score threshold for outliers
        double iqr_multiplier{1.5};             ///< IQR multiplier for outlier detection
        size_t min_data_points{10};             ///< Minimum points needed for detection
        size_t rolling_window_size{50};         ///< Rolling window size for statistics
        bool use_robust_statistics{true};       ///< Use median/MAD instead of mean/stddev
    };
    
    /**
     * @brief Constructor
     * @param config Detection configuration
     */
    explicit StatisticalAnomalyDetector(const Config& config = Config{});
    
    /**
     * @brief Detect anomalies in time series
     * @param series Time series to analyze
     * @param analysis_window_hours Hours of data to analyze
     * @return Vector of detected anomalies
     */
    std::vector<Anomaly> detect_anomalies(
        const TimeSeries& series,
        double analysis_window_hours = 1.0) const;
    
    /**
     * @brief Detect anomaly in single value against historical data
     * @param series_name Series name
     * @param current_value Current value to check
     * @param historical_points Historical data points
     * @return Anomaly if detected, nullptr otherwise
     */
    std::unique_ptr<Anomaly> detect_point_anomaly(
        const std::string& series_name,
        double current_value,
        const std::vector<TimeSeriesPoint>& historical_points) const;

private:
    Config config_;
    
    /**
     * @brief Calculate Z-score anomalies
     * @param series Time series
     * @param points Points to analyze
     * @return Vector of anomalies
     */
    std::vector<Anomaly> detect_zscore_anomalies(
        const TimeSeries& series,
        const std::vector<TimeSeriesPoint>& points) const;
    
    /**
     * @brief Calculate IQR-based anomalies
     * @param series Time series
     * @param points Points to analyze
     * @return Vector of anomalies
     */
    std::vector<Anomaly> detect_iqr_anomalies(
        const TimeSeries& series,
        const std::vector<TimeSeriesPoint>& points) const;
    
    /**
     * @brief Calculate robust statistics (median, MAD)
     * @param values Vector of values
     * @return Pair of median and MAD
     */
    std::pair<double, double> calculate_robust_stats(const std::vector<double>& values) const;
};

/**
 * @brief Moving average based anomaly detector
 */
class MovingAverageAnomalyDetector {
public:
    /**
     * @brief Configuration for moving average detection
     */
    struct Config {
        size_t window_size{20};                 ///< Moving average window size
        double threshold_multiplier{2.0};       ///< Threshold multiplier for deviation
        size_t min_data_points{10};             ///< Minimum points needed
        bool use_exponential_smoothing{false};  ///< Use exponential vs simple moving average
        double smoothing_factor{0.3};           ///< Exponential smoothing factor (alpha)
    };
    
    /**
     * @brief Constructor
     * @param config Detection configuration
     */
    explicit MovingAverageAnomalyDetector(const Config& config = Config{});
    
    /**
     * @brief Detect anomalies using moving average
     * @param series Time series to analyze
     * @param analysis_window_hours Hours of data to analyze
     * @return Vector of detected anomalies
     */
    std::vector<Anomaly> detect_anomalies(
        const TimeSeries& series,
        double analysis_window_hours = 1.0) const;

private:
    Config config_;
    
    /**
     * @brief Calculate simple moving average
     * @param values Vector of values
     * @param window_size Window size
     * @return Vector of moving averages
     */
    std::vector<double> calculate_moving_average(
        const std::vector<double>& values, size_t window_size) const;
    
    /**
     * @brief Calculate exponential moving average
     * @param values Vector of values
     * @param alpha Smoothing factor
     * @return Vector of exponentially smoothed values
     */
    std::vector<double> calculate_exponential_moving_average(
        const std::vector<double>& values, double alpha) const;
};

/**
 * @brief Seasonal decomposition anomaly detector
 */
class SeasonalAnomalyDetector {
public:
    /**
     * @brief Configuration for seasonal detection
     */
    struct Config {
        std::chrono::minutes seasonal_period{60};  ///< Expected seasonal period
        double trend_threshold{2.0};               ///< Trend deviation threshold
        double seasonal_threshold{2.0};            ///< Seasonal deviation threshold
        double residual_threshold{3.0};            ///< Residual deviation threshold
        size_t min_seasonal_cycles{3};             ///< Minimum cycles for seasonal analysis
        size_t decomposition_window_size{100};     ///< Window size for decomposition
    };
    
    /**
     * @brief Constructor
     * @param config Detection configuration
     */
    explicit SeasonalAnomalyDetector(const Config& config = Config{});
    
    /**
     * @brief Detect seasonal anomalies
     * @param series Time series to analyze
     * @param analysis_window_hours Hours of data to analyze
     * @return Vector of detected anomalies
     */
    std::vector<Anomaly> detect_anomalies(
        const TimeSeries& series,
        double analysis_window_hours = 24.0) const;

private:
    Config config_;
    
    /**
     * @brief Decompose time series into trend, seasonal, and residual components
     * @param values Time series values
     * @param timestamps Corresponding timestamps
     * @return Tuple of trend, seasonal, and residual components
     */
    std::tuple<std::vector<double>, std::vector<double>, std::vector<double>>
    decompose_time_series(const std::vector<double>& values,
                         const std::vector<std::chrono::system_clock::time_point>& timestamps) const;
};

/**
 * @brief Ensemble anomaly detector combining multiple algorithms
 */
class EnsembleAnomalyDetector {
public:
    /**
     * @brief Configuration for ensemble detection
     */
    struct Config {
        std::vector<AnomalyDetectionAlgorithm> algorithms{
            AnomalyDetectionAlgorithm::STATISTICAL_OUTLIER,
            AnomalyDetectionAlgorithm::MOVING_AVERAGE
        };
        double consensus_threshold{0.5};        ///< Fraction of algorithms that must agree
        bool weight_by_confidence{true};        ///< Weight votes by confidence scores
        double min_confidence{0.3};             ///< Minimum confidence for individual detectors
    };
    
    /**
     * @brief Constructor
     * @param config Ensemble configuration
     */
    explicit EnsembleAnomalyDetector(const Config& config = Config{});
    
    /**
     * @brief Detect anomalies using ensemble approach
     * @param series Time series to analyze
     * @param analysis_window_hours Hours of data to analyze
     * @return Vector of consensus anomalies
     */
    std::vector<Anomaly> detect_anomalies(
        const TimeSeries& series,
        double analysis_window_hours = 1.0) const;

private:
    Config config_;
    
    // Individual detectors
    std::unique_ptr<StatisticalAnomalyDetector> statistical_detector_;
    std::unique_ptr<MovingAverageAnomalyDetector> moving_average_detector_;
    std::unique_ptr<SeasonalAnomalyDetector> seasonal_detector_;
    
    // ML detectors (forward declaration)
    class MLDetectorWrapper;
    std::unique_ptr<MLDetectorWrapper> ml_detector_wrapper_;
    
    /**
     * @brief Combine anomalies from multiple detectors
     * @param all_anomalies Vector of anomaly vectors from different detectors
     * @return Consensus anomalies
     */
    std::vector<Anomaly> combine_anomalies(
        const std::vector<std::vector<Anomaly>>& all_anomalies) const;
    
    /**
     * @brief Check if anomalies are similar (same time window and metric)
     * @param a1 First anomaly
     * @param a2 Second anomaly
     * @param time_tolerance Time tolerance for matching
     * @return true if anomalies are similar
     */
    bool are_anomalies_similar(const Anomaly& a1, const Anomaly& a2,
                              std::chrono::seconds time_tolerance = std::chrono::seconds(60)) const;
};

/**
 * @brief Main anomaly detection system
 */
class AnomalyDetectionSystem {
public:
    /**
     * @brief Configuration for anomaly detection system
     */
    struct Config {
        bool enabled{true};
        std::chrono::seconds analysis_interval{30};     ///< How often to run detection
        std::chrono::hours data_retention{72};          ///< How long to keep time series data
        AnomalyDetectionAlgorithm default_algorithm{AnomalyDetectionAlgorithm::ENSEMBLE};
        
        // Metric-specific configurations
        std::unordered_map<std::string, AnomalyDetectionAlgorithm> metric_algorithms;
        std::unordered_map<std::string, double> metric_thresholds;
        
        // Alerting configuration
        bool enable_alerting{true};
        AnomalySeverity min_alert_severity{AnomalySeverity::MEDIUM};
        std::chrono::minutes alert_cooldown{15};        ///< Cooldown between alerts for same metric
        
        // Performance settings
        size_t max_concurrent_analyses{4};              ///< Maximum concurrent analysis threads
        size_t max_metrics_tracked{1000};               ///< Maximum number of metrics to track
        size_t max_points_per_metric{10000};            ///< Maximum points per time series
    };
    
    /**
     * @brief Constructor
     * @param config System configuration
     */
    explicit AnomalyDetectionSystem(const Config& config = Config{});
    
    /**
     * @brief Destructor
     */
    ~AnomalyDetectionSystem();
    
    /**
     * @brief Start anomaly detection system
     */
    void start();
    
    /**
     * @brief Stop anomaly detection system
     */
    void stop();
    
    /**
     * @brief Add metric data point for monitoring
     * @param metric_name Metric name
     * @param value Metric value
     * @param labels Optional metric labels
     */
    void add_metric_point(const std::string& metric_name, double value,
                         const std::unordered_map<std::string, std::string>& labels = {});
    
    /**
     * @brief Set metrics collector as data source
     * @param metrics_collector Metrics collector instance
     */
    void set_metrics_collector(std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector);
    
    /**
     * @brief Set alert manager for anomaly notifications
     * @param alert_manager Alert manager instance
     */
    void set_alert_manager(std::shared_ptr<AlertManager> alert_manager);
    
    /**
     * @brief Configure metric-specific detection algorithm
     * @param metric_name Metric name pattern (supports wildcards)
     * @param algorithm Detection algorithm to use
     */
    void configure_metric_algorithm(const std::string& metric_name, 
                                   AnomalyDetectionAlgorithm algorithm);
    
    /**
     * @brief Configure metric-specific threshold
     * @param metric_name Metric name pattern
     * @param threshold Custom threshold value
     */
    void configure_metric_threshold(const std::string& metric_name, double threshold);
    
    /**
     * @brief Get recent anomalies
     * @param limit Maximum number of anomalies to return
     * @param min_severity Minimum severity level
     * @return Vector of recent anomalies
     */
    std::vector<Anomaly> get_recent_anomalies(size_t limit = 100, 
                                             AnomalySeverity min_severity = AnomalySeverity::LOW) const;
    
    /**
     * @brief Get anomalies for specific metric
     * @param metric_name Metric name
     * @param hours_back Hours to look back
     * @return Vector of anomalies for metric
     */
    std::vector<Anomaly> get_metric_anomalies(const std::string& metric_name,
                                             double hours_back = 24.0) const;
    
    /**
     * @brief Get system statistics
     */
    struct Statistics {
        std::atomic<size_t> total_analyses{0};
        std::atomic<size_t> anomalies_detected{0};
        std::atomic<size_t> alerts_sent{0};
        std::atomic<size_t> metrics_tracked{0};
        std::atomic<size_t> total_data_points{0};
        std::chrono::system_clock::time_point last_analysis_time;
        std::chrono::milliseconds avg_analysis_duration{0};
        std::unordered_map<std::string, size_t> algorithm_usage_counts;
        std::unordered_map<AnomalySeverity, size_t> severity_counts;
    };
    
    Statistics get_statistics() const;
    
    /**
     * @brief Get time series data for metric
     * @param metric_name Metric name
     * @return Time series data
     */
    std::shared_ptr<TimeSeries> get_time_series(const std::string& metric_name) const;
    
    /**
     * @brief Manually trigger analysis for specific metric
     * @param metric_name Metric name
     * @param analysis_window_hours Analysis window in hours
     * @return Vector of detected anomalies
     */
    std::vector<Anomaly> analyze_metric(const std::string& metric_name,
                                       double analysis_window_hours = 1.0);

private:
    Config config_;
    std::atomic<bool> running_{false};
    
    // Time series storage
    mutable std::mutex time_series_mutex_;
    std::unordered_map<std::string, std::shared_ptr<TimeSeries>> time_series_;
    
    // Anomaly detectors
    std::unique_ptr<StatisticalAnomalyDetector> statistical_detector_;
    std::unique_ptr<MovingAverageAnomalyDetector> moving_average_detector_;
    std::unique_ptr<SeasonalAnomalyDetector> seasonal_detector_;
    std::unique_ptr<EnsembleAnomalyDetector> ensemble_detector_;
    
    // ML detectors (forward declaration)
    class MLDetectorWrapper;
    std::unique_ptr<MLDetectorWrapper> ml_detector_wrapper_;
    
    // External integrations
    std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector_;
    std::shared_ptr<AlertManager> alert_manager_;
    
    // Analysis management
    std::unique_ptr<std::thread> analysis_thread_;
    std::atomic<bool> analysis_requested_{false};
    
    // Anomaly storage
    mutable std::mutex anomalies_mutex_;
    std::vector<Anomaly> detected_anomalies_;
    
    // Alert cooldown tracking
    mutable std::mutex alert_cooldown_mutex_;
    std::unordered_map<std::string, std::chrono::system_clock::time_point> last_alert_times_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    Statistics stats_;
    
    /**
     * @brief Analysis thread main loop
     */
    void analysis_loop();
    
    /**
     * @brief Run analysis on all tracked metrics
     */
    void run_analysis_cycle();
    
    /**
     * @brief Analyze single metric
     * @param metric_name Metric name
     * @param series Time series data
     * @return Vector of detected anomalies
     */
    std::vector<Anomaly> analyze_single_metric(const std::string& metric_name,
                                              std::shared_ptr<TimeSeries> series);
    
    /**
     * @brief Get detector for metric
     * @param metric_name Metric name
     * @return Appropriate anomaly detector
     */
    AnomalyDetectionAlgorithm get_detector_for_metric(const std::string& metric_name) const;
    
    /**
     * @brief Process detected anomaly (alert, store, etc.)
     * @param anomaly Detected anomaly
     */
    void process_anomaly(const Anomaly& anomaly);
    
    /**
     * @brief Check if alert should be sent (cooldown check)
     * @param metric_name Metric name
     * @return true if alert should be sent
     */
    bool should_send_alert(const std::string& metric_name);
    
    /**
     * @brief Collect metrics from metrics collector
     */
    void collect_metrics_data();
    
    /**
     * @brief Cleanup old data
     */
    void cleanup_old_data();
    
    /**
     * @brief Generate unique anomaly ID
     * @return Unique ID string
     */
    std::string generate_anomaly_id() const;
    
    /**
     * @brief Update statistics
     * @param analysis_duration Duration of analysis
     * @param anomalies_found Number of anomalies found
     * @param algorithm Algorithm used
     */
    void update_statistics(std::chrono::milliseconds analysis_duration,
                          size_t anomalies_found,
                          AnomalyDetectionAlgorithm algorithm);
};

/**
 * @brief Factory for creating anomaly detection systems
 */
class AnomalyDetectionFactory {
public:
    /**
     * @brief Create production anomaly detection system
     * @param metrics_collector Metrics collector for data source
     * @param alert_manager Alert manager for notifications
     * @return Configured anomaly detection system
     */
    static std::unique_ptr<AnomalyDetectionSystem> create_production_system(
        std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector,
        std::shared_ptr<AlertManager> alert_manager);
    
    /**
     * @brief Create development anomaly detection system
     * @param metrics_collector Metrics collector for data source
     * @param alert_manager Alert manager for notifications
     * @return Configured system with relaxed thresholds
     */
    static std::unique_ptr<AnomalyDetectionSystem> create_development_system(
        std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector,
        std::shared_ptr<AlertManager> alert_manager);
    
    /**
     * @brief Create system from environment variables
     * Environment variables:
     * - ANOMALY_DETECTION_ENABLED: Enable/disable system
     * - ANOMALY_ANALYSIS_INTERVAL: Analysis interval in seconds
     * - ANOMALY_MIN_ALERT_SEVERITY: Minimum severity for alerts
     * - ANOMALY_ALGORITHM: Default algorithm to use
     * @param metrics_collector Metrics collector
     * @param alert_manager Alert manager
     * @return Configured system
     */
    static std::unique_ptr<AnomalyDetectionSystem> create_from_environment(
        std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector,
        std::shared_ptr<AlertManager> alert_manager);
};

} // namespace omop::common