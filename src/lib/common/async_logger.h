/**
 * @file async_logger.h
 * @brief Asynchronous logging system with lock-free queues
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include "logging.h"
#include <atomic>
#include <thread>
#include <memory>
#include <vector>
#include <queue>
#include <condition_variable>
#include <chrono>

namespace omop::common {

/**
 * @brief Lock-free circular buffer for log entries
 * 
 * High-performance circular buffer using atomic operations
 * to achieve > 100,000 messages/second capability
 */
template<typename T, size_t Size>
class LockFreeQueue {
private:
    struct alignas(64) Node {
        std::atomic<T*> data{nullptr};
        std::atomic<size_t> sequence{0};
    };
    
    static constexpr size_t buffer_size = Size;
    static_assert((buffer_size & (buffer_size - 1)) == 0, "Size must be power of 2");
    
    alignas(64) Node buffer_[buffer_size];
    alignas(64) std::atomic<size_t> head_{0};
    alignas(64) std::atomic<size_t> tail_{0};
    
public:
    LockFreeQueue() {
        for (size_t i = 0; i < buffer_size; ++i) {
            buffer_[i].sequence.store(i, std::memory_order_relaxed);
        }
    }
    
    /**
     * @brief Enqueue item (non-blocking)
     * @param item Item to enqueue
     * @return true if successful, false if queue is full
     */
    bool try_enqueue(T&& item) {
        size_t pos = head_.load(std::memory_order_relaxed);
        
        while (true) {
            Node& node = buffer_[pos & (buffer_size - 1)];
            size_t seq = node.sequence.load(std::memory_order_acquire);
            
            if (seq == pos) {
                if (head_.compare_exchange_weak(pos, pos + 1, std::memory_order_relaxed)) {
                    T* data = new T(std::move(item));
                    node.data.store(data, std::memory_order_release);
                    node.sequence.store(pos + 1, std::memory_order_release);
                    return true;
                }
            } else if (seq < pos) {
                return false; // Queue is full
            } else {
                pos = head_.load(std::memory_order_relaxed);
            }
        }
    }
    
    /**
     * @brief Dequeue item (non-blocking)
     * @param item Output item
     * @return true if successful, false if queue is empty
     */
    bool try_dequeue(T& item) {
        size_t pos = tail_.load(std::memory_order_relaxed);
        
        while (true) {
            Node& node = buffer_[pos & (buffer_size - 1)];
            size_t seq = node.sequence.load(std::memory_order_acquire);
            
            if (seq == pos + 1) {
                if (tail_.compare_exchange_weak(pos, pos + 1, std::memory_order_relaxed)) {
                    T* data = node.data.load(std::memory_order_acquire);
                    if (data) {
                        item = std::move(*data);
                        delete data;
                        node.data.store(nullptr, std::memory_order_relaxed);
                        node.sequence.store(pos + buffer_size, std::memory_order_release);
                        return true;
                    }
                }
            } else if (seq < pos + 1) {
                return false; // Queue is empty
            } else {
                pos = tail_.load(std::memory_order_relaxed);
            }
        }
    }
    
    /**
     * @brief Get approximate queue size
     * @return size_t Queue size
     */
    size_t size() const {
        size_t head = head_.load(std::memory_order_acquire);
        size_t tail = tail_.load(std::memory_order_acquire);
        return head >= tail ? head - tail : 0;
    }
    
    /**
     * @brief Check if queue is empty
     * @return true if empty
     */
    bool empty() const {
        return size() == 0;
    }
};

/**
 * @brief Asynchronous logger with high-performance lock-free queues
 * 
 * Features:
 * - > 100,000 messages/second throughput
 * - < 1μs per log message latency
 * - Lock-free producer-consumer queue
 * - Configurable flush intervals
 * - Automatic backpressure handling
 */
class AsyncLogger : public Logger {
public:
    /**
     * @brief Configuration for async logger
     */
    struct Config {
        size_t queue_size = 65536;                    // Must be power of 2
        size_t worker_threads = 1;                    // Number of worker threads
        std::chrono::milliseconds flush_interval{100}; // Flush interval
        size_t batch_size = 1000;                     // Batch size for processing
        bool enable_overflow_policy = true;           // Drop messages when full
        size_t max_memory_mb = 100;                   // Maximum memory usage
    };

    /**
     * @brief Constructor
     * @param name Logger name
     * @param config Async configuration
     */
    explicit AsyncLogger(const std::string& name);
    AsyncLogger(const std::string& name, const Config& config);

    /**
     * @brief Destructor
     */
    ~AsyncLogger() override;

    /**
     * @brief Start async logging
     */
    void start();

    /**
     * @brief Stop async logging
     * @param wait_for_flush Wait for all messages to be flushed
     */
    void stop(bool wait_for_flush = true);

    /**
     * @brief Flush all pending messages
     * @param timeout Maximum time to wait for flush
     * @return true if all messages were flushed
     */
    bool flush(std::chrono::milliseconds timeout = std::chrono::milliseconds(5000));

    /**
     * @brief Get performance statistics
     * @return Performance metrics
     */
    struct Statistics {
        uint64_t messages_queued{0};
        uint64_t messages_processed{0};
        uint64_t messages_dropped{0};
        uint64_t flush_count{0};
        uint64_t total_latency_ns{0};
        std::chrono::steady_clock::time_point start_time;
        
        double get_throughput() const {
            auto duration = std::chrono::steady_clock::now() - start_time;
            auto seconds = std::chrono::duration<double>(duration).count();
            return messages_processed / std::max(seconds, 0.001);
        }
        
        double get_average_latency_us() const {
            if (messages_processed == 0) return 0.0;
            return (total_latency_ns / messages_processed) / 1000.0;
        }
        
        double get_drop_rate() const {
            if (messages_queued == 0) return 0.0;
            return static_cast<double>(messages_dropped) / messages_queued;
        }
    };

    Statistics get_statistics() const { 
        Statistics result;
        result.messages_queued = stats_.messages_queued.load();
        result.messages_processed = stats_.messages_processed.load();
        result.messages_dropped = stats_.messages_dropped.load();
        result.flush_count = stats_.flush_count.load();
        result.total_latency_ns = stats_.total_latency_ns.load();
        result.start_time = stats_.start_time;
        return result; 
    }

protected:
    /**
     * @brief Override write_entry to use async queue
     * @param entry Log entry to write
     */
    void write_entry(const LogEntry& entry) override;

private:
    /**
     * @brief Internal log message with timing
     */
    struct AsyncLogMessage {
        LogEntry entry;
        std::chrono::steady_clock::time_point enqueue_time;
        
        AsyncLogMessage() = default;
        AsyncLogMessage(LogEntry&& e) 
            : entry(std::move(e)), enqueue_time(std::chrono::steady_clock::now()) {}
    };

    /**
     * @brief Worker thread function
     */
    void worker_thread();

    /**
     * @brief Process batch of messages
     * @param messages Batch of messages
     */
    void process_batch(std::vector<AsyncLogMessage>& messages);

    /**
     * @brief Handle queue overflow
     * @param entry Entry that couldn't be queued
     */
    void handle_overflow(const LogEntry& entry);

    Config config_;
    std::unique_ptr<LockFreeQueue<AsyncLogMessage, 65536>> queue_;
    std::vector<std::thread> workers_;
    std::atomic<bool> running_{false};
    std::atomic<bool> flush_requested_{false};
    std::condition_variable flush_cv_;
    std::mutex flush_mutex_;
    
    // Atomic statistics for thread safety
    struct AtomicStatistics {
        std::atomic<uint64_t> messages_queued{0};
        std::atomic<uint64_t> messages_processed{0};
        std::atomic<uint64_t> messages_dropped{0};
        std::atomic<uint64_t> flush_count{0};
        std::atomic<uint64_t> total_latency_ns{0};
        std::chrono::steady_clock::time_point start_time;
    };
    mutable AtomicStatistics stats_;
    
    // Memory management
    std::atomic<size_t> memory_usage_{0};
    static constexpr size_t ENTRY_MEMORY_ESTIMATE = 1024; // bytes per entry
};

/**
 * @brief Async logger factory
 */
class AsyncLoggerFactory {
public:
    /**
     * @brief Create async logger
     * @param name Logger name
     * @param config Async configuration
     * @return Shared pointer to async logger
     */
    static std::shared_ptr<AsyncLogger> create(const std::string& name);
    static std::shared_ptr<AsyncLogger> create(
        const std::string& name, 
        const AsyncLogger::Config& config);

    /**
     * @brief Get existing async logger
     * @param name Logger name
     * @return Shared pointer to logger or nullptr if not found
     */
    static std::shared_ptr<AsyncLogger> get(const std::string& name);

    /**
     * @brief Shutdown all async loggers
     * @param wait_for_flush Wait for flush
     */
    static void shutdown_all(bool wait_for_flush = true);

    /**
     * @brief Get performance statistics for all loggers
     * @return Map of logger names to statistics
     */
    static std::unordered_map<std::string, AsyncLogger::Statistics> get_all_statistics();

private:
    static std::unordered_map<std::string, std::shared_ptr<AsyncLogger>> loggers_;
    static std::mutex loggers_mutex_;
};

/**
 * @brief High-performance log sink for async processing
 */
class AsyncLogSink : public ILogSink {
public:
    /**
     * @brief Constructor
     * @param target_sink Target sink for actual writing
     * @param buffer_size Buffer size for batching
     */
    AsyncLogSink(std::shared_ptr<ILogSink> target_sink, size_t buffer_size = 1000);

    /**
     * @brief Destructor
     */
    ~AsyncLogSink() override;

    /**
     * @brief Write log entry (async)
     * @param entry Log entry
     */
    void write(const LogEntry& entry) override;

    /**
     * @brief Flush buffered logs
     */
    void flush() override;

private:
    void worker_thread();
    void process_buffer();

    std::shared_ptr<ILogSink> target_sink_;
    std::queue<LogEntry> buffer_;
    size_t buffer_size_;
    std::mutex buffer_mutex_;
    std::condition_variable buffer_cv_;
    std::thread worker_;
    std::atomic<bool> running_{true};
    std::atomic<bool> flush_requested_{false};
};

} // namespace omop::common