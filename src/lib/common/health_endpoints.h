/**
 * @file health_endpoints.h
 * @brief Comprehensive health check endpoints with detailed diagnostics
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#pragma once

#include "health_monitor.h"
#include "metrics_collector.h"
#include "http_client.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>

namespace omop::common {

/**
 * @brief Health endpoint response format
 */
enum class HealthResponseFormat {
    JSON,           ///< JSON format
    PROMETHEUS,     ///< Prometheus metrics format
    PLAIN_TEXT,     ///< Plain text format
    HTML            ///< HTML format for browser viewing
};

/**
 * @brief Detailed health check response
 */
struct DetailedHealthResponse {
    HealthStatus overall_status{HealthStatus::UNKNOWN};
    std::string overall_message;
    std::chrono::system_clock::time_point check_time;
    std::chrono::milliseconds total_check_duration{0};
    
    // Individual component results
    std::unordered_map<std::string, HealthCheckResult> component_results;
    
    // System information
    struct SystemInfo {
        std::string hostname;
        std::string version;
        std::chrono::system_clock::time_point startup_time;
        std::chrono::seconds uptime{0};
        size_t memory_usage_mb{0};
        double cpu_usage_percent{0.0};
        size_t disk_usage_mb{0};
        size_t disk_free_mb{0};
        size_t active_connections{0};
        std::unordered_map<std::string, std::string> environment_info;
    } system_info;
    
    // Service dependencies
    struct DependencyStatus {
        std::string name;
        HealthStatus status{HealthStatus::UNKNOWN};
        std::string endpoint;
        std::chrono::milliseconds response_time{0};
        std::string version;
        std::string error_message;
    };
    std::vector<DependencyStatus> dependencies;
    
    // Performance metrics
    struct PerformanceMetrics {
        double requests_per_second{0.0};
        std::chrono::milliseconds avg_response_time{0};
        size_t active_requests{0};
        size_t total_requests{0};
        size_t error_count{0};
        double error_rate{0.0};
        std::unordered_map<std::string, double> custom_metrics;
    } performance_metrics;
    
    // Configuration validation
    struct ConfigurationStatus {
        bool is_valid{true};
        std::vector<std::string> validation_errors;
        std::vector<std::string> validation_warnings;
        std::chrono::system_clock::time_point last_reload_time;
        std::string config_source;
        std::unordered_map<std::string, std::string> key_settings;
    } configuration_status;
    
    // Feature flags and toggles
    std::unordered_map<std::string, bool> feature_flags;
    
    // Custom health data
    std::unordered_map<std::string, std::any> custom_data;
};

/**
 * @brief Health endpoint handler interface
 */
class IHealthEndpointHandler {
public:
    virtual ~IHealthEndpointHandler() = default;
    
    /**
     * @brief Handle health check request
     * @param path Request path
     * @param query_params Query parameters
     * @param headers Request headers
     * @return Health response
     */
    virtual std::string handle_request(
        const std::string& path,
        const std::unordered_map<std::string, std::string>& query_params,
        const std::unordered_map<std::string, std::string>& headers) = 0;
    
    /**
     * @brief Get supported paths
     * @return List of supported endpoint paths
     */
    virtual std::vector<std::string> get_supported_paths() const = 0;
};

/**
 * @brief Default health endpoint handler
 */
class HealthEndpointHandler : public IHealthEndpointHandler {
public:
    /**
     * @brief Configuration for health endpoints
     */
    struct Config {
        bool enabled{true};
        std::string base_path{"/health"};
        bool include_system_info{true};
        bool include_dependencies{true};
        bool include_performance_metrics{true};
        bool include_configuration_status{true};
        bool include_custom_data{true};
        
        // Security settings
        bool require_authentication{false};
        std::string api_key_header{"X-API-Key"};
        std::vector<std::string> allowed_api_keys;
        std::vector<std::string> allowed_ip_ranges;
        
        // Response settings
        HealthResponseFormat default_format{HealthResponseFormat::JSON};
        bool pretty_print{true};
        bool include_detailed_errors{true};
        std::chrono::seconds cache_duration{30};
        
        // Rate limiting
        size_t max_requests_per_minute{100};
        std::chrono::minutes rate_limit_window{1};
    };
    
    /**
     * @brief Constructor
     * @param health_monitor Health monitor instance
     * @param config Endpoint configuration
     */
    HealthEndpointHandler(
        std::shared_ptr<HealthMonitor> health_monitor,
        const Config& config = Config{});
    
    /**
     * @brief Handle health check request
     */
    std::string handle_request(
        const std::string& path,
        const std::unordered_map<std::string, std::string>& query_params,
        const std::unordered_map<std::string, std::string>& headers) override;
    
    /**
     * @brief Get supported paths
     */
    std::vector<std::string> get_supported_paths() const override;
    
    /**
     * @brief Set metrics collector for performance data
     * @param metrics_collector Metrics collector instance
     */
    void set_metrics_collector(std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector);
    
    /**
     * @brief Register dependency health check
     * @param name Dependency name
     * @param check_function Function to check dependency health
     */
    void register_dependency_check(
        const std::string& name,
        std::function<DetailedHealthResponse::DependencyStatus()> check_function);
    
    /**
     * @brief Set custom data provider
     * @param provider Function that returns custom health data
     */
    void set_custom_data_provider(
        std::function<std::unordered_map<std::string, std::any>()> provider);
    
    /**
     * @brief Set feature flag provider
     * @param provider Function that returns feature flag states
     */
    void set_feature_flag_provider(
        std::function<std::unordered_map<std::string, bool>()> provider);

private:
    Config config_;
    std::shared_ptr<HealthMonitor> health_monitor_;
    std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector_;
    
    // Dependency checks
    mutable std::mutex dependency_checks_mutex_;
    std::unordered_map<std::string, std::function<DetailedHealthResponse::DependencyStatus()>> dependency_checks_;
    
    // Custom providers
    std::function<std::unordered_map<std::string, std::any>()> custom_data_provider_;
    std::function<std::unordered_map<std::string, bool>()> feature_flag_provider_;
    
    // Rate limiting
    struct RateLimitState {
        std::queue<std::chrono::system_clock::time_point> request_times;
        mutable std::mutex mutex;
    };
    std::unordered_map<std::string, RateLimitState> rate_limit_states_;
    mutable std::mutex rate_limit_mutex_;
    
    // Response cache
    struct CachedResponse {
        std::string content;
        std::chrono::system_clock::time_point created_time;
        HealthResponseFormat format;
    };
    mutable std::mutex cache_mutex_;
    std::unordered_map<std::string, CachedResponse> response_cache_;
    
    /**
     * @brief Generate detailed health response
     */
    DetailedHealthResponse generate_detailed_response();
    
    /**
     * @brief Check system information
     */
    DetailedHealthResponse::SystemInfo get_system_info();
    
    /**
     * @brief Check all dependencies
     */
    std::vector<DetailedHealthResponse::DependencyStatus> check_dependencies();
    
    /**
     * @brief Get performance metrics
     */
    DetailedHealthResponse::PerformanceMetrics get_performance_metrics();
    
    /**
     * @brief Get configuration status
     */
    DetailedHealthResponse::ConfigurationStatus get_configuration_status();
    
    /**
     * @brief Format response based on requested format
     */
    std::string format_response(
        const DetailedHealthResponse& response,
        HealthResponseFormat format,
        bool pretty_print = true);
    
    /**
     * @brief Format response as JSON
     */
    std::string format_json_response(
        const DetailedHealthResponse& response,
        bool pretty_print = true);
    
    /**
     * @brief Format response as Prometheus metrics
     */
    std::string format_prometheus_response(const DetailedHealthResponse& response);
    
    /**
     * @brief Format response as plain text
     */
    std::string format_plain_text_response(const DetailedHealthResponse& response);
    
    /**
     * @brief Format response as HTML
     */
    std::string format_html_response(const DetailedHealthResponse& response);
    
    /**
     * @brief Check authentication
     */
    bool check_authentication(const std::unordered_map<std::string, std::string>& headers);
    
    /**
     * @brief Check rate limits
     */
    bool check_rate_limits(const std::string& client_ip);
    
    /**
     * @brief Get cached response if available and valid
     */
    std::optional<std::string> get_cached_response(const std::string& cache_key);
    
    /**
     * @brief Cache response
     */
    void cache_response(const std::string& cache_key, const std::string& response, HealthResponseFormat format);
    
    /**
     * @brief Clean expired cache entries
     */
    void cleanup_cache();
    
    /**
     * @brief Extract client IP from headers
     */
    std::string extract_client_ip(const std::unordered_map<std::string, std::string>& headers);
    
    /**
     * @brief Parse query parameters to determine response format
     */
    HealthResponseFormat parse_response_format(const std::unordered_map<std::string, std::string>& query_params);
};

/**
 * @brief Health endpoint HTTP server
 */
class HealthEndpointServer {
public:
    /**
     * @brief Server configuration
     */
    struct Config {
        std::string host{"localhost"};
        int port{8080};
        std::string base_path{"/health"};
        size_t thread_pool_size{4};
        std::chrono::seconds keep_alive_timeout{30};
        size_t max_request_size{1024 * 1024}; // 1MB
        bool enable_cors{true};
        std::vector<std::string> cors_origins{"*"};
        
        // TLS configuration
        bool enable_tls{false};
        std::string tls_cert_path;
        std::string tls_key_path;
        
        // Logging
        bool log_requests{true};
        bool log_responses{false};
    };
    
    /**
     * @brief Constructor
     * @param handler Health endpoint handler
     * @param config Server configuration
     */
    HealthEndpointServer(
        std::shared_ptr<IHealthEndpointHandler> handler,
        const Config& config = Config{});
    
    /**
     * @brief Destructor
     */
    ~HealthEndpointServer();
    
    /**
     * @brief Start HTTP server
     * @return true if server started successfully
     */
    bool start();
    
    /**
     * @brief Stop HTTP server
     */
    void stop();
    
    /**
     * @brief Check if server is running
     * @return true if server is running
     */
    bool is_running() const { return running_.load(); }
    
    /**
     * @brief Get server statistics
     */
    struct Statistics {
        size_t total_requests{0};
        size_t successful_requests{0};
        size_t failed_requests{0};
        std::chrono::milliseconds avg_response_time{0};
        std::chrono::system_clock::time_point start_time;
        std::unordered_map<std::string, size_t> endpoint_request_counts;
    };
    
    Statistics get_statistics() const;

private:
    Config config_;
    std::shared_ptr<IHealthEndpointHandler> handler_;
    std::atomic<bool> running_{false};
    
    // HTTP server implementation (simplified for this example)
    std::unique_ptr<std::thread> server_thread_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    Statistics stats_;
    
    /**
     * @brief Server main loop
     */
    void server_loop();
    
    /**
     * @brief Handle HTTP request
     */
    std::string handle_http_request(const std::string& request);
    
    /**
     * @brief Parse HTTP request
     */
    struct HttpRequest {
        std::string method;
        std::string path;
        std::unordered_map<std::string, std::string> headers;
        std::unordered_map<std::string, std::string> query_params;
        std::string body;
    };
    
    HttpRequest parse_request(const std::string& request);
    
    /**
     * @brief Generate HTTP response
     */
    std::string generate_response(int status_code, const std::string& content,
                                 const std::unordered_map<std::string, std::string>& headers = {});
};

/**
 * @brief Factory for creating health endpoint configurations
 */
class HealthEndpointsFactory {
public:
    /**
     * @brief Create production-ready health endpoints
     * @param health_monitor Health monitor instance
     * @param port HTTP server port
     * @return Configured health endpoint server
     */
    static std::unique_ptr<HealthEndpointServer> create_production_endpoints(
        std::shared_ptr<HealthMonitor> health_monitor,
        int port = 8080);
    
    /**
     * @brief Create development health endpoints (with detailed debugging)
     * @param health_monitor Health monitor instance
     * @param port HTTP server port
     * @return Configured health endpoint server
     */
    static std::unique_ptr<HealthEndpointServer> create_development_endpoints(
        std::shared_ptr<HealthMonitor> health_monitor,
        int port = 8080);
    
    /**
     * @brief Create Kubernetes-compatible health endpoints
     * Includes /health/live, /health/ready, and /health/startup endpoints
     * @param health_monitor Health monitor instance
     * @param port HTTP server port
     * @return Configured health endpoint server
     */
    static std::unique_ptr<HealthEndpointServer> create_kubernetes_endpoints(
        std::shared_ptr<HealthMonitor> health_monitor,
        int port = 8080);
};

} // namespace omop::common