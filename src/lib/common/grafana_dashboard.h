/**
 * @file grafana_dashboard.h
 * @brief Grafana dashboard configuration and management for OMOP ETL monitoring
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <chrono>

namespace omop::common {

/**
 * @brief Grafana panel types
 */
enum class GrafanaPanelType {
    GRAPH,              ///< Time series graph
    SINGLESTAT,         ///< Single value display
    TABLE,              ///< Data table
    HEATMAP,            ///< Heatmap visualization
    GAUGE,              ///< Gauge display
    BAR_GAUGE,          ///< Bar gauge
    STAT,               ///< Modern stat panel
    TIMESERIES,         ///< Time series panel (Grafana 7+)
    LOGS,               ///< Log panel
    TEXT,               ///< Text/markdown panel
    ALERT_LIST          ///< Alert list panel
};

/**
 * @brief Grafana data source types
 */
enum class GrafanaDataSourceType {
    PROMETHEUS,         ///< Prometheus data source
    INFLUXDB,           ///< InfluxDB data source
    ELASTICSEARCH,      ///< Elasticsearch data source
    LOKI,               ///< Loki data source
    JAEGER,             ///< Jaeger data source
    ZIPKIN              ///< Zipkin data source
};

/**
 * @brief Grafana panel configuration
 */
struct GrafanaPanel {
    int id;
    std::string title;
    GrafanaPanelType type;
    std::string description;
    
    // Layout
    int x{0};
    int y{0};
    int width{12};
    int height{8};
    
    // Data source and queries
    std::string data_source;
    std::vector<std::string> queries;
    
    // Panel-specific configuration
    std::unordered_map<std::string, std::string> options;
    std::unordered_map<std::string, std::string> field_config;
    
    // Thresholds and alerts
    struct Threshold {
        double value;
        std::string color;
        std::string operator_type{"gt"}; // gt, lt, within_range, outside_range
    };
    std::vector<Threshold> thresholds;
    
    // Time range override
    std::string time_from;
    std::string time_to;
    
    /**
     * @brief Convert panel to Grafana JSON format
     * @return JSON representation of panel
     */
    std::string to_json() const;
};

/**
 * @brief Grafana dashboard row for grouping panels
 */
struct GrafanaDashboardRow {
    int id;
    std::string title;
    bool collapsed{false};
    int y{0};
    std::vector<GrafanaPanel> panels;
    
    /**
     * @brief Convert row to Grafana JSON format
     * @return JSON representation of row
     */
    std::string to_json() const;
};

/**
 * @brief Grafana dashboard template variable
 */
struct GrafanaTemplateVariable {
    std::string name;
    std::string type{"query"}; // query, custom, constant, interval, datasource
    std::string label;
    std::string description;
    std::string query;
    std::string data_source;
    bool multi{false};
    bool include_all{false};
    std::string current_value;
    std::vector<std::string> options;
    
    /**
     * @brief Convert variable to Grafana JSON format
     * @return JSON representation of variable
     */
    std::string to_json() const;
};

/**
 * @brief Grafana dashboard annotation
 */
struct GrafanaDashboardAnnotation {
    std::string name;
    std::string data_source;
    std::string query;
    bool enable{true};
    bool hide{false};
    std::string icon_color;
    std::string text_field;
    std::string title_field;
    std::string tags_field;
    
    /**
     * @brief Convert annotation to Grafana JSON format
     * @return JSON representation of annotation
     */
    std::string to_json() const;
};

/**
 * @brief Complete Grafana dashboard configuration
 */
class GrafanaDashboard {
public:
    /**
     * @brief Dashboard metadata
     */
    struct Metadata {
        int id{0};
        std::string uid;
        std::string title;
        std::string description;
        std::vector<std::string> tags;
        std::string timezone{"UTC"};
        std::chrono::minutes refresh_interval{5};
        std::string time_from{"1h"};
        std::string time_to{"now"};
        int schema_version{30};
        int version{1};
        bool editable{true};
        bool graph_tooltip{1}; // 0=None, 1=Shared crosshair, 2=Shared tooltip
    };
    
    /**
     * @brief Constructor
     * @param metadata Dashboard metadata
     */
    explicit GrafanaDashboard(const Metadata& metadata = Metadata{});
    
    /**
     * @brief Add panel to dashboard
     * @param panel Panel configuration
     * @param row_id Optional row ID to add panel to specific row
     */
    void add_panel(const GrafanaPanel& panel, int row_id = -1);
    
    /**
     * @brief Add row to dashboard
     * @param row Row configuration
     */
    void add_row(const GrafanaDashboardRow& row);
    
    /**
     * @brief Add template variable
     * @param variable Template variable configuration
     */
    void add_template_variable(const GrafanaTemplateVariable& variable);
    
    /**
     * @brief Add annotation
     * @param annotation Annotation configuration
     */
    void add_annotation(const GrafanaDashboardAnnotation& annotation);
    
    /**
     * @brief Set data source for all panels
     * @param data_source_name Data source name
     */
    void set_default_data_source(const std::string& data_source_name);
    
    /**
     * @brief Generate complete dashboard JSON
     * @param pretty_print Whether to format JSON with indentation
     * @return Complete Grafana dashboard JSON
     */
    std::string to_json(bool pretty_print = true) const;
    
    /**
     * @brief Save dashboard to file
     * @param filename Output filename
     * @param pretty_print Whether to format JSON with indentation
     * @return true if saved successfully
     */
    bool save_to_file(const std::string& filename, bool pretty_print = true) const;
    
    /**
     * @brief Get dashboard metadata
     * @return Dashboard metadata
     */
    const Metadata& get_metadata() const { return metadata_; }
    
    /**
     * @brief Update dashboard metadata
     * @param metadata New metadata
     */
    void set_metadata(const Metadata& metadata) { metadata_ = metadata; }
    
    /**
     * @brief Get all panels
     * @return Vector of panels
     */
    const std::vector<GrafanaPanel>& get_panels() const { return panels_; }
    
    /**
     * @brief Get all rows
     * @return Vector of rows
     */
    const std::vector<GrafanaDashboardRow>& get_rows() const { return rows_; }

private:
    Metadata metadata_;
    std::vector<GrafanaPanel> panels_;
    std::vector<GrafanaDashboardRow> rows_;
    std::vector<GrafanaTemplateVariable> template_variables_;
    std::vector<GrafanaDashboardAnnotation> annotations_;
    std::string default_data_source_;
    
    /**
     * @brief Generate next panel ID
     * @return Unique panel ID
     */
    int generate_panel_id() const;
    
    /**
     * @brief Auto-layout panels if positions not specified
     */
    void auto_layout_panels();
};

/**
 * @brief Pre-configured dashboard templates for common monitoring scenarios
 */
class GrafanaDashboardTemplates {
public:
    /**
     * @brief Create system monitoring dashboard
     * @param prometheus_data_source Prometheus data source name
     * @return Pre-configured system dashboard
     */
    static std::unique_ptr<GrafanaDashboard> create_system_monitoring_dashboard(
        const std::string& prometheus_data_source = "Prometheus");
    
    /**
     * @brief Create application monitoring dashboard
     * @param prometheus_data_source Prometheus data source name
     * @param application_name Application name for filtering
     * @return Pre-configured application dashboard
     */
    static std::unique_ptr<GrafanaDashboard> create_application_monitoring_dashboard(
        const std::string& prometheus_data_source = "Prometheus",
        const std::string& application_name = "omop-etl");
    
    /**
     * @brief Create health monitoring dashboard
     * @param prometheus_data_source Prometheus data source name
     * @return Pre-configured health dashboard
     */
    static std::unique_ptr<GrafanaDashboard> create_health_monitoring_dashboard(
        const std::string& prometheus_data_source = "Prometheus");
    
    /**
     * @brief Create ETL pipeline monitoring dashboard
     * @param prometheus_data_source Prometheus data source name
     * @return Pre-configured ETL pipeline dashboard
     */
    static std::unique_ptr<GrafanaDashboard> create_etl_pipeline_dashboard(
        const std::string& prometheus_data_source = "Prometheus");
    
    /**
     * @brief Create distributed tracing dashboard
     * @param jaeger_data_source Jaeger data source name
     * @param zipkin_data_source Zipkin data source name (optional)
     * @return Pre-configured tracing dashboard
     */
    static std::unique_ptr<GrafanaDashboard> create_distributed_tracing_dashboard(
        const std::string& jaeger_data_source = "Jaeger",
        const std::string& zipkin_data_source = "");
    
    /**
     * @brief Create alerting overview dashboard
     * @param prometheus_data_source Prometheus data source name
     * @return Pre-configured alerting dashboard
     */
    static std::unique_ptr<GrafanaDashboard> create_alerting_overview_dashboard(
        const std::string& prometheus_data_source = "Prometheus");
    
    /**
     * @brief Create comprehensive monitoring dashboard
     * Includes system, application, health, and ETL monitoring in one dashboard
     * @param prometheus_data_source Prometheus data source name
     * @param jaeger_data_source Jaeger data source name
     * @return Comprehensive monitoring dashboard
     */
    static std::unique_ptr<GrafanaDashboard> create_comprehensive_monitoring_dashboard(
        const std::string& prometheus_data_source = "Prometheus",
        const std::string& jaeger_data_source = "Jaeger");

private:
    /**
     * @brief Create common template variables for OMOP ETL dashboards
     * @return Vector of template variables
     */
    static std::vector<GrafanaTemplateVariable> create_common_template_variables();
    
    /**
     * @brief Create common annotations for OMOP ETL dashboards
     * @param prometheus_data_source Prometheus data source name
     * @return Vector of annotations
     */
    static std::vector<GrafanaDashboardAnnotation> create_common_annotations(
        const std::string& prometheus_data_source);
};

/**
 * @brief Grafana API client for dashboard management
 */
class GrafanaApiClient {
public:
    /**
     * @brief Configuration for Grafana API client
     */
    struct Config {
        std::string base_url{"http://localhost:3000"};
        std::string username{"admin"};
        std::string password{"admin"};
        std::string api_token; // Alternative to username/password
        std::chrono::milliseconds timeout{30000};
        bool verify_ssl{true};
    };
    
    /**
     * @brief Constructor
     * @param config Grafana API configuration
     */
    explicit GrafanaApiClient(const Config& config);
    
    /**
     * @brief Test connection to Grafana API
     * @return true if connection successful
     */
    bool test_connection();
    
    /**
     * @brief Create or update dashboard
     * @param dashboard Dashboard to create/update
     * @param overwrite Whether to overwrite existing dashboard
     * @return true if operation successful
     */
    bool create_or_update_dashboard(const GrafanaDashboard& dashboard, bool overwrite = true);
    
    /**
     * @brief Delete dashboard by UID
     * @param uid Dashboard UID
     * @return true if deletion successful
     */
    bool delete_dashboard(const std::string& uid);
    
    /**
     * @brief Get dashboard by UID
     * @param uid Dashboard UID
     * @return Dashboard JSON or empty string if not found
     */
    std::string get_dashboard(const std::string& uid);
    
    /**
     * @brief List all dashboards
     * @return Vector of dashboard metadata (title, uid, etc.)
     */
    std::vector<std::unordered_map<std::string, std::string>> list_dashboards();
    
    /**
     * @brief Create data source
     * @param name Data source name
     * @param type Data source type
     * @param url Data source URL
     * @param additional_config Additional configuration parameters
     * @return true if creation successful
     */
    bool create_data_source(const std::string& name, GrafanaDataSourceType type,
                           const std::string& url,
                           const std::unordered_map<std::string, std::string>& additional_config = {});
    
    /**
     * @brief List data sources
     * @return Vector of data source information
     */
    std::vector<std::unordered_map<std::string, std::string>> list_data_sources();

private:
    Config config_;
    
    /**
     * @brief Make HTTP request to Grafana API
     * @param method HTTP method (GET, POST, PUT, DELETE)
     * @param endpoint API endpoint path
     * @param payload Request payload (for POST/PUT)
     * @param additional_headers Additional HTTP headers
     * @return Response body and status code
     */
    std::pair<std::string, int> make_request(const std::string& method, 
                                            const std::string& endpoint,
                                            const std::string& payload = "",
                                            const std::unordered_map<std::string, std::string>& additional_headers = {});
    
    /**
     * @brief Get authentication headers
     * @return Authentication headers for requests
     */
    std::unordered_map<std::string, std::string> get_auth_headers();
    
    /**
     * @brief Convert data source type to string
     * @param type Data source type enum
     * @return String representation
     */
    std::string data_source_type_to_string(GrafanaDataSourceType type);
};

/**
 * @brief Grafana dashboard manager for automated setup
 */
class GrafanaDashboardManager {
public:
    /**
     * @brief Constructor
     * @param api_client Grafana API client
     */
    explicit GrafanaDashboardManager(std::shared_ptr<GrafanaApiClient> api_client);
    
    /**
     * @brief Setup complete monitoring stack
     * Creates data sources and deploys all monitoring dashboards
     * @param prometheus_url Prometheus server URL
     * @param jaeger_url Jaeger server URL (optional)
     * @param overwrite Whether to overwrite existing dashboards
     * @return true if setup successful
     */
    bool setup_monitoring_stack(const std::string& prometheus_url,
                               const std::string& jaeger_url = "",
                               bool overwrite = true);
    
    /**
     * @brief Deploy individual dashboard template
     * @param template_name Template name (system, application, health, etc.)
     * @param prometheus_data_source Prometheus data source name
     * @param jaeger_data_source Jaeger data source name (optional)
     * @param overwrite Whether to overwrite existing dashboard
     * @return true if deployment successful
     */
    bool deploy_dashboard_template(const std::string& template_name,
                                  const std::string& prometheus_data_source = "Prometheus",
                                  const std::string& jaeger_data_source = "Jaeger",
                                  bool overwrite = true);
    
    /**
     * @brief Cleanup all OMOP ETL dashboards
     * @return Number of dashboards removed
     */
    size_t cleanup_dashboards();
    
    /**
     * @brief Get deployment status
     * @return Map of dashboard name to deployment status
     */
    std::unordered_map<std::string, bool> get_deployment_status();

private:
    std::shared_ptr<GrafanaApiClient> api_client_;
    
    // Track deployed dashboards
    std::vector<std::string> deployed_dashboard_uids_;
    
    /**
     * @brief Generate unique dashboard UID
     * @param template_name Template name
     * @return Unique UID
     */
    std::string generate_dashboard_uid(const std::string& template_name);
};

} // namespace omop::common