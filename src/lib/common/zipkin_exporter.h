/**
 * @file zipkin_exporter.h
 * @brief Zipkin span exporter for distributed tracing integration
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#pragma once

#include "tracing_manager.h"
#include <memory>
#include <string>
#include <vector>
#include <chrono>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>

namespace omop::common {

/**
 * @brief Zipkin span exporter implementation
 */
class ZipkinSpanExporter : public ISpanExporter {
public:
    /**
     * @brief Zipkin exporter configuration
     */
    struct Config {
        std::string endpoint{"http://localhost:9411/api/v2/spans"};
        std::chrono::milliseconds timeout{10000};
        std::chrono::milliseconds batch_timeout{5000};
        size_t max_batch_size{100};
        std::string service_name{"omop-etl"};
        std::string service_version{"1.0.0"};
        std::unordered_map<std::string, std::string> default_tags;
        
        // HTTP configuration
        std::string user_agent{"omop-etl-zipkin-exporter/1.0"};
        std::unordered_map<std::string, std::string> headers;
        bool compress{true};
        
        // Retry configuration
        int max_retries{3};
        std::chrono::milliseconds retry_delay{1000};
        double retry_backoff_multiplier{2.0};
        
        // Resource limits
        size_t max_queue_size{2048};
        size_t max_memory_usage_mb{100};
    };

    /**
     * @brief Constructor
     * @param config Zipkin exporter configuration
     */
    explicit ZipkinSpanExporter(const Config& config = Config{});
    
    /**
     * @brief Destructor
     */
    ~ZipkinSpanExporter() override;
    
    /**
     * @brief Export spans to Zipkin
     * @param spans Spans to export
     * @return true if export successful
     */
    bool export_spans(const std::vector<std::unique_ptr<ISpan>>& spans) override;
    
    /**
     * @brief Shutdown exporter
     */
    void shutdown() override;
    
    /**
     * @brief Get export statistics
     */
    struct Statistics {
        std::atomic<size_t> spans_exported{0};
        std::atomic<size_t> export_requests{0};
        std::atomic<size_t> export_failures{0};
        std::atomic<size_t> network_errors{0};
        std::atomic<size_t> serialization_errors{0};
        std::atomic<size_t> queued_spans{0};
        std::atomic<size_t> dropped_spans{0};
        std::chrono::system_clock::time_point last_export_time;
        std::chrono::system_clock::time_point last_error_time;
        std::string last_error_message;
    };
    
    const Statistics& get_statistics() const { return stats_; }
    
    /**
     * @brief Test connectivity to Zipkin endpoint
     * @return true if endpoint is reachable
     */
    bool test_connectivity();

private:
    Config config_;
    mutable Statistics stats_;
    
    // Async export management
    std::atomic<bool> shutdown_requested_{false};
    std::unique_ptr<std::thread> export_thread_;
    std::queue<std::vector<std::unique_ptr<ISpan>>> span_queue_;
    mutable std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    // HTTP client for sending spans
    class HttpClient;
    std::unique_ptr<HttpClient> http_client_;
    
    /**
     * @brief Export thread main loop
     */
    void export_loop();
    
    /**
     * @brief Convert spans to Zipkin JSON format
     * @param spans Spans to convert
     * @return JSON string representation
     */
    std::string serialize_spans(const std::vector<std::unique_ptr<ISpan>>& spans);
    
    /**
     * @brief Send HTTP request to Zipkin endpoint
     * @param json_data JSON payload
     * @return true if request successful
     */
    bool send_to_zipkin(const std::string& json_data);
    
    /**
     * @brief Convert span to Zipkin JSON object
     * @param span Span to convert
     * @return JSON object string
     */
    std::string span_to_zipkin_json(const ISpan& span);
    
    /**
     * @brief Generate Zipkin trace ID format
     * @param trace_id OpenTelemetry trace ID
     * @return Zipkin-formatted trace ID
     */
    std::string format_zipkin_trace_id(const std::string& trace_id);
    
    /**
     * @brief Generate Zipkin span ID format
     * @param span_id OpenTelemetry span ID
     * @return Zipkin-formatted span ID
     */
    std::string format_zipkin_span_id(const std::string& span_id);
    
    /**
     * @brief Convert span kind to Zipkin kind
     * @param kind OpenTelemetry span kind
     * @return Zipkin span kind string
     */
    std::string span_kind_to_zipkin(SpanKind kind);
    
    /**
     * @brief Get current timestamp in microseconds
     * @return Timestamp in microseconds since epoch
     */
    int64_t get_timestamp_microseconds();
    
    /**
     * @brief Validate configuration
     * @return true if configuration is valid
     */
    bool validate_config();
};

/**
 * @brief Create Zipkin span exporter
 * @param config Zipkin exporter configuration
 * @return Unique pointer to Zipkin exporter
 */
std::unique_ptr<ZipkinSpanExporter> create_zipkin_exporter(
    const ZipkinSpanExporter::Config& config = ZipkinSpanExporter::Config{});

/**
 * @brief Zipkin exporter factory
 */
class ZipkinExporterFactory {
public:
    /**
     * @brief Create exporter from configuration map
     * @param config Configuration parameters
     * @return Unique pointer to exporter
     */
    static std::unique_ptr<ZipkinSpanExporter> create_from_config(
        const std::unordered_map<std::string, std::string>& config);
    
    /**
     * @brief Create exporter from environment variables
     * Environment variables:
     * - ZIPKIN_ENDPOINT: Zipkin endpoint URL
     * - ZIPKIN_SERVICE_NAME: Service name
     * - ZIPKIN_SERVICE_VERSION: Service version
     * - ZIPKIN_TIMEOUT_MS: Request timeout in milliseconds
     * - ZIPKIN_BATCH_SIZE: Maximum batch size
     * @return Unique pointer to exporter
     */
    static std::unique_ptr<ZipkinSpanExporter> create_from_environment();
    
    /**
     * @brief Get default configuration for common Zipkin deployments
     * @param deployment_type Deployment type (local, docker, kubernetes, etc.)
     * @return Configuration for deployment type
     */
    static ZipkinSpanExporter::Config get_default_config(const std::string& deployment_type = "local");
};

} // namespace omop::common