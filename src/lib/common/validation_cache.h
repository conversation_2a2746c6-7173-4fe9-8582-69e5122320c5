/**
 * @file validation_cache.h
 * @brief High-performance validation result caching system
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#pragma once

#include "validation.h"
#include <string>
#include <memory>
#include <chrono>
#include <unordered_map>
#include <atomic>
#include <mutex>
#include <optional>
#include <thread>

namespace omop::common {

/**
 * @brief Cache key for validation results
 */
struct ValidationCacheKey {
    std::string rule_name;
    std::string data_hash;
    std::string context_hash;
    
    bool operator==(const ValidationCacheKey& other) const {
        return rule_name == other.rule_name &&
               data_hash == other.data_hash &&
               context_hash == other.context_hash;
    }
};

/**
 * @brief Hash function for validation cache keys
 */
struct ValidationCacheKeyHash {
    size_t operator()(const ValidationCacheKey& key) const {
        return std::hash<std::string>{}(key.rule_name) ^
               (std::hash<std::string>{}(key.data_hash) << 1) ^
               (std::hash<std::string>{}(key.context_hash) << 2);
    }
};

/**
 * @brief Cached validation result entry
 */
struct CachedValidationResult {
    ValidationResult result;
    std::chrono::system_clock::time_point created_at;
    std::chrono::system_clock::time_point last_accessed;
    std::atomic<size_t> access_count{0};
    size_t memory_size = 0;
    
    bool is_expired(std::chrono::seconds ttl) const {
        auto now = std::chrono::system_clock::now();
        return (now - created_at) > ttl;
    }
    
    void update_access() {
        last_accessed = std::chrono::system_clock::now();
        access_count.fetch_add(1, std::memory_order_relaxed);
    }
};

/**
 * @brief High-performance validation result cache
 * 
 * Features:
 * - LRU eviction policy
 * - TTL-based expiration
 * - Thread-safe operations
 * - Memory usage tracking
 * - Hit/miss statistics
 * - Automatic cleanup
 */
class ValidationCache {
public:
    /**
     * @brief Cache configuration
     */
    struct Config {
        size_t max_entries = 10000;                    // Maximum cache entries
        size_t max_memory_mb = 100;                    // Maximum memory usage
        std::chrono::seconds default_ttl{3600};        // Default TTL (1 hour)
        std::chrono::seconds cleanup_interval{300};    // Cleanup interval (5 minutes)
        bool enable_statistics = true;                 // Enable statistics collection
        bool enable_persistence = false;               // Enable cache persistence
        std::string persistence_path;                  // Path for cache persistence
    };

    /**
     * @brief Constructor
     * @param config Cache configuration
     */
    explicit ValidationCache(const Config& config = {});

    /**
     * @brief Destructor
     */
    ~ValidationCache();

    /**
     * @brief Put validation result in cache
     * @param key Cache key
     * @param result Validation result
     * @param ttl Time to live (optional, uses default if not specified)
     */
    void put(const ValidationCacheKey& key, 
             const ValidationResult& result,
             std::optional<std::chrono::seconds> ttl = std::nullopt);

    /**
     * @brief Get validation result from cache
     * @param key Cache key
     * @return Optional cached result
     */
    std::optional<ValidationResult> get(const ValidationCacheKey& key);

    /**
     * @brief Check if key exists in cache
     * @param key Cache key
     * @return true if key exists and not expired
     */
    bool contains(const ValidationCacheKey& key) const;

    /**
     * @brief Remove entry from cache
     * @param key Cache key
     * @return true if entry was removed
     */
    bool remove(const ValidationCacheKey& key);

    /**
     * @brief Clear all cache entries
     */
    void clear();

    /**
     * @brief Force cleanup of expired entries
     * @return Number of entries removed
     */
    size_t cleanup_expired();

    /**
     * @brief Get cache statistics
     */
    struct Statistics {
        std::atomic<size_t> total_gets{0};
        std::atomic<size_t> cache_hits{0};
        std::atomic<size_t> cache_misses{0};
        std::atomic<size_t> total_puts{0};
        std::atomic<size_t> evictions{0};
        std::atomic<size_t> expirations{0};
        size_t current_entries = 0;
        size_t memory_usage_bytes = 0;
        std::chrono::system_clock::time_point created_at;
        
        double hit_rate() const {
            size_t total = total_gets.load();
            return total > 0 ? static_cast<double>(cache_hits.load()) / total : 0.0;
        }
        
        double miss_rate() const {
            return 1.0 - hit_rate();
        }
    };

    Statistics get_statistics() const;

    /**
     * @brief Reset statistics
     */
    void reset_statistics();

    /**
     * @brief Save cache to disk
     * @param path File path for cache data
     * @return true if successful
     */
    bool save_to_disk(const std::string& path) const;

    /**
     * @brief Load cache from disk
     * @param path File path for cache data
     * @return true if successful
     */
    bool load_from_disk(const std::string& path);

    /**
     * @brief Get cache configuration
     * @return Current configuration
     */
    const Config& get_config() const { return config_; }

    /**
     * @brief Update cache configuration
     * @param new_config New configuration
     */
    void update_config(const Config& new_config);

private:
    /**
     * @brief LRU list node
     */
    struct LRUNode {
        ValidationCacheKey key;
        std::shared_ptr<CachedValidationResult> value;
        std::shared_ptr<LRUNode> prev;
        std::shared_ptr<LRUNode> next;
        
        LRUNode(const ValidationCacheKey& k, std::shared_ptr<CachedValidationResult> v)
            : key(k), value(v) {}
    };

    /**
     * @brief Evict least recently used entry
     */
    void evict_lru();

    /**
     * @brief Move node to head of LRU list
     * @param node Node to move
     */
    void move_to_head(std::shared_ptr<LRUNode> node);

    /**
     * @brief Add node to head of LRU list
     * @param node Node to add
     */
    void add_to_head(std::shared_ptr<LRUNode> node);

    /**
     * @brief Remove node from LRU list
     * @param node Node to remove
     */
    void remove_node(std::shared_ptr<LRUNode> node);

    /**
     * @brief Calculate memory size of validation result
     * @param result Validation result
     * @return Memory size in bytes
     */
    size_t calculate_memory_size(const ValidationResult& result) const;

    /**
     * @brief Background cleanup thread function
     */
    void cleanup_thread();

    Config config_;
    
    // Cache storage
    std::unordered_map<ValidationCacheKey, std::shared_ptr<LRUNode>, ValidationCacheKeyHash> cache_map_;
    
    // LRU list
    std::shared_ptr<LRUNode> lru_head_;
    std::shared_ptr<LRUNode> lru_tail_;
    
    // Thread safety
    mutable std::shared_mutex cache_mutex_;
    
    // Statistics
    mutable Statistics stats_;
    
    // Background cleanup
    std::thread cleanup_thread_;
    std::atomic<bool> cleanup_running_{false};
    
    // Memory tracking
    std::atomic<size_t> total_memory_usage_{0};
};

/**
 * @brief Smart validation cache that automatically generates keys
 */
class SmartValidationCache {
public:
    /**
     * @brief Constructor
     * @param cache Underlying cache instance
     */
    explicit SmartValidationCache(std::shared_ptr<ValidationCache> cache)
        : cache_(cache) {}

    /**
     * @brief Validate with caching
     * @tparam T Data type
     * @param rule_name Validation rule name
     * @param data Data to validate
     * @param validator Validation function
     * @param context Optional validation context
     * @return Validation result (cached or computed)
     */
    template<typename T>
    ValidationResult validate_with_cache(
        const std::string& rule_name,
        const T& data,
        std::function<ValidationResult(const T&)> validator,
        const std::string& context = "");

    /**
     * @brief Validate batch with caching
     * @tparam T Data type
     * @param rule_name Validation rule name
     * @param data_batch Batch of data to validate
     * @param validator Batch validation function
     * @param context Optional validation context
     * @return Vector of validation results
     */
    template<typename T>
    std::vector<ValidationResult> validate_batch_with_cache(
        const std::string& rule_name,
        const std::vector<T>& data_batch,
        std::function<std::vector<ValidationResult>(const std::vector<T>&)> validator,
        const std::string& context = "");

    /**
     * @brief Precompute validation results for common patterns
     * @param rule_name Rule name
     * @param patterns Common data patterns
     * @param validator Validation function
     * @param context Validation context
     */
    template<typename T>
    void precompute_validations(
        const std::string& rule_name,
        const std::vector<T>& patterns,
        std::function<ValidationResult(const T&)> validator,
        const std::string& context = "");

    /**
     * @brief Get underlying cache
     * @return Validation cache instance
     */
    std::shared_ptr<ValidationCache> get_cache() const { return cache_; }

private:
    /**
     * @brief Generate hash for data
     * @tparam T Data type
     * @param data Data to hash
     * @return Hash string
     */
    template<typename T>
    std::string generate_data_hash(const T& data) const;

    std::shared_ptr<ValidationCache> cache_;
};

/**
 * @brief Global validation cache manager
 */
class ValidationCacheManager {
public:
    /**
     * @brief Get global cache instance
     * @return Shared validation cache
     */
    static std::shared_ptr<ValidationCache> get_global_cache();

    /**
     * @brief Create named cache instance
     * @param name Cache name
     * @param config Cache configuration
     * @return Named validation cache
     */
    static std::shared_ptr<ValidationCache> create_cache(
        const std::string& name,
        const ValidationCache::Config& config = {});

    /**
     * @brief Get named cache instance
     * @param name Cache name
     * @return Named validation cache or nullptr if not found
     */
    static std::shared_ptr<ValidationCache> get_cache(const std::string& name);

    /**
     * @brief Remove named cache
     * @param name Cache name
     * @return true if cache was removed
     */
    static bool remove_cache(const std::string& name);

    /**
     * @brief Get all cache statistics
     * @return Map of cache names to statistics
     */
    static std::unordered_map<std::string, ValidationCache::Statistics> get_all_statistics();

    /**
     * @brief Shutdown all caches
     */
    static void shutdown_all();

private:
    static std::unordered_map<std::string, std::shared_ptr<ValidationCache>> caches_;
    static std::mutex caches_mutex_;
    static std::shared_ptr<ValidationCache> global_cache_;
};

} // namespace omop::common