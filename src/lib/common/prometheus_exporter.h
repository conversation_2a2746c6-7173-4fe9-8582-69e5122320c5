/**
 * @file prometheus_exporter.h
 * @brief Advanced Prometheus metrics exporter with comprehensive system monitoring
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#pragma once

#include "metrics_collector.h"
#include "health_monitor.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <thread>
#include <atomic>
#include <chrono>
#include <functional>

namespace omop::common {

/**
 * @brief Prometheus metric family for grouping related metrics
 */
struct PrometheusMetricFamily {
    std::string name;
    std::string help;
    std::string type; // counter, gauge, histogram, summary
    std::vector<std::pair<std::unordered_map<std::string, std::string>, double>> samples;
    
    /**
     * @brief Add sample to metric family
     * @param labels Label key-value pairs
     * @param value Metric value
     */
    void add_sample(const std::unordered_map<std::string, std::string>& labels, double value);
    
    /**
     * @brief Format metric family for Prometheus exposition format
     * @return Prometheus-formatted string
     */
    std::string format() const;
};

/**
 * @brief System metrics collector for OS-level monitoring
 */
class SystemMetricsCollector {
public:
    /**
     * @brief Configuration for system metrics collection
     */
    struct Config {
        bool collect_cpu_metrics{true};
        bool collect_memory_metrics{true};
        bool collect_disk_metrics{true};
        bool collect_network_metrics{true};
        bool collect_process_metrics{true};
        std::chrono::seconds collection_interval{10};
        std::string metrics_prefix{"system"};
    };
    
    /**
     * @brief Constructor
     * @param config System metrics configuration
     */
    explicit SystemMetricsCollector(const Config& config = Config{});
    
    /**
     * @brief Start system metrics collection
     */
    void start();
    
    /**
     * @brief Stop system metrics collection
     */
    void stop();
    
    /**
     * @brief Get collected system metrics
     * @return Vector of metric families
     */
    std::vector<PrometheusMetricFamily> get_metrics() const;
    
    /**
     * @brief Set metrics collector for publishing
     * @param metrics_collector Metrics collector instance
     */
    void set_metrics_collector(std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector);

private:
    Config config_;
    std::atomic<bool> running_{false};
    std::unique_ptr<std::thread> collection_thread_;
    
    // Metrics storage
    mutable std::mutex metrics_mutex_;
    std::vector<PrometheusMetricFamily> system_metrics_;
    
    // Metrics collector for publishing
    std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector_;
    
    /**
     * @brief Collection thread main loop
     */
    void collection_loop();
    
    /**
     * @brief Collect CPU metrics
     */
    void collect_cpu_metrics();
    
    /**
     * @brief Collect memory metrics
     */
    void collect_memory_metrics();
    
    /**
     * @brief Collect disk metrics
     */
    void collect_disk_metrics();
    
    /**
     * @brief Collect network metrics
     */
    void collect_network_metrics();
    
    /**
     * @brief Collect process-specific metrics
     */
    void collect_process_metrics();
    
    // Platform-specific implementation helpers
    double get_cpu_usage_percent() const;
    std::pair<size_t, size_t> get_memory_info() const; // used, total
    std::vector<std::tuple<std::string, size_t, size_t, double>> get_disk_info() const; // path, used, total, usage%
    std::pair<size_t, size_t> get_network_stats() const; // bytes_in, bytes_out
    std::tuple<size_t, size_t, size_t, double> get_process_info() const; // memory, threads, fd_count, cpu%
};

/**
 * @brief Application-specific metrics collector
 */
class ApplicationMetricsCollector {
public:
    /**
     * @brief Configuration for application metrics
     */
    struct Config {
        std::string application_name{"omop-etl"};
        std::string version{"1.0.0"};
        std::string instance_id;
        std::chrono::seconds collection_interval{5};
        std::string metrics_prefix{"app"};
        
        // Feature flags
        bool collect_request_metrics{true};
        bool collect_error_metrics{true};
        bool collect_performance_metrics{true};
        bool collect_business_metrics{true};
    };
    
    /**
     * @brief Constructor
     * @param config Application metrics configuration
     */
    explicit ApplicationMetricsCollector(const Config& config = Config{});
    
    /**
     * @brief Start application metrics collection
     */
    void start();
    
    /**
     * @brief Stop application metrics collection
     */
    void stop();
    
    /**
     * @brief Record request metrics
     * @param method HTTP method or operation type
     * @param endpoint Endpoint or operation name
     * @param status_code Response status code
     * @param duration_ms Request duration in milliseconds
     */
    void record_request(const std::string& method, const std::string& endpoint, 
                       int status_code, double duration_ms);
    
    /**
     * @brief Record error occurrence
     * @param component Component name
     * @param error_type Error classification
     * @param severity Error severity level
     */
    void record_error(const std::string& component, const std::string& error_type, 
                     const std::string& severity);
    
    /**
     * @brief Record business metric
     * @param metric_name Business metric name
     * @param value Metric value
     * @param labels Optional labels
     */
    void record_business_metric(const std::string& metric_name, double value,
                               const std::unordered_map<std::string, std::string>& labels = {});
    
    /**
     * @brief Get collected application metrics
     * @return Vector of metric families
     */
    std::vector<PrometheusMetricFamily> get_metrics() const;
    
    /**
     * @brief Set metrics collector for publishing
     * @param metrics_collector Metrics collector instance
     */
    void set_metrics_collector(std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector);

private:
    Config config_;
    std::atomic<bool> running_{false};
    
    // Metrics storage
    mutable std::mutex metrics_mutex_;
    std::vector<PrometheusMetricFamily> app_metrics_;
    
    // Request tracking
    struct RequestMetrics {
        std::atomic<size_t> total_requests{0};
        std::atomic<size_t> successful_requests{0};
        std::atomic<size_t> failed_requests{0};
        std::atomic<double> total_duration_ms{0.0};
        std::unordered_map<std::string, std::atomic<size_t>> endpoint_counts;
        std::unordered_map<int, std::atomic<size_t>> status_counts;
    };
    RequestMetrics request_metrics_;
    
    // Error tracking
    struct ErrorMetrics {
        std::atomic<size_t> total_errors{0};
        std::unordered_map<std::string, std::atomic<size_t>> component_errors;
        std::unordered_map<std::string, std::atomic<size_t>> error_type_counts;
        std::unordered_map<std::string, std::atomic<size_t>> severity_counts;
    };
    ErrorMetrics error_metrics_;
    
    // Business metrics
    mutable std::mutex business_metrics_mutex_;
    std::unordered_map<std::string, std::pair<double, std::unordered_map<std::string, std::string>>> business_metrics_;
    
    // Metrics collector for publishing
    std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector_;
    
    /**
     * @brief Update internal metric families
     */
    void update_metric_families();
};

/**
 * @brief Advanced Prometheus exporter with comprehensive monitoring
 */
class PrometheusExporter {
public:
    /**
     * @brief Configuration for Prometheus exporter
     */
    struct Config {
        bool enabled{true};
        std::string listen_address{"0.0.0.0"};
        int listen_port{9090};
        std::string metrics_path{"/metrics"};
        std::chrono::seconds scrape_interval{15};
        
        // Content settings
        bool include_help_text{true};
        bool include_type_info{true};
        bool include_timestamp{false};
        
        // Security settings
        bool require_authentication{false};
        std::string api_key_header{"X-API-Key"};
        std::vector<std::string> allowed_api_keys;
        std::vector<std::string> allowed_ip_ranges;
        
        // Advanced features
        bool enable_gzip_compression{true};
        std::chrono::seconds cache_duration{5};
        size_t max_metrics_per_family{1000};
    };
    
    /**
     * @brief Constructor
     * @param config Prometheus exporter configuration
     */
    explicit PrometheusExporter(const Config& config = Config{});
    
    /**
     * @brief Destructor
     */
    ~PrometheusExporter();
    
    /**
     * @brief Start Prometheus exporter HTTP server
     * @return true if started successfully
     */
    bool start();
    
    /**
     * @brief Stop Prometheus exporter HTTP server
     */
    void stop();
    
    /**
     * @brief Check if exporter is running
     * @return true if HTTP server is running
     */
    bool is_running() const { return running_.load(); }
    
    /**
     * @brief Register metrics collector
     * @param name Collector name
     * @param collector Metrics collector instance
     */
    void register_collector(const std::string& name, 
                           std::shared_ptr<omop::monitoring::MetricsCollector> collector);
    
    /**
     * @brief Register system metrics collector
     * @param collector System metrics collector
     */
    void register_system_collector(std::shared_ptr<SystemMetricsCollector> collector);
    
    /**
     * @brief Register application metrics collector
     * @param collector Application metrics collector
     */
    void register_application_collector(std::shared_ptr<ApplicationMetricsCollector> collector);
    
    /**
     * @brief Register health monitor for health metrics
     * @param health_monitor Health monitor instance
     */
    void register_health_monitor(std::shared_ptr<HealthMonitor> health_monitor);
    
    /**
     * @brief Get current metrics in Prometheus format
     * @return Prometheus exposition format string
     */
    std::string get_metrics_text();
    
    /**
     * @brief Get exporter statistics
     */
    struct Statistics {
        std::atomic<size_t> total_scrapes{0};
        std::atomic<size_t> successful_scrapes{0};
        std::atomic<size_t> failed_scrapes{0};
        std::atomic<size_t> cache_hits{0};
        std::atomic<size_t> cache_misses{0};
        std::chrono::system_clock::time_point last_scrape_time;
        std::chrono::milliseconds avg_scrape_duration{0};
        size_t active_metric_families{0};
        size_t total_metrics{0};
    };
    
    Statistics get_statistics() const;

private:
    Config config_;
    std::atomic<bool> running_{false};
    
    // HTTP server
    std::unique_ptr<std::thread> server_thread_;
    
    // Registered collectors
    std::mutex collectors_mutex_;
    std::unordered_map<std::string, std::shared_ptr<omop::monitoring::MetricsCollector>> collectors_;
    std::vector<std::shared_ptr<SystemMetricsCollector>> system_collectors_;
    std::vector<std::shared_ptr<ApplicationMetricsCollector>> app_collectors_;
    std::shared_ptr<HealthMonitor> health_monitor_;
    
    // Caching
    mutable std::mutex cache_mutex_;
    struct CachedMetrics {
        std::string content;
        std::chrono::system_clock::time_point created_time;
    };
    std::optional<CachedMetrics> cached_metrics_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    Statistics stats_;
    
    /**
     * @brief HTTP server main loop
     */
    void server_loop();
    
    /**
     * @brief Handle HTTP request
     * @param request Raw HTTP request
     * @return HTTP response
     */
    std::string handle_request(const std::string& request);
    
    /**
     * @brief Generate metrics content
     * @return Prometheus format metrics
     */
    std::string generate_metrics();
    
    /**
     * @brief Get metrics from standard collectors
     * @return Vector of metric families
     */
    std::vector<PrometheusMetricFamily> get_collector_metrics();
    
    /**
     * @brief Get health-related metrics
     * @return Vector of health metric families
     */
    std::vector<PrometheusMetricFamily> get_health_metrics();
    
    /**
     * @brief Convert metrics collector data to Prometheus format
     * @param collector Metrics collector
     * @return Vector of metric families
     */
    std::vector<PrometheusMetricFamily> convert_collector_metrics(
        std::shared_ptr<omop::monitoring::MetricsCollector> collector);
    
    /**
     * @brief Check authentication
     * @param headers HTTP headers
     * @return true if authenticated
     */
    bool check_authentication(const std::unordered_map<std::string, std::string>& headers);
    
    /**
     * @brief Check IP allowlist
     * @param client_ip Client IP address
     * @return true if allowed
     */
    bool check_ip_allowlist(const std::string& client_ip);
    
    /**
     * @brief Parse HTTP request
     * @param request Raw HTTP request
     * @return Parsed request components
     */
    struct HttpRequest {
        std::string method;
        std::string path;
        std::unordered_map<std::string, std::string> headers;
        std::unordered_map<std::string, std::string> query_params;
    };
    
    HttpRequest parse_request(const std::string& request);
    
    /**
     * @brief Generate HTTP response
     * @param status_code HTTP status code
     * @param content Response content
     * @param headers Additional headers
     * @return Complete HTTP response
     */
    std::string generate_response(int status_code, const std::string& content,
                                 const std::unordered_map<std::string, std::string>& headers = {});
    
    /**
     * @brief Compress content using gzip
     * @param content Content to compress
     * @return Compressed content
     */
    std::string compress_content(const std::string& content);
    
    /**
     * @brief Update statistics
     * @param scrape_duration Duration of metrics generation
     * @param cache_hit Whether cache was used
     */
    void update_statistics(std::chrono::milliseconds scrape_duration, bool cache_hit);
};

/**
 * @brief Factory for creating Prometheus exporters
 */
class PrometheusExporterFactory {
public:
    /**
     * @brief Create production Prometheus exporter
     * @param port HTTP server port
     * @return Configured exporter instance
     */
    static std::unique_ptr<PrometheusExporter> create_production_exporter(int port = 9090);
    
    /**
     * @brief Create development Prometheus exporter
     * @param port HTTP server port
     * @return Configured exporter instance
     */
    static std::unique_ptr<PrometheusExporter> create_development_exporter(int port = 9090);
    
    /**
     * @brief Create Kubernetes-compatible Prometheus exporter
     * @param port HTTP server port
     * @return Configured exporter instance
     */
    static std::unique_ptr<PrometheusExporter> create_kubernetes_exporter(int port = 9090);
    
    /**
     * @brief Create exporter from environment variables
     * Environment variables:
     * - PROMETHEUS_ENABLED: Enable/disable exporter
     * - PROMETHEUS_PORT: HTTP server port
     * - PROMETHEUS_PATH: Metrics endpoint path
     * - PROMETHEUS_AUTH_ENABLED: Enable authentication
     * - PROMETHEUS_API_KEY: API key for authentication
     * @return Configured exporter instance
     */
    static std::unique_ptr<PrometheusExporter> create_from_environment();
};

} // namespace omop::common