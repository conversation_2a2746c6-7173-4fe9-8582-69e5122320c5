/**
 * @file performance_monitor.h
 * @brief Performance monitoring classes for the OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * This file contains performance monitoring utilities that can be used
 * across all components of the ETL pipeline for performance measurement
 * and benchmarking.
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <chrono>
#include <atomic>
#include <limits>
#include <thread>
#include <mutex>
#include <optional>

namespace omop::common {

/**
 * @brief Performance metric type enumeration
 */
enum class PerformanceMetricType {
    Duration,
    Throughput,
    Memory,
    CPU,
    Disk,
    Network,
    Custom
};

/**
 * @brief Performance measurement structure
 */
struct PerformanceMeasurement {
    std::string metric_name;
    PerformanceMetricType type;
    double value;
    std::string unit;
    std::chrono::system_clock::time_point timestamp;
    std::unordered_map<std::string, std::string> labels;
    std::unordered_map<std::string, std::any> metadata;
};

/**
 * @brief Performance benchmark configuration
 */
struct BenchmarkConfig {
    std::string benchmark_name;
    size_t iterations{1};
    size_t warmup_iterations{0};
    std::chrono::seconds timeout{300};
    bool collect_system_metrics{true};
    bool collect_memory_metrics{true};
    bool collect_cpu_metrics{true};
    std::chrono::milliseconds sampling_interval{100};
    double failure_threshold_pct{10.0};
    std::unordered_map<std::string, std::any> additional_config;
};

/**
 * @brief Performance benchmark result
 */
struct BenchmarkResult {
    std::string benchmark_name;
    bool success{false};
    size_t completed_iterations{0};
    size_t failed_iterations{0};
    std::chrono::duration<double> total_duration{0};
    std::chrono::duration<double> average_duration{0};
    std::chrono::duration<double> min_duration{std::chrono::duration<double>::max()};
    std::chrono::duration<double> max_duration{0};
    std::vector<PerformanceMeasurement> measurements;
    std::string failure_reason;
    std::unordered_map<std::string, std::any> statistics;
};

/**
 * @brief System resource metrics
 */
struct SystemMetrics {
    double cpu_usage_percent{0.0};
    size_t memory_used_bytes{0};
    size_t memory_available_bytes{0};
    double memory_usage_percent{0.0};
    size_t disk_read_bytes{0};
    size_t disk_write_bytes{0};
    size_t network_rx_bytes{0};
    size_t network_tx_bytes{0};
    std::chrono::system_clock::time_point timestamp;
};

// Forward declaration of TimerInfo struct
struct TimerInfo {
    std::string metric_name;
    std::unordered_map<std::string, std::string> labels;
    std::chrono::high_resolution_clock::time_point start_time;
};

/**
 * @brief Performance monitor interface
 * 
 * This interface defines the contract for performance monitors that measure
 * and analyze performance characteristics of ETL operations.
 */
class IPerformanceMonitor {
public:
    virtual ~IPerformanceMonitor() = default;

    /**
     * @brief Initialize performance monitor
     * @param config Benchmark configuration
     * @return bool True if initialization successful
     */
    virtual bool initialize(const BenchmarkConfig& config) = 0;

    /**
     * @brief Start performance monitoring
     * @return bool True if monitoring started successfully
     */
    virtual bool start_monitoring() = 0;

    /**
     * @brief Stop performance monitoring
     * @return bool True if monitoring stopped successfully
     */
    virtual bool stop_monitoring() = 0;

    /**
     * @brief Record performance measurement
     * @param measurement Performance measurement
     */
    virtual void record_measurement(const PerformanceMeasurement& measurement) = 0;

    /**
     * @brief Start timing measurement
     * @param metric_name Metric name
     * @param labels Metric labels
     * @return std::string Timer ID
     */
    virtual std::string start_timer(
        const std::string& metric_name,
        const std::unordered_map<std::string, std::string>& labels = {}) = 0;

    /**
     * @brief Stop timing measurement
     * @param timer_id Timer ID
     * @return std::optional<double> Elapsed time in seconds
     */
    virtual std::optional<double> stop_timer(const std::string& timer_id) = 0;

    /**
     * @brief Start performance monitoring for a specific operation
     * @param operation_name Operation name
     * @return bool True if started successfully
     */
    virtual bool start(const std::string& operation_name) = 0;

    /**
     * @brief Stop performance monitoring for a specific operation
     * @param operation_name Operation name
     * @return std::unordered_map<std::string, double> Performance statistics
     */
    virtual std::unordered_map<std::string, double> stop(const std::string& operation_name) = 0;

    /**
     * @brief Measure function execution time
     * @param metric_name Metric name
     * @param func Function to measure
     * @param labels Metric labels
     * @return auto Function result
     */
    template<typename Func>
    auto measure_execution(
        const std::string& metric_name,
        Func&& func,
        const std::unordered_map<std::string, std::string>& labels = {}) -> decltype(func()) {
        auto timer_id = start_timer(metric_name, labels);
        if constexpr (std::is_void_v<decltype(func())>) {
            func();
            stop_timer(timer_id);
        } else {
            auto result = func();
            stop_timer(timer_id);
            return result;
        }
    }

    /**
     * @brief Run performance benchmark
     * @param benchmark_name Benchmark name
     * @param test_func Test function to benchmark
     * @param config Benchmark configuration
     * @return BenchmarkResult Benchmark results
     */
    virtual BenchmarkResult run_benchmark(
        const std::string& benchmark_name,
        std::function<void()> test_func,
        const BenchmarkConfig& config) = 0;

    /**
     * @brief Get current system metrics
     * @return SystemMetrics Current system resource usage
     */
    virtual SystemMetrics get_system_metrics() = 0;

    /**
     * @brief Get performance measurements
     * @param metric_name Metric name filter (empty for all)
     * @return std::vector<PerformanceMeasurement> Performance measurements
     */
    virtual std::vector<PerformanceMeasurement> get_measurements(
        const std::string& metric_name = "") = 0;

    /**
     * @brief Calculate statistics
     * @param metric_name Metric name
     * @return std::unordered_map<std::string, double> Statistics (min, max, avg, std_dev, etc.)
     */
    virtual std::unordered_map<std::string, double> calculate_statistics(
        const std::string& metric_name) = 0;

    /**
     * @brief Generate performance report
     * @param format Report format (text, json, html)
     * @return std::string Performance report
     */
    virtual std::string generate_report(const std::string& format = "text") = 0;

    /**
     * @brief Export measurements to file
     * @param filename Output filename
     * @param format Export format (csv, json)
     * @return bool True if export successful
     */
    virtual bool export_measurements(
        const std::string& filename,
        const std::string& format = "csv") = 0;

    /**
     * @brief Clear all measurements
     */
    virtual void clear_measurements() = 0;

    /**
     * @brief Set performance thresholds
     * @param thresholds Performance thresholds by metric name
     */
    virtual void set_thresholds(
        const std::unordered_map<std::string, double>& thresholds) = 0;

    /**
     * @brief Check if performance thresholds are met
     * @return std::vector<std::string> List of failed threshold checks
     */
    virtual std::vector<std::string> check_thresholds() = 0;
};

/**
 * @brief Default performance monitor implementation
 */
class PerformanceMonitor : public IPerformanceMonitor {
public:
    PerformanceMonitor();
    ~PerformanceMonitor() override;

    bool initialize(const BenchmarkConfig& config) override;
    bool start_monitoring() override;
    bool stop_monitoring() override;

    void record_measurement(const PerformanceMeasurement& measurement) override;

    std::string start_timer(
        const std::string& metric_name,
        const std::unordered_map<std::string, std::string>& labels = {}) override;

    std::optional<double> stop_timer(const std::string& timer_id) override;

    bool start(const std::string& operation_name) override;

    std::unordered_map<std::string, double> stop(const std::string& operation_name) override;

    BenchmarkResult run_benchmark(
        const std::string& benchmark_name,
        std::function<void()> test_func,
        const BenchmarkConfig& config) override;

    SystemMetrics get_system_metrics() override;

    std::vector<PerformanceMeasurement> get_measurements(
        const std::string& metric_name = "") override;

    std::unordered_map<std::string, double> calculate_statistics(
        const std::string& metric_name) override;

    std::string generate_report(const std::string& format = "text") override;

    bool export_measurements(
        const std::string& filename,
        const std::string& format = "csv") override;

    void clear_measurements() override;

    void set_thresholds(
        const std::unordered_map<std::string, double>& thresholds) override;

    std::vector<std::string> check_thresholds() override;

    // Additional convenience methods for integration tests
    bool startOperation(const std::string& operation_name);
    double endOperation(const std::string& operation_name);
    size_t getCurrentMemoryUsage();
    double getCurrentCPUUsage();

private:
    BenchmarkConfig config_;
    std::vector<PerformanceMeasurement> measurements_;
    std::unordered_map<std::string, std::chrono::high_resolution_clock::time_point> active_timers_;
    std::unordered_map<std::string, std::string> operation_timers_; // Map operation names to timer IDs
    std::unordered_map<std::string, double> thresholds_;
    std::atomic<bool> monitoring_active_{false};
    std::mutex measurements_mutex_;
    std::chrono::system_clock::time_point last_measurement_time_;

    std::string generate_timer_id();
};

/**
 * @brief RAII timer for automatic performance measurement
 * 
 * This is a simplified version that can be used similar to the private
 * PerformanceMonitor class in ExtractorBase for basic timing operations.
 */
class ScopedPerformanceTimer {
public:
    /**
     * @brief Constructor for use with IPerformanceMonitor
     * @param monitor Performance monitor reference
     * @param metric_name Metric name
     * @param labels Optional metric labels
     */
    ScopedPerformanceTimer(
        IPerformanceMonitor& monitor,
        const std::string& metric_name,
        const std::unordered_map<std::string, std::string>& labels = {});

    /**
     * @brief Constructor for basic timing with direct statistics update
     * @param operation_name Operation name
     * @param operation_timings Reference to timing statistics map
     * @param operation_counts Reference to count statistics map
     */
    ScopedPerformanceTimer(
        const std::string& operation_name,
        std::unordered_map<std::string, double>& operation_timings,
        std::unordered_map<std::string, size_t>& operation_counts);

    ~ScopedPerformanceTimer();

    ScopedPerformanceTimer(const ScopedPerformanceTimer&) = delete;
    ScopedPerformanceTimer& operator=(const ScopedPerformanceTimer&) = delete;
    ScopedPerformanceTimer(ScopedPerformanceTimer&&) = default;
    ScopedPerformanceTimer& operator=(ScopedPerformanceTimer&&) = default;

    /**
     * @brief Stop timer manually
     * @return std::optional<double> Elapsed time in seconds
     */
    std::optional<double> stop();

private:
    enum class TimerType {
        MONITOR_BASED,
        DIRECT_STATS
    };

    TimerType timer_type_;
    
    // For monitor-based timing
    IPerformanceMonitor* monitor_{nullptr};
    std::string timer_id_;
    
    // For direct statistics timing
    std::string operation_name_;
    std::unordered_map<std::string, double>* operation_timings_{nullptr};
    std::unordered_map<std::string, size_t>* operation_counts_{nullptr};
    std::chrono::high_resolution_clock::time_point start_time_;
    
    bool stopped_{false};
};

/**
 * @brief Create performance monitor instance
 * @return std::unique_ptr<IPerformanceMonitor> Performance monitor instance
 */
std::unique_ptr<IPerformanceMonitor> create_performance_monitor();

/**
 * @brief Get default benchmark configuration
 * @return BenchmarkConfig Default configuration
 */
BenchmarkConfig get_default_benchmark_config();

/**
 * @brief Performance testing macros
 */
#define BENCHMARK_SCOPE(monitor, metric_name, ...) \
    auto perf_timer_##__LINE__ = omop::common::ScopedPerformanceTimer(monitor, metric_name, ##__VA_ARGS__)

#define MEASURE_PERFORMANCE(monitor, metric_name, code_block) \
    monitor.measure_execution(metric_name, [&]() { code_block; })

#define ASSERT_PERFORMANCE_THRESHOLD(monitor, metric, expected, tolerance) \
    do { \
        auto stats = monitor.calculate_statistics(metric); \
        auto actual = stats.count("average") ? stats["average"] : 0.0; \
        auto diff_pct = std::abs((actual - expected) / expected) * 100.0; \
        if (diff_pct > tolerance) { \
            throw std::runtime_error("Performance threshold exceeded for " + std::string(metric) + \
                                   ": expected " + std::to_string(expected) + \
                                   ", got " + std::to_string(actual) + \
                                   " (difference: " + std::to_string(diff_pct) + "%)"); \
        } \
    } while(0)

} // namespace omop::common