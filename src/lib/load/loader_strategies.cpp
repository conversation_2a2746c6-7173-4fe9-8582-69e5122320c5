#include "load/loader_strategies.h"
#include "common/logging.h"
#include "common/utilities.h"
#include <algorithm>
#include <sstream>
#include <thread>
#include <random>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include <any>
#include <chrono>
#include <locale>
#include <iomanip>

namespace omop::load {

// SingleRecordLoadingStrategy implementation
SingleRecordLoadingStrategy::SingleRecordLoadingStrategy() = default;

SingleRecordLoadingStrategy::~SingleRecordLoadingStrategy() = default;

bool SingleRecordLoadingStrategy::initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) {
    config_ = config;
    metrics_ = LoadingMetrics{};
    start_time_ = std::chrono::steady_clock::now();
    transaction_active_ = false;
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("SingleRecordLoadingStrategy initialised");
    
    return true;
}

bool SingleRecordLoadingStrategy::load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->warn("Attempting to load record without active transaction");
        return false;
    }
    
    try {
        // Database record loading implementation would go here
        // For now, use a more realistic simulation based on actual database operations
        std::this_thread::sleep_for(std::chrono::microseconds(100));
        
        metrics_.total_records++;
        metrics_.successful_records++;
        
        return true;
    } catch (const std::exception& e) {
        metrics_.total_records++;
        metrics_.failed_records++;
        
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to load record: {}", e.what());
        
        return false;
    }
}

size_t SingleRecordLoadingStrategy::load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) {
    size_t loaded = 0;
    for (const auto& record : batch) {
        if (load_record(record, context)) {
            loaded++;
        }
    }
    return loaded;
}

bool SingleRecordLoadingStrategy::begin_transaction(omop::core::ProcessingContext& context) {
    if (transaction_active_) {
        return false;
    }
    
    transaction_active_ = true;
    return true;
}

bool SingleRecordLoadingStrategy::commit_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Simulate transaction commit
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.successful_transactions++;
        
        return true;
    } catch (const std::exception& e) {
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.failed_transactions++;
        
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to commit transaction: {}", e.what());
        
        return false;
    }
}

bool SingleRecordLoadingStrategy::rollback_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    transaction_active_ = false;
    metrics_.total_transactions++;
    metrics_.failed_transactions++;
    
    return true;
}

bool SingleRecordLoadingStrategy::finalize(omop::core::ProcessingContext& context) {
    auto end_time = std::chrono::steady_clock::now();
    metrics_.total_processing_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("SingleRecordLoadingStrategy finalised. Processed {} records in {} ms", 
                metrics_.total_records, metrics_.total_processing_time.count());
    
    return true;
}

LoadingMetrics SingleRecordLoadingStrategy::get_metrics() const {
    return metrics_;
}

std::vector<LoadingMode> SingleRecordLoadingStrategy::get_supported_modes() const {
    return {LoadingMode::Insert, LoadingMode::Append};
}

std::vector<std::string> SingleRecordLoadingStrategy::validate_config(const LoadingConfig& config) const {
    std::vector<std::string> errors;
    
    if (config.batch_size == 0) {
        errors.push_back("Batch size must be greater than 0");
    }
    
    if (config.target_table.empty()) {
        errors.push_back("Target table must be specified");
    }
    
    return errors;
}

// BatchInsertLoadingStrategy implementation
BatchInsertLoadingStrategy::BatchInsertLoadingStrategy() = default;

BatchInsertLoadingStrategy::~BatchInsertLoadingStrategy() = default;

bool BatchInsertLoadingStrategy::initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) {
    config_ = config;
    metrics_ = LoadingMetrics{};
    start_time_ = std::chrono::steady_clock::now();
    transaction_active_ = false;
    current_batch_.reserve(config.batch_size);
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("BatchInsertLoadingStrategy initialised with batch size {}", config.batch_size);
    
    return true;
}

bool BatchInsertLoadingStrategy::load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) {
    std::lock_guard<std::mutex> lock(batch_mutex_);
    
    if (!transaction_active_) {
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->warn("Attempting to load record without active transaction");
        return false;
    }
    
    current_batch_.push_back(record);
    
    // Process batch if it's full
    if (current_batch_.size() >= config_.batch_size) {
        return process_current_batch(context);
    }
    
    return true;
}

size_t BatchInsertLoadingStrategy::load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) {
    std::lock_guard<std::mutex> lock(batch_mutex_);
    
    if (!transaction_active_) {
        return 0;
    }
    
    size_t loaded = 0;
    for (const auto& record : batch) {
        current_batch_.push_back(record);
        loaded++;
        
        // Process batch if it's full
        if (current_batch_.size() >= config_.batch_size) {
            if (!process_current_batch(context)) {
                break;
            }
        }
    }
    
    return loaded;
}

bool BatchInsertLoadingStrategy::begin_transaction(omop::core::ProcessingContext& context) {
    if (transaction_active_) {
        return false;
    }
    
    transaction_active_ = true;
    return true;
}

bool BatchInsertLoadingStrategy::commit_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(batch_mutex_);
    
    // Process any remaining records in the current batch
    if (!current_batch_.empty()) {
        if (!process_current_batch(context)) {
            return false;
        }
    }
    
    try {
        // Simulate transaction commit
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.successful_transactions++;
        
        return true;
    } catch (const std::exception& e) {
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.failed_transactions++;
        
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to commit transaction: {}", e.what());
        
        return false;
    }
}

bool BatchInsertLoadingStrategy::rollback_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(batch_mutex_);
    
    // Clear current batch
    current_batch_.clear();
    
    transaction_active_ = false;
    metrics_.total_transactions++;
    metrics_.failed_transactions++;
    
    return true;
}

bool BatchInsertLoadingStrategy::finalize(omop::core::ProcessingContext& context) {
    auto end_time = std::chrono::steady_clock::now();
    metrics_.total_processing_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("BatchInsertLoadingStrategy finalised. Processed {} records in {} batches in {} ms", 
                metrics_.total_records, metrics_.total_batches, metrics_.total_processing_time.count());
    
    return true;
}

LoadingMetrics BatchInsertLoadingStrategy::get_metrics() const {
    return metrics_;
}

std::vector<LoadingMode> BatchInsertLoadingStrategy::get_supported_modes() const {
    return {LoadingMode::Insert, LoadingMode::Append};
}

std::vector<std::string> BatchInsertLoadingStrategy::validate_config(const LoadingConfig& config) const {
    std::vector<std::string> errors;
    
    if (config.batch_size == 0) {
        errors.push_back("Batch size must be greater than 0");
    }
    
    if (config.target_table.empty()) {
        errors.push_back("Target table must be specified");
    }
    
    return errors;
}

bool BatchInsertLoadingStrategy::process_current_batch(omop::core::ProcessingContext& context) {
    if (current_batch_.empty()) {
        return true;
    }
    
    try {
        // Simulate batch processing
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        
        metrics_.total_records += current_batch_.size();
        metrics_.successful_records += current_batch_.size();
        metrics_.total_batches++;
        metrics_.successful_batches++;
        
        current_batch_.clear();
        
        return true;
    } catch (const std::exception& e) {
        metrics_.total_records += current_batch_.size();
        metrics_.failed_records += current_batch_.size();
        metrics_.total_batches++;
        metrics_.failed_batches++;
        
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to process batch: {}", e.what());
        
        current_batch_.clear();
        
        return false;
    }
}

// BulkLoadStrategy implementation
BulkLoadStrategy::BulkLoadStrategy() = default;

BulkLoadStrategy::~BulkLoadStrategy() = default;

bool BulkLoadStrategy::initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) { 
    config_ = config;
    metrics_ = LoadingMetrics{};
    start_time_ = std::chrono::steady_clock::now();
    transaction_active_ = false;
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("BulkLoadStrategy initialised");
    
    return true; 
}

bool BulkLoadStrategy::load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) { 
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Bulk record loading implementation would go here
        // Use database-specific bulk loading mechanisms
        std::this_thread::sleep_for(std::chrono::microseconds(50));
        
        metrics_.total_records++;
        metrics_.successful_records++;
        
        return true;
    } catch (const std::exception& e) {
        metrics_.total_records++;
        metrics_.failed_records++;
        
        return false;
    }
}

size_t BulkLoadStrategy::load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) { 
    size_t loaded = 0;
    for (const auto& record : batch) {
        if (load_record(record, context)) {
            loaded++;
        }
    }
    return loaded; 
}

bool BulkLoadStrategy::begin_transaction(omop::core::ProcessingContext& context) { 
    if (transaction_active_) {
        return false;
    }
    
    transaction_active_ = true;
    return true; 
}

bool BulkLoadStrategy::commit_transaction(omop::core::ProcessingContext& context) { 
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Simulate bulk transaction commit
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.successful_transactions++;
        
        return true;
    } catch (const std::exception& e) {
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.failed_transactions++;
        
        return false;
    }
}

bool BulkLoadStrategy::rollback_transaction(omop::core::ProcessingContext& context) { 
    if (!transaction_active_) {
        return false;
    }
    
    transaction_active_ = false;
    metrics_.total_transactions++;
    metrics_.failed_transactions++;
    
    return true; 
}

bool BulkLoadStrategy::finalize(omop::core::ProcessingContext& context) { 
    auto end_time = std::chrono::steady_clock::now();
    metrics_.total_processing_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("BulkLoadStrategy finalised. Processed {} records in {} ms", 
                metrics_.total_records, metrics_.total_processing_time.count());
    
    return true; 
}

LoadingMetrics BulkLoadStrategy::get_metrics() const { 
    return metrics_; 
}


std::vector<LoadingMode> BulkLoadStrategy::get_supported_modes() const { 
    return {LoadingMode::Insert, LoadingMode::Replace}; 
}

std::vector<std::string> BulkLoadStrategy::validate_config(const LoadingConfig& config) const { 
    std::vector<std::string> errors;
    
    if (config.target_table.empty()) {
        errors.push_back("Target table must be specified");
    }
    
    return errors; 
}

// UpsertLoadingStrategy implementation
UpsertLoadingStrategy::UpsertLoadingStrategy() = default;

UpsertLoadingStrategy::~UpsertLoadingStrategy() = default;

bool UpsertLoadingStrategy::initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) {
    config_ = config;
    metrics_ = LoadingMetrics{};
    start_time_ = std::chrono::steady_clock::now();
    transaction_active_ = false;
    insert_count_ = 0;
    update_count_ = 0;
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("UpsertLoadingStrategy initialised");
    
    return true;
}

bool UpsertLoadingStrategy::load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->warn("Attempting to load record without active transaction");
        return false;
    }
    
    try {
        // Check if record exists
        bool record_exists = check_record_exists(record);
        
        if (record_exists) {
            // Update existing record
            std::this_thread::sleep_for(std::chrono::microseconds(200));
            update_count_++;
        } else {
            // Insert new record
            std::this_thread::sleep_for(std::chrono::microseconds(150));
            insert_count_++;
        }
        
        metrics_.total_records++;
        metrics_.successful_records++;
        
        return true;
    } catch (const std::exception& e) {
        metrics_.total_records++;
        metrics_.failed_records++;
        
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to upsert record: {}", e.what());
        
        return false;
    }
}

size_t UpsertLoadingStrategy::load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) {
    size_t loaded = 0;
    for (const auto& record : batch) {
        if (load_record(record, context)) {
            loaded++;
        }
    }
    return loaded;
}

bool UpsertLoadingStrategy::begin_transaction(omop::core::ProcessingContext& context) {
    if (transaction_active_) {
        return false;
    }
    
    transaction_active_ = true;
    return true;
}

bool UpsertLoadingStrategy::commit_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Simulate transaction commit
        std::this_thread::sleep_for(std::chrono::milliseconds(15));
        
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.successful_transactions++;
        
        return true;
    } catch (const std::exception& e) {
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.failed_transactions++;
        
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to commit transaction: {}", e.what());
        
        return false;
    }
}

bool UpsertLoadingStrategy::rollback_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    transaction_active_ = false;
    metrics_.total_transactions++;
    metrics_.failed_transactions++;
    
    return true;
}

bool UpsertLoadingStrategy::finalize(omop::core::ProcessingContext& context) {
    auto end_time = std::chrono::steady_clock::now();
    metrics_.total_processing_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("UpsertLoadingStrategy finalised. Processed {} records ({} inserts, {} updates) in {} ms", 
                metrics_.total_records, insert_count_, update_count_, metrics_.total_processing_time.count());
    
    return true;
}

LoadingMetrics UpsertLoadingStrategy::get_metrics() const {
    return metrics_;
}


std::vector<LoadingMode> UpsertLoadingStrategy::get_supported_modes() const {
    return {LoadingMode::Upsert, LoadingMode::Merge};
}

std::vector<std::string> UpsertLoadingStrategy::validate_config(const LoadingConfig& config) const {
    std::vector<std::string> errors;
    
    if (config.target_table.empty()) {
        errors.push_back("Target table must be specified");
    }
    
    if (config.key_columns.empty()) {
        errors.push_back("Key columns must be specified for upsert operations");
    }
    
    return errors;
}

bool UpsertLoadingStrategy::check_record_exists(const omop::core::Record& record) {
    if (config_.key_columns.empty()) {
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("No key columns specified for record existence check");
        return false;
    }
    
    try {
        // Build existence check query
        std::ostringstream query;
        query << "SELECT 1 FROM " << config_.target_table << " WHERE ";
        
        bool first = true;
        for (const auto& key_col : config_.key_columns) {
            if (!first) query << " AND ";
            query << key_col << " = ?";
            first = false;
        }
        query << " LIMIT 1";
        
        // Extract key values from record
        std::vector<std::any> key_values;
        for (const auto& key_col : config_.key_columns) {
            auto field = record.getFieldOptional(key_col);
            if (!field.has_value()) {
                auto logger = common::Logger::get("omop-loading-strategy");
                logger->error("Missing key column '{}' in record", key_col);
                return false;
            }
            key_values.push_back(field.value());
        }
        
        // This would execute the actual database query in production
        // For now, we'll use a more realistic simulation based on record content
        std::string record_key;
        for (const auto& key_col : config_.key_columns) {
            auto field = record.getFieldOptional(key_col);
            if (field.has_value()) {
                record_key += key_col + "=";
                // Convert field value to string for key generation
                if (field->type() == typeid(std::string)) {
                    record_key += std::any_cast<std::string>(*field);
                } else if (field->type() == typeid(int64_t)) {
                    record_key += std::to_string(std::any_cast<int64_t>(*field));
                } else if (field->type() == typeid(int)) {
                    record_key += std::to_string(std::any_cast<int>(*field));
                }
                record_key += ";";
            }
        }
        
        // Use hash of key to determine existence more consistently
        std::hash<std::string> hasher;
        size_t key_hash = hasher(record_key);
        
        // Return consistent result based on key hash (30% existence rate)
        return (key_hash % 100) < 30;
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Error checking record existence: {}", e.what());
        return false;
    }
}

// DatabaseRecordChecker implementation
DatabaseRecordChecker::DatabaseRecordChecker(std::shared_ptr<extract::IDatabaseConnection> connector)
    : db_connector_(connector) {
}

bool DatabaseRecordChecker::record_exists(
    const omop::core::Record& record, 
    const std::string& table_name,
    const std::vector<std::string>& key_columns) {
    
    if (!db_connector_ || key_columns.empty()) {
        return false;
    }
    
    try {
        std::string query = build_exists_query(table_name, key_columns);
        
        // Extract key values from record
        std::vector<std::any> key_values;
        for (const auto& key_col : key_columns) {
            auto field_opt = record.getFieldOptional(key_col);
            if (field_opt) {
                key_values.push_back(field_opt.value());
            } else {
                // Missing key field
                return false;
            }
        }
        
        // Execute query using database connector
        // This is simplified - real implementation would use prepared statements
        auto result = db_connector_->execute_query(query);
        
        // Check if any rows returned (record exists)
        return result && result->next();
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-database-checker");
        logger->error("Error checking record existence: {}", e.what());
        return false;
    }
}

RecordChange DatabaseRecordChecker::check_record_changes(
    const omop::core::Record& record,
    const std::string& table_name,
    const std::vector<std::string>& key_columns,
    const std::vector<std::string>& tracking_columns) {
    
    RecordChange change;
    change.change_timestamp = std::chrono::system_clock::now();
    
    if (!db_connector_ || key_columns.empty()) {
        change.change_type = RecordChange::ChangeType::Insert; // Assume new if can't check
        return change;
    }
    
    try {
        // Check if record exists first
        if (!record_exists(record, table_name, key_columns)) {
            change.change_type = RecordChange::ChangeType::Insert;
            return change;
        }
        
        // Record exists, check for changes
        std::vector<std::string> columns_to_check = tracking_columns;
        if (columns_to_check.empty()) {
            // If no specific columns specified, check all non-key columns
            for (const auto& [field_name, field_value] : record.getFields()) {
                if (std::find(key_columns.begin(), key_columns.end(), field_name) == key_columns.end()) {
                    columns_to_check.push_back(field_name);
                }
            }
        }
        
        // Build and execute select query to get current database values
        std::string query = build_select_query(table_name, columns_to_check, key_columns);
        auto result = db_connector_->execute_query(query);
        
        if (!result || !result->next()) {
            // Record should exist but query returned nothing - treat as insert
            change.change_type = RecordChange::ChangeType::Insert;
            return change;
        }
        
        // Compare values to detect changes
        bool has_changes = false;
        auto db_row = result->to_record(); // Current row
        
        for (const auto& col : columns_to_check) {
            auto record_field = record.get_field(col);
            if (record_field) {
                // Simplified comparison - production would handle type conversions properly
                auto db_field = db_row.get_field(col);
                if (db_field) {
                    // Compare values (simplified)
                    std::string record_str = "record_value"; // Placeholder - would serialize properly
                    std::string db_str = "db_value";         // Placeholder - would serialize properly
                    
                    if (record_str != db_str) {
                        has_changes = true;
                        change.changed_columns.push_back(col);
                        change.change_values[col] = *record_field;
                        change.previous_values[col] = *db_field;
                    }
                }
            }
        }
        
        change.change_type = has_changes ? RecordChange::ChangeType::Update : RecordChange::ChangeType::None;
        
        return change;
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-database-checker");
        logger->error("Error checking record changes: {}", e.what());
        
        // If error, assume it's changed to be safe
        change.change_type = RecordChange::ChangeType::Update;
        return change;
    }
}

std::string DatabaseRecordChecker::get_stored_record_hash(
    const std::string& table_name,
    const std::vector<std::string>& key_columns,
    const std::vector<std::any>& key_values) {
    
    if (!db_connector_ || key_columns.size() != key_values.size()) {
        return "";
    }
    
    try {
        // Look for a hash column in the table (common in UK NHS systems)
        std::vector<std::string> hash_columns = {"record_hash", "data_hash", "checksum"};
        
        for (const auto& hash_col : hash_columns) {
            std::string query = build_select_query(table_name, {hash_col}, key_columns);
            auto result = db_connector_->execute_query(query);
            
            if (result && result->next()) {
                auto row = result->to_record();
                auto hash_field = row.get_field(hash_col);
                if (hash_field) {
                    // Return the hash value
                    return "hash_value"; // Placeholder - would extract actual value
                }
            }
        }
        
        return "";
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-database-checker");
        logger->error("Error getting stored record hash: {}", e.what());
        return "";
    }
}

std::any DatabaseRecordChecker::get_max_watermark(
    const std::string& table_name,
    const std::string& watermark_column) {
    
    if (!db_connector_ || watermark_column.empty()) {
        return std::any{};
    }
    
    try {
        std::string query = build_watermark_query(table_name, watermark_column);
        auto result = db_connector_->execute_query(query);
        
        if (result && result->next()) {
            auto watermark_field = result->get_value(0);
            return watermark_field;
        }
        
        return std::any{};
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-database-checker");
        logger->error("Error getting max watermark: {}", e.what());
        return std::any{};
    }
}

bool DatabaseRecordChecker::create_cdc_tracking_table(
    const std::string& source_table,
    const CDCConfig& cdc_config) {
    
    if (!db_connector_) {
        return false;
    }
    
    try {
        std::string cdc_table_name = "cdc_" + source_table;
        
        // Create CDC tracking table with UK NHS-specific columns
        std::string create_query = R"(
            CREATE TABLE IF NOT EXISTS )" + cdc_table_name + R"( (
                cdc_id BIGSERIAL PRIMARY KEY,
                source_table VARCHAR(100) NOT NULL DEFAULT ')" + source_table + R"(',
                operation_type CHAR(1) NOT NULL CHECK (operation_type IN ('I', 'U', 'D')),
                record_key VARCHAR(500) NOT NULL,
                change_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                data_hash VARCHAR(64),
                changed_columns TEXT[],
                change_values JSONB,
                previous_values JSONB,
                nhs_number VARCHAR(10), -- UK NHS specific
                created_by VARCHAR(100) DEFAULT USER,
                processed BOOLEAN DEFAULT FALSE,
                INDEX idx_cdc_timestamp (change_timestamp),
                INDEX idx_cdc_record_key (record_key),
                INDEX idx_cdc_operation (operation_type),
                INDEX idx_cdc_processed (processed)
            )
        )";
        
        auto result = db_connector_->execute_query(create_query);
        
        auto logger = common::Logger::get("omop-database-checker");
        logger->info("Created CDC tracking table: {}", cdc_table_name);
        
        return true;
        
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-database-checker");
        logger->error("Error creating CDC tracking table: {}", e.what());
        return false;
    }
}

std::string DatabaseRecordChecker::build_exists_query(
    const std::string& table_name,
    const std::vector<std::string>& key_columns) {
    
    std::string query = "SELECT 1 FROM " + table_name + " WHERE ";
    
    for (size_t i = 0; i < key_columns.size(); ++i) {
        if (i > 0) query += " AND ";
        query += key_columns[i] + " = ?"; // Use parameterized queries
    }
    
    query += " LIMIT 1";
    return query;
}

std::string DatabaseRecordChecker::build_select_query(
    const std::string& table_name,
    const std::vector<std::string>& columns,
    const std::vector<std::string>& key_columns) {
    
    std::string query = "SELECT ";
    
    for (size_t i = 0; i < columns.size(); ++i) {
        if (i > 0) query += ", ";
        query += columns[i];
    }
    
    query += " FROM " + table_name + " WHERE ";
    
    for (size_t i = 0; i < key_columns.size(); ++i) {
        if (i > 0) query += " AND ";
        query += key_columns[i] + " = ?";
    }
    
    return query;
}

std::string DatabaseRecordChecker::build_watermark_query(
    const std::string& table_name,
    const std::string& watermark_column) {
    
    return "SELECT MAX(" + watermark_column + ") as max_watermark FROM " + table_name;
}

// DeltaLoadingStrategy implementation
DeltaLoadingStrategy::DeltaLoadingStrategy() = default;

DeltaLoadingStrategy::~DeltaLoadingStrategy() = default;

bool DeltaLoadingStrategy::initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) {
    config_ = config;
    metrics_ = LoadingMetrics{};
    start_time_ = std::chrono::steady_clock::now();
    transaction_active_ = false;
    
    // Initialize delta-specific configuration
    watermark_column_ = config_.watermark_column;
    watermark_file_path_ = config_.watermark_file.empty() ? 
                          "/tmp/omop-etl/watermarks/" + config_.target_table + ".watermark" : 
                          config_.watermark_file;
    
    new_records_ = 0;
    changed_records_ = 0;
    unchanged_records_ = 0;
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("DeltaLoadingStrategy initialised for table: {}", config_.target_table);
    
    // Initialize watermarks
    if (!initialize_watermarks()) {
        logger->error("Failed to initialize watermarks for delta loading");
        return false;
    }
    
    return true;
}

bool DeltaLoadingStrategy::load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    try {
        auto logger = common::Logger::get("omop-loading-strategy");
        
        // Check if record has changed using timestamp or hash comparison
        if (check_record_changed(record)) {
            // Record has changed or is new
            if (config_.mode == LoadingMode::Upsert || config_.mode == LoadingMode::Merge) {
                // Determine if it's an insert or update based on key existence
                // For now, use simple logic - in production, this would query the database
                bool record_exists = check_record_changed(record);
                
                if (record_exists) {
                    changed_records_++;
                    logger->debug("Delta: Updated record with keys");
                } else {
                    new_records_++;
                    logger->debug("Delta: Inserted new record");
                }
            } else {
                new_records_++;
            }
            
            metrics_.total_records++;
            metrics_.successful_records++;
            return true;
        } else {
            // Record unchanged - skip processing
            unchanged_records_++;
            logger->trace("Delta: Skipped unchanged record");
            return true; // Not an error, just skipped
        }
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to process delta record: {}", e.what());
        
        metrics_.total_records++;
        metrics_.failed_records++;
        return false;
    }
}

size_t DeltaLoadingStrategy::load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) {
    size_t loaded = 0;
    auto logger = common::Logger::get("omop-loading-strategy");
    
    logger->info("Delta: Processing batch of {} records", batch.size());
    
    for (const auto& record : batch) {
        if (load_record(record, context)) {
            loaded++;
        }
    }
    
    logger->info("Delta: Batch complete - {} loaded, {} new, {} changed, {} unchanged", 
                 loaded, new_records_, changed_records_, unchanged_records_);
    
    return loaded;
}

bool DeltaLoadingStrategy::begin_transaction(omop::core::ProcessingContext& context) {
    if (transaction_active_) {
        return false;
    }
    
    transaction_active_ = true;
    return true;
}

bool DeltaLoadingStrategy::commit_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Update watermarks after successful transaction
        if (!update_watermarks()) {
            auto logger = common::Logger::get("omop-loading-strategy");
            logger->error("Failed to update watermarks after transaction commit");
        }
        
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.successful_transactions++;
        return true;
    } catch (const std::exception& e) {
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.failed_transactions++;
        return false;
    }
}

bool DeltaLoadingStrategy::rollback_transaction(omop::core::ProcessingContext& context) {
    if (!transaction_active_) {
        return false;
    }
    
    transaction_active_ = false;
    metrics_.total_transactions++;
    metrics_.failed_transactions++;
    return true;
}

bool DeltaLoadingStrategy::finalize(omop::core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-loading-strategy");
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
    
    // Save final watermarks
    save_watermark_to_file();
    
    logger->info("DeltaLoadingStrategy finalised. Processed {} records ({} new, {} changed, {} unchanged) in {} ms", 
                 metrics_.total_records, new_records_, changed_records_, unchanged_records_, duration.count());
    
    return true;
}

LoadingMetrics DeltaLoadingStrategy::get_metrics() const {
    auto current_metrics = metrics_;
    
    // Add delta-specific metrics
    current_metrics.additional_metrics["new_records"] = new_records_;
    current_metrics.additional_metrics["changed_records"] = changed_records_;
    current_metrics.additional_metrics["unchanged_records"] = unchanged_records_;
    current_metrics.additional_metrics["watermark_column"] = watermark_column_;
    
    return current_metrics;
}

std::vector<LoadingMode> DeltaLoadingStrategy::get_supported_modes() const {
    return {LoadingMode::Insert, LoadingMode::Update, LoadingMode::Upsert, LoadingMode::Merge};
}

std::vector<std::string> DeltaLoadingStrategy::validate_config(const LoadingConfig& config) const {
    std::vector<std::string> errors;
    
    if (config.watermark_column.empty()) {
        errors.push_back("Watermark column must be specified for delta loading");
    }
    
    if (config.target_table.empty()) {
        errors.push_back("Target table must be specified for delta loading");
    }
    
    if (config.mode == LoadingMode::Upsert || config.mode == LoadingMode::Merge) {
        if (config.key_columns.empty()) {
            errors.push_back("Key columns must be specified for upsert/merge delta operations");
        }
    }
    
    return errors;
}

bool DeltaLoadingStrategy::initialize_watermarks() {
    auto logger = common::Logger::get("omop-loading-strategy");
    
    // Set default watermark if not configured
    if (last_watermark_ == std::chrono::system_clock::time_point{}) {
        if (config_.initial_watermark != std::chrono::system_clock::time_point{}) {
            last_watermark_ = config_.initial_watermark;
        } else {
            // Default to 1 day ago for UK NHS data processing
            last_watermark_ = std::chrono::system_clock::now() - std::chrono::hours(24);
        }
    }
    
    // Try to load existing watermark from file
    if (!load_watermark_from_file()) {
        logger->info("No existing watermark found, using initial watermark: {}", 
                    std::chrono::duration_cast<std::chrono::seconds>(last_watermark_.time_since_epoch()).count());
    }
    
    return true;
}

bool DeltaLoadingStrategy::update_watermarks() {
    // Update watermark to current time for timestamp-based CDC
    last_watermark_ = std::chrono::system_clock::now();
    
    return save_watermark_to_file();
}

bool DeltaLoadingStrategy::check_record_changed(const omop::core::Record& record) {
    auto logger = common::Logger::get("omop-loading-strategy");
    
    try {
        // If using timestamp-based change detection
        if (!watermark_column_.empty()) {
            auto field_opt = record.getFieldOptional(watermark_column_);
            if (field_opt) {
                // Try to parse as timestamp - this is simplified, production would handle various formats
                if (field_opt->type() == typeid(std::string)) {
                    std::string timestamp_str = std::any_cast<std::string>(*field_opt);
                    // Simplified timestamp comparison - would use proper parsing in production
                    auto record_time = std::chrono::system_clock::now(); // Placeholder
                    return record_time > last_watermark_;
                } else if (field_opt->type() == typeid(std::chrono::system_clock::time_point)) {
                    auto record_time = std::any_cast<std::chrono::system_clock::time_point>(*field_opt);
                    return record_time > last_watermark_;
                }
            }
        }
        
        // If using hash-based change detection
        if (config_.enable_hash_comparison) {
            std::string current_hash = generate_record_hash(record);
            // In production, would compare against stored hash from database
            // For now, assume records with hash are changed
            return !current_hash.empty();
        }
        
        // Default: assume all records are new/changed
        return true;
        
    } catch (const std::exception& e) {
        logger->error("Error checking record change: {}", e.what());
        // If we can't determine, assume it's changed to be safe
        return true;
    }
}

std::chrono::system_clock::time_point DeltaLoadingStrategy::get_last_watermark() const {
    return last_watermark_;
}

bool DeltaLoadingStrategy::load_watermark_from_file() {
    if (watermark_file_path_.empty()) {
        return false;
    }
    
    try {
        if (!std::filesystem::exists(watermark_file_path_)) {
            auto logger = common::Logger::get("omop-loading-strategy");
            logger->info("No existing watermark file found at {}, will create new one", watermark_file_path_);
            return false;
        }
        
        std::ifstream file(watermark_file_path_);
        if (!file.is_open()) {
            auto logger = common::Logger::get("omop-loading-strategy");
            logger->error("Cannot open watermark file for reading: {}", watermark_file_path_);
            return false;
        }
        
        nlohmann::json watermark_json;
        file >> watermark_json;
        
        // Load timestamp watermark with UK timezone handling
        if (watermark_json.contains("timestamp")) {
            auto timestamp = watermark_json["timestamp"].get<int64_t>();
            last_watermark_ = std::chrono::system_clock::from_time_t(timestamp);
            
            auto logger = common::Logger::get("omop-loading-strategy");
            
            // Format timestamp in UK format for logging
            auto time_t_val = std::chrono::system_clock::to_time_t(last_watermark_);
            std::tm* uk_time = std::localtime(&time_t_val);
            
            std::ostringstream uk_format;
            uk_format << std::put_time(uk_time, "%d/%m/%Y %H:%M:%S");
            logger->info("Loaded watermark from file: {} (UK time)", uk_format.str());
        }
        
        // Load NHS-specific watermark fields
        if (watermark_json.contains("values")) {
            auto values = watermark_json["values"];
            for (auto& [key, value] : values.items()) {
                if (value.is_string()) {
                    watermark_values_[key] = value.get<std::string>();
                } else if (value.is_number_integer()) {
                    watermark_values_[key] = value.get<int64_t>();
                } else if (value.is_number_float()) {
                    watermark_values_[key] = value.get<double>();
                }
            }
        }
        
        // Load NHS-specific fields if present
        if (watermark_json.contains("nhs_data_centre")) {
            watermark_values_["nhs_data_centre"] = watermark_json["nhs_data_centre"].get<std::string>();
        }
        if (watermark_json.contains("ccg_code")) {
            watermark_values_["ccg_code"] = watermark_json["ccg_code"].get<std::string>();
        }
        
        return true;
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to load watermark from file {}: {}", watermark_file_path_, e.what());
        return false;
    }
}

bool DeltaLoadingStrategy::save_watermark_to_file() {
    if (watermark_file_path_.empty()) {
        return false;
    }
    
    try {
        // Create directory if it doesn't exist
        std::filesystem::create_directories(std::filesystem::path(watermark_file_path_).parent_path());
        
        std::ofstream file(watermark_file_path_);
        if (!file.is_open()) {
            return false;
        }
        
        nlohmann::json watermark_json;
        watermark_json["table"] = config_.target_table;
        watermark_json["timestamp"] = std::chrono::system_clock::to_time_t(last_watermark_);
        watermark_json["last_updated"] = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
        
        // Save additional watermark values
        if (!watermark_values_.empty()) {
            nlohmann::json values;
            for (const auto& [key, value] : watermark_values_) {
                // This is simplified - would need proper any_cast handling for different types
                values[key] = key; // Placeholder
            }
            watermark_json["values"] = values;
        }
        
        file << watermark_json.dump(2);
        return true;
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-loading-strategy");
        logger->error("Failed to save watermark to file {}: {}", watermark_file_path_, e.what());
        return false;
    }
}

std::string DeltaLoadingStrategy::generate_record_hash(const omop::core::Record& record) {
    // Simple hash generation based on record content
    // In production, would use a proper hash algorithm like SHA-256
    std::string content;
    
    if (config_.tracking_columns.empty()) {
        // Hash all fields
        for (const auto& [field_name, field_value] : record.getFields()) {
            content += field_name + ":";
            // Simplified value serialization - would need proper handling for different types
            content += "value"; // Placeholder
            content += ";";
        }
    } else {
        // Hash only tracking columns
        for (const auto& column : config_.tracking_columns) {
            auto field_opt = record.getFieldOptional(column);
            if (field_opt) {
                content += column + ":";
                content += "value"; // Placeholder
                content += ";";
            }
        }
    }
    
    // Simple hash - in production would use std::hash or crypto hash
    return std::to_string(std::hash<std::string>{}(content));
}


// ParallelLoadingStrategy implementation
ParallelLoadingStrategy::ParallelLoadingStrategy() = default;

ParallelLoadingStrategy::~ParallelLoadingStrategy() = default;

bool ParallelLoadingStrategy::initialize(const LoadingConfig& config, omop::core::ProcessingContext& context) { 
    config_ = config;
    metrics_ = LoadingMetrics{};
    start_time_ = std::chrono::steady_clock::now();
    transaction_active_ = false;
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("ParallelLoadingStrategy initialised");
    
    return true; 
}

bool ParallelLoadingStrategy::load_record(const omop::core::Record& record, omop::core::ProcessingContext& context) { 
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Simulate parallel record loading
        std::this_thread::sleep_for(std::chrono::microseconds(75));
        
        metrics_.total_records++;
        metrics_.successful_records++;
        
        return true;
    } catch (const std::exception& e) {
        metrics_.total_records++;
        metrics_.failed_records++;
        
        return false;
    }
}

size_t ParallelLoadingStrategy::load_batch(const omop::core::RecordBatch& batch, omop::core::ProcessingContext& context) { 
    size_t loaded = 0;
    for (const auto& record : batch) {
        if (load_record(record, context)) {
            loaded++;
        }
    }
    return loaded; 
}

bool ParallelLoadingStrategy::begin_transaction(omop::core::ProcessingContext& context) { 
    if (transaction_active_) {
        return false;
    }
    
    transaction_active_ = true;
    return true; 
}

bool ParallelLoadingStrategy::commit_transaction(omop::core::ProcessingContext& context) { 
    if (!transaction_active_) {
        return false;
    }
    
    try {
        // Simulate parallel transaction commit
        std::this_thread::sleep_for(std::chrono::milliseconds(8));
        
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.successful_transactions++;
        
        return true;
    } catch (const std::exception& e) {
        transaction_active_ = false;
        metrics_.total_transactions++;
        metrics_.failed_transactions++;
        
        return false;
    }
}

bool ParallelLoadingStrategy::rollback_transaction(omop::core::ProcessingContext& context) { 
    if (!transaction_active_) {
        return false;
    }
    
    transaction_active_ = false;
    metrics_.total_transactions++;
    metrics_.failed_transactions++;
    
    return true; 
}

bool ParallelLoadingStrategy::finalize(omop::core::ProcessingContext& context) { 
    auto end_time = std::chrono::steady_clock::now();
    metrics_.total_processing_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time_);
    
    auto logger = common::Logger::get("omop-loading-strategy");
    logger->info("ParallelLoadingStrategy finalised. Processed {} records in {} ms", 
                metrics_.total_records, metrics_.total_processing_time.count());
    
    return true; 
}

LoadingMetrics ParallelLoadingStrategy::get_metrics() const { 
    return metrics_; 
}


std::vector<LoadingMode> ParallelLoadingStrategy::get_supported_modes() const { 
    return {LoadingMode::Insert, LoadingMode::Append}; 
}

std::vector<std::string> ParallelLoadingStrategy::validate_config(const LoadingConfig& config) const { 
    std::vector<std::string> errors;
    
    if (config.target_table.empty()) {
        errors.push_back("Target table must be specified");
    }
    
    if (config.worker_threads == 0) {
        errors.push_back("Worker threads must be greater than 0 for parallel loading");
    }
    
    return errors; 
}

// Factory functions
std::unique_ptr<ILoadingStrategy> LoadingStrategyFactory::create_strategy(LoadingStrategy strategy) {
    switch (strategy) {
        case LoadingStrategy::SingleRecord:
            return std::make_unique<SingleRecordLoadingStrategy>();
        case LoadingStrategy::BatchInsert:
            return std::make_unique<BatchInsertLoadingStrategy>();
        case LoadingStrategy::BulkLoad:
            return std::make_unique<BulkLoadStrategy>();
        case LoadingStrategy::UpsertLoad:
            return std::make_unique<UpsertLoadingStrategy>();
        case LoadingStrategy::DeltaLoad:
            return std::make_unique<DeltaLoadingStrategy>();
        case LoadingStrategy::ParallelLoad:
            return std::make_unique<ParallelLoadingStrategy>();
        default:
            return std::make_unique<SingleRecordLoadingStrategy>();
    }
}

void LoadingStrategyFactory::register_strategy(
    LoadingStrategy strategy,
    std::function<std::unique_ptr<ILoadingStrategy>()> creator) {
    // Implementation for custom strategy registration
}

std::vector<LoadingStrategy> LoadingStrategyFactory::get_available_strategies() {
    return {
        LoadingStrategy::SingleRecord,
        LoadingStrategy::BatchInsert,
        LoadingStrategy::BulkLoad,
        LoadingStrategy::UpsertLoad,
        LoadingStrategy::DeltaLoad,
        LoadingStrategy::ParallelLoad
    };
}

LoadingStrategy LoadingStrategyFactory::get_recommended_strategy(size_t record_count, const std::string& target_system) {
    if (record_count > 1000000) {
        return LoadingStrategy::BulkLoad;
    } else if (record_count > 100000) {
        return LoadingStrategy::ParallelLoad;
    } else if (record_count > 10000) {
        return LoadingStrategy::BatchInsert;
    } else {
        return LoadingStrategy::SingleRecord;
    }
}

std::string LoadingStrategyUtils::strategy_to_string(LoadingStrategy strategy) {
    switch (strategy) {
        case LoadingStrategy::SingleRecord:
            return "single_record";
        case LoadingStrategy::BatchInsert:
            return "batch_insert";
        case LoadingStrategy::BulkLoad:
            return "bulk_load";
        case LoadingStrategy::UpsertLoad:
            return "upsert_load";
        case LoadingStrategy::DeltaLoad:
            return "delta_load";
        case LoadingStrategy::ParallelLoad:
            return "parallel_load";
        default:
            return "unknown";
    }
}

LoadingStrategy LoadingStrategyUtils::string_to_strategy(const std::string& strategy_str) {
    if (strategy_str == "single_record") return LoadingStrategy::SingleRecord;
    if (strategy_str == "batch_insert") return LoadingStrategy::BatchInsert;
    if (strategy_str == "bulk_load") return LoadingStrategy::BulkLoad;
    if (strategy_str == "upsert_load") return LoadingStrategy::UpsertLoad;
    if (strategy_str == "delta_load") return LoadingStrategy::DeltaLoad;
    if (strategy_str == "parallel_load") return LoadingStrategy::ParallelLoad;
    return LoadingStrategy::SingleRecord;
}

std::string LoadingStrategyUtils::mode_to_string(LoadingMode mode) {
    switch (mode) {
        case LoadingMode::Insert:
            return "insert";
        case LoadingMode::Update:
            return "update";
        case LoadingMode::Delete:
            return "delete";
        case LoadingMode::Upsert:
            return "upsert";
        case LoadingMode::Append:
            return "append";
        case LoadingMode::Replace:
            return "replace";
        case LoadingMode::Merge:
            return "merge";
        default:
            return "unknown";
    }
}

LoadingMode LoadingStrategyUtils::string_to_mode(const std::string& mode_str) {
    if (mode_str == "insert") return LoadingMode::Insert;
    if (mode_str == "update") return LoadingMode::Update;
    if (mode_str == "delete") return LoadingMode::Delete;
    if (mode_str == "upsert") return LoadingMode::Upsert;
    if (mode_str == "append") return LoadingMode::Append;
    if (mode_str == "replace") return LoadingMode::Replace;
    if (mode_str == "merge") return LoadingMode::Merge;
    return LoadingMode::Insert;
}

LoadingConfig LoadingStrategyUtils::get_default_config(LoadingStrategy strategy) {
    LoadingConfig config;
    
    switch (strategy) {
        case LoadingStrategy::SingleRecord:
            config.batch_size = 1;
            config.worker_threads = 1;
            break;
        case LoadingStrategy::BatchInsert:
            config.batch_size = 1000;
            config.worker_threads = 1;
            break;
        case LoadingStrategy::BulkLoad:
            config.batch_size = 10000;
            config.worker_threads = 1;
            break;
        case LoadingStrategy::UpsertLoad:
            config.batch_size = 500;
            config.worker_threads = 1;
            break;
        case LoadingStrategy::DeltaLoad:
            config.batch_size = 1000;
            config.worker_threads = 1;
            config.enable_cdc = true;
            config.enable_hash_comparison = true;
            // Set UK-specific defaults for NHS data
            config.watermark_column = "last_updated_dttm";
            break;
        case LoadingStrategy::ParallelLoad:
            config.batch_size = 1000;
            config.worker_threads = 4;
            break;
        default:
            config.batch_size = 1000;
            config.worker_threads = 1;
            break;
    }
    
    return config;
}

LoadingConfig LoadingStrategyUtils::optimize_config(
    const LoadingConfig& config,
    size_t record_count,
    const std::unordered_map<std::string, std::any>& system_info) {
    
    LoadingConfig optimised = config;
    
    // Use common optimization utility
    auto performance_requirements = std::unordered_map<std::string, std::any>{
        {"record_count", record_count}
    };
    
    auto optimized_params = omop::common::OptimizationUtils::optimize_config(
        std::unordered_map<std::string, std::any>{
            {"batch_size", config.batch_size},
            {"worker_threads", config.worker_threads}
        },
        system_info,
        performance_requirements
    );
    
    // Apply common optimizations
    if (optimized_params.count("max_workers")) {
        optimised.worker_threads = std::any_cast<size_t>(optimized_params.at("max_workers"));
    }
    
    if (optimized_params.count("buffer_size")) {
        size_t buffer_size = std::any_cast<size_t>(optimized_params.at("buffer_size"));
        if (buffer_size > 0) {
            optimised.batch_size = std::max(optimised.batch_size, buffer_size / 1024);
        }
    }
    
    // Apply strategy-specific optimizations based on record count
    if (record_count > 1000000) {
        optimised.batch_size = std::max(optimised.batch_size, size_t(10000));
        optimised.worker_threads = std::max(optimised.worker_threads, size_t(8));
    } else if (record_count > 100000) {
        optimised.batch_size = std::max(optimised.batch_size, size_t(5000));
        optimised.worker_threads = std::max(optimised.worker_threads, size_t(4));
    }
    
    return optimised;
}

size_t LoadingStrategyUtils::calculate_optimal_batch_size(
    size_t record_size,
    size_t available_memory,
    const std::string& target_system) {
    
    // Use common optimization utility with strategy-specific adjustments
    size_t base_optimal_size = omop::common::OptimizationUtils::calculate_optimal_batch_size(
        record_size, available_memory, target_system);
    
    // Apply strategy-specific adjustments
    if (target_system == "postgresql") {
        base_optimal_size = std::min(base_optimal_size, size_t(10000));
    } else if (target_system == "mysql") {
        base_optimal_size = std::min(base_optimal_size, size_t(5000));
    } else if (target_system == "sqlite") {
        base_optimal_size = std::min(base_optimal_size, size_t(1000));
    }
    
    return std::max(base_optimal_size, size_t(100));
}

} // namespace omop::load