#pragma once

/**
 * @file loader_config.h
 * @brief Configuration flags for loader components
 *
 * This file defines configuration flags that control the behavior of loader components,
 * particularly for separating production and test/mock functionality.
 */

#include <string>

namespace omop::load {

/**
 * @brief Configuration flags for loader behavior
 */
struct LoaderConfig {
    /**
     * @brief Whether mock mode is enabled
     * 
     * When enabled, allows mock endpoints and test-specific behavior.
     * Should be disabled in production builds.
     */
    static constexpr bool MOCK_MODE_ENABLED = 
#ifdef OMOP_ENABLE_MOCK_MODE
        true;
#else
        false;
#endif

    /**
     * @brief Whether test dependencies are enabled
     * 
     * When enabled, includes test-specific functionality and dependencies.
     * Should be disabled in production builds.
     */
    static constexpr bool TEST_DEPENDENCIES_ENABLED = 
#ifdef OMOP_ENABLE_TEST_DEPENDENCIES
        true;
#else
        false;
#endif

    /**
     * @brief Mock endpoint identifiers
     */
    struct MockEndpoints {
        static constexpr const char* HTTP_TEST = "mock://test";
        static constexpr const char* S3_TEST = "mock://s3";
        static constexpr const char* S3_BUCKET = "mock-bucket";
        static constexpr const char* TEST_ACCESS_KEY = "test_key";
        static constexpr const char* TEST_SECRET_KEY = "test_secret";
    };

    /**
     * @brief Check if a given endpoint is a mock endpoint
     * @param endpoint Endpoint to check
     * @return true if the endpoint is a mock endpoint
     */
    static bool is_mock_endpoint(const std::string& endpoint) {
        if (!MOCK_MODE_ENABLED) {
            return false;
        }
        
        return endpoint == MockEndpoints::HTTP_TEST ||
               endpoint == MockEndpoints::S3_TEST ||
               endpoint == MockEndpoints::S3_BUCKET;
    }

    /**
     * @brief Check if given credentials are test credentials
     * @param access_key Access key to check
     * @param secret_key Secret key to check
     * @return true if the credentials are test credentials
     */
    static bool are_test_credentials(const std::string& access_key, const std::string& secret_key) {
        if (!TEST_DEPENDENCIES_ENABLED) {
            return false;
        }
        
        return access_key == MockEndpoints::TEST_ACCESS_KEY &&
               secret_key == MockEndpoints::TEST_SECRET_KEY;
    }

    /**
     * @brief Get mock mode status message
     * @return Status message indicating mock mode status
     */
    static std::string get_mock_mode_status() {
        if (MOCK_MODE_ENABLED) {
            return "Mock mode enabled - test endpoints and mock functionality available";
        } else {
            return "Mock mode disabled - production mode only";
        }
    }
};

} // namespace omop::load
