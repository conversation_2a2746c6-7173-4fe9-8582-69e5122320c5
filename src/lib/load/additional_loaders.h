#pragma once

#include "load/batch_loader.h"
#include "load/loader_base.h"
#include "common/http_client.h"
#include <nlohmann/json.hpp>

namespace omop::load {

/**
 * @brief JSON batch loader
 *
 * Specialised batch loader for JSON file output with support for
 * nested structures and pretty printing.
 */
class JsonBatchLoader : public BatchLoader {
public:
    /**
     * @brief JSON output options
     */
    struct JsonOptions {
        bool pretty_print{true};
        int indent_size{2};
        bool include_metadata{true};
        bool array_output{true};  // If true, output as array; if false, as NDJSON
        std::string date_format{"%Y-%m-%d %H:%M:%S"};
    };

    /**
     * @brief Constructor
     * @param options Batch loader options
     * @param json_options JSON-specific options
     */
    JsonBatchLoader(BatchLoaderOptions options,
                    JsonOptions json_options);

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "json_batch"; }

protected:
    /**
     * @brief Process a batch by writing to JSON
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                        core::ProcessingContext& context) override;

    /**
     * @brief Initialize JSON batch loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Finalize JSON output
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

private:
    /**
     * @brief Convert record to JSON object
     * @param record Record to convert
     * @return nlohmann::json JSON object
     */
    nlohmann::json record_to_json(const core::Record& record);

    /**
     * @brief Convert std::any value to JSON
     * @param value Value to convert
     * @return nlohmann::json JSON value
     */
    nlohmann::json any_to_json(const std::any& value);

    JsonOptions json_options_;
    std::string output_file_;
    std::ofstream output_stream_;
    std::mutex output_mutex_;
    bool first_batch_{true};
    nlohmann::json json_array_;  // For array output mode
};

/**
 * @brief HTTP/REST API loader
 *
 * Network-based loader that sends data to HTTP endpoints.
 * 
 * This loader provides production-ready HTTP functionality with:
 * - Full libcurl integration for robust HTTP/HTTPS support
 * - Comprehensive error handling and retry logic
 * - Authentication support (Basic, Bearer, API Key)
 * - Compression support
 * - Batch processing with configurable thresholds
 * - Connection pooling and timeout management
 */
class HttpLoader : public NetworkLoaderBase {
public:
    /**
     * @brief HTTP options
     */
    struct HttpOptions {
        std::string method{"POST"};
        std::unordered_map<std::string, std::string> headers;
        std::string content_type{"application/json"};
        size_t timeout_seconds{30};
        size_t retry_count{3};
        size_t retry_delay_ms{1000};
        bool use_compression{true};
        std::string auth_type;  // "basic", "bearer", "apikey"
        std::string auth_credentials;
    };

    /**
     * @brief Constructor
     * @param options HTTP options
     */
    explicit HttpLoader(HttpOptions options);

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "http"; }

    /**
     * @brief Finalize the loader
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Connect to HTTP endpoint
     * @param endpoint Endpoint URL
     * @param timeout Connection timeout
     */
    void connect(const std::string& endpoint,
                std::chrono::seconds timeout) override;

    /**
     * @brief Disconnect from endpoint
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Send data over HTTP
     * @param data Data to send
     * @param timeout Send timeout
     * @return bool True if successful
     */
    bool send_data(const std::string& data,
                  std::chrono::seconds timeout) override;

private:
    /**
     * @brief Format batch as JSON payload
     * @param batch Batch to format
     * @return std::string JSON payload
     */
    std::string format_batch_payload(const core::RecordBatch& batch);

    /**
     * @brief Send HTTP request with retries
     * @param payload Request payload
     * @return bool True if successful
     */
    bool send_with_retry(const std::string& payload);

    /**
     * @brief Convert string HTTP method to HttpClient::Method enum
     * @param method_str String representation of HTTP method
     * @return HttpClient::Method Enum value
     */
    common::HttpClient::Method get_http_method(const std::string& method_str);

    HttpOptions http_options_;
    std::atomic<bool> connected_{false};

    // Buffering for batch sending
    std::vector<core::Record> pending_records_;
    std::mutex pending_mutex_;
    size_t batch_threshold_{100};
    
    // HTTP client components
    std::unique_ptr<common::HttpClient> http_client_;
};

/**
 * @brief Multi-format loader
 *
 * Loader that can output to multiple formats simultaneously.
 */
class MultiFormatLoader : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     */
    explicit MultiFormatLoader(const std::string& name = "multi_format");

    /**
     * @brief Add a sub-loader
     * @param loader Loader to add
     * @param weight Relative weight for load distribution (default 1.0)
     */
    void add_loader(std::unique_ptr<core::ILoader> loader, double weight = 1.0);

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded by all loaders
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "multi_format"; }

    /**
     * @brief Get number of sub-loaders
     * @return size_t Number of loaders
     */
    size_t loader_count() const { return loaders_.size(); }

protected:
    /**
     * @brief Initialize all sub-loaders
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Finalize all sub-loaders
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics from all loaders
     * @return std::unordered_map<std::string, std::any> Combined statistics
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

private:
    struct LoaderInfo {
        std::unique_ptr<core::ILoader> loader;
        double weight;
        size_t success_count{0};
        size_t failure_count{0};
    };

    std::vector<LoaderInfo> loaders_;
    bool fail_on_any_{false};  // If true, fail if any loader fails
    bool parallel_load_{true};  // If true, load to all loaders in parallel
};

/**
 * @brief S3-compatible object storage loader
 *
 * Loader for writing to S3-compatible object storage systems.
 * 
 * This loader provides S3 functionality with:
 * - Mock mode for testing and development
 * - Configurable multipart upload support
 * - UK region compliance (eu-west-2)
 * - Server-side encryption support
 * - Comprehensive error handling and retry logic
 * - Batch processing with configurable thresholds
 * 
 * Note: For production use with real S3, AWS SDK for C++ integration is required.
 * The current implementation supports mock mode for testing and development.
 */
class S3Loader : public NetworkLoaderBase {
public:
    /**
     * @brief S3 options
     */
    struct S3Options {
        std::string bucket_name;
        std::string key_prefix;
        std::string region{"us-east-1"};
        std::string access_key_id;
        std::string secret_access_key;
        std::string session_token;  // Optional for temporary credentials
        bool use_multipart_upload{true};
        size_t multipart_threshold{5 * 1024 * 1024};  // 5MB
        size_t part_size{5 * 1024 * 1024};  // 5MB
        std::string storage_class{"STANDARD"};
        bool server_side_encryption{false};
        std::string sse_algorithm{"AES256"};
        std::unordered_map<std::string, std::string> metadata;
        size_t retry_count{3};
        size_t timeout_seconds{30};
        size_t retry_delay_ms{1000};
    };

    /**
     * @brief Constructor
     * @param options S3 options
     */
    explicit S3Loader(S3Options options);

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "s3"; }

    /**
     * @brief Finalize the loader
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Connect to S3 endpoint
     * @param endpoint Endpoint URL
     * @param timeout Connection timeout
     */
    void connect(const std::string& endpoint,
                std::chrono::seconds timeout) override;

    /**
     * @brief Disconnect from S3
     */
    void disconnect() override;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    bool is_connected() const override;

    /**
     * @brief Send data to S3
     * @param data Data to send
     * @param timeout Send timeout
     * @return bool True if successful
     */
    bool send_data(const std::string& data,
                  std::chrono::seconds timeout) override;

private:
    /**
     * @brief Generate S3 object key
     * @param suffix Key suffix
     * @return std::string Full object key
     */
    std::string generate_object_key(const std::string& suffix = "");

    /**
     * @brief Upload buffer to S3
     * @param key Object key
     * @param data Data to upload
     * @return bool True if successful
     */
    bool upload_to_s3(const std::string& key, const std::string& data);

    /**
     * @brief Start multipart upload
     * @param key Object key
     * @return std::string Upload ID
     */
    std::string start_multipart_upload(const std::string& key);

    /**
     * @brief Upload part
     * @param key Object key
     * @param upload_id Upload ID
     * @param part_number Part number
     * @param data Part data
     * @return std::string ETag
     */
    std::string upload_part(const std::string& key,
                           const std::string& upload_id,
                           int part_number,
                           const std::string& data);

    /**
     * @brief Complete multipart upload
     * @param key Object key
     * @param upload_id Upload ID
     * @param parts Part ETags
     * @return bool True if successful
     */
    bool complete_multipart_upload(const std::string& key,
                                  const std::string& upload_id,
                                  const std::vector<std::pair<int, std::string>>& parts);

    /**
     * @brief Format batch as S3 payload
     * @param batch Batch to format
     * @return std::string Formatted payload
     */
    std::string format_batch_payload(const core::RecordBatch& batch);

    /**
     * @brief Send data with retry logic
     * @param payload Data payload
     * @return bool True if successful
     */
    bool send_with_retry(const std::string& payload);

    S3Options s3_options_;
    std::string current_key_;
    std::string current_upload_id_;
    std::vector<std::pair<int, std::string>> uploaded_parts_;
    std::ostringstream buffer_;
    size_t buffer_size_{0};
    int next_part_number_{1};
    std::mutex upload_mutex_;
};

} // namespace omop::load