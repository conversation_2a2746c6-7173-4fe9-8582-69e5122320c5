# Load library CMakeLists.txt

add_library(omop_load STATIC)

set(LOAD_SOURCES
    additional_loaders.cpp
    batch_loader.cpp
    batch_inserter.cpp
    database_loader.cpp
    loader_base.cpp
    loader_strategies.cpp
)

set(LOAD_HEADERS
    additional_loaders.h
    batch_loader.h
    database_loader.h
    loader_base.h
    batch_inserter.h
    loader_strategies.h
    loader_config.h
)

target_sources(omop_load PRIVATE ${LOAD_SOURCES})

# Set preprocessor definitions based on build options
if(ENABLE_MOCK_MODE)
    target_compile_definitions(omop_load PRIVATE OMOP_ENABLE_MOCK_MODE)
endif()

if(ENABLE_TEST_DEPENDENCIES)
    target_compile_definitions(omop_load PRIVATE OMOP_ENABLE_TEST_DEPENDENCIES)
endif()

omop_configure_library(omop_load
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    PUBLIC_DEPS
        omop_extract
        omop_core
        omop_common
        omop_cdm
    PRIVATE_DEPS
        nlohmann_json::nlohmann_json
        spdlog::spdlog
        fmt::fmt
        Threads::Threads
    HEADERS
        ${LOAD_HEADERS}
)

# Set include directories
target_include_directories(omop_load
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/src/lib
)