#pragma once

#include "core/interfaces.h"
#include "common/exceptions.h"
#include <chrono>
#include <atomic>
#include <unordered_map>
#include <any>
#include <fstream>
#include <mutex>
#include <vector>
#include <deque>

namespace omop::load {

/**
 * @brief Base class for all loader implementations
 *
 * Provides common functionality and statistics tracking for data loaders.
 * This abstract base class implements the ILoader interface and provides
 * shared functionality for derived loader classes.
 */
class LoaderBase : public core::ILoader {
public:
    /**
     * @brief Constructor
     * @param name Loader name for identification
     */
    explicit LoaderBase(const std::string& name);

    /**
     * @brief Virtual destructor
     */
    virtual ~LoaderBase() = default;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Get loader statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Finalize the loader
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get loader name
     * @return const std::string& Loader name
     */
    const std::string& get_name() const { return name_; }

    // Disable move semantics (due to std::atomic members)
    LoaderBase(LoaderBase&&) = delete;
    LoaderBase& operator=(LoaderBase&&) = delete;
    
    // Disable copy semantics
    LoaderBase(const LoaderBase&) = delete;
    LoaderBase& operator=(const LoaderBase&) = delete;

    /**
     * @brief Check if loader is initialised
     * @return bool True if initialised
     */
    bool is_initialised() const { return initialised_; }

protected:
    /**
     * @brief Perform loader-specific initialization
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void do_initialize(const std::unordered_map<std::string, std::any>& config,
                              core::ProcessingContext& context) = 0;

    /**
     * @brief Perform loader-specific finalization
     * @param context Processing context
     */
    virtual void do_finalize([[maybe_unused]] core::ProcessingContext& context) {}

    /**
     * @brief Get loader-specific statistics
     * @return std::unordered_map<std::string, std::any> Additional statistics
     */
    virtual std::unordered_map<std::string, std::any> get_additional_statistics() const {
        return {};
    }

    /**
     * @brief Update loading progress
     * @param loaded Number of successfully loaded records
     * @param failed Number of failed records
     */
    void update_progress(size_t loaded, size_t failed = 0);

    /**
     * @brief Record an error
     * @param error_message Error message
     * @param record_info Optional record information
     */
    void record_error(const std::string& error_message,
                     const std::string& record_info = "");

    /**
     * @brief Get elapsed time since initialization
     * @return std::chrono::duration<double> Elapsed time in seconds
     */
    std::chrono::duration<double> get_elapsed_time() const;

    /**
     * @brief Check if configuration contains a key
     * @param config Configuration map
     * @param key Key to check
     * @return bool True if key exists
     */
    bool has_config_key(const std::unordered_map<std::string, std::any>& config,
                       const std::string& key) const;

    /**
     * @brief Get configuration value with type checking
     * @tparam T Expected value type
     * @param config Configuration map
     * @param key Configuration key
     * @param default_value Default value if key not found
     * @return T Configuration value
     */
    template<typename T>
    T get_config_value(const std::unordered_map<std::string, std::any>& config,
                      const std::string& key,
                      const T& default_value) const {
        auto it = config.find(key);
        if (it != config.end()) {
            try {
                return std::any_cast<T>(it->second);
            } catch (const std::bad_any_cast& e) {
                throw common::ConfigurationException(
                    std::format("Invalid type for configuration key '{}': {}", key, e.what()));
            }
        }
        return default_value;
    }

private:
    std::string name_;
    bool initialised_{false};

    // Statistics tracking
    std::atomic<size_t> total_loaded_{0};
    std::atomic<size_t> total_failed_{0};
    std::atomic<size_t> total_processed_{0};
    std::chrono::steady_clock::time_point start_time_;
    std::chrono::steady_clock::time_point end_time_;

    // Error tracking
    mutable std::mutex error_mutex_;
    std::deque<std::pair<std::string, std::chrono::steady_clock::time_point>> errors_;
    size_t total_errors_recorded_{0};  // Track total errors even if not all stored
    static constexpr size_t MAX_ERRORS_TO_TRACK = 100;
};

/**
 * @brief File-based loader base class
 *
 * Base class for loaders that write to files (CSV, JSON, etc.)
 */
class FileLoaderBase : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * @param file_extension Default file extension
     */
    FileLoaderBase(const std::string& name, const std::string& file_extension);

    /**
     * @brief Destructor - ensures file is closed
     */
    ~FileLoaderBase() override;

protected:
    /**
     * @brief Perform file loader initialization
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Perform file loader finalization
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics for file loader
     * @return std::unordered_map<std::string, std::any> File-specific statistics
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

    /**
     * @brief Open output file
     * @param filename File path
     * @param append Whether to append to existing file
     */
    virtual void open_file(const std::string& filename, bool append = false);

    /**
     * @brief Close output file
     */
    virtual void close_file();

    /**
     * @brief Write data to file
     * @param data Data to write
     */
    virtual void write_to_file(const std::string& data);

    /**
     * @brief Flush file buffer
     */
    virtual void flush_file();

    /**
     * @brief Get output file path
     * @return const std::string& File path
     */
    const std::string& get_file_path() const { return file_path_; }

    /**
     * @brief Check if file is open
     * @return bool True if file is open
     */
    bool is_file_open() const { return file_stream_.is_open(); }

    /**
     * @brief Get file extension
     * @return const std::string& File extension
     */
    const std::string& get_file_extension() const { return file_extension_; }

private:
    std::string file_extension_;
    std::string file_path_;
    std::ofstream file_stream_;
    std::atomic<size_t> bytes_written_{0};
    mutable std::mutex file_mutex_;
};

/**
 * @brief Network-based loader base class
 *
 * Base class for loaders that send data over network (HTTP, message queues, etc.)
 */
class NetworkLoaderBase : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * @param protocol Network protocol (http, amqp, kafka, etc.)
     */
    NetworkLoaderBase(const std::string& name, const std::string& protocol);

protected:
    /**
     * @brief Perform network loader initialization
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics for network loader
     * @return std::unordered_map<std::string, std::any> Network-specific statistics
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

    /**
     * @brief Connect to remote endpoint
     * @param endpoint Endpoint URL or address
     * @param timeout Connection timeout
     */
    virtual void connect(const std::string& endpoint,
                        std::chrono::seconds timeout = std::chrono::seconds(30)) = 0;

    /**
     * @brief Disconnect from remote endpoint
     */
    virtual void disconnect() = 0;

    /**
     * @brief Check if connected
     * @return bool True if connected
     */
    virtual bool is_connected() const = 0;

    /**
     * @brief Send data over network
     * @param data Data to send
     * @param timeout Send timeout
     * @return bool True if successful
     */
    virtual bool send_data(const std::string& data,
                          std::chrono::seconds timeout = std::chrono::seconds(30)) = 0;

    /**
     * @brief Update network statistics
     * @param bytes_sent Bytes sent
     * @param success Whether send was successful
     */
    void update_network_stats(size_t bytes_sent, bool success);

    /**
     * @brief Get endpoint URL
     * @return const std::string& Endpoint
     */
    const std::string& get_endpoint() const { return endpoint_; }

    /**
     * @brief Get protocol name
     * @return const std::string& Protocol
     */
    const std::string& get_protocol() const { return protocol_; }

private:
    std::string protocol_;
    std::string endpoint_;

    // Network statistics
    std::atomic<size_t> total_bytes_sent_{0};
    std::atomic<size_t> successful_sends_{0};
    std::atomic<size_t> failed_sends_{0};
    std::atomic<size_t> connection_failures_{0};
    std::chrono::steady_clock::time_point last_connected_;
    std::chrono::steady_clock::time_point last_disconnected_;
};

} // namespace omop::load