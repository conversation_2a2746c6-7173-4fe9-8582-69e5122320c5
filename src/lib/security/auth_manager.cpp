#include <any>
#include <optional>
#include "auth_manager.h"
#include "common/logging.h"
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <random>
#include <regex>
#include <mutex>
#include <unordered_set>

// For password hashing
#ifdef _WIN32
#include <windows.h>
#include <bcrypt.h>
#pragma comment(lib, "bcrypt.lib")
#else
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/sha.h>
#endif

namespace omop::security {

namespace {
    constexpr size_t TOKEN_LENGTH = 32;
    constexpr size_t SALT_LENGTH = 16;
    constexpr int PBKDF2_ITERATIONS = 100000;

    std::string generate_random_bytes(size_t length) {
        std::string result(length, '\0');
#ifdef _WIN32
        BCRYPT_ALG_HANDLE hAlgorithm;
        if (BCryptOpenAlgorithmProvider(&hAlgorithm, BCRYPT_RNG_ALGORITHM, nullptr, 0) == 0) {
            BCryptGenRandom(hAlgorithm, reinterpret_cast<PUCHAR>(&result[0]), static_cast<ULONG>(length), 0);
            BCryptCloseAlgorithmProvider(hAlgorithm, 0);
        }
#else
        RAND_bytes(reinterpret_cast<unsigned char*>(&result[0]), length);
#endif
        return result;
    }

    std::string bytes_to_hex(const std::string& bytes) {
        std::stringstream ss;
        ss << std::hex << std::setfill('0');
        for (unsigned char c : bytes) {
            ss << std::setw(2) << static_cast<int>(c);
        }
        return ss.str();
    }

    std::string hex_to_bytes(const std::string& hex) {
        std::string bytes;
        for (size_t i = 0; i < hex.length(); i += 2) {
            std::string byte_string = hex.substr(i, 2);
            char byte = static_cast<char>(std::strtol(byte_string.c_str(), nullptr, 16));
            bytes.push_back(byte);
        }
        return bytes;
    }

    std::string hash_password_impl(const std::string& password, const std::string& salt) {
        std::string hash;
#ifdef _WIN32
        BCRYPT_ALG_HANDLE hAlgorithm;
        if (BCryptOpenAlgorithmProvider(&hAlgorithm, BCRYPT_SHA512_ALGORITHM, nullptr, 0) != 0) {
            return "";
        }
        ULONG hash_length = 64;
        hash.resize(hash_length);
        if (BCryptDeriveKeyPBKDF2(hAlgorithm,
                                  reinterpret_cast<PUCHAR>(const_cast<char*>(password.c_str())),
                                  static_cast<ULONG>(password.length()),
                                  reinterpret_cast<PUCHAR>(const_cast<char*>(salt.c_str())),
                                  static_cast<ULONG>(salt.length()),
                                  PBKDF2_ITERATIONS,
                                  reinterpret_cast<PUCHAR>(&hash[0]),
                                  hash_length, 0) != 0) {
            BCryptCloseAlgorithmProvider(hAlgorithm, 0);
            return "";
        }
        BCryptCloseAlgorithmProvider(hAlgorithm, 0);
        return hash;
#else
        hash.resize(64, '\0');
        if (PKCS5_PBKDF2_HMAC(password.c_str(), password.length(),
                              reinterpret_cast<const unsigned char*>(salt.c_str()), salt.length(),
                              PBKDF2_ITERATIONS, EVP_sha512(),
                              hash.length(), reinterpret_cast<unsigned char*>(&hash[0])) != 1) {
            return "";
        }
        return hash;
#endif
    }

    bool verify_password_impl(const std::string& password, const std::string& stored_hash, const std::string& salt) {
        std::string computed_hash = hash_password_impl(password, salt);
        return computed_hash == stored_hash;
    }

    bool is_password_strong(const std::string& password) {
        if (password.length() < 8) return false;
        bool has_upper = false, has_lower = false, has_digit = false, has_special = false;
        for (char c : password) {
            if (std::isupper(c)) has_upper = true;
            else if (std::islower(c)) has_lower = true;
            else if (std::isdigit(c)) has_digit = true;
            else has_special = true;
        }
        return has_upper && has_lower && has_digit && has_special;
    }
}

AuthManager::AuthManager() : initialised_(false) {
    logger_ = omop::common::Logger::get("auth_manager");
}

AuthManager::~AuthManager() = default;

bool AuthManager::initialize(const AuthConfig& config) {
    std::lock_guard<std::mutex> lock(auth_mutex_);
    config_ = config;
    if (!config_.enabled) {
        initialised_ = true;
        return true;
    }
    try {
        logger_->info("AuthManager initialised");
        if (config_.create_default_admin) {
            create_default_admin_user();
        }
        initialised_ = true;
        return true;
    } catch (const std::exception& e) {
        logger_->error("Failed to initialize AuthManager: {}", e.what());
        return false;
    }
}

void AuthManager::create_default_admin_user() {
    UserInfo admin_user;
    admin_user.user_id = "admin";
    admin_user.username = "admin";
    admin_user.email = "<EMAIL>";
    admin_user.roles = {"admin"};
    admin_user.status = UserStatus::Active;
    admin_user.created_at = std::chrono::system_clock::now();
    std::string salt = generate_random_bytes(SALT_LENGTH);
    std::string hash = hash_password_impl("admin123", salt);
    users_["admin"] = admin_user;
    password_hashes_["admin"] = hash;
    password_salts_["admin"] = salt;
    logger_->info("Created default admin user");
}

std::pair<AuthResult, std::optional<AuthToken>> AuthManager::authenticate(const AuthCredentials& credentials) {
    if (!initialised_ || !config_.enabled) {
        return {AuthResult::SystemError, std::nullopt};
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        auto user_it = std::find_if(users_.begin(), users_.end(),
            [&](const auto& pair) { return pair.second.username == credentials.username; });
        if (user_it == users_.end()) {
            logger_->warn("Authentication failed: user not found: {}", credentials.username);
            return {AuthResult::UserNotAuthenticated, std::nullopt};
        }
        const auto& user = user_it->second;
        if (!user.is_active) {
            logger_->warn("Authentication failed: user not active: {}", credentials.username);
            return {AuthResult::UserNotActive, std::nullopt};
        }
        if (user.status == UserStatus::Locked) {
            logger_->warn("Authentication failed: user locked: {}", credentials.username);
            return {AuthResult::AccountLocked, std::nullopt};
        }
        auto hash_it = password_hashes_.find(user.user_id);
        auto salt_it = password_salts_.find(user.user_id);
        if (hash_it == password_hashes_.end() || salt_it == password_salts_.end()) {
            logger_->error("User password data not found: {}", user.user_id);
            return {AuthResult::SystemError, std::nullopt};
        }
        if (!verify_password_impl(credentials.password, hash_it->second, salt_it->second)) {
            logger_->warn("Authentication failed: invalid password for user: {}", credentials.username);
            return {AuthResult::UserNotAuthenticated, std::nullopt};
        }
        AuthToken token;
        token.token = bytes_to_hex(generate_random_bytes(TOKEN_LENGTH));
        token.token_type = "Bearer";
        token.issued_at = std::chrono::system_clock::now();
        token.expires_at = token.issued_at + config_.token_lifetime;
        token.issuer = "omop-etl";
        token.subject = user.user_id;
        token.audience = {"omop-etl-api"};
        token.scopes = user.roles;
        token.claims["username"] = user.username;
        token.claims["email"] = user.email;
        token.claims["is_active"] = user.is_active;
        token.claims["is_locked"] = user.is_locked;
        active_tokens_[token.token] = token;
        user_sessions_[user.user_id].push_back(token.token);
        logger_->info("User {} authenticated successfully", credentials.username);
        return {AuthResult::Success, token};
    } catch (const std::exception& e) {
        logger_->error("Authentication error for user {}: {}", credentials.username, e.what());
        return {AuthResult::InternalError, std::nullopt};
    }
}

std::pair<AuthResult, std::optional<UserInfo>> AuthManager::validate_token(const std::string& token) {
    if (!initialised_ || !config_.enabled) {
        return {AuthResult::SystemError, std::nullopt};
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        if (revoked_tokens_.find(token) != revoked_tokens_.end()) {
            logger_->warn("Token validation failed: token is revoked");
            return {AuthResult::TokenRevoked, std::nullopt};
        }
        auto token_it = active_tokens_.find(token);
        if (token_it == active_tokens_.end()) {
            logger_->warn("Token validation failed: token not found");
            return {AuthResult::InvalidToken, std::nullopt};
        }
        const auto& auth_token = token_it->second;
        auto now = std::chrono::system_clock::now();
        if (auth_token.expires_at < now) {
            active_tokens_.erase(token_it);
            logger_->warn("Token validation failed: token expired");
            return {AuthResult::TokenExpired, std::nullopt};
        }
        auto user_it = users_.find(auth_token.subject);
        if (user_it == users_.end()) {
            logger_->error("Token validation failed: user not found: {}", auth_token.subject);
            return {AuthResult::UserNotFound, std::nullopt};
        }
        logger_->debug("Token validated successfully for user: {}", auth_token.subject);
        return {AuthResult::Success, user_it->second};
    } catch (const std::exception& e) {
        logger_->error("Token validation error: {}", e.what());
        return {AuthResult::InternalError, std::nullopt};
    }
}

std::pair<AuthResult, std::optional<AuthToken>> AuthManager::refresh_token(const std::string& refresh_token) {
    if (!initialised_ || !config_.enabled) {
        return {AuthResult::SystemError, std::nullopt};
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        auto validation_result = validate_token(refresh_token);
        if (validation_result.first != AuthResult::Success) {
            return {validation_result.first, std::nullopt};
        }
        auto token_it = active_tokens_.find(refresh_token);
        if (token_it == active_tokens_.end()) {
            return {AuthResult::InvalidToken, std::nullopt};
        }
        const auto& current_token = token_it->second;
        AuthToken new_token;
        new_token.token = bytes_to_hex(generate_random_bytes(TOKEN_LENGTH));
        new_token.token_type = "Bearer";
        new_token.issued_at = std::chrono::system_clock::now();
        new_token.expires_at = new_token.issued_at + config_.token_lifetime;
        new_token.issuer = current_token.issuer;
        new_token.subject = current_token.subject;
        new_token.audience = current_token.audience;
        new_token.scopes = current_token.scopes;
        new_token.claims = current_token.claims;
        revoked_tokens_.insert(refresh_token);
        active_tokens_.erase(token_it);
        active_tokens_[new_token.token] = new_token;
        auto& sessions = user_sessions_[current_token.subject];
        sessions.erase(std::remove(sessions.begin(), sessions.end(), refresh_token), sessions.end());
        sessions.push_back(new_token.token);
        logger_->info("Token refreshed for user: {}", current_token.subject);
        return {AuthResult::Success, new_token};
    } catch (const std::exception& e) {
        logger_->error("Token refresh error: {}", e.what());
        return {AuthResult::InternalError, std::nullopt};
    }
}

bool AuthManager::revoke_token(const std::string& token) {
    if (!initialised_ || !config_.enabled) {
        return false;
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        auto token_it = active_tokens_.find(token);
        if (token_it == active_tokens_.end()) {
            return false;
        }
        const auto& auth_token = token_it->second;
        revoked_tokens_.insert(token);
        active_tokens_.erase(token_it);
        auto& sessions = user_sessions_[auth_token.subject];
        sessions.erase(std::remove(sessions.begin(), sessions.end(), token), sessions.end());
        logger_->info("Token revoked for user: {}", auth_token.subject);
        return true;
    } catch (const std::exception& e) {
        logger_->error("Token revocation error: {}", e.what());
        return false;
    }
}

std::optional<UserInfo> AuthManager::get_user_info(const std::string& user_id) {
    if (!initialised_ || !config_.enabled) {
        return std::nullopt;
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    auto it = users_.find(user_id);
    if (it != users_.end()) {
        return it->second;
    }
    return std::nullopt;
}

bool AuthManager::create_user(const UserInfo& user_info, const std::string& password) {
    if (!initialised_ || !config_.enabled) {
        return false;
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        if (users_.find(user_info.user_id) != users_.end()) {
            logger_->warn("User creation failed: user {} already exists", user_info.user_id);
            return false;
        }
        if (!is_password_strong(password)) {
            logger_->warn("User creation failed: password does not meet strength requirements");
            return false;
        }
        UserInfo new_user = user_info;
        new_user.created_at = std::chrono::system_clock::now();
        new_user.updated_at = new_user.created_at;
        std::string salt = generate_random_bytes(SALT_LENGTH);
        std::string hash = hash_password_impl(password, salt);
        users_[user_info.user_id] = new_user;
        password_hashes_[user_info.user_id] = hash;
        password_salts_[user_info.user_id] = salt;
        logger_->info("User {} created successfully", user_info.user_id);
        return true;
    } catch (const std::exception& e) {
        logger_->error("User creation error for {}: {}", user_info.user_id, e.what());
        return false;
    }
}

bool AuthManager::update_user(const UserInfo& user_info) {
    if (!initialised_ || !config_.enabled) {
        return false;
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        auto it = users_.find(user_info.user_id);
        if (it == users_.end()) {
            logger_->warn("User update failed: user {} not found", user_info.user_id);
            return false;
        }
        it->second = user_info;
        it->second.updated_at = std::chrono::system_clock::now();
        logger_->info("User {} updated successfully", user_info.user_id);
        return true;
    } catch (const std::exception& e) {
        logger_->error("User update error for {}: {}", user_info.user_id, e.what());
        return false;
    }
}

bool AuthManager::delete_user(const std::string& user_id) {
    if (!initialised_ || !config_.enabled) {
        return false;
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        auto it = users_.find(user_id);
        if (it == users_.end()) {
            logger_->warn("User deletion failed: user {} not found", user_id);
            return false;
        }
        auto sessions_it = user_sessions_.find(user_id);
        if (sessions_it != user_sessions_.end()) {
            for (const auto& token : sessions_it->second) {
                revoked_tokens_.insert(token);
                active_tokens_.erase(token);
            }
            user_sessions_.erase(sessions_it);
        }
        users_.erase(it);
        password_hashes_.erase(user_id);
        password_salts_.erase(user_id);
        logger_->info("User {} deleted successfully", user_id);
        return true;
    } catch (const std::exception& e) {
        logger_->error("User deletion error for {}: {}", user_id, e.what());
        return false;
    }
}

bool AuthManager::lock_user(const std::string& user_id) {
    if (!initialised_ || !config_.enabled) {
        return false;
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        auto it = users_.find(user_id);
        if (it == users_.end()) {
            logger_->warn("User lock failed: user {} not found", user_id);
            return false;
        }
        it->second.is_locked = true;
        it->second.status = UserStatus::Locked;
        it->second.updated_at = std::chrono::system_clock::now();
        auto sessions_it = user_sessions_.find(user_id);
        if (sessions_it != user_sessions_.end()) {
            for (const auto& token : sessions_it->second) {
                revoked_tokens_.insert(token);
                active_tokens_.erase(token);
            }
            user_sessions_.erase(sessions_it);
        }
        logger_->info("User {} locked successfully", user_id);
        return true;
    } catch (const std::exception& e) {
        logger_->error("User lock error for {}: {}", user_id, e.what());
        return false;
    }
}

bool AuthManager::unlock_user(const std::string& user_id) {
    if (!initialised_ || !config_.enabled) {
        return false;
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        auto it = users_.find(user_id);
        if (it == users_.end()) {
            logger_->warn("User unlock failed: user {} not found", user_id);
            return false;
        }
        it->second.is_locked = false;
        it->second.status = UserStatus::Active;
        it->second.updated_at = std::chrono::system_clock::now();
        logger_->info("User {} unlocked successfully", user_id);
        return true;
    } catch (const std::exception& e) {
        logger_->error("User unlock error for {}: {}", user_id, e.what());
        return false;
    }
}

bool AuthManager::change_password(const std::string& user_id, const std::string& old_password, const std::string& new_password) {
    if (!initialised_ || !config_.enabled) {
        return false;
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        auto user_it = users_.find(user_id);
        if (user_it == users_.end()) {
            logger_->warn("Password change failed: user {} not found", user_id);
            return false;
        }
        auto hash_it = password_hashes_.find(user_id);
        auto salt_it = password_salts_.find(user_id);
        if (hash_it == password_hashes_.end() || salt_it == password_salts_.end()) {
            logger_->error("Password data not found for user: {}", user_id);
            return false;
        }
        if (!verify_password_impl(old_password, hash_it->second, salt_it->second)) {
            logger_->warn("Password change failed: invalid old password for user: {}", user_id);
            return false;
        }
        if (!is_password_strong(new_password)) {
            logger_->warn("Password change failed: new password does not meet strength requirements");
            return false;
        }
        std::string new_salt = generate_random_bytes(SALT_LENGTH);
        std::string new_hash = hash_password_impl(new_password, new_salt);
        password_hashes_[user_id] = new_hash;
        password_salts_[user_id] = new_salt;
        auto sessions_it = user_sessions_.find(user_id);
        if (sessions_it != user_sessions_.end()) {
            for (const auto& token : sessions_it->second) {
                revoked_tokens_.insert(token);
                active_tokens_.erase(token);
            }
            user_sessions_.erase(sessions_it);
        }
        logger_->info("Password changed successfully for user: {}", user_id);
        return true;
    } catch (const std::exception& e) {
        logger_->error("Password change error for user {}: {}", user_id, e.what());
        return false;
    }
}

bool AuthManager::reset_password(const std::string& user_id, const std::string& new_password) {
    if (!initialised_ || !config_.enabled) {
        return false;
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        auto user_it = users_.find(user_id);
        if (user_it == users_.end()) {
            logger_->warn("Password reset failed: user {} not found", user_id);
            return false;
        }
        if (!is_password_strong(new_password)) {
            logger_->warn("Password reset failed: new password does not meet strength requirements");
            return false;
        }
        std::string new_salt = generate_random_bytes(SALT_LENGTH);
        std::string new_hash = hash_password_impl(new_password, new_salt);
        password_hashes_[user_id] = new_hash;
        password_salts_[user_id] = new_salt;
        auto sessions_it = user_sessions_.find(user_id);
        if (sessions_it != user_sessions_.end()) {
            for (const auto& token : sessions_it->second) {
                revoked_tokens_.insert(token);
                active_tokens_.erase(token);
            }
            user_sessions_.erase(sessions_it);
        }
        logger_->info("Password reset successfully for user: {}", user_id);
        return true;
    } catch (const std::exception& e) {
        logger_->error("Password reset error for user {}: {}", user_id, e.what());
        return false;
    }
}

std::vector<std::string> AuthManager::get_active_sessions(const std::string& user_id) {
    if (!initialised_ || !config_.enabled) {
        return {};
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    auto it = user_sessions_.find(user_id);
    if (it != user_sessions_.end()) {
        return it->second;
    }
    return {};
}

bool AuthManager::terminate_session(const std::string& user_id, const std::string& session_token) {
    if (!initialised_ || !config_.enabled) {
        return false;
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        auto sessions_it = user_sessions_.find(user_id);
        if (sessions_it == user_sessions_.end()) {
            return false;
        }
        auto& sessions = sessions_it->second;
        auto token_it = std::find(sessions.begin(), sessions.end(), session_token);
        if (token_it == sessions.end()) {
            return false;
        }
        revoked_tokens_.insert(session_token);
        active_tokens_.erase(session_token);
        sessions.erase(token_it);
        logger_->info("Session terminated for user: {}", user_id);
        return true;
    } catch (const std::exception& e) {
        logger_->error("Session termination error for user {}: {}", user_id, e.what());
        return false;
    }
}

bool AuthManager::terminate_all_sessions(const std::string& user_id) {
    if (!initialised_ || !config_.enabled) {
        return false;
    }
    std::lock_guard<std::mutex> lock(auth_mutex_);
    try {
        auto sessions_it = user_sessions_.find(user_id);
        if (sessions_it == user_sessions_.end()) {
            return false;
        }
        for (const auto& token : sessions_it->second) {
            revoked_tokens_.insert(token);
            active_tokens_.erase(token);
        }
        user_sessions_.erase(sessions_it);
        logger_->info("All sessions terminated for user: {}", user_id);
        return true;
    } catch (const std::exception& e) {
        logger_->error("Session termination error for user {}: {}", user_id, e.what());
        return false;
    }
}

std::unordered_map<std::string, std::any> AuthManager::get_statistics() {
    std::lock_guard<std::mutex> lock(auth_mutex_);
    std::unordered_map<std::string, std::any> stats;
    stats["total_users"] = static_cast<int>(users_.size());
    stats["active_tokens"] = static_cast<int>(active_tokens_.size());
    stats["revoked_tokens"] = static_cast<int>(revoked_tokens_.size());
    int active_sessions = 0;
    for (const auto& [user_id, sessions] : user_sessions_) {
        active_sessions += static_cast<int>(sessions.size());
    }
    stats["active_sessions"] = active_sessions;
    return stats;
}

AuthConfig AuthManager::get_config() const {
    return config_;
}

bool AuthManager::update_config(const AuthConfig& config) {
    return initialize(config);
}

} // namespace omop::security