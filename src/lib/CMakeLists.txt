# Library CMake configuration

# Add subdirectories for each module
add_subdirectory(common)
add_subdirectory(core)
add_subdirectory(cdm)
add_subdirectory(extract)
add_subdirectory(etl)
add_subdirectory(ml)
add_subdirectory(transform)
add_subdirectory(load)
add_subdirectory(security)
# add_subdirectory(service)  # Temporarily disabled due to gRPC dependency issues

# Create main OMOP ETL library
add_library(omop_etl_lib INTERFACE)

# Set version information for the interface library
set_target_properties(omop_etl_lib PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    OUTPUT_NAME "omop_etl_lib"
)

# Link all component libraries
target_link_libraries(omop_etl_lib INTERFACE
    omop_common
    omop_core
    omop_cdm
    omop_extract
    omop_etl
    omop_ml
    omop_transform
    omop_load
    omop_security
    omop_service
)

# Set include directories
target_include_directories(omop_etl_lib INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:include>
)

# Install library headers
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/
    DESTINATION include/omop
    FILES_MATCHING PATTERN "*.h"
    PATTERN "CMakeLists.txt" EXCLUDE
)

# Export targets
install(TARGETS omop_etl_lib
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)