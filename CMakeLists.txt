# CMakeLists.txt : CMake project for OMOP ETL Pipeline
# Standardized and organized CMake configuration

cmake_minimum_required(VERSION 3.23)

#####################
# Project Setup
#####################

# Include standardized OMOP CMake modules
set(CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/cmake" ${CMAKE_MODULE_PATH})
include(GNUInstallDirs)
include(CMakePackageConfigHelpers)
include(FetchContent)

# Project options (must be before project() call)
option(BUILD_SHARED_LIBS "Build shared libraries" ON)
option(BUILD_STATIC_LIBS "Build static libraries" ON)
option(BUILD_APPLICATIONS "Build application executables" OFF)
option(BUILD_TESTS "Build unit tests" OFF)
option(BUILD_INTEGRATION_TESTS "Build integration tests" OFF)
option(BUILD_DOCS "Build documentation" OFF)
option(ENABLE_COVERAGE "Enable code coverage" OFF)
option(ENABLE_VALGRIND "Enable Valgrind memory checking" OFF)
option(ENABLE_SANITIZERS "Enable sanitizers (AddressSanitizer, UBSan)" OFF)
option(SEM_VER "Enable detection of SemVer" OFF)
option(ENABLE_MOCK_MODE "Enable mock/test mode features in production code" OFF)
option(ENABLE_TEST_DEPENDENCIES "Enable test-specific dependencies and features" OFF)

# Set default build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Build type" FORCE)
    set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
endif()

# Project version information
set(PROJECT_NAME "omop_etl")
set(PROJECT_VERSION_CURRENT 0)
set(PROJECT_VERSION_AGE 1)
set(PROJECT_VERSION_REVISION 1)
set(PROJECT_VERSION_INFO "${PROJECT_VERSION_CURRENT}.${PROJECT_VERSION_AGE}.${PROJECT_VERSION_REVISION}")

# Semantic versioning support
if(SEM_VER)
    include(DetermineVersion)
    find_package(Git)
    if(Git_FOUND)
        determine_version(${CMAKE_CURRENT_SOURCE_DIR} ${GIT_EXECUTABLE} ${PROJECT_NAME})
    endif()
endif()

# Project declaration
project(${PROJECT_NAME}
    DESCRIPTION "OMOP ETL Pipeline - Healthcare data transformation system"
    VERSION ${PROJECT_VERSION_INFO}
    HOMEPAGE_URL "https://github.com/omop-etl/omop-etl"
    LANGUAGES CXX
)

#####################
# Global Configuration
#####################

# C++ standard requirements
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Symbol visibility
set(CMAKE_CXX_VISIBILITY_PRESET hidden)
set(CMAKE_VISIBILITY_INLINES_HIDDEN ON)

# Position independent code
if(NOT CMAKE_POSITION_INDEPENDENT_CODE)
    set(CMAKE_POSITION_INDEPENDENT_CODE ${BUILD_SHARED_LIBS})
endif()

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# MSVC specific configurations
if(MSVC)
    # Use static runtime in static builds
    set(variables
        CMAKE_CXX_FLAGS_DEBUG
        CMAKE_CXX_FLAGS_RELEASE
        CMAKE_CXX_FLAGS_RELWITHDEBINFO
        CMAKE_CXX_FLAGS_MINSIZEREL
    )
    foreach(variable ${variables})
        if(${variable} MATCHES "/MD")
            string(REGEX REPLACE "/MD" "/MT" ${variable} "${${variable}}")
            string(REGEX REPLACE "/MDd" "/MTd" ${variable} "${${variable}}")
        endif()
    endforeach()
endif()

#####################
# Include Standard Modules
#####################

include(omop_standards)
include(omop_dependencies)

#####################
# Dependency Setup
#####################

# Setup all project dependencies using standardized functions
omop_setup_all_dependencies()

# Ensure compression dependency variables are set in the main scope
if(NOT DEFINED HAVE_LIBARCHIVE_NUM)
    if(HAVE_LIBARCHIVE)
        set(HAVE_LIBARCHIVE_NUM 1)
    else()
        set(HAVE_LIBARCHIVE_NUM 0)
    endif()
endif()

# Ensure compression dependency variables are available to all components
if(NOT DEFINED HAVE_BZLIB)
    set(HAVE_BZLIB FALSE)
endif()

if(NOT DEFINED HAVE_LZMA)
    set(HAVE_LZMA FALSE)
endif()

if(NOT DEFINED HAVE_LIBARCHIVE)
    set(HAVE_LIBARCHIVE FALSE)
endif()

# Set compile definitions for compression support
if(HAVE_BZLIB)
    add_compile_definitions(HAVE_BZLIB)
endif()

if(HAVE_LZMA)
    add_compile_definitions(HAVE_LZMA)
endif()

if(HAVE_LIBARCHIVE)
    add_compile_definitions(HAVE_LIBARCHIVE)
    add_compile_definitions(OMOP_HAVE_LIBARCHIVE)
endif()

#####################
# Schema Configuration
#####################

# CDM schema configuration
include(${CMAKE_CURRENT_SOURCE_DIR}/src/lib/cdm/sql/schema_config.cmake)

# Configure build configuration header
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/src/lib/common/config.h.in")
    configure_file(
        ${CMAKE_CURRENT_SOURCE_DIR}/src/lib/common/config.h.in
        ${CMAKE_CURRENT_BINARY_DIR}/include/omop/config.h
    )
else()
    message(FATAL_ERROR "Configuration template file not found: src/lib/common/config.h.in")
endif()

#####################
# Global Include Directories
#####################

# Add configured headers to include path
include_directories(${CMAKE_CURRENT_BINARY_DIR}/include)

# Add source include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/src/lib
    ${PostgreSQL_INCLUDE_DIRS}
    ${HTTPLIB_INCLUDE_DIR}
)

# Link directories
link_directories(
    ${PostgreSQL_LIBRARY_DIRS}
    ${MySQL_LIBRARY_DIRS}
)

#####################
# Component Targets
#####################

# Add library components
add_subdirectory(src)

# Add test components
if(BUILD_TESTS OR BUILD_INTEGRATION_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# Documentation
if(BUILD_DOCS AND HAVE_DOXYGEN)
    add_subdirectory(docs)
    message(STATUS "Documentation target 'docs' available. Build with: cmake --build build --target docs")
endif()

#####################
# Installation Configuration
#####################

# Install configuration files
install(DIRECTORY config/
    DESTINATION ${CMAKE_INSTALL_SYSCONFDIR}/omop-etl
    FILES_MATCHING PATTERN "*.yaml" PATTERN "*.yml"
)

# Install SQL schema files
install(DIRECTORY ${CMAKE_BINARY_DIR}/src/lib/cdm/sql/
    DESTINATION ${CMAKE_INSTALL_DATADIR}/omop-etl/sql
    FILES_MATCHING PATTERN "*.sql"
)

# Install configured headers
install(FILES ${CMAKE_CURRENT_BINARY_DIR}/include/omop/config.h
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/omop
)

#####################
# Export Configuration
#####################

# Note: Target export is disabled due to external dependencies (nlohmann_json, spdlog)
# that cannot be safely exported. Libraries are installed but not exported as CMake targets.
message(STATUS "Target export disabled due to external dependencies")

#####################
# CPack Configuration
#####################

set(CPACK_PACKAGE_NAME ${PROJECT_NAME})
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_VENDOR "UCL Cancer Data Engineering")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_DESCRIPTION_FILE "${CMAKE_CURRENT_SOURCE_DIR}/README.md")
set(CPACK_RESOURCE_FILE_LICENSE "${CMAKE_CURRENT_SOURCE_DIR}/LICENSE")
set(CPACK_GENERATOR "TGZ;DEB;RPM")

include(CPack)

#####################
# Development Tools
#####################

# Clang-tidy support
find_program(CMAKE_CXX_CLANG_TIDY NAMES clang-tidy)
if(CMAKE_CXX_CLANG_TIDY)
    message(STATUS "Found clang-tidy: ${CMAKE_CXX_CLANG_TIDY}")
    # Apply to all targets via the standard functions
endif()

# Sanitizer support
if(ENABLE_SANITIZERS AND NOT MSVC)
    add_compile_options(-fsanitize=address,undefined -fno-omit-frame-pointer)
    add_link_options(-fsanitize=address,undefined)
endif()

#####################
# Configuration Summary
#####################

message(STATUS "")
message(STATUS "═══════════════════════════════════════")
message(STATUS "OMOP ETL Pipeline Configuration Summary")
message(STATUS "═══════════════════════════════════════")
message(STATUS "Version:          ${PROJECT_VERSION}")
message(STATUS "Build type:       ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ Standard:     ${CMAKE_CXX_STANDARD}")
message(STATUS "")
message(STATUS "Build Options:")
message(STATUS "  Shared libs:    ${BUILD_SHARED_LIBS}")
message(STATUS "  Static libs:    ${BUILD_STATIC_LIBS}")
message(STATUS "  Applications:   ${BUILD_APPLICATIONS}")
message(STATUS "  Unit tests:     ${BUILD_TESTS}")
message(STATUS "  Integration:    ${BUILD_INTEGRATION_TESTS}")
message(STATUS "  Documentation:  ${BUILD_DOCS}")
message(STATUS "  Coverage:       ${ENABLE_COVERAGE}")
message(STATUS "  Sanitizers:     ${ENABLE_SANITIZERS}")
message(STATUS "  Mock Mode:      ${ENABLE_MOCK_MODE}")
message(STATUS "  Test Dependencies: ${ENABLE_TEST_DEPENDENCIES}")
message(STATUS "")
message(STATUS "Dependencies:")
message(STATUS "  PostgreSQL:     ${PostgreSQL_VERSION}")
message(STATUS "  MySQL:          ${MySQL_FOUND}")
message(STATUS "  OpenSSL:        ${OpenSSL_VERSION}")
message(STATUS "  gRPC:           ${HAVE_GRPC}")
message(STATUS "  LibArchive:     ${HAVE_LIBARCHIVE}")
message(STATUS "  Doxygen:        ${HAVE_DOXYGEN}")
message(STATUS "")
message(STATUS "Paths:")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  Binary output:  ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")
message(STATUS "  Library output: ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}")
message(STATUS "")
message(STATUS "Schema Configuration:")
message(STATUS "  CDM Schema:     ${CDM_SCHEMA}")
message(STATUS "  Vocab Schema:   ${VOCAB_SCHEMA}")
message(STATUS "═══════════════════════════════════════")
message(STATUS "")

message(STATUS "[DEBUG] HTTPLIB_INCLUDE_DIR at top of CMakeLists.txt: ${HTTPLIB_INCLUDE_DIR}")
message(STATUS "[DEBUG] HAVE_LIBARCHIVE_NUM at top of CMakeLists.txt: ${HAVE_LIBARCHIVE_NUM}")