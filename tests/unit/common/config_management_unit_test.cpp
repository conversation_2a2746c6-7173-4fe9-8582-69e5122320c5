/**
 * @file configuration_test.cpp
 * @brief Unit tests for configuration management with UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "common/configuration.h"
#include "common/utilities.h"
#include <yaml-cpp/yaml.h>
#include <fstream>
#include <filesystem>
#include <format>
#include <thread>
#include <atomic>
#include <chrono>
#include <locale>
#include <iomanip>
#include <sstream>

using namespace omop::common;
using namespace testing;

namespace omop::common::test {

// Test fixture for configuration tests with UK locale setup
class ConfigurationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        // UK-specific formatting
        uk_currency_symbol_ = "£";
        uk_date_format_ = "%d/%m/%Y";
        
        temp_dir_ = std::filesystem::temp_directory_path() / "omop_config_test";
        std::filesystem::create_directories(temp_dir_);
        
        setupTestYAMLContent();
    }

    void TearDown() override {
        if (std::filesystem::exists(temp_dir_)) {
            std::filesystem::remove_all(temp_dir_);
        }
    }

    void setupTestYAMLContent() {
        // UK-specific test configuration
        valid_config_yaml_ = R"(
source_database:
  type: postgresql
  host: "localhost"
  port: 5432
  database: "uk_clinical_data"
  username: "nhs_user"
  password: "secure_password"

target_database:
  type: postgresql
  host: "localhost"
  port: 5432
  database: "uk_omop_cdm"
  username: "omop_user"
  password: "secure_password"

table_mappings:
  patient_demographics:
    source_table: "nhs_patient_demographics"
    target_table: "person"
    transformations:
      - source_column: "nhs_number"
        target_column: "person_source_value"
        type: "direct"
      - source_column: "birth_date"
        target_column: "birth_datetime"
        type: "date_transform"
        date_format: "DD/MM/YYYY"
      - source_column: "postcode"
        target_column: "location_source_value"
        type: "direct"
        validation: "uk_postcode"

vocabulary_mappings:
  gender_vocab:
    Male: 8507
    Female: 8532
    Unknown: 8551

etl_settings:
  batch_size: 1000
  parallel_workers: 4
  validation_mode: "strict"
  error_threshold: 0.01
  currency_symbol: "£"
  date_format: "DD/MM/YYYY"
  temperature_unit: "celsius"
)";

        invalid_config_yaml_ = R"(
# Missing required source_database section
target_database:
  type: postgresql
  host: "localhost"
)";
    }

    std::filesystem::path temp_dir_;
    std::string uk_currency_symbol_;
    std::string uk_date_format_;
    std::string valid_config_yaml_;
    std::string invalid_config_yaml_;
};

// Test fixture for TransformationRule tests with UK data
class TransformationRuleTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        createTestYAMLNodes();
    }

    void createTestYAMLNodes() {
        // UK NHS number transformation
        nhs_rule_yaml_ = YAML::Load(R"(
            source_column: "nhs_number"
            target_column: "person_source_value"
            type: "direct"
            validation: "uk_nhs_number"
        )");

        // UK date transformation
        uk_date_rule_yaml_ = YAML::Load(R"(
            source_column: "birth_date"
            target_column: "birth_datetime"
            type: "date_transform"
            input_format: "DD/MM/YYYY"
            output_format: "YYYY-MM-DD"
        )");

        // UK postcode transformation
        postcode_rule_yaml_ = YAML::Load(R"(
            source_column: "postcode"
            target_column: "location_source_value"
            type: "direct"
            validation: "uk_postcode"
            format_output: true
        )");

        // UK name concatenation
        name_concat_yaml_ = YAML::Load(R"(
            source_columns: ["title", "first_name", "surname"]
            target_column: "full_name"
            type: "string_concatenation"
            separator: " "
            uk_title_formatting: true
        )");

        // UK gender vocabulary mapping
        gender_vocab_yaml_ = YAML::Load(R"(
            source_column: "gender_code"
            target_column: "gender_concept_id"
            type: "vocabulary_mapping"
            vocabulary: "uk_gender_vocab"
            default_value: 8551
        )");
    }

    YAML::Node nhs_rule_yaml_;
    YAML::Node uk_date_rule_yaml_;
    YAML::Node postcode_rule_yaml_;
    YAML::Node name_concat_yaml_;
    YAML::Node gender_vocab_yaml_;
};

// Test that configuration manager initializes correctly
TEST_F(ConfigurationTest, ConfigurationManagerInitializesCorrectly) {
    ConfigurationManager manager;
    EXPECT_FALSE(manager.is_loaded());
    EXPECT_TRUE(manager.get_all_mappings().empty());
    EXPECT_EQ(manager.get_config_file_path(), "");
}

// Test that configuration loads from string successfully with UK settings
TEST_F(ConfigurationTest, ConfigurationLoadsFromStringSuccessfullyWithUKSettings) {
    ConfigurationManager manager;
    
    EXPECT_NO_THROW(manager.load_config_from_string(valid_config_yaml_));
    EXPECT_TRUE(manager.is_loaded());
    
    // Verify UK-specific settings
    auto currency = manager.get_value("etl_settings.currency_symbol");
    ASSERT_TRUE(currency.has_value());
    EXPECT_EQ(currency->as<std::string>(), "£");
    
    auto date_format = manager.get_value("etl_settings.date_format");
    ASSERT_TRUE(date_format.has_value());
    EXPECT_EQ(date_format->as<std::string>(), "DD/MM/YYYY");
    
    auto temp_unit = manager.get_value("etl_settings.temperature_unit");
    ASSERT_TRUE(temp_unit.has_value());
    EXPECT_EQ(temp_unit->as<std::string>(), "celsius");
}

// Test that configuration loads from valid file successfully
TEST_F(ConfigurationTest, ConfigurationLoadsFromValidFileSuccessfully) {
    std::string config_file = temp_dir_ / "uk_config.yml";
    std::ofstream file(config_file);
    file << valid_config_yaml_;
    file.close();
    
    ConfigurationManager manager;
    EXPECT_NO_THROW(manager.load_config(config_file));
    EXPECT_TRUE(manager.is_loaded());
    EXPECT_EQ(manager.get_config_file_path(), config_file);
    
    // Verify UK database names
    const auto& source_db = manager.get_source_db();
    EXPECT_EQ(source_db.database(), "uk_clinical_data");
    
    const auto& target_db = manager.get_target_db();
    EXPECT_EQ(target_db.database(), "uk_omop_cdm");
}

// Test that configuration manager handles missing files gracefully
TEST_F(ConfigurationTest, ConfigurationManagerHandlesMissingFilesGracefully) {
    ConfigurationManager manager;
    std::string nonexistent_file = "/path/that/does/not/exist.yml";
    
    EXPECT_THROW(manager.load_config(nonexistent_file), ConfigurationException);
    EXPECT_FALSE(manager.is_loaded());
}

// Test that configuration reloading works correctly
TEST_F(ConfigurationTest, ConfigurationReloadingWorksCorrectly) {
    std::string config_file = temp_dir_ / "reload_test.yml";
    std::ofstream file(config_file);
    file << valid_config_yaml_;
    file.close();
    
    ConfigurationManager manager;
    manager.load_config(config_file);
    auto first_load_time = manager.get_load_time();
    
    // Wait a bit to ensure different timestamp
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    EXPECT_NO_THROW(manager.reload());
    auto second_load_time = manager.get_load_time();
    
    EXPECT_GT(second_load_time, first_load_time);
    EXPECT_TRUE(manager.is_loaded());
}

// Test that configuration throws error for invalid YAML
TEST_F(ConfigurationTest, ConfigurationThrowsErrorForInvalidYAML) {
    ConfigurationManager manager;
    
    EXPECT_THROW(manager.load_config_from_string(invalid_config_yaml_), ConfigurationException);
    EXPECT_FALSE(manager.is_loaded());
}

// Test that configuration validates UK database settings
TEST_F(ConfigurationTest, ConfigurationValidatesUKDatabaseSettings) {
    ConfigurationManager manager;
    manager.load_config_from_string(valid_config_yaml_);
    
    EXPECT_NO_THROW(manager.validate_config());
    
    const auto& source_db = manager.get_source_db();
    EXPECT_EQ(source_db.type(), DatabaseConfig::Type::PostgreSQL);
    EXPECT_EQ(source_db.host(), "localhost");
    EXPECT_EQ(source_db.port(), 5432);
    EXPECT_EQ(source_db.username(), "nhs_user");
}

// Test that configuration value retrieval works with UK data
TEST_F(ConfigurationTest, ConfigurationValueRetrievalWorksWithUKData) {
    ConfigurationManager manager;
    manager.load_config_from_string(valid_config_yaml_);
    
    // Test nested UK-specific values
    auto currency = manager.get_value("etl_settings.currency_symbol");
    ASSERT_TRUE(currency.has_value());
    EXPECT_EQ(currency->as<std::string>(), "£");
    
    // Test default value functionality
    auto missing_value = manager.get_value_or<std::string>("missing.key", "default");
    EXPECT_EQ(missing_value, "default");
    
    // Test UK batch size
    auto batch_size = manager.get_value_or<int>("etl_settings.batch_size", 500);
    EXPECT_EQ(batch_size, 1000);
}

// Test that database configuration handles UK connection strings
TEST_F(ConfigurationTest, DatabaseConfigurationHandlesUKConnectionStrings) {
    YAML::Node uk_db_config = YAML::Load(R"(
        type: postgresql
        connection_string: "************************************************/uk_clinical_data?sslmode=require"
    )");
    
    DatabaseConfig db_config(uk_db_config);
    EXPECT_EQ(db_config.type(), DatabaseConfig::Type::PostgreSQL);
    EXPECT_TRUE(db_config.connection_string().find("uk-db-server") != std::string::npos);
    EXPECT_TRUE(db_config.connection_string().find("uk_clinical_data") != std::string::npos);
}

// Test that table mapping retrieval works for UK NHS tables
TEST_F(ConfigurationTest, TableMappingRetrievalWorksForUKNHSTables) {
    ConfigurationManager manager;
    manager.load_config_from_string(valid_config_yaml_);
    
    auto mapping = manager.get_table_mapping("patient_demographics");
    ASSERT_TRUE(mapping.has_value());
    
    EXPECT_EQ(mapping->source_table(), "nhs_patient_demographics");
    EXPECT_EQ(mapping->target_table(), "person");
    EXPECT_FALSE(mapping->transformations().empty());
    
    // Check UK-specific transformation
    const auto& transformations = mapping->transformations();
    auto nhs_transform = std::find_if(transformations.begin(), transformations.end(),
        [](const TransformationRule& rule) {
            return rule.source_column() == "nhs_number";
        });
    
    ASSERT_NE(nhs_transform, transformations.end());
    EXPECT_EQ(nhs_transform->target_column(), "person_source_value");
}

// Test that transformation rule constructs from UK NHS number YAML
TEST_F(TransformationRuleTest, TransformationRuleConstructsFromUKNHSNumberYAML) {
    TransformationRule rule(nhs_rule_yaml_);
    
    EXPECT_EQ(rule.source_column(), "nhs_number");
    EXPECT_EQ(rule.target_column(), "person_source_value");
    EXPECT_EQ(rule.type(), TransformationRule::Type::Direct);
    EXPECT_FALSE(rule.is_multi_column());
    
    // Check UK NHS-specific validation parameter
    auto validation = rule.parameters()["validation"];
    ASSERT_TRUE(validation.IsDefined());
    EXPECT_EQ(validation.as<std::string>(), "uk_nhs_number");
}

// Test that transformation rule handles UK date formats correctly
TEST_F(TransformationRuleTest, TransformationRuleHandlesUKDateFormatsCorrectly) {
    TransformationRule rule(uk_date_rule_yaml_);
    
    EXPECT_EQ(rule.source_column(), "birth_date");
    EXPECT_EQ(rule.target_column(), "birth_datetime");
    EXPECT_EQ(rule.type(), TransformationRule::Type::DateTransform);
    
    // Check UK date format parameters
    auto input_format = rule.parameters()["input_format"];
    auto output_format = rule.parameters()["output_format"];
    
    ASSERT_TRUE(input_format.IsDefined());
    ASSERT_TRUE(output_format.IsDefined());
    EXPECT_EQ(input_format.as<std::string>(), "DD/MM/YYYY");
    EXPECT_EQ(output_format.as<std::string>(), "YYYY-MM-DD");
}

// Test that transformation rule validates UK postcode fields
TEST_F(TransformationRuleTest, TransformationRuleValidatesUKPostcodeFields) {
    TransformationRule rule(postcode_rule_yaml_);
    
    EXPECT_EQ(rule.source_column(), "postcode");
    EXPECT_EQ(rule.target_column(), "location_source_value");
    EXPECT_EQ(rule.type(), TransformationRule::Type::Direct);
    
    // Check UK postcode validation
    auto validation = rule.parameters()["validation"];
    ASSERT_TRUE(validation.IsDefined());
    EXPECT_EQ(validation.as<std::string>(), "uk_postcode");
}

// Test that transformation rule works with UK name concatenation
TEST_F(TransformationRuleTest, TransformationRuleWorksWithUKNameConcatenation) {
    TransformationRule rule(name_concat_yaml_);
    
    EXPECT_TRUE(rule.is_multi_column());
    EXPECT_EQ(rule.target_column(), "full_name");
    EXPECT_EQ(rule.type(), TransformationRule::Type::StringConcatenation);
    
    const auto& source_columns = rule.source_columns();
    EXPECT_EQ(source_columns.size(), 3u);
    EXPECT_EQ(source_columns[0], "title");
    EXPECT_EQ(source_columns[1], "first_name");
    EXPECT_EQ(source_columns[2], "surname");
    
    // Check UK title formatting
    auto uk_formatting = rule.parameters()["uk_title_formatting"];
    ASSERT_TRUE(uk_formatting.IsDefined());
    EXPECT_TRUE(uk_formatting.as<bool>());
}

// Test that vocabulary mapping works with UK gender terms
TEST_F(TransformationRuleTest, VocabularyMappingWorksWithUKGenderTerms) {
    TransformationRule rule(gender_vocab_yaml_);
    
    EXPECT_EQ(rule.source_column(), "gender_code");
    EXPECT_EQ(rule.target_column(), "gender_concept_id");
    EXPECT_EQ(rule.type(), TransformationRule::Type::VocabularyMapping);
    
    // Check UK vocabulary reference
    auto vocabulary = rule.parameters()["vocabulary"];
    ASSERT_TRUE(vocabulary.IsDefined());
    EXPECT_EQ(vocabulary.as<std::string>(), "uk_gender_vocab");
    
    // Check default value for unknown
    auto default_value = rule.parameters()["default_value"];
    ASSERT_TRUE(default_value.IsDefined());
    EXPECT_EQ(default_value.as<int>(), 8551); // OMOP concept for "Unknown"
}

} // namespace omop::common::test