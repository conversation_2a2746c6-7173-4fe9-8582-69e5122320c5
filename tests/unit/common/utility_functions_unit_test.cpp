/**
 * @file utilities_test.cpp
 * @brief Unit tests for utility functions with UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "common/utilities.h"
#include <fstream>
#include <filesystem>
#include <chrono>
#include <thread>
#include <locale>
#include <iomanip>
#include <sstream>
#include <random>
#include <cstring>
#include <any>

using namespace omop::common;

namespace omop::common::test {

// Test fixture for utility functions with UK locale setup
class UtilitiesTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        temp_dir = std::filesystem::temp_directory_path() / "omop_test";
        std::filesystem::create_directories(temp_dir);
        
        // UK-specific formatting
        uk_currency_symbol_ = "£";
        uk_date_format_ = "%d/%m/%Y";
        uk_time_format_ = "%H:%M";
    }

    void TearDown() override {
        if (std::filesystem::exists(temp_dir)) {
            std::filesystem::remove_all(temp_dir);
        }
    }

    std::filesystem::path temp_dir;
    std::string uk_currency_symbol_;
    std::string uk_date_format_;
    std::string uk_time_format_;
};

// Test any_to_string function
TEST_F(UtilitiesTest, AnyToStringFunction) {
    // Test various types
    EXPECT_EQ(any_to_string(std::any(42)), "42");
    EXPECT_EQ(any_to_string(std::any(3.14)), "3.140000");
    EXPECT_EQ(any_to_string(std::any(true)), "true");
    EXPECT_EQ(any_to_string(std::any(false)), "false");
    EXPECT_EQ(any_to_string(std::any(std::string("test"))), "test");
    EXPECT_EQ(any_to_string(std::any('A')), "A");
    
    // Test empty any
    EXPECT_EQ(any_to_string(std::any()), "");
    
    // Test various numeric types
    EXPECT_EQ(any_to_string(std::any(42L)), "42");
    EXPECT_EQ(any_to_string(std::any(42LL)), "42");
    EXPECT_EQ(any_to_string(std::any(42U)), "42");
    EXPECT_EQ(any_to_string(std::any(42UL)), "42");
    EXPECT_EQ(any_to_string(std::any(42ULL)), "42");
    EXPECT_EQ(any_to_string(std::any(3.14f)), "3.140000");
    
    // Test unknown type
    struct UnknownType { int x; };
    UnknownType unknown{42};
    std::string result = any_to_string(std::any(unknown));
    EXPECT_TRUE(result.find("<") == 0);
    EXPECT_TRUE(result.find(">") == result.length() - 1);
}

// Test UK-specific validation functions

// Test MedicalUtils functions
TEST_F(UtilitiesTest, MedicalUtilsFunctions) {
    // Test medical term detection
    EXPECT_TRUE(MedicalUtils::is_medical_term("diabetes"));
    EXPECT_TRUE(MedicalUtils::is_medical_term("hypertension"));
    EXPECT_TRUE(MedicalUtils::is_medical_term("myocardial infarction"));
    EXPECT_FALSE(MedicalUtils::is_medical_term("apple"));
    EXPECT_FALSE(MedicalUtils::is_medical_term("car"));
    
    // Test medical suffixes
    EXPECT_TRUE(MedicalUtils::has_medical_suffix("diabetes"));
    EXPECT_TRUE(MedicalUtils::has_medical_suffix("hypertension"));
    EXPECT_FALSE(MedicalUtils::has_medical_suffix("apple"));
    
    // Test medical prefixes
    EXPECT_TRUE(MedicalUtils::has_medical_prefix("hyper"));
    EXPECT_TRUE(MedicalUtils::has_medical_prefix("hypo"));
    EXPECT_FALSE(MedicalUtils::has_medical_prefix("apple"));
    
    // Test NHS number validation
    EXPECT_TRUE(MedicalUtils::is_valid_nhs_number("**********")); // Known valid NHS number
    EXPECT_TRUE(MedicalUtils::is_valid_nhs_number("**********")); // Another valid NHS number
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number("123456789"));
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number("**********1"));
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number("abcdefghij"));
    
    // Test UK NI number validation
    EXPECT_TRUE(MedicalUtils::is_valid_uk_ni_number("*********"));
    EXPECT_TRUE(MedicalUtils::is_valid_uk_ni_number("CD789012E"));
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("AB12345"));
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("AB1234567C"));
    
    // Test SNOMED CT code validation
    EXPECT_TRUE(MedicalUtils::is_valid_snomed_code("123456789"));
    EXPECT_TRUE(MedicalUtils::is_valid_snomed_code("987654321"));
    EXPECT_FALSE(MedicalUtils::is_valid_snomed_code("12345")); // Too short (5 digits)
    EXPECT_FALSE(MedicalUtils::is_valid_snomed_code("**********123456789")); // Too long (19 digits)
    
    // Test Read code validation
    EXPECT_TRUE(MedicalUtils::is_valid_read_code("A1"));
    EXPECT_TRUE(MedicalUtils::is_valid_read_code("Z99"));
    EXPECT_FALSE(MedicalUtils::is_valid_read_code("A")); // Too short (1 character)
    EXPECT_FALSE(MedicalUtils::is_valid_read_code("A100")); // Too long (4 characters)
}

// Test OptimizationUtils functions
TEST_F(UtilitiesTest, OptimizationUtilsFunctions) {
    // Test optimal batch size calculation
    size_t batch_size = OptimizationUtils::calculate_optimal_batch_size(100, 1024*1024, "linux");
    EXPECT_GT(batch_size, 0);
    EXPECT_LE(batch_size, 100000);
    
    // Test optimal worker count calculation
    size_t workers = OptimizationUtils::calculate_optimal_workers(1000, std::chrono::duration<double>(0.1), 8);
    EXPECT_GT(workers, 0);
    EXPECT_LE(workers, 8);
    
    // Test configuration optimization
    auto base_config = std::unordered_map<std::string, std::any>{
        {"batch_size", 1000},
        {"worker_threads", 2}
    };
    
    auto system_info = std::unordered_map<std::string, std::any>{
        {"cpu_cores", 8},
        {"memory_gb", 16},
        {"system_type", std::string("linux")}
    };
    
    auto performance_requirements = std::unordered_map<std::string, std::any>{
        {"target_throughput", 10000}
    };
    
    auto optimized = OptimizationUtils::optimize_config(base_config, system_info, performance_requirements);
    EXPECT_TRUE(optimized.count("parallel_processing"));
    EXPECT_TRUE(optimized.count("max_workers"));
    EXPECT_TRUE(optimized.count("buffer_size"));
    
    // Test system info retrieval
    auto sys_info = OptimizationUtils::get_system_info();
    EXPECT_TRUE(sys_info.count("cpu_cores"));
    EXPECT_TRUE(sys_info.count("system_type"));
    EXPECT_TRUE(sys_info.count("memory_gb"));
}

// Test new DateTimeUtils functions
TEST_F(UtilitiesTest, DateTimeUtilsNewFunctions) {
    // Test parse_date_multiple_formats
    std::vector<std::string> formats = {"%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y"};
    
    auto result1 = date_utils::parse_date_multiple_formats("2023-12-25", formats);
    EXPECT_TRUE(result1.has_value());
    
    auto result2 = date_utils::parse_date_multiple_formats("25/12/2023", formats);
    EXPECT_TRUE(result2.has_value());
    
    auto result3 = date_utils::parse_date_multiple_formats("12/25/2023", formats);
    EXPECT_TRUE(result3.has_value());
    
    // Test with invalid date
    auto result4 = date_utils::parse_date_multiple_formats("invalid-date", formats);
    EXPECT_FALSE(result4.has_value());
}

// Test new ValidationUtils functions
TEST_F(UtilitiesTest, ValidationUtilsNewFunctions) {
    // Test required field validation
    EXPECT_TRUE(ValidationUtils::validate_required_field<std::string>("test", "field_name"));
    EXPECT_TRUE(ValidationUtils::validate_required_field<int>(42, "field_name"));
    EXPECT_TRUE(ValidationUtils::validate_required_field<double>(3.14, "field_name"));
    EXPECT_TRUE(ValidationUtils::validate_required_field<bool>(true, "field_name"));
    
    // Test field range validation
    EXPECT_TRUE(ValidationUtils::validate_field_range<int>(5, 1, 10));
    EXPECT_TRUE(ValidationUtils::validate_field_range<double>(3.5, 1.0, 5.0));
    EXPECT_TRUE(ValidationUtils::validate_field_range<size_t>(100, 50, 200));
    EXPECT_FALSE(ValidationUtils::validate_field_range<int>(15, 1, 10));
    EXPECT_FALSE(ValidationUtils::validate_field_range<double>(6.0, 1.0, 5.0));
    
    // Test file path validation
    EXPECT_TRUE(ValidationUtils::validate_file_path("/valid/path/file.txt"));
    EXPECT_TRUE(ValidationUtils::validate_file_path("C:\\valid\\path\\file.txt"));
    EXPECT_FALSE(ValidationUtils::validate_file_path(""));
    EXPECT_FALSE(ValidationUtils::validate_file_path("invalid<>path"));
    
    // Test database connection string validation
    EXPECT_TRUE(ValidationUtils::validate_database_connection_string("********************************/db"));
    EXPECT_TRUE(ValidationUtils::validate_database_connection_string("mysql://user:pass@host:3306/db"));
    EXPECT_FALSE(ValidationUtils::validate_database_connection_string(""));
    EXPECT_FALSE(ValidationUtils::validate_database_connection_string("invalid:connection"));
    
    // Test configuration map validation
    auto config = std::unordered_map<std::string, std::any>{
        {"required_field1", std::string("value1")},
        {"required_field2", 42},
        {"optional_field", std::string("optional")}
    };
    
    std::vector<std::string> required_fields = {"required_field1", "required_field2"};
    
    auto validators = std::unordered_map<std::string, std::function<bool(const std::any&)>>{
        {"required_field1", [](const std::any& val) { return val.type() == typeid(std::string); }},
        {"required_field2", [](const std::any& val) { return val.type() == typeid(int); }}
    };
    
    auto errors = ValidationUtils::validate_config_map(config, required_fields, validators);
    EXPECT_TRUE(errors.empty());
    
    // Test with missing required field
    auto invalid_config = std::unordered_map<std::string, std::any>{
        {"required_field1", std::string("value1")}
        // missing required_field2
    };
    
    auto invalid_errors = ValidationUtils::validate_config_map(invalid_config, required_fields, validators);
    EXPECT_FALSE(invalid_errors.empty());
}
TEST_F(UtilitiesTest, UKValidationFunctionsComprehensive) {
    // Test UK postcode validation
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("M1 1AA"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("M60 1QD"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("B33 8TH"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("W1A 0AX"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("EC1A 1BB"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("SW1A 1AA"));
    
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("INVALID"));
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("M60 1QDX"));
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("123 456"));
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode(""));
    
    // Test NHS number validation
    EXPECT_TRUE(MedicalUtils::is_valid_nhs_number("**********"));
    EXPECT_TRUE(MedicalUtils::is_valid_nhs_number("************"));
    EXPECT_TRUE(MedicalUtils::is_valid_nhs_number("************"));
    
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number("**********"));
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number("450557710"));
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number("**********0"));
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number(""));
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number("abcdefghij"));
    
    // Test UK National Insurance number validation
    EXPECT_TRUE(MedicalUtils::is_valid_uk_ni_number("*********"));
    EXPECT_TRUE(MedicalUtils::is_valid_uk_ni_number("*********"));
    EXPECT_TRUE(MedicalUtils::is_valid_uk_ni_number("QQ123456C"));
    
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("DO123456C")); // Invalid prefix
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("FY123456C")); // Invalid prefix
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("*********")); // Invalid prefix
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("*********")); // Invalid prefix
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("*********")); // Invalid prefix
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("*********")); // Invalid prefix
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("AB12345C"));  // Wrong length
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("AB1234567C")); // Wrong length
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number(""));
    
    // Test UK phone number validation
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("+44 20 7946 0958"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("020 7946 0958"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("07700 900123"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("+44 7700 900123"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("0161 496 0000"));
    
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone("************")); // US format
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone("123456789"));     // Too short
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone(""));
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone("abcdefghij"));
    
    // Test SNOMED CT code validation
    EXPECT_TRUE(MedicalUtils::is_valid_snomed_code("404684003"));
    EXPECT_TRUE(MedicalUtils::is_valid_snomed_code("195967001"));
    EXPECT_TRUE(MedicalUtils::is_valid_snomed_code("22298006"));
    
    EXPECT_FALSE(MedicalUtils::is_valid_snomed_code("123"));      // Too short
    EXPECT_TRUE(MedicalUtils::is_valid_snomed_code("**********1")); // Valid length (11 digits)
    EXPECT_FALSE(MedicalUtils::is_valid_snomed_code("abcdefgh"));
    EXPECT_FALSE(MedicalUtils::is_valid_snomed_code(""));
    
    // Test Read code validation  
    EXPECT_TRUE(MedicalUtils::is_valid_read_code("G30.."));
    EXPECT_TRUE(MedicalUtils::is_valid_read_code("F25.."));
    EXPECT_TRUE(MedicalUtils::is_valid_read_code("14A.."));
    EXPECT_TRUE(MedicalUtils::is_valid_read_code("G30z."));
    
    EXPECT_FALSE(MedicalUtils::is_valid_read_code(""));
    EXPECT_FALSE(MedicalUtils::is_valid_read_code("G"));
    EXPECT_TRUE(MedicalUtils::is_valid_read_code("G30")); // Valid 3-character code
    EXPECT_FALSE(MedicalUtils::is_valid_read_code("G30.......")); // Too long (10 characters)
}

// Test UK localization functions
TEST_F(UtilitiesTest, UKLocalizationFunctions) {
    using namespace UKLocalization;
    
    // Test UK currency formatting
    EXPECT_EQ(format_uk_currency(125.50), "£125.50");
    EXPECT_EQ(format_uk_currency(0.01), "£0.01");
    EXPECT_EQ(format_uk_currency(1000.00), "£1,000.00");
    EXPECT_EQ(format_uk_currency(-50.75), "£-50.75");
    
    // Test UK date formatting
    auto test_time = std::chrono::system_clock::from_time_t(**********); // 2022-01-01 00:00:00 UTC
    std::string uk_date = format_uk_date(test_time);
    EXPECT_TRUE(uk_date.find("01/01/2022") != std::string::npos);
    
    // Test UK datetime formatting
    std::string uk_datetime = format_uk_datetime(test_time);
    EXPECT_TRUE(uk_datetime.find("01/01/2022") != std::string::npos);
    EXPECT_TRUE(uk_datetime.find(":") != std::string::npos);
    
    // Test temperature conversion
    EXPECT_DOUBLE_EQ(celsius_to_fahrenheit(0.0), 32.0);
    EXPECT_DOUBLE_EQ(celsius_to_fahrenheit(100.0), 212.0);
    EXPECT_DOUBLE_EQ(celsius_to_fahrenheit(37.0), 98.6);
    
    EXPECT_DOUBLE_EQ(fahrenheit_to_celsius(32.0), 0.0);
    EXPECT_DOUBLE_EQ(fahrenheit_to_celsius(212.0), 100.0);
    EXPECT_NEAR(fahrenheit_to_celsius(98.6), 37.0, 0.01);
    
    // Test temperature formatting
    EXPECT_EQ(format_temperature_celsius(20.5), "20.5°C");
    EXPECT_EQ(format_temperature_celsius(0.0), "0.0°C");
    EXPECT_EQ(format_temperature_celsius(-5.2), "-5.2°C");
    
    // Test UK number formatting
    EXPECT_EQ(format_uk_number(1234.567, 2), "1234.57");
    EXPECT_EQ(format_uk_number(0.123, 3), "0.123");
    EXPECT_EQ(format_uk_number(1000, 0), "1000");
    
    // Test UK address formatting
    std::string address = format_uk_address(
        "123 High Street",
        "Flat 2",
        "Manchester",
        "Greater Manchester",
        "M1 1AA"
    );
    EXPECT_TRUE(address.find("123 High Street") != std::string::npos);
    EXPECT_TRUE(address.find("Flat 2") != std::string::npos);
    EXPECT_TRUE(address.find("Manchester") != std::string::npos);
    EXPECT_TRUE(address.find("Greater Manchester") != std::string::npos);
    EXPECT_TRUE(address.find("M1 1AA") != std::string::npos);
    
    // Test with empty optional fields
    std::string minimal_address = format_uk_address(
        "123 High Street",
        "",
        "Manchester",
        "",
        "M1 1AA"
    );
    EXPECT_TRUE(minimal_address.find("123 High Street") != std::string::npos);
    EXPECT_TRUE(minimal_address.find("Manchester") != std::string::npos);
    EXPECT_TRUE(minimal_address.find("M1 1AA") != std::string::npos);
    EXPECT_FALSE(minimal_address.find("Flat 2") != std::string::npos);
}

// Test age calculation function
TEST_F(UtilitiesTest, AgeCalculationFunction) {
    // Test age calculation for someone born on 15/01/1985
    std::tm birth_tm = {};
    birth_tm.tm_year = 85;  // Years since 1900
    birth_tm.tm_mon = 0;    // January (0-based)
    birth_tm.tm_mday = 15;
    auto birth_date = std::chrono::system_clock::from_time_t(std::mktime(&birth_tm));
    
    // Calculate age as of 15/01/2025
    std::tm ref_tm = {};
    ref_tm.tm_year = 125;  // 2025 - 1900
    ref_tm.tm_mon = 0;
    ref_tm.tm_mday = 15;
    auto ref_date = std::chrono::system_clock::from_time_t(std::mktime(&ref_tm));
    
    auto age_result = date_utils::calculate_age(birth_date, ref_date);
    EXPECT_EQ(age_result.years, 40);
    
    // Test age calculation when birthday hasn't occurred yet this year
    ref_tm.tm_mday = 14;  // One day before birthday
    ref_date = std::chrono::system_clock::from_time_t(std::mktime(&ref_tm));
    auto age_result2 = date_utils::calculate_age(birth_date, ref_date);
    EXPECT_EQ(age_result2.years, 39);
}

// Test that base64 encoding works correctly
TEST_F(UtilitiesTest, Base64EncodingWorksCorrectly) {
    std::vector<uint8_t> data = {72, 101, 108, 108, 111}; // "Hello"
    std::string encoded = CryptoUtils::base64_encode(data);
    EXPECT_EQ(encoded, "SGVsbG8=");
    
    std::vector<uint8_t> decoded = CryptoUtils::base64_decode(encoded);
    EXPECT_EQ(data, decoded);
}

// Test that camel case conversion works properly
TEST_F(UtilitiesTest, CamelCaseConversionWorksCorrectly) {
    EXPECT_EQ(string_utils::to_camel_case("hello_world"), "helloWorld");
    EXPECT_EQ(string_utils::to_camel_case("nhs_patient_data"), "nhsPatientData");
    EXPECT_EQ(string_utils::to_camel_case("gp_practice_code"), "gpPracticeCode");
    EXPECT_EQ(string_utils::to_camel_case(""), "");
    EXPECT_EQ(string_utils::to_camel_case("single"), "single");
}

// Test that cryptographic hash functions work correctly
TEST_F(UtilitiesTest, CryptographicHashFunctionsWorkCorrectly) {
    std::string data = "NHS patient data";
    
    std::string md5_hash = CryptoUtils::md5(data);
    EXPECT_EQ(md5_hash.length(), 32u); // MD5 is 32 hex characters
    
    std::string sha256_hash = CryptoUtils::sha256(data);
    EXPECT_EQ(sha256_hash.length(), 64u); // SHA256 is 64 hex characters
    
    // Verify consistency
    EXPECT_EQ(CryptoUtils::md5(data), md5_hash);
    EXPECT_EQ(CryptoUtils::sha256(data), sha256_hash);
}

// Test that date parsing works with UK formats
TEST_F(UtilitiesTest, DateParsingWorksWithUKFormats) {
    // Test UK date format DD/MM/YYYY
    auto parsed_date = date_utils::parse_date("15/01/2025", "%d/%m/%Y");
    ASSERT_TRUE(parsed_date.has_value());
    
    std::string formatted = date_utils::format_date(*parsed_date, "%d/%m/%Y");
    EXPECT_EQ(formatted, "15/01/2025");
    
    // Test invalid UK date
    auto invalid_date = date_utils::parse_date("32/01/2025", "%d/%m/%Y");
    EXPECT_FALSE(invalid_date.has_value());
}

// Test that date validation recognizes UK date formats
TEST_F(UtilitiesTest, DateValidationRecognizesUKDateFormats) {
    EXPECT_TRUE(ValidationUtils::is_valid_date_format("15/01/2025", "%d/%m/%Y"));
    EXPECT_TRUE(ValidationUtils::is_valid_date_format("29/02/2024", "%d/%m/%Y")); // Leap year
    EXPECT_FALSE(ValidationUtils::is_valid_date_format("29/02/2025", "%d/%m/%Y")); // Not leap year
    EXPECT_FALSE(ValidationUtils::is_valid_date_format("32/01/2025", "%d/%m/%Y"));
    EXPECT_FALSE(ValidationUtils::is_valid_date_format("15/13/2025", "%d/%m/%Y"));
}

// Test that file operations work correctly
TEST_F(UtilitiesTest, FileOperationsWorkCorrectly) {
    // Test UK-specific file operations
    std::string test_file = "uk_test_file.txt";
    std::string uk_content = "UK Healthcare Data\nPostcode: SW1A 1AA\nCurrency: £\nTemperature: 20°C";
    
    EXPECT_TRUE(file_utils::writing::write_text_file(test_file, uk_content));
    EXPECT_TRUE(file_utils::operations::exists(test_file));
    
    auto content = file_utils::reading::read_text_file(test_file);
    EXPECT_TRUE(content.has_value());
    EXPECT_EQ(content.value(), uk_content);
    
    auto size = file_utils::operations::file_size(test_file);
    ASSERT_TRUE(size.has_value());
    EXPECT_EQ(*size, uk_content.length());
}

// Test that JSON validation works correctly
TEST_F(UtilitiesTest, JSONValidationWorksCorrectly) {
    EXPECT_TRUE(ValidationUtils::is_valid_json("{}"));
    EXPECT_TRUE(ValidationUtils::is_valid_json("{\"nhs_number\": \"**********\"}"));
    EXPECT_TRUE(ValidationUtils::is_valid_json("[1, 2, 3]"));
    EXPECT_FALSE(ValidationUtils::is_valid_json("{invalid json}"));
    EXPECT_FALSE(ValidationUtils::is_valid_json(""));
}

// Test that NHS number validation works correctly
TEST_F(UtilitiesTest, NHSNumberValidationWorksCorrectly) {
    // Valid NHS numbers (with proper checksums)
    EXPECT_TRUE(MedicalUtils::is_valid_nhs_number("**********"));
    EXPECT_TRUE(MedicalUtils::is_valid_nhs_number("************")); // With spaces
    
    // Invalid NHS numbers
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number("**********")); // Wrong checksum
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number("123456789"));  // Too short
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number("**********1")); // Too long
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number("abcd567890"));  // Contains letters
    EXPECT_FALSE(MedicalUtils::is_valid_nhs_number(""));            // Empty
}

// Test that performance timing works accurately
TEST_F(UtilitiesTest, PerformanceTimingWorksAccurately) {
    PerformanceUtils::Timer timer;
    
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    double elapsed = timer.elapsed_milliseconds();
    EXPECT_GE(elapsed, 95.0);  // Allow some variance
    EXPECT_LE(elapsed, 150.0); // But not too much
}

// Test that phone number validation recognizes UK numbers
TEST_F(UtilitiesTest, PhoneNumberValidationRecognizesUKNumbers) {
    // Valid UK phone numbers
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("07123456789"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("+44 7123 456789"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("0044 7123 456789"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_phone("020 7946 0958"));
    
    // Invalid UK phone numbers
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone("123456789"));     // Too short
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone("**********123")); // Too long
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone("1712345678"));    // Wrong prefix
    EXPECT_FALSE(ValidationUtils::is_valid_uk_phone(""));              // Empty
}

// Test that random string generation produces valid results
TEST_F(UtilitiesTest, RandomStringGenerationProducesValidResults) {
    std::string random1 = string_utils::random_string(10);
    std::string random2 = string_utils::random_string(10);
    
    EXPECT_EQ(random1.length(), 10u);
    EXPECT_EQ(random2.length(), 10u);
    EXPECT_NE(random1, random2); // Should be different
    
    // Should contain only alphanumeric characters
    for (char c : random1) {
        EXPECT_TRUE(std::isalnum(c));
    }
}

// Test that Read code validation works for UK legacy codes
TEST_F(UtilitiesTest, ReadCodeValidationWorksForUKLegacyCodes) {
    // Valid Read codes
    EXPECT_TRUE(MedicalUtils::is_valid_read_code("G20.."));
    EXPECT_TRUE(MedicalUtils::is_valid_read_code("14A6."));
    EXPECT_TRUE(MedicalUtils::is_valid_read_code("C10z."));
    EXPECT_TRUE(MedicalUtils::is_valid_read_code("ABC123"));
    
    // Invalid Read codes
    EXPECT_FALSE(MedicalUtils::is_valid_read_code(""));
    EXPECT_FALSE(MedicalUtils::is_valid_read_code("A"));       // Too short (1 character)
    EXPECT_FALSE(MedicalUtils::is_valid_read_code("ABCDEFGH")); // Too long
    EXPECT_FALSE(MedicalUtils::is_valid_read_code("A B C"));    // Contains spaces
}

// Test that SNOMED CT code validation works correctly
TEST_F(UtilitiesTest, SNOMEDCTCodeValidationWorksCorrectly) {
    // Valid SNOMED CT codes
    EXPECT_TRUE(MedicalUtils::is_valid_snomed_code("123456"));
    EXPECT_TRUE(MedicalUtils::is_valid_snomed_code("**********"));
    EXPECT_TRUE(MedicalUtils::is_valid_snomed_code("**********12345678")); // 18 digits
    
    // Invalid SNOMED CT codes
    EXPECT_FALSE(MedicalUtils::is_valid_snomed_code(""));
    EXPECT_FALSE(MedicalUtils::is_valid_snomed_code("12345"));  // Too short
    EXPECT_FALSE(MedicalUtils::is_valid_snomed_code("**********123456789")); // Too long
    EXPECT_FALSE(MedicalUtils::is_valid_snomed_code("123ABC"));  // Contains letters
}

// Test that SQL identifier validation prevents injection
TEST_F(UtilitiesTest, SQLIdentifierValidationPreventsInjection) {
    // Valid SQL identifiers
    EXPECT_TRUE(ValidationUtils::is_valid_sql_identifier("patient_id"));
    EXPECT_TRUE(ValidationUtils::is_valid_sql_identifier("_private_field"));
    EXPECT_TRUE(ValidationUtils::is_valid_sql_identifier("table123"));
    EXPECT_TRUE(ValidationUtils::is_valid_sql_identifier("nhs_number"));
    
    // Invalid SQL identifiers
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier(""));
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier("123invalid"));  // Starts with number
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier("field-name"));  // Contains hyphen
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier("SELECT"));      // Reserved word
    EXPECT_FALSE(ValidationUtils::is_valid_sql_identifier("DROP"));        // Reserved word
}

// Test that string case conversion handles UK terms correctly
TEST_F(UtilitiesTest, StringCaseConversionHandlesUKTermsCorrectly) {
    EXPECT_EQ(string_utils::to_lower("NHS Patient"), "nhs patient");
    EXPECT_EQ(string_utils::to_upper("gp practice"), "GP PRACTICE");
    EXPECT_EQ(string_utils::to_lower("GREATER MANCHESTER"), "greater manchester");
    EXPECT_EQ(string_utils::to_upper("royal mail"), "ROYAL MAIL");
    EXPECT_EQ(string_utils::to_lower(""), "");
    EXPECT_EQ(string_utils::to_upper(""), "");
}

// Test that string contains function works for UK-specific terms
TEST_F(UtilitiesTest, StringContainsFunctionWorksForUKSpecificTerms) {
    EXPECT_TRUE(string_utils::contains("NHS number validation", "NHS"));
    EXPECT_TRUE(string_utils::contains("GP practice code", "practice"));
    EXPECT_TRUE(string_utils::contains("Royal Mail postcode", "Mail"));
    EXPECT_FALSE(string_utils::contains("patient data", "NHS"));
    EXPECT_FALSE(string_utils::contains("", "test"));
}

// Test that string joining works with UK formatting
TEST_F(UtilitiesTest, StringJoiningWorksWithUKFormatting) {
    std::vector<std::string> uk_cities = {"London", "Manchester", "Birmingham", "Glasgow"};
    EXPECT_EQ(string_utils::join(uk_cities, ", "), "London, Manchester, Birmingham, Glasgow");
    EXPECT_EQ(string_utils::join(uk_cities, " and "), "London and Manchester and Birmingham and Glasgow");
    EXPECT_EQ(string_utils::join({}, ", "), "");
    EXPECT_EQ(string_utils::join({"London"}, ", "), "London");
}

// Test that string prefix and suffix detection works
TEST_F(UtilitiesTest, StringPrefixAndSuffixDetectionWorks) {
            EXPECT_TRUE(string_utils::starts_with("NHS12345", "NHS"));
        EXPECT_TRUE(string_utils::ends_with("patient.csv", ".csv"));
        EXPECT_FALSE(string_utils::starts_with("patient", "NHS"));
        EXPECT_FALSE(string_utils::ends_with("data.txt", ".csv"));
        EXPECT_FALSE(string_utils::starts_with("", "test"));
        EXPECT_FALSE(string_utils::ends_with("", "test"));
}

// Test that string replacement works for UK terminology
TEST_F(UtilitiesTest, StringReplacementWorksForUKTerminology) {
    EXPECT_EQ(string_utils::replace_all("US postcode", "US", "UK"), "UK postcode");
    EXPECT_EQ(string_utils::replace_all("zip code validation", "zip code", "postcode"), "postcode validation");
    EXPECT_EQ(string_utils::replace_all("$100.50", "$", "£"), "£100.50");
    EXPECT_EQ(string_utils::replace_all("", "old", "new"), "");
    EXPECT_EQ(string_utils::replace_all("no change", "missing", "new"), "no change");
}

// Test that string sanitization removes dangerous characters
TEST_F(UtilitiesTest, StringSanitizationRemovesDangerousCharacters) {
    EXPECT_EQ(ValidationUtils::sanitize_string("<script>alert('xss')</script>"), 
              "&lt;script&gt;alert(&#39;xss&#39;)&lt;/script&gt;");
    EXPECT_EQ(ValidationUtils::sanitize_string("Safe NHS number"), "Safe NHS number");
    EXPECT_EQ(ValidationUtils::sanitize_string("Patient \"John\" & 'Jane'"), 
              "Patient &quot;John&quot; &amp; &#39;Jane&#39;");
}

// Test that string splitting works with UK data formats
TEST_F(UtilitiesTest, StringSplittingWorksWithUKDataFormats) {
    std::vector<std::string> expected = {"01/01/2025", "15/06/2025", "31/12/2025"};
    EXPECT_EQ(string_utils::split("01/01/2025,15/06/2025,31/12/2025", ','), expected);
    
    std::vector<std::string> postcode_parts = {"M60", "1QD"};
    EXPECT_EQ(string_utils::split("M60 1QD", ' '), postcode_parts);
    
    EXPECT_EQ(string_utils::split("", ','), std::vector<std::string>{});
    EXPECT_EQ(string_utils::split("single", ','), std::vector<std::string>{"single"});
}

// Test that string trimming removes UK-specific whitespace
TEST_F(UtilitiesTest, StringTrimmingRemovesUKSpecificWhitespace) {
    EXPECT_EQ(string_utils::trim("  NHS patient data  "), "NHS patient data");
    EXPECT_EQ(string_utils::trim("\t\nGP practice code\r\n"), "GP practice code");
    EXPECT_EQ(string_utils::trim(""), "");
    EXPECT_EQ(string_utils::trim("   "), "");
    EXPECT_EQ(string_utils::trim("no-trim-needed"), "no-trim-needed");
}

// Test pattern matching with valid patterns
TEST_F(UtilitiesTest, StringPatternMatchingValid) {
    EXPECT_TRUE(string_utils::matches_pattern("<EMAIL>", R"(^[\w\.-]+@[\w\.-]+\.\w+$)"));
    EXPECT_TRUE(string_utils::matches_pattern("123-45-6789", R"(\d{3}-\d{2}-\d{4})"));
    EXPECT_TRUE(string_utils::matches_pattern("abc123", R"(^[a-z]+\d+$)"));
    EXPECT_TRUE(string_utils::matches_pattern("", R"(.*)")); // Empty string matches any pattern
}

// Test pattern matching with invalid patterns
TEST_F(UtilitiesTest, StringPatternMatchingInvalid) {
    EXPECT_FALSE(string_utils::matches_pattern("invalid-email", R"(^[\w\.-]+@[\w\.-]+\.\w+$)"));
    EXPECT_FALSE(string_utils::matches_pattern("abc", R"(^[a-z]+\d+$)"));
}

// Test pattern matching with invalid regex falls back to substring matching
TEST_F(UtilitiesTest, StringPatternMatchingInvalidRegex) {
    // Invalid regex should fall back to substring matching instead of throwing
    EXPECT_FALSE(string_utils::matches_pattern("test", "[invalid"));
    EXPECT_TRUE(string_utils::matches_pattern("test", "test")); // Valid pattern
    EXPECT_FALSE(string_utils::matches_pattern("test", "invalid"));
}

// Test that UK National Insurance number validation works
TEST_F(UtilitiesTest, UKNationalInsuranceNumberValidationWorks) {
    // Valid UK NI numbers
    EXPECT_TRUE(MedicalUtils::is_valid_uk_ni_number("*********"));
    EXPECT_TRUE(MedicalUtils::is_valid_uk_ni_number("*********"));
    EXPECT_TRUE(MedicalUtils::is_valid_uk_ni_number("QR555666A"));
    
    // Invalid UK NI numbers
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number(""));
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("AB12345C"));   // Too short
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("AB1234567C"));  // Too long
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("1B123456C"));   // First char not letter
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("A1123456C"));   // Second char not letter
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("ABCDEFGHC"));   // Middle not digits
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("*********"));   // Last char not letter
    
    // Invalid prefixes
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("*********"));   // Invalid prefix
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("*********"));   // Invalid prefix
    EXPECT_FALSE(MedicalUtils::is_valid_uk_ni_number("*********"));   // Invalid prefix
}

// Test that UK postcode validation works correctly
TEST_F(UtilitiesTest, UKPostcodeValidationWorksCorrectly) {
    // Valid UK postcodes
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("M1 1AA"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("M60 1NW"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("B33 8TH"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("W1A 0AX"));
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("GIR 0AA"));  // Special case
    EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode("SW1A1AA"));  // Without space
    
    // Invalid UK postcodes
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode(""));
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("M1"));       // Too short
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("M1 1AAA"));  // Too long
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("1M 1AA"));   // Invalid format
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("MA 1AA"));   // Invalid format
    EXPECT_FALSE(ValidationUtils::is_valid_uk_postcode("M1 A1A"));   // Invalid format
}

// Test that UUID generation and validation works
TEST_F(UtilitiesTest, UUIDGenerationAndValidationWorks) {
    std::string uuid1 = CryptoUtils::generate_uuid();
    std::string uuid2 = CryptoUtils::generate_uuid();
    
    EXPECT_TRUE(ValidationUtils::is_valid_uuid(uuid1));
    EXPECT_TRUE(ValidationUtils::is_valid_uuid(uuid2));
    EXPECT_NE(uuid1, uuid2); // Should be different
    
    // Invalid UUIDs
    EXPECT_FALSE(ValidationUtils::is_valid_uuid(""));
    EXPECT_FALSE(ValidationUtils::is_valid_uuid("invalid-uuid"));
    EXPECT_FALSE(ValidationUtils::is_valid_uuid("12345678-1234-1234-1234-**********abc")); // Too long
}

// Test memory tracker functionality
TEST_F(UtilitiesTest, MemoryTrackerFunctionality) {
    PerformanceUtils::MemoryTracker tracker;
    
    // Current usage should be non-negative
    EXPECT_GE(tracker.current_usage(), 0u);
    
    // Peak usage should be at least current usage
    EXPECT_GE(tracker.peak_usage(), tracker.current_usage());
    
    // Global peak usage should be non-negative
    EXPECT_GE(PerformanceUtils::MemoryTracker::global_peak_usage(), 0u);
    
    // Test reset
    tracker.reset();
    EXPECT_GE(tracker.current_usage(), 0u);
}

// Test format functions with UK localization
TEST_F(UtilitiesTest, FormatFunctionsWithUKLocalization) {
    // Test format_bytes
    EXPECT_EQ(PerformanceUtils::format_bytes(1024), "1.00 KB");
    EXPECT_EQ(PerformanceUtils::format_bytes(1048576), "1.00 MB");
    EXPECT_EQ(PerformanceUtils::format_bytes(1073741824), "1.00 GB");
    EXPECT_EQ(PerformanceUtils::format_bytes(512), "512.00 B");
    
    // Test format_duration
    EXPECT_EQ(PerformanceUtils::format_duration(0.5), "500ms");
    EXPECT_EQ(PerformanceUtils::format_duration(65), "1m 5s");
    EXPECT_EQ(PerformanceUtils::format_duration(3665), "1h 1m 5s");
    EXPECT_EQ(PerformanceUtils::format_duration(0.001), "1ms");
    
    // Test calculate_throughput
    EXPECT_DOUBLE_EQ(PerformanceUtils::calculate_throughput(1000, 10), 100.0);
    EXPECT_DOUBLE_EQ(PerformanceUtils::calculate_throughput(0, 10), 0.0);
    EXPECT_DOUBLE_EQ(PerformanceUtils::calculate_throughput(1000, 0), 0.0);
}

// Test ProcessingUtils functions
TEST_F(UtilitiesTest, ProcessingUtilsFunctions) {
    EXPECT_EQ(ProcessingUtils::stage_name(0), "Extract");
    EXPECT_EQ(ProcessingUtils::stage_name(1), "Transform");
    EXPECT_EQ(ProcessingUtils::stage_name(2), "Load");
    EXPECT_EQ(ProcessingUtils::stage_name(99), "Unknown");
    EXPECT_EQ(ProcessingUtils::stage_name(-1), "Unknown");
}

// Test UK localization namespace functions
TEST_F(UtilitiesTest, UKLocalizationNamespaceFunctions) {
    // Test format_uk_currency
    EXPECT_EQ(UKLocalization::format_uk_currency(100.00), "£100.00");
    EXPECT_EQ(UKLocalization::format_uk_currency(1234.56), "£1,234.56");
    EXPECT_EQ(UKLocalization::format_uk_currency(0.99), "£0.99");
    
    // Test format_uk_date
    std::tm test_tm = {};
    test_tm.tm_year = 125; // 2025
    test_tm.tm_mon = 0;    // January
    test_tm.tm_mday = 15;
    auto test_date = std::chrono::system_clock::from_time_t(std::mktime(&test_tm));
    EXPECT_EQ(UKLocalization::format_uk_date(test_date), "15/01/2025");
    
    // Test temperature conversions
    EXPECT_DOUBLE_EQ(UKLocalization::celsius_to_fahrenheit(0), 32.0);
    EXPECT_DOUBLE_EQ(UKLocalization::celsius_to_fahrenheit(100), 212.0);
    EXPECT_DOUBLE_EQ(UKLocalization::fahrenheit_to_celsius(32), 0.0);
    EXPECT_DOUBLE_EQ(UKLocalization::fahrenheit_to_celsius(212), 100.0);
    
    // Test format_temperature_celsius
    EXPECT_EQ(UKLocalization::format_temperature_celsius(36.5), "36.5°C");
    EXPECT_EQ(UKLocalization::format_temperature_celsius(0), "0.0°C");
    EXPECT_EQ(UKLocalization::format_temperature_celsius(-10.5), "-10.5°C");
    
    // Test format_uk_phone
    EXPECT_EQ(UKLocalization::format_uk_phone(20, 71234567), "+44 20 712 34567");
    EXPECT_EQ(UKLocalization::format_uk_phone(11, 1234567), "+44 11 123 4567");
    EXPECT_EQ(UKLocalization::format_uk_phone(1, 234567), "+44 1 234567");
}

// Test edge cases for validation functions
TEST_F(UtilitiesTest, ValidationEdgeCases) {
    // Test email validation edge cases
    EXPECT_FALSE(ValidationUtils::is_valid_email("@example.com"));
    EXPECT_FALSE(ValidationUtils::is_valid_email("user@"));
    EXPECT_FALSE(ValidationUtils::is_valid_email("user@.com"));
    EXPECT_FALSE(ValidationUtils::is_valid_email("user@@example.com"));
    
    // Test IP validation edge cases
    EXPECT_FALSE(ValidationUtils::is_valid_ip("256.256.256.256"));
    EXPECT_FALSE(ValidationUtils::is_valid_ip("192.168.1"));
    EXPECT_FALSE(ValidationUtils::is_valid_ip("***********.1"));
    
    // Test URL validation edge cases
    EXPECT_FALSE(ValidationUtils::is_valid_url("http://"));
    EXPECT_FALSE(ValidationUtils::is_valid_url("://example.com"));
}

} // namespace omop::common::test