#include "common/utilities.h"
#include <gtest/gtest.h>
#include <unordered_map>

class EnumUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_map_ = {
            {1, "One"},
            {2, "Two"},
            {3, "Three"}
        };
    }

    std::unordered_map<int, std::string> test_map_;
};

TEST_F(EnumUtilsTest, EnumToStringConversion) {
    EXPECT_EQ(omop::common::EnumUtils::enum_to_string(1, test_map_), "One");
    EXPECT_EQ(omop::common::EnumUtils::enum_to_string(2, test_map_), "Two");
    EXPECT_EQ(omop::common::EnumUtils::enum_to_string(3, test_map_), "Three");
    EXPECT_EQ(omop::common::EnumUtils::enum_to_string(4, test_map_), "Unknown");
}

TEST_F(EnumUtilsTest, StringToEnumConversion) {
    auto result1 = omop::common::EnumUtils::string_to_enum("One", test_map_);
    EXPECT_TRUE(result1.has_value());
    EXPECT_EQ(result1.value(), 1);

    auto result2 = omop::common::EnumUtils::string_to_enum("Two", test_map_);
    EXPECT_TRUE(result2.has_value());
    EXPECT_EQ(result2.value(), 2);

    auto result3 = omop::common::EnumUtils::string_to_enum("Three", test_map_);
    EXPECT_TRUE(result3.has_value());
    EXPECT_EQ(result3.value(), 3);

    auto invalid_result = omop::common::EnumUtils::string_to_enum("Four", test_map_);
    EXPECT_FALSE(invalid_result.has_value());
}

TEST_F(EnumUtilsTest, GetAllEnumStrings) {
    auto all_strings = omop::common::EnumUtils::get_all_enum_strings(test_map_);
    EXPECT_EQ(all_strings.size(), 3);
    EXPECT_NE(std::find(all_strings.begin(), all_strings.end(), "One"), all_strings.end());
    EXPECT_NE(std::find(all_strings.begin(), all_strings.end(), "Two"), all_strings.end());
    EXPECT_NE(std::find(all_strings.begin(), all_strings.end(), "Three"), all_strings.end());
}

TEST_F(EnumUtilsTest, ValidateEnumString) {
    EXPECT_TRUE(omop::common::EnumUtils::is_valid_enum_string("One", test_map_));
    EXPECT_TRUE(omop::common::EnumUtils::is_valid_enum_string("Two", test_map_));
    EXPECT_TRUE(omop::common::EnumUtils::is_valid_enum_string("Three", test_map_));
    EXPECT_FALSE(omop::common::EnumUtils::is_valid_enum_string("Four", test_map_));
    EXPECT_FALSE(omop::common::EnumUtils::is_valid_enum_string("", test_map_));
}

TEST_F(EnumUtilsTest, DifferentEnumTypes) {
    std::unordered_map<size_t, std::string> size_map = {
        {0, "Zero"},
        {100, "Hundred"},
        {1000, "Thousand"}
    };

    EXPECT_EQ(omop::common::EnumUtils::enum_to_string(static_cast<size_t>(0), size_map), "Zero");
    EXPECT_EQ(omop::common::EnumUtils::enum_to_string(static_cast<size_t>(100), size_map), "Hundred");
    EXPECT_EQ(omop::common::EnumUtils::enum_to_string(static_cast<size_t>(1000), size_map), "Thousand");
    EXPECT_EQ(omop::common::EnumUtils::enum_to_string(static_cast<size_t>(999), size_map), "Unknown");
}

TEST_F(EnumUtilsTest, EmptyMapHandling) {
    std::unordered_map<int, std::string> empty_map;

    EXPECT_EQ(omop::common::EnumUtils::enum_to_string(1, empty_map), "Unknown");
    EXPECT_FALSE(omop::common::EnumUtils::string_to_enum("One", empty_map).has_value());
    EXPECT_TRUE(omop::common::EnumUtils::get_all_enum_strings(empty_map).empty());
    EXPECT_FALSE(omop::common::EnumUtils::is_valid_enum_string("One", empty_map));
}

TEST_F(EnumUtilsTest, EdgeCases) {
    EXPECT_FALSE(omop::common::EnumUtils::string_to_enum("", test_map_).has_value());
    EXPECT_FALSE(omop::common::EnumUtils::is_valid_enum_string("", test_map_));
    
    EXPECT_FALSE(omop::common::EnumUtils::string_to_enum("   ", test_map_).has_value());
    EXPECT_FALSE(omop::common::EnumUtils::is_valid_enum_string("   ", test_map_));
    
    EXPECT_FALSE(omop::common::EnumUtils::string_to_enum("ONE", test_map_).has_value());
    EXPECT_FALSE(omop::common::EnumUtils::is_valid_enum_string("ONE", test_map_));
}
