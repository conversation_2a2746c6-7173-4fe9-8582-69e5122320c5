/**
 * @file error_recovery_unit_test.cpp
 * @brief Unit tests for error recovery mechanisms
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 */

#include <gtest/gtest.h>
#include "common/error_recovery.h"
#include "common/exceptions.h"
#include <chrono>
#include <thread>
#include <atomic>

using namespace omop::common;

class ErrorRecoveryTest : public ::testing::Test {
protected:
    void SetUp() override {
        ErrorRecoveryEngine::Config config;
        config.enable_retry = true;
        config.enable_fallback = true;
        config.enable_circuit_breaker = true;
        config.collect_statistics = true;
        
        recovery_engine_ = std::make_unique<ErrorRecoveryEngine>(config);
    }
    
    void TearDown() override {
        recovery_engine_.reset();
    }
    
    std::unique_ptr<ErrorRecoveryEngine> recovery_engine_;
};

// Test basic retry functionality
TEST_F(ErrorRecoveryTest, BasicRetrySuccess) {
    std::atomic<int> attempt_count{0};
    
    auto operation = [&attempt_count]() -> int {
        attempt_count++;
        if (attempt_count < 3) {
            throw std::runtime_error("Temporary failure");
        }
        return 42;
    };
    
    RetryPolicy policy;
    policy.max_attempts = 5;
    policy.base_delay = std::chrono::milliseconds(10);
    
    auto result = recovery_engine_->execute_with_recovery<int>(
        "test_retry", operation, policy);
    
    ASSERT_TRUE(std::holds_alternative<int>(result));
    EXPECT_EQ(std::get<int>(result), 42);
    EXPECT_EQ(attempt_count.load(), 3);
    
    // Check statistics
    auto stats = recovery_engine_->get_statistics();
    EXPECT_EQ(stats.total_operations, 1);
    EXPECT_EQ(stats.successful_operations, 1);
    EXPECT_GT(stats.retry_attempts, 0);
}

TEST_F(ErrorRecoveryTest, RetryExhaustion) {
    std::atomic<int> attempt_count{0};
    
    auto operation = [&attempt_count]() -> int {
        attempt_count++;
        throw std::runtime_error("Persistent failure");
    };
    
    RetryPolicy policy;
    policy.max_attempts = 3;
    policy.base_delay = std::chrono::milliseconds(1);
    
    auto result = recovery_engine_->execute_with_recovery<int>(
        "test_retry_fail", operation, policy);
    
    ASSERT_TRUE(std::holds_alternative<std::exception_ptr>(result));
    EXPECT_EQ(attempt_count.load(), 3);
    
    // Check statistics  
    auto stats = recovery_engine_->get_statistics();
    EXPECT_EQ(stats.failed_operations, 1);
}

// Test fallback functionality
TEST_F(ErrorRecoveryTest, FallbackSuccess) {
    auto failing_operation = []() -> std::string {
        throw std::runtime_error("Primary operation failed");
    };
    
    auto fallback_operation = []() -> std::string {
        return "fallback_result";
    };
    
    RetryPolicy policy;
    policy.max_attempts = 2;
    policy.base_delay = std::chrono::milliseconds(1);
    
    auto result = recovery_engine_->execute_with_recovery<std::string>(
        "test_fallback", failing_operation, policy, fallback_operation);
    
    ASSERT_TRUE(std::holds_alternative<std::string>(result));
    EXPECT_EQ(std::get<std::string>(result), "fallback_result");
    
    // Check statistics
    auto stats = recovery_engine_->get_statistics();
    EXPECT_EQ(stats.fallback_used, 1);
    EXPECT_EQ(stats.recovered_operations, 1);
}

// Test circuit breaker
TEST_F(ErrorRecoveryTest, CircuitBreakerOpen) {
    auto circuit_breaker = recovery_engine_->get_circuit_breaker("test_service");
    
    // Record multiple failures to open circuit
    for (int i = 0; i < 6; ++i) {
        circuit_breaker->record_failure();
    }
    
    EXPECT_EQ(circuit_breaker->get_state(), CircuitBreaker::State::Open);
    EXPECT_FALSE(circuit_breaker->allow_request());
}

TEST_F(ErrorRecoveryTest, CircuitBreakerHalfOpen) {
    CircuitBreaker::Config config;
    config.failure_threshold = 3;
    config.timeout = std::chrono::seconds(1);  // Very short timeout for testing
    
    CircuitBreaker circuit_breaker(config);
    
    // Open the circuit
    for (int i = 0; i < 4; ++i) {
        circuit_breaker.record_failure();
    }
    
    EXPECT_EQ(circuit_breaker.get_state(), CircuitBreaker::State::Open);
    
    // Wait for timeout to elapse
    std::this_thread::sleep_for(std::chrono::milliseconds(1100));
    
    // Should transition to half-open
    EXPECT_TRUE(circuit_breaker.allow_request());
    EXPECT_EQ(circuit_breaker.get_state(), CircuitBreaker::State::HalfOpen);
    
    // Record success to close circuit
    circuit_breaker.record_success();
    circuit_breaker.record_success();
    circuit_breaker.record_success();  // Need 3 successes by default
    
    EXPECT_EQ(circuit_breaker.get_state(), CircuitBreaker::State::Closed);
}

// Test error classification
TEST_F(ErrorRecoveryTest, ErrorClassification) {
    RecoveryContext context;
    context.operation_id = "test_classification";
    
    // Test transient error
    std::runtime_error timeout_error("Connection timeout occurred");
    EXPECT_EQ(recovery_engine_->classify_error(timeout_error, context), ErrorType::Transient);
    
    // Test resource error
    std::runtime_error memory_error("Out of memory");
    EXPECT_EQ(recovery_engine_->classify_error(memory_error, context), ErrorType::Resource);
    
    // Test configuration error
    std::runtime_error config_error("Invalid configuration parameter");
    EXPECT_EQ(recovery_engine_->classify_error(config_error, context), ErrorType::Configuration);
    
    // Test security error
    std::runtime_error auth_error("Access denied: insufficient permissions");
    EXPECT_EQ(recovery_engine_->classify_error(auth_error, context), ErrorType::Security);
    
    // Test persistent error
    std::runtime_error data_error("Invalid data format");
    EXPECT_EQ(recovery_engine_->classify_error(data_error, context), ErrorType::Persistent);
    
    // Test unknown error
    std::runtime_error unknown_error("Something went wrong");
    EXPECT_EQ(recovery_engine_->classify_error(unknown_error, context), ErrorType::Unknown);
}

// Test retry policy delay calculation
TEST_F(ErrorRecoveryTest, RetryPolicyDelayCalculation) {
    RetryPolicy policy;
    policy.base_delay = std::chrono::milliseconds(100);
    policy.backoff_multiplier = 2.0;
    policy.max_delay = std::chrono::milliseconds(5000);
    policy.jitter_factor = 0.0;  // No jitter for predictable testing
    
    // First retry (attempt 1)
    auto delay1 = policy.calculate_delay(1);
    EXPECT_EQ(delay1.count(), 100);
    
    // Second retry (attempt 2)
    auto delay2 = policy.calculate_delay(2);
    EXPECT_EQ(delay2.count(), 200);
    
    // Third retry (attempt 3)
    auto delay3 = policy.calculate_delay(3);
    EXPECT_EQ(delay3.count(), 400);
}

// Test resource bulkhead
TEST(ResourceBulkheadTest, ConcurrentOperationLimit) {
    ResourceBulkhead::Config config;
    config.max_concurrent_operations = 2;
    config.queue_timeout = std::chrono::seconds(1);  // 1 second timeout
    
    ResourceBulkhead bulkhead(config);
    
    std::atomic<int> concurrent_ops{0};
    std::atomic<int> max_concurrent{0};
    std::vector<std::future<int>> futures;
    
    // Launch 5 operations that should be limited to 2 concurrent
    for (int i = 0; i < 5; ++i) {
        futures.push_back(std::async(std::launch::async, [&bulkhead, &concurrent_ops, &max_concurrent]() {
            return bulkhead.execute<int>([&concurrent_ops, &max_concurrent]() -> int {
                int current = concurrent_ops.fetch_add(1) + 1;
                int expected = max_concurrent.load();
                while (expected < current && !max_concurrent.compare_exchange_weak(expected, current)) {
                    expected = max_concurrent.load();
                }
                
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
                concurrent_ops.fetch_sub(1);
                return current;
            });
        }));
    }
    
    // Wait for all operations to complete
    for (auto& future : futures) {
        future.get();
    }
    
    // Maximum concurrent operations should not exceed the limit
    EXPECT_LE(max_concurrent.load(), 2);
}

// Test timeout decorator
TEST(TimeoutDecoratorTest, OperationTimeout) {
    TimeoutDecorator<int> decorator(std::chrono::milliseconds(50));
    
    auto slow_operation = []() -> int {
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        return 42;
    };
    
    auto result = decorator.execute(slow_operation);
    
    ASSERT_TRUE(std::holds_alternative<std::exception_ptr>(result));
    
    try {
        std::rethrow_exception(std::get<std::exception_ptr>(result));
    } catch (const std::exception& e) {
        EXPECT_TRUE(std::string(e.what()).find("timed out") != std::string::npos);
    }
}

TEST(TimeoutDecoratorTest, OperationSuccess) {
    TimeoutDecorator<int> decorator(std::chrono::milliseconds(100));
    
    auto fast_operation = []() -> int {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        return 42;
    };
    
    auto result = decorator.execute(fast_operation);
    
    ASSERT_TRUE(std::holds_alternative<int>(result));
    EXPECT_EQ(std::get<int>(result), 42);
}

// Test error recovery statistics
TEST_F(ErrorRecoveryTest, RecoveryStatistics) {
    recovery_engine_->reset_statistics();
    
    // Successful operation
    auto success_op = []() -> int { return 1; };
    recovery_engine_->execute_with_recovery<int>("success", success_op);
    
    // Failed operation with retry
    std::atomic<int> attempt{0};
    auto retry_op = [&attempt]() -> int {
        attempt++;
        if (attempt < 2) throw std::runtime_error("fail");
        return 2;
    };
    RetryPolicy policy;
    policy.max_attempts = 3;
    policy.base_delay = std::chrono::milliseconds(1);
    recovery_engine_->execute_with_recovery<int>("retry", retry_op, policy);
    
    // Completely failed operation
    auto fail_op = []() -> int { throw std::runtime_error("fail"); };
    recovery_engine_->execute_with_recovery<int>("fail", fail_op);
    
    auto stats = recovery_engine_->get_statistics();
    
    EXPECT_EQ(stats.total_operations, 3);
    EXPECT_EQ(stats.successful_operations, 2);
    EXPECT_EQ(stats.failed_operations, 1);
    EXPECT_EQ(stats.recovered_operations, 1);
    EXPECT_GT(stats.retry_attempts, 0);
    
    // Test calculated rates
    EXPECT_DOUBLE_EQ(stats.success_rate(), 2.0/3.0);
    EXPECT_DOUBLE_EQ(stats.recovery_rate(), 1.0);
}

// Test UK healthcare specific error scenarios
TEST_F(ErrorRecoveryTest, UKHealthcareErrorScenarios) {
    // Simulate NHS database connection failure
    auto nhs_db_operation = []() -> std::string {
        throw std::runtime_error("NHS database connection timeout");
    };
    
    auto nhs_fallback = []() -> std::string {
        return "Using cached NHS data";
    };
    
    RetryPolicy nhs_policy;
    nhs_policy.max_attempts = 3;
    nhs_policy.base_delay = std::chrono::milliseconds(5);
    
    auto result = recovery_engine_->execute_with_recovery<std::string>(
        "nhs_lookup", nhs_db_operation, nhs_policy, nhs_fallback);
    
    ASSERT_TRUE(std::holds_alternative<std::string>(result));
    EXPECT_EQ(std::get<std::string>(result), "Using cached NHS data");
    
    // Error should be classified as transient
    RecoveryContext context;
    std::runtime_error nhs_error("NHS database connection timeout");
    EXPECT_EQ(recovery_engine_->classify_error(nhs_error, context), ErrorType::Transient);
}

// Test performance under load
TEST_F(ErrorRecoveryTest, PerformanceUnderLoad) {
    const int num_operations = 100;
    std::vector<std::future<void>> futures;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_operations; ++i) {
        futures.push_back(std::async(std::launch::async, [this, i]() {
            auto operation = [i]() -> int {
                if (i % 10 == 0) {  // 10% failure rate
                    throw std::runtime_error("Random failure");
                }
                return i;
            };
            
            RetryPolicy policy;
            policy.max_attempts = 2;
            policy.base_delay = std::chrono::milliseconds(1);
            
            recovery_engine_->execute_with_recovery<int>(
                "perf_test_" + std::to_string(i), operation, policy);
        }));
    }
    
    // Wait for all operations
    for (auto& future : futures) {
        future.wait();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Should handle 100 operations reasonably quickly (< 1 second)
    EXPECT_LT(duration.count(), 1000);
    
    auto stats = recovery_engine_->get_statistics();
    EXPECT_EQ(stats.total_operations, num_operations);
    EXPECT_GT(stats.successful_operations, num_operations * 0.8);  // At least 80% success
}

int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}