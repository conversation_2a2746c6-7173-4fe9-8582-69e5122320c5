#include <gtest/gtest.h>
#include "common/configuration.h"
#include "load/loader_strategies.h"
#include <sstream>
#include <filesystem>

namespace omop::test::common {

/**
 * @brief Test fixture for incremental loading configuration
 *
 * Tests the new configuration features for CDC, watermarking, and incremental processing.
 */
class IncrementalConfigurationTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_manager_ = std::make_unique<omop::common::ConfigurationManager>();
    }
    
    void TearDown() override {
        config_manager_.reset();
    }
    
    std::string create_incremental_config_yaml() {
        return R"(
version: "2.0"

# Source and target database configurations
source_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "uk_healthcare_source"
  username: "etl_user"
  password: "secure_password"

target_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "omop_cdm_uk"
  username: "omop_user"
  password: "omop_password"

# Global loading configuration with incremental features
loading:
  strategy: "delta_load"
  mode: "upsert"
  batch_size: 1000
  parallel_workers: 4
  enable_cdc: true
  enable_hash_comparison: true
  cdc_table_prefix: "cdc_"
  incremental_processing: true
  default_watermark_directory: "/var/lib/omop-etl/watermarks"
  
  # CDC configuration
  cdc:
    type: "timestamp"
    change_column: "updated_at"
    operation_column: "operation_type"
    track_deletes: true
    create_cdc_table: true
    polling_interval: 60
    max_changes_per_batch: 10000
  
  # Watermark configuration
  watermark:
    type: "timestamp"
    column_name: "last_updated"
    use_uk_datetime_format: true
    uk_date_format: "%d/%m/%Y %H:%M:%S"
    persistence_path: "/var/lib/omop-etl/watermarks/global.watermark"
  
  # Table-specific loading configurations
  tables:
    person:
      strategy: "delta_load"
      mode: "upsert"
      enable_cdc: true
      watermark_column: "updated_datetime"
      watermark_file: "/var/lib/omop-etl/watermarks/person.watermark"
      tracking_columns: ["birth_datetime", "gender_concept_id", "race_concept_id"]
      cdc:
        type: "hybrid"
        change_column: "last_modified"
        track_deletes: false
      watermark:
        type: "timestamp"
        column_name: "updated_datetime"
        use_uk_datetime_format: true
    
    visit_occurrence:
      strategy: "upsert_load"
      mode: "merge"
      enable_cdc: true
      watermark_column: "visit_end_datetime"
      tracking_columns: ["visit_start_datetime", "visit_end_datetime", "visit_type_concept_id"]
      cdc:
        type: "sequence"
        change_column: "sequence_id"
        max_changes_per_batch: 5000
      watermark:
        type: "composite"
        composite_columns: ["visit_end_datetime", "sequence_id"]
    
    measurement:
      strategy: "bulk_load"
      mode: "append"
      enable_cdc: false
      batch_size: 5000

# ETL settings
etl_settings:
  batch_size: 1000
  parallel_workers: 8
  validation_mode: "strict"
  error_threshold: 0.01

# Table mappings
tables:
  person:
    source_table: "uk_patients"
    target_table: "person"
    transformations:
      - source_column: "patient_id"
        target_column: "person_id"
        type: "direct"
      - source_column: "nhs_number"
        target_column: "person_source_value"
        type: "direct"
      - source_column: "date_of_birth"
        target_column: "birth_datetime"
        type: "date_transform"
        parameters:
          input_format: "%d/%m/%Y"
          output_format: "%Y-%m-%d %H:%M:%S"
      - source_column: "gender"
        target_column: "gender_concept_id"
        type: "vocabulary_mapping"
        parameters:
          vocabulary: "Gender"
          domain_id: "Gender"

# Vocabulary mappings
vocabulary_mappings:
  Gender:
    Male: 8507
    Female: 8532
    Unknown: 8551
  None:
    None: 0
)";
    }
    
    std::string create_minimal_incremental_config() {
        return R"(
version: "2.0"

source_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "source"
  username: "test_user"
  password: "test_pass"

target_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "target"
  username: "test_user"
  password: "test_pass"

loading:
  strategy: "delta_load"
  enable_cdc: true
  watermark_column: "updated_at"

tables:
  person:
    source_table: "test_patients"
    target_table: "person"
    transformations:
      - source_column: "id"
        target_column: "person_id"
        type: "vocabulary_mapping"
        parameters:
          vocabulary: "None"
          domain_id: "Type Concept"

# Vocabulary mappings
vocabulary_mappings:
  None:
    None: 0
)";
    }

protected:
    std::unique_ptr<omop::common::ConfigurationManager> config_manager_;
};

// Test loading configuration parsing
TEST_F(IncrementalConfigurationTest, LoadingConfiguration_BasicParsing) {
    std::string yaml_config = create_incremental_config_yaml();
    
    ASSERT_NO_THROW(config_manager_->load_config_from_string(yaml_config));
    EXPECT_TRUE(config_manager_->is_loaded());
    
    const auto& loading_config = config_manager_->get_loading_config();
    
    // Test basic loading configuration
    EXPECT_EQ(loading_config.get_strategy(), omop::load::LoadingStrategy::DeltaLoad);
    EXPECT_EQ(loading_config.get_mode(), omop::load::LoadingMode::Upsert);
    EXPECT_EQ(loading_config.get_batch_size(), 1000);
    EXPECT_EQ(loading_config.get_parallel_workers(), 4);
    EXPECT_TRUE(loading_config.is_cdc_enabled());
    EXPECT_TRUE(loading_config.is_hash_comparison_enabled());
    EXPECT_EQ(loading_config.get_cdc_table_prefix(), "cdc_");
}

// Test CDC configuration parsing
TEST_F(IncrementalConfigurationTest, LoadingConfiguration_CDCParsing) {
    std::string yaml_config = create_incremental_config_yaml();
    config_manager_->load_config_from_string(yaml_config);
    
    const auto& loading_config = config_manager_->get_loading_config();
    const auto& cdc_config = loading_config.get_cdc_config();
    
    // Test CDC configuration
    EXPECT_EQ(cdc_config.cdc_type, omop::load::CDCConfig::CDCType::Timestamp);
    EXPECT_EQ(cdc_config.change_column, "updated_at");
    EXPECT_EQ(cdc_config.operation_column, "operation_type");
    EXPECT_TRUE(cdc_config.track_deletes);
    EXPECT_TRUE(cdc_config.create_cdc_table);
    EXPECT_EQ(cdc_config.polling_interval.count(), 60);
    EXPECT_EQ(cdc_config.max_changes_per_batch, 10000);
}

// Test watermark configuration parsing
TEST_F(IncrementalConfigurationTest, LoadingConfiguration_WatermarkParsing) {
    std::string yaml_config = create_incremental_config_yaml();
    config_manager_->load_config_from_string(yaml_config);
    
    const auto& loading_config = config_manager_->get_loading_config();
    const auto& watermark_info = loading_config.get_watermark_info();
    
    // Test watermark configuration
    EXPECT_EQ(watermark_info.type, omop::load::WatermarkInfo::WatermarkType::Timestamp);
    EXPECT_EQ(watermark_info.column_name, "last_updated");
    EXPECT_TRUE(watermark_info.use_uk_datetime_format);
    EXPECT_EQ(watermark_info.uk_date_format, "%d/%m/%Y %H:%M:%S");
    EXPECT_EQ(watermark_info.persistence_path, "/var/lib/omop-etl/watermarks/global.watermark");
}

// Test global incremental processing settings
TEST_F(IncrementalConfigurationTest, LoadingConfiguration_GlobalIncrementalSettings) {
    std::string yaml_config = create_incremental_config_yaml();
    config_manager_->load_config_from_string(yaml_config);
    
    // Test global incremental processing flag
    EXPECT_TRUE(config_manager_->is_incremental_processing_enabled());
    EXPECT_EQ(config_manager_->get_default_watermark_directory(), "/var/lib/omop-etl/watermarks");
}

// Test table-specific loading configuration
TEST_F(IncrementalConfigurationTest, LoadingConfiguration_TableSpecific) {
    std::string yaml_config = create_incremental_config_yaml();
    config_manager_->load_config_from_string(yaml_config);
    
    // Test person table configuration
    auto person_config = config_manager_->get_table_loading_config("person");
    ASSERT_TRUE(person_config.has_value());
    
    EXPECT_EQ(person_config->get_strategy(), omop::load::LoadingStrategy::DeltaLoad);
    EXPECT_EQ(person_config->get_mode(), omop::load::LoadingMode::Upsert);
    EXPECT_TRUE(person_config->is_cdc_enabled());
    EXPECT_EQ(person_config->get_watermark_column(), "updated_datetime");
    EXPECT_EQ(person_config->get_watermark_file(), "/var/lib/omop-etl/watermarks/person.watermark");
    
    const auto& tracking_columns = person_config->get_tracking_columns();
    EXPECT_EQ(tracking_columns.size(), 3);
    EXPECT_TRUE(std::find(tracking_columns.begin(), tracking_columns.end(), "birth_datetime") != tracking_columns.end());
    EXPECT_TRUE(std::find(tracking_columns.begin(), tracking_columns.end(), "gender_concept_id") != tracking_columns.end());
    EXPECT_TRUE(std::find(tracking_columns.begin(), tracking_columns.end(), "race_concept_id") != tracking_columns.end());
    
    // Test person table CDC configuration
    const auto& person_cdc = person_config->get_cdc_config();
    EXPECT_EQ(person_cdc.cdc_type, omop::load::CDCConfig::CDCType::Hybrid);
    EXPECT_EQ(person_cdc.change_column, "last_modified");
    EXPECT_FALSE(person_cdc.track_deletes);
    
    // Test visit_occurrence table configuration
    auto visit_config = config_manager_->get_table_loading_config("visit_occurrence");
    ASSERT_TRUE(visit_config.has_value());
    
    EXPECT_EQ(visit_config->get_strategy(), omop::load::LoadingStrategy::UpsertLoad);
    EXPECT_EQ(visit_config->get_mode(), omop::load::LoadingMode::Merge);
    
    const auto& visit_cdc = visit_config->get_cdc_config();
    EXPECT_EQ(visit_cdc.cdc_type, omop::load::CDCConfig::CDCType::Sequence);
    EXPECT_EQ(visit_cdc.change_column, "sequence_id");
    EXPECT_EQ(visit_cdc.max_changes_per_batch, 5000);
    
    const auto& visit_watermark = visit_config->get_watermark_info();
    EXPECT_EQ(visit_watermark.type, omop::load::WatermarkInfo::WatermarkType::Composite);
    EXPECT_EQ(visit_watermark.composite_columns.size(), 2);
    EXPECT_EQ(visit_watermark.composite_columns[0], "visit_end_datetime");
    EXPECT_EQ(visit_watermark.composite_columns[1], "sequence_id");
    
    // Test measurement table configuration (non-incremental)
    auto measurement_config = config_manager_->get_table_loading_config("measurement");
    ASSERT_TRUE(measurement_config.has_value());
    
    EXPECT_EQ(measurement_config->get_strategy(), omop::load::LoadingStrategy::BulkLoad);
    EXPECT_EQ(measurement_config->get_mode(), omop::load::LoadingMode::Append);
    EXPECT_FALSE(measurement_config->is_cdc_enabled());
    EXPECT_EQ(measurement_config->get_batch_size(), 5000);
}

// Test conversion to LoadingConfig struct
TEST_F(IncrementalConfigurationTest, LoadingConfiguration_ConversionToStruct) {
    std::string yaml_config = create_incremental_config_yaml();
    config_manager_->load_config_from_string(yaml_config);
    
    const auto& loading_config = config_manager_->get_loading_config();
    auto loading_config_struct = loading_config.to_loading_config();
    
    // Verify conversion preserves all fields
    EXPECT_EQ(loading_config_struct.strategy, omop::load::LoadingStrategy::DeltaLoad);
    EXPECT_EQ(loading_config_struct.mode, omop::load::LoadingMode::Upsert);
    EXPECT_EQ(loading_config_struct.batch_size, 1000);
    EXPECT_EQ(loading_config_struct.parallel_workers, 4);
    EXPECT_TRUE(loading_config_struct.enable_cdc);
    EXPECT_TRUE(loading_config_struct.enable_hash_comparison);
    EXPECT_EQ(loading_config_struct.watermark_column, "last_updated");
    EXPECT_EQ(loading_config_struct.cdc_table_prefix, "cdc_");
}

// Test default loading configuration
TEST_F(IncrementalConfigurationTest, LoadingConfiguration_DefaultSettings) {
    std::string minimal_config = R"(
version: "2.0"
source_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "test_source"
  username: "test_user"
  password: "test_pass"
target_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "test_target"
  username: "test_user"
  password: "test_pass"
tables:
  person:
    source_table: "test_patients"
    target_table: "person"
    transformations:
      - source_column: "id"
        target_column: "person_id"
        type: "direct"
)";
    
    config_manager_->load_config_from_string(minimal_config);
    
    const auto& loading_config = config_manager_->get_loading_config();
    
    // Test default values
    EXPECT_EQ(loading_config.get_strategy(), omop::load::LoadingStrategy::BatchInsert);
    EXPECT_EQ(loading_config.get_mode(), omop::load::LoadingMode::Insert);
    EXPECT_EQ(loading_config.get_batch_size(), 1000);
    EXPECT_EQ(loading_config.get_parallel_workers(), 1);
    EXPECT_FALSE(loading_config.is_cdc_enabled());
    EXPECT_TRUE(loading_config.is_hash_comparison_enabled());
    EXPECT_EQ(loading_config.get_cdc_table_prefix(), "cdc_");
}

// Test minimal incremental configuration
TEST_F(IncrementalConfigurationTest, LoadingConfiguration_MinimalIncremental) {
    std::string yaml_config = create_minimal_incremental_config();
    config_manager_->load_config_from_string(yaml_config);
    
    const auto& loading_config = config_manager_->get_loading_config();
    
    EXPECT_EQ(loading_config.get_strategy(), omop::load::LoadingStrategy::DeltaLoad);
    EXPECT_TRUE(loading_config.is_cdc_enabled());
    EXPECT_EQ(loading_config.get_watermark_column(), "updated_at");
}

// Test different strategy and mode combinations
TEST_F(IncrementalConfigurationTest, LoadingConfiguration_StrategyModeVariations) {
    // Test different string formats for strategies and modes
    std::vector<std::pair<std::string, omop::load::LoadingStrategy>> strategy_tests = {
        {"delta_load", omop::load::LoadingStrategy::DeltaLoad},
        {"deltaload", omop::load::LoadingStrategy::DeltaLoad},
        {"upsert_load", omop::load::LoadingStrategy::UpsertLoad},
        {"bulk_load", omop::load::LoadingStrategy::BulkLoad},
        {"parallel_load", omop::load::LoadingStrategy::ParallelLoad}
    };
    
    for (const auto& [strategy_str, expected_strategy] : strategy_tests) {
        std::string test_config = std::format(R"(
version: "2.0"
source_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "test_source"
  username: "test_user"
  password: "test_pass"
target_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "test_target"
  username: "test_user"
  password: "test_pass"
loading:
  strategy: "{}"
tables:
  person:
    source_table: "test_patients"
    target_table: "person"
    transformations:
      - source_column: "id"
        target_column: "person_id"
        type: "direct"
)", strategy_str);
        
        auto test_manager = std::make_unique<omop::common::ConfigurationManager>();
        test_manager->load_config_from_string(test_config);
        
        const auto& loading_config = test_manager->get_loading_config();
        EXPECT_EQ(loading_config.get_strategy(), expected_strategy);
    }
    
    // Test different modes
    std::vector<std::pair<std::string, omop::load::LoadingMode>> mode_tests = {
        {"insert", omop::load::LoadingMode::Insert},
        {"update", omop::load::LoadingMode::Update},
        {"upsert", omop::load::LoadingMode::Upsert},
        {"merge", omop::load::LoadingMode::Merge},
        {"append", omop::load::LoadingMode::Append},
        {"replace", omop::load::LoadingMode::Replace}
    };
    
    for (const auto& [mode_str, expected_mode] : mode_tests) {
        std::string test_config = std::format(R"(
version: "2.0"
source_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "test_source"
  username: "test_user"
  password: "test_pass"
target_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "test_target"
  username: "test_user"
  password: "test_pass"
loading:
  mode: "{}"
tables:
  person:
    source_table: "test_patients"
    target_table: "person"
    transformations:
      - source_column: "id"
        target_column: "person_id"
        type: "direct"
)", mode_str);
        
        auto test_manager = std::make_unique<omop::common::ConfigurationManager>();
        test_manager->load_config_from_string(test_config);
        
        const auto& loading_config = test_manager->get_loading_config();
        EXPECT_EQ(loading_config.get_mode(), expected_mode);
    }
}

// Test error handling for invalid configurations
TEST_F(IncrementalConfigurationTest, LoadingConfiguration_ErrorHandling) {
    // Test invalid strategy
    std::string invalid_strategy_config = R"(
version: "2.0"
source_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "test_source"
  username: "test_user"
  password: "test_pass"
target_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "test_target"
  username: "test_user"
  password: "test_pass"
loading:
  strategy: "invalid_strategy"
tables:
  person:
    source_table: "test_patients"
    target_table: "person"
    transformations:
      - source_column: "id"
        target_column: "person_id"
        type: "direct"
)";
    
    // Should not throw, but should use default strategy
    EXPECT_NO_THROW(config_manager_->load_config_from_string(invalid_strategy_config));
    const auto& loading_config = config_manager_->get_loading_config();
    // Should fall back to default
    EXPECT_EQ(loading_config.get_strategy(), omop::load::LoadingStrategy::BatchInsert);
    
    // Test invalid mode
    std::string invalid_mode_config = R"(
version: "2.0"
source_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "test_source"
  username: "test_user"
  password: "test_pass"
target_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "test_target"
  username: "test_user"
  password: "test_pass"
loading:
  mode: "invalid_mode"
tables:
  person:
    source_table: "test_patients"
    target_table: "person"
    transformations:
      - source_column: "id"
        target_column: "person_id"
        type: "direct"
)";
    
    auto test_manager = std::make_unique<omop::common::ConfigurationManager>();
    EXPECT_NO_THROW(test_manager->load_config_from_string(invalid_mode_config));
    const auto& test_loading_config = test_manager->get_loading_config();
    // Should fall back to default
    EXPECT_EQ(test_loading_config.get_mode(), omop::load::LoadingMode::Insert);
}

// Test table-specific loading configuration fallback
TEST_F(IncrementalConfigurationTest, LoadingConfiguration_TableFallback) {
    std::string yaml_config = create_incremental_config_yaml();
    config_manager_->load_config_from_string(yaml_config);
    
    // Test non-existent table should get global configuration
    auto unknown_config = config_manager_->get_table_loading_config("unknown_table");
    ASSERT_TRUE(unknown_config.has_value());
    
    // Should match global configuration
    const auto& global_config = config_manager_->get_loading_config();
    EXPECT_EQ(unknown_config->get_strategy(), global_config.get_strategy());
    EXPECT_EQ(unknown_config->get_mode(), global_config.get_mode());
    EXPECT_EQ(unknown_config->is_cdc_enabled(), global_config.is_cdc_enabled());
}

// Test UK-specific datetime and watermark configurations
TEST_F(IncrementalConfigurationTest, LoadingConfiguration_UKSpecificSettings) {
    std::string uk_config = R"(
version: "2.0"
source_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "uk_source"
  username: "test_user"
  password: "test_pass"
target_database:
  type: "postgresql"  
  host: "localhost"
  port: 5432
  database: "uk_target"
  username: "test_user"
  password: "test_pass"
loading:
  watermark:
    type: "timestamp"
    use_uk_datetime_format: true
    uk_date_format: "%d/%m/%Y %H:%M:%S"
  tables:
    nhs_patients:
      watermark:
        use_uk_datetime_format: true
        uk_date_format: "%d-%m-%Y %H:%M"
tables:
  nhs_patients:
    source_table: "uk_patients"
    target_table: "person"
    transformations:
      - source_column: "id"
        target_column: "person_id"
        type: "direct"
)";
    
    config_manager_->load_config_from_string(uk_config);
    
    const auto& loading_config = config_manager_->get_loading_config();
    const auto& watermark_info = loading_config.get_watermark_info();
    
    EXPECT_TRUE(watermark_info.use_uk_datetime_format);
    EXPECT_EQ(watermark_info.uk_date_format, "%d/%m/%Y %H:%M:%S");
    
    // Test table-specific UK format
    auto nhs_config = config_manager_->get_table_loading_config("nhs_patients");
    ASSERT_TRUE(nhs_config.has_value());
    
    const auto& nhs_watermark = nhs_config->get_watermark_info();
    EXPECT_TRUE(nhs_watermark.use_uk_datetime_format);
    EXPECT_EQ(nhs_watermark.uk_date_format, "%d-%m-%Y %H:%M");
}

} // namespace omop::test::common