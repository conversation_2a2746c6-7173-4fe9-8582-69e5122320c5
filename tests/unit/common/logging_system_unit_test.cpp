/**
 * @file logging_test.cpp
 * @brief Unit tests for logging framework with UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "common/logging.h"
#include "common/utilities.h"
#include <sstream>
#include <thread>
#include <chrono>
#include <mutex>
#include <locale>
#include <iomanip>
#include <fstream>
#include <filesystem>
#include <any>

using namespace omop::common;

namespace omop::common::test {

// Mock log sink for testing UK-localized logging
class MockLogSink : public ILogSink {
public:
    void write(const LogEntry& entry) override {
        std::lock_guard<std::mutex> lock(mutex_);
        entries.push_back(entry);
        if (formatter_) {
            formatted_messages.push_back(formatter_->format(entry));
        }
    }

    void flush() override {
        std::lock_guard<std::mutex> lock(mutex_);
        flush_called = true;
    }

    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        entries.clear();
        formatted_messages.clear();
        flush_called = false;
    }

    size_t getEntryCount() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return entries.size();
    }

    LogEntry getLastEntry() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return entries.empty() ? LogEntry{} : entries.back();
    }

    std::string getLastFormattedMessage() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return formatted_messages.empty() ? "" : formatted_messages.back();
    }

    bool wasFlushCalled() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return flush_called;
    }

    std::vector<LogEntry> entries;
    std::vector<std::string> formatted_messages;
    bool flush_called = false;
    mutable std::mutex mutex_;
};

// Test fixture for logging tests with UK locale setup
class LoggingTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        mock_sink_ = std::make_shared<MockLogSink>();
        logger_ = Logger::get("uk_test_logger");
        logger_->clear_sinks();
        logger_->add_sink(mock_sink_);
        logger_->set_level(LogLevel::Trace);
        
        // UK-specific formatting
        uk_currency_symbol_ = "£";
        uk_date_format_ = "%d/%m/%Y %H:%M";
        nhs_number_ = "************";
        uk_postcode_ = "M60 1QD";
    }

    void TearDown() override {
        logger_->clear_sinks();
        mock_sink_->clear();
    }

    std::shared_ptr<MockLogSink> mock_sink_;
    std::shared_ptr<Logger> logger_;
    std::string uk_currency_symbol_;
    std::string uk_date_format_;
    std::string nhs_number_;
    std::string uk_postcode_;
};

// Test basic logger functionality
TEST_F(LoggingTest, BasicLoggerFunctionality) {
    logger_->trace("Trace message");
    logger_->debug("Debug message");
    logger_->info("Info message");
    logger_->warn("Warning message");
    logger_->error("Error message");
    logger_->critical("Critical message");
    
    EXPECT_EQ(mock_sink_->getEntryCount(), 6u);
    
    // Check that messages were logged at correct levels
    const auto& entries = mock_sink_->entries;
    EXPECT_EQ(entries[0].level, LogLevel::Trace);
    EXPECT_EQ(entries[1].level, LogLevel::Debug);
    EXPECT_EQ(entries[2].level, LogLevel::Info);
    EXPECT_EQ(entries[3].level, LogLevel::Warning);
    EXPECT_EQ(entries[4].level, LogLevel::Error);
    EXPECT_EQ(entries[5].level, LogLevel::Critical);
}

// Test logger with formatted messages
TEST_F(LoggingTest, LoggerWithFormattedMessages) {
    logger_->info("Processing NHS number: {}, Postcode: {}", nhs_number_, uk_postcode_);
    logger_->debug("Cost: {}, Temperature: {}°C", omop::common::UKLocalization::format_uk_currency(125.50), 36.5);
    
    EXPECT_EQ(mock_sink_->getEntryCount(), 2u);
    
    auto entry1 = mock_sink_->entries[0];
    EXPECT_TRUE(entry1.message.find(nhs_number_) != std::string::npos);
    EXPECT_TRUE(entry1.message.find(uk_postcode_) != std::string::npos);
    
    auto entry2 = mock_sink_->entries[1];
    EXPECT_TRUE(entry2.message.find("£125.50") != std::string::npos);
    EXPECT_TRUE(entry2.message.find("36.5°C") != std::string::npos);
}

// Test logger exception handling
TEST_F(LoggingTest, LoggerExceptionHandling) {
    try {
        throw std::runtime_error("NHS database connection failed");
    } catch (const std::exception& e) {
        std::unordered_map<std::string, std::any> context;
        context["database"] = std::string("uk_clinical_data");
        context["retry_count"] = 3;
        
        logger_->log_exception(e, context);
    }
    
    EXPECT_EQ(mock_sink_->getEntryCount(), 1u);
    auto entry = mock_sink_->getLastEntry();
    EXPECT_EQ(entry.level, LogLevel::Error);
    EXPECT_TRUE(entry.message.find("NHS database connection failed") != std::string::npos);
    EXPECT_TRUE(entry.stack_trace.has_value());
    EXPECT_EQ(std::any_cast<std::string>(entry.context.at("database")), "uk_clinical_data");
    EXPECT_EQ(std::any_cast<int>(entry.context.at("retry_count")), 3);
}

// Test file sink functionality
TEST_F(LoggingTest, FileSinkFunctionality) {
    std::string test_file = "test_uk_log.txt";
    {
        auto file_sink = std::make_shared<FileSink>(test_file, 1024 * 1024, 3);
        auto formatter = std::make_unique<TextLogFormatter>();
        file_sink->set_formatter(std::move(formatter));
        
        LogEntry entry;
        entry.timestamp = std::chrono::system_clock::now();
        entry.level = LogLevel::Info;
        entry.logger_name = "uk_test";
        entry.message = "NHS ETL process started";
        entry.thread_id = "1234";
        entry.job_id = "NHS-ETL-001";
        entry.component = "patient_loader";
        
        file_sink->write(entry);
        file_sink->flush();
    }

    // Verify file was created and contains expected content
    std::ifstream file(test_file);
    ASSERT_TRUE(file.is_open());
    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    EXPECT_TRUE(content.find("NHS ETL process started") != std::string::npos);
    file.close();
    
            // Clean up
        std::filesystem::remove(test_file);
    }
    
    // Test rotating file sink functionality
    TEST_F(LoggingTest, RotatingFileSinkFunctionality) {
        std::string base_file = "test_uk_rotating.log";
        size_t max_size = 100; // Small size to trigger rotation
        size_t max_files = 2;
        
        {
            auto rotating_sink = std::make_shared<RotatingFileSink>(base_file, max_size, max_files);
            auto formatter = std::make_unique<TextLogFormatter>();
            rotating_sink->set_formatter(std::move(formatter));
            
            // Write enough data to trigger rotation
            for (int i = 0; i < 10; ++i) {
                LogEntry entry;
                entry.timestamp = std::chrono::system_clock::now();
                entry.level = LogLevel::Info;
                entry.message = "UK healthcare data processing batch " + std::to_string(i);
                entry.thread_id = "main";
                entry.job_id = "UK-ETL-" + std::to_string(i);
                
                rotating_sink->write(entry);
                rotating_sink->flush();
            }
        }
        
        // Verify rotation files were created
        EXPECT_TRUE(std::filesystem::exists(base_file));
        EXPECT_TRUE(std::filesystem::exists(base_file + ".1"));
        
        // Clean up
        std::filesystem::remove(base_file);
        std::filesystem::remove(base_file + ".1");
    }
    
    // Test database log sink with mocked database
    TEST_F(LoggingTest, DatabaseLogSinkFunctionality) {
        auto db_sink = std::make_shared<DatabaseLogSink>(nullptr, "test_logs");
        
        LogEntry entry;
        entry.timestamp = std::chrono::system_clock::now();
        entry.level = LogLevel::Warning;
        entry.message = "NHS number validation failed";
        entry.thread_id = "validation_thread";
        entry.job_id = "UK-VALIDATION-001";
        entry.component = "nhs_validator";
        entry.context["nhs_number"] = "**********";
        entry.context["error_code"] = "INVALID_CHECKSUM";
        
        // This would normally write to database, but we're just testing the structure
        EXPECT_NO_THROW(db_sink->write(entry));
    }
    
    // Test logging configuration from YAML
    TEST_F(LoggingTest, LoggingConfigurationFromYAML) {
        std::string yaml_config = R"(
loggers:
  uk_etl:
    level: INFO
    sinks:
      - type: console
        formatter: text
      - type: file
        filename: uk_etl.log
        formatter: json
    )";
        
        // This would normally parse YAML and configure loggers
        // For now, just verify the YAML structure is valid
        EXPECT_FALSE(yaml_config.empty());
        EXPECT_TRUE(yaml_config.find("uk_etl") != std::string::npos);
    }
    
    // Test concurrent logging safety
    TEST_F(LoggingTest, ConcurrentLoggingSafety) {
        auto logger = std::make_shared<Logger>("concurrent_test");
        auto console_sink = std::make_shared<ConsoleSink>();
        auto formatter = std::make_unique<TextLogFormatter>();
        console_sink->set_formatter(std::move(formatter));
        logger->add_sink(console_sink);
        
        std::vector<std::thread> threads;
        std::atomic<int> message_count{0};
        
        // Create multiple threads logging simultaneously
        for (int i = 0; i < 5; ++i) {
            threads.emplace_back([logger, i, &message_count]() {
                for (int j = 0; j < 10; ++j) {
                    logger->info("Thread {} message {}", i, j);
                    message_count++;
                }
            });
        }
        
        // Wait for all threads to complete
        for (auto& thread : threads) {
            thread.join();
        }
        
        EXPECT_EQ(message_count.load(), 50);
    }
    
    // Test logger singleton behavior
    TEST_F(LoggingTest, LoggerSingletonBehavior) {
        auto logger1 = Logger::get("singleton_test");
        auto logger2 = Logger::get("singleton_test");
        
        EXPECT_EQ(logger1, logger2);
        EXPECT_EQ(logger1->get_name(), "singleton_test");
    }
    
    // Test logging with empty/null values
    TEST_F(LoggingTest, LoggingWithEmptyValues) {
        auto logger = std::make_shared<Logger>("empty_test");
        auto console_sink = std::make_shared<ConsoleSink>();
        auto formatter = std::make_unique<TextLogFormatter>();
        console_sink->set_formatter(std::move(formatter));
        logger->add_sink(console_sink);
        
        // Test logging empty strings
        EXPECT_NO_THROW(logger->info(""));
        EXPECT_NO_THROW(logger->info("   "));
        
        // Test logging with empty context
        LogEntry entry;
        entry.timestamp = std::chrono::system_clock::now();
        entry.level = LogLevel::Info;
        entry.message = "Test with empty context";
        entry.context = {};
        
        EXPECT_NO_THROW(console_sink->write(entry));
    }
    
    // Test performance logger edge cases
    TEST_F(LoggingTest, PerformanceLoggerEdgeCases) {
        auto logger = Logger::get("test_perf");
        auto perf_logger = std::make_shared<PerformanceLogger>(logger);
        
        // Test with zero duration
        auto start = std::chrono::steady_clock::now();
        perf_logger->log_operation("instant_operation", start, start);
        
        // Test with very long operation name
        std::string long_name(1000, 'x');
        perf_logger->log_operation(long_name, start, start);
        
        // Test with special characters in operation name
        perf_logger->log_operation("operation_with_特殊字符", start, start);
        
        EXPECT_NO_THROW(perf_logger->export_metrics());
    }
    
    // Test audit logger comprehensive UK compliance scenarios
    TEST_F(LoggingTest, AuditLoggerUKComplianceScenarios) {
        auto logger = Logger::get("test_audit");
        auto audit_logger = std::make_shared<AuditLogger>(logger);
        
        // Test NHS data access
                audit_logger->log_data_access("patient_records", "READ", 1, "nhs_user_123");
        
        // Test data modification
        audit_logger->log_data_modification("nhs_admin_456", "medication_records", "UPDATE", 1);
        
        // Test data export
        audit_logger->log_data_export("nhs_analyst_789", "clinical_data", "EXPORT", 1);
        
        // Test access denied
        audit_logger->log_access_denied("unauthorized_user", "sensitive_records", "READ", "Insufficient permissions");
        
        // Verify audit trail
        auto audit_trail = audit_logger->get_audit_trail();
        EXPECT_FALSE(audit_trail.empty());
        EXPECT_GE(audit_trail.size(), 4);
    }
    
    // Test JSON formatter pretty print
    TEST_F(LoggingTest, JSONFormatterPrettyPrint) {
        auto formatter = std::make_unique<JsonLogFormatter>();
        formatter->set_pretty_print(true);
        
        LogEntry entry;
        entry.timestamp = std::chrono::system_clock::now();
        entry.level = LogLevel::Info;
        entry.message = "UK healthcare data processed";
        entry.thread_id = "main";
        entry.job_id = "UK-ETL-001";
        entry.component = "data_processor";
        entry.context["records_processed"] = 1500;
        entry.context["processing_time_ms"] = 2500;
        
        std::string formatted = formatter->format(entry);
        
        // Verify JSON structure and pretty printing
        EXPECT_TRUE(formatted.find("\n") != std::string::npos); // Pretty print has newlines
        EXPECT_TRUE(formatted.find("UK healthcare data processed") != std::string::npos);
        EXPECT_TRUE(formatted.find("1500") != std::string::npos);
    }
    
    // Test text formatter custom pattern
    TEST_F(LoggingTest, TextFormatterCustomPattern) {
        auto formatter = std::make_unique<TextLogFormatter>("[%Y-%m-%d %H:%M:%S.%e] [%l] [%n] %v");
        
        LogEntry entry;
        entry.timestamp = std::chrono::system_clock::now();
        entry.level = LogLevel::Warning;
        entry.message = "NHS number format invalid";
        entry.logger_name = "uk_validator";
        entry.thread_id = "validation_thread";
        
        std::string formatted = formatter->format(entry);
        
        // Verify custom pattern
        EXPECT_TRUE(formatted.find("[WARN]") != std::string::npos);
        EXPECT_TRUE(formatted.find("[uk_validator]") != std::string::npos);
        EXPECT_TRUE(formatted.find("NHS number format invalid") != std::string::npos);
        // Note: TextLogFormatter doesn't currently support thread_id in pattern, 
        // so we don't test for validation_thread
    }
    
    // Test logging metrics
    TEST_F(LoggingTest, LoggingMetrics) {
        auto logger = std::make_shared<Logger>("metrics_test");
        auto console_sink = std::make_shared<ConsoleSink>();
        auto formatter = std::make_unique<TextLogFormatter>();
        console_sink->set_formatter(std::move(formatter));
        logger->add_sink(console_sink);
        
        // Log various levels
        logger->debug("Debug message");
        logger->info("Info message");
        logger->warn("Warning message");
        logger->error("Error message");
        logger->critical("Critical message");
        
        // Log metrics
        std::unordered_map<std::string, double> metrics;
        metrics["total_messages"] = 5.0;
        metrics["error_count"] = 2.0;
        metrics["warning_count"] = 1.0;
        logger->log_metrics(metrics);
    }
    
    // Test that audit logger tracks UK healthcare data access correctly
TEST_F(LoggingTest, AuditLoggerTracksUKHealthcareDataAccessCorrectly) {
    AuditLogger audit_logger(logger_);
    
    audit_logger.log_data_access("uk_nhs_patient_demographics", "read", 1500, "nhs_etl_user");
    
    EXPECT_EQ(mock_sink_->getEntryCount(), 1u);
    
    auto entry = mock_sink_->getLastEntry();
    EXPECT_TRUE(entry.message.find("uk_nhs_patient_demographics") != std::string::npos);
    EXPECT_TRUE(entry.message.find("read") != std::string::npos);
    EXPECT_TRUE(entry.message.find("1500") != std::string::npos);
    EXPECT_TRUE(entry.message.find("nhs_etl_user") != std::string::npos);
}

// Test that JSON formatter handles UK-specific log entries correctly
TEST_F(LoggingTest, JSONFormatterHandlesUKSpecificLogEntriesCorrectly) {
    auto json_formatter = std::make_unique<JsonLogFormatter>();
    json_formatter->set_pretty_print(false);
    mock_sink_->set_formatter(std::move(json_formatter));
    
    std::unordered_map<std::string, std::any> uk_context;
    uk_context["nhs_number"] = nhs_number_;
    uk_context["postcode"] = uk_postcode_;
            uk_context["cost"] = omop::common::UKLocalization::format_uk_currency(125.50);
    
    logger_->log_structured(LogLevel::Info, "Processing UK patient record", uk_context);
    
    std::string formatted = mock_sink_->getLastFormattedMessage();
    EXPECT_TRUE(formatted.find("\"message\":\"Processing UK patient record\"") != std::string::npos);
    EXPECT_TRUE(formatted.find(nhs_number_) != std::string::npos);
    EXPECT_TRUE(formatted.find(uk_postcode_) != std::string::npos);
}

// Test that logger handles concurrent UK healthcare operations safely
TEST_F(LoggingTest, LoggerHandlesConcurrentUKHealthcareOperationsSafely) {
    const int num_threads = 3;
    const int messages_per_thread = 5;
    std::vector<std::thread> threads;
    
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, messages_per_thread]() {
            for (int j = 0; j < messages_per_thread; ++j) {
                std::string message = "NHS ETL thread " + std::to_string(i) + 
                                    " processing record " + std::to_string(j);
                logger_->info(message);
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    EXPECT_EQ(mock_sink_->getEntryCount(), num_threads * messages_per_thread);
}

// Test that logger records UK NHS job context correctly
TEST_F(LoggingTest, LoggerRecordsUKNHSJobContextCorrectly) {
    std::string uk_job_id = "NHS-ETL-2025-01-15-001";
    std::string component = "uk_patient_transformer";
    
    logger_->set_job_id(uk_job_id);
    logger_->set_component(component);
    
    logger_->info("Starting UK patient data transformation");
    
    auto entry = mock_sink_->getLastEntry();
    EXPECT_EQ(entry.job_id, uk_job_id);
    EXPECT_EQ(entry.component, component);
    EXPECT_TRUE(entry.message.find("UK patient data") != std::string::npos);
}

// Test that logging level filtering works with UK operations
TEST_F(LoggingTest, LoggingLevelFilteringWorksWithUKOperations) {
    logger_->set_level(LogLevel::Warning);
    
    logger_->debug("Debug: Processing NHS number validation");
    logger_->info("Info: UK postcode standardization complete");
    logger_->warn("Warning: NHS data quality threshold exceeded");
    logger_->error("Error: Failed to connect to UK OMOP database");
    
    EXPECT_EQ(mock_sink_->getEntryCount(), 2u); // Only warning and error
    
    auto entries = mock_sink_->entries;
    EXPECT_TRUE(entries[0].message.find("NHS data quality") != std::string::npos);
    EXPECT_TRUE(entries[1].message.find("UK OMOP database") != std::string::npos);
}

// Test that performance logger tracks UK ETL metrics correctly
TEST_F(LoggingTest, PerformanceLoggerTracksUKETLMetricsCorrectly) {
    PerformanceLogger perf_logger(logger_);
    
    perf_logger.start_timing("uk_nhs_extraction");
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    perf_logger.end_timing("uk_nhs_extraction", 5000);
    
    EXPECT_GE(mock_sink_->getEntryCount(), 1u); // Only end message is logged
    
    // Check that timing information is logged
    bool found_timing = false;
    for (const auto& entry : mock_sink_->entries) {
        if (entry.message.find("uk_nhs_extraction") != std::string::npos &&
            entry.message.find("completed") != std::string::npos) {
            found_timing = true;
            break;
        }
    }
    EXPECT_TRUE(found_timing);
}

// Test that performance logger tracks UK resource usage correctly
TEST_F(LoggingTest, PerformanceLoggerTracksUKResourceUsageCorrectly) {
    PerformanceLogger perf_logger(logger_);
    
    // Simulate UK NHS ETL resource usage
    perf_logger.log_resource_usage(45.5, 2048.0, 150.0);
    
    auto entry = mock_sink_->getLastEntry();
    EXPECT_EQ(entry.message, "Metrics update");
    
    // Check that metrics are in the context
    EXPECT_TRUE(entry.context.find("cpu_percent") != entry.context.end());
    EXPECT_TRUE(entry.context.find("memory_mb") != entry.context.end());
    EXPECT_TRUE(entry.context.find("disk_io_mb") != entry.context.end());
    
    // Verify the specific values
    EXPECT_DOUBLE_EQ(std::any_cast<double>(entry.context.at("cpu_percent")), 45.5);
    EXPECT_DOUBLE_EQ(std::any_cast<double>(entry.context.at("memory_mb")), 2048.0);
    EXPECT_DOUBLE_EQ(std::any_cast<double>(entry.context.at("disk_io_mb")), 150.0);
}

// Test that scoped timer works with UK ETL operations
TEST_F(LoggingTest, ScopedTimerWorksWithUKETLOperations) {
    PerformanceLogger perf_logger(logger_);
    
    {
        auto timer = perf_logger.scoped_timer("uk_patient_validation");
        timer.set_record_count(1500);
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        // Timer destructor called automatically
    }
    
    EXPECT_GE(mock_sink_->getEntryCount(), 1u); // Only completion message is logged
    
    // Verify completion message and record count in context
    auto entry = mock_sink_->getLastEntry();
    EXPECT_TRUE(entry.message.find("uk_patient_validation") != std::string::npos);
    EXPECT_TRUE(entry.message.find("completed") != std::string::npos);
    
    // Check that record count is in the context
    EXPECT_TRUE(entry.context.find("record_count") != entry.context.end());
    EXPECT_EQ(std::any_cast<size_t>(entry.context.at("record_count")), 1500u);
}

// Test that structured logging captures UK healthcare metrics
TEST_F(LoggingTest, StructuredLoggingCapturesUKHealthcareMetrics) {
    std::unordered_map<std::string, std::any> metrics;
    metrics["processed_nhs_records"] = 15000;
    metrics["validation_failures"] = 25;
            metrics["processing_cost_gbp"] = omop::common::UKLocalization::format_uk_currency(45.80);
    metrics["average_temperature_celsius"] = 36.5;
    
    logger_->log_structured(LogLevel::Info, "UK NHS ETL batch completed", metrics);
    
    auto entry = mock_sink_->getLastEntry();
    EXPECT_FALSE(entry.context.empty());
    EXPECT_TRUE(entry.message.find("UK NHS ETL") != std::string::npos);
}

// Test that text formatter produces readable UK log messages
TEST_F(LoggingTest, TextFormatterProducesReadableUKLogMessages) {
    auto text_formatter = std::make_unique<TextLogFormatter>();
    mock_sink_->set_formatter(std::move(text_formatter));
    
    logger_->info("NHS patient {} processed from postcode {}", nhs_number_, uk_postcode_);
    
    std::string formatted = mock_sink_->getLastFormattedMessage();
    EXPECT_TRUE(formatted.find("[INFO]") != std::string::npos);
    EXPECT_TRUE(formatted.find(nhs_number_) != std::string::npos);
    EXPECT_TRUE(formatted.find(uk_postcode_) != std::string::npos);
    EXPECT_TRUE(formatted.find("uk_test_logger") != std::string::npos);
}

} // namespace omop::common::test