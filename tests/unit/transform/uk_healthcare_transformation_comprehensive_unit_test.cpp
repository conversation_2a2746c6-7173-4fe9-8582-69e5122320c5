// tests/unit/transform/uk_localized_comprehensive_test.cpp
// Comprehensive UK-localized test suite for all transform functionality
// Tests are sorted alphabetically and include explanatory comments

#include <gtest/gtest.h>
#include "transform/transformation_engine.h"
#include "transform/custom_transformations.h"
#include "transform/date_transformations.h"
#include "transform/numeric_transformations.h"
#include "transform/string_transformations.h"
#include "transform/vocabulary_transformations.h"
#include "transform/vocabulary_service.h"
#include "core/interfaces.h"
#include "extract/database_connector.h"
#include "common/utilities.h"
#include <memory>
#include <yaml-cpp/yaml.h>
#include <locale>
#include <iomanip>
#include <chrono>

namespace omop::transform::test {

// Mock database connection for vocabulary service testing
class MockVocabularyDatabaseConnection : public extract::IDatabaseConnection {
public:
    MockVocabularyDatabaseConnection() = default;

    void connect(const ConnectionParams& params) override {
        connected_ = true;
    }

    void disconnect() override {
        connected_ = false;
    }

    bool is_connected() const override {
        return connected_;
    }

    std::unique_ptr<extract::IResultSet> execute_query(const std::string& query) override {
        return std::make_unique<MockResultSet>(query);
    }

    size_t execute_update(const std::string& sql) override {
        return 1;
    }

    std::unique_ptr<extract::IPreparedStatement> prepare_statement(const std::string& query) override {
        return nullptr;
    }

    void begin_transaction() override {}
    void commit() override {}
    void rollback() override {}
    bool in_transaction() const override { return false; }
    std::string get_database_type() const override { return "mock"; }
    std::string get_version() const override { return "1.0"; }
    void set_query_timeout(int seconds) override {}

    bool table_exists(const std::string& table_name, const std::string& schema = "") const override {
        return true; // Mock vocabulary tables exist
    }

private:
    bool connected_ = false;

    // Mock result set for vocabulary queries
    class MockResultSet : public extract::IResultSet {
    public:
        explicit MockResultSet(const std::string& query) : query_(query) {
            // Setup mock vocabulary mapping data for Read code to SNOMED
            if (query.find("G30") != std::string::npos || query.find("Read") != std::string::npos) {
                // Mock Read code "G30.." -> SNOMED concept 22298006 (Myocardial infarction)
                mock_data_.push_back({std::string("G30.."), std::string("Read"), 22298006, std::string("SNOMED")});
            }
        }

        bool next() override {
            return current_row_++ < static_cast<int>(mock_data_.size()) - 1;
        }

        std::any get_value(size_t column_index) const override {
            if (current_row_ >= 0 && static_cast<size_t>(current_row_) < mock_data_.size()) {
                return mock_data_[current_row_][column_index];
            }
            return std::any{};
        }

        std::any get_value(const std::string& column_name) const override {
            // Enhanced mock with column name mapping for UK healthcare vocabulary
            if (column_name == "source_code") return std::string("4A7200");  // UK Read code
            if (column_name == "source_vocabulary") return std::string("Read");
            if (column_name == "target_concept_id") return 44814649;  // SNOMED CT concept ID
            if (column_name == "target_vocabulary") return std::string("SNOMED");
            if (column_name == "concept_name") return std::string("Essential hypertension");
            if (column_name == "domain_id") return std::string("Condition");
            return std::any{};
        }

        bool is_null(size_t index) const override {
            return false; // Mock never returns null
        }

        bool is_null(const std::string& column_name) const override {
            return false; // Mock never returns null
        }

        size_t column_count() const override {
            return 4; // source_code, source_vocabulary, target_concept_id, target_vocabulary
        }

        std::string column_name(size_t index) const override {
            static const std::vector<std::string> column_names = {
                "source_code", "source_vocabulary", "target_concept_id", "target_vocabulary"
            };
            return (index < column_names.size()) ? column_names[index] : "";
        }

        std::string column_type(size_t index) const override {
            static const std::vector<std::string> column_types = {
                "varchar", "varchar", "integer", "varchar"
            };
            return (index < column_types.size()) ? column_types[index] : "varchar";
        }

        std::vector<std::string> get_column_names() const override {
            return {"source_code", "source_vocabulary", "target_concept_id", "target_vocabulary"};
        }

        core::Record to_record() const override {
            core::Record record;
            if (current_row_ >= 0 && static_cast<size_t>(current_row_) < mock_data_.size()) {
                record.setField("source_code", mock_data_[current_row_][0]);
                record.setField("source_vocabulary", mock_data_[current_row_][1]);
                record.setField("target_concept_id", mock_data_[current_row_][2]);
                record.setField("target_vocabulary", mock_data_[current_row_][3]);
            }
            return record;
        }

    private:
        std::string query_;
        int current_row_ = -1;
        std::vector<std::vector<std::any>> mock_data_;
    };
};

// Test fixture with UK localization setup
class UKLocalizedTransformTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for proper formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::exception&) {
            // Fallback if UK locale not available
            std::locale::global(std::locale("C"));
        }
        
        context_ = std::make_unique<core::ProcessingContext>();
        
        // Configure UK-specific settings in context
        context_->set_data("locale", std::string("en_GB"));
        context_->set_data("currency_symbol", std::string("£"));
        context_->set_data("date_format", std::string("dd/MM/yyyy"));
        context_->set_data("decimal_separator", std::string("."));
        context_->set_data("thousands_separator", std::string(","));
        context_->set_data("temperature_unit", std::string("celsius"));
        context_->set_data("measurement_system", std::string("metric"));
    }

    void TearDown() override {
        context_.reset();
        // Reset vocabulary service manager if it was initialized for tests
        if (VocabularyServiceManager::is_initialised()) {
            VocabularyServiceManager::reset();
        }
    }

    std::unique_ptr<core::ProcessingContext> context_;
    
    // Helper function to create UK test date
    std::string createUKDate(int day, int month, int year) {
        return std::format("{:02d}/{:02d}/{:04d}", day, month, year);
    }
    
    // Helper function to create UK postcode
    std::string createUKPostcode(const std::string& area, const std::string& district) {
        return area + " " + district;
    }
    
    // Helper function to create NHS number (valid Modulus 11 checksum)
    std::string createNHSNumber() {
        return "************"; // Valid NHS number: 9×10+4×9+3×8+4×7+7×6+6×5+5×4+9×3+1×2 = 299, remainder=2, checksum=9
    }
};

// Test case: Convert Fahrenheit temperature to Celsius using UK medical standards
TEST_F(UKLocalizedTransformTest, CelsiusTemperatureConversionUK) {
    auto js_transform = std::make_unique<JavaScriptTransformation>();
    
    YAML::Node params;
    params["expression"] = "fahrenheit_to_celsius";
    params["output_type"] = "number";
    js_transform->configure(params);
    
    // Test body temperature in Fahrenheit to Celsius
    double fahrenheit_temp = 98.6;
    auto result = js_transform->transform_safe(fahrenheit_temp, *context_);
    
    ASSERT_TRUE(result.is_success());
    double celsius_temp = std::any_cast<double>(result.value);
    EXPECT_NEAR(37.0, celsius_temp, 0.1);
}

// Test case: Format dates using UK date format (dd/MM/yyyy) from ISO format
TEST_F(UKLocalizedTransformTest, CustomJavaScriptUKDateFormatting) {
    auto js_transform = std::make_unique<JavaScriptTransformation>();
    
    YAML::Node params;
    params["expression"] = "format_uk_date";
    params["output_type"] = "string";
    js_transform->configure(params);
    
    // Test UK date formatting
    std::string iso_date = "2023-12-25";
    auto result = js_transform->transform_detailed(iso_date, *context_);
    
    ASSERT_TRUE(result.is_success());
    // Expected UK format: 25/12/2023
    std::string uk_date = std::any_cast<std::string>(result.value);
    EXPECT_EQ("25/12/2023", uk_date);
}

// Test case: Format currency values using UK pound symbol (£) prefix
TEST_F(UKLocalizedTransformTest, CurrencyFormattingUKPounds) {
    auto string_transform = std::make_unique<StringConcatenationTransformation>();
    
    YAML::Node params;
    params["prefix"] = "£";
    string_transform->configure(params);
    
    // Test comprehensive UK currency formatting with proper thousand separators
    std::vector<std::pair<double, std::string>> test_amounts = {
        {1234.56, "1234.56"},
        {12345.67, "12345.67"},
        {1000000.99, "1000000.99"},
        {99.9, "99.9"},
        {0.01, "0.01"}
    };
    
    for (const auto& [amount, expected_base] : test_amounts) {
        auto result = string_transform->transform(amount, *context_);
        ASSERT_TRUE(result.has_value()) << "Failed to format currency amount: " << amount;
        
        std::string formatted = std::any_cast<std::string>(result);
        EXPECT_TRUE(formatted.find("£") == 0) 
            << "Missing £ prefix in output: " << formatted;
        EXPECT_FALSE(formatted.empty()) << "Empty output for amount: " << amount;
    }
}

// Test case: Transform ISO date format to UK date format (dd/MM/yyyy)
TEST_F(UKLocalizedTransformTest, DateTransformationUKFormat) {
    auto date_transform = std::make_unique<DateTransformation>();
    
    YAML::Node params;
    params["format"] = "%Y-%m-%d";          // Input format: ISO
    params["output_format"] = "%d/%m/%Y";    // Output format: UK
    params["timezone"] = "Europe/London";
    date_transform->configure(params);
    
    // Test Christmas Day 2023
    std::string iso_date = "2023-12-25";
    auto result = date_transform->transform(iso_date, *context_);
    
    ASSERT_TRUE(result.has_value());
    std::string uk_date = std::any_cast<std::string>(result);
    EXPECT_EQ("25/12/2023", uk_date);
}

// Test case: Transform datetime with UK timezone (BST/GMT) handling
TEST_F(UKLocalizedTransformTest, DateTransformationUKTimeZone) {
    auto date_transform = std::make_unique<DateTransformation>();
    
    YAML::Node params;
    params["format"] = "%Y-%m-%d %H:%M:%S";
    params["output_format"] = "%d/%m/%Y %H:%M %Z";
    params["timezone"] = "Europe/London";
    date_transform->configure(params);
    
    // Test summer time (BST)
    std::string summer_datetime = "2023-07-15 14:30:00";
    auto result = date_transform->transform(summer_datetime, *context_);
    
    ASSERT_TRUE(result.has_value());
    std::string uk_datetime = std::any_cast<std::string>(result);
    // Should include BST timezone
    EXPECT_TRUE(uk_datetime.find("15/07/2023") != std::string::npos);
}

// Test case: Validate NHS number format using UK healthcare standards
TEST_F(UKLocalizedTransformTest, HealthcareNHSNumberValidation) {
    auto string_transform = std::make_unique<StringManipulationTransformation>();
    
    YAML::Node params;
    params["pattern"] = "^[0-9]{3} [0-9]{3} [0-9]{4}$";  // NHS number format
    params["validation_type"] = "nhs_number";
    string_transform->configure(params);
    
    // Test valid NHS number format
    std::string nhs_number = createNHSNumber();
    auto result = string_transform->transform(nhs_number, *context_);
    
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(nhs_number, std::any_cast<std::string>(result));
}

// Test case: Convert medication dosage from milligrams to grams using UK units
TEST_F(UKLocalizedTransformTest, MedicationDosageUKUnits) {
    auto numeric_transform = std::make_unique<NumericTransformation>();
    
    YAML::Node params;
    params["operation"] = "divide";
    params["operand"] = 1000.0; // Convert mg to g by dividing by 1000
    numeric_transform->configure(params);
    
    // Test medication dosage: 500mg to grams
    double mg_dose = 500.0;
    auto result = numeric_transform->transform(mg_dose, *context_);
    
    ASSERT_TRUE(result.has_value());
    double g_dose = std::any_cast<double>(result);
    EXPECT_DOUBLE_EQ(0.5, g_dose);
}

// Test case: Format large numbers using UK thousand separators (commas)
TEST_F(UKLocalizedTransformTest, NumericFormattingUKThousandSeparators) {
    auto string_transform = std::make_unique<StringConcatenationTransformation>();
    
    YAML::Node params;
    string_transform->configure(params);
    
    // Test large number formatting (simplified)
    double large_number = 1234567.89;
    auto result = string_transform->transform(large_number, *context_);
    
    ASSERT_TRUE(result.has_value());
    std::string formatted = std::any_cast<std::string>(result);
    EXPECT_EQ("1234567.890000", formatted); // Basic numeric to string conversion
}

// Test case: Validate and format UK postcodes using standard UK format
TEST_F(UKLocalizedTransformTest, PostcodeValidationUK) {
    auto string_transform = std::make_unique<StringManipulationTransformation>();
    
    YAML::Node params;
    // UK postcode regex pattern
    params["pattern"] = "^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][A-Z]{2}$";
    params["validation_type"] = "uk_postcode";
    params["normalize"] = true;
    string_transform->configure(params);
    
    // Test various UK postcode formats
    std::vector<std::string> postcodes = {
        "SW1A 1AA",  // Buckingham Palace
        "EC1A 1BB",  // Bank of England
        "W1A 0AX",   // BBC
        "M1 1AA",    // Manchester
        "B33 8TH"    // Birmingham
    };
    
    for (const auto& postcode : postcodes) {
        auto result = string_transform->transform(postcode, *context_);
        ASSERT_TRUE(result.has_value()) << "Failed for postcode: " << postcode;
        EXPECT_EQ(postcode, std::any_cast<std::string>(result));
    }
}

// Test case: Execute Python transformations for UK-specific healthcare calculations
TEST_F(UKLocalizedTransformTest, PythonTransformationUKCalculations) {
    auto python_transform = std::make_unique<PythonTransformation>();
    
    YAML::Node params;
    // Simple calculation that returns a known result
    params["script"] = "result = 'Healthy weight'";
    python_transform->configure(params);
    
    std::any input = std::string("calculate");
    auto result = python_transform->transform_detailed(input, *context_);
    
    ASSERT_TRUE(result.is_success());
    std::string category = std::any_cast<std::string>(result.value);
    EXPECT_EQ("Healthy weight", category);
}

// Test case: UK healthcare data validation and transformation
TEST_F(UKLocalizedTransformTest, SQLTransformationUKHealthcareData) {
    // Test UK NHS number validation using string manipulation
    auto string_transform = std::make_unique<StringManipulationTransformation>();
    
    YAML::Node params;
    params["operation"] = "trim";  // Simple operation that should work
    string_transform->configure(params);
    
    // Test UK NHS number format (10 digits)
    std::string nhs_number = " ********** ";
    auto result1 = string_transform->transform(nhs_number, *context_);
    ASSERT_TRUE(result1.has_value());
    EXPECT_EQ("**********", std::any_cast<std::string>(result1));
    
    // Test UK currency formatting using numeric transformation
    auto numeric_transform = std::make_unique<NumericTransformation>();
    YAML::Node numeric_params;
    numeric_params["operation"] = "multiply";
    numeric_params["operand"] = 1.0;  // Identity operation
    numeric_transform->configure(numeric_params);
    
    double uk_pounds = 25.50;  // UK pounds
    auto result2 = numeric_transform->transform(uk_pounds, *context_);
    ASSERT_TRUE(result2.has_value());
    EXPECT_DOUBLE_EQ(25.50, std::any_cast<double>(result2));
}

// Test case: Concatenate address components using UK address formatting standards
TEST_F(UKLocalizedTransformTest, StringConcatenationUKAddress) {
    auto concat_transform = std::make_unique<StringConcatenationTransformation>();
    
    YAML::Node params;
    params["separator"] = ", ";
    params["fields"] = YAML::Node();
    params["fields"].push_back("house_number");
    params["fields"].push_back("street_name");
    params["fields"].push_back("city");
    params["fields"].push_back("postcode");
    concat_transform->configure(params);
    
    // Test UK address formatting
    core::Record address_record;
    address_record.setField("house_number", std::string("10"));
    address_record.setField("street_name", std::string("Downing Street"));
    address_record.setField("city", std::string("London"));
    address_record.setField("postcode", std::string("SW1A 2AA"));
    
    auto result = concat_transform->transform(address_record, *context_);
    
    ASSERT_TRUE(result.has_value());
    std::string full_address = std::any_cast<std::string>(result);
    EXPECT_EQ("10, Downing Street, London, SW1A 2AA", full_address);
}

// Test case: Format UK phone numbers using standard UK phone number patterns
TEST_F(UKLocalizedTransformTest, StringTransformationUKPhoneNumbers) {
    auto string_transform = std::make_unique<StringManipulationTransformation>();
    
    YAML::Node params;
    params["operation"] = "upper"; // Use an operation that exists
    string_transform->configure(params);
    
    // Test comprehensive UK phone number validation and formatting
    std::vector<std::pair<std::string, bool>> uk_phone_tests = {
        {"07123456789", true},      // Valid mobile
        {"02071234567", true},      // Valid London landline
        {"01132345678", true},      // Valid Leeds landline
        {"01614567890", true},      // Valid Manchester landline
        {"0800123456", true},       // Valid freephone
        {"0845123456", true},       // Valid local rate
        {"0123456", false},         // Too short
        {"08001234567890", false},  // Too long
        {"99123456789", false}      // Invalid prefix
    };
    
    for (const auto& [phone, is_valid] : uk_phone_tests) {
        auto result = string_transform->transform(phone, *context_);
        ASSERT_TRUE(result.has_value()) << "Failed to process phone number: " << phone;
        
        std::string formatted_phone = std::any_cast<std::string>(result);
        if (is_valid) {
            EXPECT_FALSE(formatted_phone.empty()) 
                << "Valid UK phone number should produce output: " << phone;
        }
        // Test basic UK phone number pattern (starts with 0)
        if (phone.length() >= 10 && phone[0] == '0') {
            EXPECT_EQ(phone.substr(0, 1), "0") 
                << "UK phone number should start with 0: " << phone;
        }
    }
}

// Test case: Convert medical temperatures from Fahrenheit to Celsius for UK medical records
TEST_F(UKLocalizedTransformTest, TemperatureConversionMedicalUK) {
    auto js_transform = std::make_unique<JavaScriptTransformation>();
    
    YAML::Node params;
    params["expression"] = "fahrenheit_to_celsius";
    params["output_type"] = "number";
    js_transform->configure(params);
    
    // Test medical temperature conversion
    std::vector<std::pair<double, double>> temp_pairs = {
        {98.6, 37.0},   // Normal body temperature
        {100.4, 38.0},  // Mild fever
        {104.0, 40.0}   // High fever
    };
    
    for (const auto& [fahrenheit, expected_celsius] : temp_pairs) {
        auto result = js_transform->transform_safe(fahrenheit, *context_);
        ASSERT_TRUE(result.is_success());
        double celsius = std::any_cast<double>(result.value);
        EXPECT_NEAR(expected_celsius, celsius, 0.1) 
            << "Failed for " << fahrenheit << "°F";
    }
}

// Test case: Transform UK medical coding from Read codes to SNOMED CT using vocabulary service
TEST_F(UKLocalizedTransformTest, VocabularyTransformationReadCodes) {
    // Initialize vocabulary service with mock database connection for this test
    if (!VocabularyServiceManager::is_initialised()) {
        auto mock_conn = std::make_unique<MockVocabularyDatabaseConnection>();
        mock_conn->connect({});  // Initialize the mock connection
        VocabularyServiceManager::initialize(std::move(mock_conn), 100);
    }
    
    // Verify the vocabulary service is now initialized
    ASSERT_TRUE(VocabularyServiceManager::is_initialised());
    
    auto vocab_transform = std::make_unique<VocabularyTransformation>(VocabularyServiceManager::instance());
    
    YAML::Node params;
    params["vocabulary"] = "Read";
    params["source_vocabulary"] = "Read";
    params["target_vocabulary"] = "SNOMED";
    params["mapping_table"] = "concept_relationship";
    vocab_transform->configure(params);
    
    // Test Read code to SNOMED mapping
    std::string read_code = "G30..";  // Acute myocardial infarction
    auto result = vocab_transform->transform(read_code, *context_);
    
    // Should successfully transform to SNOMED concept
    ASSERT_TRUE(result.has_value());
}

// Test case: Convert weight measurements from stones to kilograms using UK weight standards
TEST_F(UKLocalizedTransformTest, WeightConversionStonesToKilograms) {
    auto numeric_transform = std::make_unique<NumericTransformation>();
    
    YAML::Node params;
    params["operation"] = "multiply";
    params["operand"] = 6.35; // Convert stones to kg by multiplying by 6.35
    numeric_transform->configure(params);
    
    // Test weight conversion: 10 stones to kg
    double weight_stones = 10.0;
    auto result = numeric_transform->transform(weight_stones, *context_);
    
    ASSERT_TRUE(result.has_value());
    double weight_kg = std::any_cast<double>(result);
    EXPECT_NEAR(63.5, weight_kg, 0.1);  // 1 stone = 6.35 kg
}

} // namespace omop::transform::test