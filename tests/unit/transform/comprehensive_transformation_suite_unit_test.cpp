// tests/unit/transform/all_transformations_comprehensive_test.cpp
// Comprehensive test coverage for all transformation types
// Tests are sorted alphabetically and include explanatory comments

#include <gtest/gtest.h>
#include "transform/transformation_engine.h"
#include "transform/custom_transformations.h"
#include "transform/date_transformations.h"
#include "transform/numeric_transformations.h"
#include "transform/string_transformations.h"
#include "transform/vocabulary_transformations.h"
#include "transform/vocabulary_service.h"
#include "transform/conditional_transformations.h"
#include "transform/field_transformations.h"
#include "transform/validation_engine.h"
#include "core/interfaces.h"
#include <memory>
#include <yaml-cpp/yaml.h>
#include <thread>

namespace omop::transform::test {

// Helper function to initialize vocabulary service with mock connection
static void initialize_vocabulary_service_if_needed() {
    if (!VocabularyServiceManager::is_initialised()) {
        // Mock connection that returns no results
        class EmptyMockConnection : public extract::IDatabaseConnection {
        public:
            void connect(const ConnectionParams&) override {}
            void disconnect() override {}
            bool is_connected() const override { return true; }
            std::unique_ptr<extract::IResultSet> execute_query(const std::string&) override {
                class EmptyResult : public extract::IResultSet {
                public:
                    bool next() override { return false; }
                    std::any get_value(size_t) const override { return std::any{}; }
                    std::any get_value(const std::string&) const override { return std::any{}; }
                    bool is_null(size_t) const override { return true; }
                    bool is_null(const std::string&) const override { return true; }
                    size_t column_count() const override { return 0; }
                    std::string column_name(size_t) const override { return ""; }
                    std::string column_type(size_t) const override { return ""; }
                    std::vector<std::string> get_column_names() const override { return {}; }
                    core::Record to_record() const override { return core::Record{}; }
                };
                return std::make_unique<EmptyResult>();
            }
            size_t execute_update(const std::string&) override { return 0; }
            std::unique_ptr<extract::IPreparedStatement> prepare_statement(const std::string&) override {
                return nullptr;
            }
            void begin_transaction() override {}
            void commit() override {}
            void rollback() override {}
            bool in_transaction() const override { return false; }
            std::string get_database_type() const override { return "empty_mock"; }
            std::string get_version() const override { return "1.0"; }
            void set_query_timeout(int) override {}
            bool table_exists(const std::string& table_name, const std::string& = "") const override {
                // Return true for all OMOP CDM vocabulary tables that might be needed for initialization
                return (table_name == "concept" || table_name == "concept_relationship" || 
                        table_name == "concept_ancestor" || table_name == "vocabulary" || 
                        table_name == "domain" || table_name == "concept_class" || 
                        table_name == "relationship" || table_name == "concept_synonym" ||
                        table_name == "drug_strength" || table_name == "source_to_concept_map");
            }
        };
        
        VocabularyServiceManager::initialize(std::make_unique<EmptyMockConnection>());
    }
}

class AllTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        context_->set_data("locale", std::string("en_GB"));
        registry_ = &TransformationRegistry::instance();
        
        // Initialize vocabulary service for tests that need it
        initialize_vocabulary_service_if_needed();
    }

    void TearDown() override {
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
    TransformationRegistry* registry_;
};

// Test case: Advanced conditional transformation with complex logic
TEST_F(AllTransformationsTest, AdvancedConditionalTransformationComplexLogic) {
    auto conditional = std::make_unique<AdvancedConditionalTransformation>();
    
    YAML::Node params;
    params["conditions"] = YAML::Node();
    
    // First condition: age-based categorization
    YAML::Node condition1;
    condition1["expression"] = "age >= 65";
    condition1["value"] = "Senior";
    params["conditions"].push_back(condition1);
    
    // Second condition: middle-aged
    YAML::Node condition2;
    condition2["expression"] = "age >= 18";
    condition2["value"] = "Adult";
    params["conditions"].push_back(condition2);
    
    // Default condition
    params["default_value"] = "Child";
    
    conditional->configure(params);
    
    // Test different age values
    std::vector<std::pair<int, std::string>> test_cases = {
        {70, "Senior"},
        {35, "Adult"},
        {10, "Child"}
    };
    
    for (const auto& [age, expected] : test_cases) {
        core::Record record;
        record.setField("age", age);
        
        auto result = conditional->transform(record, *context_);
        ASSERT_TRUE(result.has_value());
        EXPECT_EQ(expected, std::any_cast<std::string>(result));
    }
}

// Test case: Batch processing with mixed transformation types
TEST_F(AllTransformationsTest, BatchProcessingMixedTransformationTypes) {
    auto engine = std::make_unique<TransformationEngine>();
    
    // Create test records with various data types
    core::RecordBatch batch;
    
    for (int i = 1; i <= 5; ++i) {
        core::Record record;
        record.setField("id", i);
        record.setField("name", std::format("Patient{}", i));
        record.setField("birth_date", std::format("198{}-01-15", i));
        record.setField("weight", 70.0 + i);
        batch.addRecord(record);
    }
    
    // Test batch processing (would need proper configuration in real scenario)
    EXPECT_NO_THROW({
        try {
            engine->transform_batch(batch, *context_);
        } catch (const common::ConfigurationException&) {
            // Expected without proper table configuration
        }
    });
}

// Test case: Composite transformation with error recovery
TEST_F(AllTransformationsTest, CompositeTransformationErrorRecovery) {
    auto composite = std::make_unique<CompositeTransformation>();
    
    YAML::Node params;
    params["transformations"] = YAML::Node();
    params["error_handling"] = "continue";  // Continue on errors
    
    // Add multiple transformations, some may fail
    YAML::Node transform1;
    transform1["type"] = "string";
    transform1["operation"] = "lowercase";
    params["transformations"].push_back(transform1);
    
    YAML::Node transform2;
    transform2["type"] = "numeric";
    transform2["operation"] = "add";
    transform2["value"] = 10;
    params["transformations"].push_back(transform2);
    
    composite->configure(params);
    
    // Test with string input (numeric transform should fail but continue)
    std::string input = "TEST STRING";
    auto result = composite->transform_detailed(input, *context_);
    
    // Should complete with warnings
    EXPECT_TRUE(result.is_success() || !result.warnings.empty());
}

// Test case: Custom JavaScript transformation with complex expressions
TEST_F(AllTransformationsTest, CustomJavaScriptTransformationComplexExpressions) {
    auto js_transform = std::make_unique<JavaScriptTransformation>();
    
    YAML::Node params;
    params["expression"] = R"(
        const words = value.split(' ');
        return words.map(word => 
            word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        ).join(' ');
    )";
    params["output_type"] = "string";
    js_transform->configure(params);
    
    std::string input = "hello WORLD test";
    auto result = js_transform->transform_detailed(input, *context_);
    
    ASSERT_TRUE(result.is_success());
    std::string output = std::any_cast<std::string>(result.value);
    EXPECT_EQ("Hello World Test", output);
}

// Test case: Date transformation with timezone handling
TEST_F(AllTransformationsTest, DateTransformationTimezoneHandling) {
    auto date_transform = std::make_unique<DateTransformation>();
    
    YAML::Node params;
    params["format"] = "%Y-%m-%d %H:%M:%S";
    params["output_format"] = "%d/%m/%Y %H:%M";
    params["timezone"] = "Europe/London";
    params["add_time"] = true;
    params["default_time"] = "12:00:00";
    date_transform->configure(params);
    
    // Test date with time
    std::string input_datetime = "2023-06-15 14:30:00";
    auto result = date_transform->transform(input_datetime, *context_);
    
    ASSERT_TRUE(result.has_value());
    std::string output = std::any_cast<std::string>(result);
    EXPECT_TRUE(output.find("15/06/2023") != std::string::npos);
}

// Test case: Field mapping transformation with complex mappings
TEST_F(AllTransformationsTest, FieldMappingTransformationComplexMappings) {
    auto field_transform = std::make_unique<DomainMappingTransformation>();
    
    YAML::Node params;
    params["mappings"] = YAML::Node();
    
    // Direct mapping
    YAML::Node mapping1;
    mapping1["source_field"] = "patient_id";
    mapping1["target_field"] = "person_id";
    mapping1["type"] = "direct";
    params["mappings"].push_back(mapping1);
    
    // Computed mapping
    YAML::Node mapping2;
    mapping2["source_field"] = "first_name";
    mapping2["target_field"] = "full_name";
    mapping2["type"] = "computed";
    mapping2["expression"] = "first_name + ' ' + last_name";
    params["mappings"].push_back(mapping2);
    
    field_transform->configure(params);
    
    core::Record input_record;
    input_record.setField("patient_id", 12345);
    input_record.setField("first_name", std::string("John"));
    input_record.setField("last_name", std::string("Doe"));
    
    auto result = field_transform->transform(input_record, *context_);
    
    ASSERT_TRUE(result.has_value());
    auto output_record = std::any_cast<core::Record>(result);
    
    auto person_id = output_record.getField("person_id");
    ASSERT_TRUE(person_id.has_value());
    EXPECT_EQ(12345, std::any_cast<int>(person_id));
    
    auto full_name = output_record.getField("full_name");
    ASSERT_TRUE(full_name.has_value());
    EXPECT_EQ("John Doe", std::any_cast<std::string>(full_name));
}

// Test case: Numeric transformation with precision handling
TEST_F(AllTransformationsTest, NumericTransformationPrecisionHandling) {
    auto numeric_transform = std::make_unique<NumericTransformation>();
    
    YAML::Node params;
    params["operation"] = "divide";
    params["divisor"] = 3.0;
    params["precision"] = 2;
    params["rounding"] = "round";
    numeric_transform->configure(params);
    
    // Test division with precision
    double input = 10.0;
    auto result = numeric_transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    double output = std::any_cast<double>(result);
    EXPECT_NEAR(3.33, output, 0.01);
}

// Test case: Plugin transformation security validation
TEST_F(AllTransformationsTest, PluginTransformationSecurityValidation) {
    auto plugin_transform = std::make_unique<PluginTransformation>();
    
    YAML::Node params;
    params["plugin_path"] = "../tests/test_plugins/safe_plugin.so";
    params["function_name"] = "transform_data";
    params["security_validation"] = true;
    
    // Should validate plugin path security
    EXPECT_NO_THROW({
        try {
            plugin_transform->configure(params);
        } catch (const std::runtime_error&) {
            // Expected if plugin doesn't exist or fails security validation
        }
    });
}

// Test case: Python transformation with variable binding
TEST_F(AllTransformationsTest, PythonTransformationVariableBinding) {
    auto python_transform = std::make_unique<PythonTransformation>();
    
    YAML::Node params;
    params["script"] = R"(
def calculate_bmi(weight, height):
    return round(weight / (height ** 2), 1)

result = calculate_bmi(weight_kg, height_m)
)";
    params["variables"] = YAML::Node();
    params["variables"]["weight_kg"] = 75.0;
    params["variables"]["height_m"] = 1.80;
    python_transform->configure(params);
    
    std::any input = std::string("calculate");
    auto result = python_transform->transform_detailed(input, *context_);
    
    ASSERT_TRUE(result.is_success());
    double bmi = std::any_cast<double>(result.value);
    EXPECT_NEAR(23.1, bmi, 0.1);
}

// Test case: SQL transformation with conditional logic
TEST_F(AllTransformationsTest, SQLTransformationConditionalLogic) {
    auto sql_transform = std::make_unique<SQLTransformation>();
    
    YAML::Node params;
    params["expression"] = R"(
        CASE 
            WHEN gender = 'M' THEN 8507
            WHEN gender = 'F' THEN 8532
            ELSE 0
        END
    )";
    params["variables"] = YAML::Node();
    sql_transform->configure(params);
    
    // Test gender mapping
    std::vector<std::pair<std::string, int>> test_cases = {
        {"M", 8507},
        {"F", 8532},
        {"U", 0}
    };
    
    for (const auto& [gender, expected_concept] : test_cases) {
        params["variables"]["gender"] = gender;
        sql_transform->configure(params);
        
        auto result = sql_transform->transform_detailed(gender, *context_);
        ASSERT_TRUE(result.is_success());
        int concept_id = std::any_cast<int>(result.value);
        EXPECT_EQ(expected_concept, concept_id);
    }
}

// Test case: String concatenation with custom separators
TEST_F(AllTransformationsTest, StringConcatenationCustomSeparators) {
    auto concat_transform = std::make_unique<StringConcatenationTransformation>();
    
    YAML::Node params;
    params["separator"] = " | ";
    params["trim_whitespace"] = true;
    params["skip_empty"] = true;
    concat_transform->configure(params);
    
    // Test concatenation with mixed content
    core::Record input_record;
    input_record.setField("field1", std::string("Value1"));
    input_record.setField("field2", std::string(""));  // Empty field
    input_record.setField("field3", std::string("  Value3  "));  // With whitespace
    input_record.setField("field4", std::string("Value4"));
    
    auto result = concat_transform->transform(input_record, *context_);
    
    ASSERT_TRUE(result.has_value());
    std::string output = std::any_cast<std::string>(result);
    EXPECT_EQ("Value1 | Value3 | Value4", output);
}

// Test case: String transformation with pattern matching
TEST_F(AllTransformationsTest, StringTransformationPatternMatching) {
    auto string_transform = std::make_unique<StringManipulationTransformation>();
    
    YAML::Node params;
    params["operation"] = "regex_replace";
    params["pattern"] = R"(\b(\w+)\s+(\w+)\b)";
    params["replacement"] = "$2, $1";  // Swap first and last names
    string_transform->configure(params);
    
    std::string input = "John Smith";
    auto result = string_transform->transform(input, *context_);
    
    ASSERT_TRUE(result.has_value());
    std::string output = std::any_cast<std::string>(result);
    EXPECT_EQ("Smith, John", output);
}

// Test case: Transformation registry functionality
TEST_F(AllTransformationsTest, TransformationRegistryFunctionality) {
    // Test registration of custom transformation
    registry_->register_transformation("custom_test", 
        []() { return std::make_unique<DirectTransformation>(); });
    
    // Test creation of registered transformation
    auto transform = registry_->create_transformation("custom_test");
    ASSERT_NE(nullptr, transform);
    EXPECT_EQ("direct", transform->get_type());
    
    // Test listing of available transformations
    auto available = registry_->get_registered_types();
    EXPECT_TRUE(std::find(available.begin(), available.end(), "custom_test") 
                != available.end());
}

// Test case: Validation engine comprehensive field validation
TEST_F(AllTransformationsTest, ValidationEngineComprehensiveFieldValidation) {
    auto validator = std::make_unique<RequiredFieldValidator>();
    
    YAML::Node config;
    config["field_name"] = "email";
    config["required"] = true;
    config["type"] = "string";
    config["pattern"] = R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})";
    config["min_length"] = 5;
    config["max_length"] = 100;
    
    validator->configure(config);
    
    // Test valid email
    std::string valid_email = "<EMAIL>";
    auto result1 = validator->validate(valid_email, *context_);
    EXPECT_TRUE(result1.is_valid());
    
    // Test invalid email
    std::string invalid_email = "invalid-email";
    auto result2 = validator->validate(invalid_email, *context_);
    EXPECT_FALSE(result2.is_valid());
    
    // Test empty email (required field)
    std::string empty_email = "";
    auto result3 = validator->validate(empty_email, *context_);
    EXPECT_FALSE(result3.is_valid());
}

// Test case: Vocabulary transformation with hierarchy navigation
TEST_F(AllTransformationsTest, VocabularyTransformationHierarchyNavigation) {
    auto vocab_transform = std::make_unique<ConceptHierarchyTransformation>();
    
    YAML::Node params;
    params["direction"] = "ancestor";
    params["relationship_type"] = "Is a";
    params["max_levels"] = 3;
    vocab_transform->configure(params);
    
    // Test concept hierarchy navigation
    int child_concept = 123456;
    auto result = vocab_transform->transform(child_concept, *context_);
    
    // Should return ancestor concept or original if no hierarchy
    ASSERT_TRUE(result.has_value());
    int ancestor_concept = std::any_cast<int>(result);
    EXPECT_GE(ancestor_concept, 0);
}

// Test case: Vocabulary transformation with multiple mappings
TEST_F(AllTransformationsTest, VocabularyTransformationMultipleMappings) {
    auto vocab_transform = std::make_unique<VocabularyTransformation>(VocabularyServiceManager::instance());
    
    YAML::Node params;
    params["vocabulary"] = "ICD10";
    params["target_vocabulary"] = "SNOMED";
    params["default_value"] = 0;
    
    vocab_transform->configure(params);
    
    std::string icd10_code = "I25.10";
    auto result = vocab_transform->transform(icd10_code, *context_);
    
    // Should map to highest priority vocabulary
    ASSERT_TRUE(result.has_value());
}

// Test case: Zero-value handling across all transformation types
TEST_F(AllTransformationsTest, ZeroValueHandlingAllTransformationTypes) {
    // Test numeric transformation with zero
    auto numeric_transform = std::make_unique<NumericTransformation>();
    YAML::Node numeric_params;
    numeric_params["operation"] = "multiply";
    numeric_params["factor"] = 5.0;
    numeric_transform->configure(numeric_params);
    
    double zero_value = 0.0;
    auto numeric_result = numeric_transform->transform(zero_value, *context_);
    ASSERT_TRUE(numeric_result.has_value());
    EXPECT_EQ(0.0, std::any_cast<double>(numeric_result));
    
    // Test string transformation with empty string
    auto string_transform = std::make_unique<StringManipulationTransformation>();
    YAML::Node string_params;
    string_params["operation"] = "uppercase";
    string_transform->configure(string_params);
    
    std::string empty_string = "";
    auto string_result = string_transform->transform(empty_string, *context_);
    ASSERT_TRUE(string_result.has_value());
    EXPECT_EQ("", std::any_cast<std::string>(string_result));
}

} // namespace omop::transform::test