// tests/unit/transform/transformation_edge_cases_test.cpp

#include <gtest/gtest.h>
#include "transform/transformation_engine.h"
#include "transform/transformations.h"
#include "transform/vocabulary_service.h"
#include "core/interfaces.h"
#include "test_helpers.h"
#include <limits>
#include <memory>
#include "transform/field_transformations.h"

namespace omop::transform::test {

class TransformationEdgeCasesTest : public ::testing::Test {
protected:
    void SetUp() override {
        TransformationRegistrar::RegisterAll();
        context_ = std::make_unique<core::ProcessingContext>();
    }

    void TearDown() override {
        // Reset singleton if it was initialized
        if (omop::transform::VocabularyServiceManager::is_initialised()) {
            omop::transform::VocabularyServiceManager::reset();
        }
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
};

// Test numeric transformation edge cases
TEST_F(TransformationEdgeCasesTest, NumericTransformationEdgeCases) {
    auto transform = TransformationRegistry::instance().create_transformation("numeric_transform");
    
    YAML::Node params;
    params["operation"] = "multiply";
    params["operand"] = 2.0;
    transform->configure(params);
    
    // Test with maximum double value
    double max_double = std::numeric_limits<double>::max();
    EXPECT_THROW(
        transform->transform(max_double, *context_),
        common::TransformationException
    );
    
    // Test with infinity
    double infinity = std::numeric_limits<double>::infinity();
    auto result = transform->transform_safe(infinity, *context_);
    EXPECT_FALSE(result.is_success());
    
    // Test with NaN
    double nan = std::numeric_limits<double>::quiet_NaN();
    result = transform->transform_safe(nan, *context_);
    EXPECT_FALSE(result.is_success());
    
    // Test with very small numbers
    double epsilon = std::numeric_limits<double>::epsilon();
    result = transform->transform_safe(epsilon, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_DOUBLE_EQ(epsilon * 2.0, std::any_cast<double>(result.value));
}

// Test string transformation edge cases
TEST_F(TransformationEdgeCasesTest, StringTransformationEdgeCases) {
    auto transform = TransformationRegistry::instance().create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "uppercase";
    transform->configure(params);
    
    // Test with empty string
    std::string empty = "";
    auto result = transform->transform_safe(empty, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ("", std::any_cast<std::string>(result.value));
    
    // Test with very long string
    std::string long_string(1000000, 'a');
    result = transform->transform_safe(long_string, *context_);
    EXPECT_TRUE(result.is_success());
    
    // Test with special characters
    std::string special = "Hello\nWorld\tTest\r\n";
    result = transform->transform_safe(special, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ("HELLO\nWORLD\tTEST\r\n", std::any_cast<std::string>(result.value));
    
    // Test with Unicode characters
    std::string unicode = "Hello 世界 🌍";
    result = transform->transform_safe(unicode, *context_);
    EXPECT_TRUE(result.is_success());
    
    // Test with null characters
    std::string with_null = "Hello\0World";
    result = transform->transform_safe(with_null, *context_);
    EXPECT_TRUE(result.is_success());
}

// Test date transformation edge cases
TEST_F(TransformationEdgeCasesTest, DateTransformationEdgeCases) {
    auto transform = TransformationRegistry::instance().create_transformation("date_transform");
    
    YAML::Node params;
    params["format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d";
    transform->configure(params);
    
    // Test with leap year dates
    std::string leap_date = "2020-02-29";
    auto result = transform->transform_safe(leap_date, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ("2020-02-29", std::any_cast<std::string>(result.value));
    
    // Test with non-leap year February 29
    std::string invalid_leap = "2021-02-29";
    result = transform->transform_safe(invalid_leap, *context_);
    EXPECT_FALSE(result.is_success());
    
    // Test with boundary dates
    std::string min_date = "1900-01-01";
    result = transform->transform_safe(min_date, *context_);
    EXPECT_TRUE(result.is_success());
    
    std::string max_date = "2099-12-31";
    result = transform->transform_safe(max_date, *context_);
    EXPECT_TRUE(result.is_success());
    
    // Test with invalid month
    std::string invalid_month = "2020-13-01";
    result = transform->transform_safe(invalid_month, *context_);
    EXPECT_FALSE(result.is_success());
    
    // Test with invalid day
    std::string invalid_day = "2020-06-31";
    result = transform->transform_safe(invalid_day, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test conditional transformation edge cases
TEST_F(TransformationEdgeCasesTest, ConditionalTransformationEdgeCases) {
    auto transform = TransformationRegistry::instance().create_transformation("conditional");
    
    YAML::Node params;
    
    // Add condition for null/empty check
    YAML::Node cond1;
    cond1["operator"] = "is_null";
    cond1["then"] = "NULL_VALUE";
    params["conditions"].push_back(cond1);
    
    // Add condition for empty string
    YAML::Node cond2;
    cond2["operator"] = "equals";
    cond2["value"] = "";
    cond2["then"] = "EMPTY_STRING";
    params["conditions"].push_back(cond2);
    
    params["default"] = "DEFAULT";
    transform->configure(params);
    
    // Test with null value
    std::any null_value;
    auto result = transform->transform_safe(null_value, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ("NULL_VALUE", std::any_cast<std::string>(result.value));
    
    // Test with empty string
    std::string empty = "";
    result = transform->transform_safe(empty, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ("EMPTY_STRING", std::any_cast<std::string>(result.value));
    
    // Test with whitespace only
    std::string whitespace = "   ";
    result = transform->transform_safe(whitespace, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ("DEFAULT", std::any_cast<std::string>(result.value));
}

// Test transformation with mixed type inputs
TEST_F(TransformationEdgeCasesTest, MixedTypeInputs) {
    auto transform = TransformationRegistry::instance().create_transformation("string_manipulation");
    
    YAML::Node params;
    params["operation"] = "uppercase";
    transform->configure(params);
    
    // Test with integer input (should convert to string)
    int int_input = 12345;
    auto result = transform->transform_safe(int_input, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ("12345", std::any_cast<std::string>(result.value));
    
    // Test with double input
    double double_input = 123.45;
    result = transform->transform_safe(double_input, *context_);
    EXPECT_TRUE(result.is_success());
    // Result might be "123.45" or "123.450000" depending on conversion
    std::string double_result = std::any_cast<std::string>(result.value);
    EXPECT_TRUE(double_result.find("123.45") != std::string::npos);
    
    // Test with boolean input
    bool bool_input = true;
    result = transform->transform_safe(bool_input, *context_);
    EXPECT_TRUE(result.is_success());
}

// Test pattern extraction edge cases
TEST_F(TransformationEdgeCasesTest, PatternExtractionEdgeCases) {
    auto transform = TransformationRegistry::instance().create_transformation("string_pattern_extraction");
    
    YAML::Node params;
    params["pattern_type"] = "custom";
    params["pattern"] = R"(\d+)";
    params["extract_all"] = true;
    params["separator"] = ",";
    transform->configure(params);
    
    // Test with no matches
    std::string no_match = "no numbers here";
    auto result = transform->transform_safe(no_match, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ("", std::any_cast<std::string>(result.value));
    
    // Test with many matches
    std::string many_matches = "1 22 333 4444 55555";
    result = transform->transform_safe(many_matches, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ("1,22,333,4444,55555", std::any_cast<std::string>(result.value));
    
    // Test with overlapping patterns
    params["pattern"] = R"(\d{2,4})";
    transform->configure(params);
    
    std::string overlapping = "12345";
    result = transform->transform_safe(overlapping, *context_);
    EXPECT_TRUE(result.is_success());
    // Should match "1234" (greedy matching)
    std::string extracted = std::any_cast<std::string>(result.value);
    EXPECT_TRUE(extracted.find("1234") != std::string::npos);
}

// Test vocabulary transformation edge cases
TEST_F(TransformationEdgeCasesTest, VocabularyTransformationEdgeCases) {
    // Initialize vocabulary service with mock connection
    if (!VocabularyServiceManager::is_initialised()) {
        // Mock connection that returns no results
        class EmptyMockConnection : public extract::IDatabaseConnection {
        public:
            void connect(const ConnectionParams&) override {}
            void disconnect() override {}
            bool is_connected() const override { return true; }
            std::unique_ptr<extract::IResultSet> execute_query(const std::string&) override {
                class EmptyResult : public extract::IResultSet {
                public:
                    bool next() override { return false; }
                    std::any get_value(size_t) const override { return std::any{}; }
                    std::any get_value(const std::string&) const override { return std::any{}; }
                    bool is_null(size_t) const override { return true; }
                    bool is_null(const std::string&) const override { return true; }
                    size_t column_count() const override { return 0; }
                    std::string column_name(size_t) const override { return ""; }
                    std::string column_type(size_t) const override { return ""; }
                    std::vector<std::string> get_column_names() const override { return {}; }
                    core::Record to_record() const override { return core::Record{}; }
                };
                return std::make_unique<EmptyResult>();
            }
            size_t execute_update(const std::string&) override { return 0; }
            std::unique_ptr<extract::IPreparedStatement> prepare_statement(const std::string&) override {
                return nullptr;
            }
            void begin_transaction() override {}
            void commit() override {}
            void rollback() override {}
            bool in_transaction() const override { return false; }
            std::string get_database_type() const override { return "empty_mock"; }
            std::string get_version() const override { return "1.0"; }
            void set_query_timeout(int) override {}
            bool table_exists(const std::string& table_name, const std::string& = "") const override {
                // Return true for all OMOP CDM vocabulary tables that might be needed for initialization
                return (table_name == "concept" || table_name == "concept_relationship" || 
                        table_name == "concept_ancestor" || table_name == "vocabulary" || 
                        table_name == "domain" || table_name == "concept_class" || 
                        table_name == "relationship" || table_name == "concept_synonym" ||
                        table_name == "drug_strength" || table_name == "source_to_concept_map");
            }
        };
        
        VocabularyServiceManager::initialize(std::make_unique<EmptyMockConnection>());
    }
    
    auto& vocab_service = VocabularyServiceManager::instance();
    auto transform = std::make_unique<VocabularyTransformation>(vocab_service);
    
    YAML::Node params;
    params["vocabulary"] = "TestVocab";
    params["default_value"] = 0;
    transform->configure(params);
    
    // Test with empty string
    std::string empty = "";
    auto result = transform->transform_safe(empty, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ(0, std::any_cast<int>(result.value));
    
    // Test with very long string
    std::string long_value(1000, 'X');
    result = transform->transform_safe(long_value, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ(0, std::any_cast<int>(result.value)); // No mapping found
    
    // Test with special characters
    std::string special = "Test\nValue\tWith\rSpecial";
    result = transform->transform_safe(special, *context_);
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ(0, std::any_cast<int>(result.value));
}

// Test transformation chain edge cases
TEST_F(TransformationEdgeCasesTest, TransformationChainEdgeCases) {
    TransformationChain chain;
    
    // Add multiple transformations that could fail
    auto trim = std::make_unique<StringManipulationTransformation>();
    YAML::Node trim_params;
    trim_params["operation"] = "trim";
    trim->configure(trim_params);
    chain.add_transformation(std::move(trim));
    
    auto upper = std::make_unique<StringManipulationTransformation>();
    YAML::Node upper_params;
    upper_params["operation"] = "uppercase";
    upper->configure(upper_params);
    chain.add_transformation(std::move(upper));
    
    auto extract = std::make_unique<StringPatternExtractionTransformation>();
    YAML::Node extract_params;
    extract_params["pattern_type"] = "custom";
    extract_params["pattern"] = R"([A-Z ]+)";
    extract->configure(extract_params);
    chain.add_transformation(std::move(extract));
    
    // Test with input that succeeds through all transformations
    std::string input = "  hello world  ";
    std::any result;
    bool success = true;
    try {
        result = chain.apply(input, *context_);
    } catch (...) {
        success = false;
    }
    EXPECT_TRUE(success);
    EXPECT_EQ("HELLO WORLD", std::any_cast<std::string>(result));
    
    // Test with null input
    std::any null_input;
    EXPECT_THROW(
        chain.apply(null_input, *context_),
        common::TransformationException
    );
}

// Test record batch edge cases
TEST_F(TransformationEdgeCasesTest, RecordBatchEdgeCases) {
    core::RecordBatch batch;
    
    // Test empty batch
    EXPECT_TRUE(batch.isEmpty());
    EXPECT_EQ(0, batch.size());
    
    // Test very large batch
    const size_t large_size = 100000;
    batch.reserve(large_size);
    
    for (size_t i = 0; i < large_size; ++i) {
        core::Record record;
        record.setField("id", static_cast<int>(i));
        batch.addRecord(record);
    }
    
    EXPECT_EQ(large_size, batch.size());
    
    // Test batch sorting with custom comparator
    batch.sortInPlace([](const core::Record& a, const core::Record& b) {
        return std::any_cast<int>(a.getField("id")) > 
               std::any_cast<int>(b.getField("id"));
    });
    
    // Verify first and last elements after sorting
    EXPECT_EQ(large_size - 1, std::any_cast<int>(batch.getRecord(0).getField("id")));
    EXPECT_EQ(0, std::any_cast<int>(batch.getRecord(large_size - 1).getField("id")));
    
    // Test removeIf with predicate
    auto removed = batch.removeIf([](const core::Record& record) {
        return std::any_cast<int>(record.getField("id")) % 2 == 0;
    });
    
    EXPECT_EQ(large_size / 2, removed);
    EXPECT_EQ(large_size / 2, batch.size());
}

// Test processing context edge cases
TEST_F(TransformationEdgeCasesTest, ProcessingContextEdgeCases) {
    core::ProcessingContext context;
    
    // Test with very long job ID
    std::string long_job_id(1000, 'X');
    context.set_job_id(long_job_id);
    EXPECT_EQ(long_job_id, context.job_id());
    
    // Test error threshold boundary conditions
    context.set_error_threshold(0.0); // No errors allowed
    context.increment_processed(100);
    context.increment_errors(1);
    EXPECT_TRUE(context.is_error_threshold_exceeded());
    
    // Test with threshold of 1.0 (all errors allowed)
    context.set_error_threshold(1.0);
    EXPECT_FALSE(context.is_error_threshold_exceeded());
    
    // Test concurrent access to context data
    const int num_threads = 10;
    std::vector<std::thread> threads;
    
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&context, i]() {
            for (int j = 0; j < 1000; ++j) {
                context.set_data("key_" + std::to_string(i), j);
                context.increment_processed();
                if (j % 10 == 0) {
                    context.increment_errors();
                }
            }
        });
    }
    
    for (auto& t : threads) {
        t.join();
    }
    
    EXPECT_EQ(num_threads * 1000 + 100, context.processed_count());
    EXPECT_EQ(num_threads * 100 + 1, context.error_count());
}

} // namespace omop::transform::test