// tests/unit/transform/field_transformation_helpers_test.cpp

#include <gtest/gtest.h>
#include "transform/field_transformations.h"
#include "transform/transformation_engine.h"
#include "core/interfaces.h"
#include "test_helpers.h"
#include <memory>
#include <thread>
#include <chrono>

namespace omop::transform::test {

class FieldTransformationHelpersTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Ensure transformations are registered
        TransformationRegistrar::RegisterAll();
        
        context_ = std::make_unique<core::ProcessingContext>();
    }

    void TearDown() override {
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
};

// Test FieldMapping structure
TEST_F(FieldTransformationHelpersTest, FieldMappingBasic) {
    FieldMapping mapping;
    mapping.source_field = "patient_id";
    mapping.target_field = "person_id";
    mapping.transformation_type = "direct";
    mapping.is_required = true;
    mapping.default_value = "0";
    mapping.validation_rules = {"required", "numeric"};

    EXPECT_EQ("patient_id", mapping.source_field);
    EXPECT_EQ("person_id", mapping.target_field);
    EXPECT_EQ("direct", mapping.transformation_type);
    EXPECT_TRUE(mapping.is_required);
    EXPECT_EQ("0", mapping.default_value);
    EXPECT_EQ(2, mapping.validation_rules.size());
}

// Test TransformationChain
TEST_F(FieldTransformationHelpersTest, TransformationChainSingle) {
    TransformationChain chain;

    auto transform = std::make_unique<DirectTransformation>();
    chain.add_transformation(std::move(transform));

    EXPECT_EQ(1, chain.size());
    EXPECT_FALSE(chain.empty());

    std::string input = "test_value";
    auto result = chain.apply(input, *context_);

    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("test_value", std::any_cast<std::string>(result));
}

// Test TransformationChain with multiple transformations
TEST_F(FieldTransformationHelpersTest, TransformationChainMultiple) {
    TransformationChain chain;

    // Add uppercase transformation
    auto upper_transform = std::make_unique<StringManipulationTransformation>();
    YAML::Node upper_params;
    upper_params["operation"] = "uppercase";
    upper_transform->configure(upper_params);
    chain.add_transformation(std::move(upper_transform));

    // Add trim transformation
    auto trim_transform = std::make_unique<StringManipulationTransformation>();
    YAML::Node trim_params;
    trim_params["operation"] = "trim";
    trim_transform->configure(trim_params);
    chain.add_transformation(std::move(trim_transform));

    EXPECT_EQ(2, chain.size());

    std::string input = "  hello world  ";
    auto result = chain.apply(input, *context_);

    ASSERT_TRUE(result.has_value());
    // Should be uppercase and trimmed
    EXPECT_EQ("HELLO WORLD", std::any_cast<std::string>(result));
}

// Test TransformationChain clear
TEST_F(FieldTransformationHelpersTest, TransformationChainClear) {
    TransformationChain chain;

    chain.add_transformation(std::make_unique<DirectTransformation>());
    chain.add_transformation(std::make_unique<DirectTransformation>());

    EXPECT_EQ(2, chain.size());

    chain.clear();

    EXPECT_EQ(0, chain.size());
    EXPECT_TRUE(chain.empty());
}

// Test TransformationChain error handling
TEST_F(FieldTransformationHelpersTest, TransformationChainError) {
    TransformationChain chain;

    // Add date transformation that will fail with invalid input
    auto date_transform = std::make_unique<DateTransformation>();
    YAML::Node params;
    params["format"] = "%Y-%m-%d";
    date_transform->configure(params);
    chain.add_transformation(std::move(date_transform));

    std::string input = "invalid-date";

    EXPECT_THROW(
        chain.apply(input, *context_),
        common::TransformationException
    );
}

// Test FieldTransformationBuilder
TEST_F(FieldTransformationHelpersTest, FieldTransformationBuilder) {
    FieldTransformationBuilder builder;

    auto mapping = builder
        .from_field("source_column")
        .to_field("target_column")
        .with_transformation("vocabulary_mapping", YAML::Node())
        .required(true)
        .with_default("0")
        .validate_with("required")
        .validate_with("numeric")
        .build();

    EXPECT_EQ("source_column", mapping.source_field);
    EXPECT_EQ("target_column", mapping.target_field);
    EXPECT_EQ("vocabulary_mapping", mapping.transformation_type);
    EXPECT_TRUE(mapping.is_required);
    EXPECT_EQ("0", mapping.default_value);
    EXPECT_EQ(2, mapping.validation_rules.size());
}

// Test FieldTransformationBuilder with parameters
TEST_F(FieldTransformationHelpersTest, FieldTransformationBuilderWithParams) {
    FieldTransformationBuilder builder;

    YAML::Node params;
    params["operation"] = "multiply";
    params["operand"] = 2.5;

    auto mapping = builder
        .from_field("height_cm")
        .to_field("height_m")
        .with_transformation("numeric_transform", params)
        .build();

    EXPECT_EQ("height_cm", mapping.source_field);
    EXPECT_EQ("height_m", mapping.target_field);
    EXPECT_EQ("numeric_transform", mapping.transformation_type);
    EXPECT_EQ("multiply", mapping.transformation_params["operation"].as<std::string>());
    EXPECT_DOUBLE_EQ(2.5, mapping.transformation_params["operand"].as<double>());
}

// Test BatchFieldTransformer basic transformation
TEST_F(FieldTransformationHelpersTest, BatchFieldTransformerBasic) {
    BatchFieldTransformer transformer;

    // Add simple mapping
    FieldMapping mapping;
    mapping.source_field = "patient_id";
    mapping.target_field = "person_id";
    mapping.transformation_type = "direct";
    transformer.add_mapping(mapping);

    // Create input record
    core::Record input_record;
    input_record.setField("patient_id", 12345);

    auto output_record = transformer.transform(input_record, *context_);

    auto person_id = output_record.getField("person_id");
    ASSERT_TRUE(person_id.has_value());
    EXPECT_EQ(12345, std::any_cast<int>(person_id));
}

// Test BatchFieldTransformer with multiple mappings
TEST_F(FieldTransformationHelpersTest, BatchFieldTransformerMultipleMappings) {
    BatchFieldTransformer transformer;

    // Add multiple mappings
    FieldMapping mapping1;
    mapping1.source_field = "first_name";
    mapping1.target_field = "given_name";
    mapping1.transformation_type = "direct";
    transformer.add_mapping(mapping1);

    FieldMapping mapping2;
    mapping2.source_field = "last_name";
    mapping2.target_field = "family_name";
    mapping2.transformation_type = "direct";
    transformer.add_mapping(mapping2);

    EXPECT_EQ(2, transformer.mapping_count());

    // Create input record
    core::Record input_record;
    input_record.setField("first_name", std::string("John"));
    input_record.setField("last_name", std::string("Doe"));

    auto output_record = transformer.transform(input_record, *context_);

    EXPECT_EQ("John", std::any_cast<std::string>(output_record.getField("given_name")));
    EXPECT_EQ("Doe", std::any_cast<std::string>(output_record.getField("family_name")));
}

// Test BatchFieldTransformer with default value
TEST_F(FieldTransformationHelpersTest, BatchFieldTransformerDefaultValue) {
    BatchFieldTransformer transformer;

    FieldMapping mapping;
    mapping.source_field = "status";
    mapping.target_field = "status_code";
    mapping.transformation_type = "direct";
    mapping.is_required = true;
    mapping.default_value = "UNKNOWN";
    transformer.add_mapping(mapping);

    // Create input record without the field
    core::Record input_record;
    input_record.setField("other_field", std::string("value"));

    auto output_record = transformer.transform(input_record, *context_);

    auto status_code = output_record.getField("status_code");
    ASSERT_TRUE(status_code.has_value());
    EXPECT_EQ("UNKNOWN", std::any_cast<std::string>(status_code));
}

// Test BatchFieldTransformer copy unmapped fields
TEST_F(FieldTransformationHelpersTest, BatchFieldTransformerCopyUnmapped) {
    BatchFieldTransformer transformer;
    transformer.set_copy_unmapped_fields(true);

    // Add one mapping
    FieldMapping mapping;
    mapping.source_field = "id";
    mapping.target_field = "identifier";
    mapping.transformation_type = "direct";
    transformer.add_mapping(mapping);

    // Create input record with mapped and unmapped fields
    core::Record input_record;
    input_record.setField("id", 123);
    input_record.setField("name", std::string("Test"));
    input_record.setField("value", 45.6);

    auto output_record = transformer.transform(input_record, *context_);

    // Check mapped field
    EXPECT_EQ(123, std::any_cast<int>(output_record.getField("identifier")));

    // Check unmapped fields were copied
    EXPECT_EQ("Test", std::any_cast<std::string>(output_record.getField("name")));
    EXPECT_DOUBLE_EQ(45.6, std::any_cast<double>(output_record.getField("value")));
}

// Test BatchFieldTransformer clear mappings
TEST_F(FieldTransformationHelpersTest, BatchFieldTransformerClearMappings) {
    BatchFieldTransformer transformer;

    // Add mappings
    transformer.add_mapping(FieldMapping{});
    transformer.add_mapping(FieldMapping{});

    EXPECT_EQ(2, transformer.mapping_count());

    transformer.clear_mappings();

    EXPECT_EQ(0, transformer.mapping_count());
}

// Test TransformationCache basic operations
TEST_F(FieldTransformationHelpersTest, TransformationCacheBasic) {
    TransformationCache cache(10);

    TransformationCache::CacheKey key = "field1:value1";
    TransformationCache::CacheValue value = std::string("transformed1");

    // Put value in cache
    cache.put(key, value);

    // Get value from cache
    auto cached = cache.get(key);
    ASSERT_TRUE(cached.has_value());
    EXPECT_EQ("transformed1", std::any_cast<std::string>(*cached));

    // Get non-existent value
    TransformationCache::CacheKey key2 = "field2:value2";
    auto not_found = cache.get(key2);
    EXPECT_FALSE(not_found.has_value());
}

// Test TransformationCache LRU eviction
TEST_F(FieldTransformationHelpersTest, TransformationCacheLRU) {
    TransformationCache cache(3); // Small cache size

    // Fill cache
    cache.put("f1:1", 100);
    cache.put("f2:2", 200);
    cache.put("f3:3", 300);

    // Access first item to make it recently used
    cache.get("f1:1");

    // Add new item, should evict f2 (least recently used)
    cache.put("f4:4", 400);

    // Check that f2 was evicted
    EXPECT_FALSE(cache.get("f2:2").has_value());

    // Check others are still there
    EXPECT_TRUE(cache.get("f1:1").has_value());
    EXPECT_TRUE(cache.get("f3:3").has_value());
    EXPECT_TRUE(cache.get("f4:4").has_value());
}

// Test TransformationCache statistics
TEST_F(FieldTransformationHelpersTest, TransformationCacheStats) {
    TransformationCache cache(100);

    // Add some items
    cache.put("f1:1", 100);
    cache.put("f2:2", 200);

    // Some hits
    cache.get("f1:1");
    cache.get("f2:2");

    // Some misses
    cache.get("f3:3");
    cache.get("f4:4");

    auto stats = cache.get_stats();
    EXPECT_EQ(2, stats.size);
    EXPECT_EQ(2, stats.hits);
    EXPECT_EQ(2, stats.misses);
    EXPECT_DOUBLE_EQ(0.5, stats.hit_rate);
}

// Test TransformationCache clear
TEST_F(FieldTransformationHelpersTest, TransformationCacheClear) {
    TransformationCache cache(100);

    cache.put("f1:1", 100);
    cache.put("f2:2", 200);

    auto stats_before = cache.get_stats();
    EXPECT_EQ(2, stats_before.size);

    cache.clear();

    auto stats_after = cache.get_stats();
    EXPECT_EQ(0, stats_after.size);
    EXPECT_EQ(0, stats_after.hits);
    EXPECT_EQ(0, stats_after.misses);
}

// Test TransformationMetrics recording
TEST_F(FieldTransformationHelpersTest, TransformationMetricsRecord) {
    TransformationMetrics metrics;

    // Record successful transformation
    metrics.record_execution("person_id", "direct",
                           std::chrono::milliseconds(10), true);

    // Record failed transformation
    metrics.record_execution("birth_date", "date_transform",
                           std::chrono::milliseconds(5), false);

    // Record another successful for same field
    metrics.record_execution("person_id", "direct",
                           std::chrono::milliseconds(8), true);

    // Get field stats
    auto person_stats = metrics.get_field_stats("person_id");
    EXPECT_EQ(2, person_stats.total_count);
    EXPECT_EQ(2, person_stats.success_count);
    EXPECT_EQ(0, person_stats.error_count);
    EXPECT_DOUBLE_EQ(1.0, person_stats.success_rate());

    auto birth_stats = metrics.get_field_stats("birth_date");
    EXPECT_EQ(1, birth_stats.total_count);
    EXPECT_EQ(0, birth_stats.success_count);
    EXPECT_EQ(1, birth_stats.error_count);
    EXPECT_DOUBLE_EQ(0.0, birth_stats.success_rate());
}

// Test TransformationMetrics by transformation type
TEST_F(FieldTransformationHelpersTest, TransformationMetricsByType) {
    TransformationMetrics metrics;

    // Record various transformations
    metrics.record_execution("f1", "direct", std::chrono::milliseconds(5), true);
    metrics.record_execution("f2", "direct", std::chrono::milliseconds(7), true);
    metrics.record_execution("f3", "vocabulary_mapping", std::chrono::milliseconds(15), true);
    metrics.record_execution("f4", "vocabulary_mapping", std::chrono::milliseconds(20), false);

    // Get transformation type stats
    auto direct_stats = metrics.get_transformation_stats("direct");
    EXPECT_EQ(2, direct_stats.total_count);
    EXPECT_EQ(2, direct_stats.success_count);
    EXPECT_DOUBLE_EQ(1.0, direct_stats.success_rate());

    auto vocab_stats = metrics.get_transformation_stats("vocabulary_mapping");
    EXPECT_EQ(2, vocab_stats.total_count);
    EXPECT_EQ(1, vocab_stats.success_count);
    EXPECT_DOUBLE_EQ(0.5, vocab_stats.success_rate());
}

// Test TransformationMetrics field names
TEST_F(FieldTransformationHelpersTest, TransformationMetricsFieldNames) {
    TransformationMetrics metrics;

    metrics.record_execution("field1", "direct", std::chrono::milliseconds(1), true);
    metrics.record_execution("field2", "direct", std::chrono::milliseconds(1), true);
    metrics.record_execution("field3", "direct", std::chrono::milliseconds(1), true);

    auto field_names = metrics.get_field_names();
    EXPECT_EQ(3, field_names.size());

    // Check all field names are present
    EXPECT_TRUE(std::find(field_names.begin(), field_names.end(), "field1") != field_names.end());
    EXPECT_TRUE(std::find(field_names.begin(), field_names.end(), "field2") != field_names.end());
    EXPECT_TRUE(std::find(field_names.begin(), field_names.end(), "field3") != field_names.end());
}

// Test TransformationMetrics reset
TEST_F(FieldTransformationHelpersTest, TransformationMetricsReset) {
    TransformationMetrics metrics;

    metrics.record_execution("field1", "direct", std::chrono::milliseconds(1), true);
    metrics.record_execution("field2", "direct", std::chrono::milliseconds(1), true);

    auto names_before = metrics.get_field_names();
    EXPECT_EQ(2, names_before.size());

    metrics.reset();

    auto names_after = metrics.get_field_names();
    EXPECT_EQ(0, names_after.size());
}

// Test TransformationMetrics average duration
TEST_F(FieldTransformationHelpersTest, TransformationMetricsAverageDuration) {
    TransformationMetrics metrics;

    // Record with different durations
    metrics.record_execution("field1", "transform1", std::chrono::milliseconds(10), true);
    metrics.record_execution("field1", "transform1", std::chrono::milliseconds(20), true);
    metrics.record_execution("field1", "transform1", std::chrono::milliseconds(30), true);

    auto stats = metrics.get_field_stats("field1");
    EXPECT_DOUBLE_EQ(0.02, stats.average_duration()); // 20ms average in seconds
}

// Test thread safety of TransformationCache
TEST_F(FieldTransformationHelpersTest, TransformationCacheThreadSafety) {
    TransformationCache cache(1000);
    const int num_threads = 10;
    const int ops_per_thread = 100;
    std::atomic<int> success_count{0};

    auto thread_func = [&cache, &success_count]() {
        for (int i = 0; i < ops_per_thread; ++i) {
            // Mix of puts and gets
            if (i % 2 == 0) {
                cache.put("field_" + std::to_string(i), i * 100);
            } else {
                auto value = cache.get("field_" + std::to_string(i - 1));
                if (value.has_value()) {
                    success_count++;
                }
            }
        }
    };

    std::vector<std::thread> threads;
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(thread_func);
    }

    for (auto& t : threads) {
        t.join();
    }

    // Should have some successful gets
    EXPECT_GT(success_count.load(), 0);
}

// Test thread safety of TransformationMetrics
TEST_F(FieldTransformationHelpersTest, TransformationMetricsThreadSafety) {
    TransformationMetrics metrics;
    const int num_threads = 10;
    const int records_per_thread = 100;

    auto thread_func = [&metrics](int thread_id) {
        for (int i = 0; i < records_per_thread; ++i) {
            std::string field = "field_" + std::to_string(thread_id);
            metrics.record_execution(field, "transform",
                                   std::chrono::milliseconds(i), true);
        }
    };

    std::vector<std::thread> threads;
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(thread_func, i);
    }

    for (auto& t : threads) {
        t.join();
    }

    // Verify all threads recorded their metrics
    auto field_names = metrics.get_field_names();
    EXPECT_EQ(num_threads, field_names.size());

    for (int i = 0; i < num_threads; ++i) {
        auto stats = metrics.get_field_stats("field_" + std::to_string(i));
        EXPECT_EQ(records_per_thread, stats.total_count);
    }
}

// Test complex transformation scenario with all helpers
TEST_F(FieldTransformationHelpersTest, ComplexTransformationScenario) {
    // Set up transformer with cache and metrics
    BatchFieldTransformer transformer;
    TransformationCache cache(100);
    TransformationMetrics metrics;

    // Build field mappings
    FieldTransformationBuilder builder;

    auto mapping1 = builder
        .from_field("patient_id")
        .to_field("person_id")
        .with_transformation("direct")
        .required()
        .build();
    transformer.add_mapping(mapping1);

    YAML::Node upper_params;
    upper_params["operation"] = "uppercase";

    auto mapping2 = builder
        .from_field("gender")
        .to_field("gender_code")
        .with_transformation("string_manipulation", upper_params)
        .with_default("U")
        .build();
    transformer.add_mapping(mapping2);

    // Transform records with caching and metrics
    for (int i = 0; i < 10; ++i) {
        core::Record input;
        input.setField("patient_id", i);
        input.setField("gender", i % 2 == 0 ? "m" : "f");

        auto start = std::chrono::steady_clock::now();

        // Check cache first
        TransformationCache::CacheKey key = "record_" + std::to_string(i);
        auto cached = cache.get(key);

        core::Record output;
        if (!cached.has_value()) {
            output = transformer.transform(input, *context_);
            cache.put(key, output);
        } else {
            output = std::any_cast<core::Record>(*cached);
        }

        auto duration = std::chrono::steady_clock::now() - start;
        metrics.record_execution("batch_transform", "complex",
                               std::chrono::duration_cast<std::chrono::milliseconds>(duration),
                               true);
    }

    // Verify results
    auto cache_stats = cache.get_stats();
    EXPECT_GT(cache_stats.size, 0);

    auto transform_stats = metrics.get_transformation_stats("complex");
    EXPECT_EQ(10, transform_stats.total_count);
    EXPECT_EQ(10, transform_stats.success_count);
}

// Test cache key comparison
TEST_F(FieldTransformationHelpersTest, CacheKeyComparison) {
    TransformationCache::CacheKey key1 = "field1:value1";
    TransformationCache::CacheKey key2 = "field2:value2";

    EXPECT_NE(key1, key2);
    EXPECT_EQ(key1, key1);
}

// Test cache operations
TEST_F(FieldTransformationHelpersTest, CacheOperations) {
    TransformationCache cache(100);

    // Test cache eviction
    for (int i = 0; i < 150; ++i) {
        cache.put("field_" + std::to_string(i), i * 100);
        if (i > 0) {
            auto value = cache.get("field_" + std::to_string(i - 1));
            EXPECT_TRUE(value.has_value());
        }
    }
}

} // namespace omop::transform::test