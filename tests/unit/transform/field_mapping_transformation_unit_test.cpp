// tests/unit/transform/field_transformation_test.cpp

#include <gtest/gtest.h>
#include "transform/transformation_engine.h"
#include "transform/transformations.h"
#include "core/interfaces.h"
#include <chrono>
#include <any>

namespace omop::transform::test {

class FieldTransformationTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
    }

    void TearDown() override {
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
};

// Test DirectTransformation
TEST_F(FieldTransformationTest, DirectTransformationPassThrough) {
    DirectTransformation transform;

    std::string input = "test_value";
    auto result = transform.transform(input, *context_);

    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("test_value", std::any_cast<std::string>(result));
}

// Test DirectTransformation validation
TEST_F(FieldTransformationTest, DirectTransformationValidation) {
    DirectTransformation transform;

    EXPECT_TRUE(transform.validate_input(std::string("test")));
    EXPECT_TRUE(transform.validate_input(123));
    EXPECT_FALSE(transform.validate_input(std::any{}));
}

// Test DirectTransformation type
TEST_F(FieldTransformationTest, DirectTransformationType) {
    DirectTransformation transform;
    EXPECT_EQ("direct", transform.get_type());
}

// Test DateTransformation with string input
TEST_F(FieldTransformationTest, DateTransformationStringInput) {
    DateTransformation transform;

    YAML::Node params;
    params["format"] = "%Y-%m-%d";
    params["output_format"] = "%Y-%m-%d %H:%M:%S";
    params["add_time"] = true;
    params["default_time"] = "12:00:00";
    transform.configure(params);

    std::string input = "2024-03-15";
    auto result = transform.transform(input, *context_);

    ASSERT_TRUE(result.has_value());
    std::string output = std::any_cast<std::string>(result);
    EXPECT_EQ("2024-03-15 12:00:00", output);
}

// Test DateTransformation with time_point input
TEST_F(FieldTransformationTest, DateTransformationTimePointInput) {
    DateTransformation transform;

    YAML::Node params;
    params["output_format"] = "%Y-%m-%d";
    transform.configure(params);

    auto now = std::chrono::system_clock::now();
    auto result = transform.transform(now, *context_);

    ASSERT_TRUE(result.has_value());
    std::string output = std::any_cast<std::string>(result);
    EXPECT_EQ(10, output.length()); // YYYY-MM-DD format
}

// Test DateTransformation with invalid input
TEST_F(FieldTransformationTest, DateTransformationInvalidInput) {
    DateTransformation transform;

    YAML::Node params;
    params["format"] = "%Y-%m-%d";
    transform.configure(params);

    std::string input = "invalid-date";
    EXPECT_THROW(
        transform.transform(input, *context_),
        common::TransformationException
    );
}

// Test DateTransformation validation
TEST_F(FieldTransformationTest, DateTransformationValidation) {
    DateTransformation transform;

    EXPECT_TRUE(transform.validate_input(std::string("2024-03-15")));
    EXPECT_TRUE(transform.validate_input(std::chrono::system_clock::now()));
    EXPECT_FALSE(transform.validate_input(std::any{}));
    EXPECT_FALSE(transform.validate_input(123));
}

// Test NumericTransformation multiply operation
TEST_F(FieldTransformationTest, NumericTransformationMultiply) {
    NumericTransformation transform;

    YAML::Node params;
    params["operation"] = "multiply";
    params["operand"] = 2.5;
    transform.configure(params);

    double input = 10.0;
    auto result = transform.transform(input, *context_);

    ASSERT_TRUE(result.has_value());
    EXPECT_DOUBLE_EQ(25.0, std::any_cast<double>(result));
}

// Test NumericTransformation divide operation
TEST_F(FieldTransformationTest, NumericTransformationDivide) {
    NumericTransformation transform;

    YAML::Node params;
    params["operation"] = "divide";
    params["operand"] = 2.0;
    transform.configure(params);

    double input = 10.0;
    auto result = transform.transform(input, *context_);

    ASSERT_TRUE(result.has_value());
    EXPECT_DOUBLE_EQ(5.0, std::any_cast<double>(result));
}

// Test NumericTransformation divide by zero
TEST_F(FieldTransformationTest, NumericTransformationDivideByZero) {
    NumericTransformation transform;

    YAML::Node params;
    params["operation"] = "divide";
    params["operand"] = 0.0;
    transform.configure(params);

    double input = 10.0;
    EXPECT_THROW(
        transform.transform(input, *context_),
        common::TransformationException
    );
}

// Test NumericTransformation round operation
TEST_F(FieldTransformationTest, NumericTransformationRound) {
    NumericTransformation transform;

    YAML::Node params;
    params["operation"] = "round";
    params["precision"] = 2;
    transform.configure(params);

    double input = 3.14159;
    auto result = transform.transform(input, *context_);

    ASSERT_TRUE(result.has_value());
    EXPECT_DOUBLE_EQ(3.14, std::any_cast<double>(result));
}

// Test NumericTransformation with min/max constraints
TEST_F(FieldTransformationTest, NumericTransformationConstraints) {
    NumericTransformation transform;

    YAML::Node params;
    params["operation"] = "add";
    params["operand"] = 0.0;
    params["min_value"] = 0.0;
    params["max_value"] = 100.0;
    transform.configure(params);

    // Test clamping to min
    double input = -10.0;
    auto result = transform.transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_DOUBLE_EQ(0.0, std::any_cast<double>(result));

    // Test clamping to max
    input = 150.0;
    result = transform.transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_DOUBLE_EQ(100.0, std::any_cast<double>(result));
}

// Test NumericTransformation with string input
TEST_F(FieldTransformationTest, NumericTransformationStringInput) {
    NumericTransformation transform;

    YAML::Node params;
    params["operation"] = "multiply";
    params["operand"] = 2.0;
    transform.configure(params);

    std::string input = "5.5";
    auto result = transform.transform(input, *context_);

    ASSERT_TRUE(result.has_value());
    EXPECT_DOUBLE_EQ(11.0, std::any_cast<double>(result));
}

// Test StringConcatenationTransformation single field
TEST_F(FieldTransformationTest, StringConcatenationSingleField) {
    StringConcatenationTransformation transform;

    YAML::Node params;
    params["prefix"] = "PRE_";
    params["suffix"] = "_SUF";
    transform.configure(params);

    std::string input = "test";
    auto result = transform.transform(input, *context_);

    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("PRE_test_SUF", std::any_cast<std::string>(result));
}

// Test StringConcatenationTransformation multiple fields
TEST_F(FieldTransformationTest, StringConcatenationMultipleFields) {
    StringConcatenationTransformation transform;

    YAML::Node params;
    params["separator"] = " - ";
    params["skip_empty"] = true;
    params["prefix"] = "[";
    params["suffix"] = "]";
    params["source_fields"] = std::vector<std::string>{"field1", "field2", "field3"};
    transform.configure(params);

    std::unordered_map<std::string, std::any> values;
    values["field1"] = std::string("Value1");
    values["field2"] = std::string("");
    values["field3"] = std::string("Value3");

    auto result = transform.transform_multiple(values, *context_);

    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("[Value1 - Value3]", std::any_cast<std::string>(result));
}

// Test ConditionalTransformation with string condition
TEST_F(FieldTransformationTest, ConditionalTransformationStringEquals) {
    ConditionalTransformation transform;

    YAML::Node params;
    YAML::Node condition;
    condition["operator"] = "equals";
    condition["value"] = "test";
    condition["then"] = "matched";
    condition["else"] = "not_matched";
    params["conditions"].push_back(condition);
    params["default"] = "default_value";
    transform.configure(params);

    // Test matching condition
    std::string input = "test";
    auto result = transform.transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("matched", std::any_cast<std::string>(result));

    // Test non-matching condition
    input = "other";
    result = transform.transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("not_matched", std::any_cast<std::string>(result));
}

// Test ConditionalTransformation with numeric condition
TEST_F(FieldTransformationTest, ConditionalTransformationNumericComparison) {
    ConditionalTransformation transform;

    YAML::Node params;
    YAML::Node condition;
    condition["operator"] = "greater_than";
    condition["value"] = 10.0;
    condition["then"] = "high";
    params["conditions"].push_back(condition);
    params["default"] = "low";
    transform.configure(params);

    // Test greater than
    double input = 15.0;
    auto result = transform.transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("high", std::any_cast<std::string>(result));

    // Test less than
    input = 5.0;
    result = transform.transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("low", std::any_cast<std::string>(result));
}

// Test ConditionalTransformation with null checks
TEST_F(FieldTransformationTest, ConditionalTransformationNullChecks) {
    ConditionalTransformation transform;

    YAML::Node params;
    YAML::Node condition;
    condition["operator"] = "is_null";
    condition["then"] = "null_value";
    params["conditions"].push_back(condition);
    params["default"] = "has_value";
    transform.configure(params);

    // Test null value
    std::any input;
    auto result = transform.transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("null_value", std::any_cast<std::string>(result));

    // Test non-null value
    input = std::string("test");
    result = transform.transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("has_value", std::any_cast<std::string>(result));
}

// Test ConditionalTransformation with regex matching
TEST_F(FieldTransformationTest, ConditionalTransformationRegexMatch) {
    ConditionalTransformation transform;

    YAML::Node params;
    YAML::Node condition;
    condition["operator"] = "matches";
    condition["value"] = R"(\d{3}-\d{2}-\d{4})";
    condition["then"] = "valid_ssn";
    params["conditions"].push_back(condition);
    params["default"] = "invalid_ssn";
    transform.configure(params);

    // Test matching pattern
    std::string input = "***********";
    auto result = transform.transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("valid_ssn", std::any_cast<std::string>(result));

    // Test non-matching pattern
    input = "invalid";
    result = transform.transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("invalid_ssn", std::any_cast<std::string>(result));
}

} // namespace omop::transform::test