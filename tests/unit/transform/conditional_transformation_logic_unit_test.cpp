// tests/unit/transform/conditional_transformations_test.cpp

#include <gtest/gtest.h>
#include "transform/conditional_transformations.h"
#include "transform/transformations.h"
#include "core/interfaces.h"
#include <memory>
#include <yaml-cpp/yaml.h>

namespace omop::transform::test {

class ConditionalTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        registry_ = &TransformationRegistry::instance();
    }

    void TearDown() override {
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
    TransformationRegistry* registry_;
};

// Test advanced conditional transformation with simple condition
TEST_F(ConditionalTransformationsTest, AdvancedConditionalSimpleCondition) {
    auto transform = registry_->create_transformation("advanced_conditional");

    YAML::Node params;
    YAML::Node rule;
    rule["description"] = "Test simple condition";

    YAML::Node condition;
    condition["field"] = "value";
    condition["operator"] = "equals";
    condition["value"] = "test";
    rule["conditions"].push_back(condition);

    YAML::Node then_action;
    then_action["type"] = "set_value";
    then_action["value"] = "matched";
    rule["then"] = then_action;

    YAML::Node else_action;
    else_action["type"] = "set_value";
    else_action["value"] = "not_matched";
    rule["else"] = else_action;

    params["rules"].push_back(rule);
    transform->configure(params);

    // Test matching condition
    std::string input = "test";
    auto result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("matched", std::any_cast<std::string>(result));

    // Test non-matching condition
    input = "other";
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("not_matched", std::any_cast<std::string>(result));
}

// Test advanced conditional transformation with numeric conditions
TEST_F(ConditionalTransformationsTest, AdvancedConditionalNumericConditions) {
    auto transform = registry_->create_transformation("advanced_conditional");

    YAML::Node params;
    YAML::Node rule;

    YAML::Node condition;
    condition["field"] = "value";
    condition["operator"] = "greater_than";
    condition["value"] = 100;
    rule["conditions"].push_back(condition);

    YAML::Node then_action;
    then_action["type"] = "set_value";
    then_action["value"] = "high";
    rule["then"] = then_action;

    params["rules"].push_back(rule);

    YAML::Node default_action;
    default_action["type"] = "set_value";
    default_action["value"] = "low";
    params["default_action"] = default_action;

    transform->configure(params);

    // Test greater than
    double input = 150.0;
    auto result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("high", std::any_cast<std::string>(result));
    // Test less than
    input = 50.0;
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("low", std::any_cast<std::string>(result));
}

// Test advanced conditional transformation with nested conditions
TEST_F(ConditionalTransformationsTest, AdvancedConditionalNestedConditions) {
    auto transform = registry_->create_transformation("advanced_conditional");

    YAML::Node params;
    YAML::Node rule;

    YAML::Node outer_condition;
    outer_condition["logical_operator"] = "AND";

    YAML::Node nested1;
    nested1["field"] = "value";
    nested1["operator"] = "greater_than";
    nested1["value"] = 10;

    YAML::Node nested2;
    nested2["field"] = "value";
    nested2["operator"] = "less_than";
    nested2["value"] = 100;

    outer_condition["nested"].push_back(nested1);
    outer_condition["nested"].push_back(nested2);

    rule["conditions"].push_back(outer_condition);

    YAML::Node then_action;
    then_action["type"] = "set_value";
    then_action["value"] = "in_range";
    rule["then"] = then_action;

    params["rules"].push_back(rule);
    params["default_action"]["type"] = "set_value";
    params["default_action"]["value"] = "out_of_range";

    transform->configure(params);

    // Test value in range
    double input = 50.0;
    auto result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("in_range", std::any_cast<std::string>(result));
    // Test value out of range
    input = 150.0;
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("out_of_range", std::any_cast<std::string>(result));
}

// Test advanced conditional transformation with string operations
TEST_F(ConditionalTransformationsTest, AdvancedConditionalStringOperations) {
    auto transform = registry_->create_transformation("advanced_conditional");

    YAML::Node params;
    // Rule 1: Check if contains
    YAML::Node rule1;
    YAML::Node condition1;
    condition1["operator"] = "contains";
    condition1["value"] = "test";
    rule1["conditions"].push_back(condition1);
    rule1["then"]["type"] = "set_value";
    rule1["then"]["value"] = "contains_test";
    params["rules"].push_back(rule1);
    // Rule 2: Check if starts with
    YAML::Node rule2;
    YAML::Node condition2;
    condition2["operator"] = "starts_with";
    condition2["value"] = "hello";
    rule2["conditions"].push_back(condition2);
    rule2["then"]["type"] = "set_value";
    rule2["then"]["value"] = "starts_with_hello";
    params["rules"].push_back(rule2);
    // Rule 3: Check if matches regex
    YAML::Node rule3;
    YAML::Node condition3;
    condition3["operator"] = "matches";
    condition3["value"] = R"(\d{3}-\d{2}-\d{4})";
    rule3["conditions"].push_back(condition3);
    rule3["then"]["type"] = "set_value";
    rule3["then"]["value"] = "valid_ssn";
    params["rules"].push_back(rule3);
    params["default_action"]["type"] = "set_value";
    params["default_action"]["value"] = "no_match";
    transform->configure(params);
    // Test contains
    std::string input = "this is a test string";
    auto result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("contains_test", std::any_cast<std::string>(result));
    // Test starts with
    input = "hello world";
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("starts_with_hello", std::any_cast<std::string>(result));
    // Test regex match
    input = "***********";
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("valid_ssn", std::any_cast<std::string>(result));
}

// Test advanced conditional transformation with null checks
TEST_F(ConditionalTransformationsTest, AdvancedConditionalNullChecks) {
    auto transform = registry_->create_transformation("advanced_conditional");

    YAML::Node params;
    YAML::Node rule1;
    YAML::Node condition1;
    condition1["operator"] = "is_null";
    rule1["conditions"].push_back(condition1);
    rule1["then"]["type"] = "set_value";
    rule1["then"]["value"] = "was_null";
    params["rules"].push_back(rule1);
    YAML::Node rule2;
    YAML::Node condition2;
    condition2["operator"] = "is_not_null";
    rule2["conditions"].push_back(condition2);
    rule2["then"]["type"] = "set_value";
    rule2["then"]["value"] = "not_null";
    params["rules"].push_back(rule2);
    transform->configure(params);
    // Test null value
    std::any input;
    auto result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("was_null", std::any_cast<std::string>(result));
    // Test non-null value
    input = std::string("value");
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("not_null", std::any_cast<std::string>(result));
}

// Test advanced conditional transformation with transformation action
TEST_F(ConditionalTransformationsTest, AdvancedConditionalTransformAction) {
    auto transform = registry_->create_transformation("advanced_conditional");

    YAML::Node params;
    YAML::Node rule;
    YAML::Node condition;
    condition["operator"] = "equals";
    condition["value"] = "transform_me";
    rule["conditions"].push_back(condition);
    YAML::Node then_action;
    then_action["type"] = "transform";
    then_action["transformation"] = "string_manipulation";
    then_action["params"]["operation"] = "uppercase";
    rule["then"] = then_action;
    params["rules"].push_back(rule);
    transform->configure(params);
    std::string input = "transform_me";
    auto result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("TRANSFORM_ME", std::any_cast<std::string>(result));
}

// Test lookup table transformation case insensitive
TEST_F(ConditionalTransformationsTest, LookupTableCaseInsensitive) {
    auto transform = registry_->create_transformation("lookup_table");
    YAML::Node params;
    params["lookup_table"]["A"] = "Alpha";
    params["lookup_table"]["B"] = "Bravo";
    params["case_sensitive"] = false;
    transform->configure(params);
    auto* lookup_transform = dynamic_cast<LookupTableTransformation*>(transform.get());
    ASSERT_NE(lookup_transform, nullptr);
    std::string input = "a";
    auto result = lookup_transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("Alpha", std::any_cast<std::string>(result));
}

// Test lookup table transformation pass through
TEST_F(ConditionalTransformationsTest, LookupTablePassThrough) {
    auto transform = registry_->create_transformation("lookup_table");
    YAML::Node params;
    params["lookup_table"]["A"] = "Alpha";
    params["pass_through_on_miss"] = true;
    transform->configure(params);
    auto* lookup_transform = dynamic_cast<LookupTableTransformation*>(transform.get());
    ASSERT_NE(lookup_transform, nullptr);
    std::string input = "Z";
    auto result = lookup_transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("Z", std::any_cast<std::string>(result));
}

// Test lookup table transformation numeric keys
TEST_F(ConditionalTransformationsTest, LookupTableNumericKeys) {
    auto transform = registry_->create_transformation("lookup_table");
    YAML::Node params;
    params["lookup_table"]["1"] = "One";
    params["lookup_table"]["2"] = "Two";
    params["lookup_table"]["3"] = "Three";
    params["case_sensitive"] = true;
    transform->configure(params);
    auto* lookup_transform = dynamic_cast<LookupTableTransformation*>(transform.get());
    ASSERT_NE(lookup_transform, nullptr);
    int input = 2;
    auto result = lookup_transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("Two", std::any_cast<std::string>(result));
}

// Test advanced conditional with multiple rules
TEST_F(ConditionalTransformationsTest, AdvancedConditionalMultipleRules) {
    auto transform = registry_->create_transformation("advanced_conditional");
    YAML::Node params;
    // Rule 1: Check range 0-10
    YAML::Node rule1;
    YAML::Node cond1_1;
    cond1_1["operator"] = "greater_than_or_equal";
    cond1_1["value"] = 0;
    YAML::Node cond1_2;
    cond1_2["operator"] = "less_than_or_equal";
    cond1_2["value"] = 10;
    rule1["conditions"].push_back(cond1_1);
    rule1["conditions"].push_back(cond1_2);
    rule1["then"]["type"] = "set_value";
    rule1["then"]["value"] = "low";
    params["rules"].push_back(rule1);
    // Rule 2: Check range 11-20
    YAML::Node rule2;
    YAML::Node cond2_1;
    cond2_1["operator"] = "greater_than";
    cond2_1["value"] = 10;
    YAML::Node cond2_2;
    cond2_2["operator"] = "less_than_or_equal";
    cond2_2["value"] = 20;
    rule2["conditions"].push_back(cond2_1);
    rule2["conditions"].push_back(cond2_2);
    rule2["then"]["type"] = "set_value";
    rule2["then"]["value"] = "medium";
    params["rules"].push_back(rule2);
    // Default
    params["default_action"]["type"] = "set_value";
    params["default_action"]["value"] = "high";
    transform->configure(params);
    // Test low range
    double input = 5.0;
    auto result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("low", std::any_cast<std::string>(result));
    // Test medium range
    input = 15.0;
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("medium", std::any_cast<std::string>(result));
    // Test high range (default)
    input = 25.0;
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("high", std::any_cast<std::string>(result));
}

// Test advanced conditional with negated condition
TEST_F(ConditionalTransformationsTest, AdvancedConditionalNegatedCondition) {
    auto transform = registry_->create_transformation("advanced_conditional");
    YAML::Node params;
    YAML::Node rule;
    YAML::Node condition;
    condition["operator"] = "equals";
    condition["value"] = "test";
    condition["negate"] = true;
    rule["conditions"].push_back(condition);
    rule["then"]["type"] = "set_value";
    rule["then"]["value"] = "not_test";
    params["rules"].push_back(rule);
    params["default_action"]["type"] = "set_value";
    params["default_action"]["value"] = "is_test";
    transform->configure(params);
    // Test with "test" (should be negated)
    std::string input = "test";
    auto result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("is_test", std::any_cast<std::string>(result));
    // Test with other value
    input = "other";
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("not_test", std::any_cast<std::string>(result));
}

// Test advanced conditional with between operator
TEST_F(ConditionalTransformationsTest, AdvancedConditionalBetweenOperator) {
    auto transform = registry_->create_transformation("advanced_conditional");
    YAML::Node params;
    YAML::Node rule;
    YAML::Node condition;
    condition["operator"] = "between";
    condition["value"] = "10,20";
    rule["conditions"].push_back(condition);
    rule["then"]["type"] = "set_value";
    rule["then"]["value"] = "in_range";
    params["rules"].push_back(rule);
    params["default_action"]["type"] = "set_value";
    params["default_action"]["value"] = "out_of_range";
    transform->configure(params);
    // Test value in range
    double input = 15.0;
    auto result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("in_range", std::any_cast<std::string>(result));
    // Test value out of range
    input = 25.0;
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("out_of_range", std::any_cast<std::string>(result));
}

// Test advanced conditional with in operator
TEST_F(ConditionalTransformationsTest, AdvancedConditionalInOperator) {
    auto transform = registry_->create_transformation("advanced_conditional");
    YAML::Node params;
    YAML::Node rule;
    YAML::Node condition;
    condition["operator"] = "in";
    condition["value"] = "apple,banana,orange";
    rule["conditions"].push_back(condition);
    rule["then"]["type"] = "set_value";
    rule["then"]["value"] = "fruit";
    params["rules"].push_back(rule);
    params["default_action"]["type"] = "set_value";
    params["default_action"]["value"] = "not_fruit";
    transform->configure(params);
    // Test value in list
    std::string input = "banana";
    auto result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("fruit", std::any_cast<std::string>(result));
    // Test value not in list
    input = "carrot";
    result = transform->transform(input, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ("not_fruit", std::any_cast<std::string>(result));
}

} // namespace omop::transform::test