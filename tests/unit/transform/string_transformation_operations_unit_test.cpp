// tests/unit/transform/string_transformations_test.cpp

#include <gtest/gtest.h>
#include "transform/string_transformations.h"
#include "transform/unified_string_transformation.h"
#include "transform/transformations.h"
#include "core/interfaces.h"
#include <memory>
#include <any>
#include <yaml-cpp/yaml.h>

namespace omop::transform::test {

class StringTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        registry_ = &TransformationRegistry::instance();
    }

    void TearDown() override {
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
    TransformationRegistry* registry_;
};

// Test string manipulation transformation uppercase
TEST_F(StringTransformationsTest, StringManipulationUppercase) {
    auto transform = registry_->create_transformation("string_manipulation");

    YAML::Node params;
    params["operation"] = "uppercase";
    transform->configure(params);

    std::string input = "hello world";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("HELLO WORLD", std::any_cast<std::string>(result.value));
}

// Test string manipulation transformation lowercase
TEST_F(StringTransformationsTest, StringManipulationLowercase) {
    auto transform = registry_->create_transformation("string_manipulation");

    YAML::Node params;
    params["operation"] = "lowercase";
    transform->configure(params);

    std::string input = "HELLO WORLD";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("hello world", std::any_cast<std::string>(result.value));
}

// Test string manipulation transformation title case
TEST_F(StringTransformationsTest, StringManipulationTitleCase) {
    auto transform = registry_->create_transformation("string_manipulation");

    YAML::Node params;
    params["operation"] = "titlecase";
    transform->configure(params);

    std::string input = "hello world example";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("Hello World Example", std::any_cast<std::string>(result.value));
}

// Test string manipulation transformation trim
TEST_F(StringTransformationsTest, StringManipulationTrim) {
    auto transform = registry_->create_transformation("string_manipulation");

    YAML::Node params;
    params["operation"] = "trim";
    transform->configure(params);

    std::string input = "  hello world  ";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("hello world", std::any_cast<std::string>(result.value));
}

// Test string manipulation transformation pad left
TEST_F(StringTransformationsTest, StringManipulationPadLeft) {
    auto transform = registry_->create_transformation("string_manipulation");

    YAML::Node params;
    params["operation"] = "pad_left";
    params["target_length"] = 10;
    params["pad_char"] = "0";
    transform->configure(params);

    std::string input = "123";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("0000000123", std::any_cast<std::string>(result.value));
}

// Test string manipulation transformation substring
TEST_F(StringTransformationsTest, StringManipulationSubstring) {
    auto transform = registry_->create_transformation("string_manipulation");

    YAML::Node params;
    params["operation"] = "substring";
    params["start_pos"] = 6;
    params["end_pos"] = 11;
    transform->configure(params);

    std::string input = "Hello World Example";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("World", std::any_cast<std::string>(result.value));
}

// Test string manipulation transformation replace
TEST_F(StringTransformationsTest, StringManipulationReplace) {
    auto transform = registry_->create_transformation("string_manipulation");

    YAML::Node params;
    params["operation"] = "replace";
    params["search_text"] = "World";
    params["replace_text"] = "Universe";
    transform->configure(params);

    std::string input = "Hello World, World!";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("Hello Universe, Universe!", std::any_cast<std::string>(result.value));
}

// Test string manipulation transformation remove non-alphanumeric
TEST_F(StringTransformationsTest, StringManipulationRemoveNonAlphanumeric) {
    auto transform = registry_->create_transformation("string_manipulation");

    YAML::Node params;
    params["operation"] = "remove_non_alphanumeric";
    params["preserve_spaces"] = false;
    transform->configure(params);

    std::string input = "Hello@World#123!";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("HelloWorld123", std::any_cast<std::string>(result.value));
}

// Test string manipulation transformation normalize whitespace
TEST_F(StringTransformationsTest, StringManipulationNormalizeWhitespace) {
    auto transform = registry_->create_transformation("string_manipulation");

    YAML::Node params;
    params["operation"] = "normalize_whitespace";
    transform->configure(params);

    std::string input = "  Hello   World   Example  ";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("Hello World Example", std::any_cast<std::string>(result.value));
}

// Test string manipulation with max length
TEST_F(StringTransformationsTest, StringManipulationMaxLength) {
    auto transform = registry_->create_transformation("string_manipulation");

    YAML::Node params;
    params["operation"] = "uppercase";
    params["max_length"] = 5;
    transform->configure(params);

    std::string input = "hello world";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("HELLO", std::any_cast<std::string>(result.value));
    EXPECT_FALSE(result.warnings.empty());
}

// Test string pattern extraction email
TEST_F(StringTransformationsTest, StringPatternExtractionEmail) {
    auto transform = registry_->create_transformation("string_pattern_extraction");

    YAML::Node params;
    params["pattern_type"] = "email";
    transform->configure(params);

    std::string input = "Contact <NAME_EMAIL> for more info";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("<EMAIL>", std::any_cast<std::string>(result.value));
}

// Test string pattern extraction phone
TEST_F(StringTransformationsTest, StringPatternExtractionPhone) {
    auto transform = registry_->create_transformation("string_pattern_extraction");

    YAML::Node params;
    params["pattern_type"] = "phone";
    transform->configure(params);

    std::string input = "Call us at (*************";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    // Should extract phone number pattern
    std::string extracted = std::any_cast<std::string>(result.value);
    EXPECT_FALSE(extracted.empty());
}

// Test string pattern extraction SSN
TEST_F(StringTransformationsTest, StringPatternExtractionSSN) {
    auto transform = registry_->create_transformation("string_pattern_extraction");

    YAML::Node params;
    params["pattern_type"] = "ssn";
    transform->configure(params);

    std::string input = "SSN: ***********";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("***********", std::any_cast<std::string>(result.value));
}

// Test string pattern extraction custom pattern
TEST_F(StringTransformationsTest, StringPatternExtractionCustom) {
    auto transform = registry_->create_transformation("string_pattern_extraction");

    YAML::Node params;
    params["pattern_type"] = "custom";
    params["pattern"] = R"([A-Z]{2}\d{4})";
    transform->configure(params);

    std::string input = "Order ID: AB1234";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("AB1234", std::any_cast<std::string>(result.value));
}

// Test string pattern extraction with capture group
TEST_F(StringTransformationsTest, StringPatternExtractionCaptureGroup) {
    auto transform = registry_->create_transformation("string_pattern_extraction");

    YAML::Node params;
    params["pattern_type"] = "custom";
    params["pattern"] = R"(ID:\s*([A-Z0-9]+))";
    params["capture_group"] = 1;
    transform->configure(params);

    std::string input = "Order ID: ABC123";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("ABC123", std::any_cast<std::string>(result.value));
}

// Test string pattern extraction extract all
TEST_F(StringTransformationsTest, StringPatternExtractionExtractAll) {
    auto transform = registry_->create_transformation("string_pattern_extraction");

    YAML::Node params;
    params["pattern_type"] = "number";
    params["extract_all"] = true;
    params["separator"] = ", ";
    transform->configure(params);

    std::string input = "Values: 123, 456, and 789";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("123, 456, 789", std::any_cast<std::string>(result.value));
}

// Test string pattern extraction no match with default
TEST_F(StringTransformationsTest, StringPatternExtractionNoMatchDefault) {
    auto transform = registry_->create_transformation("string_pattern_extraction");

    YAML::Node params;
    params["pattern_type"] = "email";
    params["default_value"] = "<EMAIL>";
    params["return_default_on_no_match"] = true;
    transform->configure(params);

    std::string input = "No email here";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("<EMAIL>", std::any_cast<std::string>(result.value));
}

// Test string pattern extraction with invalid input
TEST_F(StringTransformationsTest, StringPatternExtractionInvalidInput) {
    auto transform = registry_->create_transformation("string_pattern_extraction");

    YAML::Node params;
    params["pattern_type"] = "email";
    transform->configure(params);

    int input = 123;
    auto result = transform->transform_safe(input, *context_);
    EXPECT_FALSE(result.is_success());
}

// Test string manipulation with const char* input
TEST_F(StringTransformationsTest, StringManipulationConstCharInput) {
    auto transform = registry_->create_transformation("string_manipulation");

    YAML::Node params;
    params["operation"] = "uppercase";
    transform->configure(params);

    const char* input = "hello";
    auto result = transform->transform_safe(input, *context_);

    ASSERT_TRUE(result.is_success());
    EXPECT_EQ("HELLO", std::any_cast<std::string>(result.value));
}

// Test UK postal code validation and formatting
TEST_F(StringTransformationsTest, UKPostalCodeValidation) {
    auto transform = registry_->create_transformation("string_transform");

    YAML::Node params;
    params["operation"] = "format";
    params["pattern"] = "[A-Z]{1,2}[0-9]{1,2}[A-Z]? [0-9][A-Z]{2}";  // UK postcode pattern
    transform->configure(params);

    // Test various UK postal codes
    std::vector<std::pair<std::string, bool>> uk_postcodes = {
        {"SW1A 1AA", true},    // Buckingham Palace
        {"M1 1AA", true},      // Manchester
        {"B33 8TH", true},     // Birmingham
        {"W1A 0AX", true},     // Oxford Street
        {"EC1A 1BB", true},    // City of London
        {"G1 1XQ", true},      // Glasgow
        {"INVALID", false},    // Invalid format
        {"12345", false},      // US-style zip code
        {"", false}            // Empty string
    };

    for (const auto& [postcode, should_be_valid] : uk_postcodes) {
        auto result = transform->transform_safe(postcode, *context_);
        if (should_be_valid) {
            EXPECT_TRUE(result.is_success()) << "Valid UK postcode should pass: " << postcode;
        } else {
            // For invalid postcodes, we expect either failure or empty result
            if (result.is_success()) {
                auto value = std::any_cast<std::string>(result.value);
                EXPECT_TRUE(value.empty()) << "Invalid UK postcode should result in empty string: " << postcode;
            }
        }
    }
}

// Test NHS number validation (UK-specific healthcare identifier)
TEST_F(StringTransformationsTest, NHSNumberValidation) {
    auto transform = registry_->create_transformation("string_transform");

    YAML::Node params;
    params["operation"] = "validate";
    params["validation_type"] = "nhs_number";
    transform->configure(params);

    // Test NHS numbers (10 digits with Modulus 11 check)
    std::vector<std::pair<std::string, bool>> nhs_numbers = {
        {"**********", true},   // Valid NHS number
        {"**********", false},  // Invalid check digit
        {"123456789", false},   // Too short
        {"12345678901", false}, // Too long
        {"abcdefghij", false},  // Non-numeric
        {"", false}             // Empty
    };

    for (const auto& [nhs_number, should_be_valid] : nhs_numbers) {
        auto result = transform->transform_safe(nhs_number, *context_);
        if (should_be_valid) {
            EXPECT_TRUE(result.is_success()) << "Valid NHS number should pass: " << nhs_number;
            EXPECT_FALSE(std::any_cast<std::string>(result.value).empty()) 
                << "Valid NHS number should return non-empty result: " << nhs_number;
        } else {
            // For invalid NHS numbers, we expect either failure or empty result
            if (result.is_success()) {
                auto value = std::any_cast<std::string>(result.value);
                EXPECT_TRUE(value.empty()) << "Invalid NHS number should result in empty string: " << nhs_number;
            }
        }
    }
}

// Test direct usage of UnifiedStringTransformation::transform_detailed method
TEST_F(StringTransformationsTest, DirectUnifiedStringTransformUsage) {
    // Create UnifiedStringTransformation directly to explicitly test transform_detailed
    auto unified_transform = std::make_unique<UnifiedStringTransformation>();
    
    // Test NHS validation
    YAML::Node nhs_params;
    nhs_params["operation"] = "validate";
    nhs_params["validation_type"] = "nhs_number";
    unified_transform->configure(nhs_params);
    
    // Direct call to transform_detailed method
    auto result = unified_transform->transform_detailed(std::string("**********"), *context_);
    ASSERT_TRUE(result.is_success());
    EXPECT_FALSE(std::any_cast<std::string>(result.value).empty());
    EXPECT_TRUE(result.metadata.find("validation_type") != result.metadata.end());
    
    // Test UK postcode validation with direct transform_detailed call
    auto postcode_transform = std::make_unique<UnifiedStringTransformation>();
    YAML::Node postcode_params;
    postcode_params["operation"] = "validate";
    postcode_params["validation_type"] = "uk_postcode";
    postcode_params["return_formatted"] = true;
    postcode_transform->configure(postcode_params);
    
    auto postcode_result = postcode_transform->transform_detailed(std::string("W1A0AX"), *context_);
    ASSERT_TRUE(postcode_result.is_success());
    EXPECT_EQ(std::any_cast<std::string>(postcode_result.value), "W1A 0AX"); // Should add space
    
    // Test pattern validation with direct transform_detailed call
    auto pattern_transform = std::make_unique<UnifiedStringTransformation>();
    YAML::Node pattern_params;
    pattern_params["operation"] = "format";
    pattern_params["pattern"] = "[A-Z]{2}[0-9]{3}";
    pattern_transform->configure(pattern_params);
    
    auto pattern_result = pattern_transform->transform_detailed(std::string("AB123"), *context_);
    ASSERT_TRUE(pattern_result.is_success());
    EXPECT_TRUE(pattern_result.metadata.find("pattern_matched") != pattern_result.metadata.end());
    EXPECT_TRUE(std::any_cast<bool>(pattern_result.metadata.at("pattern_matched")));
}

} // namespace omop::transform::test