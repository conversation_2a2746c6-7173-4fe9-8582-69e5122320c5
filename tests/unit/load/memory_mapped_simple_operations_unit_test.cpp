// Simple test to debug MmapBatchLoader segfault
#include <gtest/gtest.h>
#include <memory>
#include <iostream>
#include "load/batch_loader.h"

using namespace omop::load;

TEST(MmapSimpleTest, BasicCreation) {
    std::cout << "Starting basic creation test" << std::endl;
    
    try {
        std::cout << "Creating BatchLoaderOptions..." << std::endl;
        BatchLoaderOptions options;
        options.batch_size = 10;
        options.parallel_processing = false;
        options.worker_threads = 1;
        std::cout << "Options created" << std::endl;
        
        std::cout << "Creating MmapBatchLoader..." << std::endl;
        auto loader = std::make_unique<MmapBatchLoader>(options, 1024);
        std::cout << "MmapBatchLoader created" << std::endl;
        
        std::cout << "Getting type..." << std::endl;
        std::string type = loader->get_type();
        std::cout << "Type: " << type << std::endl;
        
        EXPECT_EQ(type, "mmap_batch");
        
        std::cout << "Test completed successfully" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception caught: " << e.what() << std::endl;
        FAIL() << "Exception: " << e.what();
    }
}