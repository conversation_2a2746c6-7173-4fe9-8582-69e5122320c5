/**
 * @file platform_utils_test.cpp
 * @brief Unit tests for platform-specific utility functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <filesystem>
#include <fstream>
#include <thread>

#ifdef _WIN32
#include "extract/platform/windows_utils.h"
#else
#include "extract/platform/unix_utils.h"
#include <sys/mman.h>  // For MADV_* constants
#endif

using namespace omop::extract::platform;
namespace fs = std::filesystem;

// Test fixture for platform utilities
class PlatformUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for tests
        test_dir_ = fs::temp_directory_path() / "omop_platform_test";
        fs::create_directories(test_dir_);
    }

    void TearDown() override {
        // Clean up test directory
        fs::remove_all(test_dir_);
    }

    // Helper to create test file
    std::string createTestFile(const std::string& name, const std::string& content) {
        fs::path filepath = test_dir_ / name;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    fs::path test_dir_;
};

// ============================================================================
// Common Platform Tests
// ============================================================================

// Tests common file operations
TEST_F(PlatformUtilsTest, CommonFileOperations) {
    std::string test_file = createTestFile("common_test.txt", "test content");
    
    // Test file existence
    EXPECT_TRUE(fs::exists(test_file));
    
    // Test file size
    size_t size = get_file_size(test_file);
    EXPECT_EQ(12, size); // "test content" length
}

// Tests cross platform memory info
TEST_F(PlatformUtilsTest, CrossPlatformMemoryInfo) {
    auto mem_info = get_memory_info();
    
    EXPECT_GT(mem_info.total_physical, 0);
    EXPECT_GT(mem_info.available_physical, 0);
    
    // Available should be less than or equal to total
    EXPECT_LE(mem_info.available_physical, mem_info.total_physical);
}

// Tests cross platform timer accuracy
TEST_F(PlatformUtilsTest, CrossPlatformTimerAccuracy) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Sleep for a known duration
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Should be at least 100ms (allowing for some overhead)
    EXPECT_GE(elapsed.count(), 95); // 95ms minimum
    // Should not be too much more than 100ms
    EXPECT_LE(elapsed.count(), 150); // 150ms maximum
}

// Tests file size retrieval
TEST_F(PlatformUtilsTest, GetFileSize) {
    std::string content = "This is a test file with known size.";
    std::string test_file = createTestFile("size_test.txt", content);

    size_t size = get_file_size(test_file);
    EXPECT_EQ(content.length(), size);

    // Test non-existent file
    EXPECT_THROW(get_file_size("nonexistent.txt"), std::exception);
}

// Tests get file modification time
TEST_F(PlatformUtilsTest, GetFileMTime) {
    std::string test_file = createTestFile("mtime_test.txt", "test");
    
    auto mtime = get_file_mtime(test_file);
    EXPECT_GT(mtime, 0);
    
    // Should be recent (within last hour)
    auto now = std::chrono::system_clock::now();
    auto now_seconds = std::chrono::duration_cast<std::chrono::seconds>(
        now.time_since_epoch()).count();
    EXPECT_LE(now_seconds - mtime, 3600); // Within last hour
}

// Tests memory mapped file
TEST_F(PlatformUtilsTest, MemoryMappedFile) {
    std::string content = "test content for memory mapping";
    std::string test_file = createTestFile("mmap_test.txt", content);
    
    // Use the correct MemoryMappedFile interface
    MemoryMappedFile mmf1;
    EXPECT_TRUE(mmf1.map_file(test_file, true)); // Read-only mode
    EXPECT_TRUE(mmf1.is_mapped());
    EXPECT_EQ(content.length(), mmf1.size()); // Actual content length
    
    // Test move semantics
    MemoryMappedFile mmf2(std::move(mmf1));
    EXPECT_FALSE(mmf1.is_mapped());
    EXPECT_TRUE(mmf2.is_mapped());
    EXPECT_EQ(content.length(), mmf2.size());
}

// Tests symbolic links
TEST_F(PlatformUtilsTest, SymbolicLinks) {
    std::string target_file = createTestFile("target.txt", "target content");
    std::string link_path = (test_dir_ / "link.txt").string();
    
    // Create symbolic link using system call since create_symbolic_link doesn't exist
    EXPECT_EQ(symlink(target_file.c_str(), link_path.c_str()), 0);
    
    // Test link exists
    EXPECT_TRUE(fs::exists(link_path));
    EXPECT_TRUE(is_symbolic_link(link_path));
    
    // Test reading through link using std::ifstream since read_file_content doesn't exist
    std::ifstream link_file(link_path);
    std::string link_content((std::istreambuf_iterator<char>(link_file)),
                            std::istreambuf_iterator<char>());
    EXPECT_EQ("target content", link_content);
}

// Tests get real path
TEST_F(PlatformUtilsTest, GetRealPath) {
    std::string test_file = createTestFile("realpath_test.txt", "test");
    
    auto real_path = get_real_path(test_file);
    EXPECT_FALSE(real_path.empty());
    EXPECT_TRUE(fs::exists(real_path));
    
    // Should be absolute path
    EXPECT_TRUE(fs::path(real_path).is_absolute());
}

#ifdef _WIN32
// ============================================================================
// Windows-Specific Tests
// ============================================================================

// Tests available drives enumeration
TEST(WindowsUtilsTest, AvailableDrives) {
    std::vector<char> drives = get_available_drives();

    // Should have at least C: drive on Windows
    EXPECT_FALSE(drives.empty());
    bool has_c_drive = std::find(drives.begin(), drives.end(), 'C') != drives.end();
    EXPECT_TRUE(has_c_drive);
}

// Tests create temp file
TEST(WindowsUtilsTest, CreateTempFile) {
    std::string temp_file = create_temp_file("test_", ".tmp");
    EXPECT_FALSE(temp_file.empty());
    EXPECT_TRUE(fs::exists(temp_file));

    // Clean up
    fs::remove(temp_file);

    // Test with default parameters
    temp_file = create_temp_file();
    EXPECT_FALSE(temp_file.empty());
    EXPECT_TRUE(temp_file.find("omop_") != std::string::npos);
    EXPECT_TRUE(temp_file.find(".tmp") != std::string::npos);

    // Clean up
    fs::remove(temp_file);
}

// Tests error message retrieval
TEST(WindowsUtilsTest, ErrorMessage) {
    // Test with specific error code
    std::string error_msg = get_windows_error_message(ERROR_FILE_NOT_FOUND);
    EXPECT_FALSE(error_msg.empty());
    EXPECT_NE(error_msg.find("not found"), std::string::npos);

    // Test with current last error (0 means use GetLastError())
    SetLastError(ERROR_ACCESS_DENIED);
    error_msg = get_windows_error_message(0);
    EXPECT_FALSE(error_msg.empty());
}

// Tests file attributes
TEST_F(PlatformUtilsTest, FileAttributes) {
    std::string test_file = createTestFile("attr_test.txt", "test");

    // Get current attributes
    DWORD attrs = get_file_attributes(test_file);
    EXPECT_NE(INVALID_FILE_ATTRIBUTES, attrs);

    // Set hidden attribute
    EXPECT_TRUE(set_file_attributes(test_file, attrs | FILE_ATTRIBUTE_HIDDEN));

    // Verify attribute was set
    DWORD new_attrs = get_file_attributes(test_file);
    EXPECT_TRUE(new_attrs & FILE_ATTRIBUTE_HIDDEN);
}

// Tests file handle wrapper
TEST_F(PlatformUtilsTest, WindowsFileHandle) {
    std::string test_file = createTestFile("test.txt", "test content");
    std::wstring wide_path = utf8_to_wide(test_file);

    {
        WindowsFileHandle handle(CreateFileW(
            wide_path.c_str(),
            GENERIC_READ,
            FILE_SHARE_READ,
            nullptr,
            OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL,
            nullptr));

        EXPECT_TRUE(handle.is_valid());
        EXPECT_NE(INVALID_HANDLE_VALUE, handle.get());
    }
    // Handle should be closed after scope

    // Test move semantics
    WindowsFileHandle handle1(CreateFileW(
        wide_path.c_str(),
        GENERIC_READ,
        FILE_SHARE_READ,
        nullptr,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        nullptr));

    WindowsFileHandle handle2(std::move(handle1));
    EXPECT_FALSE(handle1.is_valid());
    EXPECT_TRUE(handle2.is_valid());
}

// Tests file mapping
TEST_F(PlatformUtilsTest, FileMapping) {
    std::string test_file = createTestFile("mapping_test.txt", "test content for file mapping");
    
    WindowsFileHandle file_handle(CreateFileW(
        utf8_to_wide(test_file).c_str(),
        GENERIC_READ,
        FILE_SHARE_READ,
        nullptr,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        nullptr));
    
    EXPECT_TRUE(file_handle.is_valid());
    
    // Test file mapping
    WindowsFileMapping mapping(file_handle.get(), PAGE_READONLY, 0, 0);
    EXPECT_TRUE(mapping.is_valid());
    EXPECT_EQ(35, mapping.size()); // "test content for file mapping" length
}

// Tests high resolution timer
TEST(WindowsUtilsTest, HighResTimer) {
    WindowsHighResTimer timer;

    // Sleep for a known duration
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto elapsed = timer.elapsed();
    EXPECT_GE(elapsed, 0.095); // At least 95ms
    EXPECT_LE(elapsed, 0.150); // No more than 150ms
}

// Tests memory info
TEST(WindowsUtilsTest, MemoryInfo) {
    auto mem_info = get_windows_memory_info();
    
    EXPECT_GT(mem_info.total_physical, 0);
    EXPECT_GT(mem_info.available_physical, 0);
    EXPECT_GT(mem_info.total_virtual, 0);
    EXPECT_GT(mem_info.available_virtual, 0);
    
    // Available should be less than or equal to total
    EXPECT_LE(mem_info.available_physical, mem_info.total_physical);
    EXPECT_LE(mem_info.available_virtual, mem_info.total_virtual);
}

// Tests network path detection
TEST(WindowsUtilsTest, NetworkPath) {
    // UNC paths
    EXPECT_TRUE(is_network_path("\\\\server\\share\\file.txt"));
    EXPECT_TRUE(is_network_path("\\\\192.168.1.1\\folder"));

    // Local paths
    EXPECT_FALSE(is_network_path("C:\\Windows\\System32"));
    EXPECT_FALSE(is_network_path("D:\\Data\\file.txt"));

    // Relative paths
    EXPECT_FALSE(is_network_path("relative\\path.txt"));
}

// Tests process priority
TEST(WindowsUtilsTest, ProcessPriority) {
    // Test getting current priority
    int priority = get_process_priority();
    EXPECT_GE(priority, 0);
    EXPECT_LE(priority, 5);
    
    // Test setting priority
    EXPECT_TRUE(set_process_priority(PROCESS_PRIORITY_CLASS_NORMAL));
}

// Tests string conversion utilities
TEST(WindowsUtilsTest, StringConversion) {
    // Test UTF-8 to wide string conversion
    std::string utf8_str = "Test String éàü";
    std::wstring wide_str = utf8_to_wide(utf8_str);
    EXPECT_FALSE(wide_str.empty());

    // Test wide to UTF-8 conversion
    std::string converted_back = wide_to_utf8(wide_str);
    EXPECT_EQ(utf8_str, converted_back);

    // Test empty string
    EXPECT_TRUE(utf8_to_wide("").empty());
    EXPECT_TRUE(wide_to_utf8(L"").empty());
}

// Tests temporary directory
TEST(WindowsUtilsTest, TempDirectory) {
    std::string temp_dir = get_temp_directory();
    EXPECT_FALSE(temp_dir.empty());
    EXPECT_TRUE(fs::exists(temp_dir));
    EXPECT_TRUE(fs::is_directory(temp_dir));
}

#else
// ============================================================================
// Unix-Specific Tests
// ============================================================================

// Tests CPU count
TEST(UnixUtilsTest, CpuCount) {
    int cpu_count = get_cpu_count();
    EXPECT_GT(cpu_count, 0);
    EXPECT_LE(cpu_count, 1024); // Reasonable upper limit
}

// Tests Unix create temp file
TEST(UnixUtilsTest, CreateTempFile) {
    std::string temp_file = create_temp_file("test_", ".tmp");
    EXPECT_FALSE(temp_file.empty());
    EXPECT_TRUE(fs::exists(temp_file));

    // Clean up
    fs::remove(temp_file);

    // Test with default parameters
    temp_file = create_temp_file();
    EXPECT_FALSE(temp_file.empty());
    EXPECT_TRUE(temp_file.find("omop_") != std::string::npos);
    EXPECT_TRUE(temp_file.find(".tmp") != std::string::npos);

    // Clean up
    fs::remove(temp_file);
}

// Tests Unix file permissions
TEST_F(PlatformUtilsTest, FilePermissions) {
    std::string test_file = createTestFile("perm_test.txt", "test");
    
    // Set permissions
    EXPECT_TRUE(set_file_permissions(test_file, 0644));
    
    // Get permissions
    mode_t perms = get_file_permissions(test_file);
    EXPECT_EQ(perms & 0777, 0644);
}

// Tests Unix high resolution timer
TEST(UnixUtilsTest, HighResTimer) {
    UnixHighResTimer timer;
    
    // Sleep for a known duration
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Use the correct method name
    auto elapsed = timer.elapsed_seconds();
    
    // Should be at least 0.1 seconds
    EXPECT_GE(elapsed, 0.095);
    
    // Reset and test again
    timer.reset();
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    auto reset_elapsed = timer.elapsed_seconds();
    EXPECT_GE(reset_elapsed, 0.045);
}

// Tests memory advice
TEST(UnixUtilsTest, MemoryAdvice) {
    void* ptr = malloc(4096);
    ASSERT_NE(ptr, nullptr);
    
    // Use the correct function name
    EXPECT_NO_THROW(advise_memory_usage(ptr, 4096, MADV_NORMAL));
    EXPECT_NO_THROW(advise_memory_usage(ptr, 4096, MADV_RANDOM));
    EXPECT_NO_THROW(advise_memory_usage(ptr, 4096, MADV_SEQUENTIAL));
    
    free(ptr);
}

// Tests memory info
TEST(UnixUtilsTest, MemoryInfo) {
    // Use the correct function name
    auto mem_info = get_memory_info();
    
    EXPECT_GT(mem_info.total_physical, 0);
    EXPECT_GT(mem_info.available_physical, 0);
    EXPECT_LE(mem_info.available_physical, mem_info.total_physical);
}

// Tests memory locking
TEST(UnixUtilsTest, MemoryLocking) {
    void* ptr = malloc(4096);
    ASSERT_NE(ptr, nullptr);
    
    // Test memory locking
    EXPECT_TRUE(lock_memory(ptr, 4096));
    EXPECT_TRUE(unlock_memory(ptr, 4096));
    
    free(ptr);
}

// Tests mounted filesystems
TEST(UnixUtilsTest, MountedFilesystems) {
    auto filesystems = get_mounted_filesystems();
    
    // Should have at least root filesystem
    EXPECT_FALSE(filesystems.empty());
    
    // Check for root filesystem
    bool has_root = false;
    for (const auto& fs : filesystems) {
        if (fs == "/") {
            has_root = true;
            break;
        }
    }
    EXPECT_TRUE(has_root);
}

// Tests network path detection
TEST(UnixUtilsTest, NetworkPath) {
    // Most Unix systems may not detect NFS paths as network paths without actual mounts
    // Test with known local paths instead
    EXPECT_FALSE(is_network_path("/home/<USER>/file.txt"));
    EXPECT_FALSE(is_network_path("/tmp/test.txt"));
    
    // Relative paths
    EXPECT_FALSE(is_network_path("relative/path.txt"));
}

// Tests process priority
TEST(UnixUtilsTest, ProcessPriority) {
    // Test getting current priority
    int priority = get_process_priority();
    EXPECT_GE(priority, -20); // Nice range
    EXPECT_LE(priority, 19);
    
    // Test setting priority
    EXPECT_TRUE(set_process_priority(0)); // Normal priority
}

// Tests system error message
TEST(UnixUtilsTest, SystemErrorMessage) {
    // Test with specific error code
    std::string error_msg = get_system_error_message(ENOENT);
    EXPECT_FALSE(error_msg.empty());
    // Error messages may vary, just check it's not empty
    
    // Test with current errno
    errno = EACCES;
    error_msg = get_system_error_message(0);
    EXPECT_FALSE(error_msg.empty());
}

// Tests temporary directory
TEST(UnixUtilsTest, TempDirectory) {
    std::string temp_dir = get_temp_directory();
    EXPECT_FALSE(temp_dir.empty());
    EXPECT_TRUE(fs::exists(temp_dir));
    EXPECT_TRUE(fs::is_directory(temp_dir));
    
    // Should be writable
    std::string test_file = temp_dir + "/test_write.txt";
    std::ofstream file(test_file);
    EXPECT_TRUE(file.is_open());
    file.close();
    fs::remove(test_file);
}

// Tests Unix file descriptor
TEST_F(PlatformUtilsTest, UnixFileDescriptor) {
    std::string test_file = createTestFile("fd_test.txt", "test content");
    
    UnixFileDescriptor fd1(open(test_file.c_str(), O_RDONLY));
    EXPECT_TRUE(fd1.is_valid());
    EXPECT_GE(fd1.get(), 0);
    
    // Test move semantics
    UnixFileDescriptor fd2(std::move(fd1));
    EXPECT_FALSE(fd1.is_valid());
    EXPECT_TRUE(fd2.is_valid());
}

#endif // _WIN32