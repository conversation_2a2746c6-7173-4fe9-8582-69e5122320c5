/**
 * @file postgresql_connector_test.cpp
 * @brief Unit tests for PostgreSQL connector functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#ifdef OMOP_HAS_POSTGRESQL

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/postgresql_connector.h"
#include "common/exceptions.h"
#include <cstring>
#include <memory>
#include <atomic>

using namespace omop::extract;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::StrEq;

// Mock PGresult for testing
struct MockPGresult {
    int ntuples;
    int nfields;
    std::vector<std::string> field_names;
    std::vector<Oid> field_types;
    std::vector<std::vector<std::string>> data;
    std::vector<std::vector<bool>> is_null;
};

// Mock libpq functions
static MockPGresult* current_mock_result = nullptr;
static PGconn* mock_connection = reinterpret_cast<PGconn*>(0x12345678);
static ConnStatusType mock_connection_status = CONNECTION_OK;
static std::string mock_error_message = "";
static std::unique_ptr<MockPGresult> mock_result_storage;

// Override libpq functions for testing
extern "C" {
    PGconn* PQconnectdb(const char*) { return mock_connection; }
    void PQfinish(PGconn*) {}
    ConnStatusType PQstatus(const PGconn*) { return mock_connection_status; }
    char* PQerrorMessage(const PGconn*) { return const_cast<char*>(mock_error_message.c_str()); }
    int PQsetClientEncoding(PGconn*, const char*) { return 0; }

    PGresult* PQexec(PGconn*, const char*) {
        if (current_mock_result) {
            // Transfer ownership to avoid double delete
            mock_result_storage.reset(current_mock_result);
            current_mock_result = mock_result_storage.get();
            return reinterpret_cast<PGresult*>(current_mock_result);
        }
        return nullptr;
    }

    PGresult* PQprepare(PGconn*, const char*, const char*, int, const Oid*) {
        return reinterpret_cast<PGresult*>(current_mock_result);
    }

    PGresult* PQexecPrepared(PGconn*, const char*, int, const char* const*,
                            const int*, const int*, int) {
        return reinterpret_cast<PGresult*>(current_mock_result);
    }

    void PQclear(PGresult*) {}

    ExecStatusType PQresultStatus(const PGresult*) {
        return current_mock_result ? PGRES_TUPLES_OK : PGRES_FATAL_ERROR;
    }

    int PQntuples(const PGresult*) {
        return current_mock_result ? current_mock_result->ntuples : 0;
    }

    int PQnfields(const PGresult*) {
        return current_mock_result ? current_mock_result->nfields : 0;
    }

    char* PQfname(const PGresult*, int field_num) {
        if (current_mock_result && field_num < current_mock_result->nfields) {
            return const_cast<char*>(current_mock_result->field_names[field_num].c_str());
        }
        return nullptr;
    }

    Oid PQftype(const PGresult*, int field_num) {
        if (current_mock_result && field_num < current_mock_result->nfields) {
            return current_mock_result->field_types[field_num];
        }
        return 0;
    }

    int PQfnumber(const PGresult*, const char* field_name) {
        if (!current_mock_result) return -1;
        for (int i = 0; i < current_mock_result->nfields; ++i) {
            if (current_mock_result->field_names[i] == field_name) {
                return i;
            }
        }
        return -1;
    }

    char* PQgetvalue(const PGresult*, int tup_num, int field_num) {
        if (current_mock_result &&
            tup_num < current_mock_result->ntuples &&
            field_num < current_mock_result->nfields) {
            return const_cast<char*>(current_mock_result->data[tup_num][field_num].c_str());
        }
        return nullptr;
    }

    int PQgetisnull(const PGresult*, int tup_num, int field_num) {
        if (current_mock_result &&
            tup_num < current_mock_result->ntuples &&
            field_num < current_mock_result->nfields) {
            return current_mock_result->is_null[tup_num][field_num] ? 1 : 0;
        }
        return 1;
    }

    char* PQcmdTuples(PGresult*) {
        static char buffer[32];
        snprintf(buffer, sizeof(buffer), "%d", current_mock_result ? current_mock_result->ntuples : 0);
        return buffer;
    }
}

// Test fixture for PostgreSQL tests
class PostgreSQLTest : public ::testing::Test {
protected:
    void SetUp() override {
        current_mock_result = nullptr;
        mock_connection_status = CONNECTION_OK;
        mock_error_message = "";
        // Initialize statement counter for unique statement names
        statement_counter_ = 0;
    }

    void TearDown() override {
        // Safe cleanup of mock result
        if (current_mock_result) {
            delete current_mock_result;
        }
        current_mock_result = nullptr;
        
        // Reset all mock state
        mock_connection_status = CONNECTION_BAD;
        mock_error_message.clear();
    }

private:
    static std::atomic<int> statement_counter_;
    
public:
    MockPGresult* createMockResult(int rows, int cols) {
        // Ensure any existing result is cleaned up first
        if (current_mock_result) {
            delete current_mock_result;
            current_mock_result = nullptr;
        }
        
        auto result = new MockPGresult;
        result->ntuples = rows;
        result->nfields = cols;
        result->data.resize(rows);
        result->is_null.resize(rows);
        for (int i = 0; i < rows; ++i) {
            result->data[i].resize(cols);
            result->is_null[i].resize(cols, false);
        }
        current_mock_result = result;
        return result;
    }
};

// RAII helper for mock results
class MockResultGuard {
public:
    MockResultGuard(MockPGresult* result) {
        current_mock_result = result;
    }
    
    ~MockResultGuard() {
        mock_result_storage.reset();
        current_mock_result = nullptr;
    }
    
    MockResultGuard(const MockResultGuard&) = delete;
    MockResultGuard& operator=(const MockResultGuard&) = delete;
};

// ============================================================================
// PostgreSQLTest Tests (Alphabetically Ordered)
// ============================================================================

// Tests connect failure
TEST_F(PostgreSQLTest, ConnectFailure) {
    mock_connection_status = CONNECTION_BAD;
    mock_error_message = "Connection refused";

    PostgreSQLConnection conn;

    IDatabaseConnection::ConnectionParams params;
    params.host = "invalid_host";
    params.database = "test_db";

    EXPECT_THROW(conn.connect(params), DatabaseException);
}

// Tests connect success
TEST_F(PostgreSQLTest, ConnectSuccess) {
    PostgreSQLConnection conn;

    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.port = 5432;
    params.database = "test_db";
    params.username = "test_user";
    params.password = "test_pass";

    ASSERT_NO_THROW(conn.connect(params));
    EXPECT_TRUE(conn.is_connected());
    EXPECT_EQ("PostgreSQL", conn.get_database_type());
}

// Tests connection retry logic
TEST_F(PostgreSQLTest, ConnectionRetryLogic) {
    // Connection will fail first 2 times, succeed on 3rd
    static int attempt_count = 0;
    
    class RetryableConnection : public PostgreSQLConnection {
    public:
        void connect(const ConnectionParams& params) override {
            attempt_count++;
            if (attempt_count < 3) {
                mock_connection_status = CONNECTION_BAD;
                mock_error_message = "Temporary network failure";
                PostgreSQLConnection::connect(params);
            } else {
                mock_connection_status = CONNECTION_OK;
                mock_error_message = "";
                PostgreSQLConnection::connect(params);
            }
        }
    };
    
    ConnectionRetryPolicy::Config retry_config;
    retry_config.max_attempts = 3;
    retry_config.initial_delay = std::chrono::milliseconds(10);
    retry_config.backoff_multiplier = 2.0;
    retry_config.add_jitter = false;
    
    ConnectionRetryPolicy retry_policy(retry_config);
    auto logger = omop::common::Logger::get("test");
    
    attempt_count = 0;
    bool connected = retry_policy.execute_with_retry(
        [&]() {
            RetryableConnection conn;
            IDatabaseConnection::ConnectionParams params;
            params.host = "localhost";
            params.database = "test_db";
            conn.connect(params);
        },
        logger
    );
    
    EXPECT_TRUE(connected);
    EXPECT_EQ(3, attempt_count);
}

// Tests connection string builder
TEST_F(PostgreSQLTest, ConnectionStringBuilder) {
    PostgreSQLConnection conn;

    IDatabaseConnection::ConnectionParams params;
    params.host = "db.example.com";
    params.port = 5433;
    params.database = "mydb";
    params.username = "user";
    params.password = "pass";
    params.options["sslmode"] = "require";
    params.options["connect_timeout"] = "10";

    // Connection string should contain all parameters
    ASSERT_NO_THROW(conn.connect(params));
}

// Tests execute query
TEST_F(PostgreSQLTest, ExecuteQuery) {
    auto result = createMockResult(2, 3);
    MockResultGuard guard(result);
    current_mock_result->field_names = {"id", "name", "value"};
    current_mock_result->field_types = {23, 25, 701}; // INT4, TEXT, FLOAT8
    current_mock_result->data[0] = {"1", "John", "100.5"};
    current_mock_result->data[1] = {"2", "Jane", "200.75"};

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto result_ptr = conn.execute_query("SELECT id, name, value FROM test_table");
    ASSERT_NE(nullptr, result_ptr);

    // Check first row
    ASSERT_TRUE(result_ptr->next());
    EXPECT_EQ(1, std::any_cast<int>(result_ptr->get_value(0)));
    EXPECT_EQ("John", std::any_cast<std::string>(result_ptr->get_value(1)));
    EXPECT_DOUBLE_EQ(100.5, std::any_cast<double>(result_ptr->get_value(2)));

    // Check second row
    ASSERT_TRUE(result_ptr->next());
    EXPECT_EQ(2, std::any_cast<int>(result_ptr->get_value(0)));
    EXPECT_EQ("Jane", std::any_cast<std::string>(result_ptr->get_value(1)));

    // No more rows
    EXPECT_FALSE(result_ptr->next());
}

// Tests get version
TEST_F(PostgreSQLTest, GetVersion) {
    current_mock_result = createMockResult(1, 1);
    current_mock_result->field_names = {"version"};
    current_mock_result->field_types = {25}; // TEXT
    current_mock_result->data[0] = {"PostgreSQL 14.5"};

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    EXPECT_EQ("PostgreSQL 14.5", conn.get_version());
}

// Tests invalid column access
TEST_F(PostgreSQLTest, InvalidColumnAccess) {
    current_mock_result = createMockResult(1, 2);
    current_mock_result->field_names = {"col1", "col2"};
    current_mock_result->field_types = {23, 23};
    current_mock_result->data[0] = {"1", "2"};

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto result = conn.execute_query("SELECT * FROM test");
    ASSERT_TRUE(result->next());

    // Test invalid index
    EXPECT_THROW(result->get_value(5), DatabaseException);
    EXPECT_THROW(result->column_name(5), DatabaseException);

    // Test invalid column name
    EXPECT_THROW(result->get_value("nonexistent"), DatabaseException);
}

// Tests network failure scenarios
TEST_F(PostgreSQLTest, NetworkFailureScenarios) {
    // Simulate various network failures
    std::vector<std::pair<ConnStatusType, std::string>> failure_scenarios = {
        {CONNECTION_BAD, "Network unreachable"},
        {CONNECTION_BAD, "Connection timed out"},
        {CONNECTION_BAD, "Connection refused"},
        {CONNECTION_BAD, "Host not found"},
        {CONNECTION_BAD, "SSL connection failed"}
    };
    
    for (const auto& [status, error] : failure_scenarios) {
        mock_connection_status = status;
        mock_error_message = error;
        
        PostgreSQLConnection conn;
        IDatabaseConnection::ConnectionParams params;
        params.host = "test_host";
        params.database = "test_db";
        
        try {
            conn.connect(params);
            FAIL() << "Expected exception for: " << error;
        } catch (const DatabaseException& e) {
            std::string error_str = e.what();
            EXPECT_NE(error_str.find(error), std::string::npos)
                << "Error message should contain: " << error;
        }
    }
}

// Tests null value handling
TEST_F(PostgreSQLTest, NullValueHandling) {
    current_mock_result = createMockResult(1, 3);
    current_mock_result->field_names = {"id", "name", "optional_value"};
    current_mock_result->field_types = {23, 25, 23};
    current_mock_result->data[0] = {"1", "Test", ""};
    current_mock_result->is_null[0][2] = true; // Third column is NULL

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto result = conn.execute_query("SELECT * FROM test_table");
    ASSERT_TRUE(result->next());

    EXPECT_FALSE(result->is_null(0));
    EXPECT_FALSE(result->is_null(1));
    EXPECT_TRUE(result->is_null(2));

    EXPECT_FALSE(result->is_null("id"));
    EXPECT_FALSE(result->is_null("name"));
    EXPECT_TRUE(result->is_null("optional_value"));

    // NULL value should return empty any
    auto null_value = result->get_value(2);
    EXPECT_FALSE(null_value.has_value());
}

// Tests PostgreSQL extractor
TEST_F(PostgreSQLTest, PostgreSQLExtractor) {
    current_mock_result = createMockResult(0, 0);

    auto conn = std::make_unique<PostgreSQLConnection>();
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn->connect(params);

    PostgreSQLExtractor extractor(std::move(conn));
    EXPECT_EQ("postgresql", extractor.get_type());
}

// Tests prepared statement
TEST_F(PostgreSQLTest, PreparedStatement) {
    // First call for prepare
    current_mock_result = createMockResult(0, 0);

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto stmt = conn.prepare_statement("SELECT * FROM person WHERE person_id = $1");
    ASSERT_NE(nullptr, stmt);

    // Bind parameter
    stmt->bind(1, 12345LL);

    // Set up result for execution
    current_mock_result = createMockResult(1, 2);
    current_mock_result->field_names = {"person_id", "birth_date"};
    current_mock_result->field_types = {20, 1082};
    current_mock_result->data[0] = {"12345", "2000-01-15"};

    auto result = stmt->execute_query();
    ASSERT_NE(nullptr, result);
    ASSERT_TRUE(result->next());
    EXPECT_EQ(12345LL, std::any_cast<long long>(result->get_value(0)));
}

// Tests prepared statement multiple params
TEST_F(PostgreSQLTest, PreparedStatementMultipleParams) {
    current_mock_result = createMockResult(0, 0);

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto stmt = conn.prepare_statement(
        "INSERT INTO person (person_id, gender_concept_id, birth_datetime) VALUES ($1, $2, $3)");

    // Bind multiple parameters of different types
    stmt->bind(1, 99999LL);
    stmt->bind(2, 8507);

    auto now = std::chrono::system_clock::now();
    stmt->bind(3, now);

    // Execute update
    current_mock_result = createMockResult(1, 0);
    size_t affected = stmt->execute_update();
    EXPECT_EQ(1, affected);
}

// Tests query timeout
TEST_F(PostgreSQLTest, QueryTimeout) {
    current_mock_result = createMockResult(0, 0);

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    // Set timeout
    ASSERT_NO_THROW(conn.set_query_timeout(30));
}

// Tests query timeout and cancellation
TEST_F(PostgreSQLTest, QueryTimeoutAndCancellation) {
    // Mock a long-running query scenario
    class SlowResultSet : public MockPGresult {
    public:
        SlowResultSet() {
            ntuples = 1000000;  // Large result set
            nfields = 3;
        }
    };
    
    current_mock_result = new SlowResultSet();
    MockResultGuard guard(current_mock_result);
    
    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    // Set a short timeout
    conn.set_query_timeout(1);  // 1 second
    
    // This would timeout in a real scenario
    // In our mock, we can't easily simulate the timeout,
    // but we verify the timeout was set
    EXPECT_NO_THROW({
        auto result = conn.execute_query("SELECT * FROM large_table");
    });
}

// Tests record conversion
TEST_F(PostgreSQLTest, RecordConversion) {
    current_mock_result = createMockResult(1, 4);
    current_mock_result->field_names = {"id", "name", "active", "created_at"};
    current_mock_result->field_types = {23, 25, 16, 1114}; // INT, TEXT, BOOL, TIMESTAMP
    current_mock_result->data[0] = {"42", "Test Record", "t", "2024-01-15 10:30:00"};

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto result = conn.execute_query("SELECT * FROM test_table");
    ASSERT_TRUE(result->next());

    // Convert to record
    omop::core::Record record = result->to_record();

    EXPECT_EQ(42, std::any_cast<int>(record.getField("id")));
    EXPECT_EQ("Test Record", std::any_cast<std::string>(record.getField("name")));
    EXPECT_TRUE(std::any_cast<bool>(record.getField("active")));
    auto created_at_field = record.getFieldOptional("created_at");
    EXPECT_TRUE(created_at_field.has_value());
    EXPECT_TRUE(created_at_field->type() == typeid(std::chrono::system_clock::time_point));
}

// Tests result set columns
TEST_F(PostgreSQLTest, ResultSetColumns) {
    auto result = createMockResult(1, 3);
    MockResultGuard guard(result);
    current_mock_result->field_names = {"person_id", "birth_date", "gender_concept_id"};
    current_mock_result->field_types = {20, 1082, 23}; // BIGINT, DATE, INTEGER
    current_mock_result->data[0] = {"12345", "2000-01-15", "8507"};

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    auto result_ptr = conn.execute_query("SELECT * FROM person LIMIT 1");

    // Test column count and names
    EXPECT_EQ(3, result_ptr->column_count());
    EXPECT_EQ("person_id", result_ptr->column_name(0));
    EXPECT_EQ("birth_date", result_ptr->column_name(1));
    EXPECT_EQ("gender_concept_id", result_ptr->column_name(2));

    // Test column types
    EXPECT_EQ("bigint", result_ptr->column_type(0));
    EXPECT_EQ("date", result_ptr->column_type(1));
    EXPECT_EQ("integer", result_ptr->column_type(2));

    // Test getting values by column name
    ASSERT_TRUE(result_ptr->next());
    EXPECT_EQ(12345LL, std::any_cast<long long>(result_ptr->get_value("person_id")));
    EXPECT_EQ(8507, std::any_cast<int>(result_ptr->get_value("gender_concept_id")));
}

// Tests table exists
TEST_F(PostgreSQLTest, TableExists) {
    // Set up result for table existence query
    current_mock_result = createMockResult(1, 1);
    current_mock_result->field_names = {"exists"};
    current_mock_result->field_types = {16}; // BOOL
    current_mock_result->data[0] = {"t"}; // true

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    EXPECT_TRUE(conn.table_exists("person"));

    // Test non-existent table
    current_mock_result->data[0] = {"f"}; // false
    EXPECT_FALSE(conn.table_exists("nonexistent_table"));
}

// Tests transactions
TEST_F(PostgreSQLTest, Transactions) {
    current_mock_result = createMockResult(0, 0);

    PostgreSQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);

    // Begin transaction
    ASSERT_NO_THROW(conn.begin_transaction());

    // Execute some operations
    conn.execute_update("INSERT INTO test VALUES (1, 'test')");

    // Commit
    ASSERT_NO_THROW(conn.commit());

    // Begin another transaction
    conn.begin_transaction();

    // Rollback
    ASSERT_NO_THROW(conn.rollback());
}

// Define static member
std::atomic<int> PostgreSQLTest::statement_counter_;

#endif // OMOP_HAS_POSTGRESQL