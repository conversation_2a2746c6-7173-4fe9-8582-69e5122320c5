/**
 * @file extract_performance_test.cpp
 * @brief Performance and stress tests for extract module components
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <chrono>
#include <thread>
#include <random>
#include <future>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <unistd.h>
#include <sys/types.h>
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/database_connector.h"
#include "extract/extract_utils.h"
#include "extract/extract.h"

using namespace omop::extract;
using namespace omop::extract::utils;
using namespace omop::core;
using namespace ::testing;

// Mock database connection for testing
class MockDatabaseConnection : public IDatabaseConnection {
public:
    MOCK_METHOD(void, connect, (const ConnectionParams& params), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD(std::unique_ptr<IResultSet>, execute_query, (const std::string& sql), (override));
    MOCK_METHOD(size_t, execute_update, (const std::string& sql), (override));
    MOCK_METHOD(std::unique_ptr<IPreparedStatement>, prepare_statement, (const std::string& sql), (override));
    MOCK_METHOD(void, begin_transaction, (), (override));
    MOCK_METHOD(void, commit, (), (override));
    MOCK_METHOD(void, rollback, (), (override));
    MOCK_METHOD(std::string, get_database_type, (), (const, override));
    MOCK_METHOD(std::string, get_version, (), (const, override));
    MOCK_METHOD(void, set_query_timeout, (int seconds), (override));
    MOCK_METHOD(bool, table_exists, (const std::string& table_name, const std::string& schema), (const, override));
    MOCK_METHOD(bool, in_transaction, (), (const, override));
};

class PerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_perf_test";
        std::filesystem::create_directories(test_dir_);
        
        // Initialize random number generator for test data
        rng_.seed(std::chrono::steady_clock::now().time_since_epoch().count());
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    // Generate large CSV file for testing
    std::string generateLargeCsv(size_t num_records, const std::string& filename) {
        std::filesystem::path filepath = test_dir_ / filename;
        std::ofstream file(filepath);
        
        file << "id,name,email,age,salary,department,hire_date,active\n";
        
        std::uniform_int_distribution<int> age_dist(22, 65);
        std::uniform_real_distribution<double> salary_dist(30000.0, 150000.0);
        std::uniform_int_distribution<int> bool_dist(0, 1);
        
        const std::vector<std::string> departments = {
            "Engineering", "Marketing", "Sales", "HR", "Finance", "Operations"
        };
        
        for (size_t i = 1; i <= num_records; ++i) {
            file << i << ","
                 << "User" << i << ","
                 << "user" << i << "@example.com,"
                 << age_dist(rng_) << ","
                 << std::fixed << std::setprecision(2) << salary_dist(rng_) << ","
                 << departments[i % departments.size()] << ","
                 << "2020-01-01,"
                 << (bool_dist(rng_) ? "true" : "false") << "\n";
                 
            // Flush periodically to avoid memory issues
            if (i % 10000 == 0) {
                file.flush();
            }
        }
        
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
    std::mt19937 rng_;
};

// Test CSV extraction performance with large files
TEST_F(PerformanceTest, LargeCsvExtractionPerformance) {
    const size_t num_records = 100000;
    std::string filepath = generateLargeCsv(num_records, "large_test.csv");
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["batch_size"] = size_t(10000);
    
    ProcessingContext context;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    extractor.initialize(config, context);
    
    size_t total_extracted = 0;
    while (extractor.has_more_data()) {
        auto batch = extractor.extract_batch(10000, context);
        total_extracted += batch.size();
    }
    
    extractor.finalize(context);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    EXPECT_EQ(total_extracted, num_records);
    
    // Performance assertion: should process at least 10,000 records per second
    double records_per_second = static_cast<double>(total_extracted) / (duration.count() / 1000.0);
    EXPECT_GE(records_per_second, 10000.0) 
        << "Performance below threshold: " << records_per_second << " records/sec";
    
    // Memory usage should be reasonable
    auto stats = extractor.get_statistics();
    EXPECT_TRUE(stats.contains("extracted_count"));
    EXPECT_EQ(std::any_cast<size_t>(stats["extracted_count"]), num_records);
}

// Test concurrent CSV processing
TEST_F(PerformanceTest, ConcurrentCsvProcessing) {
    const size_t num_files = 4;
    const size_t records_per_file = 25000;
    std::vector<std::string> test_files;
    
    // Create multiple test files
    for (size_t i = 0; i < num_files; ++i) {
        std::string filename = "concurrent_" + std::to_string(i) + ".csv";
        test_files.push_back(generateLargeCsv(records_per_file, filename));
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Process files concurrently
    std::vector<std::future<size_t>> futures;
    for (const auto& filepath : test_files) {
        futures.push_back(std::async(std::launch::async, [filepath]() {
            CsvExtractor extractor;
            std::unordered_map<std::string, std::any> config;
            config["filepath"] = filepath;
            config["batch_size"] = size_t(5000);
            
            ProcessingContext context;
            extractor.initialize(config, context);
            
            size_t count = 0;
            while (extractor.has_more_data()) {
                auto batch = extractor.extract_batch(5000, context);
                count += batch.size();
            }
            
            extractor.finalize(context);
            return count;
        }));
    }
    
    // Wait for all tasks to complete
    size_t total_processed = 0;
    for (auto& future : futures) {
        total_processed += future.get();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    EXPECT_EQ(total_processed, num_files * records_per_file);
    
    // Concurrent processing should be faster than sequential
    double records_per_second = static_cast<double>(total_processed) / (duration.count() / 1000.0);
    EXPECT_GE(records_per_second, 20000.0)
        << "Concurrent processing performance: " << records_per_second << " records/sec";
}

// Test memory usage with large datasets
TEST_F(PerformanceTest, MemoryUsageValidation) {
    const size_t num_records = 500000;
    std::string filepath = generateLargeCsv(num_records, "memory_test.csv");
    
    // Get initial memory usage
    auto get_memory_usage = []() -> size_t {
        std::ifstream statm("/proc/self/statm");
        if (statm.is_open()) {
            size_t size, resident, shared, text, lib, data, dt;
            statm >> size >> resident >> shared >> text >> lib >> data >> dt;
            return resident * getpagesize(); // RSS in bytes
        }
        return 0;
    };
    
    size_t initial_memory = get_memory_usage();
    
    {
        CsvExtractor extractor;
        std::unordered_map<std::string, std::any> config;
        config["filepath"] = filepath;
        config["batch_size"] = size_t(1000); // Small batches to test streaming
        
        ProcessingContext context;
        extractor.initialize(config, context);
        
        size_t processed = 0;
        size_t max_memory = initial_memory;
        
        while (extractor.has_more_data()) {
            auto batch = extractor.extract_batch(1000, context);
            processed += batch.size();
            
            // Check memory usage periodically
            if (processed % 50000 == 0) {
                size_t current_memory = get_memory_usage();
                max_memory = std::max(max_memory, current_memory);
            }
        }
        
        extractor.finalize(context);
        
        EXPECT_EQ(processed, num_records);
        
        // Memory usage should not grow excessively (less than 100MB for this test)
        size_t memory_increase = max_memory - initial_memory;
        EXPECT_LT(memory_increase, 100 * 1024 * 1024) // 100MB limit
            << "Memory usage increased by: " << memory_increase / (1024 * 1024) << " MB";
    }
    
    // Memory should be cleaned up after extraction
    std::this_thread::sleep_for(std::chrono::milliseconds(5));
    size_t final_memory = get_memory_usage();
    size_t final_increase = final_memory - initial_memory;
    
    // Allow some memory overhead but should be much less than peak usage
    EXPECT_LT(final_increase, 20 * 1024 * 1024) // 20MB limit
        << "Memory not properly cleaned up. Final increase: " << final_increase / (1024 * 1024) << " MB";
}

// =============================================================================
// NEW TEST SUITE 2: Integration Testing
// =============================================================================

/**
 * @file extract_integration_test.cpp
 * @brief Integration tests for extract module components
 */

class IntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_integration_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::filesystem::path test_dir_;
};

// Test complete ETL pipeline integration
TEST_F(IntegrationTest, CompleteETLPipelineIntegration) {
    // Create test CSV file
    std::string csv_content = 
        "patient_id,birth_date,gender,ethnicity\n"
        "1,1980-05-15,M,Hispanic\n"
        "2,1975-12-22,F,Caucasian\n"
        "3,1990-03-08,M,African American\n";
    
    std::filesystem::path csv_file = test_dir_ / "patients.csv";
    std::ofstream file(csv_file);
    file << csv_content;
    file.close();
    
    // Test end-to-end extraction using BatchExtractor
    auto extractor = create_extractor_auto(csv_file.string());
    ASSERT_NE(extractor, nullptr);
    
    BatchExtractor::Config config;
    config.batch_size = 2;
    config.max_records = 0; // No limit
    
    std::vector<std::string> processed_patients;
    config.progress_callback = [&processed_patients](size_t current, size_t total) {
        // Progress tracking integration
        EXPECT_LE(current, total);
    };
    
    BatchExtractor batch_extractor(std::move(extractor), config);
    
    // Extract with callback processing
    size_t total_processed = batch_extractor.extract_with_callback(
        [&processed_patients](const RecordBatch& batch) {
            for (const auto& record : batch.getRecords()) {
                if (record.hasField("patient_id")) {
                    processed_patients.push_back(
                        std::any_cast<std::string>(record.getField("patient_id")));
                }
            }
        });
    
    EXPECT_EQ(total_processed, 3);
    EXPECT_EQ(processed_patients.size(), 3);
    
    // Verify all patients were processed
    std::sort(processed_patients.begin(), processed_patients.end());
    EXPECT_EQ(processed_patients[0], "1");
    EXPECT_EQ(processed_patients[1], "2");
    EXPECT_EQ(processed_patients[2], "3");
    
    // Test statistics integration
    auto stats = batch_extractor.get_statistics();
    EXPECT_TRUE(stats.contains("batch_size"));
    EXPECT_EQ(std::any_cast<size_t>(stats["batch_size"]), 2);
}

// Test factory integration with configuration
TEST_F(IntegrationTest, ExtractorFactoryConfigurationIntegration) {
    // Test CSV extractor creation and configuration
    ExtractorConfigBuilder csv_builder("csv");
    
    std::string csv_file = (test_dir_ / "test.csv").string();
    std::ofstream(csv_file) << "id,name\n1,test\n2,test2\n";
    
    auto csv_extractor = csv_builder
        .with_file(csv_file)
        .set("has_header", true)
        .set("delimiter", ',')
        .with_columns({"id", "name"})
        .build();
    
    ASSERT_NE(csv_extractor, nullptr);
    EXPECT_EQ(csv_extractor->get_type(), "csv");
    
    // Test JSON extractor creation
    ExtractorConfigBuilder json_builder("json");
    
    std::string json_file = (test_dir_ / "test.json").string();
    std::ofstream(json_file) << R"([{"id": 1, "name": "test"}, {"id": 2, "name": "test2"}])";
    
    auto json_extractor = json_builder
        .with_file(json_file)
        .set("flatten_nested", true)
        .build();
    
    ASSERT_NE(json_extractor, nullptr);
    EXPECT_EQ(json_extractor->get_type(), "json");
    
    // Test extractor type information integration
    auto type_info = get_extractor_info();
    EXPECT_FALSE(type_info.empty());
    
    bool found_csv = false;
    bool found_json = false;
    for (const auto& info : type_info) {
        if (info.type == "csv") {
            found_csv = true;
            EXPECT_FALSE(info.description.empty());
            EXPECT_FALSE(info.required_params.empty());
            EXPECT_FALSE(info.example_config.empty());
        } else if (info.type == "json") {
            found_json = true;
        }
    }
    
    EXPECT_TRUE(found_csv);
    EXPECT_TRUE(found_json);
}

// =============================================================================
// NEW TEST SUITE 3: Error Recovery and Resilience Testing
// =============================================================================

/**
 * @file extract_resilience_test.cpp
 * @brief Resilience and error recovery tests
 */

class ResilienceTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_resilience_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::filesystem::path test_dir_;
};

// Test recovery from file system errors
TEST_F(ResilienceTest, FileSystemErrorRecovery) {
    std::string csv_content = "id,value\n1,100\n2,200\n";
    std::filesystem::path csv_file = test_dir_ / "recovery_test.csv";
    std::ofstream(csv_file) << csv_content;
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = csv_file.string();
    config["continue_on_error"] = true;
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    // Extract first batch successfully
    auto batch1 = extractor.extract_batch(1, context);
    EXPECT_EQ(batch1.size(), 1);
    
    // Simulate file system error by removing file
    std::filesystem::remove(csv_file);
    
    // Extractor should handle the error gracefully
    EXPECT_NO_THROW({
        auto batch2 = extractor.extract_batch(1, context);
        // May return empty batch due to file error
    });
    
    // Statistics should reflect the error
    auto stats = extractor.get_statistics();
    EXPECT_GE(std::any_cast<size_t>(stats.at("extracted_count")), 1);
}

// Test handling of corrupted data with recovery
TEST_F(ResilienceTest, CorruptedDataRecovery) {
    // Create file with mixed valid and corrupted data
    std::string mixed_content = 
        "id,name,value\n"                    // Valid header
        "1,\"John Doe\",100.50\n"           // Valid record
        "2,\"Jane Smith\",200\n"            // Valid record
        "3,\"Corrupted,missing quote,300\n" // Corrupted - missing closing quote
        "4,\"Bob Johnson\",400\n"           // Valid record
        "5,\"Alice\",invalid_number\n"      // Invalid number
        "6,\"Charlie Brown\",600.75\n";     // Valid record
    
    std::filesystem::path csv_file = test_dir_ / "corrupted.csv";
    std::ofstream(csv_file) << mixed_content;
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = csv_file.string();
    config["continue_on_error"] = true;
    config["batch_size"] = size_t(10);
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    std::vector<Record> all_records;
    size_t error_count = 0;
    
    while (extractor.has_more_data()) {
        try {
            auto batch = extractor.extract_batch(10, context);
            for (const auto& record : batch.getRecords()) {
                all_records.push_back(record);
            }
        } catch (const std::exception& e) {
            error_count++;
            // Should continue processing despite errors
        }
    }
    
    // Should extract valid records despite corruption
    EXPECT_GE(all_records.size(), 4); // At least 4 valid records
    EXPECT_LE(all_records.size(), 6); // At most 6 records total
    
    // Verify that valid records were extracted correctly
    bool found_john = false;
    bool found_charlie = false;
    
    for (const auto& record : all_records) {
        if (record.hasField("name")) {
            std::string name = std::any_cast<std::string>(record.getField("name"));
            if (name.find("John") != std::string::npos) {
                found_john = true;
            } else if (name.find("Charlie") != std::string::npos) {
                found_charlie = true;
            }
        }
    }
    
    EXPECT_TRUE(found_john);
    EXPECT_TRUE(found_charlie);
    
    // Check error statistics
    auto stats = extractor.get_statistics();
    EXPECT_TRUE(stats.contains("error_count"));
}

// Test resource exhaustion scenarios
TEST_F(ResilienceTest, ResourceExhaustionHandling) {
    // Create a very large CSV file to test memory limits
    std::filesystem::path large_file = test_dir_ / "large.csv";
    std::ofstream file(large_file);
    
    file << "id,data\n";
    
    // Create file with large text fields to test memory pressure
    const std::string large_text(10000, 'A'); // 10KB text per record
    
    for (int i = 1; i <= 1000; ++i) {
        file << i << ",\"" << large_text << "\"\n";
        
        if (i % 100 == 0) {
            file.flush();
        }
    }
    file.close();
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = large_file.string();
    config["batch_size"] = size_t(10); // Small batches to manage memory
    config["continue_on_error"] = true;
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    size_t total_extracted = 0;
    size_t batch_count = 0;
    
    // Extract with memory monitoring
    while (extractor.has_more_data() && batch_count < 100) { // Safety limit
        auto batch = extractor.extract_batch(10, context);
        total_extracted += batch.size();
        batch_count++;
        
        // Force garbage collection hint
        if (batch_count % 10 == 0) {
            // Small delay to allow memory management
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
    
    extractor.finalize(context);
    
    // Should have extracted significant amount of data
    EXPECT_GT(total_extracted, 500);
    
    // Verify statistics
    auto stats = extractor.get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(stats["extracted_count"]), total_extracted);
}

// =============================================================================
// NEW TEST SUITE 4: Thread Safety and Concurrency Testing
// =============================================================================

/**
 * @file extract_concurrency_test.cpp
 * @brief Thread safety and concurrency tests
 */

class ConcurrencyTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_concurrency_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::filesystem::path test_dir_;
};

// Test factory thread safety
TEST_F(ConcurrencyTest, ExtractorFactoryThreadSafety) {
    const int num_threads = 10;
    const int ops_per_thread = 100;
    
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    std::atomic<int> error_count{0};
    
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([t, ops_per_thread, &success_count, &error_count]() {
            for (int op = 0; op < ops_per_thread; ++op) {
                try {
                    // Register and create extractors concurrently
                    std::string type_name = std::format("thread_{}_{}", t, op);
                    
                    ExtractorFactoryRegistry::register_type(type_name, []() {
                        return std::make_unique<CsvExtractor>();
                    });
                    
                    // Verify registration
                    if (ExtractorFactoryRegistry::is_type_registered(type_name)) {
                        auto extractor = ExtractorFactoryRegistry::create(type_name);
                        if (extractor) {
                            success_count++;
                        }
                    }
                } catch (const std::exception& e) {
                    error_count++;
                }
            }
        });
    }
    
    // Wait for all threads
    for (auto& thread : threads) {
        thread.join();
    }
    
    // All operations should succeed
    EXPECT_EQ(success_count.load(), num_threads * ops_per_thread);
    EXPECT_EQ(error_count.load(), 0);
    
    // Verify all types were registered
    auto registered_types = ExtractorFactoryRegistry::get_registered_types();
    EXPECT_GE(registered_types.size(), num_threads * ops_per_thread);
}

// Test concurrent file processing
TEST_F(ConcurrencyTest, ConcurrentFileProcessing) {
    const int num_files = 8;
    const int records_per_file = 1000;
    
    // Create test files
    std::vector<std::string> file_paths;
    for (int i = 0; i < num_files; ++i) {
        std::filesystem::path file_path = test_dir_ / std::format("concurrent_{}.csv", i);
        std::ofstream file(file_path);
        
        file << "id,thread_id,value\n";
        for (int j = 1; j <= records_per_file; ++j) {
            file << j << "," << i << "," << (j * i) << "\n";
        }
        file.close();
        
        file_paths.push_back(file_path.string());
    }
    
    // Process files concurrently
    std::vector<std::future<std::pair<size_t, std::string>>> futures;
    std::atomic<int> active_extractors{0};
    
    for (int i = 0; i < num_files; ++i) {
        futures.push_back(std::async(std::launch::async, 
            [&file_paths, i, &active_extractors]() -> std::pair<size_t, std::string> {
                
                active_extractors++;
                
                try {
                    CsvExtractor extractor;
                    std::unordered_map<std::string, std::any> config;
                    config["filepath"] = file_paths[i];
                    config["batch_size"] = size_t(100);
                    
                    ProcessingContext context;
                    extractor.initialize(config, context);
                    
                    size_t count = 0;
                    while (extractor.has_more_data()) {
                        auto batch = extractor.extract_batch(100, context);
                        count += batch.size();
                    }
                    
                    extractor.finalize(context);
                    active_extractors--;
                    
                    return {count, "success"};
                } catch (const std::exception& e) {
                    active_extractors--;
                    return {0, e.what()};
                }
            }));
    }
    
    // Collect results
    size_t total_records = 0;
    int successful_extractions = 0;
    
    for (auto& future : futures) {
        auto [count, status] = future.get();
        if (status == "success") {
            total_records += count;
            successful_extractions++;
            EXPECT_EQ(count, records_per_file);
        } else {
            FAIL() << "Extraction failed: " << status;
        }
    }
    
    EXPECT_EQ(successful_extractions, num_files);
    EXPECT_EQ(total_records, num_files * records_per_file);
    EXPECT_EQ(active_extractors.load(), 0); // All should be finished
}

// Test connection pool under concurrent load
TEST_F(ConcurrencyTest, ConnectionPoolConcurrentLoad) {
    std::atomic<int> connection_count{0};
    std::atomic<int> max_concurrent{0};
    std::atomic<int> current_concurrent{0};
    
    auto connection_factory = [&connection_count, &max_concurrent, &current_concurrent]() -> std::unique_ptr<IDatabaseConnection> {
        connection_count++;
        current_concurrent++;
        
        int current = current_concurrent.load();
        int expected_max = max_concurrent.load();
        while (current > expected_max && 
               !max_concurrent.compare_exchange_weak(expected_max, current)) {
            expected_max = max_concurrent.load();
        }
        
        // Simulate connection work
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
        
        auto conn = std::make_unique<MockDatabaseConnection>();
        ON_CALL(*conn, is_connected()).WillByDefault(Return(true));
        
        current_concurrent--;
        return conn;
    };
    
    ConnectionPool pool(2, 8, connection_factory);
    
    const int num_threads = 16;
    const int ops_per_thread = 10;
    
    std::vector<std::thread> threads;
    std::atomic<int> successful_ops{0};
    
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([&pool, ops_per_thread, &successful_ops]() {
            for (int op = 0; op < ops_per_thread; ++op) {
                try {
                    auto conn = pool.acquire(1000); // 1 second timeout
                    if (conn) {
                        // Simulate work with connection
                        std::this_thread::sleep_for(std::chrono::milliseconds(1));
                        
                        pool.release(std::move(conn));
                        successful_ops++;
                    }
                } catch (const std::exception& e) {
                    // Some operations may timeout under heavy load
                }
            }
        });
    }
    
    // Wait for all threads
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Most operations should succeed
    EXPECT_GE(successful_ops.load(), num_threads * ops_per_thread * 0.8); // 80% success rate
    
    // Verify pool statistics
    auto stats = pool.get_statistics();
    EXPECT_GT(stats.total_acquisitions, 0);
    EXPECT_EQ(stats.active_connections, 0); // All should be returned
    
    // Connection count should be reasonable (not creating excessive connections)
    EXPECT_LE(connection_count.load(), 20); // Reasonable upper bound
    EXPECT_LE(max_concurrent.load(), 8);    // Should not exceed pool limit
}
