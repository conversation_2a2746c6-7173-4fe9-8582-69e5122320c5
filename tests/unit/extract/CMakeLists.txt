# tests/unit/extract/CMakeLists.txt

# Unit tests for OMOP Extract library with UK localization support

# Test source files in the extract directory
set(EXTRACT_TEST_SOURCES
    compressed_csv_extraction_unit_test.cpp
    database_connection_pool_unit_test.cpp
    csv_file_extraction_unit_test.cpp
    database_connection_unit_test.cpp
    extraction_utilities_extended_unit_test.cpp
    extraction_utilities_unit_test.cpp
    extractor_base_functionality_unit_test.cpp  
    extractor_factory_creation_unit_test.cpp
    json_data_extraction_unit_test.cpp
    mysql_database_connection_unit_test.cpp
    odbc_database_connection_unit_test.cpp
    platform_utilities_unit_test.cpp
    postgresql_database_connection_unit_test.cpp
    extraction_performance_unit_test.cpp
    uk_healthcare_localization_unit_test.cpp
)

# Use the consolidated approach from parent CMakeLists.txt
add_component_unit_tests(extract ${EXTRACT_TEST_SOURCES})

# Add MySQL include directory to the component test
target_include_directories(test_omop_extract_unit PRIVATE
    /usr/include/mysql
)

# Enable sanitizers for extract tests if enabled
if(ENABLE_SANITIZERS)
    target_compile_options(test_omop_extract_unit PRIVATE
        -fprofile-arcs
        -fsanitize=address,undefined
        -fno-omit-frame-pointer
    )
    target_link_options(test_omop_extract_unit PRIVATE
        -fsanitize=address,undefined
    )
endif()

# Set extract-specific test labels and timeout
set_tests_properties(test_omop_extract_unit PROPERTIES
    LABELS "unit;extract"
    TIMEOUT 600  # Increased timeout for database tests
)

# Enable code coverage for extract tests
if(ENABLE_COVERAGE)
    target_compile_options(test_omop_extract_unit PRIVATE
        -fprofile-arcs
        -ftest-coverage
    )
    target_link_options(test_omop_extract_unit PRIVATE
        -fprofile-arcs
        -ftest-coverage
    )
endif()

# Set locale for UK-specific tests
set_tests_properties(test_omop_extract_unit PROPERTIES
    ENVIRONMENT "LC_ALL=en_GB.UTF-8;LANG=en_GB.UTF-8"
)
