/**
 * @file csv_extractor_test.cpp
 * @brief Unit tests for CSV extractor functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/csv_extractor.h"
#include "extract/extractor_factory.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <filesystem>
#include <fstream>
#include <chrono>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::Throw;

// ============================================================================
// CsvFieldParserTest Tests
// ============================================================================

// Test CSV field parser functionality
class CsvFieldParserTest : public ::testing::Test {
protected:
    CsvOptions options_;
    std::unique_ptr<CsvFieldParser> parser_;

    void SetUp() override {
        options_.delimiter = ',';
        options_.quote_char = '"';
        options_.escape_char = '\\';
        options_.trim_fields = true;
        parser_ = std::make_unique<CsvFieldParser>(options_);
    }
};

// Tests field type conversion functionality
// Tests conversion of various field types including UK formats
TEST_F(CsvFieldParserTest, ConvertFieldTypes) {
    // Test integer conversion
    auto int_value = parser_->convert_field("42", "integer");
    ASSERT_TRUE(int_value.has_value());
    EXPECT_EQ(42LL, std::any_cast<long long>(int_value));

    // Test double conversion with UK decimal format
    auto double_value = parser_->convert_field("3.14", "double");
    ASSERT_TRUE(double_value.has_value());
    EXPECT_DOUBLE_EQ(3.14, std::any_cast<double>(double_value));

    // Test boolean conversion
    auto bool_value = parser_->convert_field("TRUE", "boolean");
    ASSERT_TRUE(bool_value.has_value());
    EXPECT_TRUE(std::any_cast<bool>(bool_value));

    // Test null value handling
    auto null_value = parser_->convert_field("", "");
    EXPECT_FALSE(null_value.has_value());

    // Test UK currency format (pounds)
    auto currency_value = parser_->convert_field("£123.45", "string");
    ASSERT_TRUE(currency_value.has_value());
    EXPECT_EQ("£123.45", std::any_cast<std::string>(currency_value));
}

// Tests UK localized data format parsing
TEST_F(CsvFieldParserTest, ParseUKLocalizedFormats) {
    // Set up UK-specific options
    options_.date_format = "%d/%m/%Y";
    options_.datetime_format = "%d/%m/%Y %H:%M:%S";
    parser_ = std::make_unique<CsvFieldParser>(options_);
    
    // Test UK postal codes
    std::vector<std::string> uk_postcodes = {
        "SW1A 1AA",  // Central London
        "M1 1AA",    // Manchester
        "B33 8TH",   // Birmingham
        "W1A 0AX",   // Oxford Street
        "EH1 1YZ",   // Edinburgh
        "CF10 3AT",  // Cardiff
        "BT1 5GS"    // Belfast
    };
    
    for (const auto& postcode : uk_postcodes) {
        auto postcode_val = parser_->convert_field(postcode, "string");
        ASSERT_TRUE(postcode_val.has_value());
        EXPECT_EQ(postcode, std::any_cast<std::string>(postcode_val));
    }
    
    // Test UK telephone numbers
    std::vector<std::string> uk_phone_numbers = {
        "+44 20 7946 0958",   // London with country code
        "020 7946 0958",      // London
        "0161 496 0000",      // Manchester
        "0131 496 0000",      // Edinburgh
        "029 2018 0000"       // Cardiff
    };
    
    for (const auto& phone : uk_phone_numbers) {
        auto phone_val = parser_->convert_field(phone, "string");
        ASSERT_TRUE(phone_val.has_value());
        EXPECT_EQ(phone, std::any_cast<std::string>(phone_val));
    }
    
    // Test UK currency amounts
    std::vector<std::string> uk_currencies = {
        "£0.99",      // Pence
        "£12.50",     // Standard amount
        "£1,234.56",  // Amount with thousands separator
        "£1,000,000", // Million pounds
        "£0.01"       // One penny
    };
    
    for (const auto& currency : uk_currencies) {
        auto currency_val = parser_->convert_field(currency, "string");
        ASSERT_TRUE(currency_val.has_value());
        EXPECT_EQ(currency, std::any_cast<std::string>(currency_val));
    }
    
    // Test UK temperature formats (Celsius)
    std::vector<std::string> uk_temperatures = {
        "23°C",       // Summer temperature
        "5°C",        // Winter temperature
        "-2°C",       // Below freezing
        "0°C",        // Freezing point
        "37°C"        // Body temperature
    };
    
    for (const auto& temp : uk_temperatures) {
        auto temp_val = parser_->convert_field(temp, "string");
        ASSERT_TRUE(temp_val.has_value());
        EXPECT_EQ(temp, std::any_cast<std::string>(temp_val));
    }
}

// Tests comprehensive field parsing edge cases including UK formats
// Tests edge cases in CSV field parsing with special characters and UK formats
TEST_F(CsvFieldParserTest, ComprehensiveFieldParsingEdgeCases) {
    CsvOptions options;
    options.delimiter = ',';
    options.quote_char = '"';
    options.escape_char = '\\';
    options.trim_fields = true;
    
    CsvFieldParser parser(options);
    
    // Test 1: Empty fields and whitespace handling
    std::string line1 = " , ,  \"\"  , \"  spaced  \" ";
    auto fields1 = parser.parse_line(line1);
    ASSERT_EQ(4, fields1.size());
    EXPECT_EQ("", fields1[0]);        // Empty after trim
    EXPECT_EQ("", fields1[1]);        // Empty after trim
    EXPECT_EQ("", fields1[2]);        // Empty quoted field
    EXPECT_EQ("  spaced  ", fields1[3]); // Preserve internal spaces in quoted field
    
    // Test 2: Complex escape sequences
    std::string line2 = R"("He said \"Hello\", she replied \"Hi!\"","Value with, comma","Tab\there")";
    auto fields2 = parser.parse_line(line2);
    ASSERT_EQ(3, fields2.size());
    EXPECT_EQ("He said \"Hello\", she replied \"Hi!\"", fields2[0]);
    EXPECT_EQ("Value with, comma", fields2[1]);
    EXPECT_EQ("Tab\there", fields2[2]);
    
    // Test 3: Mixed quoted and unquoted fields
    std::string line3 = "unquoted,\"quoted with, comma\",another_unquoted,\"\"";
    auto fields3 = parser.parse_line(line3);
    ASSERT_EQ(4, fields3.size());
    EXPECT_EQ("unquoted", fields3[0]);
    EXPECT_EQ("quoted with, comma", fields3[1]);
    EXPECT_EQ("another_unquoted", fields3[2]);
    EXPECT_EQ("", fields3[3]);
    
    // Test 4: Line ending handling
    std::string line4 = "field1,field2\n";
    auto fields4 = parser.parse_line(line4);
    ASSERT_EQ(2, fields4.size());
    EXPECT_EQ("field1", fields4[0]);
    EXPECT_EQ("field2", fields4[1]);
    
    // Test 5: Unicode handling including UK characters
    std::string line5 = "café,ñoño,\"中文字符\",🚀,£50.25";
    auto fields5 = parser.parse_line(line5);
    ASSERT_EQ(5, fields5.size());
    EXPECT_EQ("café", fields5[0]);
    EXPECT_EQ("ñoño", fields5[1]);
    EXPECT_EQ("中文字符", fields5[2]);
    EXPECT_EQ("🚀", fields5[3]);
    EXPECT_EQ("£50.25", fields5[4]);  // UK currency format
    
    // Test 6: UK date format handling (DD/MM/YYYY)
    std::string line6 = "15/01/2025,\"New Year's Day\",Bristol";
    auto fields6 = parser.parse_line(line6);
    ASSERT_EQ(3, fields6.size());
    EXPECT_EQ("15/01/2025", fields6[0]);  // UK date format
    EXPECT_EQ("New Year's Day", fields6[1]);
    EXPECT_EQ("Bristol", fields6[2]);  // UK city
    
    // Test 7: UK temperature format (Celsius)
    std::string line7 = "London,5°C,Manchester,3°C";
    auto fields7 = parser.parse_line(line7);
    ASSERT_EQ(4, fields7.size());
    EXPECT_EQ("London", fields7[0]);
    EXPECT_EQ("5°C", fields7[1]);     // UK temperature format
    EXPECT_EQ("Manchester", fields7[2]);
    EXPECT_EQ("3°C", fields7[3]);     // UK temperature format
    
    // Test 8: UK postal code format
    std::string line8 = "SW1A 1AA,\"10 Downing Street\",London";
    auto fields8 = parser.parse_line(line8);
    ASSERT_EQ(3, fields8.size());
    EXPECT_EQ("SW1A 1AA", fields8[0]);  // UK postal code format
    EXPECT_EQ("10 Downing Street", fields8[1]);
    EXPECT_EQ("London", fields8[2]);
}


// Tests field parsing with escape characters
// Tests parsing of fields containing escape characters
TEST_F(CsvFieldParserTest, ParseEscapedField) {
    std::string input = "field\\,with\\,escapes,normal field";
    size_t pos = 0;

    std::string field1 = parser_->parse_field(input, pos);
    EXPECT_EQ("field,with,escapes", field1);
}

// Tests parsing complete line into fields
// Tests parsing a complete CSV line into individual fields
TEST_F(CsvFieldParserTest, ParseCompleteLine) {
    std::string line = "John,Doe,30,\"New York, NY\"";

    auto fields = parser_->parse_line(line);
    ASSERT_EQ(4, fields.size());
    EXPECT_EQ("John", fields[0]);
    EXPECT_EQ("Doe", fields[1]);
    EXPECT_EQ("30", fields[2]);
    EXPECT_EQ("New York, NY", fields[3]);
}

// Tests field parsing with quoted fields
// Tests parsing of quoted fields containing special characters
TEST_F(CsvFieldParserTest, ParseQuotedField) {
    // Corrected input: both fields are quoted
    std::string input = "\"field with, comma\",\"field with \"\" quotes\"";
    size_t pos = 0;

    std::string field1 = parser_->parse_field(input, pos);
    EXPECT_EQ("field with, comma", field1);

    std::string field2 = parser_->parse_field(input, pos);
    EXPECT_EQ("field with \" quotes", field2);
}

// Tests date parsing with UK formats (DD/MM/YYYY)
// Tests parsing of dates in UK format (DD/MM/YYYY) and other formats
TEST_F(CsvFieldParserTest, ParseDateField) {
    // Test UK date format DD/MM/YYYY
    options_.date_format = "%d/%m/%Y";
    parser_ = std::make_unique<CsvFieldParser>(options_);

    auto uk_date_value = parser_->convert_field("15/01/2025", "date");
    ASSERT_TRUE(uk_date_value.has_value());
    EXPECT_TRUE(uk_date_value.type() == typeid(std::chrono::system_clock::time_point));
    
    // Test ISO format as secondary
    options_.date_format = "%Y-%m-%d";
    parser_ = std::make_unique<CsvFieldParser>(options_);
    
    auto iso_date_value = parser_->convert_field("2025-01-15", "date");
    ASSERT_TRUE(iso_date_value.has_value());
    EXPECT_TRUE(iso_date_value.type() == typeid(std::chrono::system_clock::time_point));
    
    // Test UK date with time (24-hour format)
    options_.datetime_format = "%d/%m/%Y %H:%M:%S";
    parser_ = std::make_unique<CsvFieldParser>(options_);
    
    auto uk_datetime_value = parser_->convert_field("15/01/2025 14:30:45", "datetime");
    ASSERT_TRUE(uk_datetime_value.has_value());
    EXPECT_TRUE(uk_datetime_value.type() == typeid(std::chrono::system_clock::time_point));
}

// Tests basic field parsing without quotes
// Tests parsing of simple unquoted CSV fields
TEST_F(CsvFieldParserTest, ParseSimpleField) {
    std::string input = "field1,field2,field3";
    size_t pos = 0;

    std::string field1 = parser_->parse_field(input, pos);
    EXPECT_EQ("field1", field1);
    EXPECT_EQ(7, pos);

    std::string field2 = parser_->parse_field(input, pos);
    EXPECT_EQ("field2", field2);
    EXPECT_EQ(14, pos);

    std::string field3 = parser_->parse_field(input, pos);
    EXPECT_EQ("field3", field3);
}

// Tests type conversion error handling
// Tests error handling when converting field types
TEST_F(CsvFieldParserTest, TypeConversionErrorHandling) {
    CsvOptions options;
    options.delimiter = ',';
    options.quote_char = '"';
    options.escape_char = '\\';
    options.trim_fields = true;
    
    CsvFieldParser parser(options);
    
    // Test boolean parsing with various formats
    std::vector<std::pair<std::string, bool>> bool_tests = {
        {"true", true}, {"false", false},
        {"TRUE", true}, {"FALSE", false},
        {"True", true}, {"False", false},
        {"1", true}, {"0", false},
        {"yes", true}, {"no", false},
        {"YES", true}, {"NO", false},
        {"Y", true}, {"N", false},
        {"T", true}, {"F", false},
        {"on", true}, {"off", false}
    };
    
    for (const auto& [input, expected] : bool_tests) {
        auto bool_val = parser.convert_field(input, "boolean");
        if (bool_val.has_value() && bool_val.type() == typeid(bool)) {
            EXPECT_EQ(expected, std::any_cast<bool>(bool_val))
                << "Failed for input: " << input;
        }
    }
    
    // Test date parsing with various formats including UK-preferred formats
    std::vector<std::pair<std::string, std::string>> date_formats = {
        {"%d/%m/%Y", "15/01/2025"},      // UK date format (most common)
        {"%d-%m-%Y", "15-01-2025"},      // UK date format (alternative)
        {"%d %B %Y", "15 January 2025"}, // UK long date format
        {"%d %b %Y", "15 Jan 2025"},     // UK short month format
        {"%Y-%m-%d", "2025-01-15"},      // ISO format (secondary)
        {"%Y%m%d", "20250115"},          // Compact format
        {"%d.%m.%Y", "15.01.2025"}       // European format
    };
    
    for (const auto& [format, date_str] : date_formats) {
        CsvOptions format_options = options;
        format_options.date_format = format;
        CsvFieldParser format_parser(format_options);
        
        auto date_val = format_parser.convert_field(date_str, "date");
        EXPECT_TRUE(date_val.has_value())
            << "Failed to parse date: " << date_str << " with format: " << format;
    }
}

// Tests automatic type inference including UK formats
TEST_F(CsvFieldParserTest, InferFieldType) {
    // Integer inference
    auto int_val = parser_->convert_field("12345", "");
    ASSERT_TRUE(int_val.has_value());
    EXPECT_TRUE(int_val.type() == typeid(long long));

    // Double inference with UK decimal format
    auto double_val = parser_->convert_field("123.45", "");
    ASSERT_TRUE(double_val.has_value());
    EXPECT_TRUE(double_val.type() == typeid(double));

    // Boolean inference
    auto bool_val = parser_->convert_field("true", "");
    ASSERT_TRUE(bool_val.has_value());
    EXPECT_TRUE(bool_val.type() == typeid(bool));

    // String fallback
    auto string_val = parser_->convert_field("not a number", "");
    ASSERT_TRUE(string_val.has_value());
    EXPECT_TRUE(string_val.type() == typeid(std::string));
}

// Tests type inference edge cases
TEST_F(CsvFieldParserTest, TypeInferenceEdgeCases) {
    CsvFieldParser parser(options_);
    
    // Test number parsing edge cases
    EXPECT_EQ(typeid(long long), parser.convert_field("0", "").type());
    EXPECT_EQ(typeid(long long), parser.convert_field("-123", "").type());
    EXPECT_EQ(typeid(long long), parser.convert_field("+456", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("123.45", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("-123.45", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("1.23e10", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("1.23E-5", "").type());
    
    // Test boolean parsing
    auto bool_val1 = parser.convert_field("true", "");
    EXPECT_EQ(typeid(bool), bool_val1.type());
    EXPECT_TRUE(std::any_cast<bool>(bool_val1));
    
    auto bool_val2 = parser.convert_field("FALSE", "");
    EXPECT_EQ(typeid(bool), bool_val2.type());
    EXPECT_FALSE(std::any_cast<bool>(bool_val2));
    
    auto bool_val3 = parser.convert_field("1", "boolean");
    EXPECT_EQ(typeid(bool), bool_val3.type());
    EXPECT_TRUE(std::any_cast<bool>(bool_val3));
    
    // Test string fallback for invalid numbers
    EXPECT_EQ(typeid(std::string), parser.convert_field("123abc", "").type());
    EXPECT_EQ(typeid(std::string), parser.convert_field("12.34.56", "").type());
    EXPECT_EQ(typeid(std::string), parser.convert_field("not_a_number", "").type());
    
    // Test null value handling
    auto null_val = parser.convert_field("", "");
    EXPECT_FALSE(null_val.has_value());
    
    auto null_val2 = parser.convert_field("NULL", "");
    EXPECT_FALSE(null_val2.has_value());
    
    auto null_val3 = parser.convert_field("null", "");
    EXPECT_FALSE(null_val3.has_value());
}

// Tests type inference edge cases extended
TEST_F(CsvFieldParserTest, TypeInferenceEdgeCasesExtended) {
    CsvFieldParser parser(options_);
    
    // Test number parsing edge cases
    EXPECT_EQ(typeid(long long), parser.convert_field("0", "").type());
    EXPECT_EQ(typeid(long long), parser.convert_field("-123", "").type());
    EXPECT_EQ(typeid(long long), parser.convert_field("+456", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("123.45", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("-123.45", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("1.23e10", "").type());
    EXPECT_EQ(typeid(double), parser.convert_field("1.23E-5", "").type());
    
    // Test boolean parsing
    auto bool_val1 = parser.convert_field("true", "");
    EXPECT_EQ(typeid(bool), bool_val1.type());
    EXPECT_TRUE(std::any_cast<bool>(bool_val1));
    
    auto bool_val2 = parser.convert_field("FALSE", "");
    EXPECT_EQ(typeid(bool), bool_val2.type());
    EXPECT_FALSE(std::any_cast<bool>(bool_val2));
    
    auto bool_val3 = parser.convert_field("1", "boolean");
    EXPECT_EQ(typeid(bool), bool_val3.type());
    EXPECT_TRUE(std::any_cast<bool>(bool_val3));
    
    // Test string fallback for invalid numbers
    EXPECT_EQ(typeid(std::string), parser.convert_field("123abc", "").type());
    EXPECT_EQ(typeid(std::string), parser.convert_field("12.34.56", "").type());
    EXPECT_EQ(typeid(std::string), parser.convert_field("not_a_number", "").type());
    
    // Test null value handling
    auto null_val = parser.convert_field("", "");
    EXPECT_FALSE(null_val.has_value());
    
    auto null_val2 = parser.convert_field("NULL", "");
    EXPECT_FALSE(null_val2.has_value());
    
    auto null_val3 = parser.convert_field("null", "");
    EXPECT_FALSE(null_val3.has_value());
}

// ============================================================================
// CsvExtractorTest Tests
// ============================================================================

// Test fixture for CSV extractor tests
class CsvExtractorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for test files
        test_dir_ = std::filesystem::temp_directory_path() / "omop_csv_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        // Clean up test files
        std::filesystem::remove_all(test_dir_);
    }

    // Helper function to create test CSV file
    std::string createTestCsv(const std::string& filename, const std::string& content) {
        std::filesystem::path filepath = test_dir_ / filename;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// Tests compressed CSV detection
TEST_F(CsvExtractorTest, CompressedCsvDetection) {
    // Test that compressed CSV detection works correctly
    EXPECT_TRUE(CsvExtractor::is_compressed_file("test.csv.gz"));
    EXPECT_TRUE(CsvExtractor::is_compressed_file("test.csv.bz2"));
    EXPECT_FALSE(CsvExtractor::is_compressed_file("test.csv"));
    EXPECT_FALSE(CsvExtractor::is_compressed_file("test.txt"));
}

// Tests CSV directory extractor
TEST_F(CsvExtractorTest, CsvDirectoryExtractor) {
    // Create test files in directory
    createTestCsv("data1.csv", "id,name\n1,John\n2,Jane\n");
    createTestCsv("data2.csv", "id,name\n3,Bob\n4,Alice\n");

    CsvDirectoryExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["directory"] = test_dir_.string();

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(4, batch.size());
}

// Tests CSV extraction with corrupted data
TEST_F(CsvExtractorTest, ExtractCorruptedCsv) {
    std::string csv_content =
        "name,age,city\n"
        "John,30,New York\n"
        "Jane,invalid_age,Los Angeles\n"  // Invalid age
        "Bob,,Chicago\n"                   // Missing age
        "Alice,25,\n";                     // Missing city

    std::string filepath = createTestCsv("corrupted.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["continue_on_error"] = true;

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    
    // Should extract all records, even with errors
    EXPECT_EQ(4, batch.size());
    
    // Check statistics for error reporting
    auto stats = extractor.get_statistics();
    EXPECT_TRUE(stats.contains("error_count"));
    // Should have some errors due to invalid data
    EXPECT_GE(std::any_cast<size_t>(stats["error_count"]), 0);
}

// Tests CSV extraction with custom delimiter
TEST_F(CsvExtractorTest, ExtractWithCustomDelimiter) {
    std::string csv_content =
        "name|age|city\n"
        "John|30|New York\n"
        "Jane|25|Los Angeles\n";

    std::string filepath = createTestCsv("pipe_delimited.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["delimiter"] = std::string("|");

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());
}

// Tests CSV extraction without header
TEST_F(CsvExtractorTest, ExtractWithoutHeader) {
    std::string csv_content =
        "John,30,New York\n"
        "Jane,25,Los Angeles\n";

    std::string filepath = createTestCsv("no_header.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["has_header"] = false;

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());
}

// Tests CSV extraction with column types
TEST_F(CsvExtractorTest, ExtractWithColumnTypes) {
    std::string csv_content =
        "id,name,age,active\n"
        "1,John,30,true\n"
        "2,Jane,25,false\n";

    std::string filepath = createTestCsv("typed.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["column_types"] = std::unordered_map<std::string, std::string>{
        {"id", "integer"},
        {"name", "string"},
        {"age", "integer"},
        {"active", "boolean"}
    };

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    // Use safer type checking and conversion
    auto age_field = records[0].getField("age");
    ASSERT_TRUE(age_field.type() == typeid(long long));
    EXPECT_EQ(30LL, std::any_cast<long long>(age_field));
    
    auto salary_field = records[0].getField("active");
    ASSERT_TRUE(salary_field.type() == typeid(bool));
    EXPECT_TRUE(std::any_cast<bool>(salary_field));
}

// Tests CSV extraction in batches
TEST_F(CsvExtractorTest, ExtractInBatches) {
    std::string csv_content = "id,name\n";
    for (int i = 1; i <= 25; ++i) {
        csv_content += std::to_string(i) + ",Person" + std::to_string(i) + "\n";
    }

    std::string filepath = createTestCsv("batch_test.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    // Extract in batches of 10
    auto batch1 = extractor.extract_batch(10, context);
    EXPECT_EQ(10, batch1.size());

    auto batch2 = extractor.extract_batch(10, context);
    EXPECT_EQ(10, batch2.size());

    auto batch3 = extractor.extract_batch(10, context);
    EXPECT_EQ(5, batch3.size());

    // No more data
    auto batch4 = extractor.extract_batch(10, context);
    EXPECT_EQ(0, batch4.size());
}

// Tests CSV extractor factory
TEST_F(CsvExtractorTest, CsvExtractorFactory) {
    // Test that factory can create CSV extractors
    auto extractor = CsvExtractorFactory::create("csv");
    ASSERT_NE(nullptr, extractor);
    EXPECT_EQ("csv", extractor->get_type());
}

// Tests getting statistics
TEST_F(CsvExtractorTest, GetStatistics) {
    std::string csv_content =
        "name,age,city\n"
        "John,30,New York\n"
        "Jane,25,Los Angeles\n";

    std::string filepath = createTestCsv("stats.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(2, batch.size());

    auto stats = extractor.get_statistics();
    EXPECT_TRUE(stats.contains("records_extracted"));
    EXPECT_TRUE(stats.contains("extraction_time"));
}

// Tests handling missing file
TEST_F(CsvExtractorTest, HandleMissingFile) {
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string("/nonexistent/file.csv");

    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), omop::common::ExtractionException);
}

// Tests column type inference
TEST_F(CsvExtractorTest, InferColumnTypes) {
    std::string csv_content =
        "int_col,float_col,bool_col,string_col\n"
        "123,45.67,true,hello\n"
        "456,78.90,false,world\n";

    std::string filepath = createTestCsv("infer_types.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["infer_types"] = true;

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();

    // Check inferred types with proper type checking
    const auto& int_val = records[0].getField("int_col");
    if (int_val.type() == typeid(long long)) {
        EXPECT_EQ(123LL, std::any_cast<long long>(int_val));
    } else if (int_val.type() == typeid(std::string)) {
        EXPECT_EQ("123", std::any_cast<std::string>(int_val));
    } else {
        FAIL() << "Unexpected type for int_col";
    }

    const auto& float_val = records[0].getField("float_col");
    if (float_val.type() == typeid(double)) {
        EXPECT_DOUBLE_EQ(45.67, std::any_cast<double>(float_val));
    } else if (float_val.type() == typeid(std::string)) {
        EXPECT_EQ("45.67", std::any_cast<std::string>(float_val));
    } else {
        FAIL() << "Unexpected type for float_col";
    }

    const auto& bool_val = records[0].getField("bool_col");
    EXPECT_TRUE(bool_val.type() == typeid(bool) || bool_val.type() == typeid(std::string));
}

// Tests maximum lines limit
TEST_F(CsvExtractorTest, MaxLines) {
    std::string csv_content = "id,name\n";
    for (int i = 1; i <= 20; ++i) {
        csv_content += std::to_string(i) + ",Person" + std::to_string(i) + "\n";
    }

    std::string filepath = createTestCsv("max_lines.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["max_lines"] = 10;

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(20, context);
    EXPECT_EQ(9, batch.size()); // 9 data rows (excluding header)

    auto records = batch.getRecords();
    for (size_t i = 0; i < std::min(records.size(), size_t(10)); ++i) {
        auto id_val = records[i].getField("id");
        if (id_val.type() == typeid(long long)) {
            EXPECT_EQ(static_cast<long long>(i + 1), std::any_cast<long long>(id_val));
        } else if (id_val.type() == typeid(int)) {
            EXPECT_EQ(i + 1, std::any_cast<int>(id_val));
        } else if (id_val.type() == typeid(std::string)) {
            EXPECT_EQ(std::to_string(i + 1), std::any_cast<std::string>(id_val));
        }
    }
}

// Tests multi-file CSV extractor
TEST_F(CsvExtractorTest, MultiFileCsvExtractor) {
    // Create multiple test files
    createTestCsv("multi1.csv", "id,name\n1,John\n2,Jane\n");
    createTestCsv("multi2.csv", "id,name\n3,Bob\n4,Alice\n");
    createTestCsv("multi3.csv", "id,name\n5,Charlie\n6,Diana\n");

    MultiFileCsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepaths"] = std::vector<std::string>{
        (test_dir_ / "multi1.csv").string(),
        (test_dir_ / "multi2.csv").string(),
        (test_dir_ / "multi3.csv").string()
    };

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(6, batch.size());
}

// Tests skipping lines
TEST_F(CsvExtractorTest, SkipLines) {
    std::string csv_content =
        "# This is a comment\n"
        "# Another comment\n"
        "name,age,city\n"
        "John,30,New York\n"
        "Jane,25,Los Angeles\n";

    std::string filepath = createTestCsv("skip_lines.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);
    config["skip_lines"] = 2;

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());

    auto records = batch.getRecords();
    EXPECT_EQ("John", records[0].getFieldAs<std::string>("name"));
    EXPECT_EQ("Jane", records[1].getFieldAs<std::string>("name"));
}

// Tests basic CSV extraction
TEST_F(CsvExtractorTest, ExtractSimpleCsv) {
    std::string csv_content =
        "name,age,city\n"
        "John,30,New York\n"
        "Jane,25,Los Angeles\n";

    std::string filepath = createTestCsv("simple.csv", csv_content);

    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = std::string(filepath);

    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));

    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());

    auto records = batch.getRecords();
    EXPECT_EQ("John", records[0].getFieldAs<std::string>("name"));
    const auto& age_val = records[0].getField("age");
    if (age_val.type() == typeid(std::string)) {
        EXPECT_EQ("30", std::any_cast<std::string>(age_val));
    } else if (age_val.type() == typeid(long long)) {
        EXPECT_EQ(30LL, std::any_cast<long long>(age_val));
    } else if (age_val.type() == typeid(int)) {
        EXPECT_EQ(30, std::any_cast<int>(age_val));
    } else {
        FAIL() << "Unexpected type for age: " << age_val.type().name();
    }
    EXPECT_EQ("New York", records[0].getFieldAs<std::string>("city"));
}
