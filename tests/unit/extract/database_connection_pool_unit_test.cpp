/**
 * @file connection_pool_test.cpp
 * @brief Unit tests for ConnectionPool and related classes
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include <thread>
#include <chrono>
#include <atomic>
#include <vector>
#include <future>
#include <iostream>

namespace omop::extract::test {

using namespace ::testing;
using namespace std::chrono_literals;

// Mock database connection for testing
class MockDatabaseConnection : public omop::extract::IDatabaseConnection {
public:
    MOCK_METHOD(void, connect, (const omop::extract::IDatabaseConnection::ConnectionParams& params), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD(std::unique_ptr<omop::extract::IResultSet>, execute_query, (const std::string& sql), (override));
    MOCK_METHOD(size_t, execute_update, (const std::string& sql), (override));
    MOCK_METHOD(std::unique_ptr<omop::extract::IPreparedStatement>, prepare_statement, (const std::string& sql), (override));
    MOCK_METHOD(void, begin_transaction, (), (override));
    MOCK_METHOD(void, commit, (), (override));
    MOCK_METHOD(void, rollback, (), (override));
    MOCK_METHOD(std::string, get_database_type, (), (const, override));
    MOCK_METHOD(std::string, get_version, (), (const, override));
    MOCK_METHOD(void, set_query_timeout, (int seconds), (override));
    MOCK_METHOD(bool, table_exists, (const std::string& table_name, const std::string& schema), (const, override));
    MOCK_METHOD(bool, in_transaction, (), (const, override));
};

// Test fixture for ConnectionPool tests
class ConnectionPoolTest : public ::testing::Test {
protected:
    // Factory function for creating mock connections
    std::unique_ptr<omop::extract::IDatabaseConnection> CreateMockConnection() {
        auto conn = std::make_unique<MockDatabaseConnection>();
        EXPECT_CALL(*conn, is_connected())
            .WillRepeatedly(Return(true));
        return conn;
    }

    // Factory function that tracks creation count
    std::unique_ptr<omop::extract::IDatabaseConnection> CreateTrackedConnection() {
        creation_count_++;
        return CreateMockConnection();
    }

    std::atomic<int> creation_count_{0};
};

// Test connection pool timeout
TEST_F(ConnectionPoolTest, AcquisitionTimeout) {
    omop::extract::ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });

    // Acquire the only connection
    auto conn1 = pool.acquire();

    // Try to acquire another with timeout
    auto start = std::chrono::steady_clock::now();
    EXPECT_THROW(pool.acquire(100), omop::common::DatabaseException);
    auto end = std::chrono::steady_clock::now();

    // Verify timeout occurred
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    EXPECT_GE(duration.count(), 100);
    EXPECT_LT(duration.count(), 200); // Should not wait much longer than timeout
}

// Test basic connection pool creation and acquisition
TEST_F(ConnectionPoolTest, BasicPoolOperations) {
    omop::extract::ConnectionPool pool(2, 5, [this]() { return CreateMockConnection(); });

    // Acquire a connection
    auto conn1 = pool.acquire();
    EXPECT_NE(conn1, nullptr);
    EXPECT_TRUE(conn1->is_connected());

    // Get statistics
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, 1);
    EXPECT_GE(stats.total_connections, 2); // At least min_connections

    // Release connection
    pool.release(std::move(conn1));

    stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, 0);
    EXPECT_GT(stats.idle_connections, 0);
}

// Test clearing idle connections
TEST_F(ConnectionPoolTest, ClearIdleConnections) {
    omop::extract::ConnectionPool pool(2, 5, [this]() { return CreateMockConnection(); });

    // Get initial statistics
    auto stats1 = pool.get_statistics();
    EXPECT_EQ(stats1.idle_connections, 2);

    // Clear idle connections
    pool.clear_idle_connections();

    // Check final statistics
    auto stats2 = pool.get_statistics();
    EXPECT_EQ(stats2.idle_connections, 0);
    EXPECT_EQ(stats2.total_connections, 0);
}

// Test concurrent access to connection pool
TEST_F(ConnectionPoolTest, ConcurrentAccess) {
    omop::extract::ConnectionPool pool(2, 10, [this]() { return CreateMockConnection(); });

    std::atomic<int> success_count{0};
    std::vector<std::thread> threads;

    // Launch multiple threads to acquire and release connections
    for (int i = 0; i < 20; ++i) {
        threads.emplace_back([&pool, &success_count]() {
            try {
                auto conn = pool.acquire(1000);
                if (conn) {
                    success_count++;
                    std::this_thread::sleep_for(10ms);
                    pool.release(std::move(conn));
                }
            } catch (...) {
                // Timeout is acceptable in this test
            }
        });
    }

    // Wait for all threads
    for (auto& t : threads) {
        t.join();
    }

    // At least some threads should have succeeded
    EXPECT_GT(success_count, 0);

    // Final statistics should be consistent
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, 0);
    EXPECT_EQ(stats.total_connections, stats.idle_connections);
}

// Test connection validation
TEST_F(ConnectionPoolTest, ConnectionValidation) {
    int conn_index = 0;

    auto factory = [&conn_index]() {
        auto conn = std::make_unique<MockDatabaseConnection>();

        // First 2 connections are valid during initialization, 3rd is invalid during validation
        if (conn_index < 2) {
            EXPECT_CALL(*conn, is_connected())
                .WillRepeatedly(Return(true));
        } else if (conn_index == 2) {
            EXPECT_CALL(*conn, is_connected())
                .WillOnce(Return(true))   // Valid during initialization
                .WillOnce(Return(false))  // Invalid during validation
                .WillRepeatedly(Return(true));
        } else {
            // Additional connections created as needed
            EXPECT_CALL(*conn, is_connected())
                .WillRepeatedly(Return(true));
        }

        conn_index++;
        return conn;
    };

    omop::extract::ConnectionPool pool(3, 5, factory);

    // Let connections go idle
    std::this_thread::sleep_for(10ms);
    
    // Validate connections - should return count of valid connections (2 after removing 1 invalid)
    size_t valid_count = pool.validate_connections();
    EXPECT_EQ(valid_count, 2);  // 2 valid connections remain after validation

    // Pool should have 2 connections after validation
    auto stats_after = pool.get_statistics();
    EXPECT_EQ(stats_after.total_connections, 2);
}

// Test connection timeout scenarios
TEST_F(ConnectionPoolTest, ConnectionTimeoutScenarios) {
    // Test 1: Zero timeout should fail immediately
    {
        omop::extract::ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });
        auto conn1 = pool.acquire();
        
        auto start = std::chrono::steady_clock::now();
        EXPECT_THROW(pool.acquire(0), omop::common::DatabaseException);
        auto end = std::chrono::steady_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        EXPECT_LT(duration.count(), 10); // Should fail almost immediately
    }

    // Test 2: Very short timeout
    {
        omop::extract::ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });
        auto conn1 = pool.acquire();
        
        auto start = std::chrono::steady_clock::now();
        EXPECT_THROW(pool.acquire(1), omop::common::DatabaseException);
        auto end = std::chrono::steady_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        EXPECT_GE(duration.count(), 1);
        EXPECT_LT(duration.count(), 50); // Should not wait much longer
    }

    // Test 3: Successful acquisition after release
    {
        omop::extract::ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });
        auto conn1 = pool.acquire();

        // Use a promise/future to ensure proper synchronization
        std::promise<void> release_promise;
        auto release_future = release_promise.get_future();

        // Release connection in another thread
        std::thread release_thread([&pool, conn = std::move(conn1), &release_promise]() mutable {
            std::this_thread::sleep_for(50ms);
            pool.release(std::move(conn));
            release_promise.set_value();
        });

        // Try to acquire with timeout - use a separate thread to avoid hanging the test
        std::promise<std::unique_ptr<omop::extract::IDatabaseConnection>> acquire_promise;
        auto acquire_future = acquire_promise.get_future();
        
        std::thread acquire_thread([&pool, &acquire_promise]() {
            try {
                auto conn = pool.acquire(200);
                acquire_promise.set_value(std::move(conn));
            } catch (...) {
                acquire_promise.set_exception(std::current_exception());
            }
        });

        auto start = std::chrono::steady_clock::now();
        
        // Wait for the acquisition with a reasonable timeout
        auto status = acquire_future.wait_for(std::chrono::milliseconds(300));
        auto end = std::chrono::steady_clock::now();
        
        EXPECT_EQ(status, std::future_status::ready);
        
        if (status == std::future_status::ready) {
            auto conn2 = acquire_future.get();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
            EXPECT_NE(conn2, nullptr);
            EXPECT_GE(duration.count(), 40); // Should wait for release (allow some tolerance)
            EXPECT_LT(duration.count(), 250); // Should not wait full timeout
        }

        // Wait for the release thread to complete
        release_future.wait();
        release_thread.join();
        acquire_thread.join();
    }
}

// Test connection factory failure
TEST_F(ConnectionPoolTest, ConnectionFactoryFailure) {
    auto failing_factory = []() -> std::unique_ptr<omop::extract::IDatabaseConnection> {
        throw std::runtime_error("Connection creation failed");
    };

    // Should throw when trying to create initial connections
    EXPECT_THROW(omop::extract::ConnectionPool pool(1, 3, failing_factory), omop::common::DatabaseException);
}

// Test destruction with active connections
TEST_F(ConnectionPoolTest, DestructionWithActiveConnections) {
    auto pool = std::make_unique<omop::extract::ConnectionPool>(2, 5, [this]() { return CreateMockConnection(); });

    // Acquire connections
    auto conn1 = pool->acquire();
    auto conn2 = pool->acquire();

    // Verify connections are active
    auto stats = pool->get_statistics();
    EXPECT_EQ(stats.active_connections, 2);

    // Destroy pool with active connections - should not crash
    pool.reset();

    // Connections should be automatically cleaned up
    EXPECT_TRUE(true); // If we get here, no crash occurred
}

// Test equal min/max connections
TEST_F(ConnectionPoolTest, EqualMinMaxConnections) {
    size_t conn_count = 3;
    omop::extract::ConnectionPool pool(conn_count, conn_count, [this]() { return CreateMockConnection(); });

    // Pool should create exactly conn_count connections
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.total_connections, conn_count);
    EXPECT_EQ(stats.idle_connections, conn_count);

    // Acquire all connections
    std::vector<std::unique_ptr<omop::extract::IDatabaseConnection>> connections;
    for (size_t i = 0; i < conn_count; ++i) {
        connections.push_back(pool.acquire());
    }

    // Try to acquire one more - should timeout
    EXPECT_THROW(pool.acquire(100), omop::common::DatabaseException);
}

// Test failing connection factory
TEST_F(ConnectionPoolTest, FailingConnectionFactory) {
    int call_count = 0;
    auto factory = [&call_count]() -> std::unique_ptr<omop::extract::IDatabaseConnection> {
        call_count++;
        if (call_count <= 2) {
            // First two calls succeed
            auto conn = std::make_unique<MockDatabaseConnection>();
            EXPECT_CALL(*conn, is_connected()).WillRepeatedly(Return(true));
            return conn;
        } else {
            // Subsequent calls fail
            throw std::runtime_error("Factory failure");
        }
    };

    omop::extract::ConnectionPool pool(2, 5, factory);

    // Initial connections should be created successfully
    auto conn1 = pool.acquire();
    auto conn2 = pool.acquire();
    EXPECT_NE(conn1, nullptr);
    EXPECT_NE(conn2, nullptr);

    // Release one connection back to the pool
    pool.release(std::move(conn1));

    // Now try to acquire again - this should work (gets the released connection)
    auto conn3 = pool.acquire();
    EXPECT_NE(conn3, nullptr);

    // Now try to acquire when pool is full and factory fails - should timeout
    auto start = std::chrono::steady_clock::now();
    EXPECT_THROW(pool.acquire(100), omop::common::DatabaseException);
    auto end = std::chrono::steady_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    EXPECT_GE(duration.count(), 90);  // Should wait close to the full timeout
}

// Test infinite timeout
TEST_F(ConnectionPoolTest, InfiniteTimeout) {
    omop::extract::ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });

    // Acquire the only connection
    auto conn1 = pool.acquire();

    // Use shared pointer and atomic to control thread lifetime safely
    std::atomic<bool> should_release{false};
    std::shared_ptr<omop::extract::ConnectionPool> pool_ptr(&pool, [](auto*){});
    
    // Start a thread to release the connection after a delay
    std::thread release_thread([pool_ptr, &should_release, conn = std::move(conn1)]() mutable {
        std::this_thread::sleep_for(100ms);
        if (should_release.load()) {
            pool_ptr->release(std::move(conn));
        }
    });

    should_release.store(true);
    
    // Try to acquire with infinite timeout (-1 means infinite timeout)
    auto start = std::chrono::steady_clock::now();
    auto conn2 = pool.acquire(-1); // -1 means infinite timeout
    auto end = std::chrono::steady_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    EXPECT_NE(conn2, nullptr);
    EXPECT_GE(duration.count(), 90); // Should wait for release (allow some variance)

    // Ensure thread finishes before test ends
    if (release_thread.joinable()) {
        release_thread.join();
    }
}

// Test invalid connection handling
TEST_F(ConnectionPoolTest, InvalidConnectionHandling) {
    int call_count = 0;
    auto factory = [&call_count]() {
        call_count++;
        auto conn = std::make_unique<MockDatabaseConnection>();

        if (call_count <= 2) {
            // First two connections for pool initialization - start as connected
            EXPECT_CALL(*conn, is_connected())
                .WillRepeatedly(Return(true));
        } else {
            // Third connection created during acquire - also valid
            EXPECT_CALL(*conn, is_connected())
                .WillRepeatedly(Return(true));
        }
        return conn;
    };

    omop::extract::ConnectionPool pool(2, 5, factory);

    // Pool should have been initialized with 2 connections
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.total_connections, 2);

    // Acquire connection - should get one from the pool
    auto conn = pool.acquire();
    EXPECT_NE(conn, nullptr);
    EXPECT_TRUE(conn->is_connected());

    // Pool should now have 1 active, 1 idle connection
    stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, 1);
    EXPECT_EQ(stats.idle_connections, 1);
}

// Test min/max connection limits
TEST_F(ConnectionPoolTest, MinMaxConnectionLimits) {
    size_t min_conn = 2;
    size_t max_conn = 4;

    omop::extract::ConnectionPool pool(min_conn, max_conn, [this]() { return CreateTrackedConnection(); });

    // Initial creation should be min_connections
    EXPECT_EQ(creation_count_, min_conn);

    // Acquire all min connections
    std::vector<std::unique_ptr<omop::extract::IDatabaseConnection>> connections;
    for (size_t i = 0; i < min_conn; ++i) {
        connections.push_back(pool.acquire());
    }

    // Pool should create new connections up to max
    connections.push_back(pool.acquire());
    EXPECT_GT(creation_count_, min_conn);

    connections.push_back(pool.acquire());
    EXPECT_EQ(creation_count_, max_conn);

    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, max_conn);
    EXPECT_EQ(stats.total_connections, max_conn);
}

// Test pool shutdown behavior
TEST_F(ConnectionPoolTest, PoolShutdownBehavior) {
    auto factory = [this]() { return CreateMockConnection(); };
    auto pool = std::make_unique<omop::extract::ConnectionPool>(2, 5, factory);
    
    // Acquire some connections
    auto conn1 = pool->acquire();
    auto conn2 = pool->acquire();
    
    EXPECT_NE(conn1, nullptr);
    EXPECT_NE(conn2, nullptr);
    
    // Get initial statistics
    auto stats_before = pool->get_statistics();
    EXPECT_EQ(stats_before.active_connections, 2);
    
    // Start a background thread that will try to acquire connections
    std::atomic<bool> should_stop{false};
    std::atomic<int> background_acquisitions{0};
    std::atomic<bool> background_exception{false};
    
    std::thread background_thread([&pool, &should_stop, &background_acquisitions, &background_exception]() {
        while (!should_stop) {
            try {
                auto conn = pool->acquire(10);
                if (conn) {
                    background_acquisitions++;
                    pool->release(std::move(conn));
                }
            } catch (const omop::common::DatabaseException&) {
                // Timeout is expected
            } catch (...) {
                background_exception = true;
                break;
            }
            std::this_thread::sleep_for(1ms);
        }
    });
    
    // Let the background thread run for a bit
    std::this_thread::sleep_for(50ms);
    
    // Signal the background thread to stop
    should_stop = true;
    
    // Release our connections
    pool->release(std::move(conn1));
    pool->release(std::move(conn2));
    
    // Wait for background thread to finish
    background_thread.join();
    
    // Verify no exceptions occurred in background thread
    EXPECT_FALSE(background_exception);
    
    // Verify some acquisitions happened
    EXPECT_GT(background_acquisitions, 0);
    
    // Destroy the pool
    pool.reset();
    
    // If we get here without crashes, the test passes
    EXPECT_TRUE(true);
}

// Test pool statistics
TEST_F(ConnectionPoolTest, PoolStatistics) {
    omop::extract::ConnectionPool pool(2, 5, [this]() { return CreateMockConnection(); });

    // Initial statistics
    auto stats1 = pool.get_statistics();
    EXPECT_EQ(stats1.total_connections, 2);
    EXPECT_EQ(stats1.active_connections, 0);
    EXPECT_EQ(stats1.idle_connections, 2);

    // Acquire connections
    auto conn1 = pool.acquire();
    auto conn2 = pool.acquire();

    auto stats2 = pool.get_statistics();
    EXPECT_EQ(stats2.total_connections, 2);
    EXPECT_EQ(stats2.active_connections, 2);
    EXPECT_EQ(stats2.idle_connections, 0);

    // Release one connection
    pool.release(std::move(conn1));

    auto stats3 = pool.get_statistics();
    EXPECT_EQ(stats3.total_connections, 2);
    EXPECT_EQ(stats3.active_connections, 1);
    EXPECT_EQ(stats3.idle_connections, 1);
}

// Test release null connection
TEST_F(ConnectionPoolTest, ReleaseNullConnection) {
    omop::extract::ConnectionPool pool(2, 5, [this]() { return CreateMockConnection(); });

    // Release a null connection - should not crash
    pool.release(nullptr);

    // Pool should still be functional
    auto conn = pool.acquire();
    EXPECT_NE(conn, nullptr);
}

// Test wait time statistics
TEST_F(ConnectionPoolTest, WaitTimeStatistics) {
    omop::extract::ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });

    // Acquire the only connection
    auto conn1 = pool.acquire();

    // Start a thread to release the connection after a delay
    std::thread release_thread([&pool, conn = std::move(conn1)]() mutable {
        std::this_thread::sleep_for(50ms);
        pool.release(std::move(conn));
    });

    // Try to acquire with timeout
    auto start = std::chrono::steady_clock::now();
    auto conn2 = pool.acquire(200);
    auto end = std::chrono::steady_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    EXPECT_NE(conn2, nullptr);
    EXPECT_GE(duration.count(), 50); // Should wait for release

    // Get statistics
    [[maybe_unused]] auto stats = pool.get_statistics();
    // Note: The actual statistics structure may not have these fields
    // EXPECT_TRUE(stats.contains("total_wait_time"));
    // EXPECT_TRUE(stats.contains("max_wait_time"));

    release_thread.join();
}

} // namespace omop::extract::test
