/**
 * @file validation_result_test.cpp
 * @brief Unit tests for ValidationResult class with UK localisation
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "core/interfaces.h"
#include "common/utilities.h"
#include <string>
#include <vector>
#include <locale>
#include <iomanip>
#include <sstream>

using namespace omop::core;

// Test fixture for ValidationResult tests with UK localisation
class ValidationResultTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        result_ = std::make_unique<omop::common::ValidationResult>();
        
        // UK-specific formatting
        uk_currency_symbol_ = "£";
        uk_date_format_ = "DD/MM/YYYY";
    }

    std::unique_ptr<omop::common::ValidationResult> result_;
    std::string uk_currency_symbol_;
    std::string uk_date_format_;

    // Helper to format UK postal codes
    bool isValidUKPostcode(const std::string& postcode) {
        // Basic UK postcode validation
        return postcode.length() >= 6 && postcode.length() <= 8 &&
               postcode.find(' ') != std::string::npos;
    }
};

// Test that ValidationResult creates valid result by default
TEST_F(ValidationResultTest, DefaultValidationResultIsValid) {
    omop::common::ValidationResult result;
    EXPECT_TRUE(result.is_valid());
    EXPECT_TRUE(result.errors().empty());
    EXPECT_EQ(0u, result.error_count());
    EXPECT_TRUE(result.error_messages().empty());
}

// Test that adding error makes result invalid
TEST_F(ValidationResultTest, AddingErrorMakesResultInvalid) {
    EXPECT_TRUE(result_->is_valid());
    
    result_->add_error("nhs_number", "Invalid NHS number format", "nhs_number_validation");
    
    EXPECT_FALSE(result_->is_valid());
    EXPECT_EQ(1u, result_->error_count());
    EXPECT_EQ(1u, result_->errors().size());
    
    const auto& error = result_->errors()[0];
    EXPECT_EQ("nhs_number", error.field_name);
    EXPECT_EQ("Invalid NHS number format", error.error_message);
    EXPECT_EQ("nhs_number_validation", error.rule_name);
}

// Test that adding multiple UK-specific validation errors works correctly
TEST_F(ValidationResultTest, AddingMultipleUKSpecificErrors) {
    result_->add_error("birth_date", "Date must be in DD/MM/YYYY format", "uk_date_format");
    result_->add_error("postcode", "Invalid UK postcode format", "uk_postcode_validation");
    result_->add_error("prescription_cost", "Cost must be in GBP (£)", "uk_currency_validation");
    
    EXPECT_FALSE(result_->is_valid());
    EXPECT_EQ(3u, result_->error_count());
    EXPECT_EQ(3u, result_->errors().size());
    
    // Verify UK-specific error messages
    std::string messages = result_->error_messages();
    EXPECT_NE(messages.find("DD/MM/YYYY"), std::string::npos);
    EXPECT_NE(messages.find("UK postcode"), std::string::npos);
    EXPECT_NE(messages.find("GBP"), std::string::npos);
}

// Test that error message formatting includes UK-specific details
TEST_F(ValidationResultTest, ErrorMessageFormattingWithUKDetails) {
    std::string uk_currency_error = "Value " + omop::common::UKLocalization::format_uk_currency(123.45) + " exceeds maximum allowed";
    result_->add_error("treatment_cost", uk_currency_error, "cost_validation");
    
    std::string messages = result_->error_messages();
    EXPECT_FALSE(messages.empty());
    EXPECT_NE(messages.find("£123.45"), std::string::npos);
    EXPECT_NE(messages.find("treatment_cost"), std::string::npos);
    EXPECT_NE(messages.find("cost_validation"), std::string::npos);
}

// Test that merging empty validation results works correctly
TEST_F(ValidationResultTest, MergingEmptyValidationResults) {
    omop::common::ValidationResult other;
    
    EXPECT_TRUE(result_->is_valid());
    EXPECT_TRUE(other.is_valid());
    
    result_->merge(other);
    
    EXPECT_TRUE(result_->is_valid());
    EXPECT_EQ(0u, result_->error_count());
}

// Test that merging validation results with UK healthcare errors works
TEST_F(ValidationResultTest, MergingValidationResultsWithUKHealthcareErrors) {
    result_->add_error("patient_id", "Patient ID not found in NHS database", "nhs_patient_lookup");
    
    omop::common::ValidationResult other;
    other.add_error("gp_practice", "GP practice code invalid", "gp_practice_validation");
    other.add_error("treatment_date", "Treatment date is in the future", "temporal_validation");
    
    result_->merge(other);
    
    EXPECT_FALSE(result_->is_valid());
    EXPECT_EQ(3u, result_->error_count());
    
    // Verify all UK healthcare-specific errors are present
    const auto& errors = result_->errors();
    EXPECT_EQ("patient_id", errors[0].field_name);
    EXPECT_EQ("gp_practice", errors[1].field_name);
    EXPECT_EQ("treatment_date", errors[2].field_name);
}

// Test that UK postcode validation errors are handled correctly
TEST_F(ValidationResultTest, PostcodeValidationErrorHandling) {
    std::vector<std::string> invalid_postcodes = {
        "INVALID",
        "123456",
        "SW1A1AA", // Missing space
        "SW1A 1AAA" // Too long
    };
    
    for (size_t i = 0; i < invalid_postcodes.size(); ++i) {
        std::string field_name = "postcode_" + std::to_string(i);
        std::string error_msg = "Invalid UK postcode: " + invalid_postcodes[i];
        result_->add_error(field_name, error_msg, "uk_postcode_format");
    }
    
    EXPECT_FALSE(result_->is_valid());
    EXPECT_EQ(4u, result_->error_count());
    
    std::string messages = result_->error_messages();
    for (const auto& postcode : invalid_postcodes) {
        EXPECT_NE(messages.find(postcode), std::string::npos);
    }
}

// Test that UK currency validation errors are properly formatted
TEST_F(ValidationResultTest, UKCurrencyValidationErrorFormatting) {
    std::vector<double> test_amounts = {999.99, 1234.56, 0.01, 999999.99};
    
    for (size_t i = 0; i < test_amounts.size(); ++i) {
        std::string field_name = "amount_" + std::to_string(i);
        std::string formatted_amount = omop::common::UKLocalization::format_uk_currency(test_amounts[i]);
        std::string error_msg = "Amount " + formatted_amount + " exceeds NHS funding limit";
        result_->add_error(field_name, error_msg, "nhs_funding_validation");
    }
    
    EXPECT_FALSE(result_->is_valid());
    EXPECT_EQ(4u, result_->error_count());
    
    std::string messages = result_->error_messages();
    EXPECT_NE(messages.find("£999.99"), std::string::npos);
    EXPECT_NE(messages.find("£1,234.56"), std::string::npos);
    EXPECT_NE(messages.find("£0.01"), std::string::npos);
    EXPECT_NE(messages.find("£999,999.99"), std::string::npos);
}

// Test that NHS number validation works with UK-specific formatting
TEST_F(ValidationResultTest, NHSNumberValidationWithUKFormatting) {
    std::vector<std::string> invalid_nhs_numbers = {
        "123456789",    // Too short
        "12345678901",  // Too long
        "************", // Invalid format
        "ABC1234567"    // Contains letters
    };
    
    for (size_t i = 0; i < invalid_nhs_numbers.size(); ++i) {
        std::string error_msg = "NHS Number '" + invalid_nhs_numbers[i] + 
                               "' does not match format ************";
        result_->add_error("nhs_number", error_msg, "nhs_number_format");
    }
    
    EXPECT_FALSE(result_->is_valid());
    EXPECT_EQ(4u, result_->error_count());
    
    std::string messages = result_->error_messages();
    EXPECT_NE(messages.find("************"), std::string::npos); // UK NHS number format
}

// Test that UK date validation handles DD/MM/YYYY format
TEST_F(ValidationResultTest, UKDateValidationHandling) {
    std::vector<std::string> invalid_dates = {
        "2025-01-15",    // ISO format (not UK)
        "01/15/2025",    // US format (not UK)
        "32/01/2025",    // Invalid day
        "15/13/2025"     // Invalid month
    };
    
    for (const auto& date : invalid_dates) {
        std::string error_msg = "Date '" + date + "' must be in DD/MM/YYYY format";
        result_->add_error("date_field", error_msg, "uk_date_format");
    }
    
    EXPECT_FALSE(result_->is_valid());
    EXPECT_EQ(4u, result_->error_count());
    
    std::string messages = result_->error_messages();
    EXPECT_NE(messages.find("DD/MM/YYYY"), std::string::npos);
}

// Test that validation handles edge cases with empty strings
TEST_F(ValidationResultTest, ValidationHandlesEmptyStringEdgeCases) {
    result_->add_error("", "Error with empty field name", "empty_field_rule");
    result_->add_error("field_name", "", "empty_message_rule");
    result_->add_error("another_field", "Error message", "");
    
    EXPECT_FALSE(result_->is_valid());
    EXPECT_EQ(3u, result_->error_count());
    
    const auto& errors = result_->errors();
    EXPECT_EQ("", errors[0].field_name);
    EXPECT_EQ("", errors[1].error_message);
    EXPECT_EQ("", errors[2].rule_name);
}

// Test that validation handles large numbers of errors efficiently
TEST_F(ValidationResultTest, ValidationHandlesLargeNumbersOfErrors) {
    const size_t num_errors = 1000;
    
    for (size_t i = 0; i < num_errors; ++i) {
        std::string field_name = "field_" + std::to_string(i);
        std::string error_msg = "Validation error " + std::to_string(i);
        std::string rule_name = "rule_" + std::to_string(i % 10);
        
        result_->add_error(field_name, error_msg, rule_name);
    }
    
    EXPECT_FALSE(result_->is_valid());
    EXPECT_EQ(num_errors, result_->error_count());
    EXPECT_EQ(num_errors, result_->errors().size());
    
    // Error messages should still work with many errors
    std::string messages = result_->error_messages();
    EXPECT_FALSE(messages.empty());
    EXPECT_GT(messages.length(), num_errors * 20); // At least 20 chars per error
}

// Test that UK medical coding validation works correctly
TEST_F(ValidationResultTest, UKMedicalCodingValidationErrors) {
    // Test SNOMED CT code validation (UK standard)
    result_->add_error("diagnosis_code", "SNOMED CT code 12345678901234567890 is invalid", "snomed_validation");
    
    // Test Read code validation (UK legacy system)
    result_->add_error("read_code", "Read code 'XYZ123' format invalid", "read_code_validation");
    
    // Test ICD-10 code validation
    result_->add_error("icd10_code", "ICD-10 code 'Z99.9' not recognised", "icd10_validation");
    
    EXPECT_FALSE(result_->is_valid());
    EXPECT_EQ(3u, result_->error_count());
    
    std::string messages = result_->error_messages();
    EXPECT_NE(messages.find("SNOMED CT"), std::string::npos);
    EXPECT_NE(messages.find("Read code"), std::string::npos);
    EXPECT_NE(messages.find("ICD-10"), std::string::npos);
}

// Test that validation result merging preserves UK-specific error context
TEST_F(ValidationResultTest, ValidationMergingPreservesUKContext) {
    // Primary validation with UK NHS context
    result_->add_error("patient_nhs_number", "NHS number checksum validation failed", "nhs_checksum");
    
    // Secondary validation with UK address context
    omop::common::ValidationResult address_validation;
    address_validation.add_error("postcode", "Postcode 'M60 1QD' not found in Royal Mail database", "royal_mail_lookup");
    address_validation.add_error("county", "County 'Greater Manchester' requires specific formatting", "uk_county_format");
    
    result_->merge(address_validation);
    
    EXPECT_FALSE(result_->is_valid());
    EXPECT_EQ(3u, result_->error_count());
    
    std::string messages = result_->error_messages();
    EXPECT_NE(messages.find("NHS number"), std::string::npos);
    EXPECT_NE(messages.find("Royal Mail"), std::string::npos);
    EXPECT_NE(messages.find("Greater Manchester"), std::string::npos);
}

// Test that validation handles Unicode characters in UK context
TEST_F(ValidationResultTest, ValidationHandlesUnicodeInUKContext) {
    // Test with Welsh place names (contain Unicode characters)
    result_->add_error("place_name", "Welsh place name 'Llanfairpwllgwyngyllgogerychwyrndrobwllllantysiliogogogoch' too long", "welsh_place_validation");
    
    // Test with accented characters in names
    result_->add_error("patient_name", "Name 'José María' contains non-ASCII characters", "name_ascii_validation");
    
    EXPECT_FALSE(result_->is_valid());
    EXPECT_EQ(2u, result_->error_count());
    
    std::string messages = result_->error_messages();
    EXPECT_NE(messages.find("Welsh"), std::string::npos);
    EXPECT_NE(messages.find("José"), std::string::npos);
}