/**
 * @file component_factory_test.cpp
 * @brief Unit tests for component factory implementation
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/interfaces.h"
#include "core/component_factory.h"
#include "common/exceptions.h"

namespace omop::core {

using namespace testing;

// Test verifies that component factory initialization succeeds without errors
TEST(ComponentFactoryInitTest, InitializesSuccessfully) {
    // Test that factories can be initialized without error
    EXPECT_NO_THROW(initialize_component_factories());
    
    // Test multiple initializations are safe
    EXPECT_NO_THROW(initialize_component_factories());
}

// Test verifies that extractor factory can create placeholder extractor successfully
TEST(ExtractorFactoryTest, CanCreatePlaceholderExtractor) {
    auto& factory = get_extractor_factory();
    
    EXPECT_TRUE(factory.is_registered("placeholder"));
    
    auto extractor = factory.create("placeholder");
    ASSERT_NE(extractor, nullptr);
    EXPECT_EQ(extractor->get_type(), "placeholder");
    EXPECT_FALSE(extractor->has_more_data());
}

// Test verifies that transformer factory can create placeholder transformer successfully
TEST(TransformerFactoryTest, CanCreatePlaceholderTransformer) {
    auto& factory = get_transformer_factory();
    
    EXPECT_TRUE(factory.is_registered("placeholder"));
    
    auto transformer = factory.create("placeholder");
    ASSERT_NE(transformer, nullptr);
    EXPECT_EQ(transformer->get_type(), "placeholder");
}

// Test verifies that create_extractor function creates valid extractor instances
TEST(CreateExtractorTest, CreatesValidExtractor) {
    std::unordered_map<std::string, std::any> config;
    
    auto extractor = create_extractor("placeholder", config);
    ASSERT_NE(extractor, nullptr);
    EXPECT_EQ(extractor->get_type(), "placeholder");
}

// Test verifies that create_extractor throws exception for unknown extractor types
TEST(CreateExtractorTest, ThrowsOnUnknownType) {
    std::unordered_map<std::string, std::any> config;
    
    EXPECT_THROW(create_extractor("unknown_type", config), 
                 common::ConfigurationException);
}

// Test verifies that create_transformer function creates valid transformer instances
TEST(CreateTransformerTest, CreatesValidTransformer) {
    std::unordered_map<std::string, std::any> config;
    
    auto transformer = create_transformer("placeholder", config);
    ASSERT_NE(transformer, nullptr);
    EXPECT_EQ(transformer->get_type(), "placeholder");
}

// Test verifies that create_transformer throws exception for unknown transformer types
TEST(CreateTransformerTest, ThrowsOnUnknownType) {
    std::unordered_map<std::string, std::any> config;
    
    EXPECT_THROW(create_transformer("unknown_type", config), 
                 common::ConfigurationException);
}

// Test verifies that create_loader throws exception for unknown loader types
TEST(CreateLoaderTest, ThrowsOnUnknownType) {
    std::unordered_map<std::string, std::any> config;
    
    EXPECT_THROW(create_loader("unknown_type", config), 
                 common::ConfigurationException);
}

// Test verifies that custom component types can be registered and created successfully
TEST(RegistrationTest, CanRegisterCustomTypes) {
    // Test extractor registration
    register_extractor_type("test_extractor", []() {
        class TestExtractor : public IExtractor {
        public:
            void initialize(const std::unordered_map<std::string, std::any>&,
                          ProcessingContext&) override {}
            RecordBatch extract_batch(size_t, ProcessingContext&) override {
                return RecordBatch();
            }
            bool has_more_data() const override { return false; }
            std::string get_type() const override { return "test_extractor"; }
            void finalize(ProcessingContext&) override {}
            std::unordered_map<std::string, std::any> get_statistics() const override {
                return {};
            }
        };
        return std::make_unique<TestExtractor>();
    });
    
    auto types = get_registered_extractor_types();
    EXPECT_TRUE(std::find(types.begin(), types.end(), "test_extractor") != types.end());
    
    auto extractor = create_extractor("test_extractor", {});
    ASSERT_NE(extractor, nullptr);
    EXPECT_EQ(extractor->get_type(), "test_extractor");
}

} // namespace omop::core