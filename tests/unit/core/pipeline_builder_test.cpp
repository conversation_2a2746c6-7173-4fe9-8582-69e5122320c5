/**
 * @file pipeline_builder_test.cpp
 * @brief Unit tests for pipeline builder functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/pipeline.h"
#include "core/interfaces.h"
#include "common/exceptions.h"
#include <fstream>
#include <filesystem>

namespace omop::core {

using namespace testing;

// Simple concrete implementations instead of mocks to avoid GoogleMock issues
class TestExtractor : public IExtractor {
private:
    bool initialized_ = false;
    size_t records_produced_ = 0;
    size_t total_records_ = 10;

public:
    void initialize(const std::unordered_map<std::string, std::any>& config, ProcessingContext& context) override {
        initialized_ = true;
    }

    RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) override {
        RecordBatch batch;
        size_t to_extract = std::min(batch_size, total_records_ - records_produced_);
        
        for (size_t i = 0; i < to_extract; ++i) {
            Record record;
            record.setField("id", records_produced_ + i);
            batch.addRecord(record);
        }
        
        records_produced_ += to_extract;
        return batch;
    }

    bool has_more_data() const override {
        return records_produced_ < total_records_;
    }

    std::string get_type() const override { return "test"; }

    void finalize(ProcessingContext& context) override {}

    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {{"records_extracted", records_produced_}};
    }
};

class TestTransformer : public ITransformer {
public:
    void initialize(const std::unordered_map<std::string, std::any>& config, ProcessingContext& context) override {}

    std::optional<Record> transform(const Record& record, ProcessingContext& context) override {
        return record; // Pass through
    }

    RecordBatch transform_batch(const RecordBatch& batch, ProcessingContext& context) override {
        return batch; // Pass through
    }

    std::string get_type() const override { return "test"; }

    omop::common::ValidationResult validate(const Record& record) const override {
        return omop::common::ValidationResult{}; // Default constructor creates valid result
    }

    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {{"records_transformed", 0}};
    }
};

class TestLoader : public ILoader {
private:
    size_t loaded_count_ = 0;

public:
    void initialize(const std::unordered_map<std::string, std::any>& config, ProcessingContext& context) override {}

    bool load(const Record& record, ProcessingContext& context) override {
        loaded_count_++;
        return true;
    }

    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override {
        loaded_count_ += batch.size();
        return batch.size();
    }

    void commit(ProcessingContext& context) override {}

    void rollback(ProcessingContext& context) override {}

    std::string get_type() const override { return "test"; }

    void finalize(ProcessingContext& context) override {}

    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {{"records_loaded", loaded_count_}};
    }
};

class PipelineBuilderTest : public ::testing::Test {
protected:
    PipelineBuilder builder;
};

// Test default construction
TEST_F(PipelineBuilderTest, DefaultConstruction) {
    EXPECT_NO_THROW(PipelineBuilder{});
}

// Test configuration setting
TEST_F(PipelineBuilderTest, WithConfig) {
    PipelineConfig config;
    config.batch_size = 500;
    config.max_parallel_batches = 8;
    config.error_threshold = 0.05;
    
    EXPECT_NO_THROW(builder.with_config(config));
}

// Test extractor setting
TEST_F(PipelineBuilderTest, WithExtractor) {
    auto extractor = std::make_unique<TestExtractor>();
    
    EXPECT_NO_THROW(builder.with_extractor(std::move(extractor)));
}

// Test transformer setting
TEST_F(PipelineBuilderTest, WithTransformer) {
    auto transformer = std::make_unique<TestTransformer>();
    
    EXPECT_NO_THROW(builder.with_transformer(std::move(transformer)));
}

// Test loader setting
TEST_F(PipelineBuilderTest, WithLoader) {
    auto loader = std::make_unique<TestLoader>();
    
    EXPECT_NO_THROW(builder.with_loader(std::move(loader)));
}

// Test null component validation
TEST_F(PipelineBuilderTest, RejectsNullComponents) {
    EXPECT_THROW(builder.with_extractor(nullptr), common::ConfigurationException);
    EXPECT_THROW(builder.with_transformer(nullptr), common::ConfigurationException);
    EXPECT_THROW(builder.with_loader(nullptr), common::ConfigurationException);
}

// Test callback registration
TEST_F(PipelineBuilderTest, WithCallbacks) {
    auto progress_callback = [](const JobInfo& info) {};
    auto error_callback = [](const std::string& msg, const std::exception& ex) {};
    
    EXPECT_NO_THROW(builder.with_progress_callback(progress_callback));
    EXPECT_NO_THROW(builder.with_error_callback(error_callback));
}

// Test null callback rejection
TEST_F(PipelineBuilderTest, RejectsNullCallbacks) {
    EXPECT_THROW(builder.with_progress_callback(nullptr), common::ConfigurationException);
    EXPECT_THROW(builder.with_error_callback(nullptr), common::ConfigurationException);
}

// Test processor registration
TEST_F(PipelineBuilderTest, WithProcessors) {
    auto pre_processor = [](RecordBatch& batch, ProcessingContext& ctx) {};
    auto post_processor = [](RecordBatch& batch, ProcessingContext& ctx) {};
    
    EXPECT_NO_THROW(builder.with_pre_processor(pre_processor));
    EXPECT_NO_THROW(builder.with_post_processor(post_processor));
}

// Test null processor rejection
TEST_F(PipelineBuilderTest, RejectsNullProcessors) {
    EXPECT_THROW(builder.with_pre_processor(nullptr), common::ConfigurationException);
    EXPECT_THROW(builder.with_post_processor(nullptr), common::ConfigurationException);
}

// Test pipeline building
TEST_F(PipelineBuilderTest, BuildsPipeline) {
    auto pipeline = builder.build();
    EXPECT_NE(pipeline, nullptr);
}

// Test config file loading
TEST_F(PipelineBuilderTest, WithConfigFile) {
    // Create temporary config file
    std::filesystem::create_directories("test_config");
    std::ofstream file("test_config/pipeline.yaml");
    file << "pipeline:\n"
         << "  batch_size: 100\n"
         << "  max_parallel_batches: 4\n"
         << "  error_threshold: 0.05\n";
    file.close();
    
    EXPECT_NO_THROW(builder.with_config_file("test_config/pipeline.yaml"));
    
    // Cleanup
    std::filesystem::remove_all("test_config");
}

// Test invalid config file handling
TEST_F(PipelineBuilderTest, RejectsInvalidConfigFile) {
    EXPECT_THROW(builder.with_config_file("nonexistent.yaml"), common::ConfigurationException);
}

// Test method chaining
TEST_F(PipelineBuilderTest, SupportsMethodChaining) {
    auto config = PipelineConfig{};
    config.batch_size = 100;
    
    auto pipeline = builder
        .with_config(config)
        .with_extractor(std::make_unique<TestExtractor>())
        .with_transformer(std::make_unique<TestTransformer>())
        .with_loader(std::make_unique<TestLoader>())
        .build();
    
    EXPECT_NE(pipeline, nullptr);
}

} // namespace omop::core