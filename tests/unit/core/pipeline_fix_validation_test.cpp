/**
 * @file pipeline_fix_validation_test.cpp
 * @brief Unit tests to validate pipeline fixes
 * <AUTHOR> Code Assistant
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/pipeline.h"
#include "core/interfaces.h"
#include "common/logging.h"
#include <thread>
#include <chrono>

using namespace omop::core;
using namespace testing;

// Mock components for testing using older MOCK_METHOD syntax
class MockExtractorFixed : public IExtractor {
public:
    MOCK_METHOD2(initialize, void(const std::unordered_map<std::string, std::any>&, ProcessingContext&));
    MOCK_METHOD2(extract_batch, RecordBatch(size_t, ProcessingContext&));
    MOCK_CONST_METHOD0(has_more_data, bool());
    MOCK_CONST_METHOD0(get_type, std::string());
    MOCK_METHOD1(finalize, void(ProcessingContext&));
    MOCK_CONST_METHOD0(get_statistics, std::unordered_map<std::string, std::any>());
};

class MockTransformerFixed : public ITransformer {
public:
    MOCK_METHOD2(initialize, void(const std::unordered_map<std::string, std::any>&, ProcessingContext&));
    MOCK_METHOD2(transform, std::optional<Record>(const Record&, ProcessingContext&));
    MOCK_METHOD2(transform_batch, RecordBatch(const RecordBatch&, ProcessingContext&));
    MOCK_CONST_METHOD0(get_type, std::string());
    MOCK_CONST_METHOD1(validate, omop::common::ValidationResult(const Record&));
    MOCK_CONST_METHOD0(get_statistics, std::unordered_map<std::string, std::any>());
};

class MockLoaderFixed : public ILoader {
public:
    MOCK_METHOD2(initialize, void(const std::unordered_map<std::string, std::any>&, ProcessingContext&));
    MOCK_METHOD2(load, bool(const Record&, ProcessingContext&));
    MOCK_METHOD2(load_batch, size_t(const RecordBatch&, ProcessingContext&));
    MOCK_METHOD1(commit, void(ProcessingContext&));
    MOCK_METHOD1(rollback, void(ProcessingContext&));
    MOCK_CONST_METHOD0(get_type, std::string());
    MOCK_METHOD1(finalize, void(ProcessingContext&));
    MOCK_CONST_METHOD0(get_statistics, std::unordered_map<std::string, std::any>());
};

class PipelineFixValidationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Configure pipeline with higher error threshold for testing
        PipelineConfig config;
        config.error_threshold = 0.5; // 50% error threshold for testing
        config.stop_on_error = false; // Don't stop on error during testing
        pipeline_ = std::make_unique<ETLPipeline>(config);
        
        // Create mock components
        mock_extractor_ = std::make_unique<MockExtractorFixed>();
        mock_transformer_ = std::make_unique<MockTransformerFixed>();
        mock_loader_ = std::make_unique<MockLoaderFixed>();
        
        // Store raw pointers for setting expectations
        extractor_ptr_ = mock_extractor_.get();
        transformer_ptr_ = mock_transformer_.get();
        loader_ptr_ = mock_loader_.get();
        
        // Set default expectations
        ON_CALL(*extractor_ptr_, get_type()).WillByDefault(Return("mock_extractor_fixed"));
        ON_CALL(*transformer_ptr_, get_type()).WillByDefault(Return("mock_transformer_fixed"));
        ON_CALL(*loader_ptr_, get_type()).WillByDefault(Return("mock_loader_fixed"));
    }

    std::unique_ptr<ETLPipeline> pipeline_;
    std::unique_ptr<MockExtractorFixed> mock_extractor_;
    std::unique_ptr<MockTransformerFixed> mock_transformer_;
    std::unique_ptr<MockLoaderFixed> mock_loader_;
    
    MockExtractorFixed* extractor_ptr_;
    MockTransformerFixed* transformer_ptr_;
    MockLoaderFixed* loader_ptr_;
};

// Test that pipeline correctly updates status to Completed
TEST_F(PipelineFixValidationTest, StatusUpdatedToCompleted) {
    // Set up expectations for initialization
    EXPECT_CALL(*extractor_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*transformer_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*loader_ptr_, initialize(_, _)).Times(1);
    
    // Set up extraction behavior - single batch
    RecordBatch test_batch;
    for (int i = 0; i < 5; ++i) {
        Record record;
        record.setField("id", i);
        test_batch.addRecord(record);
    }
    
    EXPECT_CALL(*extractor_ptr_, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));
    
    EXPECT_CALL(*extractor_ptr_, extract_batch(_, _))
        .WillOnce(Return(test_batch));
    
    // Set up transformation behavior
    EXPECT_CALL(*transformer_ptr_, transform_batch(_, _))
        .WillOnce(Invoke([](const RecordBatch& batch, ProcessingContext&) {
            return batch; // Pass through
        }));
    
    // Set up loading behavior
    EXPECT_CALL(*loader_ptr_, load_batch(_, _))
        .WillOnce(Return(5)); // All records loaded successfully
    
    EXPECT_CALL(*loader_ptr_, commit(_)).Times(1);
    
    // Set up finalization - this should be called
    EXPECT_CALL(*extractor_ptr_, finalize(_)).Times(1);
    EXPECT_CALL(*loader_ptr_, finalize(_)).Times(1);
    
    // Set components using PipelineBuilder
    auto builder = PipelineBuilder();
    builder.with_extractor(std::move(mock_extractor_))
           .with_transformer(std::move(mock_transformer_))
           .with_loader(std::move(mock_loader_));
    
    pipeline_ = builder.build();
    
    // Start pipeline
    pipeline_->start();
    auto execution_stats = pipeline_->get_execution_stats();
    
    // Verify status is Completed
    EXPECT_EQ(PipelineStatus::Completed, execution_stats.status);
    EXPECT_EQ(5, execution_stats.successful_records);
    EXPECT_EQ(0, execution_stats.failed_records);
    EXPECT_FALSE(execution_stats.pipeline_id.empty());
}

// Test that processed_records is correctly updated
TEST_F(PipelineFixValidationTest, ProcessedRecordsUpdated) {
    // Set up expectations for initialization
    EXPECT_CALL(*extractor_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*transformer_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*loader_ptr_, initialize(_, _)).Times(1);
    
    // Set up extraction behavior - multiple batches
    RecordBatch batch1, batch2;
    for (int i = 0; i < 3; ++i) {
        Record record;
        record.setField("id", i);
        batch1.addRecord(record);
    }
    for (int i = 3; i < 7; ++i) {
        Record record;
        record.setField("id", i);
        batch2.addRecord(record);
    }
    
    EXPECT_CALL(*extractor_ptr_, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(false));
    
    EXPECT_CALL(*extractor_ptr_, extract_batch(_, _))
        .WillOnce(Return(batch1))
        .WillOnce(Return(batch2));
    
    // Set up transformation behavior
    EXPECT_CALL(*transformer_ptr_, transform_batch(_, _))
        .Times(2)
        .WillRepeatedly(Invoke([](const RecordBatch& batch, ProcessingContext&) {
            return batch; // Pass through
        }));
    
    // Set up loading behavior
    EXPECT_CALL(*loader_ptr_, load_batch(_, _))
        .WillOnce(Return(3))
        .WillOnce(Return(4));
    
    EXPECT_CALL(*loader_ptr_, commit(_)).Times(1);
    
    // Set up finalization
    EXPECT_CALL(*extractor_ptr_, finalize(_)).Times(1);
    EXPECT_CALL(*loader_ptr_, finalize(_)).Times(1);
    
    // Set components using PipelineBuilder
    auto builder = PipelineBuilder();
    builder.with_extractor(std::move(mock_extractor_))
           .with_transformer(std::move(mock_transformer_))
           .with_loader(std::move(mock_loader_));
    
    pipeline_ = builder.build();
    
    // Start pipeline
    pipeline_->start();
    auto execution_stats = pipeline_->get_execution_stats();
    
    // Verify processed records is correctly updated
    EXPECT_EQ(PipelineStatus::Completed, execution_stats.status);
    EXPECT_EQ(7, execution_stats.successful_records); // 3 + 4 = 7
    EXPECT_EQ(0, execution_stats.failed_records);
}

// Test that finalize is called on both extractor and loader
TEST_F(PipelineFixValidationTest, FinalizeCalledCorrectly) {
    // Set up expectations for initialization
    EXPECT_CALL(*extractor_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*transformer_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*loader_ptr_, initialize(_, _)).Times(1);
    
    // Set up extraction behavior - empty batch (no data)
    EXPECT_CALL(*extractor_ptr_, has_more_data())
        .WillOnce(Return(false));
    
    EXPECT_CALL(*loader_ptr_, commit(_)).Times(0); // No commit when no data processed
    
    // The key test - finalize should be called on extractor and loader
    EXPECT_CALL(*extractor_ptr_, finalize(_)).Times(1);
    EXPECT_CALL(*loader_ptr_, finalize(_)).Times(1);
    
    // Set components
    pipeline_ = PipelineBuilder()
        .with_extractor(std::move(mock_extractor_))
        .with_transformer(std::move(mock_transformer_))
        .with_loader(std::move(mock_loader_))
        .build();
    
    // Start pipeline
    auto job_id = pipeline_->start();
    
    // Verify pipeline completed successfully
    EXPECT_FALSE(job_id.empty());
}

// Test that pre and post processors work correctly
TEST_F(PipelineFixValidationTest, PrePostProcessorsWork) {
    int pre_count = 0;
    int post_count = 0;
    
    // Set up expectations for initialization
    EXPECT_CALL(*extractor_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*transformer_ptr_, initialize(_, _)).Times(1);
    EXPECT_CALL(*loader_ptr_, initialize(_, _)).Times(1);
    
    // Set up extraction behavior
    RecordBatch test_batch;
    for (int i = 0; i < 2; ++i) {
        Record record;
        record.setField("id", i);
        test_batch.addRecord(record);
    }
    
    EXPECT_CALL(*extractor_ptr_, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));
    
    EXPECT_CALL(*extractor_ptr_, extract_batch(_, _))
        .WillOnce(Return(test_batch));
    
    // Set up transformation behavior
    EXPECT_CALL(*transformer_ptr_, transform_batch(_, _))
        .WillOnce(Invoke([](const RecordBatch& batch, ProcessingContext&) {
            return batch; // Pass through
        }));
    
    // Set up loading behavior
    EXPECT_CALL(*loader_ptr_, load_batch(_, _))
        .WillOnce(Return(2));
    
    EXPECT_CALL(*loader_ptr_, commit(_)).Times(1);
    EXPECT_CALL(*extractor_ptr_, finalize(_)).Times(1);
    EXPECT_CALL(*loader_ptr_, finalize(_)).Times(1);
    
    // Set components
    pipeline_ = PipelineBuilder()
        .with_extractor(std::move(mock_extractor_))
        .with_transformer(std::move(mock_transformer_))
        .with_loader(std::move(mock_loader_))
        .with_pre_processor([&pre_count](RecordBatch& batch, ProcessingContext& ctx) {
            pre_count++;
            for (auto& record : batch) {
                record.setField("preprocessed", true);
            }
        })
        .with_post_processor([&post_count](RecordBatch& batch, ProcessingContext& ctx) {
            post_count++;
            for (const auto& record : batch) {
                EXPECT_TRUE(std::any_cast<bool>(record.getField("preprocessed")));
            }
        })
        .build();
    
    // Start pipeline
    auto job_id = pipeline_->start();
    
    EXPECT_FALSE(job_id.empty());
    EXPECT_EQ(1, pre_count);  // Called once for the batch
    EXPECT_EQ(1, post_count); // Called once for the batch
}