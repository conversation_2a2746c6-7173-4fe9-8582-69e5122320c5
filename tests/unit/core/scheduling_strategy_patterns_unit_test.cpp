// File: tests/unit/core/job_scheduler_strategies_test.cpp

#include <gtest/gtest.h>
#include "core/job_scheduler.h"
#include "core/job_manager.h"
#include "common/configuration.h"
#include "common/logging.h"
#include <thread>
#include <chrono>

namespace omop::core {

class JobSchedulerStrategiesTest : public ::testing::Test {
protected:
    std::shared_ptr<JobManager> job_manager;
    
    void SetUp() override {
        auto config = std::make_shared<omop::common::ConfigurationManager>();
        auto logger = omop::common::Logger::get("test");
        job_manager = std::make_shared<JobManager>(config, logger);
    }
};

TEST_F(JobSchedulerStrategiesTest, DeadlineSchedulingStrategy) {
    // Test verifies that deadline scheduling strategy works correctly
    auto scheduler = std::make_unique<JobScheduler>(job_manager, SchedulingStrategy::DEADLINE);
    EXPECT_TRUE(scheduler->start());
    
    // Submit jobs with different deadlines
    JobConfig config1;
    config1.job_name = "Urgent Job";
    config1.pipeline_config_path = ""; // Empty to use default pipeline
    
    JobConfig config2;
    config2.job_name = "Later Job";
    config2.pipeline_config_path = ""; // Empty to use default pipeline
    
    // Job with earlier deadline should be processed first
    // even though it has lower priority
    auto job_id1 = scheduler->submitJob(config2, JobPriority::HIGH);
    auto job_id2 = scheduler->submitJob(config1, JobPriority::NORMAL);
    
    // Verify jobs were submitted successfully
    EXPECT_FALSE(job_id1.empty());
    EXPECT_FALSE(job_id2.empty());
    
    // Get scheduler statistics to verify jobs were scheduled
    auto stats = scheduler->getStatistics();
    EXPECT_GE(std::any_cast<size_t>(stats["jobs_scheduled"]), 2);
    
    scheduler->stop();
}

TEST_F(JobSchedulerStrategiesTest, JobDependencyResolution) {
    // Test verifies that job dependencies are properly resolved
    auto scheduler = std::make_unique<JobScheduler>(job_manager);
    scheduler->start();
    
    // Submit parent job
    JobConfig parent_config;
    parent_config.job_name = "Parent Job";
    auto parent_id = scheduler->submitJob(parent_config);
    
    // Submit dependent job
    JobConfig child_config;
    child_config.job_name = "Child Job";
    auto child_id = scheduler->submitJob(child_config, JobPriority::HIGH, {parent_id});
    
    // Child job should not execute until parent completes
    auto stats = scheduler->getStatistics();
    EXPECT_GT(std::any_cast<size_t>(stats["jobs_scheduled"]), 0);
}

TEST_F(JobSchedulerStrategiesTest, CronExpressionValidation) {
    // Test verifies that cron expression validation works correctly
    auto scheduler = std::make_unique<JobScheduler>(job_manager);
    
    JobSchedule schedule;
    schedule.trigger_type = TriggerType::SCHEDULED;
    
    // Test various cron expressions
    std::vector<std::string> test_expressions = {
        "* * * * *",      // Every minute
        "0 * * * *",      // Every hour
        "0 0 * * *",      // Every day
        "0 0 * * 0",      // Every week
        "0 0 1 * *",      // Every month
        "*/5 * * * *",    // Every 5 minutes (should default to daily)
        "invalid_cron"    // Invalid (should default to daily)
    };
    
    for (const auto& expr : test_expressions) {
        schedule.cron_expression = expr;
        auto id = scheduler->addSchedule(schedule);
        
        auto retrieved = scheduler->getSchedule(id);
        ASSERT_TRUE(retrieved.has_value());
        EXPECT_GT(retrieved->next_run.time_since_epoch().count(), 0);
    }
}

} // namespace omop::core