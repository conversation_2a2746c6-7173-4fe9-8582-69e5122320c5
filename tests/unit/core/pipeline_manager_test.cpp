/**
 * @file pipeline_manager_test.cpp
 * @brief Unit tests for pipeline manager functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/pipeline.h"
#include "core/interfaces.h"
#include "common/exceptions.h"
#include <thread>
#include <chrono>

namespace omop::core {

using namespace testing;

class PipelineManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        manager = std::make_unique<PipelineManager>(2); // Max 2 concurrent jobs
    }
    
    std::unique_ptr<PipelineManager> manager;
    
    std::unique_ptr<ETLPipeline> create_test_pipeline() {
        auto config = PipelineConfig{};
        config.batch_size = 100;
        config.max_parallel_batches = 4;
        config.error_threshold = 0.05;
        
        // Create a simple pipeline that will run but complete quickly
        return PipelineBuilder()
            .with_config(config)
            .build();
    }
};

// Test basic job submission
// Test that PipelineManager can submit jobs and track their completion status
TEST_F(PipelineManagerTest, SubmitJob) {
    auto pipeline = create_test_pipeline();
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    EXPECT_FALSE(job_id.empty());
    EXPECT_TRUE(job_id.starts_with("job-"));
}

// Test job status retrieval
TEST_F(PipelineManagerTest, GetJobStatus) {
    auto pipeline = create_test_pipeline();
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    auto status = manager->get_job_status(job_id);
    ASSERT_TRUE(status.has_value());
    // Note: Status might be Created or Running depending on timing
}

// Test job info retrieval
// Test that PipelineManager can retrieve job information by ID
TEST_F(PipelineManagerTest, GetJobInfo) {
    auto pipeline = create_test_pipeline();
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    auto info = manager->get_job_info(job_id);
    ASSERT_TRUE(info.has_value());
    EXPECT_EQ(info->job_name, "test_job");
}

// Test getting all jobs
TEST_F(PipelineManagerTest, GetAllJobs) {
    auto pipeline1 = create_test_pipeline();
    auto pipeline2 = create_test_pipeline();
    
    manager->submit_job("job_1", std::move(pipeline1));
    manager->submit_job("job_2", std::move(pipeline2));
    
    auto all_jobs = manager->get_all_jobs();
    EXPECT_EQ(all_jobs.size(), 2);
}

// Test job cancellation
TEST_F(PipelineManagerTest, CancelJob) {
    auto pipeline = create_test_pipeline();
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    bool cancelled = manager->cancel_job(job_id);
    EXPECT_TRUE(cancelled);
}

// Test job pausing
TEST_F(PipelineManagerTest, PauseJob) {
    auto pipeline = create_test_pipeline();
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    bool paused = manager->pause_job(job_id);
    EXPECT_TRUE(paused);
}

// Test job resuming
TEST_F(PipelineManagerTest, ResumeJob) {
    auto config = PipelineConfig{};
    config.batch_size = 1000; // Large batch to slow down processing
    config.max_parallel_batches = 1; // Single batch for predictable timing
    config.error_threshold = 0.05;
    
    // Create a pipeline that will take longer to complete
    auto pipeline = PipelineBuilder()
        .with_config(config)
        .build();
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    // Give the job a moment to start
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    // Check if job is still running or hasn't started yet
    auto info = manager->get_job_info(job_id);
    bool can_pause = (info.has_value() && 
                     (info->status == PipelineStatus::Running || 
                      info->status == PipelineStatus::Created));
    
    if (can_pause) {
        // First pause the job
        bool paused = manager->pause_job(job_id);
        EXPECT_TRUE(paused);
        
        // Then resume it
        bool resumed = manager->resume_job(job_id);
        EXPECT_TRUE(resumed);
    } else {
        // If job completed too quickly, just verify pause/resume return false
        // for completed jobs
        bool paused = manager->pause_job(job_id);
        EXPECT_FALSE(paused);
        
        bool resumed = manager->resume_job(job_id);
        EXPECT_FALSE(resumed);
    }
}

// Test waiting for job completion
TEST_F(PipelineManagerTest, WaitForJob) {
    auto pipeline = create_test_pipeline();
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    [[maybe_unused]] auto status = manager->wait_for_job(job_id, 5000); // 5 seconds timeout
    // Status depends on pipeline execution
}

// Test waiting for job with timeout
TEST_F(PipelineManagerTest, WaitForJobTimeout) {
    auto pipeline = create_test_pipeline();
    
    std::string job_id = manager->submit_job("test_job", std::move(pipeline));
    
    [[maybe_unused]] auto status = manager->wait_for_job(job_id, 100); // 100ms timeout
    // Should timeout quickly
}

// Test shutdown
TEST_F(PipelineManagerTest, Shutdown) {
    auto pipeline = create_test_pipeline();
    
    manager->submit_job("test_job", std::move(pipeline));
    
    manager->shutdown();
    // Should complete without errors
}

// Test submitting null pipeline throws
TEST_F(PipelineManagerTest, SubmitNullPipelineThrows) {
    EXPECT_THROW(manager->submit_job("test_job", nullptr), 
                 common::ConfigurationException);
}

// Test rejecting null pipeline
TEST_F(PipelineManagerTest, RejectsNullPipeline) {
    EXPECT_THROW(manager->submit_job("test_job", nullptr), 
                 common::ConfigurationException);
}

// Test getting non-existent job
TEST_F(PipelineManagerTest, GetNonExistentJob) {
    auto status = manager->get_job_status("non_existent_job");
    EXPECT_FALSE(status.has_value());
    
    auto info = manager->get_job_info("non_existent_job");
    EXPECT_FALSE(info.has_value());
}

// Test cancelling non-existent job
TEST_F(PipelineManagerTest, CancelNonExistentJob) {
    bool cancelled = manager->cancel_job("non_existent_job");
    EXPECT_FALSE(cancelled);
}

// Test operations on non-existent jobs
TEST_F(PipelineManagerTest, NonexistentJobOperations) {
    EXPECT_FALSE(manager->pause_job("non_existent_job"));
    EXPECT_FALSE(manager->resume_job("non_existent_job"));
    
    auto status = manager->wait_for_job("non_existent_job", 1000);
    EXPECT_FALSE(status);
}

// Test concurrent job submission
TEST_F(PipelineManagerTest, ConcurrentJobSubmission) {
    std::vector<std::string> job_ids;
    std::mutex job_ids_mutex;
    
    std::vector<std::thread> threads;
    for (int i = 0; i < 5; ++i) {
        threads.emplace_back([this, i, &job_ids, &job_ids_mutex]() {
            auto pipeline = create_test_pipeline();
            std::string job_id = manager->submit_job("job_" + std::to_string(i), std::move(pipeline));
            
            std::lock_guard<std::mutex> lock(job_ids_mutex);
            job_ids.push_back(job_id);
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    EXPECT_EQ(job_ids.size(), 5);
}

// Test job count
TEST_F(PipelineManagerTest, JobCount) {
    auto pipeline1 = create_test_pipeline();
    auto pipeline2 = create_test_pipeline();
    
    manager->submit_job("job1", std::move(pipeline1));
    manager->submit_job("job2", std::move(pipeline2));
    
    auto all_jobs = manager->get_all_jobs();
    EXPECT_EQ(all_jobs.size(), 2);
}

// Test force shutdown
TEST_F(PipelineManagerTest, ForceShutdown) {
    auto pipeline = create_test_pipeline();
    
    std::string job_id1 = manager->submit_job("test_job1", std::move(pipeline));
    
    auto pipeline2 = create_test_pipeline();
    std::string job_id2 = manager->submit_job("test_job2", std::move(pipeline2));
    
    manager->shutdown();
    // Should complete without errors
}

} // namespace omop::core