# Unit tests for core module

# Test source files in the core directory
set(CORE_TEST_SOURCES
    core_interfaces_unit_test.cpp
    data_encoding_unit_test.cpp
    job_management_unit_test.cpp
    job_scheduling_unit_test.cpp
    pipeline_execution_unit_test.cpp
    record_operations_unit_test.cpp
    scheduling_strategy_patterns_unit_test.cpp
    pipeline_component_integration_unit_test.cpp
    processing_context_thread_safety_unit_test.cpp
    validation_result_handling_unit_test.cpp
    processing_context_management_unit_test.cpp
    additional_core_tests.cpp
    pipeline_fix_validation_test.cpp
    component_factory_creation_unit_test.cpp
    error_handling_test.cpp
    pipeline_builder_test.cpp
    pipeline_manager_test.cpp
)

# Use the new consolidated approach
add_component_unit_tests(core 
    core_interfaces_unit_test.cpp
    data_encoding_unit_test.cpp
    job_management_unit_test.cpp
    job_scheduling_unit_test.cpp
    pipeline_execution_unit_test.cpp
    record_operations_unit_test.cpp
    scheduling_strategy_patterns_unit_test.cpp
    pipeline_component_integration_unit_test.cpp
    processing_context_thread_safety_unit_test.cpp
    validation_result_handling_unit_test.cpp
    processing_context_management_unit_test.cpp
    additional_core_tests.cpp
    pipeline_fix_validation_test.cpp
    component_factory_creation_unit_test.cpp
    error_handling_test.cpp
    pipeline_builder_test.cpp
    pipeline_manager_test.cpp
)

# Copy test data files if any exist
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test_data)
    file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/test_data
        DESTINATION ${CMAKE_CURRENT_BINARY_DIR}
    )
endif()

# Test configuration summary
message(STATUS "Core Unit Tests Configuration:")
message(STATUS "  Test files: ${CORE_TEST_SOURCES}")
message(STATUS "  Output directory: ${CMAKE_BINARY_DIR}/tests/unit/core")
message(STATUS "  C++ Standard: 20")

# Remove or comment out the custom target to avoid duplicate errors
# add_custom_target(test_core_tests ...)
