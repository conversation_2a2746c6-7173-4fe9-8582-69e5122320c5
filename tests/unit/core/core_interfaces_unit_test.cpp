/**
 * @file test_interfaces.cpp
 * @brief Unit tests for interfaces components
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/interfaces.h"
#include "core/record.h"
#include "common/exceptions.h"
#include <thread>
#include <chrono>
#include <any>
#include <optional>
#include <unordered_map>
#include <string>
#include <vector>

namespace omop::core {

using namespace testing;

// Type aliases to simplify MOCK_METHOD syntax
using ConfigMap = std::unordered_map<std::string, std::any>;
using StatsMap = std::unordered_map<std::string, std::any>;
using OptionalRecord = std::optional<Record>;

// Mock implementations for testing
class MockExtractor : public IExtractor {
public:
    MOCK_METHOD(void, initialize, (const ConfigMap&, ProcessingContext&), (override));
    MOCK_METHOD(RecordBatch, extract_batch, (size_t, ProcessingContext&), (override));
    MOCK_METHOD(bool, has_more_data, (), (const, override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD(StatsMap, get_statistics, (), (const, override));
};

class MockTransformer : public ITransformer {
public:
    MOCK_METHOD(void, initialize, (const ConfigMap&, ProcessingContext&), (override));
    MOCK_METHOD(OptionalRecord, transform, (const Record&, ProcessingContext&), (override));
    MOCK_METHOD(RecordBatch, transform_batch, (const RecordBatch&, ProcessingContext&), (override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(omop::common::ValidationResult, validate, (const Record&), (const, override));
    MOCK_METHOD(StatsMap, get_statistics, (), (const, override));
};

class MockLoader : public ILoader {
public:
    MOCK_METHOD(void, initialize, (const ConfigMap&, ProcessingContext&), (override));
    MOCK_METHOD(bool, load, (const Record&, ProcessingContext&), (override));
    MOCK_METHOD(size_t, load_batch, (const RecordBatch&, ProcessingContext&), (override));
    MOCK_METHOD(void, commit, (ProcessingContext&), (override));
    MOCK_METHOD(void, rollback, (ProcessingContext&), (override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD(StatsMap, get_statistics, (), (const, override));
};

// Test verifies that basic test infrastructure is working correctly
TEST(InterfacesTest, BasicTest) {
    EXPECT_TRUE(true);
}

// Interface concept tests
class ConceptTest : public ::testing::Test {
protected:
    Record record;
};

// Test verifies that Record class satisfies the RecordLike concept requirements
TEST_F(ConceptTest, RecordSatisfiesRecordLikeConcept) {
    // This test verifies that Record satisfies the RecordLike concept
    static_assert(RecordLike<Record>);

    // Test concept requirements
    record.setField("test", 42);
    auto value = record.getField("test");
    EXPECT_EQ(std::any_cast<int>(value), 42);

    auto names = record.getFieldNames();
    EXPECT_EQ(names.size(), 1);
    EXPECT_EQ(names[0], "test");
}

// Mock record that doesn't satisfy RecordLike
struct NotRecordLike {
    void someMethod() {}
};

// Test verifies that non-conforming class does not satisfy RecordLike concept
TEST_F(ConceptTest, NotRecordLikeFailsConcept) {
    static_assert(!RecordLike<NotRecordLike>);
}

// Test IExtractor interface
class ExtractorTest : public ::testing::Test {
protected:
    MockExtractor extractor;
    ProcessingContext context;
};

// Test verifies that IExtractor initialize method is called correctly
TEST_F(ExtractorTest, Initialize) {
    ConfigMap config;
    config["batch_size"] = 100;

    EXPECT_CALL(extractor, initialize(_, _)).Times(1);
    extractor.initialize(config, context);
}

// Test verifies that IExtractor extract_batch method returns correct batch size
TEST_F(ExtractorTest, ExtractBatch) {
    RecordBatch batch;
    EXPECT_CALL(extractor, extract_batch(100, _)).WillOnce(Return(batch));
    auto result = extractor.extract_batch(100, context);
    EXPECT_EQ(result.size(), batch.size());
}

// Test verifies that IExtractor has_more_data method returns correct boolean value
TEST_F(ExtractorTest, HasMoreData) {
    EXPECT_CALL(extractor, has_more_data()).WillOnce(Return(true));
    EXPECT_TRUE(extractor.has_more_data());
}

// Test verifies that IExtractor get_type method returns correct extractor type
TEST_F(ExtractorTest, GetType) {
    EXPECT_CALL(extractor, get_type()).WillOnce(Return("test_extractor"));
    EXPECT_EQ(extractor.get_type(), "test_extractor");
}

// Test verifies that IExtractor finalize method is called correctly
TEST_F(ExtractorTest, Finalize) {
    EXPECT_CALL(extractor, finalize(_)).Times(1);
    extractor.finalize(context);
}

// Test verifies that IExtractor get_statistics method returns correct statistics
TEST_F(ExtractorTest, GetStatistics) {
    StatsMap stats;
    stats["records_extracted"] = 1000;
    EXPECT_CALL(extractor, get_statistics()).WillOnce(Return(stats));
    auto result = extractor.get_statistics();
    EXPECT_EQ(std::any_cast<int>(result["records_extracted"]), 1000);
}

// Test ITransformer interface
class TransformerTest : public ::testing::Test {
protected:
    MockTransformer transformer;
    ProcessingContext context;
    Record record;
};

// Test verifies that ITransformer initialize method is called correctly
TEST_F(TransformerTest, Initialize) {
    ConfigMap config;
    config["transformation_rules"] = std::string("test_rules");

    EXPECT_CALL(transformer, initialize(_, _)).Times(1);
    transformer.initialize(config, context);
}

// Test verifies that ITransformer transform method returns optional record
TEST_F(TransformerTest, Transform) {
    EXPECT_CALL(transformer, transform(_, _)).WillOnce(Return(OptionalRecord(record)));
    auto result = transformer.transform(record, context);
    EXPECT_TRUE(result.has_value());
}

// Test verifies that ITransformer transform_batch method processes batches correctly
TEST_F(TransformerTest, TransformBatch) {
    RecordBatch batch;
    EXPECT_CALL(transformer, transform_batch(_, _)).WillOnce(Return(batch));
    auto result = transformer.transform_batch(batch, context);
    EXPECT_EQ(result.size(), batch.size());
}

// Test verifies that ITransformer get_type method returns correct transformer type
TEST_F(TransformerTest, GetType) {
    EXPECT_CALL(transformer, get_type()).WillOnce(Return("test_transformer"));
    EXPECT_EQ(transformer.get_type(), "test_transformer");
}

// Test verifies that ITransformer validate method returns validation result
TEST_F(TransformerTest, Validate) {
    omop::common::ValidationResult valid_result;
    EXPECT_CALL(transformer, validate(_)).WillOnce(Return(valid_result));
    auto result = transformer.validate(record);
    EXPECT_TRUE(result.is_valid());
}

// Test verifies that ITransformer get_statistics method returns correct statistics
TEST_F(TransformerTest, GetStatistics) {
    StatsMap stats;
    stats["records_transformed"] = 950;
    stats["records_failed"] = 50;
    EXPECT_CALL(transformer, get_statistics()).WillOnce(Return(stats));
    auto result = transformer.get_statistics();
    EXPECT_EQ(std::any_cast<int>(result["records_transformed"]), 950);
}

// Test ILoader interface
class LoaderTest : public ::testing::Test {
protected:
    MockLoader loader;
    ProcessingContext context;
    Record record;
};

// Test verifies that ILoader initialize method is called correctly
TEST_F(LoaderTest, Initialize) {
    ConfigMap config;
    config["connection_string"] = std::string("test_connection");

    EXPECT_CALL(loader, initialize(_, _)).Times(1);
    loader.initialize(config, context);
}

// Test verifies that ILoader load method returns success status
TEST_F(LoaderTest, Load) {
    EXPECT_CALL(loader, load(_, _)).WillOnce(Return(true));
    EXPECT_TRUE(loader.load(record, context));
}

// Test verifies that ILoader load_batch method returns number of loaded records
TEST_F(LoaderTest, LoadBatch) {
    RecordBatch batch;
    batch.addRecord(record);
    EXPECT_CALL(loader, load_batch(_, _)).WillOnce(Return(batch.size()));
    size_t loaded = loader.load_batch(batch, context);
    EXPECT_EQ(loaded, batch.size());
}

// Test verifies that ILoader get_type method returns correct loader type
TEST_F(LoaderTest, GetType) {
    EXPECT_CALL(loader, get_type()).WillOnce(Return("test_loader"));
    EXPECT_EQ(loader.get_type(), "test_loader");
}

// Test verifies that ILoader commit and rollback methods are called correctly
TEST_F(LoaderTest, CommitAndRollback) {
    EXPECT_CALL(loader, commit(_)).Times(1);
    EXPECT_CALL(loader, rollback(_)).Times(1);

    loader.commit(context);
    loader.rollback(context);
}

// Test verifies that ILoader finalize method is called correctly
TEST_F(LoaderTest, Finalize) {
    EXPECT_CALL(loader, finalize(_)).Times(1);
    loader.finalize(context);
}

// Test verifies that ILoader get_statistics method returns correct statistics
TEST_F(LoaderTest, GetStatistics) {
    StatsMap stats;
    stats["records_loaded"] = 900;
    stats["records_failed"] = 100;
    EXPECT_CALL(loader, get_statistics()).WillOnce(Return(stats));
    auto result = loader.get_statistics();
    EXPECT_EQ(std::any_cast<int>(result["records_loaded"]), 900);
}

// ComponentFactory tests
class ComponentFactoryTest : public ::testing::Test {
protected:
    ComponentFactory<IExtractor> factory;
};

// Test verifies that ComponentFactory can register and create components correctly
TEST_F(ComponentFactoryTest, RegisterAndCreate) {
    factory.register_creator("mock", []() {
        return std::make_unique<MockExtractor>();
    });

    EXPECT_TRUE(factory.is_registered("mock"));
    EXPECT_EQ(factory.registered_count(), 1);

    auto extractor = factory.create("mock");
    EXPECT_NE(extractor, nullptr);
}

// Test verifies that ComponentFactory throws exception when creating unregistered component
TEST_F(ComponentFactoryTest, CreateUnregisteredThrows) {
    EXPECT_THROW(factory.create("unknown"), common::ConfigurationException);
}

// Test verifies that ComponentFactory returns all registered component types
TEST_F(ComponentFactoryTest, GetRegisteredTypes) {
    factory.register_creator("type1", []() { return std::make_unique<MockExtractor>(); });
    factory.register_creator("type2", []() { return std::make_unique<MockExtractor>(); });
    factory.register_creator("type3", []() { return std::make_unique<MockExtractor>(); });

    auto types = factory.get_registered_types();
    EXPECT_EQ(types.size(), 3);
    EXPECT_TRUE(std::find(types.begin(), types.end(), "type1") != types.end());
    EXPECT_TRUE(std::find(types.begin(), types.end(), "type2") != types.end());
    EXPECT_TRUE(std::find(types.begin(), types.end(), "type3") != types.end());
}

// Test verifies that ComponentFactory correctly identifies registered and unregistered types
TEST_F(ComponentFactoryTest, IsRegistered) {
    EXPECT_FALSE(factory.is_registered("test"));

    factory.register_creator("test", []() {
        return std::make_unique<MockExtractor>();
    });

    EXPECT_TRUE(factory.is_registered("test"));
}

// Test verifies that ComponentFactory tracks registered component count correctly
TEST_F(ComponentFactoryTest, RegisteredCount) {
    EXPECT_EQ(factory.registered_count(), 0);

    factory.register_creator("type1", []() { return std::make_unique<MockExtractor>(); });
    EXPECT_EQ(factory.registered_count(), 1);

    factory.register_creator("type2", []() { return std::make_unique<MockExtractor>(); });
    EXPECT_EQ(factory.registered_count(), 2);
}

// Test verifies that ComponentFactory allows overwriting existing registered types
TEST_F(ComponentFactoryTest, OverwriteRegisteredType) {
    int creation_count = 0;

    factory.register_creator("test", [&creation_count]() {
        creation_count = 1;
        return std::make_unique<MockExtractor>();
    });

    factory.register_creator("test", [&creation_count]() {
        creation_count = 2;
        return std::make_unique<MockExtractor>();
    });

    auto extractor = factory.create("test");
    EXPECT_EQ(creation_count, 2); // Second creator was used
}

} // namespace omop::core
