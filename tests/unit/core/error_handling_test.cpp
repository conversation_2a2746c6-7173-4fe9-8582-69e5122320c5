/**
 * @file error_handling_test.cpp
 * @brief Unit tests for error handling and edge cases in core library
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/interfaces.h"
#include "core/record.h"
#include "core/pipeline.h"
#include "common/exceptions.h"
#include <thread>
#include <chrono>

namespace omop::core {

using namespace testing;

// Test ProcessingContext error threshold handling
class ProcessingContextErrorTest : public ::testing::Test {
protected:
    ProcessingContext context;
};

TEST_F(ProcessingContextErrorTest, InitialStateValid) {
    EXPECT_EQ(context.processed_count(), 0);
    EXPECT_EQ(context.error_count(), 0);
    EXPECT_FALSE(context.is_error_threshold_exceeded());
}

TEST_F(ProcessingContextErrorTest, ErrorThresholdCalculation) {
    context.set_error_threshold(0.1); // 10% threshold
    
    // Add some processed records
    context.increment_processed(100);
    context.increment_errors(5); // 5% error rate
    
    EXPECT_FALSE(context.is_error_threshold_exceeded());
    
    // Add more errors to exceed threshold
    context.increment_errors(10); // 15% error rate now
    EXPECT_TRUE(context.is_error_threshold_exceeded());
}

TEST_F(ProcessingContextErrorTest, ErrorThresholdWithZeroProcessed) {
    context.set_error_threshold(0.1);
    context.increment_errors(10);
    
    // Should not exceed threshold if no records processed
    EXPECT_FALSE(context.is_error_threshold_exceeded());
}

TEST_F(ProcessingContextErrorTest, ErrorCallbackTriggered) {
    bool callback_called = false;
    std::string callback_message;
    
    context.set_error_callback([&](const std::string& msg) {
        callback_called = true;
        callback_message = msg;
    });
    
    context.set_error_threshold(0.1);
    context.increment_processed(10);
    context.increment_errors(2); // 20% error rate
    
    EXPECT_TRUE(callback_called);
    EXPECT_FALSE(callback_message.empty());
}

// Test Record edge cases
class RecordEdgeCaseTest : public ::testing::Test {
protected:
    Record record;
};

TEST_F(RecordEdgeCaseTest, LargeFieldNames) {
    std::string large_field_name(1000, 'a');
    record.setField(large_field_name, 42);
    
    EXPECT_TRUE(record.hasField(large_field_name));
    EXPECT_EQ(std::any_cast<int>(record.getField(large_field_name)), 42);
}

TEST_F(RecordEdgeCaseTest, EmptyFieldName) {
    record.setField("", 42);
    EXPECT_TRUE(record.hasField(""));
    EXPECT_EQ(std::any_cast<int>(record.getField("")), 42);
}

TEST_F(RecordEdgeCaseTest, UnicodeFieldNames) {
    std::string unicode_field = "测试字段";
    record.setField(unicode_field, std::string("测试值"));
    
    EXPECT_TRUE(record.hasField(unicode_field));
    EXPECT_EQ(std::any_cast<std::string>(record.getField(unicode_field)), "测试值");
}

TEST_F(RecordEdgeCaseTest, VeryLargeRecord) {
    // Create record with many fields
    for (int i = 0; i < 10000; ++i) {
        record.setField("field_" + std::to_string(i), i);
    }
    
    EXPECT_EQ(record.getFieldCount(), 10000);
    EXPECT_EQ(std::any_cast<int>(record.getField("field_5000")), 5000);
}

TEST_F(RecordEdgeCaseTest, ComplexDataTypes) {
    // Test with complex data types
    std::vector<int> vec = {1, 2, 3, 4, 5};
    std::unordered_map<std::string, int> map = {{"a", 1}, {"b", 2}};
    
    record.setField("vector", vec);
    record.setField("map", map);
    
    auto retrieved_vec = std::any_cast<std::vector<int>>(record.getField("vector"));
    auto retrieved_map = std::any_cast<std::unordered_map<std::string, int>>(record.getField("map"));
    
    EXPECT_EQ(retrieved_vec.size(), 5);
    EXPECT_EQ(retrieved_map["a"], 1);
}

// Test RecordBatch edge cases
class RecordBatchEdgeCaseTest : public ::testing::Test {
protected:
    RecordBatch batch;
};

TEST_F(RecordBatchEdgeCaseTest, VeryLargeBatch) {
    // Create batch with many records
    for (int i = 0; i < 100000; ++i) {
        Record r;
        r.setField("id", i);
        batch.addRecord(std::move(r));
    }
    
    EXPECT_EQ(batch.size(), 100000);
    EXPECT_EQ(std::any_cast<int>(batch.getRecord(50000).getField("id")), 50000);
}

TEST_F(RecordBatchEdgeCaseTest, EmptyRecords) {
    for (int i = 0; i < 1000; ++i) {
        Record r; // Empty record
        batch.addRecord(r);
    }
    
    EXPECT_EQ(batch.size(), 1000);
    EXPECT_TRUE(batch.getRecord(500).isEmpty());
}

// Test ValidationResult edge cases
class ValidationResultEdgeCaseTest : public ::testing::Test {
protected:
    omop::common::ValidationResult result;
};

TEST_F(ValidationResultEdgeCaseTest, ManyErrors) {
    for (int i = 0; i < 10000; ++i) {
        result.add_error("field_" + std::to_string(i), 
                        "Error " + std::to_string(i), 
                        "rule_" + std::to_string(i));
    }
    
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(result.error_count(), 10000);
    
    std::string error_messages = result.error_messages();
    EXPECT_FALSE(error_messages.empty());
}

TEST_F(ValidationResultEdgeCaseTest, LongErrorMessages) {
    std::string long_message(10000, 'x');
    result.add_error("field", long_message, "rule");
    
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(result.errors()[0].error_message, long_message);
}

TEST_F(ValidationResultEdgeCaseTest, UnicodeErrorMessages) {
    std::string unicode_message = "错误信息：数据验证失败";
    result.add_error("unicode_field", unicode_message, "unicode_rule");
    
    EXPECT_FALSE(result.is_valid());
    EXPECT_EQ(result.errors()[0].error_message, unicode_message);
}

// Test ComponentFactory edge cases
class ComponentFactoryEdgeCaseTest : public ::testing::Test {
protected:
    ComponentFactory<IExtractor> factory;
};

TEST_F(ComponentFactoryEdgeCaseTest, RegisterSameTypeTwice) {
    auto creator1 = []() { return std::unique_ptr<IExtractor>(); };
    auto creator2 = []() { return std::unique_ptr<IExtractor>(); };
    
    factory.register_creator("test", creator1);
    EXPECT_NO_THROW(factory.register_creator("test", creator2)); // Should overwrite
    EXPECT_EQ(factory.registered_count(), 1);
}

TEST_F(ComponentFactoryEdgeCaseTest, EmptyTypeName) {
    auto creator = []() { return std::unique_ptr<IExtractor>(); };
    
    EXPECT_NO_THROW(factory.register_creator("", creator));
    EXPECT_TRUE(factory.is_registered(""));
}

TEST_F(ComponentFactoryEdgeCaseTest, VeryLongTypeName) {
    std::string long_type(10000, 'a');
    auto creator = []() { return std::unique_ptr<IExtractor>(); };
    
    factory.register_creator(long_type, creator);
    EXPECT_TRUE(factory.is_registered(long_type));
}

// Test threading safety
class ErrorHandlingThreadSafetyTest : public ::testing::Test {
protected:
    ProcessingContext context;
    Record record;
};

TEST_F(ErrorHandlingThreadSafetyTest, ConcurrentContextAccess) {
    std::vector<std::thread> threads;
    
    // Start multiple threads that access context concurrently
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([&, i]() {
            for (int j = 0; j < 1000; ++j) {
                context.increment_processed(1);
                context.increment_errors(j % 10 == 0 ? 1 : 0);
                context.set_data("key_" + std::to_string(i), j);
                (void)context.get_data("key_" + std::to_string(i));
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Verify final state
    EXPECT_EQ(context.processed_count(), 10000);
    EXPECT_EQ(context.error_count(), 1000);
}

TEST_F(ErrorHandlingThreadSafetyTest, ConcurrentRecordAccess) {
    std::vector<std::thread> threads;
    
    // Initialize record with some data
    for (int i = 0; i < 100; ++i) {
        record.setField("field_" + std::to_string(i), i);
    }
    
    // Start multiple threads that access record concurrently (read-only)
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([&]() {
            for (int j = 0; j < 1000; ++j) {
                auto names = record.getFieldNames();
                for (const auto& name : names) {
                    if (record.hasField(name)) {
                        auto value = record.getFieldOptional(name);
                        EXPECT_TRUE(value.has_value());
                    }
                }
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
}

} // namespace omop::core