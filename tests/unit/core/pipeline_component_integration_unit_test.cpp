// File: tests/unit/core/pipeline_integration_test.cpp

#include <gtest/gtest.h>
#include "core/pipeline.h"
#include "core/interfaces.h"
#include <atomic>
#include <chrono>

namespace omop::core {

class DatabaseExtractor : public IExtractor {
private:
    std::atomic<size_t> batch_count{0};
    size_t total_batches = 3;
    
public:
    void initialize(const std::unordered_map<std::string, std::any>&, ProcessingContext&) override {}
    
    RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) override {
        RecordBatch batch;
        if (batch_count < total_batches) {
            // Simulate database extraction with realistic data
            for (size_t i = 0; i < batch_size; ++i) {
                Record r;
                r.setField("patient_id", static_cast<int>(batch_count * batch_size + i + 1));
                r.set<PERSON>ield("first_name", "Patient" + std::to_string(batch_count * batch_size + i + 1));
                r.set<PERSON>ield("last_name", "Test");
                r.set<PERSON>ield("birth_date", "1980-01-01");
                r.set<PERSON>ield("gender", (i % 2 == 0) ? "M" : "F");
                batch.addRecord(r);
            }
            batch_count++;
        }
        return batch;
    }
    
    bool has_more_data() const override { return batch_count < total_batches; }
    std::string get_type() const override { return "database_extractor"; }
    void finalize(ProcessingContext&) override {}
    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {{"batches_extracted", batch_count.load()}};
    }
};

class DatabaseTransformer : public ITransformer {
private:
    std::atomic<size_t> transform_count{0};
    
public:
    void initialize(const std::unordered_map<std::string, std::any>&, ProcessingContext&) override {}
    
    std::optional<Record> transform(const Record& record, ProcessingContext&) override {
        Record r = record;
        r.setField("transformed", true);
        r.setField("transform_timestamp", std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count());
        transform_count++;
        return r;
    }
    
    RecordBatch transform_batch(const RecordBatch& batch, ProcessingContext& context) override {
        RecordBatch result;
        for (const auto& record : batch.getRecords()) {
            auto transformed = transform(record, context);
            if (transformed) {
                result.addRecord(*transformed);
            }
        }
        return result;
    }
    
    std::string get_type() const override { return "database_transformer"; }
    omop::common::ValidationResult validate(const Record&) const override { return omop::common::ValidationResult(); }
    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {{"records_transformed", transform_count.load()}};
    }
};

class DatabaseLoader : public ILoader {
private:
    std::atomic<size_t> load_count{0};
    std::atomic<size_t> commit_count{0};
    
public:
    void initialize(const std::unordered_map<std::string, std::any>&, ProcessingContext&) override {}
    
    bool load(const Record& record, ProcessingContext& context) override {
        // Simulate database loading
        load_count++;
        return true;
    }
    
    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override {
        size_t loaded = 0;
        for (const auto& record : batch.getRecords()) {
            if (load(record, context)) {
                loaded++;
            }
        }
        return loaded;
    }
    
    void commit(ProcessingContext&) override { commit_count++; }
    void rollback(ProcessingContext&) override {}
    std::string get_type() const override { return "database_loader"; }
    void finalize(ProcessingContext&) override {}
    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {
            {"records_loaded", load_count.load()},
            {"commits", commit_count.load()}
        };
    }
    
    size_t get_load_count() const { return load_count.load(); }
    size_t get_commit_count() const { return commit_count.load(); }
};

class PipelineIntegrationTest : public ::testing::Test {
protected:
    std::unique_ptr<ETLPipeline> pipeline;
    
    void SetUp() override {
        PipelineConfig config;
        config.batch_size = 10;
        config.commit_interval = 20;
        config.error_threshold = 0.1;
        pipeline = std::make_unique<ETLPipeline>(config);
    }
};

// Test verifies end-to-end pipeline execution with database components
TEST_F(PipelineIntegrationTest, EndToEndExecution) {
    auto extractor = std::make_unique<DatabaseExtractor>();
    auto transformer = std::make_unique<DatabaseTransformer>();
    auto loader = std::make_unique<DatabaseLoader>();
    
    // Use PipelineBuilder instead of direct set methods
    auto builder = PipelineBuilder();
    builder.with_extractor(std::move(extractor))
           .with_transformer(std::move(transformer))
           .with_loader(std::move(loader));
    
    pipeline = builder.build();
    
    pipeline->start();
    auto execution_stats = pipeline->get_execution_stats();
    
    // Verify the pipeline completed successfully
    EXPECT_EQ(execution_stats.status, PipelineStatus::Completed);
    EXPECT_TRUE(execution_stats.errors.empty());
    
    // Verify that the pipeline actually processed data through all stages
    // The pipeline should have processed records from the DatabaseExtractor
    // which creates 3 batches of records
    EXPECT_GT(execution_stats.total_records_processed, 0);
    EXPECT_GT(execution_stats.successful_records, 0);
    
    // Verify data integrity: successful records should equal total processed
    // since our test components don't fail
    EXPECT_EQ(execution_stats.successful_records, execution_stats.total_records_processed);
    
    // Verify no records were failed or skipped in our test scenario
    EXPECT_EQ(execution_stats.failed_records, 0);
    EXPECT_EQ(execution_stats.skipped_records, 0);
}

// Test verifies pipeline behavior when error threshold is exceeded
TEST_F(PipelineIntegrationTest, ErrorThresholdHandling) {
    // Test pipeline behavior when error threshold is exceeded
    class FailingTransformer : public DatabaseTransformer {
    private:
        std::atomic<size_t> count{0};
        
    public:
        std::optional<Record> transform(const Record& record, ProcessingContext& context) override {
            if (++count % 3 == 0) {
                context.increment_errors();
                // Check if error threshold is exceeded and stop processing
                if (context.is_error_threshold_exceeded()) {
                    throw std::runtime_error("Error threshold exceeded");
                }
                return std::nullopt; // Fail every 3rd record
            }
            return DatabaseTransformer::transform(record, context);
        }
    };
    
    // Create a new pipeline with the correct error threshold config
    PipelineConfig config;
    config.batch_size = 10;
    config.commit_interval = 20;
    config.error_threshold = 0.2; // 20% error threshold
    config.stop_on_error = true;
    
    // Use PipelineBuilder with the error threshold config
    auto builder = PipelineBuilder();
    builder.with_config(config)
           .with_extractor(std::make_unique<DatabaseExtractor>())
           .with_transformer(std::make_unique<FailingTransformer>())
           .with_loader(std::make_unique<DatabaseLoader>());
    
    auto error_pipeline = builder.build();
    
    error_pipeline->start();
    auto execution_stats = error_pipeline->get_execution_stats();
    
    // Pipeline should fail due to error threshold
    EXPECT_EQ(execution_stats.status, PipelineStatus::Failed);
}

} // namespace omop::core