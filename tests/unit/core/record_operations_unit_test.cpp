/**
 * @file test_record.cpp
 * @brief Unit tests for Record and RecordBatch classes
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "core/record.h"
#include "common/exceptions.h"
#include "common/utilities.h"
#include <chrono>
#include <thread>
#include <locale>
#include <iomanip>
#include <sstream>
#include <random>
#include <algorithm>

using namespace omop::core;

// Record tests
class RecordTest : public ::testing::Test {
protected:
    Record record;
    
    // UK-specific test data
    std::vector<std::string> uk_postcodes = {
        "SW1A 1AA", "M1 1AA", "B33 8TH", "W1A 0AX", "EC1A 1BB", "N1 9GU",
        "E1 6AN", "SE1 9GF", "LS1 1UR", "NE1 4ST", "S1 1HD", "CV1 1GF"
    };
    
    std::vector<std::string> uk_nhs_numbers = {
        "************", "************", "************", "************"
    };
    
    std::vector<std::string> uk_hospital_names = {
        "NHS Foundation Trust", "Royal London Hospital", "St. Thomas' Hospital",
        "University College Hospital", "King's College Hospital", "Hammersmith Hospital"
    };

    void SetUp() override {
        // Set up test data with UK localization
        record.setField("patient_id", 42);
        record.setField("nhs_number", std::string("************"));
        record.setField("name", std::string("John Smith"));
        record.setField("postcode", std::string("SW1A 1AA"));
        record.setField("temperature_celsius", 37.2);  // UK uses Celsius
        record.setField("weight_kg", 75.5);           // UK uses kg for weight
        record.setField("height_cm", 180.0);          // UK uses cm for height
        record.setField("is_uk_resident", true);
        
        // UK date format (DD/MM/YYYY)
        auto now = std::chrono::system_clock::now();
        record.setField("admission_date", now);
        
        // UK currency format
        record.setField("cost_pounds", 125.50);
        
        // Set up fields expected by the tests
        record.setField("int_field", 42);
        record.setField("string_field", std::string("test"));
        record.setField("double_field", 3.14);
        record.setField("bool_field", true);
    }
    
    // Helper function to generate random UK postcode
    std::string generateRandomUKPostcode() {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, uk_postcodes.size() - 1);
        return uk_postcodes[dis(gen)];
    }
    
    // Helper function to generate random NHS number
    std::string generateRandomNHSNumber() {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, uk_nhs_numbers.size() - 1);
        return uk_nhs_numbers[dis(gen)];
    }
};

// Test verifies that Record default constructor creates an empty record
TEST_F(RecordTest, DefaultConstructor) {
    Record r;
    EXPECT_TRUE(r.isEmpty());
    EXPECT_EQ(r.getFieldCount(), 0);
    EXPECT_TRUE(r.getFieldNames().empty());
}

// Test verifies that Record constructor with initial data map sets fields correctly
TEST_F(RecordTest, ConstructorWithData) {
    std::unordered_map<std::string, std::any> data;
    data["field1"] = 100;
    data["field2"] = std::string("value");

    Record r(data);
    EXPECT_FALSE(r.isEmpty());
    EXPECT_EQ(r.getFieldCount(), 2);
    EXPECT_EQ(r.getFieldAs<int>("field1"), 100);
    EXPECT_EQ(r.getFieldAs<std::string>("field2"), "value");
}

// Test verifies that Record constructor with data and metadata initializes both correctly
TEST_F(RecordTest, ConstructorWithDataAndMetadata) {
    std::unordered_map<std::string, std::any> data;
    data["field1"] = 100;

    Record::RecordMetadata metadata;
    metadata.source_table = "source_table";
    metadata.target_table = "target_table";
    metadata.source_row_number = 10;
    metadata.record_id = "rec-123";

    Record r(data, metadata);
    EXPECT_EQ(r.getMetadata().source_table, "source_table");
    EXPECT_EQ(r.getMetadata().target_table, "target_table");
    EXPECT_EQ(r.getMetadata().source_row_number, 10);
    EXPECT_EQ(r.getMetadata().record_id, "rec-123");
}

// Test verifies that Record field values can be set and retrieved correctly with UK data
TEST_F(RecordTest, SetAndGetField) {
    record.setField("new_field", 999);
    EXPECT_EQ(record.getFieldAs<int>("new_field"), 999);

    // Overwrite existing field with UK NHS number
    record.setField("nhs_number", std::string("************"));
    EXPECT_EQ(record.getFieldAs<std::string>("nhs_number"), "************");
    
    // Test UK postcode
    record.setField("postcode", std::string("M1 1AA"));
    EXPECT_EQ(record.getFieldAs<std::string>("postcode"), "M1 1AA");
}

// Test verifies that Record throws exception when getting non-existent field
TEST_F(RecordTest, GetNonExistentFieldThrows) {
    EXPECT_THROW(record.getField("nonexistent"), omop::common::ValidationException);
}

// Test verifies that Record getFieldAs performs correct type casting with UK data
TEST_F(RecordTest, GetFieldAs) {
    EXPECT_EQ(record.getFieldAs<int>("patient_id"), 42);
    EXPECT_EQ(record.getFieldAs<std::string>("nhs_number"), "************");
    EXPECT_EQ(record.getFieldAs<std::string>("name"), "John Smith");
    EXPECT_EQ(record.getFieldAs<std::string>("postcode"), "SW1A 1AA");
    EXPECT_DOUBLE_EQ(record.getFieldAs<double>("temperature_celsius"), 37.2);
    EXPECT_DOUBLE_EQ(record.getFieldAs<double>("weight_kg"), 75.5);
    EXPECT_DOUBLE_EQ(record.getFieldAs<double>("height_cm"), 180.0);
    EXPECT_EQ(record.getFieldAs<bool>("is_uk_resident"), true);
    EXPECT_DOUBLE_EQ(record.getFieldAs<double>("cost_pounds"), 125.50);
}

// Test verifies that Record getFieldAs throws exception for incorrect type cast
TEST_F(RecordTest, GetFieldAsWrongTypeThrows) {
    EXPECT_THROW(record.getFieldAs<int>("string_field"), std::bad_any_cast);
}

// Test verifies that Record getFieldOptional returns optional value correctly
TEST_F(RecordTest, GetFieldOptional) {
    auto value = record.getFieldOptional("int_field");
    ASSERT_TRUE(value.has_value());
    EXPECT_EQ(std::any_cast<int>(value.value()), 42);

    auto none = record.getFieldOptional("nonexistent");
    EXPECT_FALSE(none.has_value());
}

// Test verifies that Record hasField correctly identifies existing and non-existing fields
TEST_F(RecordTest, HasField) {
    EXPECT_TRUE(record.hasField("int_field"));
    EXPECT_TRUE(record.hasField("string_field"));
    EXPECT_FALSE(record.hasField("nonexistent"));
}

// Test verifies that Record isFieldNull correctly identifies null and non-null fields
TEST_F(RecordTest, IsFieldNull) {
    record.setField("null_field", std::any{});
    EXPECT_TRUE(record.isFieldNull("null_field"));
    EXPECT_FALSE(record.isFieldNull("int_field"));
    EXPECT_TRUE(record.isFieldNull("nonexistent"));
}

// Test verifies that Record removeField correctly removes existing fields
TEST_F(RecordTest, RemoveField) {
    EXPECT_TRUE(record.hasField("int_field"));
    EXPECT_TRUE(record.removeField("int_field"));
    EXPECT_FALSE(record.hasField("int_field"));
    EXPECT_FALSE(record.removeField("int_field")); // Already removed
}

// Test verifies that Record clear method removes all fields
TEST_F(RecordTest, Clear) {
    EXPECT_FALSE(record.isEmpty());
    record.clear();
    EXPECT_TRUE(record.isEmpty());
    EXPECT_EQ(record.getFieldCount(), 0);
}

// Test verifies that Record getFieldNames returns all field names correctly
TEST_F(RecordTest, GetFieldNames) {
    // Create a clean record with only the expected fields
    Record clean_record;
    clean_record.setField("int_field", 42);
    clean_record.setField("string_field", std::string("test"));
    clean_record.setField("double_field", 3.14);
    clean_record.setField("bool_field", true);
    
    auto names = clean_record.getFieldNames();
    EXPECT_EQ(names.size(), 4);
    EXPECT_TRUE(std::find(names.begin(), names.end(), "int_field") != names.end());
    EXPECT_TRUE(std::find(names.begin(), names.end(), "string_field") != names.end());
    EXPECT_TRUE(std::find(names.begin(), names.end(), "double_field") != names.end());
    EXPECT_TRUE(std::find(names.begin(), names.end(), "bool_field") != names.end());
}

// Test verifies that Record getFields and getFieldsMutable provide correct field access
TEST_F(RecordTest, GetFields) {
    // Create a clean record with only the expected fields
    Record clean_record;
    clean_record.setField("int_field", 42);
    clean_record.setField("string_field", std::string("test"));
    clean_record.setField("double_field", 3.14);
    clean_record.setField("bool_field", true);
    
    const auto& fields = clean_record.getFields();
    EXPECT_EQ(fields.size(), 4);

    auto& mutable_fields = clean_record.getFieldsMutable();
    mutable_fields["new_field"] = 123;
    EXPECT_TRUE(clean_record.hasField("new_field"));
}

// Test verifies that Record field metadata can be set and retrieved correctly
TEST_F(RecordTest, FieldMetadata) {
    Record::FieldMetadata metadata;
    metadata.name = "test_field";
    metadata.data_type = "integer";
    metadata.is_nullable = false;
    metadata.source_column = "src_col";
    metadata.description = "Test field";

    record.setFieldMetadata("int_field", metadata);

    auto retrieved = record.getFieldMetadata("int_field");
    ASSERT_TRUE(retrieved.has_value());
    EXPECT_EQ(retrieved->name, "test_field");
    EXPECT_EQ(retrieved->data_type, "integer");
    EXPECT_FALSE(retrieved->is_nullable);
    EXPECT_EQ(retrieved->source_column, "src_col");
    EXPECT_EQ(retrieved->description, "Test field");

    auto none = record.getFieldMetadata("nonexistent");
    EXPECT_FALSE(none.has_value());
}

// Test verifies that Record metadata can be set and retrieved correctly
TEST_F(RecordTest, RecordMetadata) {
    Record::RecordMetadata metadata;
    metadata.source_table = "patients";
    metadata.target_table = "person";
    metadata.source_row_number = 100;
    metadata.record_id = "rec-456";
    metadata.custom["key1"] = "value1";

    record.setMetadata(metadata);

    const auto& retrieved = record.getMetadata();
    EXPECT_EQ(retrieved.source_table, "patients");
    EXPECT_EQ(retrieved.target_table, "person");
    EXPECT_EQ(retrieved.source_row_number, 100);
    EXPECT_EQ(retrieved.record_id, "rec-456");
    EXPECT_EQ(retrieved.custom.at("key1"), "value1");
}

// Test verifies that Record merge operation combines records correctly with overwrite options
TEST_F(RecordTest, MergeRecords) {
    Record other;
    other.setField("int_field", 100); // Existing field
    other.setField("new_field", std::string("new"));

    record.merge(other, true); // Overwrite = true
    EXPECT_EQ(record.getFieldAs<int>("int_field"), 100);
    EXPECT_EQ(record.getFieldAs<std::string>("new_field"), "new");

    Record another;
    another.setField("int_field", 200);
    another.setField("another_field", 300);

    record.merge(another, false); // Overwrite = false
    EXPECT_EQ(record.getFieldAs<int>("int_field"), 100); // Not overwritten
    EXPECT_EQ(record.getFieldAs<int>("another_field"), 300);
}

// Test verifies that Record selectFields creates new record with only specified fields
TEST_F(RecordTest, SelectFields) {
    std::vector<std::string> fields_to_select = {"int_field", "string_field"};
    Record selected = record.selectFields(fields_to_select);

    EXPECT_EQ(selected.getFieldCount(), 2);
    EXPECT_TRUE(selected.hasField("int_field"));
    EXPECT_TRUE(selected.hasField("string_field"));
    EXPECT_FALSE(selected.hasField("double_field"));
    EXPECT_FALSE(selected.hasField("bool_field"));
}

// Test verifies that Record renameField correctly renames existing fields
TEST_F(RecordTest, RenameField) {
    EXPECT_TRUE(record.renameField("int_field", "renamed_field"));
    EXPECT_FALSE(record.hasField("int_field"));
    EXPECT_TRUE(record.hasField("renamed_field"));
    EXPECT_EQ(record.getFieldAs<int>("renamed_field"), 42);

    // Can't rename to existing field
    EXPECT_FALSE(record.renameField("string_field", "renamed_field"));

    // Can't rename non-existent field
    EXPECT_FALSE(record.renameField("nonexistent", "new_name"));
}

// Test verifies that Record JSON serialization and deserialization preserves all data
TEST_F(RecordTest, JsonSerialization) {
    // Create a clean record with only the expected fields
    Record clean_record;
    clean_record.setField("int_field", 42);
    clean_record.setField("string_field", std::string("test"));
    clean_record.setField("double_field", 3.14);
    clean_record.setField("bool_field", true);
    
    // Add metadata for complete test
    Record::RecordMetadata metadata;
    metadata.source_table = "test_source";
    metadata.target_table = "test_target";
    metadata.source_row_number = 50;
    metadata.record_id = "test-123";
    metadata.custom["custom_key"] = "custom_value";
    clean_record.setMetadata(metadata);

    // Test compact JSON
    std::string json = clean_record.toJson(false);
    EXPECT_FALSE(json.empty());

    // Test pretty JSON
    std::string pretty_json = clean_record.toJson(true);
    EXPECT_FALSE(pretty_json.empty());
    EXPECT_GT(pretty_json.length(), json.length());

    // Test deserialization
    Record deserialized = Record::fromJson(json);
    EXPECT_EQ(deserialized.getFieldCount(), clean_record.getFieldCount());
    EXPECT_EQ(deserialized.getFieldAs<int64_t>("int_field"), 42);
    EXPECT_EQ(deserialized.getFieldAs<std::string>("string_field"), "test");
    EXPECT_DOUBLE_EQ(deserialized.getFieldAs<double>("double_field"), 3.14);
    EXPECT_EQ(deserialized.getFieldAs<bool>("bool_field"), true);

    EXPECT_EQ(deserialized.getMetadata().source_table, "test_source");
    EXPECT_EQ(deserialized.getMetadata().target_table, "test_target");
}

// Test verifies that Record toString generates readable string representation
TEST_F(RecordTest, ToString) {
    // Create a clean record with only the expected fields
    Record clean_record;
    clean_record.setField("int_field", 42);
    clean_record.setField("string_field", std::string("test"));
    clean_record.setField("double_field", 3.14);
    clean_record.setField("bool_field", true);
    
    clean_record.getMetadataMutable().record_id = "test-id";
    clean_record.getMetadataMutable().source_table = "source";
    clean_record.getMetadataMutable().target_table = "target";

    std::string str = clean_record.toString();
    EXPECT_TRUE(str.find("test-id") != std::string::npos);
    EXPECT_TRUE(str.find("fields=4") != std::string::npos);
    EXPECT_TRUE(str.find("source") != std::string::npos);
    EXPECT_TRUE(str.find("target") != std::string::npos);
}

// Test verifies that Record equality operators work correctly for identical and different records
TEST_F(RecordTest, EqualityOperators) {
    Record r1;
    r1.setField("field1", 42);
    r1.setField("field2", std::string("test"));

    Record r2;
    r2.setField("field1", 42);
    r2.setField("field2", std::string("test"));

    EXPECT_EQ(r1, r2);
    EXPECT_FALSE(r1 != r2);

    r2.setField("field1", 43);
    EXPECT_NE(r1, r2);
    EXPECT_TRUE(r1 != r2);

    Record r3;
    r3.setField("field1", 42);
    EXPECT_NE(r1, r3); // Different number of fields
}

// Test verifies that Record can handle various data types including time points
TEST_F(RecordTest, DifferentDataTypes) {
    Record r;

    // Test various integer types
    r.setField("int32", int32_t(123));
    r.setField("int64", int64_t(456));
    r.setField("float", float(1.23f));
    r.setField("char_ptr", "test string");

    auto time_point = std::chrono::system_clock::now();
    r.setField("time", time_point);

    // Verify types are preserved
    EXPECT_EQ(r.getFieldAs<int32_t>("int32"), 123);
    EXPECT_EQ(r.getFieldAs<int64_t>("int64"), 456);
    EXPECT_FLOAT_EQ(r.getFieldAs<float>("float"), 1.23f);

    // JSON serialization should handle these types
    std::string json = r.toJson();
    EXPECT_FALSE(json.empty());
}

// RecordBatch tests
class RecordBatchTest : public ::testing::Test {
protected:
    RecordBatch batch;

    void SetUp() override {
        for (int i = 0; i < 5; ++i) {
            Record r;
            r.setField("id", i);
            r.setField("value", i * 10);
            batch.addRecord(r);
        }
    }
};

// Test verifies that RecordBatch default constructor creates empty batch
TEST_F(RecordBatchTest, DefaultConstructor) {
    RecordBatch b;
    EXPECT_TRUE(b.isEmpty());
    EXPECT_TRUE(b.empty());
    EXPECT_EQ(b.size(), 0);
}

// Test verifies that RecordBatch constructor with capacity reserves space correctly
TEST_F(RecordBatchTest, ConstructorWithCapacity) {
    RecordBatch b(100);
    EXPECT_TRUE(b.isEmpty());
    // Capacity is reserved but size is still 0
    EXPECT_EQ(b.size(), 0);
}

// Test verifies that RecordBatch can add records using copy and move semantics
TEST_F(RecordBatchTest, AddRecords) {
    RecordBatch b;

    Record r1;
    r1.setField("field", 1);
    b.addRecord(r1);

    Record r2;
    r2.setField("field", 2);
    b.addRecord(std::move(r2)); // Move semantics

    EXPECT_EQ(b.size(), 2);
    EXPECT_FALSE(b.isEmpty());
}

// Test verifies that RecordBatch can retrieve records by index with const and mutable access
TEST_F(RecordBatchTest, GetRecords) {
    EXPECT_EQ(batch.size(), 5);

    const Record& r = batch.getRecord(2);
    EXPECT_EQ(r.getFieldAs<int>("id"), 2);
    EXPECT_EQ(r.getFieldAs<int>("value"), 20);

    Record& mr = batch.getRecordMutable(3);
    mr.setField("value", 35);
    EXPECT_EQ(batch.getRecord(3).getFieldAs<int>("value"), 35);
}

// Test verifies that RecordBatch throws exception when accessing invalid record index
TEST_F(RecordBatchTest, GetRecordInvalidIndexThrows) {
    EXPECT_THROW(batch.getRecord(10), omop::common::ValidationException);
    EXPECT_THROW(batch.getRecordMutable(10), omop::common::ValidationException);
}

// Test verifies that RecordBatch getRecords and getRecordsMutable provide correct access to all records
TEST_F(RecordBatchTest, GetAllRecords) {
    const auto& records = batch.getRecords();
    EXPECT_EQ(records.size(), 5);

    auto& mutable_records = batch.getRecordsMutable();
    Record new_record;
    new_record.setField("id", 99);
    mutable_records.push_back(new_record);

    EXPECT_EQ(batch.size(), 6);
}

// Test verifies that RecordBatch clear method removes all records
TEST_F(RecordBatchTest, Clear) {
    EXPECT_FALSE(batch.isEmpty());
    batch.clear();
    EXPECT_TRUE(batch.isEmpty());
    EXPECT_EQ(batch.size(), 0);
}

// Test verifies that RecordBatch reserve method preallocates capacity efficiently
TEST_F(RecordBatchTest, Reserve) {
    RecordBatch b;
    b.reserve(1000);
    // Reserve doesn't change size
    EXPECT_EQ(b.size(), 0);

    // But adding many records should be efficient
    for (int i = 0; i < 100; ++i) {
        Record r;
        r.setField("id", i);
        b.addRecord(r);
    }
    EXPECT_EQ(b.size(), 100);
}

// Test verifies that RecordBatch supports range-based for loops with const and mutable iterators
TEST_F(RecordBatchTest, IteratorSupport) {
    int count = 0;
    for (auto& record : batch) {
        EXPECT_EQ(record.getFieldAs<int>("id"), count);
        count++;
    }
    EXPECT_EQ(count, 5);

    // Const iterator
    const RecordBatch& const_batch = batch;
    count = 0;
    for (const auto& record : const_batch) {
        EXPECT_EQ(record.getFieldAs<int>("id"), count);
        count++;
    }
}

// Test verifies that RecordBatch supports batch modification operations on all records
TEST_F(RecordBatchTest, BatchOperations) {
    // Modify all records in batch
    for (auto& record : batch) {
        int id = record.getFieldAs<int>("id");
        record.setField("doubled", id * 2);
    }

    // Verify modifications
    for (size_t i = 0; i < batch.size(); ++i) {
        const auto& record = batch.getRecord(i);
        int id = record.getFieldAs<int>("id");
        int doubled = record.getFieldAs<int>("doubled");
        EXPECT_EQ(doubled, id * 2);
    }
}

// UK Localization tests for dates, currency, and formatting
class UKLocalizationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for testing (with fallback to C locale if UK not available)
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            try {
                std::locale::global(std::locale("en_GB"));
            } catch (const std::runtime_error&) {
                // Fallback to C locale if UK locale not available
                std::locale::global(std::locale("C"));
            }
        }
    }
    
    void TearDown() override {
        std::locale::global(std::locale("C"));
    }
};

// Test verifies UK date format (DD/MM/YYYY) in record fields
TEST_F(UKLocalizationTest, UKDateFormat) {
    Record record;
    
    // Create a specific date: 25th December 2024
    std::tm tm = {};
    tm.tm_year = 2024 - 1900; // Years since 1900
    tm.tm_mon = 11;           // December (0-based)
    tm.tm_mday = 25;          // 25th
    
    std::time_t time = std::mktime(&tm);
    auto time_point = std::chrono::system_clock::from_time_t(time);
    
    record.setField("birth_date", time_point);
    
    // Format date in UK format
    std::ostringstream oss;
    oss << std::put_time(&tm, "%d/%m/%Y");
    std::string uk_date = oss.str();
    
    // Verify UK date format (DD/MM/YYYY)
    EXPECT_EQ(uk_date, "25/12/2024");
    
    record.setField("formatted_date", uk_date);
    EXPECT_EQ(record.getFieldAs<std::string>("formatted_date"), "25/12/2024");
}

// Test verifies UK currency formatting (£) in record fields
TEST_F(UKLocalizationTest, UKCurrencyFormat) {
    Record record;
    
    // Test various currency amounts
    record.setField("salary_amount", 45000.50);
    record.setField("bonus_amount", 2500.00);
    record.setField("expense_amount", 125.75);
    
    double salary = record.getFieldAs<double>("salary_amount");
    double bonus = record.getFieldAs<double>("bonus_amount");
    double expense = record.getFieldAs<double>("expense_amount");
    
    std::string formatted_salary = omop::common::UKLocalization::format_uk_currency(salary);
    std::string formatted_bonus = omop::common::UKLocalization::format_uk_currency(bonus);
    std::string formatted_expense = omop::common::UKLocalization::format_uk_currency(expense);
    
    EXPECT_EQ(formatted_salary, "£45,000.50");
    EXPECT_EQ(formatted_bonus, "£2,500.00");
    EXPECT_EQ(formatted_expense, "£125.75");
    
    // Store formatted values
    record.setField("formatted_salary", formatted_salary);
    record.setField("formatted_bonus", formatted_bonus);
    record.setField("formatted_expense", formatted_expense);
    
    EXPECT_EQ(record.getFieldAs<std::string>("formatted_salary"), "£45,000.50");
    EXPECT_EQ(record.getFieldAs<std::string>("formatted_bonus"), "£2,500.00");
    EXPECT_EQ(record.getFieldAs<std::string>("formatted_expense"), "£125.75");
}

// Test verifies UK temperature format (Celsius) in record fields
TEST_F(UKLocalizationTest, UKTemperatureFormat) {
    Record record;
    
    // Test temperature values in Celsius
    record.setField("body_temp", 37.2);
    record.setField("room_temp", 21.5);
    record.setField("freezing_temp", 0.0);
    
    double body_temp = record.getFieldAs<double>("body_temp");
    double room_temp = record.getFieldAs<double>("room_temp");
    double freezing_temp = record.getFieldAs<double>("freezing_temp");
    
    // Format with °C suffix using utility function
    std::string formatted_body_temp = omop::common::UKLocalization::format_temperature_celsius(body_temp);
    std::string formatted_room_temp = omop::common::UKLocalization::format_temperature_celsius(room_temp);
    std::string formatted_freezing_temp = omop::common::UKLocalization::format_temperature_celsius(freezing_temp);
    
    EXPECT_EQ(formatted_body_temp, "37.2°C");
    EXPECT_EQ(formatted_room_temp, "21.5°C");
    EXPECT_EQ(formatted_freezing_temp, "0.0°C");
    
    record.setField("formatted_body_temp", formatted_body_temp);
    record.setField("formatted_room_temp", formatted_room_temp);
    record.setField("formatted_freezing_temp", formatted_freezing_temp);
    
    EXPECT_EQ(record.getFieldAs<std::string>("formatted_body_temp"), "37.2°C");
    EXPECT_EQ(record.getFieldAs<std::string>("formatted_room_temp"), "21.5°C");
    EXPECT_EQ(record.getFieldAs<std::string>("formatted_freezing_temp"), "0.0°C");
}

// Test verifies UK postal code format validation in record fields
TEST_F(UKLocalizationTest, UKPostalCodeFormat) {
    Record record;
    
    // Valid UK postal code formats
    std::vector<std::string> valid_postcodes = {
        "SW1A 1AA",  // London (Westminster)
        "M1 1AA",    // Manchester
        "B33 8TH",   // Birmingham
        "W1A 0AX",   // London (Oxford Street)
        "EC1A 1BB",  // London (City)
        "NW1 6XE",   // London (Camden)
        "E1 6AN"     // London (East End)
    };
    
    for (size_t i = 0; i < valid_postcodes.size(); ++i) {
        std::string field_name = "postcode_" + std::to_string(i);
        record.setField(field_name, valid_postcodes[i]);
        
        std::string stored_postcode = record.getFieldAs<std::string>(field_name);
        EXPECT_EQ(stored_postcode, valid_postcodes[i]);
        
        // Verify format (letters and numbers with space)
        EXPECT_TRUE(stored_postcode.find(' ') != std::string::npos);
        EXPECT_GE(stored_postcode.length(), 6);
        EXPECT_LE(stored_postcode.length(), 8);
    }
}

// Test verifies UK time format (24-hour format) in record fields
TEST_F(UKLocalizationTest, UKTimeFormat) {
    Record record;
    
    // Create specific times
    std::tm morning = {};
    morning.tm_hour = 9;
    morning.tm_min = 30;
    morning.tm_sec = 0;
    
    std::tm afternoon = {};
    afternoon.tm_hour = 14;
    afternoon.tm_min = 45;
    afternoon.tm_sec = 30;
    
    std::tm evening = {};
    evening.tm_hour = 19;
    evening.tm_min = 15;
    evening.tm_sec = 45;
    
    // Format in UK 24-hour format
    std::ostringstream morning_stream, afternoon_stream, evening_stream;
    morning_stream << std::put_time(&morning, "%H:%M:%S");
    afternoon_stream << std::put_time(&afternoon, "%H:%M:%S");
    evening_stream << std::put_time(&evening, "%H:%M:%S");
    
    std::string morning_time = morning_stream.str();
    std::string afternoon_time = afternoon_stream.str();
    std::string evening_time = evening_stream.str();
    
    EXPECT_EQ(morning_time, "09:30:00");
    EXPECT_EQ(afternoon_time, "14:45:30");
    EXPECT_EQ(evening_time, "19:15:45");
    
    record.setField("morning_time", morning_time);
    record.setField("afternoon_time", afternoon_time);
    record.setField("evening_time", evening_time);
    
    EXPECT_EQ(record.getFieldAs<std::string>("morning_time"), "09:30:00");
    EXPECT_EQ(record.getFieldAs<std::string>("afternoon_time"), "14:45:30");
    EXPECT_EQ(record.getFieldAs<std::string>("evening_time"), "19:15:45");
}

// Test verifies UK decimal formatting (using decimal point, not comma) in record fields
TEST_F(UKLocalizationTest, UKDecimalFormat) {
    Record record;
    
    // Test various decimal values
    record.setField("precise_value", 123.456789);
    record.setField("currency_value", 1234.56);
    record.setField("scientific_value", 0.000123);
    
    double precise = record.getFieldAs<double>("precise_value");
    double currency = record.getFieldAs<double>("currency_value");
    double scientific = record.getFieldAs<double>("scientific_value");
    
    // Use the UKLocalization utility function for decimal formatting
    std::string precise_str = omop::common::UKLocalization::format_uk_decimal(precise, 3, false);
    std::string currency_str = omop::common::UKLocalization::format_uk_decimal(currency, 2, true);
    
    std::ostringstream scientific_stream;
    scientific_stream << std::scientific << std::setprecision(3) << scientific;
    std::string scientific_str = scientific_stream.str();
    
    // Verify decimal point is used (not comma)
    EXPECT_TRUE(precise_str.find('.') != std::string::npos);
    EXPECT_TRUE(currency_str.find('.') != std::string::npos);
    EXPECT_TRUE(scientific_str.find('.') != std::string::npos);
    
    EXPECT_EQ(precise_str, "123.457");  // Rounded to 3 decimal places
    EXPECT_EQ(currency_str, "1,234.56"); // 2 decimal places for currency with UK thousands separator
    
    record.setField("formatted_precise", precise_str);
    record.setField("formatted_currency", currency_str);
    record.setField("formatted_scientific", scientific_str);
    
    EXPECT_EQ(record.getFieldAs<std::string>("formatted_precise"), "123.457");
    EXPECT_EQ(record.getFieldAs<std::string>("formatted_currency"), "1,234.56");
}

// Test verifies UK medical data formats (NHS number, height in cm, weight in kg)
TEST_F(UKLocalizationTest, UKMedicalDataFormat) {
    Record record;
    
    // NHS number format (10 digits with specific validation)
    record.setField("nhs_number", std::string("**********"));
    
    // Height in centimetres (UK metric system)
    record.setField("height_cm", 175.5);
    
    // Weight in kilograms (UK metric system)
    record.setField("weight_kg", 70.3);
    
    std::string nhs_number = record.getFieldAs<std::string>("nhs_number");
    double height = record.getFieldAs<double>("height_cm");
    double weight = record.getFieldAs<double>("weight_kg");
    
    // Verify NHS number is 10 digits
    EXPECT_EQ(nhs_number.length(), 10);
    EXPECT_TRUE(std::all_of(nhs_number.begin(), nhs_number.end(), ::isdigit));
    
    // Format height and weight with units
    std::ostringstream height_stream, weight_stream;
    height_stream << std::fixed << std::setprecision(1) << height << " cm";
    weight_stream << std::fixed << std::setprecision(1) << weight << " kg";
    
    EXPECT_EQ(height_stream.str(), "175.5 cm");
    EXPECT_EQ(weight_stream.str(), "70.3 kg");
    
    record.setField("formatted_height", height_stream.str());
    record.setField("formatted_weight", weight_stream.str());
    
    EXPECT_EQ(record.getFieldAs<std::string>("formatted_height"), "175.5 cm");
    EXPECT_EQ(record.getFieldAs<std::string>("formatted_weight"), "70.3 kg");
}

// Test verifies UK-specific healthcare data validation and formatting
TEST_F(RecordTest, UKHealthcareDataValidation) {
    Record patient_record;
    
    // Set up UK patient data
    patient_record.setField("nhs_number", std::string("************"));
    patient_record.setField("name", std::string("Sarah Johnson"));
    patient_record.setField("postcode", std::string("M1 1AA"));
    patient_record.setField("hospital", std::string("Royal London Hospital"));
    patient_record.setField("temperature_celsius", 38.5);
    patient_record.setField("blood_pressure_systolic", 120);
    patient_record.setField("blood_pressure_diastolic", 80);
    patient_record.setField("prescription_cost_pounds", 8.60);  // UK prescription charge
    
    // Test NHS number format validation
    std::string nhs_number = patient_record.getFieldAs<std::string>("nhs_number");
    EXPECT_EQ(nhs_number.length(), 12); // Including spaces
    EXPECT_EQ(nhs_number[3], ' ');      // Space after first 3 digits
    EXPECT_EQ(nhs_number[7], ' ');      // Space after next 3 digits
    
    // Test UK postcode format
    std::string postcode = patient_record.getFieldAs<std::string>("postcode");
    EXPECT_TRUE(postcode.find(' ') != std::string::npos); // Should contain space
    
    // Test temperature in Celsius (UK standard)
    double temperature_c = patient_record.getFieldAs<double>("temperature_celsius");
    EXPECT_GT(temperature_c, 35.0);  // Normal body temp range
    EXPECT_LT(temperature_c, 42.0);  // Upper fever range
    
    // Test UK prescription cost
    double cost = patient_record.getFieldAs<double>("prescription_cost_pounds");
    EXPECT_DOUBLE_EQ(cost, 8.60);  // Standard UK prescription charge
}

// Test verifies UK regional date and time formatting
TEST_F(RecordTest, UKDateTimeFormatting) {
    Record appointment_record;
    
    // Set up appointment with UK date/time
    auto appointment_time = std::chrono::system_clock::now();
    appointment_record.setField("appointment_datetime", appointment_time);
    appointment_record.setField("duration_minutes", 30);
    appointment_record.setField("clinic", std::string("UCL Hospital"));
    appointment_record.setField("consultant", std::string("Dr. Smith"));
    
    // Test time retrieval
    auto retrieved_time = appointment_record.getFieldAs<std::chrono::system_clock::time_point>("appointment_datetime");
    EXPECT_EQ(retrieved_time.time_since_epoch().count(), appointment_time.time_since_epoch().count());
    
    // Test duration
    int duration = appointment_record.getFieldAs<int>("duration_minutes");
    EXPECT_EQ(duration, 30);
    
    // Test UK clinic name
    std::string clinic = appointment_record.getFieldAs<std::string>("clinic");
    EXPECT_EQ(clinic, "UCL Hospital");
}

// Test verifies complex UK medical record with comprehensive field coverage
TEST_F(RecordTest, ComplexUKMedicalRecord) {
    Record medical_record;
    
    // Patient demographics (UK format)
    medical_record.setField("patient_id", 10001);
    medical_record.setField("nhs_number", std::string("************"));
    medical_record.setField("title", std::string("Ms"));
    medical_record.setField("forename", std::string("Emily"));
    medical_record.setField("surname", std::string("Watson"));
    medical_record.setField("date_of_birth", std::string("15/03/1985")); // DD/MM/YYYY
    medical_record.setField("postcode", std::string("SW1A 1AA"));
    medical_record.setField("gp_practice", std::string("Regent Street Surgery"));
    
    // Clinical measurements (UK units)
    medical_record.setField("height_cm", 165.5);
    medical_record.setField("weight_kg", 68.2);
    medical_record.setField("bmi", 25.0);
    medical_record.setField("temperature_celsius", 36.8);
    medical_record.setField("blood_glucose_mmol_l", 5.5);  // UK uses mmol/L
    
    // Treatment and costs (UK format)
    medical_record.setField("diagnosis_code", std::string("K59.1"));  // ICD-10
    medical_record.setField("treatment_cost_pounds", 245.75);
    medical_record.setField("is_nhs_funded", true);
    medical_record.setField("ward", std::string("Ward 7B"));
    
    // Verify all fields are accessible and correct
    EXPECT_EQ(medical_record.getFieldAs<int>("patient_id"), 10001);
    EXPECT_EQ(medical_record.getFieldAs<std::string>("nhs_number"), "************");
    EXPECT_EQ(medical_record.getFieldAs<std::string>("title"), "Ms");
    EXPECT_EQ(medical_record.getFieldAs<std::string>("forename"), "Emily");
    EXPECT_EQ(medical_record.getFieldAs<std::string>("surname"), "Watson");
    EXPECT_EQ(medical_record.getFieldAs<std::string>("date_of_birth"), "15/03/1985");
    EXPECT_EQ(medical_record.getFieldAs<std::string>("postcode"), "SW1A 1AA");
    EXPECT_EQ(medical_record.getFieldAs<std::string>("gp_practice"), "Regent Street Surgery");
    
    EXPECT_DOUBLE_EQ(medical_record.getFieldAs<double>("height_cm"), 165.5);
    EXPECT_DOUBLE_EQ(medical_record.getFieldAs<double>("weight_kg"), 68.2);
    EXPECT_DOUBLE_EQ(medical_record.getFieldAs<double>("bmi"), 25.0);
    EXPECT_DOUBLE_EQ(medical_record.getFieldAs<double>("temperature_celsius"), 36.8);
    EXPECT_DOUBLE_EQ(medical_record.getFieldAs<double>("blood_glucose_mmol_l"), 5.5);
    
    EXPECT_EQ(medical_record.getFieldAs<std::string>("diagnosis_code"), "K59.1");
    EXPECT_DOUBLE_EQ(medical_record.getFieldAs<double>("treatment_cost_pounds"), 245.75);
    EXPECT_EQ(medical_record.getFieldAs<bool>("is_nhs_funded"), true);
    EXPECT_EQ(medical_record.getFieldAs<std::string>("ward"), "Ward 7B");
    
    // Verify field count and names
    EXPECT_EQ(medical_record.getFieldCount(), 17);
    auto field_names = medical_record.getFieldNames();
    EXPECT_EQ(field_names.size(), 17);
    
    // Verify required UK fields are present
    EXPECT_TRUE(medical_record.hasField("nhs_number"));
    EXPECT_TRUE(medical_record.hasField("postcode"));
    EXPECT_TRUE(medical_record.hasField("gp_practice"));
    EXPECT_TRUE(medical_record.hasField("is_nhs_funded"));
}

// Test verifies UK localized batch processing with healthcare data
TEST(RecordBatchUKTest, HealthcareBatchProcessing) {
    RecordBatch uk_patient_batch;
    
    // Create batch of UK patients
    std::vector<std::string> uk_postcodes = {
        "SW1A 1AA", "M1 1AA", "B33 8TH", "W1A 0AX", "EC1A 1BB"
    };
    
    std::vector<std::string> nhs_numbers = {
        "************", "************", "************", "************", "************"
    };
    
    for (size_t i = 0; i < uk_postcodes.size(); ++i) {
        Record patient;
        patient.setField("patient_id", static_cast<int>(i + 1));
        patient.setField("nhs_number", nhs_numbers[i]);
        patient.setField("postcode", uk_postcodes[i]);
        patient.setField("temperature_celsius", 36.5 + (i * 0.2));
        patient.setField("weight_kg", 65.0 + (i * 2.5));
        patient.setField("is_uk_resident", true);
        uk_patient_batch.addRecord(std::move(patient));
    }
    
    EXPECT_EQ(uk_patient_batch.size(), 5);
    EXPECT_FALSE(uk_patient_batch.isEmpty());
    
    // Test batch iteration
    size_t count = 0;
    for (const auto& patient : uk_patient_batch) {
        EXPECT_TRUE(patient.hasField("nhs_number"));
        EXPECT_TRUE(patient.hasField("postcode"));
        EXPECT_EQ(patient.getFieldAs<bool>("is_uk_resident"), true);
        count++;
    }
    EXPECT_EQ(count, 5);
    
    // Test sorting by NHS number
    auto sorted_batch = uk_patient_batch.sorted([](const Record& record) {
        return record.getField("nhs_number");
    });
    
    EXPECT_EQ(sorted_batch.size(), 5);
    EXPECT_TRUE(sorted_batch.getRecord(0).hasField("nhs_number"));
}
