/**
 * @file processing_context_test.cpp
 * @brief Unit tests for ProcessingContext class with UK localisation
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/interfaces.h"
#include "common/utilities.h"
#include <thread>
#include <chrono>
#include <atomic>
#include <locale>
#include <iomanip>
#include <sstream>

using namespace omop::core;
using namespace std::chrono_literals;
using ::testing::_;

// Test fixture for ProcessingContext tests with UK locale setup
class ProcessingContextTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            // Fallback if UK locale not available
            std::locale::global(std::locale("C"));
        }
        context_ = std::make_unique<ProcessingContext>();
        
        // Set UK timezone and currency formatting
        uk_currency_symbol_ = "£";
        uk_decimal_separator_ = ".";
        uk_thousands_separator_ = ",";
    }

    std::unique_ptr<ProcessingContext> context_;
    std::string uk_currency_symbol_;
    std::string uk_decimal_separator_;
    std::string uk_thousands_separator_;


};

// Test that context initialises correctly with default values
TEST_F(ProcessingContextTest, ContextInitialisationWithDefaults) {
    ProcessingContext context;
    EXPECT_EQ(ProcessingContext::Stage::Extract, context.current_stage());
    EXPECT_TRUE(context.job_id().empty());
    EXPECT_EQ(0u, context.processed_count());
    EXPECT_EQ(0u, context.error_count());
    EXPECT_FALSE(context.is_error_threshold_exceeded());
}

// Test that context data stores and retrieves values correctly with UK formatting
TEST_F(ProcessingContextTest, ContextDataStorageWithUKFormatting) {
    // Test with UK monetary values
    std::string price = omop::common::UKLocalization::format_uk_currency(1234.56);
    context_->set_data("price", price);
    
    auto retrieved_price = context_->get_data("price");
    ASSERT_TRUE(retrieved_price.has_value());
    EXPECT_EQ("£1,234.56", std::any_cast<std::string>(*retrieved_price));
    
    // Test with UK temperature values
    std::string temp = omop::common::UKLocalization::format_temperature_celsius(22.5);
    context_->set_data("temperature", temp);
    
    auto retrieved_temp = context_->get_data("temperature");
    ASSERT_TRUE(retrieved_temp.has_value());
    EXPECT_EQ("22.5°C", std::any_cast<std::string>(*retrieved_temp));
}

// Test that context handles UK postal codes correctly
TEST_F(ProcessingContextTest, ContextHandlesUKPostalCodes) {
    std::vector<std::string> uk_postcodes = {
        "SW1A 1AA",  // Central London
        "M1 1AA",    // Manchester
        "B33 8TH",   // Birmingham
        "G2 3AT",    // Glasgow
        "LS1 4DY"    // Leeds
    };
    
    for (size_t i = 0; i < uk_postcodes.size(); ++i) {
        std::string key = "postcode_" + std::to_string(i);
        context_->set_data(key, uk_postcodes[i]);
        
        auto retrieved = context_->get_data(key);
        ASSERT_TRUE(retrieved.has_value());
        EXPECT_EQ(uk_postcodes[i], std::any_cast<std::string>(*retrieved));
    }
}

// Test that context manages processing stages correctly
TEST_F(ProcessingContextTest, ContextStageManagement) {
    EXPECT_EQ(ProcessingContext::Stage::Extract, context_->current_stage());
    
    context_->set_stage(ProcessingContext::Stage::Transform);
    EXPECT_EQ(ProcessingContext::Stage::Transform, context_->current_stage());
    
    context_->set_stage(ProcessingContext::Stage::Load);
    EXPECT_EQ(ProcessingContext::Stage::Load, context_->current_stage());
}

// Test that context tracks elapsed time accurately
TEST_F(ProcessingContextTest, ContextTimeTracking) {
    auto start_elapsed = context_->elapsed_time();
    
    // Sleep for a measurable duration
    std::this_thread::sleep_for(100ms);
    
    auto end_elapsed = context_->elapsed_time();
    
    EXPECT_GT(end_elapsed.count(), start_elapsed.count());
    EXPECT_GE(end_elapsed.count(), 0.1); // At least 100ms
}

// Test that context correctly handles error thresholds
TEST_F(ProcessingContextTest, ErrorThresholdCalculationWithUKPercentages) {
    context_->set_error_threshold(0.05); // 5% error threshold (UK decimal format)
    
    // Process 100 records with 3 errors (3% error rate)
    context_->increment_processed(100);
    context_->increment_errors(3);
    EXPECT_FALSE(context_->is_error_threshold_exceeded());
    
    // Add more errors to exceed 5% threshold (8% error rate)
    context_->increment_errors(5);
    EXPECT_TRUE(context_->is_error_threshold_exceeded());
}

// Test that error callbacks work correctly with UK formatting
TEST_F(ProcessingContextTest, ErrorCallbackWithUKFormatting) {
    bool callback_called = false;
    std::string callback_message;
    
    context_->set_error_callback([&](const std::string& msg) {
        callback_called = true;
        callback_message = msg;
    });
    
    context_->set_error_threshold(0.10); // 10% threshold
    context_->increment_processed(100);
    context_->increment_errors(15); // 15% error rate
    
    EXPECT_TRUE(callback_called);
    EXPECT_FALSE(callback_message.empty());
    // Should contain percentage formatted for UK
    EXPECT_NE(callback_message.find("15"), std::string::npos);
}

// Test that job ID management works correctly
TEST_F(ProcessingContextTest, JobIdManagement) {
    EXPECT_TRUE(context_->job_id().empty());
    
    std::string uk_format_job_id = "ETL-15-01-2025-001"; // UK date format DD-MM-YYYY
    context_->set_job_id(uk_format_job_id);
    EXPECT_EQ(uk_format_job_id, context_->job_id());
}

// Test that logging functionality works with UK formatting
TEST_F(ProcessingContextTest, LoggingWithUKFormatting) {
    context_->set_job_id("UK-ETL-Job-001");
    context_->set_stage(ProcessingContext::Stage::Transform);
    
    // Test logging with UK currency amounts
    std::string log_message = "Processing record with value " + omop::common::UKLocalization::format_uk_currency(125.99);
    EXPECT_NO_THROW(context_->log("info", log_message));
    
    // Test logging with UK temperature readings
    std::string temp_message = "Sensor reading: " + omop::common::UKLocalization::format_temperature_celsius(36.5);
    EXPECT_NO_THROW(context_->log("debug", temp_message));
}

// Test that context data handles concurrent access correctly
TEST_F(ProcessingContextTest, ThreadSafeContextDataAccess) {
    const int num_threads = 10;
    const int operations_per_thread = 100;
    std::vector<std::thread> threads;
    std::atomic<bool> start_flag{false};

    // Launch threads that access context data concurrently
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, operations_per_thread, &start_flag]() {
            // Wait for all threads to be ready
            while (!start_flag.load()) {
                std::this_thread::yield();
            }
            
            for (int j = 0; j < operations_per_thread; ++j) {
                std::string key = "thread_" + std::to_string(i) + "_key_" + std::to_string(j);
                std::string value = omop::common::UKLocalization::format_uk_currency(static_cast<double>(i * operations_per_thread + j));
                
                context_->set_data(key, value);
                auto retrieved = context_->get_data(key);
                
                EXPECT_TRUE(retrieved.has_value());
            }
        });
    }

    start_flag.store(true);

    for (auto& thread : threads) {
        thread.join();
    }
}

// Test that context handles edge cases with empty strings
TEST_F(ProcessingContextTest, EdgeCaseEmptyStringHandling) {
    context_->set_job_id("");
    EXPECT_TRUE(context_->job_id().empty());
    
    context_->set_data("", std::string("empty_key_value"));
    auto val = context_->get_data("");
    ASSERT_TRUE(val.has_value());
    EXPECT_EQ("empty_key_value", std::any_cast<std::string>(*val));
    
    EXPECT_NO_THROW(context_->log("", ""));
}

// Test that context handles large counter values correctly
TEST_F(ProcessingContextTest, LargeCounterValueHandling) {
    size_t large_value = 1000000;
    context_->increment_processed(large_value);
    context_->increment_errors(large_value / 2);
    
    EXPECT_EQ(large_value, context_->processed_count());
    EXPECT_EQ(large_value / 2, context_->error_count());
    
    // Error rate should be 50%
    context_->set_error_threshold(0.4); // 40% threshold
    EXPECT_TRUE(context_->is_error_threshold_exceeded());
}

// Test that context processes a complete ETL scenario correctly
TEST_F(ProcessingContextTest, CompleteETLScenarioWithUKData) {
    std::string uk_job_id = "NHS-ETL-15-01-2025-001"; // UK date format DD-MM-YYYY
    context_->set_job_id(uk_job_id);
    
    // Extract stage with UK NHS data
    context_->set_stage(ProcessingContext::Stage::Extract);
    context_->set_data("source_file", std::string("/data/nhs_patients.csv"));
    context_->set_data("extract_start_time", std::chrono::system_clock::now());
    context_->set_data("region", std::string("Greater London"));
    
    // Simulate extraction of 1000 patient records
    for (int i = 0; i < 1000; ++i) {
        context_->increment_processed();
        if (i % 100 == 0) {
            context_->increment_errors(); // 1% error rate
        }
    }
    
    // Transform stage
    context_->set_stage(ProcessingContext::Stage::Transform);
    context_->set_data("currency_conversion", omop::common::UKLocalization::format_uk_currency(1.0)); // No conversion needed
    
    // Load stage
    context_->set_stage(ProcessingContext::Stage::Load);
    context_->set_data("target_database", std::string("UK_OMOP_CDM"));
    
    // Verify final state
    EXPECT_EQ(ProcessingContext::Stage::Load, context_->current_stage());
    EXPECT_EQ(1000u, context_->processed_count());
    EXPECT_EQ(10u, context_->error_count());
    EXPECT_EQ(uk_job_id, context_->job_id());
    
    // Verify stored UK-specific data
    auto region = context_->get_data("region");
    ASSERT_TRUE(region.has_value());
    EXPECT_EQ("Greater London", std::any_cast<std::string>(*region));
}