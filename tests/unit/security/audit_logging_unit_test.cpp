#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "security/audit_logger.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>

namespace omop::security::test {

using namespace std::chrono_literals;
using ::testing::_;
using ::testing::Return;
using ::testing::AtLeast;

class AuditLoggerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test directory
        test_dir_ = std::filesystem::temp_directory_path() / "omop_audit_test";
        std::filesystem::create_directories(test_dir_);
        
        // Set UK locale for date/time formatting
        std::locale::global(std::locale("en_GB.UTF-8"));
        
        // Create logger instance
        logger_ = create_audit_logger();
        
        // Default configuration
        config_ = get_default_audit_config();
        config_.log_file_path = (test_dir_ / "audit.log").string();
        config_.max_file_size = 1024 * 1024; // 1MB for testing
        config_.flush_interval = 1s;
        config_.buffer_size = 10;
    }
    
    void TearDown() override {
        // Clean up test directory
        std::filesystem::remove_all(test_dir_);
    }
    
    // Helper to create test event
    AuditEvent create_test_event(AuditEventType type = AuditEventType::Authentication) {
        AuditEvent event;
        event.id = generate_event_id();
        event.timestamp = std::chrono::system_clock::now();
        event.event_type = type;
        event.severity = AuditSeverity::Medium;
        event.outcome = AuditOutcome::Success;
        event.subject = "test_user";
        event.resource = "test_resource";
        event.action = "test_action";
        event.description = "Test event";
        event.source_ip = "*************";
        event.user_agent = "TestAgent/1.0";
        event.session_id = "session_123";
        event.request_id = "request_456";
        return event;
    }
    
    // Helper to read log file
    std::vector<std::string> read_log_file(const std::string& path) {
        std::vector<std::string> lines;
        std::ifstream file(path);
        std::string line;
        while (std::getline(file, line)) {
            lines.push_back(line);
        }
        return lines;
    }
    
    std::filesystem::path test_dir_;
    std::unique_ptr<IAuditLogger> logger_;
    AuditConfig config_;
};

// Tests initialization with various configurations
TEST_F(AuditLoggerTest, InitializationSuccess) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    auto retrieved_config = logger_->get_config();
    EXPECT_EQ(retrieved_config.log_file_path, config_.log_file_path);
    EXPECT_EQ(retrieved_config.max_file_size, config_.max_file_size);
}

// Tests initialization with disabled audit logging
TEST_F(AuditLoggerTest, InitializationDisabled) {
    config_.enabled = false;
    EXPECT_TRUE(logger_->initialize(config_));
    
    // Should not log events when disabled
    auto event = create_test_event();
    EXPECT_FALSE(logger_->log_event(event));
}

// Tests basic event logging
TEST_F(AuditLoggerTest, LogBasicEvent) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    auto event = create_test_event();
    EXPECT_TRUE(logger_->log_event(event));
    
    // Wait for background thread to flush
    std::this_thread::sleep_for(2s);
    
    auto lines = read_log_file(config_.log_file_path);
    EXPECT_FALSE(lines.empty());
    
    // Check that the log contains our event
    bool found_event = false;
    for (const auto& line : lines) {
        if (line.find("test_user") != std::string::npos && 
            line.find("test_action") != std::string::npos) {
            found_event = true;
            break;
        }
    }
    EXPECT_TRUE(found_event);
}

// Tests different event types
TEST_F(AuditLoggerTest, LogDifferentEventTypes) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    std::vector<AuditEventType> event_types = {
        AuditEventType::Authentication,
        AuditEventType::DataAccess,
        AuditEventType::ConfigurationChange,
        AuditEventType::JobExecution
    };
    
    for (auto type : event_types) {
        auto event = create_test_event(type);
        EXPECT_TRUE(logger_->log_event(event));
    }
    
    // Wait for background thread to flush
    std::this_thread::sleep_for(2s);
    
    auto lines = read_log_file(config_.log_file_path);
    EXPECT_EQ(lines.size(), event_types.size());
}

// Tests different severity levels
TEST_F(AuditLoggerTest, LogDifferentSeverityLevels) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    std::vector<AuditSeverity> severities = {
        AuditSeverity::Low,
        AuditSeverity::Medium,
        AuditSeverity::High,
        AuditSeverity::Critical
    };
    
    for (auto severity : severities) {
        auto event = create_test_event();
        event.severity = severity;
        EXPECT_TRUE(logger_->log_event(event));
    }
    
    // Wait for background thread to flush
    std::this_thread::sleep_for(2s);
    
    auto lines = read_log_file(config_.log_file_path);
    EXPECT_EQ(lines.size(), severities.size());
}

// Tests different outcomes
TEST_F(AuditLoggerTest, LogDifferentOutcomes) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    std::vector<AuditOutcome> outcomes = {
        AuditOutcome::Success,
        AuditOutcome::Failure,
        AuditOutcome::Partial
    };
    
    for (auto outcome : outcomes) {
        auto event = create_test_event();
        event.outcome = outcome;
        EXPECT_TRUE(logger_->log_event(event));
    }
    
    // Wait for background thread to flush
    std::this_thread::sleep_for(2s);
    
    auto lines = read_log_file(config_.log_file_path);
    EXPECT_EQ(lines.size(), outcomes.size());
}

// Tests event with context
TEST_F(AuditLoggerTest, LogEventWithContext) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    auto event = create_test_event();
    event.context["user_role"] = "admin";
    event.context["session_duration"] = "3600";
    event.context["data_source"] = "csv_file";
    
    EXPECT_TRUE(logger_->log_event(event));
    
    // Wait for background thread to flush
    std::this_thread::sleep_for(2s);
    
    auto lines = read_log_file(config_.log_file_path);
    EXPECT_FALSE(lines.empty());
    
    // Check that context is included in log
    bool found_context = false;
    for (const auto& line : lines) {
        if (line.find("admin") != std::string::npos && 
            line.find("csv_file") != std::string::npos) {
            found_context = true;
            break;
        }
    }
    EXPECT_TRUE(found_context);
}

// Tests buffer overflow handling
TEST_F(AuditLoggerTest, BufferOverflowHandling) {
    config_.buffer_size = 3;
    config_.flush_interval = 5s; // Long interval to test buffer overflow
    EXPECT_TRUE(logger_->initialize(config_));
    
    // Log more events than buffer size
    for (int i = 0; i < 5; ++i) {
        auto event = create_test_event();
        event.description = "Event " + std::to_string(i);
        EXPECT_TRUE(logger_->log_event(event));
    }
    
    // Wait a bit for potential flush
    std::this_thread::sleep_for(1s);
    
    auto lines = read_log_file(config_.log_file_path);
    // Should have flushed due to buffer overflow
    EXPECT_GT(lines.size(), 0);
}

// Tests log file rotation
TEST_F(AuditLoggerTest, LogFileRotation) {
    config_.max_file_size = 100; // Small size to trigger rotation
    EXPECT_TRUE(logger_->initialize(config_));
    
    // Log enough events to exceed file size
    for (int i = 0; i < 10; ++i) {
        auto event = create_test_event();
        event.description = "Large event description that will make the log file grow quickly " + std::to_string(i);
        EXPECT_TRUE(logger_->log_event(event));
    }
    
    // Wait for background thread to process
    std::this_thread::sleep_for(2s);
    
    // Check if rotation occurred
    bool found_backup = false;
    for (const auto& entry : std::filesystem::directory_iterator(test_dir_)) {
        if (entry.path().filename().string().find("audit_") != std::string::npos &&
            entry.path().filename().string().find(".log") != std::string::npos) {
            found_backup = true;
            break;
        }
    }
    EXPECT_TRUE(found_backup);
}

// Tests concurrent logging
TEST_F(AuditLoggerTest, ConcurrentLogging) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    const int num_threads = 5;
    const int events_per_thread = 10;
    std::vector<std::thread> threads;
    
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([this, i, events_per_thread]() {
            for (int j = 0; j < events_per_thread; ++j) {
                auto event = create_test_event();
                event.subject = "thread_" + std::to_string(i);
                event.description = "Event " + std::to_string(j) + " from thread " + std::to_string(i);
                logger_->log_event(event);
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Wait for background thread to flush
    std::this_thread::sleep_for(2s);
    
    auto lines = read_log_file(config_.log_file_path);
    EXPECT_EQ(lines.size(), num_threads * events_per_thread);
}

// Tests authentication logging
TEST_F(AuditLoggerTest, LogAuthentication) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    EXPECT_TRUE(logger_->log_authentication("test_user", "login", true, "Successful login"));
    EXPECT_TRUE(logger_->log_authentication("test_user", "login", false, "Invalid password"));
    
    // Wait for background thread to flush
    std::this_thread::sleep_for(2s);
    
    auto lines = read_log_file(config_.log_file_path);
    EXPECT_EQ(lines.size(), 2);
    
    // Check that authentication events have correct type
    bool found_success = false, found_failure = false;
    for (const auto& line : lines) {
        if (line.find("Authentication") != std::string::npos) {
            if (line.find("Success") != std::string::npos) {
                found_success = true;
            } else if (line.find("Failure") != std::string::npos) {
                found_failure = true;
            }
        }
    }
    EXPECT_TRUE(found_success);
    EXPECT_TRUE(found_failure);
}

// Tests data access logging
TEST_F(AuditLoggerTest, LogDataAccess) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    EXPECT_TRUE(logger_->log_data_access("person", "SELECT", 1000, "test_user"));
    EXPECT_TRUE(logger_->log_data_access("condition_occurrence", "INSERT", 500, "test_user"));
    
    // Wait for background thread to flush
    std::this_thread::sleep_for(2s);
    
    auto lines = read_log_file(config_.log_file_path);
    EXPECT_EQ(lines.size(), 2);
    
    // Check that data access events have correct type
    bool found_select = false, found_insert = false;
    for (const auto& line : lines) {
        if (line.find("DataAccess") != std::string::npos) {
            if (line.find("SELECT") != std::string::npos) {
                found_select = true;
            } else if (line.find("INSERT") != std::string::npos) {
                found_insert = true;
            }
        }
    }
    EXPECT_TRUE(found_select);
    EXPECT_TRUE(found_insert);
}

// Tests configuration change logging
TEST_F(AuditLoggerTest, LogConfigurationChange) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    EXPECT_TRUE(logger_->log_configuration_change("database", "connection_timeout", "30", "60", "admin"));
    EXPECT_TRUE(logger_->log_configuration_change("logging", "level", "INFO", "DEBUG", "admin"));
    
    // Wait for background thread to flush
    std::this_thread::sleep_for(2s);
    
    auto lines = read_log_file(config_.log_file_path);
    EXPECT_EQ(lines.size(), 2);
    
    // Check that configuration change events have correct type
    bool found_database = false, found_logging = false;
    for (const auto& line : lines) {
        if (line.find("ConfigurationChange") != std::string::npos) {
            if (line.find("database") != std::string::npos) {
                found_database = true;
            } else if (line.find("logging") != std::string::npos) {
                found_logging = true;
            }
        }
    }
    EXPECT_TRUE(found_database);
    EXPECT_TRUE(found_logging);
}

// Tests job execution logging
TEST_F(AuditLoggerTest, LogJobExecution) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    EXPECT_TRUE(logger_->log_job_execution("job_123", "start", "success", "test_user"));
    EXPECT_TRUE(logger_->log_job_execution("job_456", "stop", "failed", "test_user"));
    
    // Wait for background thread to flush
    std::this_thread::sleep_for(2s);
    
    auto lines = read_log_file(config_.log_file_path);
    EXPECT_EQ(lines.size(), 2);
    
    // Check that job execution events have correct type
    bool found_success = false, found_failed = false;
    for (const auto& line : lines) {
        if (line.find("JobExecution") != std::string::npos) {
            if (line.find("success") != std::string::npos) {
                found_success = true;
            } else if (line.find("failed") != std::string::npos) {
                found_failed = true;
            }
        }
    }
    EXPECT_TRUE(found_success);
    EXPECT_TRUE(found_failed);
}

// Tests event retrieval
TEST_F(AuditLoggerTest, GetEvents) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    // Log some events
    auto now = std::chrono::system_clock::now();
    for (int i = 0; i < 5; ++i) {
        auto event = create_test_event();
        event.timestamp = now + std::chrono::seconds(i);
        logger_->log_event(event);
    }
    
    // Wait for background thread to flush
    std::this_thread::sleep_for(2s);
    
    // Retrieve events
    auto events = logger_->get_events(now, now + std::chrono::seconds(10), "", "");
    EXPECT_GT(events.size(), 0);
}

// Tests statistics
TEST_F(AuditLoggerTest, GetStatistics) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    // Log some events
    for (int i = 0; i < 3; ++i) {
        auto event = create_test_event();
        logger_->log_event(event);
    }
    
    // Wait for background thread to flush
    std::this_thread::sleep_for(2s);
    
    auto stats = logger_->get_statistics();
    EXPECT_FALSE(stats.empty());
    
    // Check that statistics contain expected keys
    EXPECT_TRUE(stats.find("total_events") != stats.end());
    EXPECT_TRUE(stats.find("buffer_size") != stats.end());
    EXPECT_TRUE(stats.find("max_file_size") != stats.end());
}

// Tests configuration update
TEST_F(AuditLoggerTest, UpdateConfiguration) {
    EXPECT_TRUE(logger_->initialize(config_));
    
    // Update configuration
    AuditConfig new_config = config_;
    new_config.buffer_size = 20;
    new_config.flush_interval = 2s;
    
    EXPECT_TRUE(logger_->update_config(new_config));
    
    auto retrieved_config = logger_->get_config();
    EXPECT_EQ(retrieved_config.buffer_size, 20);
    EXPECT_EQ(retrieved_config.flush_interval, 2s);
}

// Tests error handling with invalid file path
TEST_F(AuditLoggerTest, InvalidFilePath) {
    config_.log_file_path = "/invalid/path/that/does/not/exist/audit.log";
    EXPECT_FALSE(logger_->initialize(config_));
}

// Tests error handling with invalid configuration
TEST_F(AuditLoggerTest, InvalidConfiguration) {
    config_.buffer_size = 0;
    EXPECT_FALSE(logger_->initialize(config_));
}

} // namespace omop::security::test 