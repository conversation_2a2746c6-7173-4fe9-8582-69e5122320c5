#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "security/authorization.h"
#include <chrono>

namespace omop::security::test {

using namespace std::chrono_literals;
using ::testing::_;
using ::testing::Return;
using ::testing::AtLeast;

class AuthorizationTest : public ::testing::Test {
protected:
    void SetUp() override {
        authz_manager_ = std::make_unique<AuthorizationManager>();
        
        // Default configuration
        config_.enabled = true;
        config_.load_default_policies = true;
    }
    
    void TearDown() override {
        // Cleanup
    }
    
    // Helper to create test user
    std::vector<std::string> create_test_user_roles(const std::string& role = "user") {
        return {role};
    }
    
    // Helper to create test context
    std::unordered_map<std::string, std::any> create_test_context() {
        std::unordered_map<std::string, std::any> context;
        context["time_of_day"] = "business_hours";
        context["ip_address"] = "*************";
        context["user_department"] = "IT";
        return context;
    }
    
    std::unique_ptr<AuthorizationManager> authz_manager_;
    AuthzConfig config_;
};

// Tests initialization with various configurations
TEST_F(AuthorizationTest, InitializationSuccess) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    auto retrieved_config = authz_manager_->get_config();
    EXPECT_EQ(retrieved_config.enabled, config_.enabled);
}

// Tests initialization with disabled authorization
TEST_F(AuthorizationTest, InitializationDisabled) {
    config_.enabled = false;
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Should not check permissions when disabled
    auto user_roles = create_test_user_roles();
    auto context = create_test_context();
    auto result = authz_manager_->check_permission("test_user", "data:person", "read", context);
    EXPECT_FALSE(result);
}

// Tests initialization with default policies
TEST_F(AuthorizationTest, InitializationWithDefaultPolicies) {
    config_.load_default_policies = true;
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Should have default policies loaded
    auto policies = authz_manager_->get_policies();
    EXPECT_GT(policies.size(), 0);
    
    // Should have default roles loaded
    auto roles = authz_manager_->get_roles();
    EXPECT_GT(roles.size(), 0);
}

// Tests admin role permissions
TEST_F(AuthorizationTest, AdminRolePermissions) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Assign admin role to user
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "admin"));
    
    auto user_roles = authz_manager_->get_user_roles("test_user");
    auto context = create_test_context();
    
    // Admin should have access to everything
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:person", "read", context));
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:person", "write", context));
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "system:config", "modify", context));
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "admin:users", "delete", context));
}

// Tests user role permissions
TEST_F(AuthorizationTest, UserRolePermissions) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Assign user role to user
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "user"));
    
    auto user_roles = authz_manager_->get_user_roles("test_user");
    auto context = create_test_context();
    
    // User should have read access to data
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:person", "read", context));
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "reports:summary", "view", context));
    
    // User should not have write access
    EXPECT_FALSE(authz_manager_->check_permission("test_user", "data:person", "write", context));
    EXPECT_FALSE(authz_manager_->check_permission("test_user", "system:config", "modify", context));
}

// Tests resource pattern matching
TEST_F(AuthorizationTest, ResourcePatternMatching) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Create a policy with wildcard resource
    Policy policy;
    policy.id = "test_policy";
    policy.name = "Test Policy";
    policy.description = "Test policy for pattern matching";
    policy.effect = PolicyEffect::Allow;
    policy.priority = 75;
    policy.roles = {"user"};
    policy.resources = {"data:*"};
    policy.actions = {"read"};
    policy.conditions = "true";
    
    EXPECT_TRUE(authz_manager_->add_policy(policy));
    
    // Assign user role
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "user"));
    
    auto context = create_test_context();
    
    // Should match various data resources
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:person", "read", context));
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:condition", "read", context));
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:medication", "read", context));
    
    // Should not match non-data resources
    EXPECT_FALSE(authz_manager_->check_permission("test_user", "system:config", "read", context));
    EXPECT_FALSE(authz_manager_->check_permission("test_user", "admin:users", "read", context));
}

// Tests action pattern matching
TEST_F(AuthorizationTest, ActionPatternMatching) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Create a policy with specific actions
    Policy policy;
    policy.id = "test_policy";
    policy.name = "Test Policy";
    policy.description = "Test policy for action matching";
    policy.effect = PolicyEffect::Allow;
    policy.priority = 75;
    policy.roles = {"user"};
    policy.resources = {"data:person"};
    policy.actions = {"read,view,export"};
    policy.conditions = "true";
    
    EXPECT_TRUE(authz_manager_->add_policy(policy));
    
    // Assign user role
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "user"));
    
    auto context = create_test_context();
    
    // Should match allowed actions
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:person", "read", context));
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:person", "view", context));
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:person", "export", context));
    
    // Should not match disallowed actions
    EXPECT_FALSE(authz_manager_->check_permission("test_user", "data:person", "write", context));
    EXPECT_FALSE(authz_manager_->check_permission("test_user", "data:person", "delete", context));
}

// Tests policy conditions
TEST_F(AuthorizationTest, PolicyConditions) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Create a policy with time-based condition
    Policy policy;
    policy.id = "test_policy";
    policy.name = "Test Policy";
    policy.description = "Test policy with conditions";
    policy.effect = PolicyEffect::Allow;
    policy.priority = 75;
    policy.roles = {"user"};
    policy.resources = {"data:person"};
    policy.actions = {"read"};
    policy.conditions = "time_of_day == \"business_hours\"";
    
    EXPECT_TRUE(authz_manager_->add_policy(policy));
    
    // Assign user role
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "user"));
    
    // Test with matching condition
    auto context = create_test_context();
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:person", "read", context));
    
    // Test with non-matching condition
    context["time_of_day"] = "after_hours";
    EXPECT_FALSE(authz_manager_->check_permission("test_user", "data:person", "read", context));
}

// Tests policy priority
TEST_F(AuthorizationTest, PolicyPriority) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Create a deny policy with high priority
    Policy deny_policy;
    deny_policy.id = "deny_policy";
    deny_policy.name = "Deny Policy";
    deny_policy.description = "High priority deny policy";
    deny_policy.effect = PolicyEffect::Deny;
    deny_policy.priority = 100;
    deny_policy.roles = {"user"};
    deny_policy.resources = {"data:person"};
    deny_policy.actions = {"write"};
    deny_policy.conditions = "true";
    
    // Create an allow policy with lower priority
    Policy allow_policy;
    allow_policy.id = "allow_policy";
    allow_policy.name = "Allow Policy";
    allow_policy.description = "Lower priority allow policy";
    allow_policy.effect = PolicyEffect::Allow;
    allow_policy.priority = 50;
    allow_policy.roles = {"user"};
    allow_policy.resources = {"data:*"};
    allow_policy.actions = {"*"};
    allow_policy.conditions = "true";
    
    EXPECT_TRUE(authz_manager_->add_policy(deny_policy));
    EXPECT_TRUE(authz_manager_->add_policy(allow_policy));
    
    // Assign user role
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "user"));
    
    auto context = create_test_context();
    
    // Deny policy should take precedence
    EXPECT_FALSE(authz_manager_->check_permission("test_user", "data:person", "write", context));
    
    // Allow policy should work for other resources
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:condition", "read", context));
}

// Tests role management
TEST_F(AuthorizationTest, RoleManagement) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Create a custom role
    Role custom_role;
    custom_role.id = "data_analyst";
    custom_role.name = "Data Analyst";
    custom_role.description = "Role for data analysis";
    custom_role.permissions = {"data:*:read", "reports:*:view"};
    
    EXPECT_TRUE(authz_manager_->add_role(custom_role));
    
    // Get role
    auto retrieved_role = authz_manager_->get_role("data_analyst");
    EXPECT_TRUE(retrieved_role.has_value());
    EXPECT_EQ(retrieved_role->name, "Data Analyst");
    
    // Update role
    custom_role.description = "Updated description";
    EXPECT_TRUE(authz_manager_->update_role("data_analyst", custom_role));
    
    retrieved_role = authz_manager_->get_role("data_analyst");
    EXPECT_TRUE(retrieved_role.has_value());
    EXPECT_EQ(retrieved_role->description, "Updated description");
    
    // Delete role
    EXPECT_TRUE(authz_manager_->delete_role("data_analyst"));
    
    retrieved_role = authz_manager_->get_role("data_analyst");
    EXPECT_FALSE(retrieved_role.has_value());
}

// Tests policy management
TEST_F(AuthorizationTest, PolicyManagement) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Create a custom policy
    Policy custom_policy;
    custom_policy.id = "custom_policy";
    custom_policy.name = "Custom Policy";
    custom_policy.description = "Test custom policy";
    custom_policy.effect = PolicyEffect::Allow;
    custom_policy.priority = 75;
    custom_policy.roles = {"user"};
    custom_policy.resources = {"custom:resource"};
    custom_policy.actions = {"read"};
    custom_policy.conditions = "true";
    
    EXPECT_TRUE(authz_manager_->add_policy(custom_policy));
    
    // Get policy
    auto retrieved_policy = authz_manager_->get_policy("custom_policy");
    EXPECT_TRUE(retrieved_policy.has_value());
    EXPECT_EQ(retrieved_policy->name, "Custom Policy");
    
    // Update policy
    custom_policy.description = "Updated description";
    EXPECT_TRUE(authz_manager_->update_policy("custom_policy", custom_policy));
    
    retrieved_policy = authz_manager_->get_policy("custom_policy");
    EXPECT_TRUE(retrieved_policy.has_value());
    EXPECT_EQ(retrieved_policy->description, "Updated description");
    
    // Delete policy
    EXPECT_TRUE(authz_manager_->delete_policy("custom_policy"));
    
    retrieved_policy = authz_manager_->get_policy("custom_policy");
    EXPECT_FALSE(retrieved_policy.has_value());
}

// Tests user role assignment
TEST_F(AuthorizationTest, UserRoleAssignment) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Assign role to user
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "user"));
    
    // Check user roles
    auto user_roles = authz_manager_->get_user_roles("test_user");
    EXPECT_EQ(user_roles.size(), 1);
    EXPECT_EQ(user_roles[0], "user");
    
    // Assign another role
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "admin"));
    
    user_roles = authz_manager_->get_user_roles("test_user");
    EXPECT_EQ(user_roles.size(), 2);
    EXPECT_TRUE(std::find(user_roles.begin(), user_roles.end(), "user") != user_roles.end());
    EXPECT_TRUE(std::find(user_roles.begin(), user_roles.end(), "admin") != user_roles.end());
    
    // Remove role
    EXPECT_TRUE(authz_manager_->remove_role_from_user("test_user", "user"));
    
    user_roles = authz_manager_->get_user_roles("test_user");
    EXPECT_EQ(user_roles.size(), 1);
    EXPECT_EQ(user_roles[0], "admin");
}

// Tests multiple role permissions
TEST_F(AuthorizationTest, MultipleRolePermissions) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Create a custom role
    Role custom_role;
    custom_role.id = "reporter";
    custom_role.name = "Reporter";
    custom_role.description = "Role for reporting";
    custom_role.permissions = {"reports:*:view", "reports:*:export"};
    
    EXPECT_TRUE(authz_manager_->add_role(custom_role));
    
    // Assign multiple roles to user
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "user"));
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "reporter"));
    
    auto context = create_test_context();
    
    // Should have permissions from both roles
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:person", "read", context)); // from user role
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "reports:summary", "view", context)); // from user role
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "reports:detailed", "export", context)); // from reporter role
}

// Tests complex conditions
TEST_F(AuthorizationTest, ComplexConditions) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Create a policy with complex condition
    Policy policy;
    policy.id = "complex_policy";
    policy.name = "Complex Policy";
    policy.description = "Policy with complex conditions";
    policy.effect = PolicyEffect::Allow;
    policy.priority = 75;
    policy.roles = {"user"};
    policy.resources = {"data:person"};
    policy.actions = {"read"};
    policy.conditions = "time_of_day == \"business_hours\" && user_department == \"IT\"";
    
    EXPECT_TRUE(authz_manager_->add_policy(policy));
    
    // Assign user role
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "user"));
    
    // Test with matching conditions
    auto context = create_test_context();
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:person", "read", context));
    
    // Test with non-matching conditions
    context["time_of_day"] = "after_hours";
    EXPECT_FALSE(authz_manager_->check_permission("test_user", "data:person", "read", context));
    
    context["time_of_day"] = "business_hours";
    context["user_department"] = "HR";
    EXPECT_FALSE(authz_manager_->check_permission("test_user", "data:person", "read", context));
}

// Tests wildcard permissions
TEST_F(AuthorizationTest, WildcardPermissions) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Create a policy with wildcard permissions
    Policy policy;
    policy.id = "wildcard_policy";
    policy.name = "Wildcard Policy";
    policy.description = "Policy with wildcard permissions";
    policy.effect = PolicyEffect::Allow;
    policy.priority = 75;
    policy.roles = {"user"};
    policy.resources = {"*"};
    policy.actions = {"*"};
    policy.conditions = "true";
    
    EXPECT_TRUE(authz_manager_->add_policy(policy));
    
    // Assign user role
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "user"));
    
    auto context = create_test_context();
    
    // Should have access to everything
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "data:person", "read", context));
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "system:config", "modify", context));
    EXPECT_TRUE(authz_manager_->check_permission("test_user", "admin:users", "delete", context));
}

// Tests statistics
TEST_F(AuthorizationTest, GetStatistics) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Create some policies and roles
    Role custom_role;
    custom_role.id = "custom_role";
    custom_role.name = "Custom Role";
    custom_role.permissions = {"data:*:read"};
    
    Policy custom_policy;
    custom_policy.id = "custom_policy";
    custom_policy.name = "Custom Policy";
    custom_policy.effect = PolicyEffect::Allow;
    custom_policy.priority = 75;
    custom_policy.roles = {"custom_role"};
    custom_policy.resources = {"data:*"};
    custom_policy.actions = {"read"};
    custom_policy.conditions = "true";
    
    EXPECT_TRUE(authz_manager_->add_role(custom_role));
    EXPECT_TRUE(authz_manager_->add_policy(custom_policy));
    EXPECT_TRUE(authz_manager_->assign_role_to_user("test_user", "custom_role"));
    
    auto stats = authz_manager_->get_statistics();
    EXPECT_FALSE(stats.empty());
    
    // Check that statistics contain expected keys
    EXPECT_TRUE(stats.find("total_policies") != stats.end());
    EXPECT_TRUE(stats.find("total_roles") != stats.end());
    EXPECT_TRUE(stats.find("total_users_with_roles") != stats.end());
    EXPECT_TRUE(stats.find("total_role_assignments") != stats.end());
}

// Tests error handling with invalid operations
TEST_F(AuthorizationTest, InvalidOperations) {
    EXPECT_TRUE(authz_manager_->initialize(config_));
    
    // Try to get non-existent role
    auto role = authz_manager_->get_role("nonexistent");
    EXPECT_FALSE(role.has_value());
    
    // Try to get non-existent policy
    auto policy = authz_manager_->get_policy("nonexistent");
    EXPECT_FALSE(policy.has_value());
    
    // Try to update non-existent role
    Role custom_role;
    custom_role.id = "nonexistent";
    custom_role.name = "Non-existent Role";
    EXPECT_FALSE(authz_manager_->update_role("nonexistent", custom_role));
    
    // Try to update non-existent policy
    Policy custom_policy;
    custom_policy.id = "nonexistent";
    custom_policy.name = "Non-existent Policy";
    EXPECT_FALSE(authz_manager_->update_policy("nonexistent", custom_policy));
    
    // Try to delete non-existent role
    EXPECT_FALSE(authz_manager_->delete_role("nonexistent"));
    
    // Try to delete non-existent policy
    EXPECT_FALSE(authz_manager_->delete_policy("nonexistent"));
}

} // namespace omop::security::test 