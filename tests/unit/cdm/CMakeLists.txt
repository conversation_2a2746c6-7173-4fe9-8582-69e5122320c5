# Unit tests for OMOP CDM library

# Test source files in the cdm directory
set(CDM_TEST_SOURCES
    table_definition_validation_unit_test.cpp
    omop_table_structures_unit_test.cpp
)

# Create individual test executables for each test file
foreach(test_source ${CDM_TEST_SOURCES})
    # Extract test name from filename (remove .cpp extension)
    get_filename_component(test_name ${test_source} NAME_WE)

    # Create the test executable using the parent function
    create_unit_test_executable(${test_name} ${test_source})

    # Link CDM library and dependencies
    target_link_libraries(${test_name} PRIVATE 
        omop_cdm
        omop_common
        omop_core
        spdlog::spdlog
        nlohmann_json::n<PERSON><PERSON>_json
    )

    # Add component-specific include directories
    target_include_directories(${test_name} PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/src/lib/cdm
        ${CMAKE_SOURCE_DIR}/src/lib/common
        ${CMAKE_CURRENT_SOURCE_DIR}
    )

    # Set test-specific properties with RPATH
    set_target_properties(${test_name} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/unit/cdm
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
        BUILD_WITH_INSTALL_RPATH TRUE
        INSTALL_RPATH "${CMAKE_BINARY_DIR}/lib"
        INSTALL_RPATH_USE_LINK_PATH TRUE
        SKIP_BUILD_RPATH FALSE
        BUILD_WITH_INSTALL_RPATH TRUE
    )

    # Add coverage flags if enabled
    if(ENABLE_COVERAGE)
        target_compile_options(${test_name} PRIVATE --coverage)
        target_link_options(${test_name} PRIVATE --coverage)
    endif()
endforeach()

# Create a combined test executable for all CDM tests
create_unit_test_executable(test_cdm_all "${CDM_TEST_SOURCES}")

# Link CDM library and dependencies for combined test
target_link_libraries(test_cdm_all PRIVATE 
    omop_cdm
    omop_common
    omop_core
    spdlog::spdlog
    nlohmann_json::nlohmann_json
)

# Add component-specific include directories for combined test
target_include_directories(test_cdm_all PRIVATE
    ${CMAKE_SOURCE_DIR}/src/lib
    ${CMAKE_SOURCE_DIR}/src/lib/cdm
    ${CMAKE_SOURCE_DIR}/src/lib/common
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Set properties for combined test with RPATH
set_target_properties(test_cdm_all PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/unit/cdm
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    BUILD_WITH_INSTALL_RPATH TRUE
    INSTALL_RPATH "${CMAKE_BINARY_DIR}/lib"
    INSTALL_RPATH_USE_LINK_PATH TRUE
    SKIP_BUILD_RPATH FALSE
    BUILD_WITH_INSTALL_RPATH TRUE
)

# Add coverage flags if enabled for combined test
if(ENABLE_COVERAGE)
    target_compile_options(test_cdm_all PRIVATE --coverage)
    target_link_options(test_cdm_all PRIVATE --coverage)
endif()

# Copy test data files if any exist
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test_data)
    file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/test_data
        DESTINATION ${CMAKE_CURRENT_BINARY_DIR}
    )
endif()

# Test configuration summary
message(STATUS "CDM Unit Tests Configuration:")
message(STATUS "  Test files: ${CDM_TEST_SOURCES}")
message(STATUS "  Output directory: ${CMAKE_BINARY_DIR}/tests/unit/cdm")
message(STATUS "  C++ Standard: 20")

# Use the new consolidated approach
add_component_unit_tests(cdm ${CDM_TEST_SOURCES})

# Fix RPATH for the component test executable created by add_component_unit_tests
set_target_properties(test_omop_cdm_unit PROPERTIES
    BUILD_WITH_INSTALL_RPATH TRUE
    INSTALL_RPATH "${CMAKE_BINARY_DIR}/lib"
    INSTALL_RPATH_USE_LINK_PATH TRUE
    SKIP_BUILD_RPATH FALSE
    BUILD_WITH_INSTALL_RPATH TRUE
)
