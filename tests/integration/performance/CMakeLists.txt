# tests/performance/CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

# Performance test configuration
set(PERFORMANCE_TEST_SOURCES
    system_load_performance_test.cpp
    memory_usage_optimization_test.cpp
    concurrent_operation_performance_test.cpp
    system_scalability_test.cpp
)

# Create performance test executable
add_executable(omop_performance_tests ${PERFORMANCE_TEST_SOURCES})

target_include_directories(omop_performance_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/tests/integration/test_helpers
        ${CMAKE_CURRENT_BINARY_DIR}
        ${CMAKE_SOURCE_DIR}/src/lib
)

target_link_libraries(omop_performance_tests
    PRIVATE
        integration_test_helpers
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
#        omop_service
        gtest
        gtest_main
        gmock
        Threads::Threads
)

# Add performance test
add_test(NAME performance_tests COMMAND omop_performance_tests)