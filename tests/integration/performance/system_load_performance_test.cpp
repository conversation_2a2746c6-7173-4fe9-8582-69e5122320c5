// tests/performance/test_load_performance.cpp
// Load testing scenarios to measure throughput and latency under various workloads

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>
#include <random>
#include "core/pipeline.h"
#include "extract/csv_extractor.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"

namespace omop::performance {

using namespace std::chrono_literals;

class LoadPerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_data_generator_ = std::make_unique<test::TestDataGenerator>();
        metrics_.clear();
    }

    void TearDown() override {
        // Cleanup temporary files
        for (const auto& file : temp_files_) {
            std::filesystem::remove(file);
        }
    }

    void GenerateLargeCSV(const std::string& filename, size_t record_count) {
        auto records = test_data_generator_->generatePatientRecords(record_count);
        test_data_generator_->writeToCSV(filename, records);
        temp_files_.push_back(filename);
    }

    struct PerformanceMetrics {
        size_t total_records;
        std::chrono::duration<double> total_time;
        std::chrono::duration<double> extract_time;
        std::chrono::duration<double> transform_time;
        std::chrono::duration<double> load_time;
        double records_per_second;
        size_t peak_memory_mb;
        size_t errors;
    };

    PerformanceMetrics RunPipelinePerformanceTest(
        size_t record_count,
        size_t batch_size,
        size_t parallel_workers) {

        PerformanceMetrics metrics;
        metrics.total_records = record_count;

        // Generate test data
        std::string test_file = "perf_test_" + std::to_string(record_count) + ".csv";
        GenerateLargeCSV(test_file, record_count);

        // Configure pipeline
        core::PipelineConfig config;
        config.batch_size = batch_size;
        config.max_parallel_batches = parallel_workers;
        config.validate_records = false; // Disable for performance testing

        auto pipeline = core::PipelineBuilder()
            .with_config(config)
            .with_extractor(std::make_unique<extract::CsvExtractor>())
            .with_transformer(std::make_unique<transform::TransformationEngine>())
            .with_loader(std::make_unique<test::MockDatabaseLoader>())
            .build();

        // Add performance monitoring
        auto start_time = std::chrono::high_resolution_clock::now();
        size_t peak_memory = 0;

        std::atomic<bool> monitoring{true};
        std::thread memory_monitor([&]() {
            while (monitoring) {
                size_t current_memory = getCurrentMemoryUsageMB();
                peak_memory = std::max(peak_memory, current_memory);
                std::this_thread::sleep_for(100ms);
            }
        });

        // Run pipeline
        auto future = pipeline->start("perf_test_job_" + std::to_string(record_count));
        auto job_info = future.get();

        monitoring = false;
        memory_monitor.join();

        auto end_time = std::chrono::high_resolution_clock::now();

        // Calculate metrics
        metrics.total_time = end_time - start_time;
        metrics.records_per_second = record_count / metrics.total_time.count();
        metrics.peak_memory_mb = peak_memory;
        metrics.errors = job_info.error_records;

        return metrics;
    }

    size_t getCurrentMemoryUsageMB() {
        // Platform-specific memory usage retrieval
        #ifdef __linux__
            std::ifstream status("/proc/self/status");
            std::string line;
            while (std::getline(status, line)) {
                if (line.find("VmRSS:") == 0) {
                    std::istringstream iss(line);
                    std::string label;
                    size_t memory_kb;
                    iss >> label >> memory_kb;
                    return memory_kb / 1024;
                }
            }
        #endif
        return 0;
    }

protected:
    std::unique_ptr<test::TestDataGenerator> test_data_generator_;
    std::vector<std::string> temp_files_;
    std::vector<PerformanceMetrics> metrics_;
};

TEST_F(LoadPerformanceTest, MeasureThroughputSmallBatches) {
    // Test with small batch sizes
    const size_t record_count = 100000;
    const std::vector<size_t> batch_sizes = {100, 500, 1000};

    for (size_t batch_size : batch_sizes) {
        auto metrics = RunPipelinePerformanceTest(record_count, batch_size, 1);

        // Log performance metrics
        std::cout << "Batch size: " << batch_size << "\n"
                  << "Records/second: " << metrics.records_per_second << "\n"
                  << "Total time: " << metrics.total_time.count() << "s\n"
                  << "Peak memory: " << metrics.peak_memory_mb << " MB\n\n";

        // Performance assertions
        EXPECT_GT(metrics.records_per_second, 1000)
            << "Throughput too low for batch size " << batch_size;
        EXPECT_LT(metrics.peak_memory_mb, 500)
            << "Memory usage too high for batch size " << batch_size;
        EXPECT_EQ(metrics.errors, 0)
            << "Errors occurred during processing";
    }
}

TEST_F(LoadPerformanceTest, MeasureThroughputLargeBatches) {
    // Test with large batch sizes
    const size_t record_count = 500000;
    const std::vector<size_t> batch_sizes = {5000, 10000, 50000};

    for (size_t batch_size : batch_sizes) {
        auto metrics = RunPipelinePerformanceTest(record_count, batch_size, 1);

        // Performance assertions for large batches
        EXPECT_GT(metrics.records_per_second, 5000)
            << "Throughput too low for large batch size " << batch_size;
        EXPECT_LT(metrics.peak_memory_mb, 2000)
            << "Memory usage excessive for batch size " << batch_size;
    }
}

TEST_F(LoadPerformanceTest, MeasureParallelProcessingScalability) {
    // Test scalability with different numbers of workers
    const size_t record_count = 200000;
    const size_t batch_size = 5000;
    const std::vector<size_t> worker_counts = {1, 2, 4, 8};

    std::vector<PerformanceMetrics> parallel_metrics;

    for (size_t workers : worker_counts) {
        auto metrics = RunPipelinePerformanceTest(record_count, batch_size, workers);
        parallel_metrics.push_back(metrics);

        std::cout << "Workers: " << workers << "\n"
                  << "Records/second: " << metrics.records_per_second << "\n"
                  << "Speedup: " << metrics.records_per_second / parallel_metrics[0].records_per_second << "x\n\n";
    }

    // Verify scalability
    for (size_t i = 1; i < parallel_metrics.size(); ++i) {
        double speedup = parallel_metrics[i].records_per_second / parallel_metrics[0].records_per_second;
        double efficiency = speedup / worker_counts[i];

        // Expect at least 60% efficiency
        EXPECT_GT(efficiency, 0.6)
            << "Poor parallel efficiency with " << worker_counts[i] << " workers";
    }
}

TEST_F(LoadPerformanceTest, StressTestHighVolume) {
    // Stress test with very high volume
    const size_t record_count = 1000000;
    const size_t batch_size = 10000;
    const size_t workers = 4;

    auto start_time = std::chrono::high_resolution_clock::now();
    auto metrics = RunPipelinePerformanceTest(record_count, batch_size, workers);

    // Verify completion within reasonable time
    EXPECT_LT(metrics.total_time.count(), 300) // 5 minutes max
        << "Pipeline took too long for 1M records";

    // Verify no errors under stress
    EXPECT_EQ(metrics.errors, 0)
        << "Errors occurred during high-volume processing";

    // Verify memory usage stays reasonable
    EXPECT_LT(metrics.peak_memory_mb, 4000)
        << "Excessive memory usage during stress test";
}

TEST_F(LoadPerformanceTest, MeasureLatencyPercentiles) {
    // Measure latency percentiles for individual record processing
    const size_t sample_size = 10000;
    std::vector<double> latencies;
    latencies.reserve(sample_size);

    // Create simple pipeline for latency testing
    core::PipelineConfig config;
    config.batch_size = 1; // Process one record at a time
    auto pipeline = std::make_unique<core::ETLPipeline>(config);

    // Generate test records
    auto records = test_data_generator_->generatePatientRecords(sample_size);

    // Measure individual record processing times
    for (const auto& record : records) {
        auto start = std::chrono::high_resolution_clock::now();

        // Process single record through pipeline
        core::RecordBatch batch;
        batch.addRecord(record);

        // Transform
        transform::TransformationEngine transformer;
        core::ProcessingContext context;
        auto transformed = transformer.transform_batch(batch, context);

        auto end = std::chrono::high_resolution_clock::now();
        auto latency = std::chrono::duration<double, std::micro>(end - start).count();
        latencies.push_back(latency);
    }

    // Calculate percentiles
    std::sort(latencies.begin(), latencies.end());

    auto p50 = latencies[latencies.size() * 0.50];
    auto p90 = latencies[latencies.size() * 0.90];
    auto p95 = latencies[latencies.size() * 0.95];
    auto p99 = latencies[latencies.size() * 0.99];

    std::cout << "Latency Percentiles (microseconds):\n"
              << "P50: " << p50 << "\n"
              << "P90: " << p90 << "\n"
              << "P95: " << p95 << "\n"
              << "P99: " << p99 << "\n";

    // Performance assertions
    EXPECT_LT(p50, 100) << "Median latency too high";
    EXPECT_LT(p95, 500) << "95th percentile latency too high";
    EXPECT_LT(p99, 1000) << "99th percentile latency too high";
}

} // namespace omop::performance