// Integration tests for transformation registry improvements
#include <gtest/gtest.h>
#include "transform/transformation_registry_improvements.h"
#include "transform/transformation_engine.h"
#include "test_helpers/transform_test_fixture.h"

namespace omop::test {

class TransformationRegistryIntegrationTest : public TransformTestFixture {
protected:
    void SetUp() override {
        try {
            TransformTestFixture::SetUp();
            registry_ = &transform::EnhancedTransformationRegistry::enhanced_instance();
            
            // Clear any existing registrations
            registry_->clear_all();
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("transformation-registry-test");
            logger->warn("TransformationRegistryIntegrationTest SetUp failed: {}", e.what());
        }
    }
    
    void TearDown() override {
        // Clean up registrations
        registry_->clear_all();
        TransformTestFixture::TearDown();
    }
    
    transform::EnhancedTransformationRegistry* registry_;
};

// Tests basic registration and unregistration functionality
TEST_F(TransformationRegistryIntegrationTest, BasicRegistrationUnregistration) {
    if (!registry_) {
        GTEST_SKIP() << "Transformation registry not available";
        return;
    }
    
    // Register a custom transformation
    bool registered = false;
    registry_->register_transformation("test_transform",
        [&registered]() -> std::unique_ptr<transform::FieldTransformation> {
            registered = true;
            return std::make_unique<transform::DirectTransformation>();
        });
    
    // Verify registration
    EXPECT_TRUE(registry_->has_transformation("test_transform"));
    
    // Create transformation
    auto transform = registry_->create_transformation("test_transform");
    EXPECT_TRUE(registered) << "Factory function should have been called";
    EXPECT_NE(transform, nullptr);
    
    // Unregister
    bool unregistered = registry_->unregister_transformation("test_transform");
    EXPECT_TRUE(unregistered);
    EXPECT_FALSE(registry_->has_transformation("test_transform"));
    
    // Try to create after unregistration
    EXPECT_THROW(registry_->create_transformation("test_transform"), std::exception);
}

// Tests scoped registration with automatic cleanup
TEST_F(TransformationRegistryIntegrationTest, ScopedRegistration) {
    EXPECT_FALSE(registry_->has_transformation("scoped_transform"));
    
    {
        // Create scoped registration
        transform::EnhancedTransformationRegistry::ScopedRegistration scoped(
            *registry_,
            "scoped_transform",
            []() { return std::make_unique<transform::DirectTransformation>(); }
        );
        
        // Should be registered within scope
        EXPECT_TRUE(registry_->has_transformation("scoped_transform"));
        
        auto transform = registry_->create_transformation("scoped_transform");
        EXPECT_NE(transform, nullptr);
    }
    
    // Should be automatically unregistered after scope
    EXPECT_FALSE(registry_->has_transformation("scoped_transform"));
}

// Tests multiple scoped registrations
TEST_F(TransformationRegistryIntegrationTest, MultipleScopedRegistrations) {
    std::vector<std::unique_ptr<transform::EnhancedTransformationRegistry::ScopedRegistration>> registrations;
    
    // Register multiple transformations
    for (int i = 0; i < 5; ++i) {
        std::string name = "scoped_" + std::to_string(i);
        
        registrations.push_back(
            std::make_unique<transform::EnhancedTransformationRegistry::ScopedRegistration>(
                *registry_,
                name,
                [i]() { 
                    class IndexedTransformation : public transform::FieldTransformation {
                    public:
                        explicit IndexedTransformation(int index) : index_(index) {}
                        
                        std::any transform(const std::any& input, core::ProcessingContext& context) override {
                            return index_;
                        }
                        
                        bool validate_input(const std::any& input) const override { return true; }
                        std::string get_type() const override { return "indexed_" + std::to_string(index_); }
                        void configure(const YAML::Node& params) override {}
                        
                    private:
                        int index_;
                    };
                    
                    return std::make_unique<IndexedTransformation>(i);
                }
            )
        );
    }
    
    // Verify all are registered
    for (int i = 0; i < 5; ++i) {
        std::string name = "scoped_" + std::to_string(i);
        EXPECT_TRUE(registry_->has_transformation(name));
    }
    
    // Clear some registrations
    registrations.erase(registrations.begin() + 2, registrations.begin() + 4);
    
    // Check which are still registered
    EXPECT_TRUE(registry_->has_transformation("scoped_0"));
    EXPECT_TRUE(registry_->has_transformation("scoped_1"));
    EXPECT_FALSE(registry_->has_transformation("scoped_2"));
    EXPECT_FALSE(registry_->has_transformation("scoped_3"));
    EXPECT_TRUE(registry_->has_transformation("scoped_4"));
    
    // Clear all
    registrations.clear();
    
    // None should be registered
    for (int i = 0; i < 5; ++i) {
        std::string name = "scoped_" + std::to_string(i);
        EXPECT_FALSE(registry_->has_transformation(name));
    }
}

// Tests clear_all functionality
TEST_F(TransformationRegistryIntegrationTest, ClearAllTransformations) {
    // Register multiple transformations
    std::vector<std::string> names = {"transform1", "transform2", "transform3"};
    
    for (const auto& name : names) {
        registry_->register_transformation(name,
            []() { return std::make_unique<transform::DirectTransformation>(); });
    }
    
    // Verify all registered
    for (const auto& name : names) {
        EXPECT_TRUE(registry_->has_transformation(name));
    }
    
    // Clear all
    registry_->clear_all();
    
    // Verify all cleared
    for (const auto& name : names) {
        EXPECT_FALSE(registry_->has_transformation(name));
    }
    
    auto types = registry_->get_registered_types();
    EXPECT_TRUE(types.empty());
}

// Tests thread-safe registration and unregistration
TEST_F(TransformationRegistryIntegrationTest, ThreadSafeOperations) {
    const int num_threads = 10;
    const int operations_per_thread = 100;
    
    std::vector<std::thread> threads;
    std::atomic<int> successful_registrations(0);
    std::atomic<int> successful_unregistrations(0);
    std::atomic<int> successful_creations(0);
    
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, &successful_registrations, &successful_unregistrations, 
                             &successful_creations, t]() {
            for (int i = 0; i < operations_per_thread; ++i) {
                std::string name = "thread_" + std::to_string(t) + "_" + std::to_string(i % 10);
                
                // Register
                try {
                    registry_->register_transformation(name,
                        []() { return std::make_unique<transform::DirectTransformation>(); });
                    successful_registrations++;
                } catch (...) {
                    // Already registered
                }
                
                // Create
                try {
                    auto transform = registry_->create_transformation(name);
                    if (transform) {
                        successful_creations++;
                    }
                } catch (...) {
                    // Not registered
                }
                
                // Unregister (50% chance)
                if (i % 2 == 0) {
                    if (registry_->unregister_transformation(name)) {
                        successful_unregistrations++;
                    }
                }
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    logger_->info("Thread-safe operations results:");
    logger_->info("  - Successful registrations: {}", successful_registrations.load());
    logger_->info("  - Successful unregistrations: {}", successful_unregistrations.load());
    logger_->info("  - Successful creations: {}", successful_creations.load());
    
    // Verify some operations succeeded
    EXPECT_GT(successful_registrations.load(), 0);
    EXPECT_GT(successful_creations.load(), 0);
}

// Tests registry with transformation engine integration
TEST_F(TransformationRegistryIntegrationTest, EngineIntegration) {
    // Register UK-specific transformations
    registry_->register_transformation("uk_currency",
        []() -> std::unique_ptr<transform::FieldTransformation> {
            class UKCurrencyTransformation : public transform::FieldTransformation {
            public:
                std::any transform(const std::any& input, core::ProcessingContext& context) override {
                    if (input.type() == typeid(double)) {
                        double value = std::any_cast<double>(input);
                        return std::string("£") + std::to_string(value);
                    }
                    return input;
                }
                
                bool validate_input(const std::any& input) const override {
                    return input.type() == typeid(double);
                }
                
                std::string get_type() const override { return "uk_currency"; }
                void configure(const YAML::Node& params) override {}
            };
            
            return std::make_unique<UKCurrencyTransformation>();
        });
    
    // Verify registration
    EXPECT_TRUE(registry_->has_transformation("uk_currency"));
    
    // Use in transformation
    auto transform = registry_->create_transformation("uk_currency");
    core::ProcessingContext context;
    auto result = transform->transform(123.45, context);
    
    EXPECT_TRUE(result.type() == typeid(std::string));
    std::string formatted = std::any_cast<std::string>(result);
    EXPECT_TRUE(formatted.find("£") != std::string::npos);
}

} // namespace omop::test 