/**
 * Integration test for custom transformation implementations and registration
 */

#include <gtest/gtest.h>
#include "transform/transformation_engine.h"
#include "transform/transformations.h"
#include "transform/field_transformations.h"
#include "test_helpers/integration_test_base.h"

namespace omop::test::integration {

// Custom transformation for testing
class CustomAgeGroupTransformation : public transform::ComplexTransformation {
public:
    transform::TransformationResult transform_detailed(
        const std::any& input,
        core::ProcessingContext& context) override {

        transform::TransformationResult result;

        try {
            int age = std::any_cast<int>(input);

            std::string age_group;
            if (age < 18) {
                age_group = "Pediatric";
            } else if (age < 65) {
                age_group = "Adult";
            } else {
                age_group = "Geriatric";
            }

            result.value = age_group;

            if (age > 120) {
                result.add_warning("Unusually high age value: " + std::to_string(age));
            }

        } catch (const std::bad_any_cast& e) {
            result.set_error("Invalid input type for age group transformation");
        }

        return result;
    }

    bool validate_input(const std::any& input) const override {
        return input.type() == typeid(int) || input.type() == typeid(double);
    }

    std::string get_type() const override { return "age_group"; }

    void configure(const YAML::Node& params) override {
        // Custom configuration if needed
    }
};

class CustomTransformationIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();

        // Register custom transformation
        transform::TransformationRegistry::instance().register_transformation(
            "age_group",
            []() { return std::make_unique<CustomAgeGroupTransformation>(); }
        );

        // Initialize transformation engine
        engine_ = std::make_unique<transform::TransformationEngine>();
    }

    std::unique_ptr<transform::TransformationEngine> engine_;
};

TEST_F(CustomTransformationIntegrationTest, TestCustomTransformationRegistration) {
    // Verify custom transformation is registered
    auto& registry = transform::TransformationRegistry::instance();
    EXPECT_TRUE(registry.has_transformation("age_group"));

    auto registered_types = registry.get_registered_types();
    auto it = std::find(registered_types.begin(), registered_types.end(), "age_group");
    EXPECT_NE(it, registered_types.end());
}

TEST_F(CustomTransformationIntegrationTest, TestCustomTransformationExecution) {
    // Create configuration with custom transformation using YAML
    YAML::Node mapping_node;
    mapping_node["source_table"] = "patients";
    mapping_node["target_table"] = "person_custom";
    YAML::Node trans_node;
    trans_node["source_column"] = "age";
    trans_node["target_column"] = "age_group";
    trans_node["type"] = "custom";
    trans_node["type"] = "age_group"; // custom type for the test
    mapping_node["transformations"].push_back(trans_node);

    common::TableMapping mapping(mapping_node);
    std::unordered_map<std::string, std::any> config;
    config["table_mapping"] = mapping;

    core::ProcessingContext context;
    engine_->initialize(config, context);

    // Test transformation
    core::Record input_record;
    input_record.setField("age", 25);
    input_record.setField("patient_id", 1001);

    auto result = engine_->transform(input_record, context);
    ASSERT_TRUE(result.has_value());

    auto age_group = result->getFieldAs<std::string>("age_group");
    EXPECT_EQ(age_group, "Adult");
}

TEST_F(CustomTransformationIntegrationTest, TestComplexTransformationChain) {
    // Test chaining multiple transformations including custom ones
    transform::TransformationChain chain;

    // Add numeric transformation first
    auto numeric_transform = std::make_unique<transform::NumericTransformation>();
    YAML::Node numeric_params;
    numeric_params["operation"] = "multiply";
    numeric_params["operand"] = 365.25;
    numeric_transform->configure(numeric_params);
    chain.add_transformation(std::move(numeric_transform));

    // Add custom age group transformation
    chain.add_transformation(std::make_unique<CustomAgeGroupTransformation>());

    core::ProcessingContext context;

    // Input: age in years
    std::any input = 30;

    // First transformation: years to days
    // Second transformation: Should still work with numeric input
    auto result = chain.apply(input, context);

    // The age group transformation should handle the numeric input
    EXPECT_EQ(std::any_cast<std::string>(result), "Adult");
}

TEST_F(CustomTransformationIntegrationTest, TestTransformationWithCache) {
    // Test transformation caching for performance
    transform::TransformationCache cache(100);

    auto transformation = std::make_unique<CustomAgeGroupTransformation>();

    core::ProcessingContext context;

    // First execution - cache miss
    auto key = "age_25";
    auto cached = cache.get(key);
    EXPECT_FALSE(cached.has_value());

    auto result = transformation->transform_detailed(25, context);
    cache.put(key, result.value);

    // Second execution - cache hit
    cached = cache.get(key);
    ASSERT_TRUE(cached.has_value());
    EXPECT_EQ(std::any_cast<std::string>(*cached), "Adult");

    // Verify cache statistics
    auto stats = cache.get_stats();
    EXPECT_EQ(stats.hits, 1);
    EXPECT_EQ(stats.misses, 1);
    EXPECT_EQ(stats.hit_rate, 0.5);
}

TEST_F(CustomTransformationIntegrationTest, TestBatchTransformationWithMetrics) {
    // Test batch transformation with performance metrics
    transform::BatchFieldTransformer batch_transformer;
    transform::TransformationMetrics metrics;

    // Add field mappings
    transform::FieldMapping age_mapping;
    age_mapping.source_field = "age";
    age_mapping.target_field = "age_group";
    age_mapping.transformation_type = "age_group";
    batch_transformer.add_mapping(age_mapping);

    core::ProcessingContext context;

    // Process batch of records
    std::vector<core::Record> test_records;
    for (int i = 0; i < 100; ++i) {
        core::Record record;
        record.setField("age", 20 + (i % 60)); // Ages 20-79
        record.setField("patient_id", 1000 + i);
        test_records.push_back(record);
    }

    // Transform and measure
    for (const auto& record : test_records) {
        auto start = std::chrono::steady_clock::now();

        try {
            auto result = batch_transformer.transform(record, context);
            auto duration = std::chrono::steady_clock::now() - start;

            metrics.record_execution("age", "age_group", duration, true);

            // Verify transformation
            EXPECT_TRUE(result.hasField("age_group"));
        } catch (const std::exception& e) {
            auto duration = std::chrono::steady_clock::now() - start;
            metrics.record_execution("age", "age_group", duration, false);
        }
    }

    // Check metrics
    auto field_stats = metrics.get_field_stats("age");
    EXPECT_EQ(field_stats.total_count, 100);
    EXPECT_EQ(field_stats.success_count, 100);
    EXPECT_GT(field_stats.average_duration(), 0.0);
}

} // namespace omop::test::integration