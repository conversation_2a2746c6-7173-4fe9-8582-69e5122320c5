/**
 * @file test_transformation_engine_integration.cpp
 * @brief Integration tests for TransformationEngine
 */

#include <gtest/gtest.h>
#include "test_helpers/transform_test_fixture.h"
#include "test_helpers/test_data_generator.h"
#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "common/configuration.h"
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#include "test_helpers/database_connection_factory.h"
#include <yaml-cpp/yaml.h>

namespace omop::test {

class TransformationEngineIntegrationTest : public TransformTestFixture {
protected:
    void SetUp() override {
        try {
            TransformTestFixture::SetUp();
            
            // Initialize vocabulary service if database is available
            if (db_available_) {
                initialize_vocabulary_service();
            }
            
            // Create transformation engine
            engine_ = std::make_unique<transform::TransformationEngine>();
        } catch (const std::exception& e) {
            // Log error but allow tests to continue with limited functionality
            auto logger = common::Logger::get("transformation-engine-test");
            logger->warn("TransformationEngineIntegrationTest SetUp failed: {}", e.what());
        }
    }
    
    void configure_person_transformation() {
        std::string config_yaml = R"(
            source_database:
              type: postgresql
              host: localhost
              port: 5432
              database: test_db
              username: test_user
              password: test_pass
            
            target_database:
              type: postgresql
              host: localhost
              port: 5433
              database: omop_cdm
              username: omop_user
              password: omop_pass
            
            vocabulary_mappings:
              CUSTOM_GENDER:
                source_vocabulary: gender
                target_vocabulary: Gender
                mappings:
                  - source_code: "Male"
                    target_concept_id: 8507
                  - source_code: "Female"
                    target_concept_id: 8532
              CUSTOM_RACE:
                source_vocabulary: race
                target_vocabulary: Race
                mappings:
                  - source_code: "White"
                    target_concept_id: 8527
                  - source_code: "Black"
                    target_concept_id: 8516
              CUSTOM_ETHNICITY:
                source_vocabulary: ethnicity
                target_vocabulary: Ethnicity
                mappings:
                  - source_code: "Not Hispanic"
                    target_concept_id: 38003564
                  - source_code: "Hispanic"
                    target_concept_id: 38003563
            
            tables:
              person:
                source_table: patient_data
                target_table: person
                transformations:
                  - source_column: patient_id
                    target_column: person_id
                    type: direct
                  - source_column: gender
                    target_column: gender_concept_id
                    type: vocabulary_mapping
                    parameters:
                      vocabulary: CUSTOM_GENDER
                      default_concept_id: 0
                  - source_column: birth_date
                    target_column: year_of_birth
                    type: date_transform
                    parameters:
                      format: "%Y-%m-%d"
                      output_format: "%Y"
                  - source_column: race
                    target_column: race_concept_id
                    type: vocabulary_mapping
                    parameters:
                      vocabulary: CUSTOM_RACE
                  - source_column: ethnicity
                    target_column: ethnicity_concept_id
                    type: vocabulary_mapping
                    parameters:
                      vocabulary: CUSTOM_ETHNICITY
                filters:
                  - field: age
                    operator: ">="
                    value: 18
                validations:
                  - field: person_id
                    type: required
                  - field: gender_concept_id
                    type: required
                  - field: year_of_birth
                    type: range
                    min: 1900
                    max: 2023
        )";
        
        config_manager_->load_config_from_string(config_yaml);
    }
    
    std::unique_ptr<extract::IDatabaseConnection> create_omop_db_connection() {
        // Use the centralized database connection factory
        try {
            return DatabaseConnectionFactory::createOmopConnection();
        } catch (const std::exception& e) {
            // Log warning but continue - some tests may not need database
            auto logger = common::Logger::get("transformation-engine-test");
            logger->warn("Exception connecting to OMOP database: {}", e.what());
            return nullptr;
        }
    }
    
    std::unique_ptr<transform::VocabularyService> create_transformation_engine_vocabulary_service() {
        // Create real vocabulary service with OMOP database connection
        auto omop_connection = create_omop_db_connection();
        if (!omop_connection) {
            return nullptr;
        }
        auto vocab_service = std::make_unique<transform::VocabularyService>(std::move(omop_connection));
        vocab_service->initialize(1000);
        return vocab_service;
    }
    
    std::unique_ptr<transform::TransformationEngine> engine_;
};

// Test case: Transform person data with vocabulary mapping and date conversion using real database
TEST_F(TransformationEngineIntegrationTest, BasicPersonTransformation) {
    if (!engine_) {
        GTEST_SKIP() << "Transformation engine not available";
        return;
    }
    
    try {
        configure_person_transformation();
        
        // Initialize engine
        std::unordered_map<std::string, std::any> config{
            {"table_name", std::string("person")}
        };
        
        core::ProcessingContext context;
        engine_->initialize(config, context);
    
        // Create test record
        auto input_record = create_test_record({
            {"patient_id", 12345},
            {"gender", std::string("Male")},
            {"birth_date", std::string("1985-06-15")},
            {"race", std::string("White")},
            {"ethnicity", std::string("Not Hispanic")},
            {"age", 38}
        });
    
        // Transform record
        auto result = engine_->transform(input_record, context);
        
        ASSERT_TRUE(result.has_value());
        
        const auto& transformed = result.value();
        EXPECT_TRUE(transformed.hasField("person_id"));
        EXPECT_EQ(std::any_cast<int>(transformed.getField("person_id")), 12345);
        
        EXPECT_TRUE(transformed.hasField("year_of_birth"));
        EXPECT_EQ(std::any_cast<std::string>(transformed.getField("year_of_birth")), "1985");
        
    } catch (const std::exception& e) {
        GTEST_SKIP() << "BasicPersonTransformation failed: " << e.what();
    }
}

// Test case: Process a batch of records and verify batch processing functionality
TEST_F(TransformationEngineIntegrationTest, BatchTransformation) {
    if (!engine_) {
        GTEST_SKIP() << "Transformation engine not available";
        return;
    }
    
    try {
        configure_person_transformation();
        
        core::ProcessingContext context;
        std::unordered_map<std::string, std::any> config{
            {"table_name", std::string("person")}
        };
        engine_->initialize(config, context);

        // Create batch of test records
        TestDataGenerator generator;
        auto records = generator.generate_patient_records(100);
        
        core::RecordBatch input_batch;
        for (const auto& record : records) {
            input_batch.addRecord(record);
        }
        
        // Transform batch
        auto result_batch = engine_->transform_batch(input_batch, context);
        
        // Verify results
        EXPECT_GT(result_batch.size(), 0);
        EXPECT_LE(result_batch.size(), input_batch.size()); // Some may be filtered out
    
        // Check first record
        if (!result_batch.isEmpty()) {
            const auto& first = result_batch.getRecord(0);
            EXPECT_TRUE(first.hasField("person_id"));
            EXPECT_TRUE(first.hasField("gender_concept_id"));
            EXPECT_TRUE(first.hasField("year_of_birth"));
        }
        
    } catch (const std::exception& e) {
        GTEST_SKIP() << "BatchTransformation failed: " << e.what();
    }
}

// Test case: Validate records against configured validation rules and check error reporting
TEST_F(TransformationEngineIntegrationTest, ValidationRules) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Test with invalid record (missing required fields)
    auto invalid_record = create_test_record({
        {"patient_id", 12345},
        // Missing gender
        {"birth_date", std::string("1850-01-01")}, // Too old
        {"age", 173}
    });
    
    auto validation_result = engine_->validate(invalid_record);
    EXPECT_FALSE(validation_result.is_valid());
    EXPECT_GT(validation_result.error_count(), 0);
    
    // Test with valid record
    auto valid_record = create_test_record({
        {"patient_id", 12345},
        {"gender", std::string("Male")},
        {"birth_date", std::string("1985-06-15")},
        {"age", 38}
    });
    
    validation_result = engine_->validate(valid_record);
    EXPECT_TRUE(validation_result.is_valid());
}

// Test case: Apply filtering rules to records and verify filtering behavior
TEST_F(TransformationEngineIntegrationTest, FilteringRules) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Test record that should pass filters
    auto valid_record = create_test_record({
        {"patient_id", 12345},
        {"gender", std::string("Male")},
        {"birth_date", std::string("1985-06-15")},
        {"age", 38}
    });
    
    auto result = engine_->transform(valid_record, context);
    EXPECT_TRUE(result.has_value());
    
    // Test record that should be filtered out (missing patient_id)
    auto filtered_record = create_test_record({
        {"gender", std::string("Male")},
        {"birth_date", std::string("1985-06-15")},
        {"age", 38}
    });
    
    result = engine_->transform(filtered_record, context);
    EXPECT_FALSE(result.has_value());
}

// Test case: Map vocabulary terms using vocabulary service and verify concept ID mapping
TEST_F(TransformationEngineIntegrationTest, VocabularyMapping) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Test vocabulary mapping
    auto record = create_test_record({
        {"patient_id", 12345},
        {"gender", std::string("Male")}, // Should map to concept ID
        {"birth_date", std::string("1985-06-15")},
        {"age", 38}
    });
    
    auto result = engine_->transform(record, context);
    ASSERT_TRUE(result.has_value());
    
    const auto& transformed = result.value();
    EXPECT_TRUE(transformed.hasField("gender_concept_id"));
    
    // Verify that gender was mapped to a concept ID
    auto gender_concept = transformed.getField("gender_concept_id");
    EXPECT_TRUE(gender_concept.type() == typeid(int));
    int concept_id = std::any_cast<int>(gender_concept);
    EXPECT_GT(concept_id, 0);
}

// Test case: Handle transformation errors gracefully and verify error reporting
TEST_F(TransformationEngineIntegrationTest, ErrorHandling) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Test with malformed data that should cause transformation errors
    auto error_record = create_test_record({
        {"patient_id", 12345},
        {"gender", std::string("InvalidGender")}, // Invalid gender value
        {"birth_date", std::string("invalid-date")}, // Invalid date format
        {"age", -5} // Invalid age
    });
    
    // Should handle errors gracefully
    auto result = engine_->transform(error_record, context);
    // May return nullopt or transformed record with default values
    // The important thing is that it doesn't crash
}

// Test case: Measure transformation performance with large datasets
TEST_F(TransformationEngineIntegrationTest, PerformanceTest) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Create large batch for performance testing
    TestDataGenerator generator;
    auto records = generator.generate_patient_records(1000);
    
    core::RecordBatch input_batch;
    for (const auto& record : records) {
        input_batch.addRecord(record);
    }
    
    auto start_time = std::chrono::steady_clock::now();
    auto result_batch = engine_->transform_batch(input_batch, context);
    auto end_time = std::chrono::steady_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Performance assertion: should process 1000 records in reasonable time
    EXPECT_LT(duration.count(), 5000); // Less than 5 seconds
    EXPECT_GT(result_batch.size(), 0);
}

// Test case: Reload configuration and verify engine reinitialization
TEST_F(TransformationEngineIntegrationTest, ConfigurationReloading) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    
    // Initial initialization
    engine_->initialize(config, context);
    
    // Test with first configuration
    auto record1 = create_test_record({
        {"patient_id", 12345},
        {"gender", std::string("Male")},
        {"birth_date", std::string("1985-06-15")},
        {"age", 38}
    });
    
    auto result1 = engine_->transform(record1, context);
    ASSERT_TRUE(result1.has_value());
    
    // Reinitialize with different configuration
    std::unordered_map<std::string, std::any> new_config{
        {"table_name", std::string("person")},
        {"test_mode", true}
    };
    
    engine_->initialize(new_config, context);
    
    // Test with new configuration
    auto record2 = create_test_record({
        {"patient_id", 67890},
        {"gender", std::string("Female")},
        {"birth_date", std::string("1990-03-20")},
        {"age", 33}
    });
    
    auto result2 = engine_->transform(record2, context);
    ASSERT_TRUE(result2.has_value());
    
    // Both should work with their respective configurations
    // Verify that both transformations completed successfully
    EXPECT_TRUE(result1.has_value());
    EXPECT_TRUE(result2.has_value());
}

// Test case: Verify proper memory management and resource cleanup
TEST_F(TransformationEngineIntegrationTest, MemoryManagement) {
    configure_person_transformation();
    
    core::ProcessingContext context;
    std::unordered_map<std::string, std::any> config{
        {"table_name", std::string("person")}
    };
    engine_->initialize(config, context);
    
    // Create many records to test memory management
    TestDataGenerator generator;
    auto records = generator.generate_patient_records(10000);
    
    core::RecordBatch input_batch;
    for (const auto& record : records) {
        input_batch.addRecord(record);
    }
    
    // Process large batch multiple times to test memory management
    for (int i = 0; i < 5; ++i) {
        auto result_batch = engine_->transform_batch(input_batch, context);
        EXPECT_GT(result_batch.size(), 0);
        
        // Clear result batch to free memory
        result_batch.clear();
    }
    
    // Get statistics to verify memory usage is reasonable
    auto stats = engine_->get_statistics();
    EXPECT_GT(std::any_cast<size_t>(stats["records_transformed"]), 0);
    EXPECT_GE(std::any_cast<size_t>(stats["records_transformed"]), std::any_cast<size_t>(stats["records_filtered"]));
}

} // namespace omop::test