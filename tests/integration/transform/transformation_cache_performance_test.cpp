// Integration tests for transformation cache and metrics
#include <gtest/gtest.h>
#include "transform/field_transformations.h"
#include "transform/transformation_engine.h"
#include "test_helpers/transform_test_fixture.h"
#include <random>
#include <thread>

namespace omop::test {

class TransformationCacheMetricsTest : public TransformTestFixture {
protected:
    void SetUp() override {
        TransformTestFixture::SetUp();
        cache_ = std::make_unique<transform::TransformationCache>(1000);
        metrics_ = std::make_unique<transform::TransformationMetrics>();
    }
    
    std::unique_ptr<transform::TransformationCache> cache_;
    std::unique_ptr<transform::TransformationMetrics> metrics_;
};

// Tests basic cache operations with UK-specific data
TEST_F(TransformationCacheMetricsTest, BasicCacheOperations) {
    // Test cache put and get
    cache_->put("postcode:SW1A1AA", std::string("SW1A 1AA"));
    cache_->put("phone:+442079460000", std::string("+44 20 7946 0000"));
    cache_->put("currency:123.45", std::string("£123.45"));
    
    // Test cache hits
    auto postcode = cache_->get("postcode:SW1A1AA");
    ASSERT_TRUE(postcode.has_value());
    EXPECT_EQ(std::any_cast<std::string>(*postcode), "SW1A 1AA");
    
    auto phone = cache_->get("phone:+442079460000");
    ASSERT_TRUE(phone.has_value());
    EXPECT_EQ(std::any_cast<std::string>(*phone), "+44 20 7946 0000");
    
    // Test cache miss
    auto missing = cache_->get("postcode:INVALID");
    EXPECT_FALSE(missing.has_value());
    
    // Check statistics
    auto stats = cache_->get_stats();
    EXPECT_EQ(stats.hits, 2);
    EXPECT_EQ(stats.misses, 1);
    EXPECT_DOUBLE_EQ(stats.hit_rate, 2.0 / 3.0);
}

// Tests LRU eviction policy
TEST_F(TransformationCacheMetricsTest, LRUEviction) {
    // Create cache with small size
    auto small_cache = std::make_unique<transform::TransformationCache>(3);
    
    // Fill cache
    small_cache->put("nhs1", std::string("************"));
    small_cache->put("nhs2", std::string("************"));
    small_cache->put("nhs3", std::string("************"));
    
    // Access first item to make it recently used
    auto accessed = small_cache->get("nhs1");
    EXPECT_TRUE(accessed.has_value());
    
    // Add new item, should evict nhs2 (least recently used)
    small_cache->put("nhs4", std::string("************"));
    
    // Check eviction
    EXPECT_TRUE(small_cache->get("nhs1").has_value()); // Recently accessed
    EXPECT_FALSE(small_cache->get("nhs2").has_value()); // Evicted
    EXPECT_TRUE(small_cache->get("nhs3").has_value()); // Still there
    EXPECT_TRUE(small_cache->get("nhs4").has_value()); // Newly added
}

// Tests cache performance with high load
TEST_F(TransformationCacheMetricsTest, CachePerformance) {
    const int num_unique_keys = 500;
    const int num_accesses = 10000;
    
    std::vector<std::string> keys;
    keys.reserve(num_unique_keys);
    
    // Generate UK-specific cache keys
    for (int i = 0; i < num_unique_keys; ++i) {
        if (i % 3 == 0) {
            keys.push_back("postcode:" + std::to_string(i));
        } else if (i % 3 == 1) {
            keys.push_back("nhs:" + std::to_string(i));
        } else {
            keys.push_back("currency:" + std::to_string(i));
        }
    }
    
    // Populate cache
    for (const auto& key : keys) {
        cache_->put(key, std::string("value_" + key));
    }
    
    // Random access pattern with skewed distribution (80/20 rule)
    std::mt19937 rng(42);
    std::uniform_real_distribution<> dist(0.0, 1.0);
    
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_accesses; ++i) {
        std::string key;
        if (dist(rng) < 0.8) {
            // 80% of accesses to 20% of keys (hot data)
            key = keys[i % (num_unique_keys / 5)];
        } else {
            // 20% of accesses to remaining keys
            key = keys[num_unique_keys / 5 + (i % (num_unique_keys * 4 / 5))];
        }
        
        cache_->get(key);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    auto stats = cache_->get_stats();
    double avg_access_time = static_cast<double>(duration.count()) / num_accesses;
    
    logger_->info("Cache performance:");
    logger_->info("  - Total accesses: {}", num_accesses);
    logger_->info("  - Average access time: {:.2f} µs", avg_access_time);
    logger_->info("  - Hit rate: {:.2f}%", stats.hit_rate * 100);
    logger_->info("  - Cache size: {}", stats.size);
    
    EXPECT_GT(stats.hit_rate, 0.7) << "Hit rate too low for skewed access pattern";
    EXPECT_LT(avg_access_time, 10.0) << "Cache access too slow";
}

// Tests concurrent cache access
TEST_F(TransformationCacheMetricsTest, ConcurrentCacheAccess) {
    const int num_threads = 8;
    const int operations_per_thread = 1000;
    
    std::vector<std::thread> threads;
    std::atomic<int> total_hits(0);
    std::atomic<int> total_misses(0);
    
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, &total_hits, &total_misses, t]() {
            std::mt19937 rng(t);
            std::uniform_int_distribution<> dist(0, 99);
            
            for (int i = 0; i < operations_per_thread; ++i) {
                std::string key = "key_" + std::to_string(dist(rng));
                
                // 70% reads, 30% writes
                if (i % 10 < 7) {
                    auto value = cache_->get(key);
                    if (value.has_value()) {
                        total_hits++;
                    } else {
                        total_misses++;
                    }
                } else {
                    cache_->put(key, std::string("thread_") + std::to_string(t) + "_value_" + std::to_string(i));
                }
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    auto stats = cache_->get_stats();
    
    logger_->info("Concurrent cache access:");
    logger_->info("  - Total operations: {}", num_threads * operations_per_thread);
    logger_->info("  - Thread-counted hits: {}", total_hits.load());
    logger_->info("  - Thread-counted misses: {}", total_misses.load());
    logger_->info("  - Cache stats hits: {}", stats.hits);
    logger_->info("  - Cache stats misses: {}", stats.misses);
    
    // Verify no operations were lost
    EXPECT_GT(total_hits.load() + total_misses.load(), 0);
}

// Tests transformation metrics collection
TEST_F(TransformationCacheMetricsTest, MetricsCollection) {
    // Record various transformation executions
    std::vector<std::pair<std::string, std::string>> field_transforms = {
        {"postcode", "pattern_validation"},
        {"nhs_number", "checksum_validation"},
        {"temperature_c", "range_validation"},
        {"admission_date", "date_parsing"},
        {"cost_gbp", "currency_formatting"}
    };
    
    std::mt19937 rng(42);
    std::normal_distribution<> duration_dist(5.0, 2.0); // Mean 5ms, std dev 2ms
    std::bernoulli_distribution success_dist(0.95); // 95% success rate
    
    // Simulate transformation executions
    for (int i = 0; i < 1000; ++i) {
        const auto& [field, transform_type] = field_transforms[i % field_transforms.size()];
        
        double duration_ms = std::max(0.1, duration_dist(rng));
        bool success = success_dist(rng);
        
        metrics_->record_execution(
            field,
            transform_type,
            std::chrono::duration<double, std::milli>(duration_ms),
            success
        );
    }
    
    // Check field statistics
    for (const auto& [field, _] : field_transforms) {
        auto stats = metrics_->get_field_stats(field);
        
        logger_->info("Field '{}' stats:", field);
        logger_->info("  - Total: {}", stats.total_count);
        logger_->info("  - Success rate: {:.2f}%", stats.success_rate() * 100);
        logger_->info("  - Avg duration: {:.2f} ms", stats.average_duration() * 1000);
        
        EXPECT_GT(stats.total_count, 0);
        EXPECT_GT(stats.success_rate(), 0.9) << "Success rate too low for field: " << field;
    }
    
    // Check transformation type statistics
    for (const auto& [_, transform_type] : field_transforms) {
        auto stats = metrics_->get_transformation_stats(transform_type);
        
        EXPECT_GT(stats.total_count, 0);
        EXPECT_GT(stats.average_duration(), 0.0);
    }
}

// Tests metrics aggregation and reporting
TEST_F(TransformationCacheMetricsTest, MetricsAggregation) {
    // Simulate a day's worth of transformations with UK business hours pattern
    const int hours_in_day = 24;
    const int base_load_per_hour = 1000;
    
    // UK business hours (9 AM - 5 PM) have higher load
    std::vector<int> hourly_load(hours_in_day, base_load_per_hour);
    for (int hour = 9; hour < 17; ++hour) {
        hourly_load[hour] = base_load_per_hour * 3; // 3x load during business hours
    }
    
    // Simulate hourly transformations
    for (int hour = 0; hour < hours_in_day; ++hour) {
        for (int i = 0; i < hourly_load[hour]; ++i) {
            // Different transformation types have different performance characteristics
            if (i % 5 == 0) {
                // Vocabulary lookup - slower
                metrics_->record_execution(
                    "diagnosis_code",
                    "vocabulary_lookup",
                    std::chrono::milliseconds(10 + (i % 5)),
                    true
                );
            } else if (i % 5 == 1) {
                // Date parsing - fast
                metrics_->record_execution(
                    "admission_date",
                    "date_parsing",
                    std::chrono::milliseconds(2 + (i % 2)),
                    true
                );
            } else if (i % 5 == 2) {
                // Pattern validation - medium
                metrics_->record_execution(
                    "postcode",
                    "pattern_validation",
                    std::chrono::milliseconds(5 + (i % 3)),
                    i % 100 != 0 // 1% failure rate
                );
            } else if (i % 5 == 3) {
                // Currency formatting - fast
                metrics_->record_execution(
                    "amount_gbp",
                    "currency_formatting",
                    std::chrono::milliseconds(1),
                    true
                );
            } else {
                // Range validation - fast
                metrics_->record_execution(
                    "temperature_c",
                    "range_validation",
                    std::chrono::milliseconds(1),
                    true
                );
            }
        }
    }
    
    // Analyze overall metrics
    auto field_names = metrics_->get_field_names();
    
    logger_->info("Daily transformation metrics summary:");
    
    size_t total_transformations = 0;
    size_t total_failures = 0;
    double total_duration = 0.0;
    
    for (const auto& field : field_names) {
        auto stats = metrics_->get_field_stats(field);
        total_transformations += stats.total_count;
        total_failures += stats.error_count;
        total_duration += stats.total_duration.count();
        
        logger_->info("  {}: {} transforms, {:.2f}% success, {:.2f} ms avg",
                     field,
                     stats.total_count,
                     stats.success_rate() * 100,
                     stats.average_duration() * 1000);
    }
    
    logger_->info("Total: {} transformations, {:.2f}% success rate, {:.2f} seconds total time",
                 total_transformations,
                 (1.0 - static_cast<double>(total_failures) / total_transformations) * 100,
                 total_duration);
    
    // Verify expected totals
    size_t expected_total = std::accumulate(hourly_load.begin(), hourly_load.end(), 0);
    EXPECT_EQ(total_transformations, expected_total);
    EXPECT_LT(total_failures, total_transformations * 0.02) << "Failure rate too high";
}

} // namespace omop::test 