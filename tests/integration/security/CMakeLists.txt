# Security integration tests
set(SECURITY_INTEGRATION_TEST_SOURCES
    authentication_security_integration_test.cpp
)

add_executable(security_integration_tests ${SECURITY_INTEGRATION_TEST_SOURCES})

target_link_libraries(security_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        omop_security
#        omop_service
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(security_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME security_integration_tests
    COMMAND security_integration_tests
)

set_tests_properties(security_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;security"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)