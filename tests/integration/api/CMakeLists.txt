# Updated API integration tests CMakeLists.txt
set(API_INTEGRATION_TEST_SOURCES
    combined_rest_grpc_api_test.cpp
    grpc_service_endpoints_test.cpp
    rest_endpoints_functionality_test.cpp
    test_api_integration_updated.cpp
)

add_executable(api_integration_tests_updated ${API_INTEGRATION_TEST_SOURCES})

target_link_libraries(api_integration_tests_updated
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        integration_test_helpers
        gtest
        gtest_main
        gmock
        nlohmann_json::nlohmann_json
        Threads::Threads
)

target_include_directories(api_integration_tests_updated
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/src/app/api
        ${CMAKE_SOURCE_DIR}/tests/integration
        ${CMAKE_CURRENT_SOURCE_DIR}/../test_helpers
)

# Add compile definitions for test configuration
target_compile_definitions(api_integration_tests_updated
    PRIVATE
        TEST_DATA_DIR="${CMAKE_CURRENT_SOURCE_DIR}/../test_data"
        TEST_OUTPUT_DIR="${CMAKE_CURRENT_BINARY_DIR}/test_output"
        OMOP_TEST_INTEGRATION
)

add_test(
    NAME api_integration_tests_updated
    COMMAND api_integration_tests_updated
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

set_tests_properties(api_integration_tests_updated PROPERTIES
    TIMEOUT 600  # 10 minutes for API tests
    LABELS "integration;api;updated"
    ENVIRONMENT "TEST_DATA_DIR=${CMAKE_CURRENT_SOURCE_DIR}/../test_data;TEST_OUTPUT_DIR=${CMAKE_CURRENT_BINARY_DIR}/test_output"
)

# Create test output directory
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_output)