# Core integration tests
set(CORE_INTEGRATION_TEST_SOURCES
    job_lifecycle_management_test.cpp
    job_scheduling_strategies_test.cpp
    etl_pipeline_execution_test.cpp
    record_batch_operations_test.cpp
    core_interfaces_compatibility_test.cpp
    component_factory_creation_test.cpp
    uk_healthcare_pipeline_test.cpp
)

add_executable(core_integration_tests ${CORE_INTEGRATION_TEST_SOURCES})

target_link_libraries(core_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
#        omop_service
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(core_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME core_integration_tests
    COMMAND core_integration_tests
)

set_tests_properties(core_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;core"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)