// tests/integration/core/test_record_batch_integration.cpp
// Tests record batch operations including creation, manipulation, and processing

#include <gtest/gtest.h>
#include "core/record.h"
#include "core/interfaces.h"
#include "common/utilities.h"
#include "test_helpers/integration_test_base.h"
#include <memory>
#include <thread>
#include <chrono>
#include <random>

namespace omop::core::test {

class RecordBatchIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize any required resources
    }

    void TearDown() override {
        // Clean up resources
    }

    // Helper method to create test records
    Record createTestRecord(int id, const std::string& name, int age) {
        Record record;
        record.setField("person_id", id);
        record.setField("person_name", name);
        record.setField("age", age);
        record.setField("birth_date", std::chrono::system_clock::now() - std::chrono::hours(24 * 365 * age));
        record.setField("gender", (id % 2 == 0) ? "M" : "F");
        record.setField("created_at", std::chrono::system_clock::now());
        return record;
    }

    // Helper method to create batch with test data
    RecordBatch createTestBatch(size_t size) {
        RecordBatch batch;
        for (size_t i = 0; i < size; ++i) {
            batch.addRecord(createTestRecord(
                static_cast<int>(i + 1),
                "Person_" + std::to_string(i + 1),
                20 + static_cast<int>(i % 60)
            ));
        }
        return batch;
    }
};

// Test basic record operations
TEST_F(RecordBatchIntegrationTest, BasicRecordOperations) {
    Record record;

    // Test setting and getting fields
    record.setField("id", 123);
    record.setField("name", std::string("John Doe"));
    record.setField("amount", 99.99);
    record.setField("active", true);

    EXPECT_EQ(record.getFieldAs<int>("id"), 123);
    EXPECT_EQ(record.getFieldAs<std::string>("name"), "John Doe");
    EXPECT_DOUBLE_EQ(record.getFieldAs<double>("amount"), 99.99);
    EXPECT_TRUE(record.getFieldAs<bool>("active"));

    // Test field existence
    EXPECT_TRUE(record.hasField("id"));
    EXPECT_TRUE(record.hasField("name"));
    EXPECT_FALSE(record.hasField("nonexistent"));

    // Test optional field access
    auto optional_field = record.getFieldOptional("amount");
    EXPECT_TRUE(optional_field.has_value());
    EXPECT_DOUBLE_EQ(std::any_cast<double>(*optional_field), 99.99);

    auto missing_field = record.getFieldOptional("missing");
    EXPECT_FALSE(missing_field.has_value());
}

// Test record metadata
TEST_F(RecordBatchIntegrationTest, RecordMetadata) {
    Record record;

    // Set metadata
    Record::RecordMetadata metadata;
    metadata.source_table = "patient_data";
    metadata.target_table = "person";
    metadata.source_row_number = 42;
    metadata.extraction_time = std::chrono::system_clock::now();
    metadata.record_id = "REC_" + omop::common::CryptoUtils::generate_uuid();
    metadata.custom["hospital_id"] = "HOSP_001";
    metadata.custom["data_version"] = "2.0";

    record.setMetadata(metadata);

    // Verify metadata
    const auto& retrieved_metadata = record.getMetadata();
    EXPECT_EQ(retrieved_metadata.source_table, "patient_data");
    EXPECT_EQ(retrieved_metadata.target_table, "person");
    EXPECT_EQ(retrieved_metadata.source_row_number, 42);
    EXPECT_FALSE(retrieved_metadata.record_id.empty());
    EXPECT_EQ(retrieved_metadata.custom.at("hospital_id"), "HOSP_001");
}

// Test batch operations
TEST_F(RecordBatchIntegrationTest, BatchOperations) {
    RecordBatch batch(100); // Pre-allocate capacity

    // Add records
    for (int i = 0; i < 50; ++i) {
        batch.addRecord(createTestRecord(i, "Person_" + std::to_string(i), 25 + i));
    }

    EXPECT_EQ(batch.size(), 50);
    EXPECT_FALSE(batch.isEmpty());

    // Access records
    const auto& first_record = batch.getRecord(0);
    EXPECT_EQ(first_record.getFieldAs<int>("person_id"), 0);

    auto& mutable_record = batch.getRecordMutable(10);
    mutable_record.setField("updated", true);
    EXPECT_TRUE(batch.getRecord(10).getFieldAs<bool>("updated"));

    // Clear batch
    batch.clear();
    EXPECT_TRUE(batch.isEmpty());
    EXPECT_EQ(batch.size(), 0);
}

// Test batch iteration
TEST_F(RecordBatchIntegrationTest, BatchIteration) {
    auto batch = createTestBatch(100);

    // Range-based for loop
    int count = 0;
    for (const auto& record : batch) {
        EXPECT_TRUE(record.hasField("person_id"));
        count++;
    }
    EXPECT_EQ(count, 100);

    // Iterator-based modification
    for (auto& record : batch) {
        record.setField("processed", true);
    }

    // Verify all records were modified
    for (const auto& record : batch) {
        EXPECT_TRUE(record.getFieldAs<bool>("processed"));
    }
}

// Test record field operations
TEST_F(RecordBatchIntegrationTest, RecordFieldOperations) {
    Record record = createTestRecord(1, "Test Person", 30);

    // Get all field names
    auto field_names = record.getFieldNames();
    EXPECT_GE(field_names.size(), 5); // At least the fields we set

    // Check field count
    EXPECT_EQ(record.getFieldCount(), field_names.size());

    // Remove field
    bool removed = record.removeField("age");
    EXPECT_TRUE(removed);
    EXPECT_FALSE(record.hasField("age"));

    // Try to remove non-existent field
    removed = record.removeField("nonexistent");
    EXPECT_FALSE(removed);

    // Rename field
    bool renamed = record.renameField("person_name", "full_name");
    EXPECT_TRUE(renamed);
    EXPECT_FALSE(record.hasField("person_name"));
    EXPECT_TRUE(record.hasField("full_name"));
    EXPECT_EQ(record.getFieldAs<std::string>("full_name"), "Test Person");
}

// Test record merging
TEST_F(RecordBatchIntegrationTest, RecordMerging) {
    Record record1 = createTestRecord(1, "John Doe", 30);

    Record record2;
    record2.setField("address", std::string("123 Main St"));
    record2.setField("city", std::string("New York"));
    record2.setField("age", 31); // Different age

    // Merge without overwrite
    record1.merge(record2, false);
    EXPECT_EQ(record1.getFieldAs<std::string>("address"), "123 Main St");
    EXPECT_EQ(record1.getFieldAs<std::string>("city"), "New York");
    EXPECT_EQ(record1.getFieldAs<int>("age"), 30); // Original age preserved

    // Merge with overwrite
    record1.merge(record2, true);
    EXPECT_EQ(record1.getFieldAs<int>("age"), 31); // Age updated
}

// Test record field selection
TEST_F(RecordBatchIntegrationTest, RecordFieldSelection) {
    Record record = createTestRecord(1, "John Doe", 30);
    record.setField("ssn", std::string("***********"));
    record.setField("phone", std::string("555-1234"));

    // Select specific fields
    std::vector<std::string> fields_to_select = {"person_id", "person_name", "age"};
    Record selected = record.selectFields(fields_to_select);

    EXPECT_EQ(selected.getFieldCount(), 3);
    EXPECT_TRUE(selected.hasField("person_id"));
    EXPECT_TRUE(selected.hasField("person_name"));
    EXPECT_TRUE(selected.hasField("age"));
    EXPECT_FALSE(selected.hasField("ssn")); // Excluded
    EXPECT_FALSE(selected.hasField("phone")); // Excluded
}

// Test JSON serialization
TEST_F(RecordBatchIntegrationTest, JsonSerialization) {
    Record record = createTestRecord(1, "John Doe", 30);
    record.setField("salary", 75000.50);
    record.setField("married", true);

    // Convert to JSON
    std::string json = record.toJson(true); // Pretty print
    EXPECT_FALSE(json.empty());
    EXPECT_NE(json.find("person_id"), std::string::npos);
    EXPECT_NE(json.find("John Doe"), std::string::npos);

    // Create record from JSON
    Record from_json = Record::fromJson(json);
    EXPECT_EQ(from_json.getFieldAs<int64_t>("person_id"), 1);
    EXPECT_EQ(from_json.getFieldAs<std::string>("person_name"), "John Doe");
    EXPECT_DOUBLE_EQ(from_json.getFieldAs<double>("salary"), 75000.50);
    EXPECT_TRUE(from_json.getFieldAs<bool>("married"));
}

// Test field iteration
TEST_F(RecordBatchIntegrationTest, FieldIteration) {
    Record record = createTestRecord(1, "John Doe", 30);

    // Get all field names and verify they exist
    auto field_names = record.getFieldNames();
    EXPECT_GE(field_names.size(), 5);

    // Verify we can access each field
    bool found_person_id = false;
    for (const auto& field_name : field_names) {
        EXPECT_TRUE(record.hasField(field_name));
        if (field_name == "person_id") {
            found_person_id = true;
            EXPECT_EQ(record.getFieldAs<int>(field_name), 1);
        }
    }
    EXPECT_TRUE(found_person_id);
}

// Test large batch processing
TEST_F(RecordBatchIntegrationTest, LargeBatchProcessing) {
    const size_t large_batch_size = 10000;

    auto start = std::chrono::high_resolution_clock::now();

    // Create large batch
    RecordBatch batch;
    batch.reserve(large_batch_size);

    for (size_t i = 0; i < large_batch_size; ++i) {
        batch.addRecord(createTestRecord(
            static_cast<int>(i),
            "Person_" + std::to_string(i),
            20 + static_cast<int>(i % 60)
        ));
    }

    auto creation_time = std::chrono::high_resolution_clock::now() - start;

    EXPECT_EQ(batch.size(), large_batch_size);

    // Process batch
    start = std::chrono::high_resolution_clock::now();

    size_t total_age = 0;
    for (const auto& record : batch) {
        total_age += record.getFieldAs<int>("age");
    }

    auto processing_time = std::chrono::high_resolution_clock::now() - start;

    // Log performance metrics
    auto creation_ms = std::chrono::duration_cast<std::chrono::milliseconds>(creation_time).count();
    auto processing_ms = std::chrono::duration_cast<std::chrono::milliseconds>(processing_time).count();

    std::cout << "Large batch creation time: " << creation_ms << " ms" << std::endl;
    std::cout << "Large batch processing time: " << processing_ms << " ms" << std::endl;
    std::cout << "Records per second: " << (large_batch_size * 1000.0 / processing_ms) << std::endl;

    EXPECT_GT(total_age, 0);
}

// Test concurrent batch access
TEST_F(RecordBatchIntegrationTest, ConcurrentBatchAccess) {
    auto batch = createTestBatch(1000);
    std::atomic<size_t> total_processed{0};
    std::atomic<bool> error_occurred{false};

    // Multiple threads reading from the same batch
    std::vector<std::thread> readers;
    const size_t num_readers = 5;

    for (size_t t = 0; t < num_readers; ++t) {
        readers.emplace_back([&batch, &total_processed, &error_occurred, t]() {
            try {
                size_t local_count = 0;
                for (size_t i = t; i < batch.size(); i += num_readers) {
                    const auto& record = batch.getRecord(i);
                    if (record.hasField("person_id")) {
                        local_count++;
                    }
                }
                total_processed += local_count;
            } catch (const std::exception& e) {
                std::cout << "Thread " << t << " error: " << e.what() << std::endl;
                error_occurred = true;
            }
        });
    }

    // Wait for all threads
    for (auto& thread : readers) {
        thread.join();
    }

    EXPECT_FALSE(error_occurred);
    EXPECT_EQ(total_processed, batch.size());
}

// Test batch memory efficiency
TEST_F(RecordBatchIntegrationTest, BatchMemoryEfficiency) {
    const size_t batch_sizes[] = {100, 1000, 5000, 10000};

    for (size_t batch_size : batch_sizes) {
        RecordBatch batch;
        batch.reserve(batch_size); // Pre-allocate

        // Measure memory before
        size_t estimated_memory_before = batch.size() * sizeof(Record);

        // Fill batch
        for (size_t i = 0; i < batch_size; ++i) {
            batch.addRecord(createTestRecord(
                static_cast<int>(i),
                "Person_" + std::to_string(i),
                25
            ));
        }

        // Estimate memory usage
        size_t estimated_memory_after = 0;
        for (const auto& record : batch) {
            estimated_memory_after += sizeof(Record);
            estimated_memory_after += record.getFieldCount() * (sizeof(std::string) + sizeof(std::any));
        }

        std::cout << "Batch size: " << batch_size
                  << ", Estimated memory: " << estimated_memory_after / 1024 << " KB" << std::endl;

        // Verify batch integrity
        EXPECT_EQ(batch.size(), batch_size);
        for (size_t i = 0; i < batch_size; ++i) {
            EXPECT_EQ(batch.getRecord(i).getFieldAs<int>("person_id"), static_cast<int>(i));
        }
    }
}

// Test record equality comparison
TEST_F(RecordBatchIntegrationTest, RecordEquality) {
    Record record1 = createTestRecord(1, "John Doe", 30);
    Record record2 = createTestRecord(1, "John Doe", 30);
    Record record3 = createTestRecord(2, "Jane Doe", 28);

    // Same data should be equal
    EXPECT_EQ(record1, record2);

    // Different data should not be equal
    EXPECT_NE(record1, record3);

    // Modify record2 and verify inequality
    record2.setField("extra_field", 123);
    EXPECT_NE(record1, record2);
}

// Test field metadata
TEST_F(RecordBatchIntegrationTest, FieldMetadata) {
    Record record;

    // Set field with metadata
    Record::FieldMetadata id_metadata;
    id_metadata.name = "person_id";
    id_metadata.data_type = "INTEGER";
    id_metadata.is_nullable = false;
    id_metadata.source_column = "patient_id";
    id_metadata.description = "Unique person identifier";

    record.setField("person_id", 12345);
    record.setFieldMetadata("person_id", id_metadata);

    // Retrieve metadata
    auto metadata = record.getFieldMetadata("person_id");
    ASSERT_TRUE(metadata.has_value());
    EXPECT_EQ(metadata->data_type, "INTEGER");
    EXPECT_FALSE(metadata->is_nullable);
    EXPECT_EQ(metadata->source_column, "patient_id");
}

} // namespace omop::core::test