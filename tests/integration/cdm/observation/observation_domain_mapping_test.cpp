/**
 * @file test_observation_integration.cpp
 * @brief Integration tests for Observation table with UK NHS clinical scenarios
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <chrono>
#include <vector>
#include <memory>
#include <map>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"

using namespace omop::cdm;
using namespace std::chrono;

/**
 * @brief Test fixture for Observation integration tests
 */
class ObservationIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Common UK dates for testing
        uk_observation_date_2024 = createUKDate(10, 7, 2024);
        uk_observation_date_2023 = createUKDate(15, 11, 2023);
        uk_observation_datetime_2024 = createUKDateTime(10, 7, 2024, 14, 30);
        current_date = system_clock::now();
        
        // Set up UK-specific test data
        setupUKTestData();
    }

    system_clock::time_point createUKDate(int day, int month, int year) {
        tm timeinfo = {};
        timeinfo.tm_mday = day;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_year = year - 1900;
        return system_clock::from_time_t(mktime(&timeinfo));
    }

    system_clock::time_point createUKDateTime(int day, int month, int year, int hour, int minute) {
        tm timeinfo = {};
        timeinfo.tm_mday = day;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_year = year - 1900;
        timeinfo.tm_hour = hour;
        timeinfo.tm_min = minute;
        return system_clock::from_time_t(mktime(&timeinfo));
    }

    void setupUKTestData() {
        // UK social observation concepts
        uk_smoking_status_concept = 4275495; // Smoking status
        uk_alcohol_intake_concept = 4220562; // Alcohol consumption
        uk_employment_status_concept = 4053609; // Employment status
        uk_housing_status_concept = 4161979; // Housing status
        
        // UK clinical observation concepts
        uk_pain_scale_concept = 4139394; // Pain scale (0-10)
        uk_blood_pressure_concept = 4154790; // Blood pressure observation
        uk_mental_health_concept = 4058243; // Mental health assessment
        uk_mobility_concept = 4161723; // Mobility assessment
        
        // UK observation type concepts
        uk_patient_reported_concept = 45905771; // Patient reported outcome
        uk_clinician_assessed_concept = 45905770; // Clinician assessment
        uk_survey_concept = 45905772; // Survey/questionnaire
        
        // UK provider and facility IDs
        uk_gp_provider_id = 50001;
        uk_mental_health_provider_id = 50012;
        uk_community_nurse_provider_id = 50045;
        uk_nhs_trust_care_site_id = 20001;
        uk_gp_practice_care_site_id = 20055;
    }

    system_clock::time_point uk_observation_date_2024;
    system_clock::time_point uk_observation_date_2023;
    system_clock::time_point uk_observation_datetime_2024;
    system_clock::time_point current_date;
    
    // UK-specific concept IDs
    int32_t uk_smoking_status_concept;
    int32_t uk_alcohol_intake_concept;
    int32_t uk_employment_status_concept;
    int32_t uk_housing_status_concept;
    int32_t uk_pain_scale_concept;
    int32_t uk_blood_pressure_concept;
    int32_t uk_mental_health_concept;
    int32_t uk_mobility_concept;
    
    // UK observation types
    int32_t uk_patient_reported_concept;
    int32_t uk_clinician_assessed_concept;
    int32_t uk_survey_concept;
    
    // UK healthcare providers and facilities
    int32_t uk_gp_provider_id;
    int32_t uk_mental_health_provider_id;
    int32_t uk_community_nurse_provider_id;
    int32_t uk_nhs_trust_care_site_id;
    int32_t uk_gp_practice_care_site_id;
};

// Tests comprehensive NHS patient-reported outcome record
TEST_F(ObservationIntegrationTest, CompleteNHSPatientReportedOutcome) {
    Observation observation;
    
    // Set comprehensive NHS patient-reported data
    observation.observation_id = 3000001;
    observation.person_id = 1000001;
    observation.observation_concept_id = uk_pain_scale_concept;
    observation.observation_date = uk_observation_date_2024;
    observation.observation_datetime = uk_observation_datetime_2024;
    observation.observation_type_concept_id = uk_patient_reported_concept;
    observation.value_as_number = 7.0; // Pain scale 7/10
    observation.value_as_string = "Severe pain";
    observation.value_as_concept_id = 4141062; // Severe pain concept
    observation.qualifier_concept_id = 4124400; // Continuous pain qualifier
    observation.unit_concept_id = 8510; // Pain scale unit
    observation.provider_id = uk_gp_provider_id;
    observation.visit_occurrence_id = 2000001;
    observation.visit_detail_id = 3000001;
    observation.observation_source_value = "PAIN_SCALE_7";
    observation.observation_source_concept_id = 0;
    observation.unit_source_value = "0-10 scale";
    observation.qualifier_source_value = "continuous";
    observation.value_source_value = "7/10 severe";
    
    // Validate record
    EXPECT_TRUE(observation.validate());
    auto errors = observation.validation_errors();
    EXPECT_TRUE(errors.empty());
    
    // Test SQL generation
    auto sql = observation.to_insert_sql(true);
    EXPECT_NE(sql.find("INSERT INTO cdm.observation"), std::string::npos);
    EXPECT_NE(sql.find("7"), std::string::npos); // Pain scale value
    EXPECT_NE(sql.find("Severe pain"), std::string::npos);
}

// Tests UK social determinants of health observations
TEST_F(ObservationIntegrationTest, UKSocialDeterminantsObservations) {
    std::vector<Observation> social_observations;
    
    // Smoking status observation
    Observation smoking;
    smoking.observation_id = 3000002;
    smoking.person_id = 1000001;
    smoking.observation_concept_id = uk_smoking_status_concept;
    smoking.observation_date = uk_observation_date_2024;
    smoking.observation_type_concept_id = uk_survey_concept;
    smoking.value_as_concept_id = 4144272; // Former smoker
    smoking.value_as_string = "Former smoker (quit 2 years ago)";
    smoking.observation_source_value = "SMOKING_STATUS_FORMER";
    smoking.value_source_value = "Ex-smoker 2yr";
    
    // Employment status observation
    Observation employment;
    employment.observation_id = 3000003;
    employment.person_id = 1000001;
    employment.observation_concept_id = uk_employment_status_concept;
    employment.observation_date = uk_observation_date_2024;
    employment.observation_type_concept_id = uk_survey_concept;
    employment.value_as_concept_id = 4053608; // Employed
    employment.value_as_string = "Full-time employed";
    employment.observation_source_value = "EMPLOYMENT_FULLTIME";
    employment.value_source_value = "Full-time";
    
    // Housing status observation
    Observation housing;
    housing.observation_id = 3000004;
    housing.person_id = 1000001;
    housing.observation_concept_id = uk_housing_status_concept;
    housing.observation_date = uk_observation_date_2024;
    housing.observation_type_concept_id = uk_survey_concept;
    housing.value_as_concept_id = 4161980; // Owner occupied
    housing.value_as_string = "Owner-occupied house";
    housing.observation_source_value = "HOUSING_OWNER_OCC";
    housing.value_source_value = "Owner occupied";
    
    social_observations.push_back(std::move(smoking));
    social_observations.push_back(std::move(employment));
    social_observations.push_back(std::move(housing));
    
    // Validate all social observations
    for (const auto& obs : social_observations) {
        EXPECT_TRUE(obs.validate());
        auto errors = obs.validation_errors();
        EXPECT_TRUE(errors.empty());
        
        // Verify UK-specific source values
        EXPECT_TRUE(obs.observation_source_value.has_value());
        EXPECT_FALSE(obs.observation_source_value.value().empty());
    }
}

// Tests NHS mental health assessment observations
TEST_F(ObservationIntegrationTest, NHSMentalHealthAssessments) {
    Observation mental_health;
    
    // Set NHS mental health assessment data
    mental_health.observation_id = 3000005;
    mental_health.person_id = 1000002;
    mental_health.observation_concept_id = uk_mental_health_concept;
    mental_health.observation_date = uk_observation_date_2024;
    mental_health.observation_datetime = uk_observation_datetime_2024;
    mental_health.observation_type_concept_id = uk_clinician_assessed_concept;
    mental_health.value_as_number = 15.0; // PHQ-9 score
    mental_health.value_as_string = "Moderate depression (PHQ-9: 15)";
    mental_health.value_as_concept_id = 440383; // Depression concept
    mental_health.qualifier_concept_id = 4124401; // Moderate severity
    mental_health.unit_concept_id = 8510; // Score unit
    mental_health.provider_id = uk_mental_health_provider_id;
    mental_health.visit_occurrence_id = 2000002;
    mental_health.observation_source_value = "PHQ9_MODERATE";
    mental_health.unit_source_value = "PHQ-9 scale";
    mental_health.qualifier_source_value = "moderate";
    mental_health.value_source_value = "15/27";
    
    // Validate mental health observation
    EXPECT_TRUE(mental_health.validate());
    
    // Test field extraction
    auto field_names = mental_health.field_names();
    auto field_values = mental_health.field_values();
    EXPECT_EQ(field_names.size(), field_values.size());
    EXPECT_EQ(field_names.size(), 21); // Total observation fields
    
    // Test SQL generation contains NHS mental health data
    auto sql = mental_health.to_insert_sql(true);
    EXPECT_NE(sql.find("PHQ9_MODERATE"), std::string::npos);
    EXPECT_NE(sql.find("15"), std::string::npos); // PHQ-9 score
}

// Tests UK community care observations
TEST_F(ObservationIntegrationTest, UKCommunityCareObservations) {
    Observation mobility_assessment;
    
    // Set UK community care mobility assessment
    mobility_assessment.observation_id = 3000006;
    mobility_assessment.person_id = 1000003;
    mobility_assessment.observation_concept_id = uk_mobility_concept;
    mobility_assessment.observation_date = uk_observation_date_2024;
    mobility_assessment.observation_type_concept_id = uk_clinician_assessed_concept;
    mobility_assessment.value_as_concept_id = 4161724; // Requires walking aid
    mobility_assessment.value_as_string = "Requires walking frame for mobility";
    mobility_assessment.qualifier_concept_id = 4125400; // Activities of daily living
    mobility_assessment.provider_id = uk_community_nurse_provider_id;
    mobility_assessment.observation_source_value = "MOBILITY_WALKING_AID";
    mobility_assessment.value_source_value = "Walking frame";
    mobility_assessment.qualifier_source_value = "ADL support";
    
    // Validate community care observation
    EXPECT_TRUE(mobility_assessment.validate());
    
    // Test that provider reflects community care
    EXPECT_EQ(mobility_assessment.provider_id.value(), uk_community_nurse_provider_id);
    
    // Verify SQL generation
    auto sql = mobility_assessment.to_insert_sql(true);
    EXPECT_NE(sql.find("MOBILITY_WALKING_AID"), std::string::npos);
    EXPECT_NE(sql.find("Walking frame"), std::string::npos);
}

// Tests UK alcohol consumption observations with units
TEST_F(ObservationIntegrationTest, UKAlcoholConsumptionWithUnits) {
    Observation alcohol_intake;
    
    // Set UK alcohol consumption data (units per week)
    alcohol_intake.observation_id = 3000007;
    alcohol_intake.person_id = 1000001;
    alcohol_intake.observation_concept_id = uk_alcohol_intake_concept;
    alcohol_intake.observation_date = uk_observation_date_2024;
    alcohol_intake.observation_type_concept_id = uk_patient_reported_concept;
    alcohol_intake.value_as_number = 14.0; // Units per week (UK guideline limit)
    alcohol_intake.value_as_string = "14 units per week (within guidelines)";
    alcohol_intake.value_as_concept_id = 4220563; // Moderate alcohol consumption
    alcohol_intake.unit_concept_id = 8511; // Units per week
    alcohol_intake.provider_id = uk_gp_provider_id;
    alcohol_intake.observation_source_value = "ALCOHOL_14_UNITS_WEEK";
    alcohol_intake.unit_source_value = "units/week";
    alcohol_intake.value_source_value = "14 units/week";
    
    // Validate alcohol observation
    EXPECT_TRUE(alcohol_intake.validate());
    
    // Test UK-specific alcohol unit reporting
    EXPECT_EQ(alcohol_intake.value_as_number.value(), 14.0);
    EXPECT_TRUE(alcohol_intake.unit_source_value.has_value());
    EXPECT_EQ(alcohol_intake.unit_source_value.value(), "units/week");
    
    // Verify SQL contains UK alcohol data
    auto sql = alcohol_intake.to_insert_sql(true);
    EXPECT_NE(sql.find("14"), std::string::npos);
    EXPECT_NE(sql.find("units/week"), std::string::npos);
}

// Tests observation validation errors
TEST_F(ObservationIntegrationTest, ObservationValidationErrors) {
    Observation invalid_observation;
    
    // Create observation with missing required fields
    invalid_observation.observation_id = 0; // Invalid ID
    invalid_observation.person_id = 0; // Invalid person ID
    invalid_observation.observation_concept_id = 0; // Invalid concept
    invalid_observation.observation_date = uk_observation_date_2024;
    invalid_observation.observation_type_concept_id = 0; // Invalid type
    
    // Validate should return false
    EXPECT_FALSE(invalid_observation.validate());
    
    // Check specific validation errors
    auto errors = invalid_observation.validation_errors();
    EXPECT_FALSE(errors.empty());
    EXPECT_GE(errors.size(), 4); // At least 4 validation errors
    
    // Verify error messages
    bool has_obs_id_error = false;
    bool has_person_id_error = false;
    bool has_concept_error = false;
    bool has_type_error = false;
    
    for (const auto& error : errors) {
        if (error.find("observation_id must be positive") != std::string::npos) {
            has_obs_id_error = true;
        }
        if (error.find("person_id must be positive") != std::string::npos) {
            has_person_id_error = true;
        }
        if (error.find("observation_concept_id must be positive") != std::string::npos) {
            has_concept_error = true;
        }
        if (error.find("observation_type_concept_id must be positive") != std::string::npos) {
            has_type_error = true;
        }
    }
    
    EXPECT_TRUE(has_obs_id_error);
    EXPECT_TRUE(has_person_id_error);
    EXPECT_TRUE(has_concept_error);
    EXPECT_TRUE(has_type_error);
}

// Tests bulk observation creation performance
TEST_F(ObservationIntegrationTest, BulkObservationCreationPerformance) {
    const int num_observations = 1000;
    std::vector<Observation> observations;
    observations.reserve(num_observations);
    
    auto start_time = high_resolution_clock::now();
    
    // Create bulk observations
    for (int i = 0; i < num_observations; ++i) {
        Observation obs;
        obs.observation_id = 3000000 + i;
        obs.person_id = 1000000 + (i % 100);
        obs.observation_concept_id = uk_pain_scale_concept;
        obs.observation_date = uk_observation_date_2024;
        obs.observation_type_concept_id = uk_patient_reported_concept;
        obs.value_as_number = static_cast<double>(i % 10); // Pain scale 0-9
        obs.provider_id = uk_gp_provider_id;
        obs.observation_source_value = "BULK_OBS_" + std::to_string(i);
        
        observations.push_back(std::move(obs));
    }
    
    auto end_time = high_resolution_clock::now();
    auto duration = duration_cast<milliseconds>(end_time - start_time);
    
    // Validate all observations
    for (const auto& obs : observations) {
        EXPECT_TRUE(obs.validate());
    }
    
    // Performance should be reasonable (less than 1000ms for 1000 observations)
    EXPECT_LT(duration.count(), 1000);
    EXPECT_EQ(observations.size(), num_observations);
}

// Tests observation field visitor implementation
TEST_F(ObservationIntegrationTest, FieldVisitorImplementation) {
    class TestVisitor : public FieldVisitor {
    public:
        std::map<std::string, std::string> visited_fields;
        
        void visit(const std::string& name, const std::any& /* value */) override {
            // Simple string representation for testing
            visited_fields[name] = "visited";
        }
    };
    
    Observation observation;
    observation.observation_id = 3000008;
    observation.person_id = 1000001;
    observation.observation_concept_id = uk_smoking_status_concept;
    observation.observation_date = uk_observation_date_2024;
    observation.observation_type_concept_id = uk_survey_concept;
    observation.value_as_string = "Never smoked";
    observation.observation_source_value = "SMOKING_NEVER";
    
    TestVisitor visitor;
    observation.visit_fields(visitor);
    
    // Verify all observation fields were visited
    EXPECT_EQ(visitor.visited_fields.size(), 21); // Total observation fields
    EXPECT_TRUE(visitor.visited_fields.count("observation_id") > 0);
    EXPECT_TRUE(visitor.visited_fields.count("person_id") > 0);
    EXPECT_TRUE(visitor.visited_fields.count("observation_concept_id") > 0);
    EXPECT_TRUE(visitor.visited_fields.count("observation_date") > 0);
    EXPECT_TRUE(visitor.visited_fields.count("value_as_string") > 0);
    EXPECT_TRUE(visitor.visited_fields.count("observation_source_value") > 0);
}

// Tests comprehensive UK health survey observations
TEST_F(ObservationIntegrationTest, ComprehensiveUKHealthSurvey) {
    std::vector<Observation> survey_observations;
    
    // Create multiple related survey observations
    const std::vector<std::pair<int32_t, std::string>> survey_questions = {
        {uk_smoking_status_concept, "SMOKING_NEVER"},
        {uk_alcohol_intake_concept, "ALCOHOL_MODERATE"},
        {uk_employment_status_concept, "EMPLOYMENT_RETIRED"},
        {uk_housing_status_concept, "HOUSING_COUNCIL"},
        {uk_pain_scale_concept, "PAIN_MILD"}
    };
    
    for (size_t i = 0; i < survey_questions.size(); ++i) {
        Observation obs;
        obs.observation_id = 3000100 + i;
        obs.person_id = 1000010;
        obs.observation_concept_id = survey_questions[i].first;
        obs.observation_date = uk_observation_date_2024;
        obs.observation_type_concept_id = uk_survey_concept;
        obs.provider_id = uk_gp_provider_id;
        obs.visit_occurrence_id = 2000010;
        obs.observation_source_value = survey_questions[i].second;
        obs.value_source_value = survey_questions[i].second;
        
        survey_observations.push_back(std::move(obs));
    }
    
    // Validate all survey observations
    for (const auto& obs : survey_observations) {
        EXPECT_TRUE(obs.validate());
        EXPECT_EQ(obs.person_id, 1000010); // Same patient
        EXPECT_EQ(obs.visit_occurrence_id.value(), 2000010); // Same visit
        EXPECT_EQ(obs.observation_type_concept_id, uk_survey_concept); // Survey type
    }
    
    EXPECT_EQ(survey_observations.size(), 5);
}

// Tests observation with event linkage
TEST_F(ObservationIntegrationTest, ObservationWithEventLinkage) {
    Observation linked_observation;
    
    // Set observation linked to specific event
    linked_observation.observation_id = 3000009;
    linked_observation.person_id = 1000001;
    linked_observation.observation_concept_id = uk_pain_scale_concept;
    linked_observation.observation_date = uk_observation_date_2024;
    linked_observation.observation_type_concept_id = uk_patient_reported_concept;
    linked_observation.value_as_number = 8.0;
    linked_observation.provider_id = uk_gp_provider_id;
    linked_observation.visit_occurrence_id = 2000001;
    linked_observation.visit_detail_id = 3000001;
    linked_observation.observation_event_id = 4000001; // Linked to procedure
    linked_observation.obs_event_field_concept_id = 1147127; // Post-procedure observation
    linked_observation.observation_source_value = "POST_PROCEDURE_PAIN";
    linked_observation.value_source_value = "8/10 post-op";
    
    // Validate event-linked observation
    EXPECT_TRUE(linked_observation.validate());
    
    // Verify event linkage fields
    EXPECT_TRUE(linked_observation.observation_event_id.has_value());
    EXPECT_TRUE(linked_observation.obs_event_field_concept_id.has_value());
    EXPECT_EQ(linked_observation.observation_event_id.value(), 4000001);
    
    // Test SQL generation includes event linkage
    auto sql = linked_observation.to_insert_sql(true);
    EXPECT_NE(sql.find("4000001"), std::string::npos); // Event ID
    EXPECT_NE(sql.find("POST_PROCEDURE_PAIN"), std::string::npos);
} 