/**
 * @file test_person_integration.cpp
 * @brief Integration tests for Person table with UK NHS data scenarios
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <chrono>
#include <memory>
#include <vector>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"

using namespace omop::cdm;
using namespace std::chrono;

/**
 * @brief Test fixture for Person table integration tests
 */
class PersonIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create UK test dates
        uk_birth_date_1980 = createUKDate(25, 12, 1980);
        uk_birth_date_1965 = createUKDate(15, 6, 1965);
        uk_birth_date_2000 = createUKDate(29, 2, 2000); // Leap year
        current_date = system_clock::now();
    }

    system_clock::time_point createUKDate(int day, int month, int year) {
        tm timeinfo = {};
        timeinfo.tm_mday = day;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_year = year - 1900;
        return system_clock::from_time_t(mktime(&timeinfo));
    }

    system_clock::time_point uk_birth_date_1980;
    system_clock::time_point uk_birth_date_1965;
    system_clock::time_point uk_birth_date_2000;
    system_clock::time_point current_date;
};

// Test complete NHS patient record creation
TEST_F(PersonIntegrationTest, CompleteNHSPatientRecord) {
    Person patient;
    
    // Set comprehensive NHS patient data
    patient.person_id = 1000001;
    patient.gender_concept_id = 8507; // Male
    patient.year_of_birth = 1980;
    patient.month_of_birth = 12;
    patient.day_of_birth = 25;
    patient.birth_datetime = uk_birth_date_1980;
    patient.race_concept_id = 8527; // White
    patient.ethnicity_concept_id = 38003564; // British
    patient.location_id = 10001; // London location
    patient.provider_id = 50001; // GP provider
    patient.care_site_id = 20001; // NHS Trust
    patient.person_source_value = "NHS-**********"; // NHS number format
    patient.gender_source_value = "M";
    patient.gender_source_concept_id = 0;
    patient.race_source_value = "White British";
    patient.race_source_concept_id = 0;
    patient.ethnicity_source_value = "British";
    patient.ethnicity_source_concept_id = 0;
    
    // Validate record
    EXPECT_TRUE(patient.validate());
    auto errors = patient.validation_errors();
    EXPECT_TRUE(errors.empty());
    
    // Test SQL generation
    auto sql = patient.to_insert_sql(true);
    EXPECT_NE(sql.find("INSERT INTO cdm.person"), std::string::npos);
    EXPECT_NE(sql.find("NHS-**********"), std::string::npos);
}

// Test UK-specific gender concepts
TEST_F(PersonIntegrationTest, UKGenderConcepts) {
    std::vector<std::pair<int32_t, std::string>> uk_genders = {
        {8507, "Male"},
        {8532, "Female"},
        {8570, "Ambiguous"},
        {8551, "Unknown"}
    };
    
    for (const auto& [concept_id, source_value] : uk_genders) {
        Person person;
        person.person_id = 1000000 + concept_id;
        person.gender_concept_id = concept_id;
        person.gender_source_value = source_value;
        person.year_of_birth = 1990;
        person.race_concept_id = 8527;
        person.ethnicity_concept_id = 38003564;
        
        EXPECT_TRUE(person.validate()) << "Failed for gender: " << source_value;
    }
}

// Test UK ethnicity categories
TEST_F(PersonIntegrationTest, UKEthnicityCategories) {
    struct EthnicityTest {
        int32_t concept_id;
        std::string source_value;
        std::string description;
    };
    
    std::vector<EthnicityTest> uk_ethnicities = {
        {38003564, "A", "White - British"},
        {38003565, "B", "White - Irish"},
        {38003566, "C", "White - Any other White background"},
        {38003567, "D", "Mixed - White and Black Caribbean"},
        {38003568, "E", "Mixed - White and Black African"},
        {38003569, "F", "Mixed - White and Asian"},
        {38003570, "G", "Mixed - Any other Mixed background"},
        {38003571, "H", "Asian or Asian British - Indian"},
        {38003572, "J", "Asian or Asian British - Pakistani"},
        {38003573, "K", "Asian or Asian British - Bangladeshi"},
        {38003574, "L", "Asian or Asian British - Any other Asian background"},
        {38003575, "M", "Black or Black British - Caribbean"},
        {38003576, "N", "Black or Black British - African"},
        {38003577, "P", "Black or Black British - Any other Black background"},
        {38003578, "R", "Other ethnic groups - Chinese"},
        {38003579, "S", "Other ethnic groups - Any other ethnic group"},
        {38003580, "Z", "Not stated"}
    };
    
    for (const auto& ethnicity : uk_ethnicities) {
        Person person;
        person.person_id = 2000000 + ethnicity.concept_id;
        person.gender_concept_id = 8507;
        person.year_of_birth = 1985;
        person.race_concept_id = 8527;
        person.ethnicity_concept_id = ethnicity.concept_id;
        person.ethnicity_source_value = ethnicity.source_value + " - " + ethnicity.description;
        
        EXPECT_TRUE(person.validate()) << "Failed for ethnicity: " << ethnicity.description;
    }
}

// Test birth date validation
TEST_F(PersonIntegrationTest, BirthDateValidation) {
    Person person;
    person.person_id = 3000001;
    person.gender_concept_id = 8532;
    person.race_concept_id = 8527;
    person.ethnicity_concept_id = 38003564;
    
    // Test valid leap year date
    person.year_of_birth = 2000;
    person.month_of_birth = 2;
    person.day_of_birth = 29;
    person.birth_datetime = uk_birth_date_2000;
    EXPECT_TRUE(person.validate());
    
    // Test invalid leap year date
    person.year_of_birth = 2001; // Not a leap year
    person.month_of_birth = 2;
    person.day_of_birth = 29;
    EXPECT_FALSE(person.validate());
    
    // Test future birth year
    person.year_of_birth = 2030;
    person.month_of_birth = 1;
    person.day_of_birth = 1;
    EXPECT_FALSE(person.validate());
    
    // Test very old birth year
    person.year_of_birth = 1849;
    EXPECT_FALSE(person.validate());
}

// Test NHS number validation patterns
TEST_F(PersonIntegrationTest, NHSNumberPatterns) {
    std::vector<std::string> valid_nhs_numbers = {
        "NHS-**********",
        "**********",
        "************",
        "NHS**********",
        "************" // Valid NHS number with check digit
    };
    
    for (const auto& nhs_number : valid_nhs_numbers) {
        Person person;
        person.person_id = 4000001;
        person.gender_concept_id = 8507;
        person.year_of_birth = 1975;
        person.race_concept_id = 8527;
        person.ethnicity_concept_id = 38003564;
        person.person_source_value = nhs_number;
        
        EXPECT_TRUE(person.validate()) << "Failed for NHS number: " << nhs_number;
        
        // Test SQL escaping
        auto sql = person.to_insert_sql(true);
        EXPECT_NE(sql.find("person_source_value"), std::string::npos);
    }
}

// Test location references for UK regions
TEST_F(PersonIntegrationTest, UKLocationReferences) {
    struct LocationTest {
        int32_t location_id;
        std::string region;
    };
    
    std::vector<LocationTest> uk_locations = {
        {10001, "Greater London"},
        {10002, "West Midlands"},
        {10003, "Greater Manchester"},
        {10004, "West Yorkshire"},
        {10005, "Tyne and Wear"},
        {10006, "Merseyside"},
        {10007, "South Yorkshire"},
        {10008, "Hampshire"},
        {10009, "Kent"},
        {10010, "Essex"}
    };
    
    for (const auto& location : uk_locations) {
        Person person;
        person.person_id = 5000000 + location.location_id;
        person.gender_concept_id = 8532;
        person.year_of_birth = 1988;
        person.race_concept_id = 8527;
        person.ethnicity_concept_id = 38003564;
        person.location_id = location.location_id;
        
        EXPECT_TRUE(person.validate()) << "Failed for location: " << location.region;
    }
}

// Test care site references (NHS Trusts)
TEST_F(PersonIntegrationTest, NHSTrustReferences) {
    std::vector<std::pair<int32_t, std::string>> nhs_trusts = {
        {20001, "University College London Hospitals NHS Foundation Trust"},
        {20002, "Guy's and St Thomas' NHS Foundation Trust"},
        {20003, "Imperial College Healthcare NHS Trust"},
        {20004, "Cambridge University Hospitals NHS Foundation Trust"},
        {20005, "Oxford University Hospitals NHS Foundation Trust"}
    };
    
    for (const auto& [trust_id, trust_name] : nhs_trusts) {
        Person person;
        person.person_id = 6000000 + trust_id;
        person.gender_concept_id = 8507;
        person.year_of_birth = 1970;
        person.race_concept_id = 8527;
        person.ethnicity_concept_id = 38003564;
        person.care_site_id = trust_id;
        
        EXPECT_TRUE(person.validate()) << "Failed for NHS Trust: " << trust_name;
    }
}

// Test provider references (UK medical practitioners)
TEST_F(PersonIntegrationTest, UKProviderReferences) {
    std::vector<std::pair<int32_t, std::string>> provider_types = {
        {50001, "General Practitioner"},
        {50002, "Consultant"},
        {50003, "Registrar"},
        {50004, "Foundation Doctor"},
        {50005, "Nurse Practitioner"}
    };
    
    for (const auto& [provider_id, provider_type] : provider_types) {
        Person person;
        person.person_id = 7000000 + provider_id;
        person.gender_concept_id = 8532;
        person.year_of_birth = 1992;
        person.race_concept_id = 8527;
        person.ethnicity_concept_id = 38003564;
        person.provider_id = provider_id;
        
        EXPECT_TRUE(person.validate()) << "Failed for provider type: " << provider_type;
    }
}

// Test SQL injection prevention
TEST_F(PersonIntegrationTest, SQLInjectionPrevention) {
    Person person;
    person.person_id = 8000001;
    person.gender_concept_id = 8507;
    person.year_of_birth = 1985;
    person.race_concept_id = 8527;
    person.ethnicity_concept_id = 38003564;
    
    // Test various SQL injection attempts
    std::vector<std::string> injection_attempts = {
        "'; DROP TABLE person; --",
        "\" OR 1=1 --",
        "' UNION SELECT * FROM users --",
        "Robert'); DROP TABLE Students;--",
        "O'Brien's NHS-123" // Legitimate apostrophe
    };
    
    for (const auto& injection : injection_attempts) {
        person.person_source_value = injection;
        person.gender_source_value = injection;
        person.race_source_value = injection;
        person.ethnicity_source_value = injection;
        
        EXPECT_TRUE(person.validate());
        
        auto sql = person.to_insert_sql(true);
        
        // Check that SQL is properly escaped
        // The dangerous strings will still appear but should be safely quoted
        // Check that the value is properly quoted (starts and ends with single quotes)
        EXPECT_NE(sql.find("'"), std::string::npos);
        
        // Check apostrophes are escaped by doubling them
        if (injection.find("'") != std::string::npos) {
            EXPECT_NE(sql.find("''"), std::string::npos);
        }
        
        // Verify the dangerous strings are safely contained within quotes
        // (they should appear but be harmless when properly quoted)
        if (injection.find("DROP TABLE") != std::string::npos) {
            EXPECT_NE(sql.find("DROP TABLE"), std::string::npos);
        }
        if (injection.find("UNION SELECT") != std::string::npos) {
            EXPECT_NE(sql.find("UNION SELECT"), std::string::npos);
        }
    }
}

// Test bulk person creation performance
TEST_F(PersonIntegrationTest, BulkPersonCreationPerformance) {
    const int num_persons = 10000;
    auto start = steady_clock::now();
    
    std::vector<std::unique_ptr<Person>> persons;
    persons.reserve(num_persons);
    
    for (int i = 0; i < num_persons; ++i) {
        auto person = std::make_unique<Person>();
        person->person_id = 9000000 + i;
        person->gender_concept_id = (i % 2 == 0) ? 8507 : 8532;
        person->year_of_birth = 1950 + (i % 70);
        person->month_of_birth = 1 + (i % 12);
        person->day_of_birth = 1 + (i % 28);
        person->race_concept_id = 8527;
        person->ethnicity_concept_id = 38003564 + (i % 17);
        person->location_id = 10001 + (i % 10);
        person->person_source_value = "NHS-" + std::to_string(********** + i);
        
        EXPECT_TRUE(person->validate());
        persons.push_back(std::move(person));
    }
    
    auto end = steady_clock::now();
    auto duration = duration_cast<milliseconds>(end - start);
    
    // Should complete in reasonable time
    EXPECT_LT(duration.count(), 5000) << "Bulk creation took too long: " << duration.count() << "ms";
    
    // Verify all persons were created
    EXPECT_EQ(persons.size(), num_persons);
}

// Test field visitor pattern
TEST_F(PersonIntegrationTest, FieldVisitorPattern) {
    class PersonFieldCounter : public FieldVisitor {
    public:
        int total_fields = 0;
        int optional_fields = 0;
        std::vector<std::string> field_names;
        
        void visit(const std::string& name, const std::any& value) override {
            total_fields++;
            field_names.push_back(name);
            
            // Check if field has value (for optional fields)
            try {
                if (value.type() == typeid(std::optional<int32_t>)) {
                    auto opt_val = std::any_cast<std::optional<int32_t>>(value);
                    if (opt_val.has_value()) optional_fields++;
                } else if (value.type() == typeid(std::optional<std::string>)) {
                    auto opt_val = std::any_cast<std::optional<std::string>>(value);
                    if (opt_val.has_value()) optional_fields++;
                }
            } catch (...) {}
        }
    };
    
    Person person;
    person.person_id = 10000001;
    person.gender_concept_id = 8507;
    person.year_of_birth = 1975;
    person.race_concept_id = 8527;
    person.ethnicity_concept_id = 38003564;
    person.location_id = 10001;
    
    PersonFieldCounter counter;
    person.visit_fields(counter);
    
    // Check that required fields are present
    EXPECT_GT(counter.total_fields, 0);
    EXPECT_GT(counter.field_names.size(), 0);
    // Check that at least one optional field is counted
    EXPECT_GE(counter.optional_fields, 1);
}
