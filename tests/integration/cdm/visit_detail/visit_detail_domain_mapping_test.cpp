/**
 * @file test_visit_detail_integration.cpp
 * @brief Integration tests for VisitDetail table with UK NHS ward and department transfers
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <chrono>
#include <vector>
#include <memory>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"

using namespace omop::cdm;
using namespace std::chrono;

/**
 * @brief Test fixture for VisitDetail integration tests
 */
class VisitDetailIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Common UK dates and times
        uk_admission_start = createUKDateTime(10, 3, 2024, 14, 30);
        uk_icu_transfer = createUKDateTime(10, 3, 2024, 18, 45);
        uk_ward_transfer = createUKDateTime(12, 3, 2024, 10, 00);
        uk_discharge_time = createUKDateTime(15, 3, 2024, 11, 30);
    }

    system_clock::time_point createUKDateTime(int day, int month, int year, int hour, int minute) {
        tm timeinfo = {};
        timeinfo.tm_mday = day;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_year = year - 1900;
        timeinfo.tm_hour = hour;
        timeinfo.tm_min = minute;
        return system_clock::from_time_t(mktime(&timeinfo));
    }

    system_clock::time_point uk_admission_start;
    system_clock::time_point uk_icu_transfer;
    system_clock::time_point uk_ward_transfer;
    system_clock::time_point uk_discharge_time;
};

// Test complete patient journey through hospital departments
TEST_F(VisitDetailIntegrationTest, CompletePatientJourneyThroughDepartments) {
    // Parent visit occurrence
    int64_t visit_occurrence_id = 1000001;
    int64_t person_id = 100001;
    
    // A&E admission detail
    VisitDetail ae_detail;
    ae_detail.visit_detail_id = 1100001;
    ae_detail.person_id = person_id;
    ae_detail.visit_detail_concept_id = 8870; // Emergency Room
    ae_detail.visit_detail_start_date = uk_admission_start;
    ae_detail.visit_detail_start_datetime = uk_admission_start;
    ae_detail.visit_detail_end_date = uk_icu_transfer;
    ae_detail.visit_detail_end_datetime = uk_icu_transfer;
    ae_detail.visit_detail_type_concept_id = 44818518; // EHR derived
    ae_detail.provider_id = 50001; // A&E consultant
    ae_detail.care_site_id = 20101; // A&E department
    ae_detail.visit_detail_source_value = "A&E-2024-0310-001";
    ae_detail.admitted_from_concept_id = 8976; // Ambulance
    ae_detail.admitted_from_source_value = "999-AMBULANCE";
    ae_detail.discharged_to_concept_id = 8923; // ICU
    ae_detail.discharged_to_source_value = "ICU-TRANSFER";
    ae_detail.visit_occurrence_id = visit_occurrence_id;
    
    EXPECT_TRUE(ae_detail.validate());
    
    // ICU stay detail
    VisitDetail icu_detail;
    icu_detail.visit_detail_id = 1100002;
    icu_detail.person_id = person_id;
    icu_detail.visit_detail_concept_id = 32037; // Intensive Care
    icu_detail.visit_detail_start_date = uk_icu_transfer;
    icu_detail.visit_detail_start_datetime = uk_icu_transfer;
    icu_detail.visit_detail_end_date = uk_ward_transfer;
    icu_detail.visit_detail_end_datetime = uk_ward_transfer;
    icu_detail.visit_detail_type_concept_id = 44818518;
    icu_detail.provider_id = 50002; // ICU consultant
    icu_detail.care_site_id = 20102; // ICU
    icu_detail.visit_detail_source_value = "ICU-2024-0310-001";
    icu_detail.admitted_from_concept_id = 8870; // Emergency Room
    icu_detail.discharged_to_concept_id = 8920; // General Ward
    icu_detail.preceding_visit_detail_id = ae_detail.visit_detail_id;
    icu_detail.visit_occurrence_id = visit_occurrence_id;
    
    EXPECT_TRUE(icu_detail.validate());
    
    // General ward detail
    VisitDetail ward_detail;
    ward_detail.visit_detail_id = 1100003;
    ward_detail.person_id = person_id;
    ward_detail.visit_detail_concept_id = 8920; // General Ward
    ward_detail.visit_detail_start_date = uk_ward_transfer;
    ward_detail.visit_detail_start_datetime = uk_ward_transfer;
    ward_detail.visit_detail_end_date = uk_discharge_time;
    ward_detail.visit_detail_end_datetime = uk_discharge_time;
    ward_detail.visit_detail_type_concept_id = 44818518;
    ward_detail.provider_id = 50003; // Ward consultant
    ward_detail.care_site_id = 20103; // Medical ward
    ward_detail.visit_detail_source_value = "WARD-2024-0312-001";
    ward_detail.admitted_from_concept_id = 32037; // ICU
    ward_detail.discharged_to_concept_id = 8536; // Home
    ward_detail.discharged_to_source_value = "HOME";
    ward_detail.preceding_visit_detail_id = icu_detail.visit_detail_id;
    ward_detail.visit_occurrence_id = visit_occurrence_id;
    
    EXPECT_TRUE(ward_detail.validate());
}

// Test UK hospital department types
TEST_F(VisitDetailIntegrationTest, UKHospitalDepartmentTypes) {
    struct DepartmentTest {
        int32_t concept_id;
        std::string department_name;
        int32_t care_site_id;
    };
    
    std::vector<DepartmentTest> departments = {
        {8870, "Accident & Emergency", 20101},
        {32037, "Intensive Care Unit", 20102},
        {8920, "General Medical Ward", 20103},
        {8921, "Surgical Ward", 20104},
        {8922, "Paediatric Ward", 20105},
        {8923, "Psychiatric Ward", 20106},
        {8924, "Rehabilitation Ward", 20107},
        {581382, "Maternity Ward", 20108},
        {38004207, "Coronary Care Unit", 20109},
        {38004208, "High Dependency Unit", 20110}
    };
    
    int64_t detail_id = 2000001;
    for (const auto& dept : departments) {
        VisitDetail detail;
        detail.visit_detail_id = detail_id++;
        detail.person_id = 100002;
        detail.visit_detail_concept_id = dept.concept_id;
        detail.visit_detail_start_date = uk_admission_start;
        detail.visit_detail_end_date = uk_admission_start + hours(24);
        detail.visit_detail_type_concept_id = 44818518;
        detail.care_site_id = dept.care_site_id;
        detail.visit_detail_source_value = dept.department_name;
        detail.visit_occurrence_id = 2000000;
        
        EXPECT_TRUE(detail.validate()) << "Failed for department: " << dept.department_name;
    }
}

// Test operating theatre visits
TEST_F(VisitDetailIntegrationTest, OperatingTheatreVisits) {
    // Pre-op holding
    VisitDetail preop;
    preop.visit_detail_id = 3000001;
    preop.person_id = 100003;
    preop.visit_detail_concept_id = 8925; // Pre-operative room
    preop.visit_detail_start_date = createUKDateTime(15, 3, 2024, 7, 00);
    preop.visit_detail_start_datetime = createUKDateTime(15, 3, 2024, 7, 00);
    preop.visit_detail_end_date = createUKDateTime(15, 3, 2024, 8, 30);
    preop.visit_detail_end_datetime = createUKDateTime(15, 3, 2024, 8, 30);
    preop.visit_detail_type_concept_id = 44818518;
    preop.provider_id = 50201; // Anaesthetist
    preop.care_site_id = 20201; // Pre-op area
    preop.visit_occurrence_id = 3000000;
    
    EXPECT_TRUE(preop.validate());
    
    // Operating theatre
    VisitDetail theatre;
    theatre.visit_detail_id = 3000002;
    theatre.person_id = 100003;
    theatre.visit_detail_concept_id = 8926; // Operating room
    theatre.visit_detail_start_date = createUKDateTime(15, 3, 2024, 8, 30);
    theatre.visit_detail_start_datetime = createUKDateTime(15, 3, 2024, 8, 30);
    theatre.visit_detail_end_date = createUKDateTime(15, 3, 2024, 11, 45);
    theatre.visit_detail_end_datetime = createUKDateTime(15, 3, 2024, 11, 45);
    theatre.visit_detail_type_concept_id = 44818518;
    theatre.provider_id = 50202; // Surgeon
    theatre.care_site_id = 20202; // Theatre 3
    theatre.preceding_visit_detail_id = preop.visit_detail_id;
    theatre.visit_occurrence_id = 3000000;
    
    EXPECT_TRUE(theatre.validate());
    
    // Recovery room
    VisitDetail recovery;
    recovery.visit_detail_id = 3000003;
    recovery.person_id = 100003;
    recovery.visit_detail_concept_id = 8927; // Recovery room
    recovery.visit_detail_start_date = createUKDateTime(15, 3, 2024, 11, 45);
    recovery.visit_detail_start_datetime = createUKDateTime(15, 3, 2024, 11, 45);
    recovery.visit_detail_end_date = createUKDateTime(15, 3, 2024, 13, 30);
    recovery.visit_detail_end_datetime = createUKDateTime(15, 3, 2024, 13, 30);
    recovery.visit_detail_type_concept_id = 44818518;
    recovery.provider_id = 50203; // Recovery nurse
    recovery.care_site_id = 20203; // Recovery area
    recovery.preceding_visit_detail_id = theatre.visit_detail_id;
    recovery.visit_occurrence_id = 3000000;
    
    EXPECT_TRUE(recovery.validate());
}

// Test parent-child visit relationships
TEST_F(VisitDetailIntegrationTest, ParentChildVisitRelationships) {
    // Parent ward stay
    VisitDetail parent_ward;
    parent_ward.visit_detail_id = 4000001;
    parent_ward.person_id = 100004;
    parent_ward.visit_detail_concept_id = 8920; // General Ward
    parent_ward.visit_detail_start_date = createUKDateTime(1, 3, 2024, 8, 00);
    parent_ward.visit_detail_end_date = createUKDateTime(5, 3, 2024, 16, 00);
    parent_ward.visit_detail_type_concept_id = 44818518;
    parent_ward.care_site_id = 20301;
    parent_ward.visit_occurrence_id = 4000000;
    
    EXPECT_TRUE(parent_ward.validate());
    
    // Child visits for procedures/tests
    std::vector<std::pair<std::string, int32_t>> child_visits = {
        {"X-Ray Department", 8928},
        {"CT Scan Suite", 8929},
        {"Endoscopy Suite", 8930},
        {"Dialysis Unit", 8931}
    };
    
    int64_t child_id = 4000002;
    for (const auto& [location, concept_id] : child_visits) {
        VisitDetail child;
        child.visit_detail_id = child_id++;
        child.person_id = 100004;
        child.visit_detail_concept_id = concept_id;
        child.visit_detail_start_date = createUKDateTime(2, 3, 2024, 10, 00);
        child.visit_detail_end_date = createUKDateTime(2, 3, 2024, 11, 00);
        child.visit_detail_type_concept_id = 44818518;
        child.parent_visit_detail_id = parent_ward.visit_detail_id;
        child.visit_occurrence_id = 4000000;
        
        EXPECT_TRUE(child.validate()) << "Failed for: " << location;
    }
}

// Test validation errors
TEST_F(VisitDetailIntegrationTest, ValidationErrors) {
    VisitDetail detail;
    
    // Missing required fields
    detail.visit_detail_id = 0; // Invalid
    detail.person_id = -1; // Invalid
    detail.visit_detail_concept_id = 0; // Invalid
    detail.visit_detail_start_date = uk_discharge_time; // End before start
    detail.visit_detail_end_date = uk_admission_start;
    detail.visit_detail_type_concept_id = 0; // Invalid
    detail.visit_occurrence_id = 0; // Invalid
    
    EXPECT_FALSE(detail.validate());
    
    auto errors = detail.validation_errors();
    EXPECT_GE(errors.size(), 5);
    
    // Check specific error messages
    bool has_id_error = false;
    bool has_date_error = false;
    bool has_occurrence_error = false;
    
    for (const auto& error : errors) {
        if (error.find("visit_detail_id") != std::string::npos) has_id_error = true;
        if (error.find("start_date") != std::string::npos && 
            error.find("end_date") != std::string::npos) has_date_error = true;
        if (error.find("visit_occurrence_id") != std::string::npos) has_occurrence_error = true;
    }
    
    EXPECT_TRUE(has_id_error);
    EXPECT_TRUE(has_date_error);
    EXPECT_TRUE(has_occurrence_error);
}

// Test bulk visit detail creation
TEST_F(VisitDetailIntegrationTest, BulkVisitDetailCreation) {
    const int num_details = 5000;
    auto start_time = steady_clock::now();
    
    std::vector<std::unique_ptr<VisitDetail>> details;
    details.reserve(num_details);
    
    for (int i = 0; i < num_details; ++i) {
        auto detail = std::make_unique<VisitDetail>();
        detail->visit_detail_id = 5000000 + i;
        detail->person_id = 300000 + (i % 500); // 500 different patients
        
        // Rotate through different department types
        std::vector<int32_t> dept_concepts = {8870, 32037, 8920, 8921, 8922};
        detail->visit_detail_concept_id = dept_concepts[i % dept_concepts.size()];
        
        // Sequential dates
        detail->visit_detail_start_date = createUKDateTime(1, 1, 2024, 0, 0) + hours(i * 6);
        detail->visit_detail_end_date = detail->visit_detail_start_date + hours(4);
        detail->visit_detail_type_concept_id = 44818518;
        detail->visit_occurrence_id = 5000000 + (i / 10); // 10 details per visit
        
        EXPECT_TRUE(detail->validate());
        details.push_back(std::move(detail));
    }
    
    auto end_time = steady_clock::now();
    auto duration = duration_cast<milliseconds>(end_time - start_time);
    
    EXPECT_LT(duration.count(), 3000); // Should complete within 3 seconds
    EXPECT_EQ(details.size(), num_details);
}

// Test field visitor implementation
TEST_F(VisitDetailIntegrationTest, FieldVisitorImplementation) {
    class DetailFieldCollector : public FieldVisitor {
    public:
        std::map<std::string, std::any> collected_fields;
        
        void visit(const std::string& name, const std::any& value) override {
            collected_fields[name] = value;
        }
    };
    
    VisitDetail detail;
    detail.visit_detail_id = 6000001;
    detail.person_id = 100005;
    detail.visit_detail_concept_id = 8920; // General Ward
    detail.visit_detail_start_date = createUKDateTime(1, 4, 2024, 8, 0);
    detail.visit_detail_end_date = createUKDateTime(1, 4, 2024, 16, 0);
    detail.visit_detail_type_concept_id = 44818518;
    detail.visit_occurrence_id = 6000000;
    
    DetailFieldCollector collector;
    detail.visit_fields(collector);
    
    // Verify all required fields are collected
    EXPECT_TRUE(collector.collected_fields.find("visit_detail_id") != collector.collected_fields.end());
    EXPECT_TRUE(collector.collected_fields.find("person_id") != collector.collected_fields.end());
    EXPECT_TRUE(collector.collected_fields.find("visit_detail_concept_id") != collector.collected_fields.end());
    EXPECT_TRUE(collector.collected_fields.find("visit_detail_start_date") != collector.collected_fields.end());
    EXPECT_TRUE(collector.collected_fields.find("visit_detail_end_date") != collector.collected_fields.end());
    EXPECT_TRUE(collector.collected_fields.find("visit_detail_type_concept_id") != collector.collected_fields.end());
    EXPECT_TRUE(collector.collected_fields.find("visit_occurrence_id") != collector.collected_fields.end());
    
    // Verify field values
    EXPECT_EQ(std::any_cast<int64_t>(collector.collected_fields["visit_detail_id"]), 6000001);
    EXPECT_EQ(std::any_cast<int64_t>(collector.collected_fields["person_id"]), 100005);
    EXPECT_EQ(std::any_cast<int32_t>(collector.collected_fields["visit_detail_concept_id"]), 8920);
}
