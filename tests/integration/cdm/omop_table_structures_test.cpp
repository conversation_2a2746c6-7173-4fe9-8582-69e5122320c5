/**
 * @file test_omop_tables_integration.cpp
 * @brief Integration tests for OMOP CDM table classes with UK localization
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */
#include <gtest/gtest.h>
#include <thread>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <regex>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"
#include "common/utilities.h"
using namespace omop::cdm;
using namespace std::chrono;
/**
 * @brief Test fixture for OMOP table integration tests
 */
class OmopTablesIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set up UK locale time points
        uk_test_date_2024 = createUKDate(15, 3, 2024);  // 15/03/2024
        uk_test_date_2025 = createUKDate(1, 1, 2025);   // 01/01/2025
        uk_birth_date = createUKDate(25, 12, 1980);     // 25/12/1980
    }
    /**
     * @brief Create a UK date from day/month/year
     */
    system_clock::time_point createUKDate(int day, int month, int year) {
        tm timeinfo = {};
        timeinfo.tm_mday = day;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_year = year - 1900;
        auto time_t_val = mktime(&timeinfo);
        return system_clock::from_time_t(time_t_val);
    }

    system_clock::time_point uk_test_date_2024;
    system_clock::time_point uk_test_date_2025;
    system_clock::time_point uk_birth_date;
};
// Validates Person table with UK-specific demographic data including NHS number and UK birth date
TEST_F(OmopTablesIntegrationTest, PersonTableUKDataValidation) {
    Person person;
    
    // Set up person with UK data
    person.person_id = 100001;
    person.gender_concept_id = 8507;  // Male
    person.year_of_birth = 1980;
    person.month_of_birth = 12;
    person.day_of_birth = 25;
    person.birth_datetime = uk_birth_date;
    person.race_concept_id = 8527;     // White
    person.ethnicity_concept_id = 38003564;  // British
    person.location_id = 1001;         // London location
    person.person_source_value = "NHS-123456789";  // NHS number format
    // Validate all fields
    EXPECT_TRUE(person.validate());
    auto errors = person.validation_errors();
    EXPECT_TRUE(errors.empty());
    // Test UK date formatting
    auto uk_date_str = OmopTable::format_uk_date(person.birth_datetime.value());
    EXPECT_EQ(uk_date_str, "25/12/1980");
}
// Tests Person table SQL generation with proper escaping of special characters like apostrophes in Irish names
TEST_F(OmopTablesIntegrationTest, PersonTableSQLGenerationWithEscaping) {
    Person person;
    person.person_id = 100002;
    person.gender_concept_id = 8532;  // Female
    person.year_of_birth = 1975;
    person.race_concept_id = 8527;
    person.ethnicity_concept_id = 38003564;
    person.person_source_value = "O'Brien's NHS-987654321";  // Test SQL escaping
    
    auto sql = person.to_insert_sql(true);
    EXPECT_NE(sql.find("O''Brien''s"), std::string::npos);  // Check escaping
    EXPECT_NE(sql.find("INSERT INTO cdm.person"), std::string::npos);
}
// Verifies that Person table validation correctly identifies and reports multiple validation errors
TEST_F(OmopTablesIntegrationTest, PersonTableValidationErrors) {
    Person person;
    
    // Invalid person_id
    person.person_id = -1;
    person.gender_concept_id = 0;
    person.year_of_birth = 1800;  // Too old
    person.month_of_birth = 13;   // Invalid month
    person.day_of_birth = 32;     // Invalid day
    
    EXPECT_FALSE(person.validate());
    auto errors = person.validation_errors();
    EXPECT_GE(errors.size(), 5u);
}
// Tests ObservationPeriod table with UK date formatting (DD/MM/YYYY) and validates date ranges
TEST_F(OmopTablesIntegrationTest, ObservationPeriodUKDates) {
    ObservationPeriod period;
    period.observation_period_id = 200001;
    period.person_id = 100001;
    period.observation_period_start_date = uk_test_date_2024;
    period.observation_period_end_date = uk_test_date_2025;
    period.period_type_concept_id = 44814724;  // Period while enrolled
    
    EXPECT_TRUE(period.validate());
    
    // Test UK date formatting
    EXPECT_EQ(omop::common::UKLocalization::format_uk_date(period.observation_period_start_date), "15/03/2024");
    EXPECT_EQ(omop::common::UKLocalization::format_uk_date(period.observation_period_end_date), "01/01/2025");
}
// Validates VisitOccurrence table with NHS hospital admission data and UK healthcare patterns
TEST_F(OmopTablesIntegrationTest, VisitOccurrenceNHSHospital) {
    VisitOccurrence visit;
    visit.visit_occurrence_id = 300001;
    visit.person_id = 100001;
    visit.visit_concept_id = 9201;  // Inpatient Visit
    visit.visit_start_date = uk_test_date_2024;
    visit.visit_end_date = uk_test_date_2024 + hours(48);  // 2-day stay
    visit.visit_type_concept_id = 44818518;  // Visit derived from EHR
    visit.care_site_id = 2001;  // NHS Trust hospital
    visit.visit_source_value = "NHS-ADMISSION-2024-0315";
    
    EXPECT_TRUE(visit.validate());
    
    auto sql = visit.to_insert_sql();
    EXPECT_NE(sql.find("cdm.visit_occurrence"), std::string::npos);
}
// Tests ConditionOccurrence table using ICD-10 codes as used in UK healthcare system
TEST_F(OmopTablesIntegrationTest, ConditionOccurrenceICD10UK) {
    ConditionOccurrence condition;
    condition.condition_occurrence_id = 400001;
    condition.person_id = 100001;
    condition.condition_concept_id = 320128;  // Essential hypertension
    condition.condition_start_date = uk_test_date_2024;
    condition.condition_type_concept_id = 32817;  // EHR diagnosis
    condition.condition_source_value = "I10";  // ICD-10 code
    condition.provider_id = 5001;  // GP
    condition.visit_occurrence_id = 300001;
    
    EXPECT_TRUE(condition.validate());
    
    // Test field visitor pattern
    class TestVisitor : public FieldVisitor {
    public:
        int field_count = 0;
        void visit(const std::string& name, const std::any& value) override {
            field_count++;
        }
    };
    
    TestVisitor visitor;
    condition.visit_fields(visitor);
    EXPECT_GT(visitor.field_count, 10);
}

// Test DrugExposure with UK prescription data
TEST_F(OmopTablesIntegrationTest, DrugExposureUKPrescription) {
    DrugExposure drug;
    drug.drug_exposure_id = 500001;
    drug.person_id = 100001;
    drug.drug_concept_id = 1308216;  // Lisinopril
    drug.drug_exposure_start_date = uk_test_date_2024;
    drug.drug_exposure_end_date = uk_test_date_2024 + days(28);  // 4-week supply
    drug.drug_type_concept_id = 38000177;  // Prescription dispensed
    drug.quantity = 28.0;  // UK decimal format
    drug.days_supply = 28;
    drug.drug_source_value = "BNF-0205051";  // British National Formulary code
    drug.dose_unit_source_value = "mg";
    
    EXPECT_TRUE(drug.validate());
    
    // Validate quantity format
    EXPECT_DOUBLE_EQ(drug.quantity.value(), 28.0);
}

// Test ProcedureOccurrence with OPCS-4 codes
TEST_F(OmopTablesIntegrationTest, ProcedureOccurrenceOPCS4) {
    ProcedureOccurrence procedure;
    procedure.procedure_occurrence_id = 600001;
    procedure.person_id = 100001;
    procedure.procedure_concept_id = 4013201;  // Appendectomy
    procedure.procedure_date = uk_test_date_2024;
    procedure.procedure_type_concept_id = 38000275;  // EHR procedure
    procedure.procedure_source_value = "H01";  // OPCS-4 code
    procedure.provider_id = 5002;  // Surgeon
    procedure.visit_occurrence_id = 300001;
    
    EXPECT_TRUE(procedure.validate());
}

// Test Measurement with UK units
TEST_F(OmopTablesIntegrationTest, MeasurementUKUnits) {
    Measurement measurement;
    measurement.measurement_id = 700001;
    measurement.person_id = 100001;
    measurement.measurement_concept_id = 3004249;  // Body temperature
    measurement.measurement_date = uk_test_date_2024;
    measurement.measurement_type_concept_id = 44818702;  // Lab result
    measurement.value_as_number = 37.5;  // Celsius (UK standard)
    measurement.unit_concept_id = 8653;  // Celsius
    measurement.unit_source_value = "°C";
    measurement.range_low = 36.1;
    measurement.range_high = 37.2;
    
    EXPECT_TRUE(measurement.validate());
    
    // Test UK decimal format
    EXPECT_DOUBLE_EQ(measurement.value_as_number.value(), 37.5);
}

// Test Observation with UK-specific social data
TEST_F(OmopTablesIntegrationTest, ObservationUKSocialData) {
    Observation observation;
    observation.observation_id = 800001;
    observation.person_id = 100001;
    observation.observation_concept_id = 4058286;  // Tobacco use
    observation.observation_date = uk_test_date_2024;
    observation.observation_type_concept_id = 38000280;  // Observation from EHR
    observation.value_as_string = "Former smoker - quit 01/01/2020";  // UK date format
    observation.observation_source_value = "SMOKING-STATUS";
    
    EXPECT_TRUE(observation.validate());
    EXPECT_EQ(observation.value_as_string.value(), "Former smoker - quit 01/01/2020");
}

// Test Death record
TEST_F(OmopTablesIntegrationTest, DeathRecordValidation) {
    Death death;
    death.person_id = 100001;
    death.death_date = uk_test_date_2024;
    death.death_type_concept_id = 38003569;  // EHR record
    death.cause_concept_id = 4306655;  // Myocardial infarction
    death.cause_source_value = "I21.9";  // ICD-10
    
    EXPECT_TRUE(death.validate());
    
    // Test future date validation
    death.death_date = system_clock::now() + days(30);
    EXPECT_FALSE(death.validate());
}

// Test Note with UK medical terminology
TEST_F(OmopTablesIntegrationTest, NoteUKMedicalRecord) {
    Note note;
    note.note_id = 900001;
    note.person_id = 100001;
    note.note_date = uk_test_date_2024;
    note.note_type_concept_id = 44814637;  // Outpatient note
    note.note_class_concept_id = 4122967;  // Clinical note
    note.note_title = "GP Consultation - Hypertension Review";
    note.note_text = "Patient attended for BP review. Current readings: 145/90 mmHg. "
                     "Discussed lifestyle modifications. Next review: 15/06/2024.";
    note.encoding_concept_id = 4180186;  // UTF-8
    note.language_concept_id = 4180190;  // English
    note.provider_id = 5001;
    
    EXPECT_TRUE(note.validate());
    
    // Test SQL generation with text escaping
    auto sql = note.to_insert_sql(true);
    EXPECT_NE(sql.find("cdm.note"), std::string::npos);
}

// Test Concept table
TEST_F(OmopTablesIntegrationTest, ConceptTableValidation) {
    Concept concept_rec;
    concept_rec.concept_id = 1000001;
    concept_rec.concept_name = "Hypertension";
    concept_rec.domain_id = "Condition";
    concept_rec.vocabulary_id = "SNOMED";
    concept_rec.concept_class_id = "Clinical Finding";
    concept_rec.concept_code = "38341003";
    concept_rec.valid_start_date = createUKDate(1, 1, 1970);
    concept_rec.valid_end_date = createUKDate(31, 12, 2099);
    concept_rec.standard_concept = "S";
    
    EXPECT_TRUE(concept_rec.validate());
}

// Test Location with UK address
TEST_F(OmopTablesIntegrationTest, LocationUKAddress) {
    Location location;
    location.location_id = 1001;
    location.address_1 = "123 High Street";
    location.address_2 = "Westminster";
    location.city = "London";
    location.state = "Greater London";  // UK county
    location.zip = "SW1A 1AA";  // UK postcode
    location.county = "Greater London";
    location.country = "United Kingdom";
    location.latitude = 51.5074;
    location.longitude = -0.1278;
    
    EXPECT_TRUE(location.validate());
    
    // Test invalid UK postcode
    location.zip = "12345";  // US format
    EXPECT_FALSE(location.validate());
    auto errors = location.validation_errors();
    EXPECT_FALSE(errors.empty());
}

// Test CareSite with NHS Trust
TEST_F(OmopTablesIntegrationTest, CareSiteNHSTrust) {
    CareSite site;
    site.care_site_id = 2001;
    site.care_site_name = "University College London Hospitals NHS Trust";
    site.place_of_service_concept_id = 8756;  // Inpatient Hospital
    site.location_id = 1001;
    site.care_site_source_value = "RRV";  // NHS Trust code
    
    EXPECT_TRUE(site.validate());
}

// Test Provider with GMC number
TEST_F(OmopTablesIntegrationTest, ProviderGMCRegistration) {
    Provider provider;
    provider.provider_id = 5001;
    provider.provider_name = "Dr. Sarah Johnson";
    provider.npi = "1234567";  // GMC number format
    provider.specialty_concept_id = 38004456;  // General Practice
    provider.care_site_id = 2001;
    provider.year_of_birth = 1975;
    provider.gender_concept_id = 8532;  // Female
    provider.provider_source_value = "GMC-1234567";
    
    EXPECT_TRUE(provider.validate());
}

// Test VisitDetail
TEST_F(OmopTablesIntegrationTest, VisitDetailICUStay) {
    VisitDetail detail;
    detail.visit_detail_id = 1100001;
    detail.person_id = 100001;
    detail.visit_detail_concept_id = 32037;  // Intensive Care
    detail.visit_detail_start_date = uk_test_date_2024;
    detail.visit_detail_end_date = uk_test_date_2024 + hours(72);
    detail.visit_detail_type_concept_id = 44818518;
    detail.visit_occurrence_id = 300001;
    detail.care_site_id = 2002;  // ICU ward
    detail.visit_detail_source_value = "ICU-STAY-2024-001";
    
    EXPECT_TRUE(detail.validate());
}

// Test OmopTableFactory thread safety
TEST_F(OmopTablesIntegrationTest, FactoryThreadSafety) {
    const int num_threads = 10;
    const int operations_per_thread = 100;
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    
    auto worker = [&success_count, operations_per_thread]() {
        for (int i = 0; i < operations_per_thread; ++i) {
            auto table = OmopTableFactory::create("person");
            if (table && table->table_name() == "person") {
                success_count++;
            }
            
            // Also test is_supported
            if (OmopTableFactory::is_supported("measurement")) {
                success_count++;
            }
        }
    };
    
    // Launch threads
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(worker);
    }
    
    // Wait for completion
    for (auto& t : threads) {
        t.join();
    }
    
    EXPECT_EQ(success_count.load(), num_threads * operations_per_thread * 2);
}

// Test factory with all table types
TEST_F(OmopTablesIntegrationTest, FactoryAllTableTypes) {
    std::vector<std::string> expected_tables = {
        "person", "observation_period", "visit_occurrence", "visit_detail",
        "condition_occurrence", "drug_exposure", "procedure_occurrence",
        "measurement", "observation", "death", "note", "concept",
        "location", "care_site", "provider"
    };
    
    for (const auto& table_name : expected_tables) {
        EXPECT_TRUE(OmopTableFactory::is_supported(table_name)) 
            << "Table not supported: " << table_name;
        
        auto table = OmopTableFactory::create(table_name);
        EXPECT_NE(table, nullptr) << "Failed to create: " << table_name;
        EXPECT_EQ(table->table_name(), table_name);
    }
}

// Test custom table registration
TEST_F(OmopTablesIntegrationTest, CustomTableRegistration) {
    // Define a custom table
    class CustomTable : public OmopTable {
    public:
        std::string table_name() const override { return "custom_table"; }
        std::string to_insert_sql(bool escape_values = true) const override { return ""; }
        std::vector<std::string> field_names() const override { return {}; }
        std::vector<std::any> field_values() const override { return {}; }
        void visit_fields(FieldVisitor& visitor) const override {}
        bool validate() const override { return true; }
        std::vector<std::string> validation_errors() const override { return {}; }
    };
    
    // Register custom table
    OmopTableFactory::register_table("custom_table", 
        []() { return std::make_unique<CustomTable>(); });
    
    EXPECT_TRUE(OmopTableFactory::is_supported("custom_table"));
    
    auto table = OmopTableFactory::create("custom_table");
    EXPECT_NE(table, nullptr);
    EXPECT_EQ(table->table_name(), "custom_table");
    
    // Unregister
    OmopTableFactory::unregister_table("custom_table");
    EXPECT_FALSE(OmopTableFactory::is_supported("custom_table"));
}

// Test field value extraction
TEST_F(OmopTablesIntegrationTest, FieldValueExtraction) {
    Person person;
    person.person_id = 100001;
    person.gender_concept_id = 8507;
    person.year_of_birth = 1980;
    person.race_concept_id = 8527;
    person.ethnicity_concept_id = 38003564;
    
    auto names = person.field_names();
    auto values = person.field_values();
    
    EXPECT_EQ(names.size(), values.size());
    EXPECT_GE(names.size(), 18u);  // Person has at least 18 fields
    
    // Check first few fields
    EXPECT_EQ(names[0], "person_id");
    EXPECT_EQ(std::any_cast<int64_t>(values[0]), 100001);
}

// Test complex validation scenarios
TEST_F(OmopTablesIntegrationTest, ComplexValidationScenarios) {
    // Test leap year validation
    Person person;
    person.person_id = 100001;
    person.gender_concept_id = 8507;
    person.year_of_birth = 2000;  // Leap year
    person.month_of_birth = 2;
    person.day_of_birth = 29;  // Valid in leap year
    person.race_concept_id = 8527;
    person.ethnicity_concept_id = 38003564;
    
    EXPECT_TRUE(person.validate());
    
    // Test measurement range validation
    Measurement measurement;
    measurement.measurement_id = 700002;
    measurement.person_id = 100001;
    measurement.measurement_concept_id = 3004249;
    measurement.measurement_date = uk_test_date_2024;
    measurement.measurement_type_concept_id = 44818702;
    measurement.range_low = 100.0;
    measurement.range_high = 50.0;  // Invalid: low > high
    
    EXPECT_FALSE(measurement.validate());
    auto errors = measurement.validation_errors();
    EXPECT_FALSE(errors.empty());
}

// Test SQL injection prevention
TEST_F(OmopTablesIntegrationTest, SQLInjectionPrevention) {
    Person person;
    person.person_id = 100001;
    person.gender_concept_id = 8507;
    person.year_of_birth = 1980;
    person.race_concept_id = 8527;
    person.ethnicity_concept_id = 38003564;
    person.person_source_value = "'; DROP TABLE person; --";  // SQL injection attempt
    
    auto sql = person.to_insert_sql(true);
    
    // Check that dangerous SQL is properly escaped
    // The single quotes should be doubled to escape them
    auto expected_escaped = "''; DROP TABLE person; --";
    // The value should be inside single quotes in the SQL
    EXPECT_NE(sql.find(expected_escaped), std::string::npos);
    // The value should be surrounded by single quotes
    auto pos = sql.find(expected_escaped);
    ASSERT_NE(pos, std::string::npos);
    EXPECT_EQ(sql[pos-1], '\'');
    EXPECT_EQ(sql[pos+strlen(expected_escaped)], '\'');
}

// Test edge cases for optional fields
TEST_F(OmopTablesIntegrationTest, OptionalFieldEdgeCases) {
    DrugExposure drug;
    drug.drug_exposure_id = 500002;
    drug.person_id = 100001;
    drug.drug_concept_id = 1308216;
    drug.drug_exposure_start_date = uk_test_date_2024;
    drug.drug_exposure_end_date = uk_test_date_2025;
    drug.drug_type_concept_id = 38000177;
    
    // Leave all optional fields empty
    EXPECT_TRUE(drug.validate());
    
    auto sql = drug.to_insert_sql();
    
    // Count NULL occurrences
    size_t null_count = 0;
    size_t pos = 0;
    while ((pos = sql.find("NULL", pos)) != std::string::npos) {
        null_count++;
        pos += 4;
    }
    
    EXPECT_GT(null_count, 10u);  // Many optional fields should be NULL
}

// Test date boundary conditions
TEST_F(OmopTablesIntegrationTest, DateBoundaryConditions) {
    // Test with epoch date
    Concept concept_rec;
    concept_rec.concept_id = 1000002;
    concept_rec.concept_name = "Test Concept";
    concept_rec.domain_id = "Condition";
    concept_rec.vocabulary_id = "SNOMED";
    concept_rec.concept_class_id = "Clinical Finding";
    concept_rec.concept_code = "12345678";
    concept_rec.valid_start_date = system_clock::time_point{};  // Epoch
    concept_rec.valid_end_date = createUKDate(31, 12, 2099);
    
    EXPECT_TRUE(concept_rec.validate());
    
    // Test with far future date
    ObservationPeriod period;
    period.observation_period_id = 200002;
    period.person_id = 100001;
    period.observation_period_start_date = uk_test_date_2025;
    period.observation_period_end_date = createUKDate(31, 12, 2099);
    period.period_type_concept_id = 44814724;
    
    EXPECT_TRUE(period.validate());
}

// Test bulk operations performance
TEST_F(OmopTablesIntegrationTest, BulkOperationsPerformance) {
    const int num_records = 1000;
    auto start = steady_clock::now();
    
    std::vector<std::unique_ptr<Person>> persons;
    for (int i = 0; i < num_records; ++i) {
        auto person = std::make_unique<Person>();
        person->person_id = 100000 + i;
        person->gender_concept_id = (i % 2 == 0) ? 8507 : 8532;
        person->year_of_birth = 1950 + (i % 50);
        person->race_concept_id = 8527;
        person->ethnicity_concept_id = 38003564;
        
        EXPECT_TRUE(person->validate());
        persons.push_back(std::move(person));
    }
    
    auto end = steady_clock::now();
    auto duration = duration_cast<milliseconds>(end - start);
    
    // Should complete in reasonable time
    EXPECT_LT(duration.count(), 1000);  // Less than 1 second for 1000 records
}

// Test UK-specific currency formatting (for future extensions)
TEST_F(OmopTablesIntegrationTest, UKCurrencyFormatting) {
    // This test demonstrates UK decimal formatting with currency
    // Although OMOP doesn't have currency fields, this shows proper UK formatting
    double uk_amount = 1234.56;  // £1,234.56
    
    std::stringstream ss;
    ss << std::fixed << std::setprecision(2) << uk_amount;
    EXPECT_EQ(ss.str(), "1234.56");
}

// Test comprehensive validation result
TEST_F(OmopTablesIntegrationTest, ComprehensiveValidationResult) {
    Person person;
    // Set invalid data
    person.person_id = -1;
    person.gender_concept_id = 0;
    person.year_of_birth = 0;
    person.race_concept_id = 0;
    person.ethnicity_concept_id = 0;
    
    auto result = person.validate_detailed();
    EXPECT_FALSE(result.is_valid);
    EXPECT_FALSE(result);  // Test implicit bool conversion
    EXPECT_GE(result.errors.size(), 3u);  // person_id, gender_concept_id, year_of_birth
    
    // Check specific error messages
    bool found_person_id_error = false;
    for (const auto& error : result.errors) {
        if (error.find("person_id") != std::string::npos) {
            found_person_id_error = true;
            break;
        }
    }
    EXPECT_TRUE(found_person_id_error);
}

// Test memory management with move semantics
TEST_F(OmopTablesIntegrationTest, MoveSemantics) {
    Person person1;
    person1.person_id = 100001;
    person1.gender_concept_id = 8507;
    person1.year_of_birth = 1980;
    person1.race_concept_id = 8527;
    person1.ethnicity_concept_id = 38003564;
    person1.person_source_value = "NHS-123456789";
    
    // Move constructor
    Person person2(std::move(person1));
    EXPECT_EQ(person2.person_id, 100001);
    EXPECT_EQ(person2.person_source_value.value(), "NHS-123456789");
    
    // Move assignment
    Person person3;
    person3 = std::move(person2);
    EXPECT_EQ(person3.person_id, 100001);
    EXPECT_EQ(person3.person_source_value.value(), "NHS-123456789");
}

// Test UK temperature conversion scenarios
TEST_F(OmopTablesIntegrationTest, UKTemperatureUnits) {
    // Test body temperature in Celsius (UK standard)
    Measurement temp_celsius;
    temp_celsius.measurement_id = 700003;
    temp_celsius.person_id = 100001;
    temp_celsius.measurement_concept_id = 3004249;
    temp_celsius.measurement_date = uk_test_date_2024;
    temp_celsius.measurement_type_concept_id = 44818702;
    temp_celsius.value_as_number = 37.5;  // Normal body temp in Celsius
    temp_celsius.unit_concept_id = 8653;  // Celsius
    temp_celsius.unit_source_value = "°C";
    
    EXPECT_TRUE(temp_celsius.validate());
    
    // Test room temperature
    Measurement room_temp;
    room_temp.measurement_id = 700004;
    room_temp.person_id = 100001;
    room_temp.measurement_concept_id = 3004250;  // Room temperature
    room_temp.measurement_date = uk_test_date_2024;
    room_temp.measurement_type_concept_id = 44818702;
    room_temp.value_as_number = 21.0;  // Typical UK room temp
    room_temp.unit_concept_id = 8653;
    room_temp.unit_source_value = "°C";
    
    EXPECT_TRUE(room_temp.validate());
}

// Test comprehensive field visitor implementation
TEST_F(OmopTablesIntegrationTest, ComprehensiveFieldVisitor) {
    class DetailedVisitor : public FieldVisitor {
    public:
        std::unordered_map<std::string, std::any> fields;
        
        void visit(const std::string& name, const std::any& value) override {
            fields[name] = value;
        }
    };
    
    DrugExposure drug;
    drug.drug_exposure_id = 500003;
    drug.person_id = 100001;
    drug.drug_concept_id = 1308216;
    drug.drug_exposure_start_date = uk_test_date_2024;
    drug.drug_exposure_end_date = uk_test_date_2025;
    drug.drug_type_concept_id = 38000177;
    drug.quantity = 84.0;  // 12-week supply
    drug.days_supply = 84;
    drug.sig = "Take one tablet daily";
    drug.drug_source_value = "BNF-0205051";
    
    DetailedVisitor visitor;
    drug.visit_fields(visitor);
    
    // Verify key fields were visited
    EXPECT_EQ(visitor.fields.size(), drug.field_names().size());
    EXPECT_TRUE(visitor.fields.count("drug_exposure_id") > 0);
    EXPECT_TRUE(visitor.fields.count("quantity") > 0);
    EXPECT_TRUE(visitor.fields.count("sig") > 0);
}

// Test error handling for invalid table creation
TEST_F(OmopTablesIntegrationTest, InvalidTableCreation) {
    // Test empty table name
    auto table1 = OmopTableFactory::create("");
    EXPECT_EQ(table1, nullptr);
    
    // Test non-existent table
    auto table2 = OmopTableFactory::create("non_existent_table");
    EXPECT_EQ(table2, nullptr);
    
    // Test null creator registration
    EXPECT_THROW(
        OmopTableFactory::register_table("test", nullptr),
        std::invalid_argument
    );
    
    // Test empty name registration
    EXPECT_THROW(
        OmopTableFactory::register_table("", []() { return nullptr; }),
        std::invalid_argument
    );
}

// Test complete patient journey through multiple tables
TEST_F(OmopTablesIntegrationTest, CompletePatientJourney) {
    // Create a patient
    Person patient;
    patient.person_id = 999999;
    patient.gender_concept_id = 8532;  // Female
    patient.year_of_birth = 1965;
    patient.race_concept_id = 8527;
    patient.ethnicity_concept_id = 38003564;
    patient.location_id = 1001;
    EXPECT_TRUE(patient.validate());
    
    // Add observation period
    ObservationPeriod period;
    period.observation_period_id = 999999;
    period.person_id = patient.person_id;
    period.observation_period_start_date = createUKDate(1, 1, 2020);
    period.observation_period_end_date = uk_test_date_2025;
    period.period_type_concept_id = 44814724;
    EXPECT_TRUE(period.validate());
    
    // Add hospital visit
    VisitOccurrence visit;
    visit.visit_occurrence_id = 999999;
    visit.person_id = patient.person_id;
    visit.visit_concept_id = 9201;
    visit.visit_start_date = uk_test_date_2024;
    visit.visit_end_date = uk_test_date_2024 + days(3);
    visit.visit_type_concept_id = 44818518;
    EXPECT_TRUE(visit.validate());
    
    // Add diagnosis
    ConditionOccurrence diagnosis;
    diagnosis.condition_occurrence_id = 999999;
    diagnosis.person_id = patient.person_id;
    diagnosis.condition_concept_id = 320128;
    diagnosis.condition_start_date = uk_test_date_2024;
    diagnosis.condition_type_concept_id = 32817;
    diagnosis.visit_occurrence_id = visit.visit_occurrence_id;
    EXPECT_TRUE(diagnosis.validate());
    
    // Add prescription
    DrugExposure prescription;
    prescription.drug_exposure_id = 999999;
    prescription.person_id = patient.person_id;
    prescription.drug_concept_id = 1308216;
    prescription.drug_exposure_start_date = uk_test_date_2024;
    prescription.drug_exposure_end_date = uk_test_date_2024 + days(30);
    prescription.drug_type_concept_id = 38000177;
    prescription.visit_occurrence_id = visit.visit_occurrence_id;
    EXPECT_TRUE(prescription.validate());
    
    // Add lab result
    Measurement lab;
    lab.measurement_id = 999999;
    lab.person_id = patient.person_id;
    lab.measurement_concept_id = 3004249;
    lab.measurement_date = uk_test_date_2024;
    lab.measurement_type_concept_id = 44818702;
    lab.value_as_number = 140.0;  // Systolic BP
    lab.unit_concept_id = 8876;  // mmHg
    lab.visit_occurrence_id = visit.visit_occurrence_id;
    EXPECT_TRUE(lab.validate());
    
    // Verify all records are valid and linked
    EXPECT_EQ(period.person_id, patient.person_id);
    EXPECT_EQ(visit.person_id, patient.person_id);
    EXPECT_EQ(diagnosis.person_id, patient.person_id);
    EXPECT_EQ(prescription.person_id, patient.person_id);
    EXPECT_EQ(lab.person_id, patient.person_id);
}

// Test UK postcode validation edge cases
TEST_F(OmopTablesIntegrationTest, UKPostcodeValidationEdgeCases) {
    Location location;
    location.location_id = 1002;
    
    // Valid UK postcodes
    std::vector<std::string> valid_postcodes = {
        "SW1A 1AA",  // Westminster
        "EH1 1YZ",   // Edinburgh
        "CF10 1DD",  // Cardiff
        "BT1 1AA",   // Belfast
        "B1 1AA",    // Birmingham
        "M1 1AA",    // Manchester
        "LS1 1AA",   // Leeds
        "G1 1AA",    // Glasgow
        "L1 1AA",    // Liverpool
        "NE1 1AA"    // Newcastle
    };
    
    for (const auto& postcode : valid_postcodes) {
        location.zip = postcode;
        EXPECT_TRUE(location.validate()) << "Failed for postcode: " << postcode;
    }
    
    // Invalid postcodes
    std::vector<std::string> invalid_postcodes = {
        "12345",      // US ZIP
        "A1A 1A1",    // Canadian
        "1234 AB",    // Dutch
        "ABC DEF",    // Invalid format
        "SW1A",       // Incomplete
        "SW1A 1A",    // Too short
        "SW1A 1AAA"   // Too long
    };
    
    for (const auto& postcode : invalid_postcodes) {
        location.zip = postcode;
        EXPECT_FALSE(location.validate()) << "Should fail for postcode: " << postcode;
    }
}

// Test all tables SQL generation
TEST_F(OmopTablesIntegrationTest, AllTablesSQLGeneration) {
    auto table_names = OmopTableFactory::get_supported_tables();
    
    for (const auto& name : table_names) {
        auto table = OmopTableFactory::create(name);
        ASSERT_NE(table, nullptr) << "Failed to create table: " << name;
        
        // Generate SQL without escaping
        auto sql_no_escape = table->to_insert_sql(false);
        EXPECT_FALSE(sql_no_escape.empty());
        EXPECT_NE(sql_no_escape.find("INSERT INTO"), std::string::npos);
        EXPECT_NE(sql_no_escape.find(name), std::string::npos);
        
        // Generate SQL with escaping
        auto sql_escape = table->to_insert_sql(true);
        EXPECT_FALSE(sql_escape.empty());
    }
}

// Test schema name customization
TEST_F(OmopTablesIntegrationTest, SchemaNameCustomization) {
    Person person;
    EXPECT_EQ(person.schema_name(), "cdm");
    
    auto sql = person.to_insert_sql();
    EXPECT_NE(sql.find("cdm.person"), std::string::npos);
}
// Final comprehensive test
TEST_F(OmopTablesIntegrationTest, ComprehensiveIntegrationTest) {
    // This test ensures all components work together
    auto supported_tables = OmopTableFactory::get_supported_tables();
    EXPECT_EQ(supported_tables.size(), 15u);  // All OMOP tables
    
    // Create one of each table type and validate
    for (const auto& table_name : supported_tables) {
        auto table = OmopTableFactory::create(table_name);
        ASSERT_NE(table, nullptr);
        
        // Each table should have field names
        auto fields = table->field_names();
        EXPECT_FALSE(fields.empty());
        
        // Each table should generate SQL
        auto sql = table->to_insert_sql();
        EXPECT_FALSE(sql.empty());
        
        // Validation should work (even if it returns false for empty data)
        auto errors = table->validation_errors();
        // Errors may or may not be empty depending on required fields
    }
}
