/**
 * @file test_condition_occurrence_integration.cpp
 * @brief Integration tests for ConditionOccurrence table with UK NHS diagnosis scenarios
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <chrono>
#include <vector>
#include <memory>
#include <map>
#include "cdm/omop_tables.h"
#include "cdm/table_definitions.h"

using namespace omop::cdm;
using namespace std::chrono;

/**
 * @brief Test fixture for ConditionOccurrence integration tests
 */
class ConditionOccurrenceIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Common UK dates for testing
        uk_diagnosis_date_2024 = createUKDate(15, 3, 2024);
        uk_condition_start_2023 = createUKDate(1, 9, 2023);
        uk_condition_end_2024 = createUKDate(30, 6, 2024);
        current_date = system_clock::now();
    }

    system_clock::time_point createUKDate(int day, int month, int year) {
        tm timeinfo = {};
        timeinfo.tm_mday = day;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_year = year - 1900;
        return system_clock::from_time_t(mktime(&timeinfo));
    }

    system_clock::time_point createUKDateTime(int day, int month, int year, int hour, int minute) {
        tm timeinfo = {};
        timeinfo.tm_mday = day;
        timeinfo.tm_mon = month - 1;
        timeinfo.tm_year = year - 1900;
        timeinfo.tm_hour = hour;
        timeinfo.tm_min = minute;
        return system_clock::from_time_t(mktime(&timeinfo));
    }

    system_clock::time_point uk_diagnosis_date_2024;
    system_clock::time_point uk_condition_start_2023;
    system_clock::time_point uk_condition_end_2024;
    system_clock::time_point current_date;
};

// Test complete NHS diagnosis record
TEST_F(ConditionOccurrenceIntegrationTest, CompleteNHSDiagnosisRecord) {
    ConditionOccurrence condition;
    
    condition.condition_occurrence_id = 1000001;
    condition.person_id = 100001;
    condition.condition_concept_id = 320128; // Essential hypertension
    condition.condition_start_date = uk_diagnosis_date_2024;
    condition.condition_start_datetime = createUKDateTime(15, 3, 2024, 10, 30);
    condition.condition_type_concept_id = 32817; // EHR diagnosis
    condition.condition_status_concept_id = 4230359; // Active
    condition.provider_id = 50001; // GP
    condition.visit_occurrence_id = 300001;
    condition.condition_source_value = "I10"; // ICD-10
    condition.condition_source_concept_id = 45591524; // ICD-10 concept
    condition.condition_status_source_value = "ACTIVE";
    
    EXPECT_TRUE(condition.validate());
    
    auto sql = condition.to_insert_sql(true);
    EXPECT_NE(sql.find("INSERT INTO cdm.condition_occurrence"), std::string::npos);
    EXPECT_NE(sql.find("320128"), std::string::npos);
    EXPECT_NE(sql.find("I10"), std::string::npos);
}

// Test UK-specific ICD-10 diagnoses
TEST_F(ConditionOccurrenceIntegrationTest, UKSpecificICD10Diagnoses) {
    struct DiagnosisTest {
        int32_t concept_id;
        std::string icd10_code;
        std::string description;
        int32_t type_concept_id;
    };
    
    std::vector<DiagnosisTest> uk_diagnoses = {
        {320128, "I10", "Essential hypertension", 32817},
        {201820, "E11", "Type 2 diabetes mellitus", 32817},
        {257628, "J44", "Chronic obstructive pulmonary disease", 32817},
        {4329847, "F32", "Major depressive disorder", 32819}, // Mental health
        {316139, "K92.2", "Gastrointestinal haemorrhage", 32818}, // Emergency
        {432867, "C50", "Malignant neoplasm of breast", 32817},
        {4196141, "S72", "Fracture of femur", 32818}, // Trauma
        {380834, "N18", "Chronic kidney disease", 32817},
        {4110183, "J18", "Pneumonia", 32818}, // Acute condition
        {318736, "F41", "Anxiety disorder", 32819} // Mental health
    };
    
    int64_t condition_id = 2000001;
    for (const auto& diagnosis : uk_diagnoses) {
        ConditionOccurrence condition;
        condition.condition_occurrence_id = condition_id++;
        condition.person_id = 100002;
        condition.condition_concept_id = diagnosis.concept_id;
        condition.condition_start_date = uk_diagnosis_date_2024;
        condition.condition_type_concept_id = diagnosis.type_concept_id;
        condition.condition_source_value = diagnosis.icd10_code;
        
        EXPECT_TRUE(condition.validate()) << "Failed for diagnosis: " << diagnosis.description;
    }
}

// Test chronic condition management
TEST_F(ConditionOccurrenceIntegrationTest, ChronicConditionManagement) {
    // Diabetes with complications progression
    std::vector<ConditionOccurrence> diabetes_progression;
    
    // Initial diabetes diagnosis
    ConditionOccurrence diabetes;
    diabetes.condition_occurrence_id = 3000001;
    diabetes.person_id = 100003;
    diabetes.condition_concept_id = 201820; // Type 2 diabetes
    diabetes.condition_start_date = createUKDate(1, 1, 2020);
    diabetes.condition_type_concept_id = 32817;
    diabetes.condition_status_concept_id = 4230359; // Active
    diabetes.condition_source_value = "E11.9";
    diabetes_progression.push_back(diabetes);
    
    // Diabetic retinopathy
    ConditionOccurrence retinopathy;
    retinopathy.condition_occurrence_id = 3000002;
    retinopathy.person_id = 100003;
    retinopathy.condition_concept_id = 4174977; // Diabetic retinopathy
    retinopathy.condition_start_date = createUKDate(15, 6, 2022);
    retinopathy.condition_type_concept_id = 32817;
    retinopathy.condition_source_value = "E11.3";
    diabetes_progression.push_back(retinopathy);
    
    // Diabetic neuropathy
    ConditionOccurrence neuropathy;
    neuropathy.condition_occurrence_id = 3000003;
    neuropathy.person_id = 100003;
    neuropathy.condition_concept_id = 443767; // Diabetic neuropathy
    neuropathy.condition_start_date = createUKDate(1, 3, 2023);
    neuropathy.condition_type_concept_id = 32817;
    neuropathy.condition_source_value = "E11.4";
    diabetes_progression.push_back(neuropathy);
    
    for (const auto& condition : diabetes_progression) {
        EXPECT_TRUE(condition.validate());
    }
    
    // Verify chronological progression
    EXPECT_LT(diabetes_progression[0].condition_start_date, 
              diabetes_progression[1].condition_start_date);
    EXPECT_LT(diabetes_progression[1].condition_start_date, 
              diabetes_progression[2].condition_start_date);
}

// Test condition status concepts
TEST_F(ConditionOccurrenceIntegrationTest, ConditionStatusConcepts) {
    struct StatusTest {
        int32_t status_concept_id;
        std::string status_source;
        std::string description;
        bool has_end_date;
    };
    
    std::vector<StatusTest> statuses = {
        {4230359, "ACTIVE", "Active condition", false},
        {4127785, "RESOLVED", "Resolved condition", true},
        {4033240, "INACTIVE", "Inactive condition", true},
        {4163735, "RECURRENT", "Recurrent condition", false},
        {4195385, "REMISSION", "In remission", false},
        {45884084, "CHRONIC", "Chronic condition", false}
    };
    
    int64_t condition_id = 4000001;
    for (const auto& status : statuses) {
        ConditionOccurrence condition;
        condition.condition_occurrence_id = condition_id++;
        condition.person_id = 100004;
        condition.condition_concept_id = 320128; // Hypertension
        condition.condition_start_date = uk_condition_start_2023;
        condition.condition_type_concept_id = 32817;
        condition.condition_status_concept_id = status.status_concept_id;
        condition.condition_status_source_value = status.status_source;
        
        if (status.has_end_date) {
            condition.condition_end_date = uk_condition_end_2024;
            condition.stop_reason = "Treatment successful";
        }
        
        EXPECT_TRUE(condition.validate()) << "Failed for status: " << status.description;
    }
}

// Test mental health conditions
TEST_F(ConditionOccurrenceIntegrationTest, MentalHealthConditions) {
    struct MentalHealthTest {
        int32_t concept_id;
        std::string icd10_code;
        std::string condition_name;
        int32_t provider_type;
    };
    
    std::vector<MentalHealthTest> mental_health = {
        {4329847, "F32", "Major depressive disorder", 50301}, // Psychiatrist
        {318736, "F41", "Generalised anxiety disorder", 50302}, // Psychologist
        {436665, "F20", "Schizophrenia", 50301},
        {4102594, "F31", "Bipolar disorder", 50301},
        {440377, "F43.1", "Post-traumatic stress disorder", 50302},
        {4173420, "F90", "Attention deficit hyperactivity disorder", 50301},
        {441542, "F50", "Eating disorder", 50303}, // Specialist
        {4336230, "F60", "Personality disorder", 50301}
    };
    
    int64_t condition_id = 5000001;
    for (const auto& mh : mental_health) {
        ConditionOccurrence condition;
        condition.condition_occurrence_id = condition_id++;
        condition.person_id = 100005;
        condition.condition_concept_id = mh.concept_id;
        condition.condition_start_date = uk_diagnosis_date_2024;
        condition.condition_type_concept_id = 32819; // Mental health diagnosis
        condition.provider_id = mh.provider_type;
        condition.condition_source_value = mh.icd10_code;
        
        EXPECT_TRUE(condition.validate()) << "Failed for: " << mh.condition_name;
    }
}

// Test cancer diagnoses with staging
TEST_F(ConditionOccurrenceIntegrationTest, CancerDiagnosesWithStaging) {
    // Primary cancer diagnosis
    ConditionOccurrence primary_cancer;
    primary_cancer.condition_occurrence_id = 6000001;
    primary_cancer.person_id = 100006;
    primary_cancer.condition_concept_id = 432867; // Breast cancer
    primary_cancer.condition_start_date = uk_diagnosis_date_2024;
    primary_cancer.condition_type_concept_id = 32817;
    primary_cancer.provider_id = 50401; // Oncologist
    primary_cancer.visit_occurrence_id = 600001;
    primary_cancer.condition_source_value = "C50.9";
    
    EXPECT_TRUE(primary_cancer.validate());
    
    // Cancer staging conditions
    std::vector<std::pair<std::string, std::string>> staging = {
        {"C78.0", "Secondary malignant neoplasm of lung"},
        {"C77.2", "Secondary malignant neoplasm of lymph nodes"},
        {"Z85.3", "Personal history of malignant neoplasm of breast"}
    };
    
    int64_t condition_id = 6000002;
    for (const auto& [code, description] : staging) {
        ConditionOccurrence stage;
        stage.condition_occurrence_id = condition_id++;
        stage.person_id = 100006;
        stage.condition_concept_id = 4100000 + condition_id; // Placeholder concept
        stage.condition_start_date = uk_diagnosis_date_2024 + days(30);
        stage.condition_type_concept_id = 32817;
        stage.condition_source_value = code;
        
        EXPECT_TRUE(stage.validate()) << "Failed for: " << description;
    }
}

// Test condition date validation
TEST_F(ConditionOccurrenceIntegrationTest, ConditionDateValidation) {
    ConditionOccurrence condition;
    condition.condition_occurrence_id = 7000001;
    condition.person_id = 100007;
    condition.condition_concept_id = 320128;
    condition.condition_type_concept_id = 32817;
    
    // Test various date scenarios
    condition.condition_start_date = uk_diagnosis_date_2024;
    EXPECT_TRUE(condition.validate());
    
    // Add datetime precision
    condition.condition_start_datetime = createUKDateTime(15, 3, 2024, 14, 30);
    EXPECT_TRUE(condition.validate());
    
    // Add end date after start
    condition.condition_end_date = uk_diagnosis_date_2024 + days(90);
    condition.condition_end_datetime = createUKDateTime(13, 6, 2024, 10, 00);
    EXPECT_TRUE(condition.validate());
    
    // Test with stop reason
    condition.stop_reason = "Resolved with treatment";
    EXPECT_TRUE(condition.validate());
}

// Test bulk condition creation performance
TEST_F(ConditionOccurrenceIntegrationTest, BulkConditionCreationPerformance) {
    const int num_conditions = 10000;
    auto start_time = steady_clock::now();
    
    std::vector<std::unique_ptr<ConditionOccurrence>> conditions;
    conditions.reserve(num_conditions);
    
    // Common UK conditions for bulk testing
    std::vector<std::pair<int32_t, std::string>> common_conditions = {
        {320128, "I10"},    // Hypertension
        {201820, "E11"},    // Type 2 diabetes
        {257628, "J44"},    // COPD
        {316866, "I25"},    // Ischemic heart disease
        {321588, "I48"},    // Atrial fibrillation
        {378253, "E78"},    // Hyperlipidemia
        {4329847, "F32"},   // Depression
        {255573, "J45"},    // Asthma
        {80809, "G47"},     // Sleep disorders
        {4209145, "M79"}    // Fibromyalgia
    };
    
    for (int i = 0; i < num_conditions; ++i) {
        auto condition = std::make_unique<ConditionOccurrence>();
        condition->condition_occurrence_id = 8000000 + i;
        condition->person_id = 200000 + (i % 1000); // 1000 different patients
        
        auto& [concept_id, icd_code] = common_conditions[i % common_conditions.size()];
        condition->condition_concept_id = concept_id;
        condition->condition_source_value = icd_code;
        
        // Vary start dates across 5 years
        int days_offset = i % (365 * 5);
        condition->condition_start_date = createUKDate(1, 1, 2020) + days(days_offset);
        condition->condition_type_concept_id = 32817;
        
        EXPECT_TRUE(condition->validate());
        conditions.push_back(std::move(condition));
    }
    
    auto end_time = steady_clock::now();
    auto duration = duration_cast<milliseconds>(end_time - start_time);
    
    EXPECT_LT(duration.count(), 5000); // Should complete within 5 seconds
    EXPECT_EQ(conditions.size(), num_conditions);
}
