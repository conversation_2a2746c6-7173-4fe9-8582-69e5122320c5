# CDM integration tests
set(CDM_INTEGRATION_TEST_SOURCES
    cdm_schema_creation_test.cpp
    cdm_database_operations_test.cpp
    cdm_comprehensive_mapping_test.cpp
    omop_table_structures_test.cpp
    table_definition_validation_test.cpp
    # Individual table integration tests with full ETL functionality
    condition_occurrence/condition_occurrence_domain_test.cpp
    observation_period/observation_period_domain_test.cpp
    person/person_domain_mapping_test.cpp
    visit_detail/visit_detail_domain_mapping_test.cpp
    visit_occurrence/visit_occurrence_domain_test.cpp
    death/death_domain_mapping_test.cpp
    drug_exposure/drug_exposure_domain_test.cpp
    measurement/measurement_domain_mapping_test.cpp
    note/note_domain_mapping_test.cpp
    observation/observation_domain_mapping_test.cpp
    procedure_occurrence/procedure_occurrence_domain_test.cpp
)

add_executable(cdm_integration_tests ${CDM_INTEGRATION_TEST_SOURCES})

target_link_libraries(cdm_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
#        omop_service
        integration_test_helpers
        gtest
        gtest_main
        gmock
        gmock_main
)

target_include_directories(cdm_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME cdm_integration_tests
    COMMAND cdm_integration_tests
)

set_tests_properties(cdm_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;cdm"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)