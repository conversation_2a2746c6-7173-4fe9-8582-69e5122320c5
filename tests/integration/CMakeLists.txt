# Integration Tests Root CMakeLists.txt

# Enable testing
enable_testing()

# Set test data directory
set(TEST_DATA_DIR "${CMAKE_CURRENT_SOURCE_DIR}/test_data")
set(TEST_OUTPUT_DIR "${CMAKE_CURRENT_BINARY_DIR}/test_output")

# Add compiler definitions for test directories
add_compile_definitions(
    TEST_DATA_DIR="${TEST_DATA_DIR}"
    TEST_OUTPUT_DIR="${TEST_OUTPUT_DIR}"
)

# Define all required test configuration files for integration tests
set(INTEGRATION_TEST_CONFIG_FILES
    test_config.yaml
    slow_config.yaml
    pauseable_config.yaml
    failing_config.yaml
    quick_config.yaml
    checkpoint_config.yaml
    priority_test_config.yaml
    stats_config.yaml
    advanced_mapping_config.yaml
    mapping_config.yaml
    priority_config.yaml
)

# Create custom command to set up integration test environment
add_custom_command(
    OUTPUT ${CMAKE_CURRENT_BINARY_DIR}/integration_test_setup.stamp
    COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_CURRENT_BINARY_DIR}
    COMMAND ${CMAKE_COMMAND} -E make_directory ${TEST_OUTPUT_DIR}
    COMMENT "Setting up integration test environment"
)

# Copy all required configuration files to build directory
foreach(CONFIG_FILE ${INTEGRATION_TEST_CONFIG_FILES})
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test_data/yaml/${CONFIG_FILE})
        add_custom_command(
            OUTPUT ${CMAKE_CURRENT_BINARY_DIR}/integration_test_setup.stamp
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
                ${CMAKE_CURRENT_SOURCE_DIR}/test_data/yaml/${CONFIG_FILE}
                ${CMAKE_CURRENT_BINARY_DIR}/${CONFIG_FILE}
            DEPENDS ${CMAKE_CURRENT_SOURCE_DIR}/test_data/yaml/${CONFIG_FILE}
            APPEND
        )
    endif()
endforeach()

# Create a timestamp file to track setup completion
add_custom_command(
    OUTPUT ${CMAKE_CURRENT_BINARY_DIR}/integration_test_setup.stamp
    COMMAND ${CMAKE_COMMAND} -E touch ${CMAKE_CURRENT_BINARY_DIR}/integration_test_setup.stamp
    APPEND
)

# Create a custom target for integration test setup
add_custom_target(setup_integration_tests 
    DEPENDS ${CMAKE_CURRENT_BINARY_DIR}/integration_test_setup.stamp
    COMMENT "Setting up integration test configuration files and artifacts"
)

# Common test utilities library
add_library(integration_test_helpers STATIC
    test_helpers/database_fixture.cpp
    test_helpers/database_connection_factory.cpp
    test_helpers/test_data_generator.cpp
    test_helpers/mock_loader_utilities.cpp
)

# Make test helpers depend on integration test setup
add_dependencies(integration_test_helpers setup_integration_tests)

# Set preprocessor definitions for test dependencies
if(ENABLE_TEST_DEPENDENCIES)
    target_compile_definitions(integration_test_helpers PRIVATE OMOP_ENABLE_TEST_DEPENDENCIES)
endif()

if(ENABLE_MOCK_MODE)
    target_compile_definitions(integration_test_helpers PRIVATE OMOP_ENABLE_MOCK_MODE)
endif()

target_include_directories(integration_test_helpers
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/test_helpers
        ${CMAKE_CURRENT_BINARY_DIR}
        ${CMAKE_SOURCE_DIR}/src/lib
    PRIVATE
        ${CMAKE_BINARY_DIR}/_deps/spdlog-src/include
        ${CMAKE_BINARY_DIR}/_deps/nlohmann_json-src/include
        ${CMAKE_BINARY_DIR}/_deps/fmt-src/include
)

target_link_libraries(integration_test_helpers
    PUBLIC
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
#        omop_service
        gtest
        gtest_main
        gmock
        gmock_main
        Threads::Threads
        spdlog::spdlog
        nlohmann_json::nlohmann_json
        fmt::fmt
)

# Function to add integration test executable
function(add_integration_test test_name)
    add_executable(${test_name} ${ARGN})
    
    # Make all integration tests depend on the setup target
    add_dependencies(${test_name} setup_integration_tests)
    
    target_include_directories(${test_name}
        PRIVATE
            ${CMAKE_CURRENT_BINARY_DIR}
            ${CMAKE_SOURCE_DIR}/src/lib
    )
    target_link_libraries(${test_name}
        PRIVATE
            integration_test_helpers
            omop_common
            omop_core
            omop_cdm
            omop_extract
            omop_transform
            omop_load
            gtest
            gtest_main
            gmock
            gmock_main
    )
    
    # Run tests from the integration test build directory where config files are available
    add_test(NAME ${test_name} 
             COMMAND ${test_name}
             WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR})

    # Set test properties
    set_tests_properties(${test_name} PROPERTIES
        LABELS "integration"
        TIMEOUT 300  # 5 minutes timeout
        ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR};TEST_OUTPUT_DIR=${TEST_OUTPUT_DIR}"
    )
endfunction()

# Add subdirectories for module-specific tests
add_subdirectory(cdm)
add_subdirectory(common)
add_subdirectory(core)
add_subdirectory(extract)
add_subdirectory(transform)
add_subdirectory(load)
add_subdirectory(api)
add_subdirectory(config)
add_subdirectory(security)
add_subdirectory(e2e)
add_subdirectory(monitoring)
add_subdirectory(performance)
add_subdirectory(quality)
add_subdirectory(workflow)

# Copy test data to build directory if it exists
if(EXISTS ${TEST_DATA_DIR})
    file(COPY ${TEST_DATA_DIR} DESTINATION ${CMAKE_CURRENT_BINARY_DIR})
endif()