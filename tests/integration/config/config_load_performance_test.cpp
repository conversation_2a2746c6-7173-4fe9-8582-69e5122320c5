// tests/integration/config/config_load_performance_test.cpp
// Tests configuration loading performance under various load conditions
#include <gtest/gtest.h>
#include <filesystem>
#include <fstream>
#include "common/configuration.h"

namespace omop::config::test {

class LoadPerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<common::ConfigurationManager>();
        config_dir_ = "/tmp/omop_config_perf_test";
        std::filesystem::create_directories(config_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(config_dir_);
    }

    void createConfigFile(const std::string& filename, const std::string& content) {
        std::ofstream file(config_dir_ + "/" + filename);
        file << content;
        file.close();
    }

    std::string config_dir_;
    std::shared_ptr<common::ConfigurationManager> config_;
};

// Tests configuration loading performance with large files
TEST_F(LoadPerformanceTest, LargeConfigurationFilePerformance) {
    // Generate a large configuration file
    std::string large_config = R"(
        version: 2.0
        source_database:
            type: postgresql
            host: localhost
            port: 5432
            database: perf_test_db
            username: test_user
            password: test_pass
            pool_size: 50

        target_database:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm_perf
            username: test_user
            password: test_pass
            pool_size: 50

        etl_settings:
            batch_size: 5000
            max_parallel_jobs: 16
            queue_size: 100000
            memory_limit_mb: 4096

        performance:
            enable_profiling: true
            metrics_interval_seconds: 1
            detailed_timing: true

        table_mappings:
            patients:
                source_table: patients
                target_table: person
                transformations:
                    - source_column: patient_id
                      target_column: person_id
                      type: direct

        tables:
    )";

    // Add many table mappings to simulate large configuration
    for (int i = 0; i < 1000; ++i) {
        large_config += R"(
            - source_table: table_)" + std::to_string(i) + R"(
              target_table: omop_table_)" + std::to_string(i) + R"(
              transformations:
                - source_column: id
                  target_column: id
                  type: direct
                - source_column: name
                  target_column: name
                  type: direct
        )";
    }

    createConfigFile("large_config.yaml", large_config);

    // Measure loading time
    auto start = std::chrono::high_resolution_clock::now();
    config_->load_config(config_dir_ + "/large_config.yaml");
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    // Configuration loading should complete within reasonable time
    EXPECT_LT(duration.count(), 5000) << "Large configuration took too long to load: " << duration.count() << "ms";
    
    // Verify configuration was loaded correctly
    EXPECT_EQ(config_->get_value_or<int>("etl_settings.batch_size", 0), 5000);
    EXPECT_EQ(config_->get_all_mappings().size(), 1000);
}

// Tests configuration validation performance
TEST_F(LoadPerformanceTest, ConfigurationValidationPerformance) {
    std::string complex_config = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: source_db
            username: user
            password: pass

        target_db:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm
            username: user
            password: pass

        tables:
    )";

    // Add complex table mappings with many transformations
    for (int i = 0; i < 100; ++i) {
        complex_config += R"(
            - source_table: complex_table_)" + std::to_string(i) + R"(
              target_table: omop_complex_)" + std::to_string(i) + R"(
              transformations:
        )";
        
        for (int j = 0; j < 20; ++j) {
            complex_config += R"(
                - source_column: col_)" + std::to_string(j) + R"(
                  target_column: target_col_)" + std::to_string(j) + R"(
                  type: )" + (j % 3 == 0 ? "direct" : (j % 3 == 1 ? "date_transform" : "vocabulary_mapping")) + R"(
            )";
        }
    }

    // Add vocabulary mappings
    complex_config += R"(
        vocabulary_mappings:
    )";
    
    for (int i = 0; i < 50; ++i) {
        complex_config += R"(
            vocab_)" + std::to_string(i) + R"(:
                type: lookup_table
                source: vocab_table_)" + std::to_string(i) + R"(
                key_field: key
                value_field: value
        )";
    }

    createConfigFile("complex_config.yaml", complex_config);
    config_->load_config(config_dir_ + "/complex_config.yaml");

    // Measure validation time
    auto start = std::chrono::high_resolution_clock::now();
    config_->validate_config();
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    // Validation should complete within reasonable time
    EXPECT_LT(duration.count(), 2000) << "Complex configuration validation took too long: " << duration.count() << "ms";
}

// Tests concurrent configuration access performance
TEST_F(LoadPerformanceTest, ConcurrentConfigurationAccess) {
    std::string config_content = R"(
        version: 2.0
        source_database:
            type: postgresql
            host: localhost
            port: 5432
            database: source_db
            username: test_user
            password: test_pass

        target_database:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm
            username: test_user
            password: test_pass

        etl_settings:
            batch_size: 1000
            max_parallel_jobs: 4
            error_threshold: 0.05

        table_mappings:
            patients:
                source_table: patients
                target_table: person
                transformations:
                    - source_column: patient_id
                      target_column: person_id
                      type: direct
    )";

    createConfigFile("concurrent_config.yaml", config_content);
    config_->load_config(config_dir_ + "/concurrent_config.yaml");

    const int num_threads = 10;
    const int operations_per_thread = 1000;
    std::vector<std::thread> threads;
    std::atomic<int> successful_operations{0};

    auto start = std::chrono::high_resolution_clock::now();

    // Launch concurrent threads accessing configuration
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([&, t]() {
            for (int i = 0; i < operations_per_thread; ++i) {
                try {
                    // Mix of different access patterns
                    auto batch_size = config_->get_value_or<int>("etl_settings.batch_size", 0);
                    auto db_host = config_->get_value_or<std::string>("source_database.host", "");
                    auto version = config_->get_version();
                    
                    if (batch_size == 1000 && db_host == "localhost" && !version.empty()) {
                        successful_operations.fetch_add(1);
                    }
                } catch (const std::exception&) {
                    // Ignore errors for this performance test
                }
            }
        });
    }

    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

    // All operations should succeed
    EXPECT_EQ(successful_operations.load(), num_threads * operations_per_thread);
    
    // Concurrent access should be reasonably fast
    EXPECT_LT(duration.count(), 5000) << "Concurrent access took too long: " << duration.count() << "ms";
    
    double ops_per_second = (num_threads * operations_per_thread * 1000.0) / duration.count();
    EXPECT_GT(ops_per_second, 10000) << "Configuration access rate too low: " << ops_per_second << " ops/sec";
}

// Tests UK-specific configuration performance with NHS data processing
TEST_F(LoadPerformanceTest, UKConfigurationPerformance) {
    std::string uk_config = R"(
        version: 2.0
        environment: production
        locale: en_GB
        timezone: Europe/London
        
        source_database:
            type: postgresql
            host: nhs-cluster.local
            port: 5432
            database: nhs_clinical_data
            username: test_user
            password: test_pass
            pool_size: 100
            ssl_mode: require

        target_database:
            type: postgresql
            host: omop-cluster.local
            port: 5432
            database: omop_cdm_uk
            username: test_user
            password: test_pass
            pool_size: 200
            ssl_mode: require

        etl_settings:
            batch_size: 10000
            max_parallel_jobs: 32
            queue_size: 500000
            memory_limit_mb: 8192
            error_threshold: 0.001

        uk_specific:
            nhs_data_processing: true
            nhs_number_validation: true
            postcode_standardization: true
            uk_date_handling: true
            uk_currency_processing: true
            
        table_mappings:
            patients:
                source_table: patients
                target_table: person
                transformations:
                    - source_column: patient_id
                      target_column: person_id
                      type: direct
                    - source_column: nhs_number
                      target_column: nhs_number
                      type: custom
            
        nhs_trusts:
    )";

    // Add NHS trust configurations
    for (int i = 0; i < 200; ++i) {
        uk_config += R"(
            trust_)" + std::to_string(i) + R"(:
                name: "NHS Trust )" + std::to_string(i) + R"("
                code: "TRUST)" + std::to_string(i) + R"("
                region: "Region )" + std::to_string(i % 10) + R"("
                postcodes:
                    - "SW1A 1AA"
                    - "M1 1AA"
                    - "B33 8TH"
                data_sources:
                    - gp_systems
                    - hospital_systems
                    - community_services
        )";
    }

    uk_config += R"(
        
        gp_systems:
    )";

    // Add GP system configurations
    for (int i = 0; i < 100; ++i) {
        uk_config += R"(
            gp_system_)" + std::to_string(i) + R"(:
                name: "GP System )" + std::to_string(i) + R"("
                vendor: "Vendor )" + std::to_string(i % 5) + R"("
                version: ")" + std::to_string(2020 + (i % 4)) + R"("
                api_endpoints:
                    - "https://api.gp)" + std::to_string(i) + R"(.nhs.uk/patient-data"
                    - "https://api.gp)" + std::to_string(i) + R"(.nhs.uk/prescriptions"
                authentication:
                    type: oauth2
                    client_id: "gp_client_)" + std::to_string(i) + R"("
                    scope: "patient_data prescriptions"
        )";
    }

    createConfigFile("uk_performance_config.yaml", uk_config);

    // Measure UK configuration loading performance
    auto start = std::chrono::high_resolution_clock::now();
    config_->load_config(config_dir_ + "/uk_performance_config.yaml");
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    // UK configuration should load within reasonable time despite complexity
    EXPECT_LT(duration.count(), 10000) << "UK configuration took too long to load: " << duration.count() << "ms";
    
    // Verify UK-specific settings were loaded
    EXPECT_EQ(config_->get_value_or<std::string>("locale", ""), "en_GB");
    EXPECT_EQ(config_->get_value_or<std::string>("timezone", ""), "Europe/London");
    EXPECT_TRUE(config_->get_value_or<bool>("uk_specific.nhs_data_processing", false));
    EXPECT_TRUE(config_->get_value_or<bool>("uk_specific.nhs_number_validation", false));
    EXPECT_TRUE(config_->get_value_or<bool>("uk_specific.postcode_standardization", false));
    
    // Verify NHS trust configurations
    EXPECT_EQ(config_->get_value_or<std::string>("nhs_trusts.trust_0.name", ""), "NHS Trust 0");
    EXPECT_EQ(config_->get_value_or<std::string>("nhs_trusts.trust_199.name", ""), "NHS Trust 199");
    
    // Verify GP system configurations
    EXPECT_EQ(config_->get_value_or<std::string>("gp_systems.gp_system_0.name", ""), "GP System 0");
    EXPECT_EQ(config_->get_value_or<std::string>("gp_systems.gp_system_99.name", ""), "GP System 99");
}

// Tests UK configuration validation performance with NHS compliance rules
TEST_F(LoadPerformanceTest, UKConfigurationValidationPerformance) {
    std::string uk_validation_config = R"(
        version: 2.0
        environment: production
        locale: en_GB
        
        nhs_compliance:
            enabled: true
            gdpr_compliance: true
            data_retention_policy: 8_years
            patient_consent_tracking: true
            audit_trail: mandatory
            
        data_quality_rules:
            nhs_number_validation:
                enabled: true
                validation_rules:
                    - check_digit_validation
                    - format_validation
                    - uniqueness_check
                    - nhs_database_verification
                error_threshold: 0.001
                
            postcode_validation:
                enabled: true
                validation_rules:
                    - format_validation
                    - real_postcode_check
                    - nhs_trust_mapping
                    - geographic_validation
                error_threshold: 0.01
                
            uk_date_validation:
                enabled: true
                input_formats:
                    - "dd/MM/yyyy"
                    - "dd-MM-yyyy"
                    - "dd.MM.yyyy"
                    - "yyyy-MM-dd"
                output_format: "yyyy-MM-dd"
                validation_rules:
                    - future_date_check
                    - reasonable_date_range
                    - nhs_operational_period
                    
        nhs_workflows:
    )";

    // Add NHS workflow configurations
    for (int i = 0; i < 50; ++i) {
        uk_validation_config += R"(
            workflow_)" + std::to_string(i) + R"(:
                name: "NHS Workflow )" + std::to_string(i) + R"("
                enabled: true
                schedule: "0 )" + std::to_string(i % 24) + R"( * * *"
                priority: )" + ((i % 3 == 0) ? "high" : (i % 3 == 1) ? "normal" : "low") + R"(
                nhs_compliance_required: true
                data_quality_checks:
                    - nhs_number_validation
                    - postcode_validation
                    - uk_date_validation
                error_handling:
                    max_retry_attempts: 3
                    retry_delay_seconds: 60
                    escalation_threshold: 5
        )";
    }

    createConfigFile("uk_validation_config.yaml", uk_validation_config);
    config_->load_config(config_dir_ + "/uk_validation_config.yaml");

    // Measure UK configuration validation performance
    auto start = std::chrono::high_resolution_clock::now();
    config_->validate_config();
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    // UK configuration validation should complete within reasonable time
    EXPECT_LT(duration.count(), 5000) << "UK configuration validation took too long: " << duration.count() << "ms";
    
    // Verify NHS compliance settings
    EXPECT_TRUE(config_->get_value_or<bool>("nhs_compliance.enabled", false));
    EXPECT_TRUE(config_->get_value_or<bool>("nhs_compliance.gdpr_compliance", false));
    EXPECT_EQ(config_->get_value_or<std::string>("nhs_compliance.data_retention_policy", ""), "8_years");
    EXPECT_TRUE(config_->get_value_or<bool>("nhs_compliance.patient_consent_tracking", false));
    
    // Verify data quality rules
    EXPECT_TRUE(config_->get_value_or<bool>("data_quality_rules.nhs_number_validation.enabled", false));
    EXPECT_EQ(config_->get_value_or<double>("data_quality_rules.nhs_number_validation.error_threshold", 0.0), 0.001);
    
    EXPECT_TRUE(config_->get_value_or<bool>("data_quality_rules.postcode_validation.enabled", false));
    EXPECT_EQ(config_->get_value_or<double>("data_quality_rules.postcode_validation.error_threshold", 0.0), 0.01);
    
    EXPECT_TRUE(config_->get_value_or<bool>("data_quality_rules.uk_date_validation.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("data_quality_rules.uk_date_validation.output_format", ""), "yyyy-MM-dd");
    
    // Verify NHS workflows
    EXPECT_EQ(config_->get_value_or<std::string>("nhs_workflows.workflow_0.name", ""), "NHS Workflow 0");
    EXPECT_EQ(config_->get_value_or<std::string>("nhs_workflows.workflow_49.name", ""), "NHS Workflow 49");
    EXPECT_TRUE(config_->get_value_or<bool>("nhs_workflows.workflow_0.nhs_compliance_required", false));
}

} // namespace omop::config::test