# Configuration integration tests
set(CONFIG_INTEGRATION_TEST_SOURCES
    dynamic_config_reload_test.cpp
    config_version_migration_test.cpp
    config_load_performance_test.cpp
    data_quality_config_test.cpp
    job_dependency_workflow_test.cpp
)

add_executable(config_integration_tests ${CONFIG_INTEGRATION_TEST_SOURCES})

target_link_libraries(config_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
#        omop_service
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(config_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

# Set C++20 standard
target_compile_features(config_integration_tests PRIVATE cxx_std_20)

# Add test data directory
target_compile_definitions(config_integration_tests
    PRIVATE
        TEST_DATA_DIR="${CMAKE_SOURCE_DIR}/tests/integration/test_data"
)

add_test(
    NAME config_integration_tests
    COMMAND config_integration_tests
)

set_tests_properties(config_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;config"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)