// tests/integration/config/config_version_migration_test.cpp
// Tests configuration version migration and backward compatibility
#include <gtest/gtest.h>
#include <filesystem>
#include <fstream>
#include "common/configuration.h"

namespace omop::config::test {

class ConfigurationMigrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        migrator_ = std::make_unique<common::ConfigurationMigrator>();
        config_dir_ = "/tmp/omop_config_migration";
        std::filesystem::create_directories(config_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(config_dir_);
    }

    void createConfigFile(const std::string& filename, const std::string& content) {
        std::ofstream file(config_dir_ + "/" + filename);
        file << content;
        file.close();
    }

    std::unique_ptr<common::ConfigurationMigrator> migrator_;
    std::string config_dir_;
};

// Tests migration from v1 to v2 configuration format
TEST_F(ConfigurationMigrationTest, MigrateV1ToV2) {
    // Create v1 configuration
    std::string v1_config = R"(
        version: 1.0
        source_database:
            type: postgresql
            connection_string: postgresql://user:pass@localhost/source_db
            username: user
            password: pass
        target_database:
            type: postgresql
            connection_string: postgresql://user:pass@localhost/omop_cdm
            username: user
            password: pass

        etl_config:
            batch_size: 1000
            threads: 4
    )";

    createConfigFile("config_v1.yaml", v1_config);

    // Migrate to v2
    auto migrated_config = migrator_->migrate(config_dir_ + "/config_v1.yaml", "2.0");

    // Verify migration
    EXPECT_EQ(migrated_config->get_value_or<std::string>("version", ""), "2.0");

    // Check structure changes
    EXPECT_TRUE(migrated_config->get_value("source_db.type").has_value());
    EXPECT_TRUE(migrated_config->get_value("target_db.type").has_value());
    EXPECT_FALSE(migrated_config->get_value("database").has_value());

    // Check renamed fields
    EXPECT_EQ(migrated_config->get_value_or<int>("etl.batch_size", 0), 1000);
    EXPECT_EQ(migrated_config->get_value_or<int>("etl.max_parallel_jobs", 0), 4);
}

// Tests backward compatibility
TEST_F(ConfigurationMigrationTest, BackwardCompatibility) {
    // Create modern v2 configuration
    std::string v2_config = R"(
        version: 2.0
        source_database:
            type: postgresql
            host: localhost
            port: 5432
            database: source_db
            username: user
            password: pass

        target_database:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm
            username: user
            password: pass

        etl_settings:
            batch_size: 2000
            max_parallel_jobs: 8
    )";

    createConfigFile("config_v2.yaml", v2_config);

    // Test compatibility layer for v1 API
    auto config = std::make_shared<common::ConfigurationManager>();
    config->load_config(config_dir_ + "/config_v2.yaml");

    // Enable compatibility mode
    config->enable_compatibility_mode("1.0");

    // Access using new v2 paths should still work
    auto batch_size = config->get_value_or<int>("etl.batch_size", 0);
    EXPECT_EQ(batch_size, 2000);
    
    auto max_jobs = config->get_value_or<int>("etl.max_parallel_jobs", 0);
    EXPECT_EQ(max_jobs, 8);
}

// Tests configuration version detection
TEST_F(ConfigurationMigrationTest, ConfigurationVersionDetection) {
    // Test v1 configuration without explicit version
    std::string v1_implicit = R"(
        database:
            source:
                connection_string: postgresql://user:pass@localhost/db
    )";
    
    createConfigFile("implicit_v1.yaml", v1_implicit);
    
    // Should default to v1.0
    EXPECT_EQ(common::ConfigurationMigrator::get_config_version(config_dir_ + "/implicit_v1.yaml"), "1.0");
    EXPECT_TRUE(common::ConfigurationMigrator::needs_migration(config_dir_ + "/implicit_v1.yaml", "2.0"));

    // Test v2 configuration with explicit version
    std::string v2_explicit = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: localhost
    )";
    
    createConfigFile("explicit_v2.yaml", v2_explicit);
    
    EXPECT_EQ(common::ConfigurationMigrator::get_config_version(config_dir_ + "/explicit_v2.yaml"), "2.0");
    EXPECT_FALSE(common::ConfigurationMigrator::needs_migration(config_dir_ + "/explicit_v2.yaml", "2.0"));
}

// Tests database connection string parsing during migration
TEST_F(ConfigurationMigrationTest, DatabaseConnectionStringParsing) {
    std::string v1_with_complex_conn = R"(
        version: 1.0
        database:
            source:
                connection_string: ******************************************/testdb
            target:
                connection_string: **************************************************/targetdb
    )";

    createConfigFile("complex_conn.yaml", v1_with_complex_conn);

    auto migrated = migrator_->migrate(config_dir_ + "/complex_conn.yaml", "2.0");

    // Verify source database parsing
    EXPECT_EQ(migrated->get_value_or<std::string>("source_db.host", ""), "dbhost");
    EXPECT_EQ(migrated->get_value_or<int>("source_db.port", 0), 5433);
    EXPECT_EQ(migrated->get_value_or<std::string>("source_db.database", ""), "testdb");

    // Verify target database parsing  
    EXPECT_EQ(migrated->get_value_or<std::string>("target_db.host", ""), "targethost");
    EXPECT_EQ(migrated->get_value_or<int>("target_db.port", 0), 5434);
    EXPECT_EQ(migrated->get_value_or<std::string>("target_db.database", ""), "targetdb");
}

// Tests migration error handling
TEST_F(ConfigurationMigrationTest, MigrationErrorHandling) {
    // Test with non-existent file
    EXPECT_THROW(
        migrator_->migrate("/non/existent/file.yaml", "2.0"),
        std::exception
    );

    // Test with invalid YAML
    createConfigFile("invalid.yaml", "invalid: yaml: content: [");
    EXPECT_THROW(
        migrator_->migrate(config_dir_ + "/invalid.yaml", "2.0"),
        std::exception
    );
}

// Tests no-op migration when versions match
TEST_F(ConfigurationMigrationTest, NoOpMigrationWhenVersionsMatch) {
    std::string v2_config = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: test_db
    )";

    createConfigFile("v2_config.yaml", v2_config);

    // Migration from v2.0 to v2.0 should be a no-op
    auto result = migrator_->migrate(config_dir_ + "/v2_config.yaml", "2.0");
    
    // Verify original structure is preserved
    EXPECT_EQ(result->get_value_or<std::string>("version", ""), "2.0");
    EXPECT_EQ(result->get_value_or<std::string>("source_db.host", ""), "localhost");
    EXPECT_EQ(result->get_value_or<int>("source_db.port", 0), 5432);
}

// Tests UK-specific configuration migration
TEST_F(ConfigurationMigrationTest, UKSpecificConfigurationMigration) {
    std::string v1_uk_config = R"(
        version: 1.0
        locale: en_GB
        database:
            source:
                connection_string: postgresql://nhs_user:<EMAIL>/gp_data
            target:
                connection_string: postgresql://omop_user:<EMAIL>/omop_cdm_uk

        etl_config:
            batch_size: 2000
            threads: 8
            nhs_number_validation: true
            postcode_standardization: true
            
        uk_transformations:
            date_format: "dd/MM/yyyy"
            postcode_format: "SW1A 1AA"
            currency_symbol: "£"
    )";

    createConfigFile("uk_v1_config.yaml", v1_uk_config);

    auto migrated = migrator_->migrate(config_dir_ + "/uk_v1_config.yaml", "2.0");

    // Verify UK-specific settings are preserved
    EXPECT_EQ(migrated->get_value_or<std::string>("locale", ""), "en_GB");
    EXPECT_EQ(migrated->get_value_or<std::string>("source_db.database", ""), "gp_data");
    EXPECT_EQ(migrated->get_value_or<std::string>("target_db.database", ""), "omop_cdm_uk");
    
    // Verify UK transformations are migrated
    EXPECT_EQ(migrated->get_value_or<std::string>("uk_transformations.date_format", ""), "dd/MM/yyyy");
    EXPECT_EQ(migrated->get_value_or<std::string>("uk_transformations.postcode_format", ""), "SW1A 1AA");
    EXPECT_EQ(migrated->get_value_or<std::string>("uk_transformations.currency_symbol", ""), "£");
}

// Tests complex configuration migration with table mappings
TEST_F(ConfigurationMigrationTest, ComplexConfigurationMigration) {
    std::string v1_complex_config = R"(
        version: 1.0
        database:
            source:
                connection_string: postgresql://user:pass@localhost/source_db
            target:
                connection_string: postgresql://user:pass@localhost/omop_cdm

        etl_config:
            batch_size: 5000
            threads: 16
            validation_enabled: true
            
        table_mappings:
            patients:
                target: person
                columns:
                    patient_id: person_id
                    birth_date: year_of_birth
                    gender: gender_concept_id
            visits:
                target: visit_occurrence
                columns:
                    visit_id: visit_occurrence_id
                    patient_id: person_id
                    visit_date: visit_start_date
    )";

    createConfigFile("complex_v1_config.yaml", v1_complex_config);

    auto migrated = migrator_->migrate(config_dir_ + "/complex_v1_config.yaml", "2.0");

    // Verify version migration
    EXPECT_EQ(migrated->get_value_or<std::string>("version", ""), "2.0");
    
    // Verify ETL config migration
    EXPECT_EQ(migrated->get_value_or<int>("etl.batch_size", 0), 5000);
    EXPECT_EQ(migrated->get_value_or<int>("etl.max_parallel_jobs", 0), 16);
    
    // Verify table mappings structure is preserved
    EXPECT_TRUE(migrated->get_value("table_mappings.patients").has_value());
    EXPECT_TRUE(migrated->get_value("table_mappings.visits").has_value());
}

// Tests configuration encryption during migration
TEST_F(ConfigurationMigrationTest, EncryptedConfigurationMigration) {
    common::ConfigurationEncryptor encryptor("test_migration_key_123");
    
    // Create encrypted values
    std::string encrypted_password = encryptor.encrypt("secret_password");
    std::string encrypted_api_key = encryptor.encrypt("api_key_xyz");
    
    std::string v1_encrypted_config = R"(
        version: 1.0
        database:
            source:
                connection_string: postgresql://user:)" + encrypted_password + R"(@localhost/source_db
            target:
                connection_string: postgresql://user:)" + encrypted_password + R"(@localhost/omop_cdm

        etl_config:
            batch_size: 1000
            threads: 4
            
        api_settings:
            vocabulary_api_key: )" + encrypted_api_key + R"(
    )";

    createConfigFile("encrypted_v1_config.yaml", v1_encrypted_config);

    auto migrated = migrator_->migrate(config_dir_ + "/encrypted_v1_config.yaml", "2.0");
    migrated->set_encryptor(&encryptor);

    // Verify migration preserved encrypted values and they can be decrypted
    EXPECT_EQ(migrated->get_value_or<std::string>("version", ""), "2.0");
    
    // Verify encrypted values are still encrypted in configuration
    auto api_key_value = migrated->get_value_or<std::string>("api_settings.vocabulary_api_key", "");
    EXPECT_TRUE(common::ConfigurationEncryptor::is_encrypted(api_key_value));
    
    // Verify encrypted values can be decrypted
    std::string decrypted_api_key = encryptor.decrypt(api_key_value);
    EXPECT_EQ(decrypted_api_key, "api_key_xyz");
}

} // namespace omop::config::test