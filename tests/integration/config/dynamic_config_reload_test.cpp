// tests/integration/config/dynamic_config_reload_test.cpp
// Tests dynamic configuration updates and hot reloading across the ETL pipeline
#include <gtest/gtest.h>
#include "common/configuration.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>
#include <regex>
#include <cstdlib>

namespace omop::config::test {

class ConfigurationManagementTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test configuration directory
        config_dir_ = "/tmp/omop_config_test";
        std::filesystem::create_directories(config_dir_);

        // Create initial configuration file
        createConfigFile("main_config.yaml", getBaseConfig());

        config_manager_ = std::make_shared<common::ConfigurationManager>();
        config_watcher_ = std::make_unique<common::ConfigurationWatcher>(config_manager_);
    }

    void TearDown() override {
        config_watcher_->stop();
        std::filesystem::remove_all(config_dir_);
    }

    void createConfigFile(const std::string& filename, const std::string& content) {
        std::ofstream file(config_dir_ + "/" + filename);
        file << content;
        file.close();
    }

    std::string getBaseConfig() {
        return R"(
            version: 1.0
            environment: test

            source_database:
                type: postgresql
                host: localhost
                port: 5432
                database: source_db
                username: test_user
                password: test_pass
                pool_size: 10

            target_database:
                type: postgresql
                host: localhost
                port: 5432
                database: omop_cdm
                username: test_user
                password: test_pass
                pool_size: 20

            etl_settings:
                batch_size: 1000
                max_parallel_jobs: 4
                error_threshold: 0.05

            table_mappings:
                patients:
                    source_table: patients
                    target_table: person
                    transformations:
                        - source_column: patient_id
                          target_column: person_id
                          type: direct
                        - source_column: birth_date
                          target_column: birth_datetime
                          type: date_transform

            logging:
                level: info
                file: /tmp/omop_etl.log
                max_size_mb: 100
                retention_days: 7
        )";
    }

    std::string config_dir_;
    std::shared_ptr<common::ConfigurationManager> config_manager_;
    std::unique_ptr<common::ConfigurationWatcher> config_watcher_;
};

// Tests hot configuration reloading
TEST_F(ConfigurationManagementTest, HotConfigurationReload) {
    // Load initial configuration
    config_manager_->load_config(config_dir_ + "/main_config.yaml");

    // Start configuration watcher
    config_watcher_->start(config_dir_);

    // Verify initial values
    EXPECT_EQ(config_manager_->get_value_or<int>("etl_settings.batch_size", 0), 1000);

    // Update configuration file
    auto updated_config = getBaseConfig();
    std::regex batch_size_regex("batch_size: 1000");
    updated_config = std::regex_replace(updated_config, batch_size_regex, "batch_size: 2000");

    createConfigFile("main_config.yaml", updated_config);

    // Wait for watcher to detect change
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // Verify configuration was reloaded
    EXPECT_EQ(config_manager_->get_value_or<int>("etl_settings.batch_size", 0), 2000);
}

// Tests configuration validation
TEST_F(ConfigurationManagementTest, ConfigurationValidation) {
    // Test valid configuration
    config_manager_->load_config(config_dir_ + "/main_config.yaml");

    try {
        config_manager_->validate_config();
        SUCCEED();
    } catch (const common::ConfigurationException& e) {
        FAIL() << "Valid configuration failed validation: " << e.what();
    }

    // Test invalid configuration
    std::string invalid_config = R"(
        source_db:
            type: invalid_db_type
            host:
            port: -1

        etl:
            batch_size: -100
            error_threshold: 2.0
    )";

    createConfigFile("invalid_config.yaml", invalid_config);

    auto invalid_manager = std::make_shared<common::ConfigurationManager>();
    invalid_manager->load_config(config_dir_ + "/invalid_config.yaml");

    EXPECT_THROW(invalid_manager->validate_config(), common::ConfigurationException);
}

// Tests environment-specific configuration loading
TEST_F(ConfigurationManagementTest, EnvironmentSpecificConfig) {
    // Test development environment
    setenv("OMOP_ENV", "dev", 1);
    
    std::string dev_config = R"(
        version: 2.0
        environment: dev
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: dev_source_db
    )";
    
    createConfigFile("dev_config.yaml", dev_config);
    config_manager_->load_config(config_dir_ + "/dev_config.yaml");
    
    EXPECT_EQ(config_manager_->get_value_or<std::string>("environment", ""), "dev");
    
    // Test production environment
    setenv("OMOP_ENV", "prod", 1);
    
    std::string prod_config = R"(
        version: 2.0
        environment: prod
        source_db:
            type: postgresql
            host: prod-cluster.local
            port: 5432
            database: prod_source_db
    )";
    
    createConfigFile("prod_config.yaml", prod_config);
    config_manager_->load_config(config_dir_ + "/prod_config.yaml");
    
    EXPECT_EQ(config_manager_->get_value_or<std::string>("environment", ""), "prod");
}

// Tests configuration encryption for sensitive data
TEST_F(ConfigurationManagementTest, SecureConfigurationHandling) {
    common::ConfigurationEncryptor encryptor("test_key_12345678");

    // First, let's encrypt some test data
    std::string original_password = "super_secret_password";
    std::string encrypted_password = encryptor.encrypt(original_password);
    
    std::string original_api_key = "api_key_12345";
    std::string encrypted_api_key = encryptor.encrypt(original_api_key);

    // Create configuration with encrypted values
    std::string secure_config = R"(
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: source_db
            username: db_user
            password: )" + encrypted_password + R"(

        api_keys:
            vocabulary_service: )" + encrypted_api_key + R"(
    )";

    createConfigFile("secure_config.yaml", secure_config);

    // Load with decryption
    config_manager_->set_encryptor(&encryptor);
    config_manager_->load_config(config_dir_ + "/secure_config.yaml");

    // Verify encrypted values are decrypted
    auto password = config_manager_->get_value_or<std::string>("source_db.password", "");
    EXPECT_EQ(password, original_password);
    
    auto api_key = config_manager_->get_value_or<std::string>("api_keys.vocabulary_service", "");
    EXPECT_EQ(api_key, original_api_key);
}

// Tests configuration file watching functionality
TEST_F(ConfigurationManagementTest, ConfigurationFileWatching) {
    config_manager_->load_config(config_dir_ + "/main_config.yaml");
    
    // Verify watcher is not running initially
    EXPECT_FALSE(config_watcher_->is_watching());
    
    // Start watching
    config_watcher_->start(config_dir_, 500); // Check every 500ms
    EXPECT_TRUE(config_watcher_->is_watching());
    
    // Initial value
    EXPECT_EQ(config_manager_->get_value_or<int>("etl_settings.batch_size", 0), 1000);
    
    // Modify configuration file
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    auto updated_config = getBaseConfig();
    std::regex batch_size_regex("batch_size: 1000");
    updated_config = std::regex_replace(updated_config, batch_size_regex, "batch_size: 5000");
    createConfigFile("main_config.yaml", updated_config);
    
    // Wait for file watcher to pick up changes
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    
    // Verify configuration was reloaded
    EXPECT_EQ(config_manager_->get_value_or<int>("etl_settings.batch_size", 0), 5000);
    
    // Stop watching
    config_watcher_->stop();
    EXPECT_FALSE(config_watcher_->is_watching());
}

// Tests UK localization support in configuration
TEST_F(ConfigurationManagementTest, UKLocalizationSupport) {
    std::string uk_config = R"(
        version: 2.0
        environment: test
        locale: en_GB
        
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: nhs_source_db
            
        target_db:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm_uk
            
        etl:
            batch_size: 1000
            max_parallel_jobs: 4
            error_threshold: 0.05
            
        uk_specific:
            nhs_number_validation: true
            postcode_format: uk
            date_format: "dd/MM/yyyy"
            currency_symbol: "£"
            temperature_unit: "celsius"
            distance_unit: "kilometres"
            
        data_sources:
            gp_systems:
                - emis
                - tpp
                - vision
            hospital_systems:
                - epic
                - cerner
                - allscripts
                
        transformations:
            postcode_standardization:
                enabled: true
                format: "SW1A 1AA"
                validation_regex: "^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][A-Z]{2}$"
            
            nhs_number:
                enabled: true
                validation: check_digit
                format: "### ### ####"
                
            date_formats:
                input_formats:
                    - "dd/MM/yyyy"
                    - "dd-MM-yyyy"
                    - "dd.MM.yyyy"
                output_format: "yyyy-MM-dd"
    )";

    createConfigFile("uk_config.yaml", uk_config);
    config_manager_->load_config(config_dir_ + "/uk_config.yaml");

    // Verify UK-specific settings
    EXPECT_EQ(config_manager_->get_value_or<std::string>("locale", ""), "en_GB");
    EXPECT_EQ(config_manager_->get_value_or<std::string>("uk_specific.date_format", ""), "dd/MM/yyyy");
    EXPECT_EQ(config_manager_->get_value_or<std::string>("uk_specific.currency_symbol", ""), "£");
    EXPECT_EQ(config_manager_->get_value_or<std::string>("uk_specific.postcode_format", ""), "uk");
    EXPECT_TRUE(config_manager_->get_value_or<bool>("uk_specific.nhs_number_validation", false));
    
    // Verify postcode validation configuration
    EXPECT_TRUE(config_manager_->get_value_or<bool>("transformations.postcode_standardization.enabled", false));
    EXPECT_EQ(config_manager_->get_value_or<std::string>("transformations.postcode_standardization.format", ""), "SW1A 1AA");
    
    // Verify NHS number configuration
    EXPECT_TRUE(config_manager_->get_value_or<bool>("transformations.nhs_number.enabled", false));
    EXPECT_EQ(config_manager_->get_value_or<std::string>("transformations.nhs_number.format", ""), "### ### ####");
}

// Tests comprehensive configuration validation
TEST_F(ConfigurationManagementTest, ComprehensiveConfigurationValidation) {
    // Test configuration with all required fields
    std::string complete_config = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: source_db
            username: user
            password: pass
            
        target_db:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm
            username: user
            password: pass
            
        tables:
            - source_table: patients
              target_table: person
              transformations:
                - source_column: patient_id
                  target_column: person_id
                  type: direct
                - source_column: birth_date
                  target_column: year_of_birth
                  type: date_transform
                  
        etl:
            batch_size: 1000
            max_parallel_jobs: 4
            error_threshold: 0.05
    )";

    createConfigFile("complete_config.yaml", complete_config);
    auto complete_manager = std::make_shared<common::ConfigurationManager>();
    complete_manager->load_config(config_dir_ + "/complete_config.yaml");
    
    EXPECT_NO_THROW(complete_manager->validate_config());
    
    // Test configuration with invalid values
    std::string invalid_config = R"(
        version: 2.0
        source_db:
            type: unsupported_db
            host: ""
            port: -1
            database: ""
            
        etl:
            batch_size: -100
            max_parallel_jobs: 0
            error_threshold: 1.5
    )";

    createConfigFile("invalid_complete_config.yaml", invalid_config);
    auto invalid_complete_manager = std::make_shared<common::ConfigurationManager>();
    invalid_complete_manager->load_config(config_dir_ + "/invalid_complete_config.yaml");
    
    EXPECT_THROW(invalid_complete_manager->validate_config(), common::ConfigurationException);
}

// Tests configuration version management
TEST_F(ConfigurationManagementTest, ConfigurationVersionManagement) {
    std::string versioned_config = R"(
        version: 2.1
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: test_db
    )";

    createConfigFile("versioned_config.yaml", versioned_config);
    config_manager_->load_config(config_dir_ + "/versioned_config.yaml");
    
    // Test version retrieval
    EXPECT_EQ(config_manager_->get_version(), "2.0"); // Default version
    
    // Test compatibility mode
    config_manager_->enable_compatibility_mode("1.0");
    // Add tests for compatibility mode behavior when implemented
}

// Tests configuration reload functionality
TEST_F(ConfigurationManagementTest, ConfigurationReload) {
    // Load initial configuration
    config_manager_->load_config(config_dir_ + "/main_config.yaml");
    auto initial_batch_size = config_manager_->get_value_or<int>("etl_settings.batch_size", 0);
    EXPECT_EQ(initial_batch_size, 1000);
    
    // Modify the file
    auto updated_config = getBaseConfig();
    std::regex batch_size_regex("batch_size: 1000");
    updated_config = std::regex_replace(updated_config, batch_size_regex, "batch_size: 3000");
    createConfigFile("main_config.yaml", updated_config);
    
    // Reload configuration
    config_manager_->reload();
    
    // Verify changes were loaded
    auto new_batch_size = config_manager_->get_value_or<int>("etl_settings.batch_size", 0);
    EXPECT_EQ(new_batch_size, 3000);
}

// Tests configuration with real database parameters
TEST_F(ConfigurationManagementTest, RealDatabaseConfiguration) {
    std::string db_config = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: postgres
            port: 5432
            database: source_clinical_data
            username: etl_user
            password: etl_password
            pool_size: 10
            connection_timeout: 30
            parameters:
                sslmode: prefer
                application_name: omop_etl
                
        target_db:
            type: postgresql
            host: postgres
            port: 5432
            database: omop_cdm_v6
            username: omop_user
            password: omop_password
            pool_size: 20
            connection_timeout: 30
            parameters:
                sslmode: require
                application_name: omop_etl_target
                
        etl:
            batch_size: 5000
            max_parallel_jobs: 8
            error_threshold: 0.01
            validation_mode: strict
            
        logging:
            level: info
            file: /var/log/omop_etl.log
            max_size_mb: 500
            retention_days: 30
    )";

    createConfigFile("db_config.yaml", db_config);
    config_manager_->load_config(config_dir_ + "/db_config.yaml");
    
    // Verify database configuration parsing
    const auto& source_db = config_manager_->get_source_db();
    EXPECT_EQ(static_cast<int>(source_db.type()), static_cast<int>(common::DatabaseConfig::Type::PostgreSQL));
    EXPECT_EQ(source_db.host(), "postgres");
    EXPECT_EQ(source_db.port(), 5432);
    EXPECT_EQ(source_db.database(), "source_clinical_data");
    EXPECT_EQ(source_db.username(), "etl_user");
    
    const auto& target_db = config_manager_->get_target_db();
    EXPECT_EQ(static_cast<int>(target_db.type()), static_cast<int>(common::DatabaseConfig::Type::PostgreSQL));
    EXPECT_EQ(target_db.host(), "postgres");
    EXPECT_EQ(target_db.database(), "omop_cdm_v6");
    EXPECT_EQ(target_db.username(), "omop_user");
    
    // Verify connection parameters
    const auto& source_params = source_db.parameters();
    EXPECT_EQ(source_params.at("sslmode"), "prefer");
    EXPECT_EQ(source_params.at("application_name"), "omop_etl");
    
    const auto& target_params = target_db.parameters();
    EXPECT_EQ(target_params.at("sslmode"), "require");
    EXPECT_EQ(target_params.at("application_name"), "omop_etl_target");
}

// Tests table mapping configuration
TEST_F(ConfigurationManagementTest, TableMappingConfiguration) {
    std::string mapping_config = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: source_db
            
        target_db:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm
            
        tables:
            - source_table: nhs_patients
              target_table: person
              transformations:
                - source_column: patient_id
                  target_column: person_id
                  type: direct
                - source_column: nhs_number
                  target_column: person_source_value
                  type: direct
                - source_column: birth_date
                  target_column: year_of_birth
                  type: date_transform
                  parameters:
                    extract: year
                - source_column: postcode
                  target_column: location_id
                  type: vocabulary_mapping
                  parameters:
                    vocabulary: postcode_to_location
                    
            - source_table: gp_visits
              target_table: visit_occurrence
              transformations:
                - source_column: visit_id
                  target_column: visit_occurrence_id
                  type: direct
                - source_column: patient_id
                  target_column: person_id
                  type: direct
                - source_column: visit_date
                  target_column: visit_start_date
                  type: date_transform
                  
        vocabulary_mappings:
            postcode_to_location:
                type: lookup_table
                source: uk_postcodes
                key_field: postcode
                value_field: location_id
    )";

    createConfigFile("mapping_config.yaml", mapping_config);
    config_manager_->load_config(config_dir_ + "/mapping_config.yaml");
    
    // Verify table mapping parsing
    const auto& all_mappings = config_manager_->get_all_mappings();
    EXPECT_EQ(all_mappings.size(), 2);
    
    // Verify person table mapping
    auto person_mapping = config_manager_->get_table_mapping("person");
    ASSERT_TRUE(person_mapping.has_value());
    EXPECT_EQ(person_mapping->source_table(), "nhs_patients");
    EXPECT_EQ(person_mapping->target_table(), "person");
    EXPECT_EQ(person_mapping->transformations().size(), 4);
    
    // Verify visit_occurrence table mapping
    auto visit_mapping = config_manager_->get_table_mapping("visit_occurrence");
    ASSERT_TRUE(visit_mapping.has_value());
    EXPECT_EQ(visit_mapping->source_table(), "gp_visits");
    EXPECT_EQ(visit_mapping->target_table(), "visit_occurrence");
    EXPECT_EQ(visit_mapping->transformations().size(), 3);
    
    // Verify vocabulary mappings
    const auto& vocab_mappings = config_manager_->get_vocabulary_mappings();
    EXPECT_TRUE(vocab_mappings["postcode_to_location"]);
}

// Tests error handling and edge cases
TEST_F(ConfigurationManagementTest, ErrorHandlingAndEdgeCases) {
    // Test loading non-existent file
    EXPECT_THROW(
        config_manager_->load_config("/non/existent/file.yaml"),
        common::ConfigurationException
    );
    
    // Test loading invalid YAML
    createConfigFile("invalid.yaml", "invalid: yaml: [content");
    EXPECT_THROW(
        config_manager_->load_config(config_dir_ + "/invalid.yaml"),
        common::ConfigurationException
    );
    
    // Test configuration without required fields
    std::string minimal_config = R"(
        version: 2.0
    )";
    
    createConfigFile("minimal.yaml", minimal_config);
    auto minimal_manager = std::make_shared<common::ConfigurationManager>();
    minimal_manager->load_config(config_dir_ + "/minimal.yaml");
    
    // Should throw on validation due to missing required fields
    EXPECT_THROW(minimal_manager->validate_config(), common::ConfigurationException);
    
    // Test reload without prior file load
    auto empty_manager = std::make_shared<common::ConfigurationManager>();
    EXPECT_THROW(empty_manager->reload(), common::ConfigurationException);
}

} // namespace omop::config::test
