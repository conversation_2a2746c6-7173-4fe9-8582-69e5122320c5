// tests/integration/config/data_quality_config_test.cpp
// Tests data quality configuration and validation functionality
#include <gtest/gtest.h>
#include <filesystem>
#include <fstream>
#include "common/configuration.h"

namespace omop::config::test {

class DataQualityConfigTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<common::ConfigurationManager>();
        config_dir_ = "/tmp/omop_config_quality_test";
        std::filesystem::create_directories(config_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(config_dir_);
    }

    void createConfigFile(const std::string& filename, const std::string& content) {
        std::ofstream file(config_dir_ + "/" + filename);
        file << content;
        file.close();
    }

    std::string config_dir_;
    std::shared_ptr<common::ConfigurationManager> config_;
};

// Tests comprehensive data quality configuration
TEST_F(DataQualityConfigTest, DataQualityRulesConfiguration) {
    std::string quality_config = R"(
        version: 2.0
        source_database:
            type: postgresql
            host: localhost
            port: 5432
            database: source_db
            username: test_user
            password: test_pass

        target_database:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm
            username: test_user
            password: test_pass

        data_quality:
            enabled: true
            validation_mode: strict
            error_threshold: 0.01
            
            rules:
                patient_validation:
                    - name: birth_year_range
                      type: range
                      column: birth_year
                      min: 1900
                      max: 2023
                      severity: error
                      
                    - name: gender_values
                      type: in_list
                      column: gender
                      values: ["M", "F", "O", "U"]
                      severity: warning
                      
                    - name: patient_id_unique
                      type: unique
                      column: patient_id
                      severity: error
                      
                visit_validation:
                    - name: visit_dates
                      type: date_range
                      column: visit_date
                      min_date: "1900-01-01"
                      max_date: "2023-12-31"
                      severity: error
                      
                    - name: visit_type_codes
                      type: vocabulary_check
                      column: visit_type_concept_id
                      vocabulary: visit_type
                      severity: warning
                      
            profiling:
                enabled: true
                sample_size: 10000
                statistics:
                    - completeness
                    - uniqueness
                    - distribution
                    - patterns
                    
            anomaly_detection:
                enabled: true
                methods:
                    - statistical_outliers
                    - pattern_deviation
                    - temporal_anomalies
                thresholds:
                    z_score: 3.0
                    iqr_multiplier: 1.5
                    
        tables:
            - source_table: patients
              target_table: person
              quality_checks:
                  pre_transform:
                      - birth_year_range
                      - gender_values
                      - patient_id_unique
                  post_transform:
                      - completeness_check
                      - referential_integrity
              transformations:
                  - source_column: patient_id
                    target_column: person_id
                    type: direct
                  - source_column: birth_year
                    target_column: year_of_birth
                    type: direct
    )";

    createConfigFile("quality_config.yaml", quality_config);
    config_->load_config(config_dir_ + "/quality_config.yaml");

    // Verify data quality configuration is loaded
    EXPECT_TRUE(config_->get_value_or<bool>("data_quality.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("data_quality.validation_mode", ""), "strict");
    EXPECT_EQ(config_->get_value_or<double>("data_quality.error_threshold", 0.0), 0.01);
    
    // Verify profiling configuration
    EXPECT_TRUE(config_->get_value_or<bool>("data_quality.profiling.enabled", false));
    EXPECT_EQ(config_->get_value_or<int>("data_quality.profiling.sample_size", 0), 10000);
    
    // Verify anomaly detection configuration
    EXPECT_TRUE(config_->get_value_or<bool>("data_quality.anomaly_detection.enabled", false));
    EXPECT_EQ(config_->get_value_or<double>("data_quality.anomaly_detection.thresholds.z_score", 0.0), 3.0);
}

// Tests UK-specific data quality configuration
TEST_F(DataQualityConfigTest, UKDataQualityConfiguration) {
    std::string uk_quality_config = R"(
        version: 2.0
        locale: en_GB
        
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: nhs_source_db

        target_db:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm_uk

        data_quality:
            enabled: true
            localization: uk
            
            uk_specific_rules:
                nhs_number_validation:
                    enabled: true
                    check_digit_validation: true
                    format_validation: true
                    expected_format: "### ### ####"
                    
                postcode_validation:
                    enabled: true
                    format_regex: "^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][A-Z]{2}$"
                    case_normalization: uppercase
                    
                date_format_validation:
                    enabled: true
                    input_formats:
                        - "dd/MM/yyyy"
                        - "dd-MM-yyyy" 
                        - "dd.MM.yyyy"
                    output_format: "yyyy-MM-dd"
                    
                clinical_coding_validation:
                    snomed_ct:
                        enabled: true
                        version: "UK_CLINICAL"
                        concept_validation: true
                    read_codes:
                        enabled: true
                        version: "v2"
                        mapping_to_snomed: true
                    icd10:
                        enabled: true
                        version: "2019"
                        
            data_sources:
                gp_systems:
                    emis:
                        validation_rules:
                            - patient_demographics
                            - clinical_events
                            - prescriptions
                    tpp:
                        validation_rules:
                            - patient_registration
                            - consultations
                            - referrals
                    vision:
                        validation_rules:
                            - patient_records
                            - appointments
                            
        tables:
            - source_table: nhs_patients
              target_table: person
              quality_checks:
                  pre_transform:
                      - nhs_number_validation
                      - postcode_validation
                      - birth_date_format
                  post_transform:
                      - omop_person_completeness
              transformations:
                  - source_column: nhs_number
                    target_column: person_source_value
                    type: direct
                    validation:
                        - nhs_number_check_digit
                  - source_column: postcode
                    target_column: location_id
                    type: vocabulary_mapping
                    parameters:
                        vocabulary: uk_postcodes
                    validation:
                        - postcode_format_check
    )";

    createConfigFile("uk_quality_config.yaml", uk_quality_config);
    config_->load_config(config_dir_ + "/uk_quality_config.yaml");

    // Verify UK-specific quality configuration
    EXPECT_EQ(config_->get_value_or<std::string>("locale", ""), "en_GB");
    EXPECT_EQ(config_->get_value_or<std::string>("data_quality.localization", ""), "uk");
    
    // Verify NHS number validation
    EXPECT_TRUE(config_->get_value_or<bool>("data_quality.uk_specific_rules.nhs_number_validation.enabled", false));
    EXPECT_TRUE(config_->get_value_or<bool>("data_quality.uk_specific_rules.nhs_number_validation.check_digit_validation", false));
    
    // Verify postcode validation
    EXPECT_TRUE(config_->get_value_or<bool>("data_quality.uk_specific_rules.postcode_validation.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("data_quality.uk_specific_rules.postcode_validation.format_regex", ""), 
              "^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][A-Z]{2}$");
    
    // Verify clinical coding validation
    EXPECT_TRUE(config_->get_value_or<bool>("data_quality.uk_specific_rules.clinical_coding_validation.snomed_ct.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("data_quality.uk_specific_rules.clinical_coding_validation.snomed_ct.version", ""), 
              "UK_CLINICAL");
}

// Tests data quality rule validation
TEST_F(DataQualityConfigTest, DataQualityRuleValidation) {
    std::string invalid_quality_config = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: source_db

        target_db:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm

        data_quality:
            enabled: true
            error_threshold: 1.5  # Invalid: should be between 0 and 1
            
            rules:
                invalid_rule:
                    - name: invalid_range
                      type: range
                      column: birth_year
                      min: 2025  # Invalid: min > max
                      max: 1900
                      
        tables:
            - source_table: patients
              target_table: person
              transformations:
                  - source_column: patient_id
                    target_column: person_id
                    type: direct
    )";

    createConfigFile("invalid_quality_config.yaml", invalid_quality_config);
    config_->load_config(config_dir_ + "/invalid_quality_config.yaml");

    // Configuration validation should catch invalid quality rules
    EXPECT_THROW(config_->validate_config(), common::ConfigurationException);
}

// Tests data lineage configuration
TEST_F(DataQualityConfigTest, DataLineageConfiguration) {
    std::string lineage_config = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: source_db

        target_db:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm

        data_lineage:
            enabled: true
            tracking_mode: detailed
            
            metadata_capture:
                source_system: true
                transformation_logic: true
                data_flow_timestamps: true
                user_context: true
                
            storage:
                type: database
                schema: data_lineage
                retention_days: 365
                
            reporting:
                impact_analysis: true
                dependency_mapping: true
                change_tracking: true
                
        tables:
            - source_table: patients
              target_table: person
              lineage:
                  source_system: "NHS_GP_SYSTEM"
                  data_classification: "PERSONAL_DATA"
                  retention_policy: "7_YEARS"
              transformations:
                  - source_column: patient_id
                    target_column: person_id
                    type: direct
                    lineage:
                        transformation_type: "DIRECT_MAPPING"
                        business_rules: "Patient ID direct mapping"
    )";

    createConfigFile("lineage_config.yaml", lineage_config);
    config_->load_config(config_dir_ + "/lineage_config.yaml");

    // Verify data lineage configuration
    EXPECT_TRUE(config_->get_value_or<bool>("data_lineage.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("data_lineage.tracking_mode", ""), "detailed");
    EXPECT_TRUE(config_->get_value_or<bool>("data_lineage.metadata_capture.source_system", false));
    EXPECT_EQ(config_->get_value_or<int>("data_lineage.storage.retention_days", 0), 365);
}

// Tests data profiling configuration validation
TEST_F(DataQualityConfigTest, DataProfilingConfigurationValidation) {
    std::string profiling_config = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: source_db

        target_db:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm

        data_profiling:
            enabled: true
            scheduling:
                frequency: daily
                time: "02:00"
                
            analysis_types:
                structural:
                    enabled: true
                    column_types: true
                    null_analysis: true
                    unique_values: true
                    
                statistical:
                    enabled: true
                    distributions: true
                    correlations: true
                    outliers: true
                    
                semantic:
                    enabled: true
                    pattern_detection: true
                    format_analysis: true
                    classification: true
                    
            output:
                format: json
                location: "/var/lib/omop-etl/profiling"
                retention_days: 90
                
        tables:
            - source_table: patients
              target_table: person
              profiling:
                  priority: high
                  sample_size: 50000
                  analysis_depth: comprehensive
              transformations:
                  - source_column: patient_id
                    target_column: person_id
                    type: direct
    )";

    createConfigFile("profiling_config.yaml", profiling_config);
    config_->load_config(config_dir_ + "/profiling_config.yaml");

    // Verify data profiling configuration
    EXPECT_TRUE(config_->get_value_or<bool>("data_profiling.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("data_profiling.scheduling.frequency", ""), "daily");
    EXPECT_EQ(config_->get_value_or<std::string>("data_profiling.scheduling.time", ""), "02:00");
    
    // Verify analysis types
    EXPECT_TRUE(config_->get_value_or<bool>("data_profiling.analysis_types.structural.enabled", false));
    EXPECT_TRUE(config_->get_value_or<bool>("data_profiling.analysis_types.statistical.enabled", false));
    EXPECT_TRUE(config_->get_value_or<bool>("data_profiling.analysis_types.semantic.enabled", false));
    
    // Verify configuration validation passes
    EXPECT_NO_THROW(config_->validate_config());
}

} // namespace omop::config::test