// tests/integration/config/job_dependency_workflow_test.cpp
// Tests job dependency management and complex workflow configuration
#include <gtest/gtest.h>
#include <filesystem>
#include <fstream>
#include "common/configuration.h"

namespace omop::config::test {

class WorkflowConfigTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<common::ConfigurationManager>();
        config_dir_ = "/tmp/omop_config_workflow_test";
        std::filesystem::create_directories(config_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(config_dir_);
    }

    void createConfigFile(const std::string& filename, const std::string& content) {
        std::ofstream file(config_dir_ + "/" + filename);
        file << content;
        file.close();
    }

    std::string config_dir_;
    std::shared_ptr<common::ConfigurationManager> config_;
};

// Tests workflow dependency configuration
TEST_F(WorkflowConfigTest, WorkflowDependencyConfiguration) {
    std::string workflow_config = R"(
        version: 2.0
        source_database:
            type: postgresql
            host: localhost
            port: 5432
            database: test_db
            username: test_user
            password: test_pass

        target_database:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm
            username: test_user
            password: test_pass

        workflow:
            enabled: true
            execution_mode: parallel
            max_concurrent_jobs: 8
            dependency_resolution: strict
            
            retry_policy:
                max_attempts: 3
                backoff_strategy: exponential
                initial_delay_seconds: 5
                max_delay_seconds: 300
                
            monitoring:
                progress_tracking: true
                performance_metrics: true
                error_reporting: detailed
                
        table_mappings:
            patients:
                source_table: patients
                target_table: person
                transformations:
                    - source_column: patient_id
                      target_column: person_id
                      type: direct
            visits:
                source_table: visits
                target_table: visit_occurrence
                transformations:
                    - source_column: visit_id
                      target_column: visit_occurrence_id
                      type: direct
                
        job_definitions:
            extract_patients:
                type: extract
                source_table: patients
                dependencies: []
                priority: high
                timeout_minutes: 30
                
            extract_visits:
                type: extract
                source_table: visits
                dependencies: []
                priority: high
                timeout_minutes: 45
                
            transform_patients:
                type: transform
                source_table: patients
                target_table: person
                dependencies: [extract_patients]
                priority: normal
                timeout_minutes: 60
                
            transform_visits:
                type: transform
                source_table: visits
                target_table: visit_occurrence
                dependencies: [extract_visits, transform_patients]
                priority: normal
                timeout_minutes: 90
                
            load_person:
                type: load
                target_table: person
                dependencies: [transform_patients]
                priority: normal
                timeout_minutes: 30
                
            load_visit_occurrence:
                type: load
                target_table: visit_occurrence
                dependencies: [transform_visits, load_person]
                priority: normal
                timeout_minutes: 45
                
            validate_data:
                type: validation
                tables: [person, visit_occurrence]
                dependencies: [load_person, load_visit_occurrence]
                priority: low
                timeout_minutes: 15
                
        workflow_templates:
            full_etl_pipeline:
                description: "Complete ETL pipeline for patient and visit data"
                jobs:
                    - extract_patients
                    - extract_visits
                    - transform_patients
                    - transform_visits
                    - load_person
                    - load_visit_occurrence
                    - validate_data
                execution_order: dependency_based
                
            patient_only_pipeline:
                description: "Patient data only pipeline"
                jobs:
                    - extract_patients
                    - transform_patients
                    - load_person
                execution_order: sequential
    )";

    createConfigFile("workflow_config.yaml", workflow_config);
    config_->load_config(config_dir_ + "/workflow_config.yaml");

    // Verify workflow configuration
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("workflow.execution_mode", ""), "parallel");
    EXPECT_EQ(config_->get_value_or<int>("workflow.max_concurrent_jobs", 0), 8);
    
    // Verify retry policy
    EXPECT_EQ(config_->get_value_or<int>("workflow.retry_policy.max_attempts", 0), 3);
    EXPECT_EQ(config_->get_value_or<std::string>("workflow.retry_policy.backoff_strategy", ""), "exponential");
    
    // Verify job definitions exist
    EXPECT_TRUE(config_->get_value("job_definitions.extract_patients").has_value());
    EXPECT_TRUE(config_->get_value("job_definitions.transform_patients").has_value());
    EXPECT_TRUE(config_->get_value("job_definitions.load_person").has_value());
    
    // Verify workflow templates
    EXPECT_TRUE(config_->get_value("workflow_templates.full_etl_pipeline").has_value());
    EXPECT_TRUE(config_->get_value("workflow_templates.patient_only_pipeline").has_value());
}

// Tests conditional workflow configuration
TEST_F(WorkflowConfigTest, ConditionalWorkflowConfiguration) {
    std::string conditional_config = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: test_db

        target_db:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm

        workflow:
            enabled: true
            conditional_execution: true
            
        job_definitions:
            data_quality_check:
                type: validation
                source_table: patients
                dependencies: []
                outputs:
                    quality_score: quality_metrics.overall_score
                    error_count: quality_metrics.error_count
                    
            clean_data_processing:
                type: transform
                source_table: patients
                target_table: person
                dependencies: [data_quality_check]
                conditions:
                    - "${data_quality_check.quality_score} > 0.9"
                    - "${data_quality_check.error_count} < 100"
                    
            dirty_data_processing:
                type: transform
                source_table: patients
                target_table: person
                dependencies: [data_quality_check]
                conditions:
                    - "${data_quality_check.quality_score} <= 0.9"
                    - "${data_quality_check.error_count} >= 100"
                preprocessing:
                    - data_cleaning
                    - error_correction
                    
            final_validation:
                type: validation
                target_table: person
                dependencies: [clean_data_processing, dirty_data_processing]
                execution_mode: always_run
                
        conditional_workflows:
            adaptive_etl:
                description: "ETL pipeline that adapts based on data quality"
                trigger_job: data_quality_check
                branches:
                    high_quality:
                        condition: "${data_quality_check.quality_score} > 0.9"
                        jobs: [clean_data_processing, final_validation]
                    low_quality:
                        condition: "${data_quality_check.quality_score} <= 0.9"
                        jobs: [dirty_data_processing, final_validation]
    )";

    createConfigFile("conditional_config.yaml", conditional_config);
    config_->load_config(config_dir_ + "/conditional_config.yaml");

    // Verify conditional workflow configuration
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.conditional_execution", false));
    
    // Verify conditional job definitions
    EXPECT_TRUE(config_->get_value("job_definitions.clean_data_processing.conditions").has_value());
    EXPECT_TRUE(config_->get_value("job_definitions.dirty_data_processing.conditions").has_value());
    
    // Verify conditional workflows
    EXPECT_TRUE(config_->get_value("conditional_workflows.adaptive_etl").has_value());
    EXPECT_TRUE(config_->get_value("conditional_workflows.adaptive_etl.branches.high_quality").has_value());
    EXPECT_TRUE(config_->get_value("conditional_workflows.adaptive_etl.branches.low_quality").has_value());
}

// Tests workflow scheduling configuration
TEST_F(WorkflowConfigTest, WorkflowSchedulingConfiguration) {
    std::string scheduling_config = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: test_db

        target_db:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm

        workflow:
            enabled: true
            scheduling:
                enabled: true
                timezone: "Europe/London"
                
        scheduled_workflows:
            daily_full_etl:
                schedule: "0 2 * * *"  # Daily at 2 AM
                workflow: full_etl_pipeline
                enabled: true
                retry_on_failure: true
                notifications:
                    on_success: ["<EMAIL>"]
                    on_failure: ["<EMAIL>", "<EMAIL>"]
                    
            weekly_data_quality:
                schedule: "0 1 * * 0"  # Weekly on Sunday at 1 AM
                workflow: data_quality_assessment
                enabled: true
                timeout_hours: 4
                
            monthly_archive:
                schedule: "0 0 1 * *"  # Monthly on 1st at midnight
                workflow: data_archival
                enabled: true
                prerequisites:
                    - check_disk_space
                    - verify_backup_integrity
                    
        job_definitions:
            full_etl_pipeline:
                type: composite
                description: "Complete ETL pipeline"
                jobs: [extract_all, transform_all, load_all, validate_all]
                
            data_quality_assessment:
                type: validation
                description: "Comprehensive data quality assessment"
                scope: all_tables
                
            data_archival:
                type: maintenance
                description: "Archive old data and cleanup"
                retention_policy: "7_years"
                
        notification_settings:
            email:
                smtp_server: "smtp.hospital.nhs.uk"
                port: 587
                use_tls: true
                from_address: "<EMAIL>"
                
            slack:
                webhook_url: "*****************************************************************************"
                channel: "#data-engineering"
    )";

    createConfigFile("scheduling_config.yaml", scheduling_config);
    config_->load_config(config_dir_ + "/scheduling_config.yaml");

    // Verify scheduling configuration
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.scheduling.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("workflow.scheduling.timezone", ""), "Europe/London");
    
    // Verify scheduled workflows
    EXPECT_EQ(config_->get_value_or<std::string>("scheduled_workflows.daily_full_etl.schedule", ""), "0 2 * * *");
    EXPECT_TRUE(config_->get_value_or<bool>("scheduled_workflows.daily_full_etl.enabled", false));
    
    // Verify notification settings
    EXPECT_EQ(config_->get_value_or<std::string>("notification_settings.email.smtp_server", ""), "smtp.hospital.nhs.uk");
    EXPECT_EQ(config_->get_value_or<int>("notification_settings.email.port", 0), 587);
}

// Tests workflow error handling configuration
TEST_F(WorkflowConfigTest, WorkflowErrorHandlingConfiguration) {
    std::string error_handling_config = R"(
        version: 2.0
        source_db:
            type: postgresql
            host: localhost
            port: 5432
            database: test_db

        target_db:
            type: postgresql
            host: localhost
            port: 5432
            database: omop_cdm

        workflow:
            enabled: true
            error_handling:
                strategy: continue_on_error
                max_failures_per_workflow: 3
                failure_escalation: true
                
        error_policies:
            data_extraction_errors:
                max_retry_attempts: 5
                retry_delay_seconds: 30
                fallback_action: skip_record
                notification_threshold: 100
                
            transformation_errors:
                max_retry_attempts: 3
                retry_delay_seconds: 60
                fallback_action: use_default_values
                error_quarantine: true
                
            loading_errors:
                max_retry_attempts: 2
                retry_delay_seconds: 120
                fallback_action: rollback_batch
                alert_immediately: true
                
        recovery_procedures:
            workflow_failure:
                auto_recovery: true
                checkpoint_restore: true
                partial_restart: true
                
            data_corruption:
                verification_checks: mandatory
                rollback_to_checkpoint: true
                manual_intervention_required: true
                
        monitoring:
            health_checks:
                enabled: true
                interval_seconds: 60
                endpoints:
                    - database_connectivity
                    - disk_space
                    - memory_usage
                    
            alerting:
                channels: [email, slack, pagerduty]
                severity_levels:
                    critical: immediate
                    warning: 5_minutes
                    info: hourly_digest
    )";

    createConfigFile("error_handling_config.yaml", error_handling_config);
    config_->load_config(config_dir_ + "/error_handling_config.yaml");

    // Verify error handling configuration
    EXPECT_EQ(config_->get_value_or<std::string>("workflow.error_handling.strategy", ""), "continue_on_error");
    EXPECT_EQ(config_->get_value_or<int>("workflow.error_handling.max_failures_per_workflow", 0), 3);
    
    // Verify error policies
    EXPECT_EQ(config_->get_value_or<int>("error_policies.data_extraction_errors.max_retry_attempts", 0), 5);
    EXPECT_EQ(config_->get_value_or<std::string>("error_policies.transformation_errors.fallback_action", ""), "use_default_values");
    
    // Verify recovery procedures
    EXPECT_TRUE(config_->get_value_or<bool>("recovery_procedures.workflow_failure.auto_recovery", false));
    EXPECT_TRUE(config_->get_value_or<bool>("recovery_procedures.data_corruption.manual_intervention_required", false));
    
    // Verify monitoring configuration
    EXPECT_TRUE(config_->get_value_or<bool>("monitoring.health_checks.enabled", false));
    EXPECT_EQ(config_->get_value_or<int>("monitoring.health_checks.interval_seconds", 0), 60);
}

// Tests UK-specific workflow configuration
TEST_F(WorkflowConfigTest, UKWorkflowConfiguration) {
    std::string uk_workflow_config = R"(
        version: 2.0
        environment: production
        locale: en_GB
        timezone: Europe/London
        
        source_db:
            type: postgresql
            host: nhs-db-cluster.local
            port: 5432
            database: nhs_clinical_data
            ssl_mode: require

        target_db:
            type: postgresql
            host: omop-db-cluster.local
            port: 5432
            database: omop_cdm_uk
            ssl_mode: require

        workflow:
            enabled: true
            execution_mode: parallel
            max_concurrent_jobs: 8
            dependency_resolution: strict
            
            uk_specific_settings:
                nhs_data_processing: true
                uk_date_handling: true
                uk_currency_processing: true
                nhs_number_validation: true
                postcode_standardization: true
                
            nhs_workflows:
                gp_data_extraction:
                    enabled: true
                    schedule: "0 1 * * *"  # Daily at 1 AM
                    source_systems: [emis, tpp, vision]
                    priority: high
                    
                hospital_data_extraction:
                    enabled: true
                    schedule: "0 2 * * *"  # Daily at 2 AM
                    source_systems: [epic, cerner, allscripts]
                    priority: high
                    
                nhs_digital_integration:
                    enabled: true
                    schedule: "0 3 * * *"  # Daily at 3 AM
                    api_endpoints:
                        - "https://api.digital.nhs.uk/patient-data"
                        - "https://api.digital.nhs.uk/prescriptions"
                    priority: critical
                    
            data_quality_workflows:
                nhs_number_validation:
                    enabled: true
                    validation_rules:
                        - check_digit_validation
                        - format_validation
                        - uniqueness_check
                    error_threshold: 0.001
                    
                postcode_validation:
                    enabled: true
                    validation_rules:
                        - format_validation
                        - real_postcode_check
                        - nhs_trust_mapping
                    error_threshold: 0.01
                    
            compliance_workflows:
                gdpr_compliance:
                    enabled: true
                    data_retention_policy: 8_years
                    anonymization_level: pseudonymized
                    audit_trail: mandatory
                    
                nhs_compliance:
                    enabled: true
                    data_sharing_agreements: required
                    patient_consent_tracking: enabled
                    data_usage_monitoring: real_time
                    
        notification_settings:
            email:
                smtp_server: "smtp.nhs.net"
                port: 587
                encryption: tls
                from_address: "<EMAIL>"
                
            nhs_alerts:
                enabled: true
                channels:
                    - nhs_mail
                    - nhs_secure_messaging
                escalation_path:
                    - data_team
                    - nhs_digital_support
                    - nhs_emergency_contact
                    
        monitoring:
            nhs_metrics:
                enabled: true
                kpis:
                    - data_freshness
                    - processing_accuracy
                    - nhs_number_completeness
                    - postcode_accuracy
                reporting_frequency: daily
                
            audit_logging:
                enabled: true
                log_level: detailed
                retention_period: 10_years
                nhs_audit_compliance: true
    )";

    createConfigFile("uk_workflow_config.yaml", uk_workflow_config);
    config_->load_config(config_dir_ + "/uk_workflow_config.yaml");

    // Verify UK-specific settings
    EXPECT_EQ(config_->get_value_or<std::string>("locale", ""), "en_GB");
    EXPECT_EQ(config_->get_value_or<std::string>("timezone", ""), "Europe/London");
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.uk_specific_settings.nhs_data_processing", false));
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.uk_specific_settings.uk_date_handling", false));
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.uk_specific_settings.nhs_number_validation", false));
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.uk_specific_settings.postcode_standardization", false));
    
    // Verify NHS workflows
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.nhs_workflows.gp_data_extraction.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("workflow.nhs_workflows.gp_data_extraction.schedule", ""), "0 1 * * *");
    EXPECT_EQ(config_->get_value_or<std::string>("workflow.nhs_workflows.gp_data_extraction.priority", ""), "high");
    
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.nhs_workflows.hospital_data_extraction.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("workflow.nhs_workflows.hospital_data_extraction.schedule", ""), "0 2 * * *");
    
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.nhs_workflows.nhs_digital_integration.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("workflow.nhs_workflows.nhs_digital_integration.priority", ""), "critical");
    
    // Verify data quality workflows
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.data_quality_workflows.nhs_number_validation.enabled", false));
    EXPECT_EQ(config_->get_value_or<double>("workflow.data_quality_workflows.nhs_number_validation.error_threshold", 0.0), 0.001);
    
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.data_quality_workflows.postcode_validation.enabled", false));
    EXPECT_EQ(config_->get_value_or<double>("workflow.data_quality_workflows.postcode_validation.error_threshold", 0.0), 0.01);
    
    // Verify compliance workflows
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.compliance_workflows.gdpr_compliance.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("workflow.compliance_workflows.gdpr_compliance.data_retention_policy", ""), "8_years");
    EXPECT_EQ(config_->get_value_or<std::string>("workflow.compliance_workflows.gdpr_compliance.anonymization_level", ""), "pseudonymized");
    
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.compliance_workflows.nhs_compliance.enabled", false));
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.compliance_workflows.nhs_compliance.patient_consent_tracking", false));
    EXPECT_TRUE(config_->get_value_or<bool>("workflow.compliance_workflows.nhs_compliance.data_usage_monitoring", false));
    
    // Verify notification settings
    EXPECT_EQ(config_->get_value_or<std::string>("notification_settings.email.smtp_server", ""), "smtp.nhs.net");
    EXPECT_EQ(config_->get_value_or<std::string>("notification_settings.email.from_address", ""), "<EMAIL>");
    
    EXPECT_TRUE(config_->get_value_or<bool>("notification_settings.nhs_alerts.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("notification_settings.nhs_alerts.escalation_path[0]", ""), "data_team");
    
    // Verify monitoring settings
    EXPECT_TRUE(config_->get_value_or<bool>("monitoring.nhs_metrics.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("monitoring.nhs_metrics.reporting_frequency", ""), "daily");
    
    EXPECT_TRUE(config_->get_value_or<bool>("monitoring.audit_logging.enabled", false));
    EXPECT_EQ(config_->get_value_or<std::string>("monitoring.audit_logging.retention_period", ""), "10_years");
    EXPECT_TRUE(config_->get_value_or<bool>("monitoring.audit_logging.nhs_audit_compliance", false));
}

} // namespace omop::config::test