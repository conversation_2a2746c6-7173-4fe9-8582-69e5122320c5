// tests/integration/quality/test_data_lineage.cpp
// Data lineage tracking tests

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <unordered_set>
#include <queue>
#include "core/pipeline.h"
#include "service/etl_service.h"

namespace omop::quality::test {

class DataLineageTest : public ::testing::Test {
protected:
    struct LineageNode {
        std::string id;
        std::string type; // source, transform, target
        std::string name;
        std::unordered_map<std::string, std::any> metadata;
        std::vector<std::string> input_ids;
        std::vector<std::string> output_ids;
        std::chrono::system_clock::time_point timestamp;
    };

    struct DataLineageTracker {
        std::unordered_map<std::string, LineageNode> nodes;
        std::unordered_map<std::string, std::string> record_to_node;

        std::string AddSourceNode(const std::string& source_name,
                                 const std::unordered_map<std::string, std::any>& metadata = {}) {
            std::string node_id = "source_" + std::to_string(nodes.size());
            LineageNode node{
                .id = node_id,
                .type = "source",
                .name = source_name,
                .metadata = metadata,
                .timestamp = std::chrono::system_clock::now()
            };
            nodes[node_id] = node;
            return node_id;
        }

        std::string AddTransformNode(const std::string& transform_name,
                                    const std::vector<std::string>& input_nodes,
                                    const std::unordered_map<std::string, std::any>& metadata = {}) {
            std::string node_id = "transform_" + std::to_string(nodes.size());
            LineageNode node{
                .id = node_id,
                .type = "transform",
                .name = transform_name,
                .metadata = metadata,
                .input_ids = input_nodes,
                .timestamp = std::chrono::system_clock::now()
            };

            // Update output links in input nodes
            for (const auto& input_id : input_nodes) {
                if (nodes.find(input_id) != nodes.end()) {
                    nodes[input_id].output_ids.push_back(node_id);
                }
            }

            nodes[node_id] = node;
            return node_id;
        }

        std::string AddTargetNode(const std::string& target_name,
                                 const std::string& input_node,
                                 const std::unordered_map<std::string, std::any>& metadata = {}) {
            std::string node_id = "target_" + std::to_string(nodes.size());
            LineageNode node{
                .id = node_id,
                .type = "target",
                .name = target_name,
                .metadata = metadata,
                .input_ids = {input_node},
                .timestamp = std::chrono::system_clock::now()
            };

            // Update output link in input node
            if (nodes.find(input_node) != nodes.end()) {
                nodes[input_node].output_ids.push_back(node_id);
            }

            nodes[node_id] = node;
            return node_id;
        }

        void TrackRecordLineage(const std::string& record_id, const std::string& node_id) {
            record_to_node[record_id] = node_id;
        }

        std::vector<std::string> GetLineageChain(const std::string& node_id) {
            std::vector<std::string> chain;
            std::unordered_set<std::string> visited;

            // Backward traversal to sources
            std::function<void(const std::string&)> traverse_backward;
            traverse_backward = [&](const std::string& current_id) {
                if (visited.find(current_id) != visited.end()) return;
                visited.insert(current_id);

                auto it = nodes.find(current_id);
                if (it == nodes.end()) return;

                // Add sources first (reverse order)
                for (const auto& input_id : it->second.input_ids) {
                    traverse_backward(input_id);
                }

                chain.push_back(current_id);
            };

            traverse_backward(node_id);
            return chain;
        }

        std::vector<std::string> GetImpactAnalysis(const std::string& node_id) {
            std::vector<std::string> impacted;
            std::queue<std::string> to_visit;
            std::unordered_set<std::string> visited;

            to_visit.push(node_id);

            while (!to_visit.empty()) {
                std::string current = to_visit.front();
                to_visit.pop();

                if (visited.find(current) != visited.end()) continue;
                visited.insert(current);

                auto it = nodes.find(current);
                if (it == nodes.end()) continue;

                if (current != node_id) {
                    impacted.push_back(current);
                }

                for (const auto& output_id : it->second.output_ids) {
                    to_visit.push(output_id);
                }
            }

            return impacted;
        }
    };

    void SimulateETLPipeline(DataLineageTracker& tracker) {
        // Simulate a typical ETL pipeline with lineage tracking

        // Source nodes
        auto csv_source = tracker.AddSourceNode("patients.csv", {
            {"file_path", "/data/patients.csv"},
            {"record_count", 1000},
            {"timestamp", std::chrono::system_clock::now()}
        });

        auto db_source = tracker.AddSourceNode("clinical_db.conditions", {
            {"connection", "postgresql://localhost/clinical"},
            {"query", "SELECT * FROM conditions WHERE year = 2023"},
            {"record_count", 5000}
        });

        // Transformation nodes
        auto clean_transform = tracker.AddTransformNode("data_cleaning", {csv_source}, {
            {"rules_applied", std::vector<std::string>{"trim_whitespace", "standardize_dates"}},
            {"records_cleaned", 950}
        });

        auto validate_transform = tracker.AddTransformNode("validation", {clean_transform}, {
            {"validation_rules", 15},
            {"records_passed", 940},
            {"records_failed", 10}
        });

        auto join_transform = tracker.AddTransformNode("patient_condition_join",
            {validate_transform, db_source}, {
            {"join_type", "inner"},
            {"join_keys", std::vector<std::string>{"patient_id"}},
            {"output_records", 4500}
        });

        auto vocab_transform = tracker.AddTransformNode("vocabulary_mapping", {join_transform}, {
            {"vocabularies_used", std::vector<std::string>{"ICD10", "SNOMED"}},
            {"mappings_applied", 4200},
            {"unmapped_values", 300}
        });

        // Target nodes
        auto person_target = tracker.AddTargetNode("omop.person", validate_transform, {
            {"table", "person"},
            {"records_loaded", 940},
            {"load_timestamp", std::chrono::system_clock::now()}
        });

        auto condition_target = tracker.AddTargetNode("omop.condition_occurrence",
            vocab_transform, {
            {"table", "condition_occurrence"},
            {"records_loaded", 4200},
            {"load_timestamp", std::chrono::system_clock::now()}
        });
    }

protected:
    std::unique_ptr<DataLineageTracker> lineage_tracker_;

    void SetUp() override {
        lineage_tracker_ = std::make_unique<DataLineageTracker>();
    }
};

TEST_F(DataLineageTest, TrackCompleteLineage) {
    // Create a complex lineage graph
    SimulateETLPipeline(*lineage_tracker_);

    // Verify lineage tracking
    EXPECT_EQ(lineage_tracker_->nodes.size(), 8)
        << "Should have 8 nodes in lineage graph";

    // Check source nodes
    int source_count = 0;
    for (const auto& [id, node] : lineage_tracker_->nodes) {
        if (node.type == "source") source_count++;
    }
    EXPECT_EQ(source_count, 2) << "Should have 2 source nodes";

    // Check transformation nodes
    int transform_count = 0;
    for (const auto& [id, node] : lineage_tracker_->nodes) {
        if (node.type == "transform") transform_count++;
    }
    EXPECT_EQ(transform_count, 4) << "Should have 4 transformation nodes";

    // Check target nodes
    int target_count = 0;
    for (const auto& [id, node] : lineage_tracker_->nodes) {
        if (node.type == "target") target_count++;
    }
    EXPECT_EQ(target_count, 2) << "Should have 2 target nodes";
}

TEST_F(DataLineageTest, TraceDataLineage) {
    // Create lineage graph
    SimulateETLPipeline(*lineage_tracker_);

    // Find condition target node
    std::string condition_target_id;
    for (const auto& [id, node] : lineage_tracker_->nodes) {
        if (node.name == "omop.condition_occurrence") {
            condition_target_id = id;
            break;
        }
    }

    ASSERT_FALSE(condition_target_id.empty()) << "Condition target node not found";

    // Trace lineage back to sources
    auto lineage_chain = lineage_tracker_->GetLineageChain(condition_target_id);

    // Verify lineage chain
    EXPECT_GE(lineage_chain.size(), 5)
        << "Lineage chain should include all nodes from sources to target";

    // Verify chain starts with sources
    bool has_csv_source = false;
    bool has_db_source = false;

    for (const auto& node_id : lineage_chain) {
        const auto& node = lineage_tracker_->nodes[node_id];
        if (node.type == "source") {
            if (node.name == "patients.csv") has_csv_source = true;
            if (node.name == "clinical_db.conditions") has_db_source = true;
        }
    }

    EXPECT_TRUE(has_db_source)
        << "Lineage should trace back to database source";

    // Verify chain ends with target
    EXPECT_EQ(lineage_tracker_->nodes[lineage_chain.back()].name,
              "omop.condition_occurrence")
        << "Lineage chain should end with target node";
}

TEST_F(DataLineageTest, ImpactAnalysis) {
    // Create lineage graph
    SimulateETLPipeline(*lineage_tracker_);

    // Find CSV source node
    std::string csv_source_id;
    for (const auto& [id, node] : lineage_tracker_->nodes) {
        if (node.name == "patients.csv") {
            csv_source_id = id;
            break;
        }
    }

    // Analyze impact of changes to CSV source
    auto impacted_nodes = lineage_tracker_->GetImpactAnalysis(csv_source_id);

    // Verify impact analysis
    EXPECT_GE(impacted_nodes.size(), 3)
        << "Changes to CSV source should impact multiple downstream nodes";

    // Check that person target is impacted
    bool person_impacted = false;
    for (const auto& node_id : impacted_nodes) {
        if (lineage_tracker_->nodes[node_id].name == "omop.person") {
            person_impacted = true;
            break;
        }
    }

    EXPECT_TRUE(person_impacted)
        << "Person table should be impacted by CSV source changes";
}

TEST_F(DataLineageTest, RecordLevelLineage) {
    // Track lineage at record level
    DataLineageTracker record_tracker;

    // Simulate record flow
    auto source = record_tracker.AddSourceNode("test_source");

    // Track individual records
    std::vector<std::string> record_ids;
    for (int i = 0; i < 10; ++i) {
        std::string record_id = "record_" + std::to_string(i);
        record_ids.push_back(record_id);
        record_tracker.TrackRecordLineage(record_id, source);
    }

    // Transform records
    auto transform = record_tracker.AddTransformNode("transform", {source});

    // Track transformed records (some filtered out)
    for (size_t i = 0; i < record_ids.size(); ++i) {
        if (i % 2 == 0) { // Only even records pass
            record_tracker.TrackRecordLineage(record_ids[i], transform);
        }
    }

    // Verify record-level tracking
    int source_records = 0;
    int transform_records = 0;

    for (const auto& [record_id, node_id] : record_tracker.record_to_node) {
        if (node_id == source) source_records++;
        if (node_id == transform) transform_records++;
    }

    EXPECT_EQ(source_records, 10) << "All records should be tracked at source";
    EXPECT_EQ(transform_records, 5) << "Only half the records should pass transform";
}

TEST_F(DataLineageTest, LineageMetadata) {
    // Test metadata tracking in lineage
    DataLineageTracker metadata_tracker;

    // Create nodes with rich metadata
    auto source = metadata_tracker.AddSourceNode("data_lake", {
        {"format", "parquet"},
        {"compression", "snappy"},
        {"size_mb", 1024.5},
        {"partitions", 32},
        {"schema_version", "2.1.0"}
    });

    auto transform = metadata_tracker.AddTransformNode("quality_check", {source}, {
        {"rules", std::vector<std::string>{
            "check_nulls", "validate_ranges", "verify_references"
        }},
        {"quality_score", 0.95},
        {"issues_found", std::vector<std::string>{
            "5% null values in optional fields",
            "2 records with future dates"
        }}
    });

    // Verify metadata is preserved
    auto& source_node = metadata_tracker.nodes[source];
    EXPECT_EQ(std::any_cast<std::string>(source_node.metadata["format"]), "parquet");
    EXPECT_DOUBLE_EQ(std::any_cast<double>(source_node.metadata["size_mb"]), 1024.5);

    auto& transform_node = metadata_tracker.nodes[transform];
    EXPECT_DOUBLE_EQ(std::any_cast<double>(transform_node.metadata["quality_score"]), 0.95);

    auto rules = std::any_cast<std::vector<std::string>>(transform_node.metadata["rules"]);
    EXPECT_EQ(rules.size(), 3);
}

TEST_F(DataLineageTest, LineageVersioning) {
    // Test versioning in data lineage
    struct VersionedLineageTracker : DataLineageTracker {
        std::unordered_map<std::string, std::vector<LineageNode>> node_versions;

        void UpdateNode(const std::string& node_id,
                       const std::unordered_map<std::string, std::any>& new_metadata) {
            auto it = nodes.find(node_id);
            if (it != nodes.end()) {
                // Save current version
                node_versions[node_id].push_back(it->second);

                // Update node
                it->second.metadata = new_metadata;
                it->second.timestamp = std::chrono::system_clock::now();
            }
        }

        size_t GetVersionCount(const std::string& node_id) {
            return node_versions[node_id].size() + 1; // +1 for current version
        }
    };

    VersionedLineageTracker versioned_tracker;

    // Create initial pipeline
    auto source = versioned_tracker.AddSourceNode("config_file", {
        {"version", "1.0"},
        {"parameters", std::unordered_map<std::string, int>{{"batch_size", 100}}}
    });

    // Update source configuration
    versioned_tracker.UpdateNode(source, {
        {"version", "1.1"},
        {"parameters", std::unordered_map<std::string, int>{{"batch_size", 500}}}
    });

    // Update again
    versioned_tracker.UpdateNode(source, {
        {"version", "2.0"},
        {"parameters", std::unordered_map<std::string, int>{
            {"batch_size", 1000},
            {"parallel_workers", 4}
        }}
    });

    // Verify versioning
    EXPECT_EQ(versioned_tracker.GetVersionCount(source), 3)
        << "Should have 3 versions of the node";

    // Check version history
    auto& versions = versioned_tracker.node_versions[source];
    EXPECT_EQ(std::any_cast<std::string>(versions[0].metadata["version"]), "1.0");
    EXPECT_EQ(std::any_cast<std::string>(versions[1].metadata["version"]), "1.1");

    // Current version
    auto& current_node = versioned_tracker.nodes[source];
    EXPECT_EQ(std::any_cast<std::string>(current_node.metadata["version"]), "2.0");
}

TEST_F(DataLineageTest, CrossSystemLineage) {
    // Test lineage tracking across multiple systems
    DataLineageTracker cross_system_tracker;

    // External system sources
    auto kafka_source = cross_system_tracker.AddSourceNode("kafka_stream", {
        {"system", "Apache Kafka"},
        {"topic", "patient_events"},
        {"consumer_group", "etl_pipeline"},
        {"offset", 12345678}
    });

    auto s3_source = cross_system_tracker.AddSourceNode("s3_bucket", {
        {"system", "AWS S3"},
        {"bucket", "healthcare-data"},
        {"prefix", "raw/2024/"},
        {"format", "json"}
    });

    auto api_source = cross_system_tracker.AddSourceNode("fhir_api", {
        {"system", "FHIR Server"},
        {"endpoint", "https://fhir.example.com/Patient"},
        {"version", "R4"},
        {"auth_method", "OAuth2"}
    });

    // Merge data from multiple systems
    auto merge_transform = cross_system_tracker.AddTransformNode(
        "multi_source_merge",
        {kafka_source, s3_source, api_source},
        {
            {"merge_strategy", "timestamp_based"},
            {"deduplication", true},
            {"conflict_resolution", "latest_wins"}
        }
    );

    // Verify cross-system lineage
    auto lineage = cross_system_tracker.GetLineageChain(merge_transform);

    // Should include all three external systems
    std::unordered_set<std::string> systems_found;
    for (const auto& node_id : lineage) {
        auto& node = cross_system_tracker.nodes[node_id];
        if (node.metadata.find("system") != node.metadata.end()) {
            systems_found.insert(std::any_cast<std::string>(node.metadata["system"]));
        }
    }

    EXPECT_EQ(systems_found.size(), 3) << "Should track lineage from 3 different systems";
    EXPECT_TRUE(systems_found.find("Apache Kafka") != systems_found.end());
    EXPECT_TRUE(systems_found.find("AWS S3") != systems_found.end());
    EXPECT_TRUE(systems_found.find("FHIR Server") != systems_found.end());
}

} // namespace omop::quality::test