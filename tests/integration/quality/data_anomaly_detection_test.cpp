// tests/integration/quality/test_anomaly_detection.cpp
// Anomaly detection in data tests

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <random>
#include <algorithm>
#include "core/record.h"
#include "test_helpers/test_data_generator.h"

namespace omop::quality::test {

class AnomalyDetectionTest : public ::testing::Test {
protected:
    struct AnomalyDetector {
        // Statistical anomaly detection parameters
        double z_score_threshold{3.0};
        double iqr_multiplier{1.5};
        size_t min_frequency{5};

        struct AnomalyResult {
            bool is_anomaly{false};
            std::string anomaly_type;
            double anomaly_score{0.0};
            std::string description;
        };

        // Detect numeric outliers using z-score
        AnomalyResult DetectNumericOutlier(double value,
                                          const std::vector<double>& reference_values) {
            AnomalyResult result;

            if (reference_values.size() < 2) return result;

            // Calculate mean and standard deviation
            double mean = std::accumulate(reference_values.begin(),
                                         reference_values.end(), 0.0) / reference_values.size();

            double sq_sum = std::inner_product(reference_values.begin(),
                                             reference_values.end(),
                                             reference_values.begin(), 0.0);
            double stddev = std::sqrt(sq_sum / reference_values.size() - mean * mean);

            if (stddev == 0) return result;

            // Calculate z-score
            double z_score = std::abs((value - mean) / stddev);

            if (z_score > z_score_threshold) {
                result.is_anomaly = true;
                result.anomaly_type = "numeric_outlier";
                result.anomaly_score = z_score;
                result.description = "Value " + std::to_string(value) +
                                   " has z-score " + std::to_string(z_score);
            }

            return result;
        }

        // Detect using IQR method
        AnomalyResult DetectIQROutlier(double value,
                                       std::vector<double> reference_values) {
            AnomalyResult result;

            if (reference_values.size() < 4) return result;

            std::sort(reference_values.begin(), reference_values.end());

            size_t q1_index = reference_values.size() / 4;
            size_t q3_index = 3 * reference_values.size() / 4;

            double q1 = reference_values[q1_index];
            double q3 = reference_values[q3_index];
            double iqr = q3 - q1;

            double lower_bound = q1 - iqr_multiplier * iqr;
            double upper_bound = q3 + iqr_multiplier * iqr;

            if (value < lower_bound || value > upper_bound) {
                result.is_anomaly = true;
                result.anomaly_type = "iqr_outlier";
                result.anomaly_score = std::max(
                    std::abs(value - lower_bound),
                    std::abs(value - upper_bound)) / iqr;
                result.description = "Value " + std::to_string(value) +
                                   " outside IQR bounds [" +
                                   std::to_string(lower_bound) + ", " +
                                   std::to_string(upper_bound) + "]";
            }

            return result;
        }

        // Detect categorical anomalies
        AnomalyResult DetectCategoricalAnomaly(const std::string& value,
                                              const std::unordered_map<std::string, size_t>& frequencies) {
            AnomalyResult result;

            size_t total_count = 0;
            for (const auto& [val, count] : frequencies) {
                total_count += count;
            }

            auto it = frequencies.find(value);
            if (it == frequencies.end() || it->second < min_frequency) {
                result.is_anomaly = true;
                result.anomaly_type = "rare_category";
                result.anomaly_score = it != frequencies.end() ?
                    static_cast<double>(min_frequency - it->second) / min_frequency : 1.0;
                result.description = "Rare value: " + value;
            }

            return result;
        }

        // Detect temporal anomalies
        AnomalyResult DetectTemporalAnomaly(
            const std::chrono::system_clock::time_point& timestamp,
            const std::vector<std::chrono::system_clock::time_point>& reference_timestamps) {

            AnomalyResult result;

            if (reference_timestamps.size() < 2) return result;

            // Check for out-of-sequence timestamps
            auto min_time = *std::min_element(reference_timestamps.begin(),
                                            reference_timestamps.end());
            auto max_time = *std::max_element(reference_timestamps.begin(),
                                            reference_timestamps.end());

            if (timestamp < min_time || timestamp > max_time) {
                auto duration = timestamp < min_time ?
                    min_time - timestamp : timestamp - max_time;

                auto days = std::chrono::duration_cast<std::chrono::days>(duration).count();

                if (days > 30) {  // More than 30 days outside range
                    result.is_anomaly = true;
                    result.anomaly_type = "temporal_outlier";
                    result.anomaly_score = static_cast<double>(days) / 30.0;
                    result.description = "Timestamp " + std::to_string(days) +
                                       " days outside expected range";
                }
            }

            return result;
        }

        // Pattern-based anomaly detection
        AnomalyResult DetectPatternAnomaly(const std::string& value,
                                          const std::string& expected_pattern) {
            AnomalyResult result;

            // Convert value to pattern
            std::string actual_pattern;
            for (char c : value) {
                if (std::isdigit(c)) actual_pattern += 'N';
                else if (std::isalpha(c)) actual_pattern += (std::isupper(c) ? 'A' : 'a');
                else if (std::isspace(c)) actual_pattern += '_';
                else actual_pattern += c;
            }

            if (actual_pattern != expected_pattern) {
                result.is_anomaly = true;
                result.anomaly_type = "pattern_mismatch";
                result.anomaly_score = 1.0;
                result.description = "Pattern mismatch: expected " + expected_pattern +
                                   ", got " + actual_pattern;
            }

            return result;
        }
    };

    std::vector<AnomalyDetector::AnomalyResult> DetectAnomaliesInDataset(
        const std::vector<core::Record>& records) {

        std::vector<AnomalyDetector::AnomalyResult> anomalies;
        AnomalyDetector detector;

        // Collect reference data
        std::unordered_map<std::string, std::vector<double>> numeric_columns;
        std::unordered_map<std::string, std::unordered_map<std::string, size_t>> categorical_columns;
        std::unordered_map<std::string, std::vector<std::chrono::system_clock::time_point>> temporal_columns;

        // First pass: collect data
        for (const auto& record : records) {
            for (const auto& field_name : record.getFieldNames()) {
                auto value_opt = record.getFieldOptional(field_name);
                if (!value_opt.has_value() || !value_opt->has_value()) continue;

                const auto& value = value_opt.value();

                // Try numeric
                try {
                    double num_val = 0;
                    if (value.type() == typeid(int)) {
                        num_val = std::any_cast<int>(value);
                    } else if (value.type() == typeid(double)) {
                        num_val = std::any_cast<double>(value);
                    }
                    numeric_columns[field_name].push_back(num_val);
                } catch (...) {
                    // Try string
                    try {
                        if (value.type() == typeid(std::string)) {
                            auto str_val = std::any_cast<std::string>(value);
                            categorical_columns[field_name][str_val]++;
                        }
                    } catch (...) {
                        // Try temporal
                        try {
                            if (value.type() == typeid(std::chrono::system_clock::time_point)) {
                                auto time_val = std::any_cast<std::chrono::system_clock::time_point>(value);
                                temporal_columns[field_name].push_back(time_val);
                            }
                        } catch (...) {}
                    }
                }
            }
        }

        // Second pass: detect anomalies
        for (size_t i = 0; i < records.size(); ++i) {
            const auto& record = records[i];

            for (const auto& field_name : record.getFieldNames()) {
                auto value_opt = record.getFieldOptional(field_name);
                if (!value_opt.has_value() || !value_opt->has_value()) continue;

                const auto& value = value_opt.value();

                // Check numeric anomalies
                if (numeric_columns.find(field_name) != numeric_columns.end()) {
                    try {
                        double num_val = 0;
                        if (value.type() == typeid(int)) {
                            num_val = std::any_cast<int>(value);
                        } else if (value.type() == typeid(double)) {
                            num_val = std::any_cast<double>(value);
                        }

                        auto result = detector.DetectNumericOutlier(
                            num_val, numeric_columns[field_name]);

                        if (result.is_anomaly) {
                            anomalies.push_back(result);
                        }
                    } catch (...) {}
                }

                // Check categorical anomalies
                if (categorical_columns.find(field_name) != categorical_columns.end()) {
                    try {
                        if (value.type() == typeid(std::string)) {
                            auto str_val = std::any_cast<std::string>(value);
                            auto result = detector.DetectCategoricalAnomaly(
                                str_val, categorical_columns[field_name]);

                            if (result.is_anomaly) {
                                anomalies.push_back(result);
                            }
                        }
                    } catch (...) {}
                }
            }
        }

        return anomalies;
    }

protected:
    std::unique_ptr<omop::test::TestDataGenerator> generator_;
};

TEST_F(AnomalyDetectionTest, DetectNumericOutliers) {
    // Create dataset with intentional outliers
    std::vector<core::Record> records;
    std::random_device rd;
    std::mt19937 gen(rd());
    std::normal_distribution<> dist(100.0, 10.0);

    // Generate normal data
    for (int i = 0; i < 1000; ++i) {
        core::Record record;
        record.setField("value", dist(gen));
        record.setField("id", i);
        records.push_back(record);
    }

    // Add outliers
    core::Record outlier1;
    outlier1.setField("value", 200.0); // Far above mean
    outlier1.setField("id", 1000);
    records.push_back(outlier1);

    core::Record outlier2;
    outlier2.setField("value", -50.0); // Far below mean
    outlier2.setField("id", 1001);
    records.push_back(outlier2);

    // Detect anomalies
    auto anomalies = DetectAnomaliesInDataset(records);

    // Verify outliers detected
    EXPECT_GE(anomalies.size(), 2) << "Should detect at least 2 outliers";

    // Check anomaly scores
    for (const auto& anomaly : anomalies) {
        if (anomaly.anomaly_type == "numeric_outlier") {
            EXPECT_GT(anomaly.anomaly_score, 3.0) << "Outlier should have high z-score";
        }
    }
}

TEST_F(AnomalyDetectionTest, DetectCategoricalAnomalies) {
    // Create dataset with rare categories
    std::vector<core::Record> records;

    // Common categories
    std::vector<std::string> common_values = {"A", "B", "C", "D"};

    for (int i = 0; i < 1000; ++i) {
        core::Record record;
        record.setField("category", common_values[i % common_values.size()]);
        record.setField("id", i);
        records.push_back(record);
    }

    // Add rare categories
    core::Record rare1;
    rare1.setField("category", "Z"); // Very rare
    rare1.setField("id", 1000);
    records.push_back(rare1);

    core::Record rare2;
    rare2.setField("category", "XYZ"); // Never seen before
    rare2.setField("id", 1001);
    records.push_back(rare2);

    // Detect anomalies
    auto anomalies = DetectAnomaliesInDataset(records);

    // Verify rare categories detected
    int rare_count = 0;
    for (const auto& anomaly : anomalies) {
        if (anomaly.anomaly_type == "rare_category") {
            rare_count++;
        }
    }

    EXPECT_GE(rare_count, 2) << "Should detect rare categories";
}

TEST_F(AnomalyDetectionTest, DetectTemporalAnomalies) {
    // Create time series with anomalies
    std::vector<core::Record> records;
    auto base_time = std::chrono::system_clock::now();

    // Generate regular time series
    for (int i = 0; i < 100; ++i) {
        core::Record record;
        auto timestamp = base_time + std::chrono::hours(i);
        record.setField("timestamp", timestamp);
        record.setField("value", 100.0 + i);
        records.push_back(record);
    }

    // Add temporal anomalies
    core::Record future_anomaly;
    auto future_time = base_time + std::chrono::hours(24 * 365); // 1 year in future
    future_anomaly.setField("timestamp", future_time);
    future_anomaly.setField("value", 500.0);
    records.push_back(future_anomaly);

    core::Record past_anomaly;
    auto past_time = base_time - std::chrono::hours(24 * 365); // 1 year in past
    past_anomaly.setField("timestamp", past_time);
    past_anomaly.setField("value", -100.0);
    records.push_back(past_anomaly);

    // Detect anomalies
    auto anomalies = DetectAnomaliesInDataset(records);

    // Verify temporal anomalies detected
    int temporal_count = 0;
    for (const auto& anomaly : anomalies) {
        if (anomaly.anomaly_type == "temporal_outlier") {
            temporal_count++;
            EXPECT_GT(anomaly.anomaly_score, 1.0) << "Temporal anomaly should have high score";
        }
    }

    EXPECT_GE(temporal_count, 2) << "Should detect temporal anomalies";
}

TEST_F(AnomalyDetectionTest, DetectPatternAnomalies) {
    // Create dataset with pattern violations
    std::vector<core::Record> records;

    // Regular pattern (US phone numbers)
    for (int i = 0; i < 100; ++i) {
        core::Record record;
        std::string phone = std::to_string(100 + i % 900) + "-" +
                           std::to_string(100 + (i * 7) % 900) + "-" +
                           std::to_string(1000 + (i * 13) % 9000);
        record.setField("phone", phone);
        records.push_back(record);
    }

    // Add pattern violations
    core::Record bad_pattern1;
    bad_pattern1.setField("phone", "1234567890"); // Missing dashes
    records.push_back(bad_pattern1);

    core::Record bad_pattern2;
    bad_pattern2.setField("phone", "ABC-DEF-GHIJ"); // Letters instead of numbers
    records.push_back(bad_pattern2);

    // Detect pattern anomalies
    AnomalyDetector detector;
    int pattern_anomalies = 0;

    for (const auto& record : records) {
        auto phone = std::any_cast<std::string>(record.getField("phone"));
        auto result = detector.DetectPatternAnomaly(phone, "NNN-NNN-NNNN");
        if (result.is_anomaly) {
            pattern_anomalies++;
        }
    }

    EXPECT_EQ(pattern_anomalies, 2) << "Should detect pattern anomalies";
}

TEST_F(AnomalyDetectionTest, MultivariatAnomalyDetection) {
    // Test detection of anomalies in multivariate data
    std::vector<core::Record> records;
    std::random_device rd;
    std::mt19937 gen(rd());

    // Generate correlated normal data
    for (int i = 0; i < 1000; ++i) {
        core::Record record;
        double x = std::normal_distribution<>(0.0, 1.0)(gen);
        double y = 0.8 * x + 0.2 * std::normal_distribution<>(0.0, 1.0)(gen);

        record.setField("x", x);
        record.setField("y", y);
        record.setField("id", i);
        records.push_back(record);
    }

    // Add multivariate outliers (break correlation)
    core::Record outlier1;
    outlier1.setField("x", 2.0);
    outlier1.setField("y", -2.0); // Should be positive if correlated
    outlier1.setField("id", 1000);
    records.push_back(outlier1);

    core::Record outlier2;
    outlier2.setField("x", -2.0);
    outlier2.setField("y", 3.0); // Should be negative if correlated
    outlier2.setField("id", 1001);
    records.push_back(outlier2);

    // Simple multivariate detection using residuals
    std::vector<double> x_values, y_values;
    for (const auto& record : records) {
        x_values.push_back(std::any_cast<double>(record.getField("x")));
        y_values.push_back(std::any_cast<double>(record.getField("y")));
    }

    // Calculate correlation
    double n = x_values.size();
    double sum_x = std::accumulate(x_values.begin(), x_values.end(), 0.0);
    double sum_y = std::accumulate(y_values.begin(), y_values.end(), 0.0);
    double sum_xy = std::inner_product(x_values.begin(), x_values.end(),
                                      y_values.begin(), 0.0);

    double mean_x = sum_x / n;
    double mean_y = sum_y / n;

    // Simple linear regression
    double slope = (sum_xy - n * mean_x * mean_y) /
                  (std::inner_product(x_values.begin(), x_values.end(),
                                     x_values.begin(), 0.0) - n * mean_x * mean_x);
    double intercept = mean_y - slope * mean_x;

    // Check residuals for last two points
    for (size_t i = n - 2; i < n; ++i) {
        double predicted_y = slope * x_values[i] + intercept;
        double residual = std::abs(y_values[i] - predicted_y);

        EXPECT_GT(residual, 2.0) << "Multivariate outliers should have large residuals";
    }
}

TEST_F(AnomalyDetectionTest, SeasonalAnomalyDetection) {
    // Test detection of anomalies in seasonal data
    std::vector<core::Record> records;
    auto base_time = std::chrono::system_clock::now();

    // Generate seasonal data with anomalies
    for (int day = 0; day < 365; ++day) {
        core::Record record;

        auto timestamp = base_time + std::chrono::hours(24 * day);
        record.setField("date", timestamp);

        // Seasonal pattern (higher in summer)
        double seasonal_value = 100.0 + 30.0 * std::sin(2 * M_PI * day / 365);

        // Add anomaly on specific days
        if (day == 180 || day == 270) {
            seasonal_value *= 2.0; // Double the expected value
        }

        record.setField("value", seasonal_value);
        records.push_back(record);
    }

    // Detect seasonal anomalies
    // Simple approach: compare to same day in previous period
    std::vector<AnomalyDetector::AnomalyResult> seasonal_anomalies;

    for (size_t i = 30; i < records.size(); ++i) {
        double current_value = std::any_cast<double>(records[i].getField("value"));

        // Compare to values from similar days (e.g., 30 days ago)
        std::vector<double> reference_values;
        for (int j = 1; j <= 4; ++j) {
            if (i >= j * 30) {
                reference_values.push_back(
                    std::any_cast<double>(records[i - j * 30].getField("value")));
            }
        }

        if (!reference_values.empty()) {
            AnomalyDetector detector;
            auto result = detector.DetectNumericOutlier(current_value, reference_values);
            if (result.is_anomaly) {
                seasonal_anomalies.push_back(result);
            }
        }
    }

    // Should detect the injected anomalies
    EXPECT_GE(seasonal_anomalies.size(), 2) << "Should detect seasonal anomalies";
}

} // namespace omop::quality::test