// tests/integration/quality/test_data_profiling.cpp
// Data profiling and statistics collection tests

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <random>
#include <numeric>
#include <cmath>
#include "core/record.h"
#include "transform/transformation_engine.h"
#include "test_helpers/test_data_generator.h"

namespace omop::quality::test {

class DataProfilingTest : public ::testing::Test {
protected:
    void SetUp() override {
        generator_ = std::make_unique<omop::test::TestDataGenerator>();
    }

    struct ColumnProfile {
        std::string column_name;
        std::string data_type;
        size_t total_values{0};
        size_t null_count{0};
        size_t distinct_values{0};

        // Numeric statistics
        std::optional<double> min_value;
        std::optional<double> max_value;
        std::optional<double> mean_value;
        std::optional<double> stddev_value;
        std::optional<double> median_value;
        std::vector<double> percentiles;

        // String statistics
        std::optional<size_t> min_length;
        std::optional<size_t> max_length;
        std::optional<double> avg_length;

        // Pattern analysis
        std::unordered_map<std::string, size_t> value_frequencies;
        std::vector<std::string> patterns;

        // Data quality metrics
        double completeness{0.0};
        double uniqueness{0.0};
        double validity{0.0};
    };

    struct DatasetProfile {
        size_t total_records{0};
        size_t total_columns{0};
        std::unordered_map<std::string, ColumnProfile> column_profiles;
        std::chrono::system_clock::time_point profiling_timestamp;
        std::chrono::duration<double> profiling_duration;
    };

    DatasetProfile ProfileRecords(const std::vector<core::Record>& records) {
        auto start_time = std::chrono::system_clock::now();
        DatasetProfile profile;

        if (records.empty()) return profile;

        profile.total_records = records.size();

        // Get all column names
        std::set<std::string> all_columns;
        for (const auto& record : records) {
            for (const auto& field : record.getFieldNames()) {
                all_columns.insert(field);
            }
        }

        profile.total_columns = all_columns.size();

        // Profile each column
        for (const auto& column : all_columns) {
            profile.column_profiles[column] = ProfileColumn(records, column);
        }

        auto end_time = std::chrono::system_clock::now();
        profile.profiling_timestamp = end_time;
        profile.profiling_duration = end_time - start_time;

        return profile;
    }

    ColumnProfile ProfileColumn(const std::vector<core::Record>& records,
                               const std::string& column_name) {
        ColumnProfile profile;
        profile.column_name = column_name;
        profile.total_values = records.size();

        std::vector<double> numeric_values;
        std::vector<std::string> string_values;
        std::set<std::string> unique_values;

        // Collect values
        for (const auto& record : records) {
            auto field_opt = record.getFieldOptional(column_name);

            if (!field_opt.has_value() || !field_opt->has_value()) {
                profile.null_count++;
                continue;
            }

            const auto& value = field_opt.value();

            // Try to extract as numeric
            try {
                double num_val = 0;
                if (value.type() == typeid(int)) {
                    num_val = std::any_cast<int>(value);
                } else if (value.type() == typeid(int64_t)) {
                    num_val = static_cast<double>(std::any_cast<int64_t>(value));
                } else if (value.type() == typeid(double)) {
                    num_val = std::any_cast<double>(value);
                } else if (value.type() == typeid(float)) {
                    num_val = std::any_cast<float>(value);
                }

                numeric_values.push_back(num_val);
                unique_values.insert(std::to_string(num_val));
            } catch (...) {
                // Try as string
                try {
                    std::string str_val;
                    if (value.type() == typeid(std::string)) {
                        str_val = std::any_cast<std::string>(value);
                    } else {
                        str_val = "unknown";
                    }

                    string_values.push_back(str_val);
                    unique_values.insert(str_val);

                    // Update frequency map
                    profile.value_frequencies[str_val]++;
                } catch (...) {
                    // Skip unparseable values
                }
            }
        }

        profile.distinct_values = unique_values.size();

        // Calculate numeric statistics if applicable
        if (!numeric_values.empty()) {
            profile.data_type = "numeric";
            CalculateNumericStats(numeric_values, profile);
        }

        // Calculate string statistics if applicable
        if (!string_values.empty()) {
            if (profile.data_type.empty()) {
                profile.data_type = "string";
            }
            CalculateStringStats(string_values, profile);
        }

        // Calculate data quality metrics
        profile.completeness = 1.0 - (static_cast<double>(profile.null_count) / profile.total_values);
        profile.uniqueness = static_cast<double>(profile.distinct_values) /
                           (profile.total_values - profile.null_count);
        profile.validity = 1.0; // Simplified - all parseable values are considered valid

        return profile;
    }

    void CalculateNumericStats(std::vector<double>& values, ColumnProfile& profile) {
        if (values.empty()) return;

        std::sort(values.begin(), values.end());

        profile.min_value = values.front();
        profile.max_value = values.back();

        // Calculate mean
        double sum = std::accumulate(values.begin(), values.end(), 0.0);
        profile.mean_value = sum / values.size();

        // Calculate standard deviation
        double sq_sum = std::inner_product(values.begin(), values.end(), values.begin(), 0.0);
        profile.stddev_value = std::sqrt(sq_sum / values.size() - (*profile.mean_value) * (*profile.mean_value));

        // Calculate median
        if (values.size() % 2 == 0) {
            profile.median_value = (values[values.size() / 2 - 1] + values[values.size() / 2]) / 2;
        } else {
            profile.median_value = values[values.size() / 2];
        }

        // Calculate percentiles (10th, 25th, 75th, 90th)
        std::vector<double> percentile_points = {0.1, 0.25, 0.75, 0.9};
        for (double p : percentile_points) {
            size_t index = static_cast<size_t>(p * (values.size() - 1));
            profile.percentiles.push_back(values[index]);
        }
    }

    void CalculateStringStats(const std::vector<std::string>& values, ColumnProfile& profile) {
        if (values.empty()) return;

        size_t total_length = 0;
        profile.min_length = std::numeric_limits<size_t>::max();
        profile.max_length = 0;

        std::unordered_map<std::string, size_t> pattern_counts;

        for (const auto& value : values) {
            size_t len = value.length();
            total_length += len;
            profile.min_length = std::min(*profile.min_length, len);
            profile.max_length = std::max(*profile.max_length, len);

            // Detect patterns
            std::string pattern = DetectPattern(value);
            pattern_counts[pattern]++;
        }

        profile.avg_length = static_cast<double>(total_length) / values.size();

        // Get top patterns
        std::vector<std::pair<std::string, size_t>> pattern_vec(
            pattern_counts.begin(), pattern_counts.end());
        std::sort(pattern_vec.begin(), pattern_vec.end(),
            [](const auto& a, const auto& b) { return a.second > b.second; });

        for (size_t i = 0; i < std::min(size_t(5), pattern_vec.size()); ++i) {
            profile.patterns.push_back(pattern_vec[i].first);
        }
    }

    std::string DetectPattern(const std::string& value) {
        std::string pattern;
        for (char c : value) {
            if (std::isdigit(c)) pattern += 'N';
            else if (std::isalpha(c)) pattern += (std::isupper(c) ? 'A' : 'a');
            else if (std::isspace(c)) pattern += '_';
            else pattern += c;
        }
        return pattern;
    }

protected:
    std::unique_ptr<omop::test::TestDataGenerator> generator_;
};

TEST_F(DataProfilingTest, ProfilePatientData) {
    // Generate test patient data
    auto records = generator_->generate_patient_records(1000);

    // Profile the dataset
    auto profile = ProfileRecords(records);

    // Verify profiling results
    EXPECT_EQ(profile.total_records, 1000);
    EXPECT_GT(profile.total_columns, 0);

    // Check specific column profiles
    auto person_id_profile = profile.column_profiles["person_id"];
    EXPECT_EQ(person_id_profile.data_type, "numeric");
    EXPECT_EQ(person_id_profile.null_count, 0);
    EXPECT_EQ(person_id_profile.distinct_values, 1000); // All unique
    EXPECT_DOUBLE_EQ(person_id_profile.completeness, 1.0);
    EXPECT_DOUBLE_EQ(person_id_profile.uniqueness, 1.0);

    // Check gender profile
    auto gender_profile = profile.column_profiles["gender_source_value"];
    EXPECT_GT(gender_profile.distinct_values, 0);
    EXPECT_LE(gender_profile.distinct_values, 3); // M, F, Other

    // Check birth date profile
    auto birth_date_profile = profile.column_profiles["birth_date"];
    EXPECT_GT(birth_date_profile.total_values, 0);
}

TEST_F(DataProfilingTest, ProfileNumericDistributions) {
    // Create records with known distributions
    std::vector<core::Record> records;
    std::random_device rd;
    std::mt19937 gen(rd());

    // Normal distribution
    std::normal_distribution<> normal_dist(100.0, 15.0);

    // Uniform distribution
    std::uniform_real_distribution<> uniform_dist(0.0, 100.0);

    // Exponential distribution
    std::exponential_distribution<> exp_dist(0.1);

    for (int i = 0; i < 10000; ++i) {
        core::Record record;
        record.setField("normal_value", normal_dist(gen));
        record.setField("uniform_value", uniform_dist(gen));
        record.setField("exponential_value", exp_dist(gen));
        records.push_back(record);
    }

    auto profile = ProfileRecords(records);

    // Verify normal distribution profile
    auto normal_profile = profile.column_profiles["normal_value"];
    EXPECT_NEAR(*normal_profile.mean_value, 100.0, 1.0);
    EXPECT_NEAR(*normal_profile.stddev_value, 15.0, 1.0);
    EXPECT_NEAR(*normal_profile.median_value, 100.0, 2.0);

    // Verify uniform distribution profile
    auto uniform_profile = profile.column_profiles["uniform_value"];
    EXPECT_NEAR(*uniform_profile.mean_value, 50.0, 2.0);
    EXPECT_NEAR(*uniform_profile.min_value, 0.0, 1.0);
    EXPECT_NEAR(*uniform_profile.max_value, 100.0, 1.0);

    // Verify exponential distribution profile
    auto exp_profile = profile.column_profiles["exponential_value"];
    EXPECT_NEAR(*exp_profile.mean_value, 10.0, 1.0);
    EXPECT_GT(*exp_profile.max_value, *exp_profile.mean_value * 3);
}

TEST_F(DataProfilingTest, ProfileStringPatterns) {
    // Create records with various string patterns
    std::vector<core::Record> records;

    // Phone numbers
    std::vector<std::string> phone_patterns = {
        "************", "************", "************"
    };

    // SSNs (fake)
    std::vector<std::string> ssn_patterns = {
        "***********", "***********", "***********"
    };

    // Email addresses
    std::vector<std::string> email_patterns = {
        "<EMAIL>", "<EMAIL>", "<EMAIL>"
    };

    // Postal codes
    std::vector<std::string> postal_patterns = {
        "12345", "12345-6789", "23456", "34567-8901"
    };

    for (int i = 0; i < 100; ++i) {
        core::Record record;
        record.setField("phone", phone_patterns[i % phone_patterns.size()]);
        record.setField("ssn", ssn_patterns[i % ssn_patterns.size()]);
        record.setField("email", email_patterns[i % email_patterns.size()]);
        record.setField("postal_code", postal_patterns[i % postal_patterns.size()]);
        records.push_back(record);
    }

    auto profile = ProfileRecords(records);

    // Verify phone pattern detection
    auto phone_profile = profile.column_profiles["phone"];
    EXPECT_FALSE(phone_profile.patterns.empty());
    EXPECT_EQ(phone_profile.patterns[0], "NNN-NNN-NNNN");

    // Verify SSN pattern detection
    auto ssn_profile = profile.column_profiles["ssn"];
    EXPECT_FALSE(ssn_profile.patterns.empty());
    EXPECT_EQ(ssn_profile.patterns[0], "NNN-NN-NNNN");

    // Verify email has variable patterns
    auto email_profile = profile.column_profiles["email"];
    EXPECT_GT(email_profile.patterns.size(), 1);

    // Verify postal code patterns
    auto postal_profile = profile.column_profiles["postal_code"];
    EXPECT_GE(postal_profile.patterns.size(), 2); // Two different formats
}

TEST_F(DataProfilingTest, ProfileDataQualityMetrics) {
    // Create records with known quality issues
    std::vector<core::Record> records;

    for (int i = 0; i < 1000; ++i) {
        core::Record record;

        // Complete field
        record.setField("complete_field", i);

        // Field with nulls (80% complete)
        if (i % 5 != 0) {
            record.setField("partial_field", i);
        }

        // Field with low cardinality
        record.setField("low_cardinality", i % 10);

        // Field with high cardinality
        record.setField("high_cardinality", std::to_string(i));

        records.push_back(record);
    }

    auto profile = ProfileRecords(records);

    // Verify completeness metrics
    EXPECT_DOUBLE_EQ(profile.column_profiles["complete_field"].completeness, 1.0);
    EXPECT_NEAR(profile.column_profiles["partial_field"].completeness, 0.8, 0.01);

    // Verify uniqueness metrics
    EXPECT_DOUBLE_EQ(profile.column_profiles["high_cardinality"].uniqueness, 1.0);
    EXPECT_NEAR(profile.column_profiles["low_cardinality"].uniqueness, 0.01, 0.001);
}

TEST_F(DataProfilingTest, ProfileTimeSeriesData) {
    // Create time series data with trends
    std::vector<core::Record> records;
    auto base_time = std::chrono::system_clock::now();

    for (int i = 0; i < 365; ++i) {
        core::Record record;

        auto timestamp = base_time + std::chrono::hours(24 * i);
        record.setField("timestamp", timestamp);

        // Linear trend
        double linear_value = 100.0 + i * 0.5;
        record.setField("linear_metric", linear_value);

        // Seasonal pattern (monthly)
        double seasonal_value = 100.0 + 20.0 * std::sin(2 * M_PI * i / 30);
        record.setField("seasonal_metric", seasonal_value);

        // Random walk
        static double walk_value = 100.0;
        walk_value += (rand() % 21 - 10) * 0.1;
        record.setField("random_walk", walk_value);

        records.push_back(record);
    }

    auto profile = ProfileRecords(records);

    // Verify linear trend detected
    auto linear_profile = profile.column_profiles["linear_metric"];
    EXPECT_GT(*linear_profile.max_value, *linear_profile.min_value);
    EXPECT_NEAR(*linear_profile.max_value - *linear_profile.min_value, 182.0, 1.0);

    // Verify seasonal pattern range
    auto seasonal_profile = profile.column_profiles["seasonal_metric"];
    EXPECT_NEAR(*seasonal_profile.min_value, 80.0, 5.0);
    EXPECT_NEAR(*seasonal_profile.max_value, 120.0, 5.0);
}

TEST_F(DataProfilingTest, IncrementalProfiling) {
    // Test incremental profiling updates
    DatasetProfile initial_profile;

    // Generate patient records first to get patient IDs
    auto patient_records = generator_->generate_patient_records(100);
    std::vector<int64_t> patient_ids;
    for (const auto& record : patient_records) {
        try {
            patient_ids.push_back(std::any_cast<int64_t>(record.getField("person_id")));
        } catch (...) {
            patient_ids.push_back(static_cast<int64_t>(patient_ids.size() + 1));
        }
    }

    // Process data in chunks
    const int chunk_size = 100;
    const int num_chunks = 10;

    for (int chunk = 0; chunk < num_chunks; ++chunk) {
        auto records = generator_->generate_condition_records(chunk_size, patient_ids);

        // Profile this chunk
        auto chunk_profile = ProfileRecords(records);

        // Merge profiles (simplified)
        if (chunk == 0) {
            initial_profile = chunk_profile;
        } else {
            initial_profile.total_records += chunk_profile.total_records;

            // Update column profiles
            for (auto& [col_name, col_profile] : chunk_profile.column_profiles) {
                auto& existing = initial_profile.column_profiles[col_name];
                existing.total_values += col_profile.total_values;
                existing.null_count += col_profile.null_count;

                // Update min/max
                if (col_profile.min_value.has_value()) {
                    if (!existing.min_value.has_value() ||
                        *col_profile.min_value < *existing.min_value) {
                        existing.min_value = col_profile.min_value;
                    }
                }

                if (col_profile.max_value.has_value()) {
                    if (!existing.max_value.has_value() ||
                        *col_profile.max_value > *existing.max_value) {
                        existing.max_value = col_profile.max_value;
                    }
                }
            }
        }
    }

    // Verify incremental profiling
    EXPECT_EQ(initial_profile.total_records, chunk_size * num_chunks);
    EXPECT_GT(initial_profile.column_profiles.size(), 0);
}

// Tests UK-specific data profiling with NHS numbers and postal codes
TEST_F(DataProfilingTest, UKDataProfiling) {
    // Generate UK-specific test data
    std::vector<core::Record> uk_records;
    
    // UK postal codes and NHS numbers
    std::vector<std::string> uk_postcodes = {
        "SW1A 1AA", "M1 1AA", "B33 8TH", "CR2 6XH", "DN55 1PT",
        "W1A 1AA", "M60 1NW", "LS1 8JQ", "L1 8JQ", "EH1 1BB"
    };
    
    std::vector<std::string> nhs_numbers = {
        "**********", "**********", "**********", "**********", "**********",
        "**********", "**********", "**********", "**********", "**********"
    };

    for (int i = 0; i < 100; ++i) {
        core::Record record;
        record.setField("nhs_number", nhs_numbers[i % nhs_numbers.size()]);
        record.setField("postal_code", uk_postcodes[i % uk_postcodes.size()]);
        record.setField("date_of_birth", "15/03/1985"); // UK date format
        record.setField("currency_amount", "£1,234.56"); // UK currency format
        uk_records.push_back(record);
    }

    auto profile = ProfileRecords(uk_records);

    // Verify NHS number profiling
    auto nhs_profile = profile.column_profiles["nhs_number"];
    EXPECT_EQ(nhs_profile.total_values, 100);
    EXPECT_EQ(nhs_profile.distinct_values, 10); // 10 unique NHS numbers
    EXPECT_EQ(nhs_profile.null_count, 0);
    
    // Check NHS number pattern detection
    bool found_nhs_pattern = false;
    for (const auto& pattern : nhs_profile.patterns) {
        if (pattern.find("\\d{10}") != std::string::npos) {
            found_nhs_pattern = true;
            break;
        }
    }
    EXPECT_TRUE(found_nhs_pattern);

    // Verify UK postal code profiling
    auto postcode_profile = profile.column_profiles["postal_code"];
    EXPECT_EQ(postcode_profile.total_values, 100);
    EXPECT_EQ(postcode_profile.distinct_values, 10); // 10 unique postal codes
    EXPECT_EQ(postcode_profile.null_count, 0);
    
    // Check UK postal code pattern detection
    bool found_postcode_pattern = false;
    for (const auto& pattern : postcode_profile.patterns) {
        if (pattern.find("[A-Z]{1,2}\\d[A-Z\\d]? ?\\d[A-Z]{2}") != std::string::npos) {
            found_postcode_pattern = true;
            break;
        }
    }
    EXPECT_TRUE(found_postcode_pattern);

    // Verify UK date format profiling
    auto date_profile = profile.column_profiles["date_of_birth"];
    EXPECT_EQ(date_profile.total_values, 100);
    EXPECT_EQ(date_profile.distinct_values, 1); // All same date
    EXPECT_EQ(date_profile.null_count, 0);
    
    // Check UK date pattern detection
    bool found_date_pattern = false;
    for (const auto& pattern : date_profile.patterns) {
        if (pattern.find("\\d{2}/\\d{2}/\\d{4}") != std::string::npos) {
            found_date_pattern = true;
            break;
        }
    }
    EXPECT_TRUE(found_date_pattern);

    // Verify UK currency format profiling
    auto currency_profile = profile.column_profiles["currency_amount"];
    EXPECT_EQ(currency_profile.total_values, 100);
    EXPECT_EQ(currency_profile.distinct_values, 1); // All same amount
    EXPECT_EQ(currency_profile.null_count, 0);
    
    // Check UK currency pattern detection
    bool found_currency_pattern = false;
    for (const auto& pattern : currency_profile.patterns) {
        if (pattern.find("£\\d{1,3}(,\\d{3})*(\\.\\d{2})?") != std::string::npos) {
            found_currency_pattern = true;
            break;
        }
    }
    EXPECT_TRUE(found_currency_pattern);
}

// Tests UK-specific data quality validation
TEST_F(DataProfilingTest, UKDataQualityValidation) {
    // Generate UK data with quality issues
    std::vector<core::Record> uk_records;
    
    // Valid UK data
    for (int i = 0; i < 80; ++i) {
        core::Record record;
        record.setField("nhs_number", std::to_string(********** + i));
        record.setField("postal_code", "SW1A 1AA");
        record.setField("date_of_birth", "15/03/1985");
        record.setField("currency_amount", "£1,234.56");
        uk_records.push_back(record);
    }
    
    // Invalid UK data (for quality testing)
    for (int i = 0; i < 20; ++i) {
        core::Record record;
        record.setField("nhs_number", "123456789"); // Too short
        record.setField("postal_code", "INVALID"); // Invalid format
        record.setField("date_of_birth", "1985-03-15"); // Wrong format
        record.setField("currency_amount", "$1,234.56"); // Wrong currency
        uk_records.push_back(record);
    }

    auto profile = ProfileRecords(uk_records);

    // Verify NHS number quality metrics
    auto nhs_profile = profile.column_profiles["nhs_number"];
    EXPECT_EQ(nhs_profile.total_values, 100);
    EXPECT_EQ(nhs_profile.distinct_values, 81); // 80 valid + 1 invalid
    EXPECT_EQ(nhs_profile.null_count, 0);
    
    // Calculate quality metrics
    double nhs_quality = static_cast<double>(nhs_profile.distinct_values - 1) / nhs_profile.total_values;
    EXPECT_GT(nhs_quality, 0.8); // At least 80% quality

    // Verify postal code quality metrics
    auto postcode_profile = profile.column_profiles["postal_code"];
    EXPECT_EQ(postcode_profile.total_values, 100);
    EXPECT_EQ(postcode_profile.distinct_values, 2); // 1 valid + 1 invalid
    EXPECT_EQ(postcode_profile.null_count, 0);
    
    double postcode_quality = static_cast<double>(postcode_profile.distinct_values - 1) / postcode_profile.total_values;
    EXPECT_GT(postcode_quality, 0.8); // At least 80% quality

    // Verify date format quality metrics
    auto date_profile = profile.column_profiles["date_of_birth"];
    EXPECT_EQ(date_profile.total_values, 100);
    EXPECT_EQ(date_profile.distinct_values, 2); // 1 valid + 1 invalid
    EXPECT_EQ(date_profile.null_count, 0);
    
    double date_quality = static_cast<double>(date_profile.distinct_values - 1) / date_profile.total_values;
    EXPECT_GT(date_quality, 0.8); // At least 80% quality

    // Verify currency format quality metrics
    auto currency_profile = profile.column_profiles["currency_amount"];
    EXPECT_EQ(currency_profile.total_values, 100);
    EXPECT_EQ(currency_profile.distinct_values, 2); // 1 valid + 1 invalid
    EXPECT_EQ(currency_profile.null_count, 0);
    
    double currency_quality = static_cast<double>(currency_profile.distinct_values - 1) / currency_profile.total_values;
    EXPECT_GT(currency_quality, 0.8); // At least 80% quality
}

} // namespace omop::quality::test