// Test conditional workflow paths and decision logic
#include <gtest/gtest.h>
#include "core/job_manager.h"
#include "core/job_scheduler.h"
#include "core/pipeline.h"
#include "service/etl_service.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"
#include <condition_variable>

namespace omop::test::integration::workflow {

using namespace omop::core;
using namespace omop::service;
using namespace std::chrono_literals;

class ConditionalWorkflowTest : public IntegrationTestBase, public DatabaseFixture {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        DatabaseFixture::SetUp();

        auto config = create_test_config();

        job_manager_ = std::make_unique<JobManager>(config, logger_);
        job_manager_->start();

        scheduler_ = std::make_unique<JobScheduler>(job_manager_);
        scheduler_->start();

        pipeline_manager_ = std::make_shared<PipelineManager>(4);
        etl_service_ = std::make_unique<ETLService>(config, pipeline_manager_);

        create_test_environment();
    }

    void TearDown() override {
        scheduler_->stop();
        job_manager_->stop();
        pipeline_manager_->shutdown();

        DatabaseFixture::TearDown();
        IntegrationTestBase::TearDown();
    }

    void create_test_environment() {
        // Create decision tables
        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS workflow_decisions (
                id SERIAL PRIMARY KEY,
                job_id VARCHAR(100),
                decision_type VARCHAR(50),
                condition_evaluated TEXT,
                result BOOLEAN,
                branch_taken VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )");

        // Create metric tables for decision making
        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS data_quality_metrics (
                id SERIAL PRIMARY KEY,
                table_name VARCHAR(100),
                metric_name VARCHAR(100),
                metric_value DECIMAL(10,4),
                threshold_value DECIMAL(10,4),
                passed BOOLEAN,
                evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )");

        // Create workflow state table
        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS workflow_state (
                workflow_id VARCHAR(100) PRIMARY KEY,
                current_state VARCHAR(50),
                state_data JSONB,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )");
    }

    struct ConditionalJobConfig {
        std::string name;
        std::string condition_type;
        std::unordered_map<std::string, std::any> condition_params;
        std::string true_branch_job;
        std::string false_branch_job;
        std::optional<std::string> default_branch_job;
    };

    std::string create_conditional_workflow(const ConditionalJobConfig& config) {
        // Create evaluation job
        JobConfig eval_job;
        eval_job.job_name = config.name + "_evaluation";
        eval_job.job_id = generate_unique_id(eval_job.job_name);
        eval_job.pipeline_config_path = "conditional_eval_pipeline.yaml";
        eval_job.parameters["condition_type"] = config.condition_type;

        for (const auto& [key, value] : config.condition_params) {
            eval_job.parameters[key] = value;
        }

        auto eval_job_id = job_manager_->submitJob(eval_job);

        // Create branch jobs
        JobConfig true_branch;
        true_branch.job_name = config.true_branch_job;
        true_branch.job_id = generate_unique_id(true_branch.job_name);
        true_branch.pipeline_config_path = "branch_pipeline.yaml";

        JobConfig false_branch;
        false_branch.job_name = config.false_branch_job;
        false_branch.job_id = generate_unique_id(false_branch.job_name);
        false_branch.pipeline_config_path = "branch_pipeline.yaml";

        // Set up conditional execution
        scheduler_->addConditionalDependency(
            true_branch.job_id, eval_job_id,
            [this, eval_job_id](const JobInfo& info) {
                return check_job_condition_result(eval_job_id, true);
            });

        scheduler_->addConditionalDependency(
            false_branch.job_id, eval_job_id,
            [this, eval_job_id](const JobInfo& info) {
                return check_job_condition_result(eval_job_id, false);
            });

        job_manager_->submitJob(true_branch);
        job_manager_->submitJob(false_branch);

        return eval_job_id;
    }

    bool check_job_condition_result(const std::string& job_id, bool expected_result) {
        auto result = execute_scalar<bool>(
            std::format("SELECT result FROM workflow_decisions "
                       "WHERE job_id = '{}' ORDER BY created_at DESC LIMIT 1",
                       job_id)
        );
        return result == expected_result;
    }

protected:
    std::unique_ptr<JobManager> job_manager_;
    std::unique_ptr<JobScheduler> scheduler_;
    std::shared_ptr<PipelineManager> pipeline_manager_;
    std::unique_ptr<ETLService> etl_service_;
};

TEST_F(ConditionalWorkflowTest, DataQualityBasedBranching) {
    // Test workflow branching based on data quality thresholds

    // Insert quality metrics
    execute_sql(R"(
        INSERT INTO data_quality_metrics (table_name, metric_name, metric_value, threshold_value, passed)
        VALUES
            ('patient_data', 'completeness', 0.95, 0.90, true),
            ('patient_data', 'validity', 0.88, 0.85, true),
            ('patient_data', 'uniqueness', 0.99, 0.95, true)
    )");

    ConditionalJobConfig config;
    config.name = "quality_check_workflow";
    config.condition_type = "data_quality";
    config.condition_params["table_name"] = "patient_data";
    config.condition_params["min_completeness"] = 0.90;
    config.condition_params["min_validity"] = 0.85;
    config.true_branch_job = "standard_processing";
    config.false_branch_job = "data_cleansing";

    auto workflow_id = create_conditional_workflow(config);

    // Wait for workflow completion
    std::this_thread::sleep_for(5s);

    // Check which branch was executed
    auto decision = execute_query<std::tuple<std::string, bool>>(
        std::format("SELECT branch_taken, result FROM workflow_decisions "
                   "WHERE job_id = '{}'", workflow_id)
    );

    ASSERT_FALSE(decision.empty());
    auto [branch, result] = decision[0];

    // Should take true branch (standard processing) as quality is good
    EXPECT_TRUE(result);
    EXPECT_EQ(branch, "standard_processing");

    // Verify the correct job was executed
    auto true_job = job_manager_->getJobsByStatus(JobStatus::Completed);
    auto false_job = job_manager_->getJobsByStatus(JobStatus::Cancelled);

    EXPECT_FALSE(true_job.empty());
}

TEST_F(ConditionalWorkflowTest, ThresholdBasedProcessing) {
    // Test different processing paths based on data volume thresholds

    // Create test data with varying volumes
    execute_sql(R"(
        CREATE TABLE IF NOT EXISTS data_volume_test (
            id SERIAL PRIMARY KEY,
            batch_id INTEGER,
            data_size_mb INTEGER,
            record_count INTEGER
        )
    )");

    // Test cases for different volume scenarios
    std::vector<std::tuple<int, int, std::string>> test_cases = {
        {100, 10000, "batch_processing"},      // Small volume
        {5000, 1000000, "parallel_processing"}, // Large volume
        {50000, 10000000, "distributed_processing"} // Very large volume
    };

    for (const auto& [size_mb, record_count, expected_branch] : test_cases) {
        // Insert volume data
        execute_sql(std::format(
            "INSERT INTO data_volume_test (batch_id, data_size_mb, record_count) "
            "VALUES (1, {}, {})", size_mb, record_count
        ));

        ConditionalJobConfig config;
        config.name = std::format("volume_based_workflow_{}", size_mb);
        config.condition_type = "volume_threshold";
        config.condition_params["size_threshold_mb"] = 1000;
        config.condition_params["record_threshold"] = 500000;
        config.true_branch_job = "parallel_processing";
        config.false_branch_job = "batch_processing";

        auto workflow_id = create_conditional_workflow(config);

        // Wait for completion
        std::this_thread::sleep_for(3s);

        // Verify the correct processing path was taken
        auto branch = execute_scalar<std::string>(
            std::format("SELECT branch_taken FROM workflow_decisions "
                       "WHERE job_id = '{}' ORDER BY created_at DESC LIMIT 1",
                       workflow_id)
        );

        // For very large volumes, we'd need a more complex workflow
        if (expected_branch == "distributed_processing") {
            // This would be handled by additional logic
            EXPECT_TRUE(branch == "parallel_processing" ||
                       branch == "distributed_processing");
        } else {
            EXPECT_EQ(branch, expected_branch);
        }

        // Clean up for next iteration
        execute_sql("TRUNCATE TABLE data_volume_test");
    }
}

TEST_F(ConditionalWorkflowTest, MultiConditionDecisionTree) {
    // Test complex decision tree with multiple conditions

    // Create state data
    execute_sql(R"(
        INSERT INTO workflow_state (workflow_id, current_state, state_data)
        VALUES ('multi_condition_test', 'initialized', '{
            "source_system": "legacy",
            "data_format": "csv",
            "has_header": true,
            "encoding": "UTF-8",
            "validation_level": "strict"
        }'::jsonb)
    )");

    // Define multi-level decision tree
    struct DecisionNode {
        std::string condition;
        std::string true_action;
        std::string false_action;
        std::optional<DecisionNode> true_child;
        std::optional<DecisionNode> false_child;
    };

    // Root decision: Check source system
    ConditionalJobConfig root_config;
    root_config.name = "check_source_system";
    root_config.condition_type = "state_check";
    root_config.condition_params["state_field"] = "source_system";
    root_config.condition_params["expected_value"] = "legacy";
    root_config.true_branch_job = "legacy_parser";
    root_config.false_branch_job = "modern_parser";

    auto root_job_id = create_conditional_workflow(root_config);

    // Wait for first level decision
    std::this_thread::sleep_for(3s);

    // Second level: Check data format (for legacy path)
    ConditionalJobConfig format_config;
    format_config.name = "check_data_format";
    format_config.condition_type = "state_check";
    format_config.condition_params["state_field"] = "data_format";
    format_config.condition_params["expected_value"] = "csv";
    format_config.true_branch_job = "csv_processor";
    format_config.false_branch_job = "xml_processor";

    // Add dependency on legacy parser completion
    auto format_job_id = create_conditional_workflow(format_config);

    // Third level: Check validation requirements
    ConditionalJobConfig validation_config;
    validation_config.name = "check_validation_level";
    validation_config.condition_type = "state_check";
    validation_config.condition_params["state_field"] = "validation_level";
    validation_config.condition_params["expected_value"] = "strict";
    validation_config.true_branch_job = "strict_validator";
    validation_config.false_branch_job = "basic_validator";

    auto validation_job_id = create_conditional_workflow(validation_config);

    // Wait for full workflow completion
    std::this_thread::sleep_for(10s);

    // Verify decision path
    auto decisions = execute_query<std::tuple<std::string, std::string, bool>>(
        "SELECT decision_type, branch_taken, result FROM workflow_decisions "
        "ORDER BY created_at"
    );

    ASSERT_GE(decisions.size(), 3);

    // Verify the decision tree was followed correctly
    auto [type1, branch1, result1] = decisions[0];
    EXPECT_EQ(type1, "state_check");
    EXPECT_TRUE(result1); // Legacy system
    EXPECT_EQ(branch1, "legacy_parser");

    auto [type2, branch2, result2] = decisions[1];
    EXPECT_EQ(type2, "state_check");
    EXPECT_TRUE(result2); // CSV format
    EXPECT_EQ(branch2, "csv_processor");

    auto [type3, branch3, result3] = decisions[2];
    EXPECT_EQ(type3, "state_check");
    EXPECT_TRUE(result3); // Strict validation
    EXPECT_EQ(branch3, "strict_validator");
}

TEST_F(ConditionalWorkflowTest, DynamicWorkflowGeneration) {
    // Test dynamic workflow generation based on metadata

    // Create workflow template metadata
    execute_sql(R"(
        CREATE TABLE IF NOT EXISTS workflow_templates (
            id SERIAL PRIMARY KEY,
            template_name VARCHAR(100),
            source_pattern VARCHAR(200),
            conditions JSONB,
            actions JSONB
        )
    )");

    execute_sql(R"(
        INSERT INTO workflow_templates (template_name, source_pattern, conditions, actions)
        VALUES ('patient_import', 'patient_*.csv',
            '{
                "file_size_check": {
                    "threshold_mb": 100,
                    "operator": ">"
                },
                "record_count_check": {
                    "threshold": 10000,
                    "operator": ">"
                }
            }'::jsonb,
            '{
                "large_file": {
                    "action": "split_and_process",
                    "parallelism": 4
                },
                "small_file": {
                    "action": "direct_process",
                    "parallelism": 1
                }
            }'::jsonb
        )
    )");

    // Simulate file discovery
    std::vector<std::pair<std::string, int>> discovered_files = {
        {"patient_001.csv", 50},   // 50 MB
        {"patient_002.csv", 150},  // 150 MB
        {"patient_003.csv", 500}   // 500 MB
    };

    std::vector<std::string> workflow_ids;

    for (const auto& [filename, size_mb] : discovered_files) {
        // Generate workflow based on template
        auto template_data = execute_query<std::tuple<std::string, std::string>>(
            "SELECT conditions::text, actions::text FROM workflow_templates "
            "WHERE template_name = 'patient_import'"
        );

        ASSERT_FALSE(template_data.empty());

        // Create dynamic workflow
        ConditionalJobConfig config;
        config.name = std::format("dynamic_workflow_{}", filename);
        config.condition_type = "file_size";
        config.condition_params["filename"] = filename;
        config.condition_params["size_mb"] = size_mb;
        config.condition_params["threshold_mb"] = 100;

        if (size_mb > 100) {
            config.true_branch_job = "split_and_process";
            config.false_branch_job = "direct_process";
        } else {
            config.true_branch_job = "direct_process";
            config.false_branch_job = "skip_processing";
        }

        auto workflow_id = create_conditional_workflow(config);
        workflow_ids.push_back(workflow_id);
    }

    // Wait for all workflows
    std::this_thread::sleep_for(10s);

    // Verify workflows were generated and executed correctly
    for (size_t i = 0; i < workflow_ids.size(); ++i) {
        auto branch = execute_scalar<std::string>(
            std::format("SELECT branch_taken FROM workflow_decisions "
                       "WHERE job_id = '{}'", workflow_ids[i])
        );

        if (discovered_files[i].second > 100) {
            EXPECT_EQ(branch, "split_and_process");
        } else {
            EXPECT_EQ(branch, "direct_process");
        }
    }
}

TEST_F(ConditionalWorkflowTest, TimeBasedConditionalExecution) {
    // Test conditional execution based on time windows and schedules

    // Create schedule configuration
    execute_sql(R"(
        CREATE TABLE IF NOT EXISTS processing_windows (
            id SERIAL PRIMARY KEY,
            window_name VARCHAR(50),
            start_hour INTEGER,
            end_hour INTEGER,
            processing_type VARCHAR(50),
            max_concurrent_jobs INTEGER
        )
    )");

    execute_sql(R"(
        INSERT INTO processing_windows (window_name, start_hour, end_hour, processing_type, max_concurrent_jobs)
        VALUES
            ('business_hours', 8, 18, 'light_processing', 2),
            ('after_hours', 18, 23, 'heavy_processing', 8),
            ('overnight', 23, 8, 'batch_processing', 16)
    )");

    // Get current hour
    auto current_hour = std::chrono::duration_cast<std::chrono::hours>(
        std::chrono::system_clock::now().time_since_epoch()
    ).count() % 24;

    ConditionalJobConfig config;
    config.name = "time_based_workflow";
    config.condition_type = "time_window";
    config.condition_params["current_hour"] = current_hour;

    // Determine expected processing type based on current time
    std::string expected_processing;
    if (current_hour >= 8 && current_hour < 18) {
        expected_processing = "light_processing";
        config.true_branch_job = "light_processing";
        config.false_branch_job = "queue_for_later";
    } else if (current_hour >= 18 && current_hour < 23) {
        expected_processing = "heavy_processing";
        config.true_branch_job = "heavy_processing";
        config.false_branch_job = "light_processing";
    } else {
        expected_processing = "batch_processing";
        config.true_branch_job = "batch_processing";
        config.false_branch_job = "immediate_processing";
    }

    auto workflow_id = create_conditional_workflow(config);

    // Wait for execution
    std::this_thread::sleep_for(5s);

    // Verify time-based decision
    auto decision_data = execute_query<std::tuple<std::string, std::string>>(
        std::format("SELECT condition_evaluated, branch_taken "
                   "FROM workflow_decisions WHERE job_id = '{}'",
                   workflow_id)
    );

    ASSERT_FALSE(decision_data.empty());
    auto [condition, branch] = decision_data[0];

    // Branch should match expected processing type for current time
    EXPECT_TRUE(branch == expected_processing ||
                branch == "light_processing" ||
                branch == "queue_for_later");
}

TEST_F(ConditionalWorkflowTest, EventDrivenConditionalWorkflow) {
    // Test workflow triggered and branched based on external events

    // Create event table
    execute_sql(R"(
        CREATE TABLE IF NOT EXISTS workflow_events (
            id SERIAL PRIMARY KEY,
            event_type VARCHAR(50),
            event_source VARCHAR(100),
            event_data JSONB,
            processed BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    )");

    // Simulate different types of events
    std::vector<std::tuple<std::string, std::string, std::string>> events = {
        {"file_arrival", "ftp_server", R"({"filename": "patients.csv", "size": 1024})"},
        {"api_webhook", "external_system", R"({"action": "update", "entity": "medication"})"},
        {"scheduled_task", "cron", R"({"task": "daily_validation", "priority": "high"})"},
        {"error_alert", "monitoring", R"({"severity": "critical", "component": "loader"})"}
    };

    for (const auto& [type, source, data] : events) {
        execute_sql(std::format(
            "INSERT INTO workflow_events (event_type, event_source, event_data) "
            "VALUES ('{}', '{}', '{}'::jsonb)",
            type, source, data
        ));

        // Create event-driven workflow
        ConditionalJobConfig config;
        config.name = std::format("event_driven_{}", type);
        config.condition_type = "event_based";
        config.condition_params["event_type"] = type;
        config.condition_params["event_source"] = source;

        // Define branches based on event type
        if (type == "file_arrival") {
            config.true_branch_job = "file_processing";
            config.false_branch_job = "skip_processing";
        } else if (type == "api_webhook") {
            config.true_branch_job = "api_sync";
            config.false_branch_job = "log_only";
        } else if (type == "scheduled_task") {
            config.true_branch_job = "execute_task";
            config.false_branch_job = "queue_task";
        } else if (type == "error_alert") {
            config.true_branch_job = "error_recovery";
            config.false_branch_job = "notify_admin";
        }

        auto workflow_id = create_conditional_workflow(config);

        // Mark event as processed
        execute_sql(std::format(
            "UPDATE workflow_events SET processed = TRUE "
            "WHERE event_type = '{}' AND event_source = '{}'",
            type, source
        ));
    }

    // Wait for all event-driven workflows
    std::this_thread::sleep_for(10s);

    // Verify each event triggered appropriate workflow
    auto processed_events = execute_scalar<int>(
        "SELECT COUNT(*) FROM workflow_events WHERE processed = TRUE"
    );

    EXPECT_EQ(processed_events, events.size());

    // Verify workflow decisions
    auto decisions = execute_query<std::tuple<std::string, std::string>>(
        "SELECT decision_type, branch_taken FROM workflow_decisions "
        "WHERE decision_type = 'event_based'"
    );

    EXPECT_EQ(decisions.size(), events.size());
}

} // namespace omop::test::integration::workflow