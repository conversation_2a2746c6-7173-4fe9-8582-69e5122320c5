// Test complex workflow orchestration and execution patterns
#include <gtest/gtest.h>
#include "core/job_manager.h"
#include "core/job_scheduler.h"
#include "core/pipeline.h"
#include "service/etl_service.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"
#include <future>
#include <random>

namespace omop::test::integration::workflow {

using namespace omop::core;
using namespace omop::service;
using namespace std::chrono_literals;

class WorkflowOrchestrationTest : public IntegrationTestBase, public DatabaseFixture {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        DatabaseFixture::SetUp();

        auto config = create_test_config();

        // Initialize services
        job_manager_ = std::make_unique<JobManager>(config, logger_);
        job_manager_->start();

        scheduler_ = std::make_unique<JobScheduler>(job_manager_);
        scheduler_->start();

        pipeline_manager_ = std::make_shared<PipelineManager>(4);
        etl_service_ = std::make_unique<ETLService>(config, pipeline_manager_);

        // Create test tables
        create_test_tables();
    }

    void TearDown() override {
        scheduler_->stop();
        job_manager_->stop();
        pipeline_manager_->shutdown();

        DatabaseFixture::TearDown();
        IntegrationTestBase::TearDown();
    }

    void create_test_tables() {
        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS workflow_test_source (
                id SERIAL PRIMARY KEY,
                patient_id INTEGER,
                event_date DATE,
                status VARCHAR(50),
                value DECIMAL(10,2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )");

        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS workflow_test_staging (
                id SERIAL PRIMARY KEY,
                source_id INTEGER,
                patient_id INTEGER,
                processed_date TIMESTAMP,
                validation_status VARCHAR(50),
                transformation_applied BOOLEAN DEFAULT FALSE
            )
        )");

        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS workflow_test_target (
                id SERIAL PRIMARY KEY,
                person_id INTEGER,
                observation_date DATE,
                concept_id INTEGER,
                value_as_number DECIMAL(10,2),
                unit_concept_id INTEGER,
                load_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )");
    }

    void populate_test_data(size_t record_count) {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> patient_dist(1, 100);
        std::uniform_real_distribution<> value_dist(0.0, 1000.0);

        for (size_t i = 0; i < record_count; ++i) {
            execute_sql(std::format(
                "INSERT INTO workflow_test_source (patient_id, event_date, status, value) "
                "VALUES ({}, CURRENT_DATE - INTERVAL '{} days', '{}', {:.2f})",
                patient_dist(gen),
                i % 365,
                (i % 3 == 0) ? "completed" : "pending",
                value_dist(gen)
            ));
        }
    }

    ETLJobRequest create_workflow_job_request(const std::string& name,
                                            const std::string& source_table,
                                            const std::string& target_table) {
        ETLJobRequest request;
        request.name = name;
        request.description = "Workflow test job";
        request.source_table = source_table;
        request.target_table = target_table;
        request.extractor_type = "database";
        request.loader_type = "database";

        request.extractor_config["table"] = source_table;
        request.extractor_config["batch_size"] = 100;

        request.loader_config["table"] = target_table;
        request.loader_config["batch_size"] = 100;

        request.pipeline_config.batch_size = 100;
        request.pipeline_config.max_parallel_batches = 4;

        return request;
    }

protected:
    std::unique_ptr<JobManager> job_manager_;
    std::unique_ptr<JobScheduler> scheduler_;
    std::shared_ptr<PipelineManager> pipeline_manager_;
    std::unique_ptr<ETLService> etl_service_;
};

TEST_F(WorkflowOrchestrationTest, MultiStageETLWorkflow) {
    // Test multi-stage ETL workflow: Extract -> Stage -> Transform -> Load
    populate_test_data(1000);

    // Stage 1: Extract from source to staging
    auto extract_request = create_workflow_job_request(
        "extract_to_staging",
        "workflow_test_source",
        "workflow_test_staging"
    );

    auto extract_job_id = etl_service_->create_job(extract_request);

    // Wait for extraction to complete
    bool extract_complete = false;
    etl_service_->set_completion_callback(
        [&extract_complete, &extract_job_id](const std::string& job_id, const ETLJobResult& result) {
            if (job_id == extract_job_id) {
                extract_complete = true;
                EXPECT_EQ(result.status, JobStatus::Completed);
                EXPECT_GT(result.processed_records, 0);
            }
        });

    // Wait for extraction
    auto start = std::chrono::steady_clock::now();
    while (!extract_complete &&
           std::chrono::steady_clock::now() - start < 30s) {
        std::this_thread::sleep_for(100ms);
    }
    ASSERT_TRUE(extract_complete);

    // Stage 2: Transform and load to target
    auto transform_request = create_workflow_job_request(
        "transform_to_target",
        "workflow_test_staging",
        "workflow_test_target"
    );

    // Add transformation rules
    transform_request.extractor_config["filter"] = "validation_status != 'failed'";

    auto transform_job_id = etl_service_->create_job(transform_request);

    // Wait for transformation to complete
    bool transform_complete = false;
    etl_service_->set_completion_callback(
        [&transform_complete, &transform_job_id](const std::string& job_id, const ETLJobResult& result) {
            if (job_id == transform_job_id) {
                transform_complete = true;
                EXPECT_EQ(result.status, JobStatus::Completed);
            }
        });

    start = std::chrono::steady_clock::now();
    while (!transform_complete &&
           std::chrono::steady_clock::now() - start < 30s) {
        std::this_thread::sleep_for(100ms);
    }
    ASSERT_TRUE(transform_complete);

    // Verify data flow
    auto staging_count = execute_scalar<int>(
        "SELECT COUNT(*) FROM workflow_test_staging");
    auto target_count = execute_scalar<int>(
        "SELECT COUNT(*) FROM workflow_test_target");

    EXPECT_GT(staging_count, 0);
    EXPECT_GT(target_count, 0);
    EXPECT_LE(target_count, staging_count); // Some records may be filtered
}

TEST_F(WorkflowOrchestrationTest, ParallelTableProcessing) {
    // Test parallel processing of multiple tables
    std::vector<std::string> tables = {
        "person", "observation_period", "visit_occurrence",
        "condition_occurrence", "drug_exposure"
    };

    // Create test tables
    for (const auto& table : tables) {
        execute_sql(std::format(R"(
            CREATE TABLE IF NOT EXISTS test_source_{} (
                id SERIAL PRIMARY KEY,
                data JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )", table));

        // Insert test data
        for (int i = 0; i < 100; ++i) {
            execute_sql(std::format(
                "INSERT INTO test_source_{} (data) VALUES ('{}')",
                table, "{\"value\": " + std::to_string(i) + "}"
            ));
        }
    }

    // Submit jobs for all tables in parallel
    auto job_map = etl_service_->run_all_tables(true);

    EXPECT_EQ(job_map.size(), tables.size());

    // Track completion
    std::atomic<size_t> completed_count{0};
    std::map<std::string, ETLJobResult> results;
    std::mutex results_mutex;

    etl_service_->set_completion_callback(
        [&completed_count, &results, &results_mutex](const std::string& job_id, const ETLJobResult& result) {
            std::lock_guard<std::mutex> lock(results_mutex);
            results[job_id] = result;
            completed_count++;
        });

    // Wait for all jobs to complete
    auto start = std::chrono::steady_clock::now();
    while (completed_count < tables.size() &&
           std::chrono::steady_clock::now() - start < 60s) {
        std::this_thread::sleep_for(100ms);
    }

    EXPECT_EQ(completed_count, tables.size());

    // Verify all jobs completed successfully
    for (const auto& [table, job_id] : job_map) {
        auto it = results.find(job_id);
        ASSERT_NE(it, results.end());
        EXPECT_EQ(it->second.status, JobStatus::Completed);
        EXPECT_GT(it->second.processed_records, 0);
    }
}

TEST_F(WorkflowOrchestrationTest, ConditionalWorkflowBranching) {
    // Test workflow with conditional branching based on data quality
    populate_test_data(500);

    // Data quality check job
    ETLJobRequest quality_check;
    quality_check.name = "data_quality_check";
    quality_check.source_table = "workflow_test_source";
    quality_check.target_table = "quality_metrics";
    quality_check.extractor_config["query"] = R"(
        SELECT
            COUNT(*) as total_records,
            COUNT(CASE WHEN value IS NOT NULL THEN 1 END) as valid_records,
            COUNT(CASE WHEN value IS NULL THEN 1 END) as null_records
        FROM workflow_test_source
    )";

    auto quality_job_id = etl_service_->create_job(quality_check);

    // Wait for quality check
    std::optional<ETLJobResult> quality_result;
    bool quality_complete = false;

    etl_service_->set_completion_callback(
        [&quality_complete, &quality_result, &quality_job_id]
        (const std::string& job_id, const ETLJobResult& result) {
            if (job_id == quality_job_id) {
                quality_complete = true;
                quality_result = result;
            }
        });

    auto start = std::chrono::steady_clock::now();
    while (!quality_complete &&
           std::chrono::steady_clock::now() - start < 30s) {
        std::this_thread::sleep_for(100ms);
    }

    ASSERT_TRUE(quality_complete);
    ASSERT_TRUE(quality_result.has_value());

    // Determine workflow path based on quality metrics
    double error_rate = quality_result->error_records /
                       static_cast<double>(quality_result->total_records);

    if (error_rate < 0.05) {
        // Good quality: proceed with normal processing
        auto normal_request = create_workflow_job_request(
            "normal_processing",
            "workflow_test_source",
            "workflow_test_target"
        );

        auto normal_job_id = etl_service_->create_job(normal_request);

        // Wait and verify
        auto result = etl_service_->get_job_result(normal_job_id);
        ASSERT_TRUE(result.has_value());
        EXPECT_EQ(result->status, JobStatus::Completed);

    } else {
        // Poor quality: run data cleansing first
        ETLJobRequest cleansing_request;
        cleansing_request.name = "data_cleansing";
        cleansing_request.source_table = "workflow_test_source";
        cleansing_request.target_table = "workflow_test_source_clean";
        cleansing_request.extractor_config["filter"] = "value IS NOT NULL";

        auto cleansing_job_id = etl_service_->create_job(cleansing_request);

        // Then process cleaned data
        auto clean_request = create_workflow_job_request(
            "process_cleaned_data",
            "workflow_test_source_clean",
            "workflow_test_target"
        );

        auto process_job_id = etl_service_->create_job(clean_request);

        // Verify both jobs completed
        auto cleansing_result = etl_service_->get_job_result(cleansing_job_id);
        auto process_result = etl_service_->get_job_result(process_job_id);

        ASSERT_TRUE(cleansing_result.has_value());
        ASSERT_TRUE(process_result.has_value());
        EXPECT_EQ(cleansing_result->status, JobStatus::Completed);
        EXPECT_EQ(process_result->status, JobStatus::Completed);
    }
}

TEST_F(WorkflowOrchestrationTest, RecurringWorkflowExecution) {
    // Test recurring workflow execution with incremental processing

    // Initial data load
    populate_test_data(100);

    // Create watermark table for incremental processing
    execute_sql(R"(
        CREATE TABLE IF NOT EXISTS etl_watermarks (
            table_name VARCHAR(100) PRIMARY KEY,
            last_processed_timestamp TIMESTAMP,
            last_processed_id INTEGER
        )
    )");

    // Run workflow multiple times
    const int iterations = 3;
    std::vector<std::string> job_ids;

    for (int i = 0; i < iterations; ++i) {
        // Add new data for each iteration
        if (i > 0) {
            populate_test_data(50);
        }

        // Create incremental job
        ETLJobRequest incremental_request;
        incremental_request.name = std::format("incremental_load_{}", i);
        incremental_request.source_table = "workflow_test_source";
        incremental_request.target_table = "workflow_test_target";

        // Get last processed ID
        auto last_id = execute_scalar<int>(
            "SELECT COALESCE(MAX(last_processed_id), 0) FROM etl_watermarks "
            "WHERE table_name = 'workflow_test_source'"
        );

        incremental_request.extractor_config["filter"] =
            std::format("id > {}", last_id);

        auto job_id = etl_service_->create_job(incremental_request);
        job_ids.push_back(job_id);

        // Wait for completion
        bool complete = false;
        etl_service_->set_completion_callback(
            [&complete, &job_id](const std::string& id, const ETLJobResult& result) {
                if (id == job_id) {
                    complete = true;
                }
            });

        auto start = std::chrono::steady_clock::now();
        while (!complete &&
               std::chrono::steady_clock::now() - start < 30s) {
            std::this_thread::sleep_for(100ms);
        }

        ASSERT_TRUE(complete);

        // Update watermark
        auto max_id = execute_scalar<int>(
            "SELECT COALESCE(MAX(id), 0) FROM workflow_test_source"
        );

        execute_sql(std::format(
            "INSERT INTO etl_watermarks (table_name, last_processed_timestamp, last_processed_id) "
            "VALUES ('workflow_test_source', CURRENT_TIMESTAMP, {}) "
            "ON CONFLICT (table_name) DO UPDATE SET "
            "last_processed_timestamp = EXCLUDED.last_processed_timestamp, "
            "last_processed_id = EXCLUDED.last_processed_id",
            max_id
        ));
    }

    // Verify incremental processing
    std::vector<size_t> processed_counts;
    for (const auto& job_id : job_ids) {
        auto result = etl_service_->get_job_result(job_id);
        ASSERT_TRUE(result.has_value());
        processed_counts.push_back(result->processed_records);
    }

    // First iteration should process all initial records
    EXPECT_GT(processed_counts[0], 0);

    // Subsequent iterations should only process new records
    for (size_t i = 1; i < processed_counts.size(); ++i) {
        EXPECT_LT(processed_counts[i], processed_counts[0]);
    }
}

TEST_F(WorkflowOrchestrationTest, ErrorHandlingAndRecovery) {
    // Test workflow error handling and recovery mechanisms
    populate_test_data(200);

    // Inject some bad data that will cause errors
    execute_sql(R"(
        INSERT INTO workflow_test_source (patient_id, event_date, status, value)
        VALUES
            (NULL, CURRENT_DATE, 'invalid', NULL),
            (-1, NULL, 'error', -999999),
            (999999, '9999-99-99', NULL, NULL)
    )");

    // Create job with error handling
    ETLJobRequest request;
    request.name = "error_handling_workflow";
    request.source_table = "workflow_test_source";
    request.target_table = "workflow_test_target";
    request.pipeline_config.stop_on_error = false;
    request.pipeline_config.error_threshold = 0.1; // 10% error threshold

    // Add validation rules
    request.extractor_config["validation_rules"] = YAML::Load(R"(
        - field: patient_id
          type: not_null
        - field: patient_id
          type: range
          min: 1
          max: 10000
        - field: event_date
          type: not_null
        - field: value
          type: range
          min: 0
          max: 10000
    )");

    auto job_id = etl_service_->create_job(request);

    // Track errors
    std::vector<std::string> errors;
    std::mutex errors_mutex;

    etl_service_->set_error_callback(
        [&errors, &errors_mutex](const std::string& job_id, const std::exception& e) {
            std::lock_guard<std::mutex> lock(errors_mutex);
            errors.push_back(e.what());
        });

    // Wait for completion
    auto result = etl_service_->get_job_result(job_id);

    // Wait for job to complete
    auto start = std::chrono::steady_clock::now();
    while (!result.has_value() &&
           std::chrono::steady_clock::now() - start < 30s) {
        std::this_thread::sleep_for(100ms);
        result = etl_service_->get_job_result(job_id);
    }

    ASSERT_TRUE(result.has_value());

    // Job should complete despite errors (below threshold)
    EXPECT_EQ(result->status, JobStatus::Completed);
    EXPECT_GT(result->error_records, 0);
    EXPECT_GT(result->processed_records, result->error_records);

    // Verify error records were logged
    execute_sql(R"(
        CREATE TABLE IF NOT EXISTS etl_error_log (
            id SERIAL PRIMARY KEY,
            job_id VARCHAR(100),
            record_id INTEGER,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    )");

    auto error_count = execute_scalar<int>(
        std::format("SELECT COUNT(*) FROM etl_error_log WHERE job_id = '{}'", job_id)
    );

    EXPECT_GT(error_count, 0);
}

} // namespace omop::test::integration::workflow