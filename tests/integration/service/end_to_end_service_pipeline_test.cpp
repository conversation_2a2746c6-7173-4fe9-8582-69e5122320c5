/**
 * Integration test for complete end-to-end ETL pipeline execution with all components
 */

#include <gtest/gtest.h>
#include "service/etl_service.h"
#include "extract/csv_extractor.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"
#include <filesystem>

namespace omop::test::integration {

class EndToEndPipelineTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();

        // Set up test database
        db_fixture_ = std::make_unique<DatabaseFixture>();
        db_fixture_->setup_test_database();

        // Generate test data
        data_generator_ = std::make_unique<TestDataGenerator>();
        test_data_dir_ = std::filesystem::temp_directory_path() / "omop_e2e_test";
        std::filesystem::create_directories(test_data_dir_);

        // Generate comprehensive test data
        generate_test_data();

        // Initialize configuration with test data paths
        setup_configuration();

        // Initialize services
        pipeline_manager_ = std::make_shared<core::PipelineManager>(4);
        etl_service_ = std::make_shared<service::ETLService>(config_, pipeline_manager_);
    }

    void TearDown() override {
        pipeline_manager_->shutdown(true);
        std::filesystem::remove_all(test_data_dir_);
        db_fixture_->cleanup_test_database();
        IntegrationTestBase::TearDown();
    }

private:
    void generate_test_data() {
        // Generate patient data with various edge cases
        auto patients = data_generator_->generate_patients(1000);
        data_generator_->save_to_csv(test_data_dir_ / "patients.csv", patients);

        // Generate visits
        auto visits = data_generator_->generate_visits(patients, 5000);
        data_generator_->save_to_csv(test_data_dir_ / "visits.csv", visits);

        // Generate conditions
        auto conditions = data_generator_->generate_conditions(visits, 8000);
        data_generator_->save_to_csv(test_data_dir_ / "conditions.csv", conditions);

        // Generate medications
        auto medications = data_generator_->generate_medications(visits, 10000);
        data_generator_->save_to_csv(test_data_dir_ / "medications.csv", medications);

        // Generate measurements
        auto measurements = data_generator_->generate_measurements(visits, 15000);
        data_generator_->save_to_csv(test_data_dir_ / "measurements.csv", measurements);

        // Generate procedures
        auto procedures = data_generator_->generate_procedures(visits, 3000);
        data_generator_->save_to_csv(test_data_dir_ / "procedures.csv", procedures);
    }

    void setup_configuration() {
        config_ = std::make_shared<common::ConfigurationManager>();

        std::string config_yaml = R"(
            etl:
                batch_size: 500
                parallel_jobs: 4
                error_threshold: 0.01
                enable_validation: true
                checkpoint_interval: 1000

            source_db:
                type: csv_directory
                directory: )" + test_data_dir_.string() + R"(
                pattern: ".*\\.csv$"

            target_db:
                type: postgresql
                host: )" + db_fixture_->get_host() + R"(
                port: )" + std::to_string(db_fixture_->get_port()) + R"(
                database: omop_test
                username: test_user
                password: test_pass

            vocabulary:
                database: omop_vocab
                cache_size: 50000

            mappings:
                person:
                    source_table: patients.csv
                    target_table: person
                    filters: "age >= 0 AND age <= 120"
                    validations:
                        - field: person_id
                          type: not_null
                        - field: birth_datetime
                          type: date_range
                          min: "1900-01-01"
                          max: "2024-12-31"
                    transformations:
                        - source_column: patient_id
                          target_column: person_id
                          type: direct
                        - source_column: birth_date
                          target_column: birth_datetime
                          type: date_transform
                          parameters:
                              input_format: "%Y-%m-%d"
                              output_format: "%Y-%m-%d %H:%M:%S"
                              add_time: true
                        - source_column: gender
                          target_column: gender_concept_id
                          type: vocabulary_mapping
                          parameters:
                              vocabulary_name: Gender
                              default_concept_id: 0

                visit_occurrence:
                    source_table: visits.csv
                    target_table: visit_occurrence
                    transformations:
                        - source_column: visit_id
                          target_column: visit_occurrence_id
                          type: direct
                        - source_column: patient_id
                          target_column: person_id
                          type: direct
                        - source_column: visit_type
                          target_column: visit_concept_id
                          type: vocabulary_mapping
                          parameters:
                              vocabulary_name: Visit

                condition_occurrence:
                    source_table: conditions.csv
                    target_table: condition_occurrence
                    transformations:
                        - source_column: condition_id
                          target_column: condition_occurrence_id
                          type: direct
                        - source_column: patient_id
                          target_column: person_id
                          type: direct
                        - source_column: icd10_code
                          target_column: condition_concept_id
                          type: vocabulary_mapping
                          parameters:
                              vocabulary_name: ICD10
                              source_vocabulary: ICD10

                drug_exposure:
                    source_table: medications.csv
                    target_table: drug_exposure
                    transformations:
                        - source_column: medication_id
                          target_column: drug_exposure_id
                          type: direct
                        - source_column: drug_name
                          target_column: drug_concept_id
                          type: vocabulary_mapping
                          parameters:
                              vocabulary_name: RxNorm

                measurement:
                    source_table: measurements.csv
                    target_table: measurement
                    transformations:
                        - source_column: measurement_id
                          target_column: measurement_id
                          type: direct
                        - source_column: lab_code
                          target_column: measurement_concept_id
                          type: vocabulary_mapping
                          parameters:
                              vocabulary_name: LOINC
                        - source_column: value
                          target_column: value_as_number
                          type: numeric_transform
                          parameters:
                              operation: round
                              precision: 2

                procedure_occurrence:
                    source_table: procedures.csv
                    target_table: procedure_occurrence
                    transformations:
                        - source_column: procedure_id
                          target_column: procedure_occurrence_id
                          type: direct
                        - source_column: cpt_code
                          target_column: procedure_concept_id
                          type: vocabulary_mapping
                          parameters:
                              vocabulary_name: CPT4
        )";

        config_->load_config_from_string(config_yaml);
    }

    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<service::ETLService> etl_service_;
    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::unique_ptr<TestDataGenerator> data_generator_;
    std::filesystem::path test_data_dir_;
};

TEST_F(EndToEndPipelineTest, TestCompleteETLWorkflow) {
    // Execute ETL for all configured tables
    auto job_map = etl_service_->run_all_tables(true);

    ASSERT_EQ(job_map.size(), 6); // 6 tables configured

    // Wait for all jobs to complete
    for (const auto& [table_name, job_id] : job_map) {
        pipeline_manager_->wait_for_job(job_id, 30000); // 30 second timeout
    }

    // Verify results for each table
    std::map<std::string, size_t> expected_counts = {
        {"person", 1000},
        {"visit_occurrence", 5000},
        {"condition_occurrence", 8000},
        {"drug_exposure", 10000},
        {"measurement", 15000},
        {"procedure_occurrence", 3000}
    };

    for (const auto& [table_name, job_id] : job_map) {
        auto result = etl_service_->get_job_result(job_id);
        ASSERT_TRUE(result.has_value()) << "No result for table: " << table_name;

        EXPECT_EQ(result->status, core::JobStatus::Completed)
            << "Job failed for table: " << table_name;

        // Allow for some filtered records
        EXPECT_GE(result->processed_records, expected_counts[table_name] * 0.95)
            << "Insufficient records processed for table: " << table_name;

        // Check error rate
        double error_rate = static_cast<double>(result->error_records) / result->total_records;
        EXPECT_LE(error_rate, 0.01) << "High error rate for table: " << table_name;

        // Verify data in target database
        auto record_count = db_fixture_->count_records("cdm." + table_name);
        EXPECT_GE(record_count, expected_counts[table_name] * 0.95)
            << "Insufficient records in database for table: " << table_name;
    }
}

TEST_F(EndToEndPipelineTest, TestDataIntegrityAcrossTables) {
    // Execute ETL
    auto job_map = etl_service_->run_all_tables(false); // Sequential to ensure order

    // Wait for completion
    for (const auto& [table_name, job_id] : job_map) {
        pipeline_manager_->wait_for_job(job_id, 30000);
    }

    // Verify referential integrity
    auto orphaned_visits = db_fixture_->execute_query(R"(
        SELECT COUNT(*) FROM cdm.visit_occurrence v
        LEFT JOIN cdm.person p ON v.person_id = p.person_id
        WHERE p.person_id IS NULL
    )");

    EXPECT_EQ(orphaned_visits[0][0], "0") << "Found orphaned visit records";

    auto orphaned_conditions = db_fixture_->execute_query(R"(
        SELECT COUNT(*) FROM cdm.condition_occurrence c
        LEFT JOIN cdm.person p ON c.person_id = p.person_id
        WHERE p.person_id IS NULL
    )");

    EXPECT_EQ(orphaned_conditions[0][0], "0") << "Found orphaned condition records";

    // Verify concept mappings
    auto unmapped_concepts = db_fixture_->execute_query(R"(
        SELECT COUNT(*) FROM cdm.condition_occurrence
        WHERE condition_concept_id = 0
    )");

    auto total_conditions = db_fixture_->count_records("cdm.condition_occurrence");
    double unmapped_rate = std::stod(unmapped_concepts[0][0]) / total_conditions;

    EXPECT_LE(unmapped_rate, 0.05) << "High rate of unmapped concepts";
}

TEST_F(EndToEndPipelineTest, TestIncrementalETL) {
    // First run - full load
    auto initial_job_map = etl_service_->run_all_tables(false);

    for (const auto& [table_name, job_id] : initial_job_map) {
        pipeline_manager_->wait_for_job(job_id, 30000);
    }

    // Record initial counts
    std::map<std::string, size_t> initial_counts;
    for (const auto& [table_name, _] : initial_job_map) {
        initial_counts[table_name] = db_fixture_->count_records("cdm." + table_name);
    }

    // Generate incremental data
    auto new_patients = data_generator_->generate_patients(100, 1001); // IDs 1001-1100
    data_generator_->save_to_csv(test_data_dir_ / "patients_delta.csv", new_patients);

    // Configure incremental load
    service::ETLJobRequest incremental_request;
    incremental_request.name = "Incremental Person Load";
    incremental_request.source_table = "patients_delta.csv";
    incremental_request.target_table = "person";
    incremental_request.extractor_type = "csv";
    incremental_request.extractor_config["filepath"] =
        (test_data_dir_ / "patients_delta.csv").string();

    auto incremental_job_id = etl_service_->create_job(incremental_request);
    pipeline_manager_->wait_for_job(incremental_job_id, 10000);

    // Verify incremental data was loaded
    auto final_count = db_fixture_->count_records("cdm.person");
    EXPECT_EQ(final_count, initial_counts["person"] + 100);

    // Verify no duplicates
    auto duplicate_check = db_fixture_->execute_query(R"(
        SELECT person_id, COUNT(*) as cnt
        FROM cdm.person
        GROUP BY person_id
        HAVING COUNT(*) > 1
    )");

    EXPECT_TRUE(duplicate_check.empty()) << "Found duplicate person records";
}

TEST_F(EndToEndPipelineTest, TestErrorHandlingAndRecovery) {
    // Create data with various error conditions
    auto error_data = data_generator_->generate_error_test_data();
    data_generator_->save_to_csv(test_data_dir_ / "error_test.csv", error_data);

    service::ETLJobRequest request;
    request.name = "Error Handling Test";
    request.source_table = "error_test.csv";
    request.target_table = "person";
    request.pipeline_config.stop_on_error = false;
    request.pipeline_config.error_threshold = 0.2; // Allow up to 20% errors
    request.extractor_type = "csv";
    request.extractor_config["filepath"] = (test_data_dir_ / "error_test.csv").string();

    auto job_id = etl_service_->create_job(request);
    pipeline_manager_->wait_for_job(job_id, 10000);

    auto result = etl_service_->get_job_result(job_id);
    ASSERT_TRUE(result.has_value());

    // Job should complete despite errors
    EXPECT_EQ(result->status, core::JobStatus::Completed);

    // Verify some records were processed successfully
    EXPECT_GT(result->processed_records, result->total_records * 0.5);

    // Verify errors were tracked
    EXPECT_GT(result->error_records, 0);
    EXPECT_FALSE(result->errors.empty());

    // Verify error details contain useful information
    bool found_validation_error = false;
    for (const auto& error : result->errors) {
        if (error.find("validation") != std::string::npos ||
            error.find("invalid") != std::string::npos) {
            found_validation_error = true;
            break;
        }
    }

    EXPECT_TRUE(found_validation_error) << "No validation errors recorded";
}

TEST_F(EndToEndPipelineTest, TestPerformanceMetrics) {
    // Configure performance monitoring
    service::ETLJobRequest request;
    request.name = "Performance Test";
    request.source_table = "measurements.csv";
    request.target_table = "measurement";
    request.pipeline_config.batch_size = 1000;
    request.extractor_type = "csv";
    request.extractor_config["filepath"] = (test_data_dir_ / "measurements.csv").string();

    auto start_time = std::chrono::steady_clock::now();
    auto job_id = etl_service_->create_job(request);

    // Monitor progress
    std::vector<double> progress_points;
    while (true) {
        auto job_info = pipeline_manager_->get_job_info(job_id);
        if (job_info && job_info->status != core::JobStatus::Running) {
            break;
        }

        if (job_info) {
            progress_points.push_back(job_info->progress());
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - start_time).count();

    auto result = etl_service_->get_job_result(job_id);
    ASSERT_TRUE(result.has_value());

    // Calculate throughput
    double records_per_second =
        static_cast<double>(result->processed_records) / (duration / 1000.0);

    // Performance expectations
    EXPECT_GT(records_per_second, 1000) << "Low throughput";

    // Verify progress reporting
    EXPECT_GT(progress_points.size(), 5) << "Insufficient progress updates";

    // Verify metrics collection
    ASSERT_TRUE(result->metrics.contains("extraction_time_ms"));
    ASSERT_TRUE(result->metrics.contains("transformation_time_ms"));
    ASSERT_TRUE(result->metrics.contains("loading_time_ms"));

    auto extraction_time = std::any_cast<double>(result->metrics["extraction_time_ms"]);
    auto transformation_time = std::any_cast<double>(result->metrics["transformation_time_ms"]);
    auto loading_time = std::any_cast<double>(result->metrics["loading_time_ms"]);

    // Verify time breakdown is reasonable
    auto total_component_time = extraction_time + transformation_time + loading_time;
    EXPECT_LE(total_component_time, duration * 1.1) << "Component times exceed total time";
}

} // namespace omop::test::integration