// Updated service integration test for current OMOP ETL implementation
#include <gtest/gtest.h>
#include "service/etl_service.h"
#include "service/extract_service.h"
#include "service/transform_service.h" 
#include "service/load_service.h"
#include "service/service_orchestrator.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"
#include <chrono>
#include <thread>

namespace omop::service::test {

/**
 * @brief Updated service integration test class that matches current implementation
 */
class ServiceIntegrationTestUpdated : public omop::test::DatabaseFixture {
protected:
    void SetUp() override {
        omop::test::DatabaseFixture::SetUp();

        // Initialize configuration manager
        config_manager_ = std::make_shared<common::ConfigurationManager>();
        load_service_config();

        // Initialize services
        extract_service_ = std::make_shared<ExtractService>(config_manager_);
        transform_service_ = std::make_shared<TransformService>(config_manager_);
        load_service_ = std::make_shared<LoadService>(config_manager_);
        
        // Initialize pipeline manager first
        pipeline_manager_ = std::make_shared<core::PipelineManager>();
        
        // Initialize ETL service with all component services
        etl_service_ = std::make_shared<ETLService>(config_manager_, pipeline_manager_);
        
        // Initialize service orchestrator
        orchestrator_ = std::make_shared<ServiceOrchestrator>(
            extract_service_, transform_service_, load_service_);

        // Create test data
        create_service_test_data();
    }

    void TearDown() override {
        // Clean up test data
        cleanup_service_test_data();
        omop::test::DatabaseFixture::TearDown();
    }

    void load_service_config() {
        std::string config_yaml = R"(
            services:
                extract:
                    max_concurrent_extractors: 4
                    batch_size: 1000
                    timeout_seconds: 300
                    
                transform:
                    max_concurrent_transformers: 2
                    validation_enabled: true
                    cache_size: 10000
                    
                load:
                    max_concurrent_loaders: 2
                    batch_size: 500
                    retry_attempts: 3
                    
            database:
                source:
                    type: postgresql
                    host: localhost
                    port: 5432
                    database: test_source
                    
                target:
                    type: postgresql
                    host: localhost
                    port: 5432
                    database: test_omop
                    
            etl:
                job_timeout_minutes: 60
                max_retry_attempts: 3
                enable_metrics: true
        )";
        
        config_manager_->load_config_from_string(config_yaml);
    }

    void create_service_test_data() {
        // Create temporary directory for test files
        test_data_dir_ = std::filesystem::temp_directory_path() / "service_integration_test";
        std::filesystem::create_directories(test_data_dir_);

        // Create test CSV files
        create_patients_csv();
        create_visits_csv();
        create_procedures_csv();
    }

    void cleanup_service_test_data() {
        if (std::filesystem::exists(test_data_dir_)) {
            std::filesystem::remove_all(test_data_dir_);
        }
    }

    void create_patients_csv() {
        std::ofstream file(test_data_dir_ / "patients.csv");
        file << "patient_id,first_name,last_name,birth_date,gender,race,ethnicity\n";
        file << "1,John,Doe,1980-01-01,M,White,Not Hispanic\n";
        file << "2,Jane,Smith,1975-05-15,F,White,Not Hispanic\n"; 
        file << "3,Maria,Garcia,1990-03-10,F,Hispanic,Hispanic\n";
        file << "4,David,Chen,1985-07-20,M,Asian,Not Hispanic\n";
        file << "5,Sarah,Johnson,1978-12-05,F,Black,Not Hispanic\n";
        file.close();
    }

    void create_visits_csv() {
        std::ofstream file(test_data_dir_ / "visits.csv");
        file << "visit_id,patient_id,visit_start_date,visit_end_date,visit_type\n";
        file << "1,1,2023-01-15,2023-01-15,Outpatient\n";
        file << "2,2,2023-02-20,2023-02-22,Inpatient\n";
        file << "3,3,2023-03-10,2023-03-10,Emergency\n";
        file << "4,1,2023-04-05,2023-04-05,Outpatient\n";
        file << "5,4,2023-05-12,2023-05-14,Inpatient\n";
        file.close();
    }

    void create_procedures_csv() {
        std::ofstream file(test_data_dir_ / "procedures.csv");
        file << "procedure_id,patient_id,visit_id,procedure_date,procedure_code,procedure_description\n";
        file << "1,1,1,2023-01-15,99213,Office Visit\n";
        file << "2,2,2,2023-02-20,99223,Hospital Visit\n";
        file << "3,3,3,2023-03-10,99281,Emergency Visit\n";
        file << "4,1,4,2023-04-05,99214,Office Visit\n";
        file << "5,4,5,2023-05-12,99222,Hospital Visit\n";
        file.close();
    }

protected:
    std::shared_ptr<common::ConfigurationManager> config_manager_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<ExtractService> extract_service_;
    std::shared_ptr<TransformService> transform_service_;
    std::shared_ptr<LoadService> load_service_;
    std::shared_ptr<ETLService> etl_service_;
    std::shared_ptr<ServiceOrchestrator> orchestrator_;
    std::filesystem::path test_data_dir_;
};

// Test extract service functionality
TEST_F(ServiceIntegrationTestUpdated, TestExtractService) {
    // Configure CSV extraction
    std::unordered_map<std::string, std::any> extract_config = {
        {"source_type", std::string("csv")},
        {"file_path", (test_data_dir_ / "patients.csv").string()},
        {"has_header", true},
        {"delimiter", std::string(",")},
        {"table_name", std::string("patients")}
    };

    // Perform extraction
    auto result = extract_service_->extract_data(extract_config);
    
    EXPECT_TRUE(result.success);
    EXPECT_GT(result.records_extracted, 0);
    EXPECT_EQ(result.records_extracted, 5); // 5 patients in test data
    EXPECT_FALSE(result.error_message.has_value());
}

// Test transform service functionality  
TEST_F(ServiceIntegrationTestUpdated, TestTransformService) {
    // Create sample record for transformation
    std::unordered_map<std::string, std::any> source_record = {
        {"patient_id", 1},
        {"first_name", std::string("John")},
        {"last_name", std::string("Doe")},
        {"birth_date", std::string("1980-01-01")},
        {"gender", std::string("M")},
        {"race", std::string("White")},
        {"ethnicity", std::string("Not Hispanic")}
    };

    // Configure transformation mappings
    std::unordered_map<std::string, std::any> transform_config = {
        {"target_table", std::string("person")},
        {"mappings", std::vector<std::unordered_map<std::string, std::any>>{
            {{"source_field", std::string("patient_id")}, {"target_field", std::string("person_id")}, {"type", std::string("direct")}},
            {{"source_field", std::string("gender")}, {"target_field", std::string("gender_concept_id")}, {"type", std::string("vocabulary_lookup")}},
            {{"source_field", std::string("birth_date")}, {"target_field", std::string("year_of_birth")}, {"type", std::string("date_extract_year")}}
        }}
    };

    // Perform transformation
    auto result = transform_service_->transform_record(source_record, transform_config);
    
    EXPECT_TRUE(result.success);
    EXPECT_TRUE(result.transformed_record.contains("person_id"));
    EXPECT_TRUE(result.transformed_record.contains("gender_concept_id"));
    EXPECT_TRUE(result.transformed_record.contains("year_of_birth"));
    
    // Verify transformations
    EXPECT_EQ(std::any_cast<int>(result.transformed_record.at("person_id")), 1);
    EXPECT_EQ(std::any_cast<int>(result.transformed_record.at("year_of_birth")), 1980);
}

// Test load service functionality
TEST_F(ServiceIntegrationTestUpdated, TestLoadService) {
    // Create test records for loading
    std::vector<std::unordered_map<std::string, std::any>> records = {
        {
            {"person_id", 1},
            {"gender_concept_id", 8507}, // Male
            {"year_of_birth", 1980},
            {"race_concept_id", 8527}, // White  
            {"ethnicity_concept_id", 38003564} // Not Hispanic
        },
        {
            {"person_id", 2},
            {"gender_concept_id", 8532}, // Female
            {"year_of_birth", 1975},
            {"race_concept_id", 8527}, // White
            {"ethnicity_concept_id", 38003564} // Not Hispanic
        }
    };

    // Configure loading
    std::unordered_map<std::string, std::any> load_config = {
        {"target_table", std::string("person")},
        {"batch_size", 10},
        {"upsert_mode", true},
        {"primary_key", std::string("person_id")}
    };

    // Perform loading
    auto result = load_service_->load_data(records, load_config);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.records_loaded, 2);
    EXPECT_FALSE(result.error_message.has_value());
}

// Test service orchestrator coordination
TEST_F(ServiceIntegrationTestUpdated, TestServiceOrchestrator) {
    // Configure full ETL pipeline
    std::unordered_map<std::string, std::any> pipeline_config = {
        {"job_name", std::string("Orchestrator Test Pipeline")},
        {"extract_config", std::unordered_map<std::string, std::any>{
            {"source_type", std::string("csv")},
            {"file_path", (test_data_dir_ / "patients.csv").string()},
            {"has_header", true}
        }},
        {"transform_config", std::unordered_map<std::string, std::any>{
            {"target_table", std::string("person")},
            {"mappings", std::vector<std::unordered_map<std::string, std::any>>{
                {{"source_field", std::string("patient_id")}, {"target_field", std::string("person_id")}, {"type", std::string("direct")}},
                {{"source_field", std::string("gender")}, {"target_field", std::string("gender_concept_id")}, {"type", std::string("vocabulary_lookup")}}
            }}
        }},
        {"load_config", std::unordered_map<std::string, std::any>{
            {"target_table", std::string("person")},
            {"batch_size", 5}
        }}
    };

    // Execute pipeline through orchestrator
    auto job_id = orchestrator_->execute_pipeline(pipeline_config);
    EXPECT_FALSE(job_id.empty());

    // Wait for completion
    bool completed = wait_for_condition([this, &job_id]() {
        auto status = orchestrator_->get_job_status(job_id);
        return status && (*status == "completed" || *status == "failed");
    }, 30000);

    EXPECT_TRUE(completed);

    // Verify successful completion
    auto final_status = orchestrator_->get_job_status(job_id);
    EXPECT_TRUE(final_status.has_value());
    EXPECT_EQ(*final_status, "completed");
}

// Test ETL service end-to-end functionality
TEST_F(ServiceIntegrationTestUpdated, TestETLServiceEndToEnd) {
    // Submit multiple jobs with different data sources
    std::vector<std::string> job_ids;

    // Job 1: Patients
    {
        std::unordered_map<std::string, std::any> job_config = {
            {"name", std::string("Import Patients")},
            {"source_type", std::string("csv")},
            {"source_path", (test_data_dir_ / "patients.csv").string()},
            {"target_table", std::string("person")},
            {"priority", 1}
        };
        job_ids.push_back(etl_service_->submit_job(job_config));
    }

    // Job 2: Visits (depends on patients)
    {
        std::unordered_map<std::string, std::any> job_config = {
            {"name", std::string("Import Visits")},
            {"source_type", std::string("csv")},
            {"source_path", (test_data_dir_ / "visits.csv").string()},
            {"target_table", std::string("visit_occurrence")},
            {"priority", 2},
            {"dependencies", std::vector<std::string>{job_ids[0]}}
        };
        job_ids.push_back(etl_service_->submit_job(job_config));
    }

    // Job 3: Procedures (depends on visits)
    {
        std::unordered_map<std::string, std::any> job_config = {
            {"name", std::string("Import Procedures")},
            {"source_type", std::string("csv")},
            {"source_path", (test_data_dir_ / "procedures.csv").string()},
            {"target_table", std::string("procedure_occurrence")},
            {"priority", 3},
            {"dependencies", std::vector<std::string>{job_ids[1]}}
        };
        job_ids.push_back(etl_service_->submit_job(job_config));
    }

    // Wait for all jobs to complete
    for (const auto& job_id : job_ids) {
        bool completed = wait_for_condition([this, &job_id]() {
            auto status = etl_service_->get_job_status(job_id);
            return status && (*status == "completed" || *status == "failed");
        }, 60000);

        EXPECT_TRUE(completed);
        
        auto final_status = etl_service_->get_job_status(job_id);
        EXPECT_TRUE(final_status.has_value());
        EXPECT_EQ(*final_status, "completed");
    }
}

// Test error handling and rollback
TEST_F(ServiceIntegrationTestUpdated, TestErrorHandlingAndRollback) {
    // Create a job that will fail
    std::unordered_map<std::string, std::any> failing_job_config = {
        {"name", std::string("Failing Job")},
        {"source_type", std::string("csv")},
        {"source_path", std::string("/nonexistent/file.csv")}, // Invalid path
        {"target_table", std::string("person")}
    };

    auto job_id = etl_service_->submit_job(failing_job_config);
    EXPECT_FALSE(job_id.empty());

    // Wait for job to fail
    bool completed = wait_for_condition([this, &job_id]() {
        auto status = etl_service_->get_job_status(job_id);
        return status && *status == "failed";
    }, 10000);

    EXPECT_TRUE(completed);

    // Verify error information
    auto job_info = etl_service_->get_job_info(job_id);
    EXPECT_TRUE(job_info.has_value());
    EXPECT_TRUE(job_info->contains("error_message"));
    
    // Verify rollback was performed if applicable
    auto rollback_status = etl_service_->get_rollback_status(job_id);
    if (rollback_status.has_value()) {
        EXPECT_TRUE(*rollback_status);
    }
}

// Test service performance and metrics
TEST_F(ServiceIntegrationTestUpdated, TestServiceMetrics) {
    // Submit a job to generate metrics
    std::unordered_map<std::string, std::any> job_config = {
        {"name", std::string("Metrics Test Job")},
        {"source_type", std::string("csv")},
        {"source_path", (test_data_dir_ / "patients.csv").string()},
        {"target_table", std::string("person")},
        {"enable_detailed_metrics", true}
    };

    auto job_id = etl_service_->submit_job(job_config);

    // Wait for completion
    wait_for_condition([this, &job_id]() {
        auto status = etl_service_->get_job_status(job_id);
        return status && *status == "completed";
    }, 30000);

    // Get comprehensive metrics
    auto job_metrics = etl_service_->get_job_metrics(job_id);
    EXPECT_TRUE(job_metrics.has_value());

    // Verify metrics contain expected performance data
    EXPECT_TRUE(job_metrics->contains("extract_time"));
    EXPECT_TRUE(job_metrics->contains("transform_time"));
    EXPECT_TRUE(job_metrics->contains("load_time"));
    EXPECT_TRUE(job_metrics->contains("total_time"));
    EXPECT_TRUE(job_metrics->contains("records_per_second"));
    
    // Get service-level metrics
    auto extract_metrics = extract_service_->get_metrics();
    auto transform_metrics = transform_service_->get_metrics();
    auto load_metrics = load_service_->get_metrics();
    
    EXPECT_TRUE(extract_metrics.contains("total_extractions"));
    EXPECT_TRUE(transform_metrics.contains("total_transformations"));
    EXPECT_TRUE(load_metrics.contains("total_loads"));
}

// Test concurrent service operations
TEST_F(ServiceIntegrationTestUpdated, TestConcurrentOperations) {
    const int num_concurrent_jobs = 5;
    std::vector<std::string> job_ids;

    // Submit multiple jobs concurrently
    for (int i = 0; i < num_concurrent_jobs; ++i) {
        std::unordered_map<std::string, std::any> job_config = {
            {"name", std::string("Concurrent Job ") + std::to_string(i)},
            {"source_type", std::string("csv")},
            {"source_path", (test_data_dir_ / "patients.csv").string()},
            {"target_table", std::string("person")},
            {"job_id_suffix", std::to_string(i)} // Avoid conflicts
        };

        job_ids.push_back(etl_service_->submit_job(job_config));
    }

    // Verify all jobs are accepted
    EXPECT_EQ(job_ids.size(), num_concurrent_jobs);
    for (const auto& job_id : job_ids) {
        EXPECT_FALSE(job_id.empty());
    }

    // Wait for all to complete
    for (const auto& job_id : job_ids) {
        bool completed = wait_for_condition([this, &job_id]() {
            auto status = etl_service_->get_job_status(job_id);
            return status && (*status == "completed" || *status == "failed");
        }, 60000);

        EXPECT_TRUE(completed);
    }

    // Verify service handled concurrency correctly
    auto service_metrics = etl_service_->get_service_metrics();
    EXPECT_TRUE(service_metrics.contains("max_concurrent_jobs"));
    EXPECT_TRUE(service_metrics.contains("total_jobs_processed"));
}

} // namespace omop::service::test