// tests/integration/e2e/test_data_quality_validation_e2e.cpp
// Tests comprehensive data quality validation across the entire pipeline
#include <gtest/gtest.h>
#include "service/etl_service.h"
#include "test_helpers/test_data_generator.h"

namespace omop::e2e::test {

class DataQualityValidationTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<common::ConfigurationManager>();
        config_->load_config_from_string(R"(
            source_db:
                type: postgresql
                host: localhost
                port: 5432
                database: source_db

            target_db:
                type: postgresql
                host: localhost
                port: 5432
                database: omop_cdm

            data_quality:
                enabled: true
                rules_path: /config/dq_rules.yaml
                report_path: /reports/dq_reports
                fail_on_quality_issues: false
                thresholds:
                    completeness: 0.95
                    accuracy: 0.98
                    consistency: 0.99
                    timeliness: 0.90

            validation:
                pre_etl: true
                post_etl: true
                continuous: true
        )");

        pipeline_manager_ = std::make_shared<core::PipelineManager>(4);
        etl_service_ = std::make_shared<ETLService>(config_, pipeline_manager_);

        // Generate test data with known quality issues
        generateTestDataWithQualityIssues();
    }

    void generateTestDataWithQualityIssues() {
        TestDataGenerator generator;

        // Generate data with various quality issues
        generator.generatePatientData("patients", 1000, {
            {"missing_gender_rate", 0.05},
            {"invalid_birth_year_rate", 0.02},
            {"duplicate_rate", 0.01}
        });

        generator.generateVisitData("visits", 5000, {
            {"orphan_visit_rate", 0.03},
            {"overlapping_dates_rate", 0.02},
            {"future_dates_rate", 0.01}
        });
    }

    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<ETLService> etl_service_;
    DatabaseFixture db_fixture_;
};

// Tests comprehensive data quality validation
TEST_F(DataQualityValidationTest, ValidateDataQualityMetrics) {
    DataQualityValidator validator(config_);

    // Run pre-ETL validation
    auto pre_etl_report = validator.validateSourceData("patients");

    EXPECT_TRUE(pre_etl_report.has_issues());
    EXPECT_NEAR(pre_etl_report.completeness_score, 0.95, 0.01);
    EXPECT_GT(pre_etl_report.issues.size(), 0);

    // Run ETL with quality monitoring
    ETLJobRequest request;
    request.name = "DQ Test ETL";
    request.source_table = "patients";
    request.target_table = "person";
    request.pipeline_config.validate_records = true;

    auto job_id = etl_service_->create_job(request);

    // Wait for completion
    std::this_thread::sleep_for(std::chrono::seconds(10));

    // Run post-ETL validation
    auto post_etl_report = validator.validateTargetData("person");

    // Verify data quality improvements
    EXPECT_GT(post_etl_report.completeness_score, pre_etl_report.completeness_score);
    EXPECT_LT(post_etl_report.issues.size(), pre_etl_report.issues.size());

    // Check specific quality dimensions
    EXPECT_GE(post_etl_report.accuracy_score, 0.98);
    EXPECT_GE(post_etl_report.consistency_score, 0.99);
    EXPECT_GE(post_etl_report.timeliness_score, 0.90);
}

// Tests data profiling and anomaly detection
TEST_F(DataQualityValidationTest, DataProfilingAndAnomalyDetection) {
    DataProfiler profiler;
    AnomalyDetector detector;

    // Profile source data
    auto source_profile = profiler.profileTable("patients", {
        "patient_id", "birth_year", "gender", "race", "ethnicity"
    });

    // Detect anomalies
    auto anomalies = detector.detectAnomalies(source_profile);

    EXPECT_FALSE(anomalies.empty());

    // Verify specific anomalies are detected
    bool found_birth_year_anomaly = false;
    bool found_missing_gender_anomaly = false;

    for (const auto& anomaly : anomalies) {
        if (anomaly.field == "birth_year" && anomaly.type == "outlier") {
            found_birth_year_anomaly = true;
        }
        if (anomaly.field == "gender" && anomaly.type == "missing_values") {
            found_missing_gender_anomaly = true;
        }
    }

    EXPECT_TRUE(found_birth_year_anomaly);
    EXPECT_TRUE(found_missing_gender_anomaly);
}

// Tests continuous data quality monitoring
TEST_F(DataQualityValidationTest, ContinuousQualityMonitoring) {
    DataQualityMonitor monitor(config_);
    monitor.start();

    std::vector<DataQualityAlert> received_alerts;
    monitor.setAlertCallback([&](const DataQualityAlert& alert) {
        received_alerts.push_back(alert);
    });

    // Run multiple ETL jobs to trigger monitoring
    for (int i = 0; i < 5; ++i) {
        ETLJobRequest request;
        request.name = "DQ Monitor Test " + std::to_string(i);
        request.source_table = "patients";
        request.target_table = "person";

        etl_service_->create_job(request);
        std::this_thread::sleep_for(std::chrono::seconds(2));
    }

    // Wait for monitoring to process
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // Check quality trends
    auto trends = monitor.getQualityTrends("person", std::chrono::hours(1));

    EXPECT_FALSE(trends.empty());
    EXPECT_TRUE(trends.contains("completeness_trend"));
    EXPECT_TRUE(trends.contains("accuracy_trend"));

    // Verify alerts were generated for quality issues
    EXPECT_FALSE(received_alerts.empty());
}

// Tests data lineage tracking
TEST_F(DataQualityValidationTest, DataLineageTracking) {
    DataLineageTracker lineage_tracker;

    // Enable lineage tracking
    ETLJobRequest request;
    request.name = "Lineage Test";
    request.source_table = "patients";
    request.target_table = "person";
    request.pipeline_config.track_lineage = true;

    auto job_id = etl_service_->create_job(request);

    // Wait for completion
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // Query lineage information
    auto lineage = lineage_tracker.getFieldLineage("person", "person_id");

    ASSERT_TRUE(lineage.has_value());
    EXPECT_EQ(lineage->source_table, "patients");
    EXPECT_EQ(lineage->source_field, "patient_id");
    EXPECT_EQ(lineage->transformation_type, "direct");
    EXPECT_EQ(lineage->job_id, job_id);

    // Test complex lineage (concatenated fields)
    auto complex_lineage = lineage_tracker.getFieldLineage("person", "person_source_value");

    ASSERT_TRUE(complex_lineage.has_value());
    EXPECT_GT(complex_lineage->source_fields.size(), 1);
}

} // namespace omop::e2e::test
