/**
 * @file test_comprehensive_omop_etl.cpp
 * @brief Comprehensive end-to-end tests for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <memory>
#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <chrono>

#include "core/pipeline.h"
#include "core/interfaces.h"
#include "common/configuration.h"
#include "common/logging.h"
#include "cdm/omop_tables.h"

namespace omop::test::e2e {

using namespace omop::core;
using namespace omop::common;

/**
 * @brief Mock extractor for E2E testing
 * 
 * This extractor simulates reading patient data from a source system
 * and provides it in batches for the ETL pipeline.
 */
class MockE2EExtractor : public IExtractor {
public:
    MockE2EExtractor(size_t total_records = 1000) 
        : total_records_(total_records), current_position_(0), initialized_(false) {}

    void initialize(const std::unordered_map<std::string, std::any>& config,
                   [[maybe_unused]] ProcessingContext& context) override {
        initialized_ = true;
        if (config.find("total_records") != config.end()) {
            total_records_ = std::any_cast<size_t>(config.at("total_records"));
        }
    }

    RecordBatch extract_batch(size_t batch_size, [[maybe_unused]] ProcessingContext& context) override {
        RecordBatch batch;
        size_t records_to_extract = std::min(batch_size, total_records_ - current_position_);
        
        for (size_t i = 0; i < records_to_extract; ++i) {
            Record record;
            size_t patient_id = current_position_ + i + 1;
            
            // Create realistic patient data
            record.setField("patient_id", static_cast<int>(patient_id));
            record.setField("first_name", std::string("Patient") + std::to_string(patient_id));
            record.setField("last_name", std::string("Surname") + std::to_string(patient_id));
            record.setField("birth_date", "1980-01-01");
            record.setField("gender", patient_id % 2 == 0 ? "M" : "F");
            record.setField("race", "White");
            record.setField("ethnicity", "Not Hispanic or Latino");
            
            // Add clinical data
            record.setField("diagnosis_code", std::string("I10")); // Hypertension
            record.setField("procedure_code", std::string("99213")); // Office visit
            record.setField("medication", std::string("Lisinopril"));
            record.setField("lab_value", 120.5 + (i % 50)); // Systolic BP
            
            batch.addRecord(std::move(record));
        }
        
        current_position_ += records_to_extract;
        return batch;
    }

    bool has_more_data() const override {
        return current_position_ < total_records_;
    }

    std::string get_type() const override { return "mock_e2e_extractor"; }

    void finalize([[maybe_unused]] ProcessingContext& context) override {
        // Cleanup
    }

    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {
            {"total_records", total_records_},
            {"extracted", current_position_},
            {"initialized", initialized_}
        };
    }

private:
    size_t total_records_;
    size_t current_position_;
    bool initialized_;
};

/**
 * @brief Mock transformer for E2E testing
 * 
 * This transformer converts source data to OMOP CDM format,
 * applying validation rules and data quality checks.
 */
class MockE2ETransformer : public ITransformer {
public:
    MockE2ETransformer() : initialized_(false), transformed_count_(0), failed_count_(0) {}

    void initialize([[maybe_unused]] const std::unordered_map<std::string, std::any>& config,
                   [[maybe_unused]] ProcessingContext& context) override {
        initialized_ = true;
    }

    std::optional<Record> transform(const Record& record, [[maybe_unused]] ProcessingContext& context) override {
        try {
            Record transformed;
            
            // Transform to OMOP Person table format
            if (record.hasField("patient_id")) {
                transformed.setField("person_id", record.getField("patient_id"));
            }
            
            // Gender concept mapping
            if (record.hasField("gender")) {
                std::string gender = record.getFieldAs<std::string>("gender");
                int gender_concept_id = (gender == "M") ? 8507 : 8532; // OMOP gender concepts
                transformed.setField("gender_concept_id", gender_concept_id);
            }
            
            // Birth date
            if (record.hasField("birth_date")) {
                transformed.setField("birth_datetime", record.getField("birth_date"));
            }
            
            // Race concept mapping
            if (record.hasField("race")) {
                transformed.setField("race_concept_id", 8527); // White
            }
            
            // Ethnicity concept mapping  
            if (record.hasField("ethnicity")) {
                transformed.setField("ethnicity_concept_id", 38003564); // Not Hispanic
            }
            
            // Add OMOP metadata
            transformed.setField("person_source_value", record.getField("patient_id"));
            transformed.setField("gender_source_value", record.getField("gender"));
            transformed.setField("race_source_value", record.getField("race"));
            transformed.setField("ethnicity_source_value", record.getField("ethnicity"));
            
            transformed_count_++;
            return transformed;
            
        } catch (const std::exception& e) {
            failed_count_++;
            return std::nullopt;
        }
    }

    RecordBatch transform_batch(const RecordBatch& batch, [[maybe_unused]] ProcessingContext& context) override {
        RecordBatch transformed_batch;
        for (const auto& record : batch) {
            auto result = transform(record, context);
            if (result) {
                transformed_batch.addRecord(std::move(*result));
            }
        }
        return transformed_batch;
    }

    std::string get_type() const override { return "mock_e2e_transformer"; }

    ValidationResult validate(const Record& record) const override {
        ValidationResult result;
        
        // Validate required fields
        if (!record.hasField("patient_id")) {
            result.add_error("patient_id", "Missing required field", "required");
        }
        
        return result;
    }

    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {
            {"transformed_count", static_cast<size_t>(transformed_count_)},
            {"failed_count", static_cast<size_t>(failed_count_)},
            {"initialized", initialized_}
        };
    }

private:
    bool initialized_;
    std::atomic<size_t> transformed_count_;
    std::atomic<size_t> failed_count_;
};

/**
 * @brief Mock loader for E2E testing
 * 
 * This loader simulates loading data into the OMOP CDM database,
 * with proper transaction management and error handling.
 */
class MockE2ELoader : public ILoader {
public:
    MockE2ELoader() : initialized_(false), loaded_count_(0), failed_count_(0), commit_count_(0) {}

    void initialize([[maybe_unused]] const std::unordered_map<std::string, std::any>& config,
                   [[maybe_unused]] ProcessingContext& context) override {
        initialized_ = true;
    }

    bool load(const Record& record, [[maybe_unused]] ProcessingContext& context) override {
        try {
            // Validate OMOP format
            if (!record.hasField("person_id")) {
                failed_count_++;
                return false;
            }
            
            // Simulate database insertion
            loaded_records_.push_back(record);
            loaded_count_++;
            return true;
            
        } catch (const std::exception& e) {
            failed_count_++;
            return false;
        }
    }

    size_t load_batch(const RecordBatch& batch, [[maybe_unused]] ProcessingContext& context) override {
        size_t loaded = 0;
        for (const auto& record : batch) {
            if (load(record, context)) {
                loaded++;
            }
        }
        return loaded;
    }

    void commit([[maybe_unused]] ProcessingContext& context) override {
        commit_count_++;
    }

    void rollback([[maybe_unused]] ProcessingContext& context) override {
        // Rollback simulation
    }

    std::string get_type() const override { return "mock_e2e_loader"; }

    void finalize([[maybe_unused]] ProcessingContext& context) override {
        // Finalization
    }

    std::unordered_map<std::string, std::any> get_statistics() const override {
        return {
            {"loaded_count", static_cast<size_t>(loaded_count_)},
            {"failed_count", static_cast<size_t>(failed_count_)},
            {"commit_count", static_cast<size_t>(commit_count_)},
            {"initialized", initialized_}
        };
    }

    // Test helper methods
    const std::vector<Record>& get_loaded_records() const { return loaded_records_; }
    size_t get_loaded_count() const { return loaded_count_; }

private:
    bool initialized_;
    std::atomic<size_t> loaded_count_;
    std::atomic<size_t> failed_count_;
    std::atomic<size_t> commit_count_;
    std::vector<Record> loaded_records_;
};

/**
 * @brief Comprehensive E2E test fixture
 * 
 * This fixture sets up a complete ETL pipeline environment for testing
 * end-to-end functionality with realistic data volumes and scenarios.
 */
class ComprehensiveOMOPETLTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize logging
        LoggingConfig::initialize_default();
        
        // Create test data directory
        test_data_dir_ = "/tmp/omop_e2e_test";
        std::filesystem::create_directories(test_data_dir_);
        
        // Initialize configuration
        config_manager_ = std::make_unique<ConfigurationManager>();
        
        // Create CSV test file
        createTestDataFile();
    }

    void TearDown() override {
        // Cleanup test directory
        std::filesystem::remove_all(test_data_dir_);
    }

    void createTestDataFile() {
        std::string csv_path = test_data_dir_ + "/patients.csv";
        std::ofstream csv_file(csv_path);
        
        // CSV header
        csv_file << "patient_id,first_name,last_name,birth_date,gender,race,ethnicity,diagnosis_code,procedure_code,medication,lab_value\n";
        
        // Generate test data
        for (int i = 1; i <= 100; ++i) {
            csv_file << i << ",Patient" << i << ",Surname" << i << ",1980-01-01,"
                    << (i % 2 == 0 ? "M" : "F") << ",White,Not Hispanic or Latino,"
                    << "I10,99213,Lisinopril," << (120.5 + (i % 50)) << "\n";
        }
        
        csv_file.close();
        csv_test_file_ = csv_path;
    }

    // Test components
    std::unique_ptr<ConfigurationManager> config_manager_;
    std::string test_data_dir_;
    std::string csv_test_file_;
};

// ============================================================================
// E2E TESTS
// ============================================================================

/**
 * @brief Test complete ETL pipeline execution
 * 
 * This test verifies that the entire ETL pipeline can process data
 * from source to target with proper transformation and validation.
 */
TEST_F(ComprehensiveOMOPETLTest, CompleteETLPipelineExecution) {
    // Configure pipeline
    PipelineConfig config;
    config.batch_size = 50;
    config.max_parallel_batches = 2;
    config.enable_checkpointing = true;
    config.checkpoint_interval = std::chrono::seconds(100);
    config.stop_on_error = false;
    config.error_threshold = 0.05; // 5% error threshold
    
    auto pipeline = PipelineBuilder()
        .with_config(config)
        .with_extractor(std::make_unique<MockE2EExtractor>(500))
        .with_transformer(std::make_unique<MockE2ETransformer>())
        .with_loader(std::make_unique<MockE2ELoader>())
        .build();
    
    // Execute pipeline
    auto future = pipeline->start("e2e_comprehensive_test");
    auto job_info = future.get();
    
    // Verify execution results
    EXPECT_EQ(job_info.status, JobStatus::Completed);
    EXPECT_EQ(job_info.processed_records, 500);
    EXPECT_EQ(job_info.error_records, 0);
    
    // Verify extractor statistics
    auto extractor_stats = pipeline->get_extractor()->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(extractor_stats["extracted"]), 500);
    EXPECT_TRUE(std::any_cast<bool>(extractor_stats["initialized"]));
    
    // Verify transformer statistics
    auto transformer_stats = pipeline->get_transformer()->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(transformer_stats["transformed_count"]), 500);
    EXPECT_EQ(std::any_cast<size_t>(transformer_stats["failed_count"]), 0);
    
    // Verify loader statistics
    auto loader_stats = pipeline->get_loader()->get_statistics();
    EXPECT_EQ(std::any_cast<size_t>(loader_stats["loaded_count"]), 500);
    EXPECT_GT(std::any_cast<size_t>(loader_stats["commit_count"]), 0);
    
    // Verify data quality
    const auto& loaded_records = pipeline->get_loader()->get_loaded_records();
    EXPECT_EQ(loaded_records.size(), 500);
    
    // Check OMOP format compliance
    const auto& first_record = loaded_records[0];
    EXPECT_TRUE(first_record.hasField("person_id"));
    EXPECT_TRUE(first_record.hasField("gender_concept_id"));
    EXPECT_TRUE(first_record.hasField("birth_datetime"));
    EXPECT_TRUE(first_record.hasField("race_concept_id"));
    EXPECT_TRUE(first_record.hasField("ethnicity_concept_id"));
}

/**
 * @brief Test pipeline with large data volumes
 * 
 * This test ensures the pipeline can handle realistic data volumes
 * that would be encountered in a healthcare setting.
 */
TEST_F(ComprehensiveOMOPETLTest, LargeVolumeDataProcessing) {
    PipelineConfig config;
    config.batch_size = 100;
    config.max_parallel_batches = 4;
    config.enable_checkpointing = true;
    config.checkpoint_interval = std::chrono::seconds(1000);
    
    auto pipeline = PipelineBuilder()
        .with_config(config)
        .with_extractor(std::make_unique<MockE2EExtractor>(10000))
        .with_transformer(std::make_unique<MockE2ETransformer>())
        .with_loader(std::make_unique<MockE2ELoader>())
        .build();
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    auto future = pipeline->start("e2e_large_volume_test");
    auto job_info = future.get();
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Verify results
    EXPECT_EQ(job_info.status, JobStatus::Completed);
    EXPECT_EQ(job_info.processed_records, 10000);
    EXPECT_EQ(pipeline->get_loader()->get_loaded_count(), 10000);
    
    // Performance check - should process at least 1000 records/second
    double records_per_second = 10000.0 / (duration.count() / 1000.0);
    EXPECT_GT(records_per_second, 1000.0);
    
    std::cout << "Processed " << job_info.processed_records 
              << " records in " << duration.count() << "ms ("
              << records_per_second << " records/sec)" << std::endl;
}

/**
 * @brief Test error handling and recovery
 * 
 * This test verifies that the pipeline properly handles errors
 * and can recover or fail gracefully based on configuration.
 */
TEST_F(ComprehensiveOMOPETLTest, ErrorHandlingAndRecovery) {
    PipelineConfig config;
    config.batch_size = 10;
    config.stop_on_error = false;
    config.error_threshold = 0.1; // 10% error threshold
    // Note: max_retries is not available in PipelineConfig
    
    auto pipeline = PipelineBuilder()
        .with_config(config)
        .with_extractor(std::make_unique<MockE2EExtractor>(100))
        .with_transformer(std::make_unique<MockE2ETransformer>())
        .with_loader(std::make_unique<MockE2ELoader>())
        .build();
    
    auto future = pipeline->start("e2e_error_handling_test");
    auto job_info = future.get();
    
    // Should complete despite some potential errors
    EXPECT_EQ(job_info.status, JobStatus::Completed);
    EXPECT_GT(job_info.processed_records, 80); // At least 80% should process
}

/**
 * @brief Test concurrent pipeline execution
 * 
 * This test verifies that multiple pipelines can run concurrently
 * without interfering with each other.
 */
TEST_F(ComprehensiveOMOPETLTest, ConcurrentPipelineExecution) {
    const int num_pipelines = 3;
    std::vector<std::unique_ptr<ETLPipeline>> pipelines;
    std::vector<std::future<JobInfo>> futures;
    
    // Create multiple pipelines
    for (int i = 0; i < num_pipelines; ++i) {
        PipelineConfig config;
        config.batch_size = 25;
        config.max_parallel_batches = 2;
        
        auto pipeline = PipelineBuilder()
            .with_config(config)
            .with_extractor(std::make_unique<MockE2EExtractor>(200))
            .with_transformer(std::make_unique<MockE2ETransformer>())
            .with_loader(std::make_unique<MockE2ELoader>())
            .build();
        
        // Start pipeline
        futures.push_back(pipeline->start("e2e_concurrent_test_" + std::to_string(i)));
        pipelines.push_back(std::move(pipeline));
    }
    
    // Wait for all pipelines to complete
    size_t total_processed = 0;
    for (auto& future : futures) {
        auto job_info = future.get();
        EXPECT_EQ(job_info.status, JobStatus::Completed);
        EXPECT_EQ(job_info.processed_records, 200);
        total_processed += job_info.processed_records;
    }
    
    EXPECT_EQ(total_processed, num_pipelines * 200);
}

} // namespace omop::test::e2e