// tests/integration/resource/test_resource_management_integration.cpp
// Tests resource management, memory cleanup, and connection pooling across the ETL pipeline

#include <gtest/gtest.h>
#include <memory>
#include <thread>
#include <chrono>
#include <atomic>
#include "core/pipeline.h"
#include "extract/database_connector.h"
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "load/database_loader.h"
#include "transform/transformation_engine.h"
#include "common/logging.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"

namespace omop::test {

class ResourceManagementTest : public DatabaseFixture {
protected:
    void SetUp() override {
        DatabaseFixture::SetUp();

        // Initialize logging
        common::LoggingConfig::initialize_default();
        logger_ = common::Logger::get("resource-test");

        // Set resource limits for testing
        max_memory_mb_ = 512;
        max_connections_ = 10;
        max_file_handles_ = 100;
    }

    void TearDown() override {
        // Verify all resources are cleaned up
        EXPECT_EQ(0, active_connections_.load());
        EXPECT_EQ(0, active_file_handles_.load());

        DatabaseFixture::TearDown();
    }

    // Helper to track resource usage
    void trackResourceUsage() {
        initial_memory_ = getCurrentMemoryUsage();
        initial_handles_ = getOpenFileHandles();
    }

    void verifyResourcesReleased() {
        auto final_memory = getCurrentMemoryUsage();
        auto final_handles = getOpenFileHandles();

        // Allow small memory increase for test overhead
        EXPECT_LE(final_memory, initial_memory_ + 10 * 1024 * 1024); // 10MB tolerance
        EXPECT_EQ(initial_handles_, final_handles);
    }

    size_t getCurrentMemoryUsage() {
        // Platform-specific memory usage detection
        // This is a simplified version - real implementation would use platform APIs
        return 0; // Placeholder
    }

    size_t getOpenFileHandles() {
        // Platform-specific file handle counting
        // This is a simplified version - real implementation would use platform APIs
        return 0; // Placeholder
    }

protected:
    std::shared_ptr<common::Logger> logger_;
    size_t max_memory_mb_;
    size_t max_connections_;
    size_t max_file_handles_;

    std::atomic<size_t> active_connections_{0};
    std::atomic<size_t> active_file_handles_{0};

    size_t initial_memory_;
    size_t initial_handles_;
};

// Test connection pool resource management
TEST_F(ResourceManagementTest, ConnectionPoolResourceManagement) {
    // Create connection pool with limited size
    auto connection_factory = [this]() {
        active_connections_++;
        auto conn = createTestConnection();
        return conn;
    };

    extract::ConnectionPool pool(2, 5, connection_factory);

    // Test acquiring connections up to limit
    std::vector<std::unique_ptr<extract::IDatabaseConnection>> connections;

    // Acquire maximum connections
    for (size_t i = 0; i < 5; ++i) {
        auto conn = pool.acquire(1000);
        ASSERT_NE(nullptr, conn);
        connections.push_back(std::move(conn));
    }

    EXPECT_EQ(5, active_connections_.load());

    // Try to acquire one more - should timeout
    auto extra_conn = pool.acquire(100);
    EXPECT_EQ(nullptr, extra_conn);

    // Release one connection
    pool.release(std::move(connections.back()));
    connections.pop_back();

    // Now we should be able to acquire again
    extra_conn = pool.acquire(100);
    ASSERT_NE(nullptr, extra_conn);

    // Release all connections
    for (auto& conn : connections) {
        pool.release(std::move(conn));
    }
    pool.release(std::move(extra_conn));

    // Verify pool statistics
    auto stats = pool.get_statistics();
    EXPECT_EQ(5, stats.total_connections);
    EXPECT_EQ(0, stats.active_connections);
    EXPECT_EQ(5, stats.idle_connections);
}

// Test file handle management with multiple extractors
TEST_F(ResourceManagementTest, FileHandleManagement) {
    trackResourceUsage();

    // Create multiple file-based extractors
    std::vector<std::unique_ptr<core::IExtractor>> extractors;

    // Create CSV extractors
    for (int i = 0; i < 10; ++i) {
        auto csv_file = generateTestCSV(1000,
            fmt::format("test_file_{}.csv", i));

        auto extractor = std::make_unique<extract::CsvExtractor>();
        std::unordered_map<std::string, std::any> config{
            {"filepath", csv_file}
        };

        core::ProcessingContext context;
        extractor->initialize(config, context);
        extractors.push_back(std::move(extractor));
    }

    // Create JSON extractors
    for (int i = 0; i < 10; ++i) {
        auto json_file = generateTestJSON(100,
            fmt::format("test_file_{}.json", i));

        auto extractor = std::make_unique<extract::JsonExtractor>();
        std::unordered_map<std::string, std::any> config{
            {"filepath", json_file}
        };

        core::ProcessingContext context;
        extractor->initialize(config, context);
        extractors.push_back(std::move(extractor));
    }

    // Process some data from each extractor
    core::ProcessingContext context;
    for (auto& extractor : extractors) {
        auto batch = extractor->extract_batch(10, context);
        EXPECT_GT(batch.size(), 0);
    }

    // Finalize all extractors
    for (auto& extractor : extractors) {
        extractor->finalize(context);
    }

    // Clear extractors to force cleanup
    extractors.clear();

    // Verify resources are released
    verifyResourcesReleased();
}

// Test memory management during large batch processing
TEST_F(ResourceManagementTest, MemoryManagementLargeBatches) {
    trackResourceUsage();

    // Create pipeline with memory-intensive operations
    core::PipelineConfig config;
    config.batch_size = 10000;
    config.max_parallel_batches = 4;
    config.queue_size = 50000;

    auto pipeline = std::make_unique<core::ETLPipeline>(config);

    // Set up extractor for large dataset
    auto csv_file = generateTestCSV(100000, "large_dataset.csv");
    auto extractor = std::make_unique<extract::CsvExtractor>();

    // Set up transformer
    auto transformer = std::make_unique<transform::TransformationEngine>();

    // Set up loader with batch optimization
    auto connection = createTestConnection();
    auto loader = std::make_unique<load::DatabaseLoader>(
        std::move(connection),
        load::DatabaseLoaderOptions{.batch_size = 5000}
    );

    pipeline = PipelineBuilder()
        .with_extractor(std::move(extractor))
        .with_transformer(std::move(transformer))
        .with_loader(std::move(loader))
        .build();

    // Run pipeline
    auto future = pipeline->start("memory-test-job");
    auto result = future.get();

    EXPECT_EQ(core::JobStatus::Completed, result.status);
    EXPECT_EQ(100000, result.total_records);

    // Verify memory is properly released
    pipeline.reset();

    // Give time for cleanup
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    verifyResourcesReleased();
}

// Test resource cleanup on pipeline failure
TEST_F(ResourceManagementTest, ResourceCleanupOnFailure) {
    trackResourceUsage();

    // Create pipeline that will fail during processing
    core::PipelineConfig config;
    config.batch_size = 1000;
    config.stop_on_error = true;

    auto pipeline = std::make_unique<core::ETLPipeline>(config);

    // Set up extractor
    auto csv_file = generateTestCSV(5000, "failure_test.csv");
    auto extractor = std::make_unique<extract::CsvExtractor>();

    // Set up transformer that will fail on certain records
    class FailingTransformer : public core::ITransformer {
    public:
        void initialize(const std::unordered_map<std::string, std::any>&,
                       core::ProcessingContext&) override {}

        std::optional<core::Record> transform(const core::Record& record,
                                            core::ProcessingContext& context) override {
            transform_count_++;
            if (transform_count_ == 2500) {
                throw std::runtime_error("Simulated transformation failure");
            }
            return record;
        }

        core::RecordBatch transform_batch(const core::RecordBatch& batch,
                                        core::ProcessingContext& context) override {
            core::RecordBatch result;
            for (const auto& record : batch) {
                auto transformed = transform(record, context);
                if (transformed) {
                    result.addRecord(*transformed);
                }
            }
            return result;
        }

        std::string get_type() const override { return "failing"; }

        omop::common::ValidationResult validate(const core::Record&) const override {
            return omop::common::ValidationResult{};
        }

        std::unordered_map<std::string, std::any> get_statistics() const override {
            return {{"transform_count", transform_count_}};
        }

    private:
        mutable std::atomic<int> transform_count_{0};
    };

    auto transformer = std::make_unique<FailingTransformer>();

    // Set up loader
    auto connection = createTestConnection();
    auto loader = std::make_unique<load::DatabaseLoader>(
        std::move(connection)
    );

    pipeline = PipelineBuilder()
        .with_extractor(std::move(extractor))
        .with_transformer(std::move(transformer))
        .with_loader(std::move(loader))
        .build();

    // Run pipeline - should fail
    auto future = pipeline->start("failure-test-job");
    auto result = future.get();

    EXPECT_EQ(core::JobStatus::Failed, result.status);
    EXPECT_GT(result.processed_records, 0);
    EXPECT_LT(result.processed_records, 5000);

    // Verify resources are still properly cleaned up after failure
    pipeline.reset();

    // Give time for cleanup
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    verifyResourcesReleased();
}

// Test concurrent resource access
TEST_F(ResourceManagementTest, ConcurrentResourceAccess) {
    const int num_threads = 10;
    const int operations_per_thread = 100;

    // Create shared resources
    auto connection_factory = [this]() {
        return createTestConnection();
    };

    extract::ConnectionPool pool(5, 10, connection_factory);

    std::atomic<int> success_count{0};
    std::atomic<int> failure_count{0};

    // Launch concurrent threads
    std::vector<std::thread> threads;

    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&, thread_id = i]() {
            for (int j = 0; j < operations_per_thread; ++j) {
                try {
                    // Acquire connection
                    auto conn = pool.acquire(50);

                    if (conn) {
                        // Simulate some work
                        std::this_thread::sleep_for(
                            std::chrono::microseconds(100));

                        // Execute a simple query
                        auto result = conn->execute_query("SELECT 1");

                        // Release connection
                        pool.release(std::move(conn));

                        success_count++;
                    } else {
                        failure_count++;
                    }
                } catch (const std::exception& e) {
                    logger_->error("Thread {} operation {} failed: {}",
                                  thread_id, j, e.what());
                    failure_count++;
                }
            }
        });
    }

    // Wait for all threads
    for (auto& thread : threads) {
        thread.join();
    }

    // Verify results
    int total_operations = num_threads * operations_per_thread;
    EXPECT_GT(success_count.load(), 0);
    EXPECT_EQ(total_operations, success_count.load() + failure_count.load());

    // Verify pool state
    auto stats = pool.get_statistics();
    EXPECT_GE(stats.total_connections, 5);
    EXPECT_LE(stats.total_connections, 10);
    EXPECT_EQ(0, stats.active_connections);
}

// Test resource limits enforcement
TEST_F(ResourceManagementTest, ResourceLimitsEnforcement) {
    // Test batch size limits
    core::RecordBatch batch(1000);

    // Try to add more records than capacity
    for (int i = 0; i < 1500; ++i) {
        core::Record record;
        record.setField("id", i);
        record.setField("value", fmt::format("test_{}", i));

        if (i < 1000) {
            batch.addRecord(record);
        } else {
            // Should handle gracefully when at capacity
            EXPECT_NO_THROW(batch.addRecord(record));
        }
    }

    EXPECT_LE(batch.size(), 1500);
}

// Test transformer cache resource management
TEST_F(ResourceManagementTest, TransformerCacheManagement) {
    transform::TransformationCache cache(100);

    // Fill cache beyond capacity
    for (int i = 0; i < 200; ++i) {
        std::string key = fmt::format("key_{}", i);
        cache.put(key, i * 10);
    }

    // Verify cache size is limited
    auto stats = cache.get_stats();
    EXPECT_LE(stats.size, 100);

    // Verify LRU eviction
    // Early entries should be evicted
    for (int i = 0; i < 50; ++i) {
        std::string key = fmt::format("key_{}", i);
        auto value = cache.get(key);
        EXPECT_FALSE(value.has_value());
    }

    // Recent entries should still be present
    for (int i = 150; i < 200; ++i) {
        std::string key = fmt::format("key_{}", i);
        auto value = cache.get(key);
        EXPECT_TRUE(value.has_value());
        if (value) {
            EXPECT_EQ(i * 10, std::any_cast<int>(*value));
        }
    }
}

} // namespace omop::test