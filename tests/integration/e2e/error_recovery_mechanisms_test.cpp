// tests/integration/error_recovery/test_error_recovery_integration.cpp
// Tests error recovery mechanisms, retry logic, and graceful degradation across the ETL pipeline

#include <gtest/gtest.h>
#include <memory>
#include <thread>
#include <chrono>
#include <random>
#include "core/pipeline.h"
#include "core/job_manager.h"
#include "extract/database_connector.h"
#include "extract/csv_extractor.h"
#include "load/database_loader.h"
#include "transform/transformation_engine.h"
#include "common/logging.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"

namespace omop::test {

class ErrorRecoveryTest : public DatabaseFixture {
protected:
    void SetUp() override {
        DatabaseFixture::SetUp();

        // Initialize logging
        common::LoggingConfig::initialize_default();
        logger_ = common::Logger::get("error-recovery-test");

        // Set up job manager
        auto config = std::make_shared<common::ConfigurationManager>();
        job_manager_ = std::make_shared<core::JobManager>(config, logger_);
        job_manager_->start();
    }

    void TearDown() override {
        if (job_manager_) {
            job_manager_->stop();
        }
        DatabaseFixture::TearDown();
    }

    // Create a flaky database connection that fails intermittently
    class FlakyDatabaseConnection : public extract::IDatabaseConnection {
    public:
        FlakyDatabaseConnection(std::unique_ptr<extract::IDatabaseConnection> real_conn,
                               double failure_rate = 0.1)
            : real_connection_(std::move(real_conn)),
              failure_rate_(failure_rate),
              rng_(std::random_device{}()),
              dist_(0.0, 1.0) {}

        void connect(const ConnectionParams& params) override {
            maybeFailWithException("connect");
            real_connection_->connect(params);
        }

        void disconnect() override {
            real_connection_->disconnect();
        }

        bool is_connected() const override {
            return real_connection_->is_connected();
        }

        std::unique_ptr<IResultSet> execute_query(const std::string& sql) override {
            maybeFailWithException("execute_query");
            return real_connection_->execute_query(sql);
        }

        size_t execute_update(const std::string& sql) override {
            maybeFailWithException("execute_update");
            return real_connection_->execute_update(sql);
        }

        std::unique_ptr<IPreparedStatement> prepare_statement(const std::string& sql) override {
            maybeFailWithException("prepare_statement");
            return real_connection_->prepare_statement(sql);
        }

        void begin_transaction() override {
            maybeFailWithException("begin_transaction");
            real_connection_->begin_transaction();
        }

        void commit() override {
            maybeFailWithException("commit");
            real_connection_->commit();
        }

        void rollback() override {
            real_connection_->rollback();
        }

        std::string get_database_type() const override {
            return real_connection_->get_database_type();
        }

        std::string get_version() const override {
            return real_connection_->get_version();
        }

        void set_query_timeout(int seconds) override {
            real_connection_->set_query_timeout(seconds);
        }

        bool table_exists(const std::string& table_name,
                         const std::string& schema = "") const override {
            return real_connection_->table_exists(table_name, schema);
        }

    private:
        void maybeFailWithException(const std::string& operation) {
            if (dist_(rng_) < failure_rate_) {
                failure_count_++;
                throw common::DatabaseException(
                    fmt::format("Simulated {} failure", operation),
                    "TestDB", -1);
            }
            success_count_++;
        }

    public:
        mutable std::atomic<int> failure_count_{0};
        mutable std::atomic<int> success_count_{0};

    private:
        std::unique_ptr<extract::IDatabaseConnection> real_connection_;
        double failure_rate_;
        mutable std::mt19937 rng_;
        mutable std::uniform_real_distribution<double> dist_;
    };

protected:
    std::shared_ptr<common::Logger> logger_;
    std::shared_ptr<core::JobManager> job_manager_;
};

// Test automatic retry on transient failures
TEST_F(ErrorRecoveryTest, AutomaticRetryOnTransientFailure) {
    // Create job configuration with retry settings
    core::JobConfig job_config;
    job_config.job_id = "retry-test-001";
    job_config.job_name = "Retry Test Job";
    job_config.max_retries = 3;
    job_config.retry_delay = std::chrono::seconds(1);

    // Create pipeline with flaky components
    auto pipeline = std::make_unique<core::ETLPipeline>();

    // Set up extractor
    auto csv_file = generateTestCSV(1000, "retry_test.csv");
    auto extractor = std::make_unique<extract::CsvExtractor>();

    // Set up transformer
    auto transformer = std::make_unique<transform::TransformationEngine>();

    // Set up loader with flaky connection
    auto real_connection = createTestConnection();
    auto flaky_connection = std::make_unique<FlakyDatabaseConnection>(
        std::move(real_connection), 0.2); // 20% failure rate

    auto flaky_ptr = flaky_connection.get();

    auto loader = std::make_unique<load::DatabaseLoader>(
        std::move(flaky_connection),
        load::DatabaseLoaderOptions{.batch_size = 100}
    );

    pipeline = PipelineBuilder()
        .with_extractor(std::move(extractor))
        .with_transformer(std::move(transformer))
        .with_loader(std::move(loader))
        .build();

    // Submit job
    auto job = std::make_unique<core::Job>(job_config, std::move(pipeline));
    job_manager_->submitJob(job_config);

    // Wait for job completion
    std::this_thread::sleep_for(std::chrono::seconds(30));

    // Check job status
    auto final_job = job_manager_->getJob(job_config.job_id);
    ASSERT_NE(nullptr, final_job);

    // Job should eventually succeed despite transient failures
    auto status = final_job->getStatus();
    EXPECT_TRUE(status == core::JobStatus::Completed ||
                status == core::JobStatus::Failed);

    // Check retry count
    EXPECT_GT(final_job->getRetryCount(), 0);
    EXPECT_LE(final_job->getRetryCount(), 3);

    // Verify some failures occurred and were recovered from
    EXPECT_GT(flaky_ptr->failure_count_.load(), 0);
    EXPECT_GT(flaky_ptr->success_count_.load(), 0);
}

// Test checkpoint recovery after failure
TEST_F(ErrorRecoveryTest, CheckpointRecoveryAfterFailure) {
    // Create job with checkpointing enabled
    core::JobConfig job_config;
    job_config.job_id = "checkpoint-test-001";
    job_config.job_name = "Checkpoint Test Job";
    job_config.enable_checkpointing = true;
    job_config.checkpoint_interval = 500; // Checkpoint every 500 records

    // Create pipeline that will fail midway
    core::PipelineConfig pipeline_config;
    pipeline_config.batch_size = 100;

    auto pipeline = std::make_unique<core::ETLPipeline>(pipeline_config);

    // Set up components
    auto csv_file = generateTestCSV(2000, "checkpoint_test.csv");
    auto extractor = std::make_unique<extract::CsvExtractor>();

    // Create transformer that fails after 1000 records
    class FailAfterNTransformer : public core::ITransformer {
    public:
        explicit FailAfterNTransformer(int fail_after)
            : fail_after_(fail_after) {}

        void initialize(const std::unordered_map<std::string, std::any>&,
                       core::ProcessingContext&) override {}

        std::optional<core::Record> transform(const core::Record& record,
                                            core::ProcessingContext& context) override {
            count_++;
            if (count_ == fail_after_) {
                throw std::runtime_error("Simulated failure after N records");
            }
            return record;
        }

        core::RecordBatch transform_batch(const core::RecordBatch& batch,
                                        core::ProcessingContext& context) override {
            core::RecordBatch result;
            for (const auto& record : batch) {
                auto transformed = transform(record, context);
                if (transformed) {
                    result.addRecord(*transformed);
                }
            }
            return result;
        }

        std::string get_type() const override { return "fail_after_n"; }

        omop::common::ValidationResult validate(const core::Record&) const override {
            return omop::common::ValidationResult{};
        }

        std::unordered_map<std::string, std::any> get_statistics() const override {
            return {{"processed_count", count_.load()}};
        }

    private:
        int fail_after_;
        std::atomic<int> count_{0};
    };

    auto transformer = std::make_unique<FailAfterNTransformer>(1000);

    auto connection = createTestConnection();
    auto loader = std::make_unique<load::DatabaseLoader>(std::move(connection));

    pipeline->set_extractor(std::move(extractor));
    pipeline->set_transformer(std::move(transformer));
    pipeline->set_loader(std::move(loader));

    // Create job
    auto job = std::make_unique<core::Job>(job_config, std::move(pipeline));

    // Save checkpoint should be called multiple times before failure
    bool checkpoint_saved = job->saveCheckpoint();
    EXPECT_TRUE(checkpoint_saved);

    // Submit job - it will fail
    job_manager_->submitJob(job_config);

    // Wait for failure
    std::this_thread::sleep_for(std::chrono::seconds(10));

    auto failed_job = job_manager_->getJob(job_config.job_id);
    ASSERT_NE(nullptr, failed_job);
    EXPECT_EQ(core::JobStatus::Failed, failed_job->getStatus());

    // Create new job with same ID to resume from checkpoint
    auto resume_pipeline = std::make_unique<core::ETLPipeline>(pipeline_config);

    // Set up components again
    auto resume_extractor = std::make_unique<extract::CsvExtractor>();
    auto resume_transformer = std::make_unique<transform::TransformationEngine>();
    auto resume_connection = createTestConnection();
    auto resume_loader = std::make_unique<load::DatabaseLoader>(
        std::move(resume_connection));

    resume_pipeline->set_extractor(std::move(resume_extractor));
    resume_pipeline->set_transformer(std::move(resume_transformer));
    resume_pipeline->set_loader(std::move(resume_loader));

    // Create resume job
    core::JobConfig resume_config = job_config;
    resume_config.job_id = "checkpoint-test-001-resume";

    auto resume_job = std::make_unique<core::Job>(resume_config,
                                                  std::move(resume_pipeline));

    // Load checkpoint
    bool checkpoint_loaded = resume_job->loadCheckpoint();
    EXPECT_TRUE(checkpoint_loaded);

    // Submit resume job
    job_manager_->submitJob(resume_config);

    // Wait for completion
    std::this_thread::sleep_for(std::chrono::seconds(10));

    auto completed_job = job_manager_->getJob(resume_config.job_id);
    ASSERT_NE(nullptr, completed_job);

    // Verify job completed successfully
    EXPECT_EQ(core::JobStatus::Completed, completed_job->getStatus());

    // Verify total records processed
    auto stats = completed_job->getStatistics();
    EXPECT_GE(stats.total_records_processed, 1000); // At least resumed from checkpoint
}

// Test graceful degradation with partial failures
TEST_F(ErrorRecoveryTest, GracefulDegradationPartialFailures) {
    // Create pipeline that continues on errors
    core::PipelineConfig config;
    config.batch_size = 100;
    config.error_threshold = 0.2; // Allow up to 20% errors
    config.stop_on_error = false;

    auto pipeline = std::make_unique<core::ETLPipeline>(config);

    // Set up extractor
    auto csv_file = generateTestCSV(1000, "partial_failure_test.csv");
    auto extractor = std::make_unique<extract::CsvExtractor>();

    // Create transformer that fails randomly
    class RandomFailTransformer : public core::ITransformer {
    public:
        RandomFailTransformer(double failure_rate)
            : failure_rate_(failure_rate),
              rng_(std::random_device{}()),
              dist_(0.0, 1.0) {}

        void initialize(const std::unordered_map<std::string, std::any>&,
                       core::ProcessingContext&) override {}

        std::optional<core::Record> transform(const core::Record& record,
                                            core::ProcessingContext& context) override {
            total_count_++;

            if (dist_(rng_) < failure_rate_) {
                failure_count_++;
                context.increment_errors();
                // Return empty optional to skip record
                return std::nullopt;
            }

            success_count_++;
            return record;
        }

        core::RecordBatch transform_batch(const core::RecordBatch& batch,
                                        core::ProcessingContext& context) override {
            core::RecordBatch result;
            for (const auto& record : batch) {
                auto transformed = transform(record, context);
                if (transformed) {
                    result.addRecord(*transformed);
                }
            }
            return result;
        }

        std::string get_type() const override { return "random_fail"; }

        omop::common::ValidationResult validate(const core::Record&) const override {
            return omop::common::ValidationResult{};
        }

        std::unordered_map<std::string, std::any> get_statistics() const override {
            return {
                {"total_count", total_count_.load()},
                {"success_count", success_count_.load()},
                {"failure_count", failure_count_.load()}
            };
        }

    private:
        double failure_rate_;
        mutable std::mt19937 rng_;
        mutable std::uniform_real_distribution<double> dist_;

    public:
        std::atomic<int> total_count_{0};
        std::atomic<int> success_count_{0};
        std::atomic<int> failure_count_{0};
    };

    auto transformer = std::make_unique<RandomFailTransformer>(0.1); // 10% failure
    auto transformer_ptr = transformer.get();

    auto connection = createTestConnection();
    auto loader = std::make_unique<load::DatabaseLoader>(std::move(connection));

    pipeline->set_extractor(std::move(extractor));
    pipeline->set_transformer(std::move(transformer));
    pipeline->set_loader(std::move(loader));

    // Run pipeline
    auto future = pipeline->start("partial-failure-job");
    auto result = future.get();

    // Pipeline should complete despite partial failures
    EXPECT_EQ(core::JobStatus::Completed, result.status);
    EXPECT_EQ(1000, result.total_records);

    // Verify error handling
    EXPECT_GT(result.error_records, 0);
    EXPECT_LT(result.error_records, 200); // Less than 20% threshold

    // Verify transformer statistics
    EXPECT_EQ(1000, transformer_ptr->total_count_.load());
    EXPECT_GT(transformer_ptr->failure_count_.load(), 0);
    EXPECT_GT(transformer_ptr->success_count_.load(), 800);
}

// Test error aggregation and reporting
TEST_F(ErrorRecoveryTest, ErrorAggregationAndReporting) {
    // Create pipeline with detailed error tracking
    core::PipelineConfig config;
    config.batch_size = 50;
    config.validate_records = true;

    auto pipeline = std::make_unique<core::ETLPipeline>(config);

    // Generate test data with known errors
    auto csv_file = generateTestCSVWithErrors(1000, "error_tracking_test.csv", {
        {100, "missing_required_field"},
        {200, "invalid_date_format"},
        {300, "out_of_range_value"},
        {400, "duplicate_key"},
        {500, "invalid_concept_id"}
    });

    auto extractor = std::make_unique<extract::CsvExtractor>();

    // Create transformer with validation
    class ValidatingTransformer : public core::ITransformer {
    public:
        void initialize(const std::unordered_map<std::string, std::any>&,
                       core::ProcessingContext&) override {}

        std::optional<core::Record> transform(const core::Record& record,
                                            core::ProcessingContext& context) override {
            // Validate record
            auto validation_result = validate(record);

            if (!validation_result.is_valid()) {
                for (const auto& error : validation_result.errors()) {
                    error_counts_[error.rule_name]++;
                    context.log("error", fmt::format("Validation failed: {} - {}",
                                                   error.field_name,
                                                   error.error_message));
                }
                context.increment_errors();
                return std::nullopt;
            }

            return record;
        }

        core::RecordBatch transform_batch(const core::RecordBatch& batch,
                                        core::ProcessingContext& context) override {
            core::RecordBatch result;
            for (const auto& record : batch) {
                auto transformed = transform(record, context);
                if (transformed) {
                    result.addRecord(*transformed);
                }
            }
            return result;
        }

        std::string get_type() const override { return "validating"; }

        omop::common::ValidationResult validate(const core::Record& record) const override {
            omop::common::ValidationResult result;

            // Check for required fields
            if (!record.hasField("person_id")) {
                result.add_error("person_id", "Required field missing",
                               "missing_required_field");
            }

            // Check date format
            if (record.hasField("birth_date")) {
                try {
                    auto date_str = std::any_cast<std::string>(
                        record.getField("birth_date"));
                    // Simple date validation
                    if (date_str.find("INVALID") != std::string::npos) {
                        result.add_error("birth_date", "Invalid date format",
                                       "invalid_date_format");
                    }
                } catch (...) {
                    result.add_error("birth_date", "Date parsing failed",
                                   "invalid_date_format");
                }
            }

            // Check numeric ranges
            if (record.hasField("age")) {
                try {
                    auto age = std::any_cast<int>(record.getField("age"));
                    if (age < 0 || age > 150) {
                        result.add_error("age", "Age out of valid range",
                                       "out_of_range_value");
                    }
                } catch (...) {
                    result.add_error("age", "Invalid age value",
                                   "out_of_range_value");
                }
            }

            return result;
        }

        std::unordered_map<std::string, std::any> get_statistics() const override {
            std::unordered_map<std::string, std::any> stats;
            for (const auto& [error_type, count] : error_counts_) {
                stats[error_type] = count;
            }
            return stats;
        }

    private:
        mutable std::unordered_map<std::string, int> error_counts_;
    };

    auto transformer = std::make_unique<ValidatingTransformer>();

    auto connection = createTestConnection();
    auto loader = std::make_unique<load::DatabaseLoader>(std::move(connection));

    pipeline->set_extractor(std::move(extractor));
    pipeline->set_transformer(std::move(transformer));
    pipeline->set_loader(std::move(loader));

    // Set error callback to collect error information
    std::vector<std::string> collected_errors;
    std::mutex error_mutex;

    pipeline->set_error_callback(
        [&collected_errors, &error_mutex](const std::string& stage,
                                         const std::exception& e) {
            std::lock_guard<std::mutex> lock(error_mutex);
            collected_errors.push_back(fmt::format("{}: {}", stage, e.what()));
        });

    // Run pipeline
    auto future = pipeline->start("error-aggregation-job");
    auto result = future.get();

    // Verify error collection
    EXPECT_GT(result.error_records, 0);
    EXPECT_GE(collected_errors.size(), 5); // At least our 5 known errors

    // Check error messages contain expected patterns
    bool has_missing_field = false;
    bool has_invalid_date = false;
    bool has_out_of_range = false;

    for (const auto& error : result.errors) {
        if (error.find("missing_required_field") != std::string::npos) {
            has_missing_field = true;
        }
        if (error.find("invalid_date_format") != std::string::npos) {
            has_invalid_date = true;
        }
        if (error.find("out_of_range_value") != std::string::npos) {
            has_out_of_range = true;
        }
    }

    EXPECT_TRUE(has_missing_field);
    EXPECT_TRUE(has_invalid_date);
    EXPECT_TRUE(has_out_of_range);
}

// Test transaction rollback on failure
TEST_F(ErrorRecoveryTest, TransactionRollbackOnFailure) {
    // Create loader with transaction support
    auto connection = createTestConnection();

    load::DatabaseLoaderOptions options;
    options.batch_size = 100;
    options.commit_interval = 500;

    auto loader = std::make_unique<load::DatabaseLoader>(
        std::move(connection), options);

    // Initialize loader
    std::unordered_map<std::string, std::any> config{
        {"table", "test_rollback"},
        {"schema", "test"}
    };

    core::ProcessingContext context;
    loader->initialize(config, context);

    // Begin loading records
    bool transaction_started = false;

    try {
        // Load some records successfully
        core::RecordBatch batch;
        for (int i = 0; i < 400; ++i) {
            core::Record record;
            record.setField("id", i);
            record.setField("value", fmt::format("test_{}", i));
            batch.addRecord(record);
        }

        size_t loaded = loader->load_batch(batch, context);
        EXPECT_EQ(400, loaded);
        transaction_started = true;

        // Now try to load records that will cause an error
        core::RecordBatch bad_batch;
        for (int i = 400; i < 500; ++i) {
            core::Record record;
            record.setField("id", i);
            // Intentionally cause an error (e.g., constraint violation)
            record.setField("id", 1); // Duplicate ID
            bad_batch.addRecord(record);
        }

        // This should fail
        EXPECT_THROW(loader->load_batch(bad_batch, context), std::exception);

        // Rollback should be called
        loader->rollback(context);

    } catch (const std::exception& e) {
        logger_->error("Expected error during load: {}", e.what());
        if (transaction_started) {
            loader->rollback(context);
        }
    }

    // Verify no records were committed
    auto verify_connection = createTestConnection();
    auto result = verify_connection->execute_query(
        "SELECT COUNT(*) FROM test.test_rollback");

    if (result && result->next()) {
        auto count = std::any_cast<int64_t>(result->get_value(0));
        EXPECT_EQ(0, count); // No records should be committed
    }
}

// Test timeout and cancellation handling
TEST_F(ErrorRecoveryTest, TimeoutAndCancellationHandling) {
    // Create job with timeout
    core::JobConfig job_config;
    job_config.job_id = "timeout-test-001";
    job_config.job_name = "Timeout Test Job";
    job_config.timeout = std::chrono::seconds(5);

    // Create pipeline with slow processing
    auto pipeline = std::make_unique<core::ETLPipeline>();

    auto csv_file = generateTestCSV(10000, "timeout_test.csv");
    auto extractor = std::make_unique<extract::CsvExtractor>();

    // Create slow transformer
    class SlowTransformer : public core::ITransformer {
    public:
        void initialize(const std::unordered_map<std::string, std::any>&,
                       core::ProcessingContext&) override {}

        std::optional<core::Record> transform(const core::Record& record,
                                            core::ProcessingContext& context) override {
            // Simulate slow processing
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            return record;
        }

        core::RecordBatch transform_batch(const core::RecordBatch& batch,
                                        core::ProcessingContext& context) override {
            core::RecordBatch result;
            for (const auto& record : batch) {
                auto transformed = transform(record, context);
                if (transformed) {
                    result.addRecord(*transformed);
                }
            }
            return result;
        }

        std::string get_type() const override { return "slow"; }

        omop::common::ValidationResult validate(const core::Record&) const override {
            return omop::common::ValidationResult{};
        }

        std::unordered_map<std::string, std::any> get_statistics() const override {
            return {};
        }
    };

    auto transformer = std::make_unique<SlowTransformer>();

    auto connection = createTestConnection();
    auto loader = std::make_unique<load::DatabaseLoader>(std::move(connection));

    pipeline->set_extractor(std::move(extractor));
    pipeline->set_transformer(std::move(transformer));
    pipeline->set_loader(std::move(loader));

    // Submit job
    auto job = std::make_unique<core::Job>(job_config, std::move(pipeline));
    job_manager_->submitJob(job_config);

    // Wait for timeout
    std::this_thread::sleep_for(std::chrono::seconds(7));

    auto timed_out_job = job_manager_->getJob(job_config.job_id);
    ASSERT_NE(nullptr, timed_out_job);

    // Job should be cancelled due to timeout
    auto status = timed_out_job->getStatus();
    EXPECT_TRUE(status == core::JobStatus::Cancelled ||
                status == core::JobStatus::Failed);

    // Test manual cancellation
    core::JobConfig cancel_config;
    cancel_config.job_id = "cancel-test-001";
    cancel_config.job_name = "Cancel Test Job";

    auto cancel_pipeline = std::make_unique<core::ETLPipeline>();

    auto cancel_csv_file = generateTestCSV(10000, "cancel_test.csv");
    auto cancel_extractor = std::make_unique<extract::CsvExtractor>();
    auto cancel_transformer = std::make_unique<SlowTransformer>();
    auto cancel_connection = createTestConnection();
    auto cancel_loader = std::make_unique<load::DatabaseLoader>(
        std::move(cancel_connection));

    cancel_pipeline->set_extractor(std::move(cancel_extractor));
    cancel_pipeline->set_transformer(std::move(cancel_transformer));
    cancel_pipeline->set_loader(std::move(cancel_loader));

    auto cancel_job = std::make_unique<core::Job>(cancel_config,
                                                  std::move(cancel_pipeline));
    job_manager_->submitJob(cancel_config);

    // Let it run for a bit
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // Cancel the job
    bool cancelled = job_manager_->cancelJob(cancel_config.job_id);
    EXPECT_TRUE(cancelled);

    // Wait a bit more
    std::this_thread::sleep_for(std::chrono::seconds(2));

    auto cancelled_job = job_manager_->getJob(cancel_config.job_id);
    ASSERT_NE(nullptr, cancelled_job);
    EXPECT_EQ(core::JobStatus::Cancelled, cancelled_job->getStatus());
}

} // namespace omop::test