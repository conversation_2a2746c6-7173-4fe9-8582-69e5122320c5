// tests/integration/e2e/test_multi_tenant_etl_e2e.cpp
// Tests multi-tenant ETL scenarios with data isolation and concurrent processing
#include <gtest/gtest.h>
#include "service/etl_service.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/test_data_generator.h"
#include <thread>
#include <vector>

namespace omop::e2e::test {

class MultiTenantETLTest : public ::testing::Test {
protected:
    struct TenantConfig {
        std::string tenant_id;
        std::string source_schema;
        std::string target_schema;
        std::shared_ptr<common::ConfigurationManager> config;
        std::shared_ptr<ETLService> etl_service;
    };

    void SetUp() override {
        // Create configurations for multiple tenants
        for (int i = 1; i <= 3; ++i) {
            TenantConfig tenant;
            tenant.tenant_id = "tenant_" + std::to_string(i);
            tenant.source_schema = "source_" + tenant.tenant_id;
            tenant.target_schema = "cdm_" + tenant.tenant_id;

            tenant.config = std::make_shared<common::ConfigurationManager>();
            tenant.config->load_config_from_string(createTenantConfig(tenant));

            auto pipeline_manager = std::make_shared<core::PipelineManager>(2);
            tenant.etl_service = std::make_shared<ETLService>(tenant.config, pipeline_manager);

            tenants_.push_back(std::move(tenant));
        }

        // Initialize test database with tenant schemas
        initializeTenantSchemas();
    }

    void TearDown() override {
        // Clean up tenant data
        for (auto& tenant : tenants_) {
            cleanupTenantData(tenant.tenant_id);
        }
    }

    std::string createTenantConfig(const TenantConfig& tenant) {
        return std::format(R"(
            tenant_id: {}
            source_db:
                type: postgresql
                host: localhost
                port: 5432
                database: multi_tenant_db
                schema: {}
                username: tenant_user
                password: tenant_pass

            target_db:
                type: postgresql
                host: localhost
                port: 5432
                database: omop_cdm_multi
                schema: {}
                username: cdm_user
                password: cdm_pass

            etl:
                batch_size: 1000
                enable_multi_tenancy: true
                tenant_isolation: strict

            tables:
                person:
                    source_table: patients
                    target_table: person
                    tenant_column: tenant_id
                visit_occurrence:
                    source_table: visits
                    target_table: visit_occurrence
                    tenant_column: tenant_id
        )", tenant.tenant_id, tenant.source_schema, tenant.target_schema);
    }

    void initializeTenantSchemas() {
        // Initialize database schemas for each tenant
        for (const auto& tenant : tenants_) {
            db_fixture_.createSchema(tenant.source_schema);
            db_fixture_.createSchema(tenant.target_schema);

            // Generate test data for each tenant
            TestDataGenerator generator;
            generator.generatePatientData(tenant.source_schema, 1000);
            generator.generateVisitData(tenant.source_schema, 5000);
        }
    }

    void cleanupTenantData(const std::string& tenant_id) {
        // Clean up tenant-specific data
        db_fixture_.dropSchema("source_" + tenant_id);
        db_fixture_.dropSchema("cdm_" + tenant_id);
    }

    std::vector<TenantConfig> tenants_;
    DatabaseFixture db_fixture_;
};

// Tests concurrent ETL execution for multiple tenants
TEST_F(MultiTenantETLTest, ConcurrentTenantProcessing) {
    std::vector<std::thread> tenant_threads;
    std::vector<std::string> job_ids;
    std::mutex job_ids_mutex;

    // Start ETL for each tenant concurrently
    for (auto& tenant : tenants_) {
        tenant_threads.emplace_back([&tenant, &job_ids, &job_ids_mutex]() {
            auto job_map = tenant.etl_service->run_all_tables(true);

            std::lock_guard<std::mutex> lock(job_ids_mutex);
            for (const auto& [table, job_id] : job_map) {
                job_ids.push_back(job_id);
            }
        });
    }

    // Wait for all threads to complete
    for (auto& thread : tenant_threads) {
        thread.join();
    }

    // Verify all jobs completed successfully
    std::this_thread::sleep_for(std::chrono::seconds(10));

    for (size_t i = 0; i < tenants_.size(); ++i) {
        auto& tenant = tenants_[i];
        auto jobs = tenant.etl_service->get_all_job_results();

        for (const auto& job : jobs) {
            EXPECT_EQ(job.status, core::JobStatus::Completed)
                << "Job failed for tenant " << tenant.tenant_id;
            EXPECT_EQ(job.error_records, 0)
                << "Errors found for tenant " << tenant.tenant_id;
        }
    }
}

// Tests data isolation between tenants
TEST_F(MultiTenantETLTest, VerifyTenantDataIsolation) {
    // Run ETL for all tenants
    for (auto& tenant : tenants_) {
        tenant.etl_service->run_all_tables(false);
    }

    // Wait for completion
    std::this_thread::sleep_for(std::chrono::seconds(10));

    // Verify each tenant's data is isolated
    for (const auto& tenant : tenants_) {
        // Query person table for tenant
        auto query = std::format(
            "SELECT COUNT(*) FROM {}.person WHERE tenant_id = '{}'",
            tenant.target_schema, tenant.tenant_id
        );

        auto count = db_fixture_.executeScalar<int>(query);
        EXPECT_GT(count, 0) << "No data found for tenant " << tenant.tenant_id;

        // Verify no cross-tenant data
        auto cross_query = std::format(
            "SELECT COUNT(*) FROM {}.person WHERE tenant_id != '{}'",
            tenant.target_schema, tenant.tenant_id
        );

        auto cross_count = db_fixture_.executeScalar<int>(cross_query);
        EXPECT_EQ(cross_count, 0) << "Cross-tenant data found for " << tenant.tenant_id;
    }
}

// Tests resource allocation and throttling across tenants
TEST_F(MultiTenantETLTest, ResourceAllocationAcrossTenants) {
    // Configure different resource limits for each tenant
    for (size_t i = 0; i < tenants_.size(); ++i) {
        auto& tenant = tenants_[i];
        ETLJobRequest request;
        request.name = "Resource Test " + tenant.tenant_id;
        request.source_table = "patients";
        request.target_table = "person";

        // Different batch sizes for different tenants
        request.pipeline_config.batch_size = (i + 1) * 500;
        request.pipeline_config.max_parallel_batches = i + 1;

        tenant.etl_service->create_job(request);
    }

    // Monitor resource usage
    std::this_thread::sleep_for(std::chrono::seconds(5));

    // Verify resource limits were respected
    for (const auto& tenant : tenants_) {
        auto stats = tenant.etl_service->get_statistics();

        // Check memory usage is within limits
        auto memory_usage = std::any_cast<size_t>(stats["peak_memory_usage_mb"]);
        EXPECT_LT(memory_usage, 1024) << "Excessive memory use for " << tenant.tenant_id;
    }
}

} // namespace omop::e2e::test
