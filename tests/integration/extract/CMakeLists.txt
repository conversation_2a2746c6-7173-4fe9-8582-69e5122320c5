# Extract integration tests
set(EXTRACT_INTEGRATION_TEST_SOURCES
    csv_file_extraction_test.cpp
    database_source_extraction_test.cpp
    json_data_extraction_test.cpp
    multi_source_data_extraction_test.cpp
    platform_utilities_integration_test.cpp
    uk_healthcare_data_extraction_test.cpp
    extractor_base_functionality_test.cpp
    extraction_utilities_integration_test.cpp
)

add_executable(extract_integration_tests ${EXTRACT_INTEGRATION_TEST_SOURCES})

target_link_libraries(extract_integration_tests
    PRIVATE
        integration_test_helpers
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
#        omop_service
        gtest
        gtest_main
        gmock
)

target_include_directories(extract_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib/extract
        ${CMAKE_SOURCE_DIR}/tests/integration
        ${CMAKE_SOURCE_DIR}/tests/integration/test_helpers
)

add_test(
    NAME extract_integration_tests
    COMMAND extract_integration_tests
)

set_tests_properties(extract_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;extract"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)