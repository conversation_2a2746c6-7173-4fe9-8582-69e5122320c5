/**
 * @file test_uk_localized_extract_integration.cpp
 * @brief Comprehensive UK healthcare localized integration tests for extract library
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 *
 * Tests extract functionality with UK healthcare data formats, NHS numbers,
 * UK postcodes, SNOMED CT codes, ICD-10 codes, and proper regional formatting
 */

#include <gtest/gtest.h>
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/database_connector.h"
#include "extract/extract_utils.h"
#include "common/utilities.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/uk_test_utilities.h"
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <locale>
#include <sstream>

namespace omop::extract::test {

namespace fs = std::filesystem;

/**
 * @brief UK Healthcare localized test fixture for extract library
 * 
 * Provides comprehensive testing of extract functionality with UK healthcare
 * data including NHS numbers, GP practice codes, UK postcodes, and
 * proper regional formatting standards.
 */
class UKLocalizedExtractTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }

        // Create test directory
        test_dir_ = fs::temp_directory_path() / ("uk_extract_test_" + omop::common::CryptoUtils::generate_uuid());
        fs::create_directories(test_dir_);

        // UK healthcare constants
        uk_currency_symbol_ = "£";
        uk_date_format_ = "%d/%m/%Y";
        uk_datetime_format_ = "%d/%m/%Y %H:%M:%S";
        uk_temperature_unit_ = "°C";
        current_time_ = std::chrono::system_clock::now();
    }

    void TearDown() override {
        if (fs::exists(test_dir_)) {
            fs::remove_all(test_dir_);
        }
    }

    /**
     * @brief Create test CSV file with given content
     * @param filename Filename within test directory
     * @param lines CSV lines to write
     */
    void createCSVFile(const std::string& filename, const std::vector<std::string>& lines) {
        std::ofstream file(test_dir_ / filename);
        for (const auto& line : lines) {
            file << line << "\n";
        }
        file.close();
    }

    /**
     * @brief Create test JSON file with given content
     * @param filename Filename within test directory
     * @param json_content JSON content as string
     */
    void createJSONFile(const std::string& filename, const std::string& json_content) {
        std::ofstream file(test_dir_ / filename);
        file << json_content;
        file.close();
    }

    fs::path test_dir_;
    std::string uk_currency_symbol_;
    std::string uk_date_format_;
    std::string uk_datetime_format_;
    std::string uk_temperature_unit_;
    std::chrono::system_clock::time_point current_time_;
};

/**
 * @brief Test CSV extraction with NHS patient data and UK regional formatting
 * 
 * Validates extraction of healthcare data with proper NHS numbers, GP practice codes,
 * UK postcodes, and regional date/currency formatting standards.
 */
TEST_F(UKLocalizedExtractTest, NHSPatientDataExtraction) {
    // Create comprehensive NHS patient data CSV
    std::vector<std::string> patient_data = {
        "nhs_number,title,forename,surname,date_of_birth,postcode,gp_practice_code,registration_date,cost_band",
        omop::test::uk::generateNHSNumber(123456789) + ",Mr,John,Smith,15/03/1980," + omop::test::uk::UKTestDataGenerator().generateUKPostcodeFromComponents("SW", 1, 2, "AB") + ",G12345,01/04/2020," + omop::common::UKLocalization::format_uk_currency(125.50),
        omop::test::uk::generateNHSNumber(234567890) + ",Mrs,Sarah,Jones,22/07/1975," + omop::test::uk::UKTestDataGenerator().generateUKPostcodeFromComponents("EC", 2, 3, "CD") + ",H23456,15/06/2019," + omop::common::UKLocalization::format_uk_currency(89.25),
        omop::test::uk::generateNHSNumber(345678901) + ",Dr,Michael,Brown,08/11/1965," + omop::test::uk::UKTestDataGenerator().generateUKPostcodeFromComponents("W", 1, 4, "EF") + ",J34567,03/09/2021," + omop::common::UKLocalization::format_uk_currency(0.00),
        omop::test::uk::generateNHSNumber(456789012) + ",Ms,Emily,Wilson,14/12/1990," + omop::test::uk::UKTestDataGenerator().generateUKPostcodeFromComponents("NW", 3, 1, "GH") + ",K45678,28/02/2022," + omop::common::UKLocalization::format_uk_currency(245.75)
    };

    createCSVFile("nhs_patients.csv", patient_data);

    // Configure CSV extractor with UK locale settings
    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "nhs_patients.csv").string()},
        {"date_format", uk_date_format_},
        {"locale", std::string("en_GB")},
        {"encoding", std::string("UTF-8")}
    };

    core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 4);

    // Validate NHS number format and check digits
    for (size_t i = 0; i < batch.size(); ++i) {
        const auto& record = batch.getRecord(i);
        std::string nhs_number = record.getFieldAs<std::string>("nhs_number");
        
        // NHS number should be 10 digits
        EXPECT_EQ(nhs_number.length(), 10);
        
        // Validate NHS number format
        for (char c : nhs_number) {
            EXPECT_TRUE(std::isdigit(c));
        }
        
        // Validate UK postcode format
        std::string postcode = record.getFieldAs<std::string>("postcode");
        EXPECT_TRUE(postcode.find(' ') != std::string::npos);
        
        // Validate GP practice code format (should start with letter)
        std::string gp_code = record.getFieldAs<std::string>("gp_practice_code");
        EXPECT_TRUE(std::isalpha(gp_code[0]));
        
        // Validate UK date format (DD/MM/YYYY)
        std::string date_of_birth = record.getFieldAs<std::string>("date_of_birth");
        EXPECT_TRUE(date_of_birth.find('/') != std::string::npos);
        
        // Validate UK currency format
        std::string cost_band = record.getFieldAs<std::string>("cost_band");
        EXPECT_TRUE(cost_band.find(uk_currency_symbol_) != std::string::npos);
    }
}

/**
 * @brief Test CSV extraction with SNOMED CT and ICD-10 medical codes
 * 
 * Validates extraction of clinical data with proper SNOMED CT concept IDs,
 * ICD-10 diagnostic codes, and UK healthcare terminology standards.
 */
TEST_F(UKLocalizedExtractTest, ClinicalCodesExtraction) {
    std::vector<std::string> clinical_data = {
        "patient_id,snomed_concept_id,snomed_description,icd10_code,icd10_description,diagnosis_date,temperature,blood_pressure",
        "1,38341003,Hypertensive disorder,I10,Essential hypertension,12/01/2023,36.8" + uk_temperature_unit_ + ",140/90 mmHg",
        "2,73211009,Diabetes mellitus,E11.9,Type 2 diabetes mellitus without complications,25/03/2023,37.2" + uk_temperature_unit_ + ",125/85 mmHg",
        "3,195967001,Asthma,J45.9,Asthma unspecified,08/07/2023,36.5" + uk_temperature_unit_ + ",110/70 mmHg",
        "4,22298006,Myocardial infarction,I21.9,Acute myocardial infarction unspecified,14/11/2023,37.0" + uk_temperature_unit_ + ",160/95 mmHg"
    };

    createCSVFile("clinical_codes.csv", clinical_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "clinical_codes.csv").string()},
        {"date_format", uk_date_format_},
        {"locale", std::string("en_GB")}
    };

    core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_EQ(batch.size(), 4);

    // Validate SNOMED CT concept IDs and ICD-10 codes
    for (size_t i = 0; i < batch.size(); ++i) {
        const auto& record = batch.getRecord(i);
        
        // SNOMED CT concept ID should be numeric
        std::string snomed_id = record.getFieldAs<std::string>("snomed_concept_id");
        EXPECT_TRUE(std::all_of(snomed_id.begin(), snomed_id.end(), ::isdigit));
        EXPECT_TRUE(snomed_id.length() >= 6);
        
        // ICD-10 code should follow pattern (letter + number + optional subcodes)
        std::string icd10_code = record.getFieldAs<std::string>("icd10_code");
        EXPECT_TRUE(std::isalpha(icd10_code[0]));
        EXPECT_TRUE(std::isdigit(icd10_code[1]));
        
        // Temperature should include Celsius unit
        std::string temperature = record.getFieldAs<std::string>("temperature");
        EXPECT_TRUE(temperature.find(uk_temperature_unit_) != std::string::npos);
        
        // Validate UK date format
        std::string diagnosis_date = record.getFieldAs<std::string>("diagnosis_date");
        EXPECT_EQ(std::count(diagnosis_date.begin(), diagnosis_date.end(), '/'), 2);
    }
}

/**
 * @brief Test JSON extraction with NHS hospital trust data
 * 
 * Validates extraction of complex JSON hospital data with nested UK regional
 * information, trust codes, and healthcare facility details.
 */
TEST_F(UKLocalizedExtractTest, NHSTrustDataJSONExtraction) {
    std::string hospital_json = R"({
        "nhs_trusts": [
            {
                "trust_code": "RYJ",
                "trust_name": "Imperial College Healthcare NHS Trust",
                "address": {
                    "street": "Du Cane Road",
                    "city": "London",
                    "postcode": "W12 0HS",
                    "region": "London"
                },
                "contact": {
                    "phone": "020 3313 1000",
                    "email": "<EMAIL>"
                },
                "performance_metrics": {
                    "beds_available": 1200,
                    "occupancy_rate": 0.85,
                    "waiting_times": {
                        "a_e_avg_minutes": 45,
                        "elective_avg_weeks": 12.5
                    },
                    "financial": {
                        "annual_budget": "£850000000",
                        "cost_per_patient": "£3250.75"
                    }
                },
                "clinical_areas": ["Cardiology", "Oncology", "Emergency Medicine"],
                "last_cqc_rating": "Good",
                "inspection_date": "15/09/2023"
            },
            {
                "trust_code": "RWF",
                "trust_name": "King's College Hospital NHS Foundation Trust",
                "address": {
                    "street": "Denmark Hill",
                    "city": "London", 
                    "postcode": "SE5 9RS",
                    "region": "London"
                },
                "contact": {
                    "phone": "020 3299 9000",
                    "email": "<EMAIL>"
                },
                "performance_metrics": {
                    "beds_available": 900,
                    "occupancy_rate": 0.88,
                    "waiting_times": {
                        "a_e_avg_minutes": 52,
                        "elective_avg_weeks": 14.2
                    },
                    "financial": {
                        "annual_budget": "£720000000",
                        "cost_per_patient": "£3890.50"
                    }
                },
                "clinical_areas": ["Liver Transplantation", "Neurology", "Trauma"],
                "last_cqc_rating": "Outstanding",
                "inspection_date": "22/11/2023"
            }
        ]
    })";

    createJSONFile("nhs_trusts.json", hospital_json);

    // Test JSON extraction
    JsonOptions options;
    options.root_path = "nhs_trusts";
    options.flatten_nested = true;
    options.parse_dates = true;

    auto records = utils::extract_json((test_dir_ / "nhs_trusts.json").string(), options);
    EXPECT_EQ(records.size(), 2);

    // Validate trust data structure and UK formatting
    for (const auto& record : records) {
        // Validate NHS trust code format (3 letters)
        std::string trust_code = record.getFieldAs<std::string>("trust_code");
        EXPECT_EQ(trust_code.length(), 3);
        EXPECT_TRUE(std::all_of(trust_code.begin(), trust_code.end(), ::isalpha));
        
        // Validate UK postcode format
        std::string postcode = record.getFieldAs<std::string>("address_postcode");
        EXPECT_TRUE(postcode.find(' ') != std::string::npos);
        
        // Validate UK phone number format
        std::string phone = record.getFieldAs<std::string>("contact_phone");
        EXPECT_TRUE(phone.find("020") == 0); // London numbers start with 020
        
        // Validate UK currency format in financial data
        std::string budget = record.getFieldAs<std::string>("performance_metrics_financial_annual_budget");
        EXPECT_TRUE(budget.find(uk_currency_symbol_) != std::string::npos);
        
        std::string cost_per_patient = record.getFieldAs<std::string>("performance_metrics_financial_cost_per_patient");
        EXPECT_TRUE(cost_per_patient.find(uk_currency_symbol_) != std::string::npos);
        
        // Validate CQC rating values
        std::string cqc_rating = record.getFieldAs<std::string>("last_cqc_rating");
        EXPECT_TRUE(cqc_rating == "Outstanding" || cqc_rating == "Good" || 
                   cqc_rating == "Requires Improvement" || cqc_rating == "Inadequate");
    }
}

/**
 * @brief Test multi-file CSV extraction with UK prescription data
 * 
 * Validates extraction from multiple prescription files with proper BNF codes,
 * UK drug names, and regional pharmacy data formatting.
 */
TEST_F(UKLocalizedExtractTest, UKPrescriptionDataMultiFileExtraction) {
    // Create multiple prescription files representing different CCGs
    std::vector<std::string> ccg_data_1 = {
        "prescription_id,bnf_code,drug_name,quantity,unit_cost,total_cost,pharmacy_ods_code,prescriber_code,issue_date",
        "RX001,0407010A0AAABAB,Amlodipine 5mg tablets,28," + omop::common::UKLocalization::format_uk_currency(0.85) + "," + omop::common::UKLocalization::format_uk_currency(23.80) + ",FAH01,G12345,05/01/2023",
        "RX002,0206010M0AAAHAH,Metformin 500mg tablets,56," + omop::common::UKLocalization::format_uk_currency(1.20) + "," + omop::common::UKLocalization::format_uk_currency(67.20) + ",FAH01,G12345,05/01/2023",
        "RX003,0601012M0AAACAC,Omeprazole 20mg capsules,28," + omop::common::UKLocalization::format_uk_currency(2.15) + "," + omop::common::UKLocalization::format_uk_currency(60.20) + ",FAH02,G23456,06/01/2023"
    };

    std::vector<std::string> ccg_data_2 = {
        "prescription_id,bnf_code,drug_name,quantity,unit_cost,total_cost,pharmacy_ods_code,prescriber_code,issue_date",
        "RX004,0504010Y0AAABAB,Warfarin 3mg tablets,28," + omop::common::UKLocalization::format_uk_currency(3.45) + "," + omop::common::UKLocalization::format_uk_currency(96.60) + ",FAH03,G34567,07/01/2023",
        "RX005,1305010C0AAADAD,Co-codamol 30/500mg tablets,100," + omop::common::UKLocalization::format_uk_currency(4.20) + "," + omop::common::UKLocalization::format_uk_currency(420.00) + ",FAH03,G34567,07/01/2023"
    };

    createCSVFile("prescriptions_ccg_1.csv", ccg_data_1);
    createCSVFile("prescriptions_ccg_2.csv", ccg_data_2);

    // Test multi-file extraction
    auto extractor = std::make_unique<MultiFileCsvExtractor>();
    std::vector<std::string> files = {
        (test_dir_ / "prescriptions_ccg_1.csv").string(),
        (test_dir_ / "prescriptions_ccg_2.csv").string()
    };

    std::unordered_map<std::string, std::any> config = {
        {"files", files},
        {"date_format", uk_date_format_},
        {"locale", std::string("en_GB")}
    };

    core::ProcessingContext context;
    extractor->initialize(config, context);

    size_t total_records = 0;
    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(10, context);
        total_records += batch.size();

        // Validate prescription data format for each record
        for (size_t i = 0; i < batch.size(); ++i) {
            const auto& record = batch.getRecord(i);
            
            // Validate BNF code format (13 characters)
            std::string bnf_code = record.getFieldAs<std::string>("bnf_code");
            EXPECT_EQ(bnf_code.length(), 13);
            
            // Validate pharmacy ODS code format
            std::string pharmacy_code = record.getFieldAs<std::string>("pharmacy_ods_code");
            EXPECT_TRUE(pharmacy_code.find("FA") == 0);
            
            // Validate UK currency format
            std::string unit_cost = record.getFieldAs<std::string>("unit_cost");
            EXPECT_TRUE(unit_cost.find(uk_currency_symbol_) != std::string::npos);
            
            std::string total_cost = record.getFieldAs<std::string>("total_cost");
            EXPECT_TRUE(total_cost.find(uk_currency_symbol_) != std::string::npos);
            
            // Validate UK date format
            std::string issue_date = record.getFieldAs<std::string>("issue_date");
            EXPECT_EQ(std::count(issue_date.begin(), issue_date.end(), '/'), 2);
        }
    }

    EXPECT_EQ(total_records, 5); // 3 from file 1 + 2 from file 2
}

/**
 * @brief Test CSV directory extraction with UK laboratory results
 * 
 * Validates extraction from directory containing multiple lab result files
 * with proper UK reference ranges and measurement units.
 */
TEST_F(UKLocalizedExtractTest, UKLaboratoryResultsDirectoryExtraction) {
    // Create subdirectory for lab results
    fs::path lab_dir = test_dir_ / "laboratory_results";
    fs::create_directories(lab_dir);

    // Create multiple lab result files
    std::vector<std::vector<std::string>> lab_data = {
        { // haematology.csv
            "test_id,patient_nhs,test_name,result_value,reference_range,unit,collection_date,lab_code",
            "HB001," + omop::test::uk::generateNHSNumber(111111111) + ",Full Blood Count - Haemoglobin,135,120-160,g/L,10/01/2023,GOSH",
            "WBC001," + omop::test::uk::generateNHSNumber(222222222) + ",Full Blood Count - White Cell Count,7.2,4.0-11.0,×10⁹/L,10/01/2023,GOSH"
        },
        { // biochemistry.csv
            "test_id,patient_nhs,test_name,result_value,reference_range,unit,collection_date,lab_code",
            "GLU001," + omop::test::uk::generateNHSNumber(333333333) + ",Fasting Glucose,5.8,3.0-6.0,mmol/L,11/01/2023,UCLH",
            "CHOL001," + omop::test::uk::generateNHSNumber(444444444) + ",Total Cholesterol,4.2,<5.0,mmol/L,11/01/2023,UCLH"
        },
        { // microbiology.csv
            "test_id,patient_nhs,test_name,result_value,reference_range,unit,collection_date,lab_code",
            "CULT001," + omop::test::uk::generateNHSNumber(555555555) + ",Urine Culture,Negative,Negative,-,12/01/2023,KCH",
            "SENS001," + omop::test::uk::generateNHSNumber(666666666) + ",Antibiotic Sensitivity,Sensitive to Amoxicillin,-,-,12/01/2023,KCH"
        }
    };

    std::vector<std::string> filenames = {"haematology.csv", "biochemistry.csv", "microbiology.csv"};
    
    for (size_t i = 0; i < lab_data.size(); ++i) {
        std::ofstream file(lab_dir / filenames[i]);
        for (const auto& line : lab_data[i]) {
            file << line << "\n";
        }
        file.close();
    }

    // Test directory extraction
    auto extractor = std::make_unique<CsvDirectoryExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"directory", lab_dir.string()},
        {"pattern", ".*\\.csv$"},
        {"date_format", uk_date_format_},
        {"locale", std::string("en_GB")}
    };

    core::ProcessingContext context;
    extractor->initialize(config, context);

    size_t total_records = 0;
    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(10, context);
        total_records += batch.size();

        // Validate lab data format
        for (size_t i = 0; i < batch.size(); ++i) {
            const auto& record = batch.getRecord(i);
            
            // Validate NHS number format
            std::string nhs_number = record.getFieldAs<std::string>("patient_nhs");
            EXPECT_EQ(nhs_number.length(), 10);
            
            // Validate UK date format
            std::string collection_date = record.getFieldAs<std::string>("collection_date");
            EXPECT_EQ(std::count(collection_date.begin(), collection_date.end(), '/'), 2);
            
            // Validate lab code format (UK hospital codes)
            std::string lab_code = record.getFieldAs<std::string>("lab_code");
            EXPECT_TRUE(lab_code == "GOSH" || lab_code == "UCLH" || lab_code == "KCH");
            
            // Validate UK measurement units
            std::string unit = record.getFieldAs<std::string>("unit");
            bool valid_unit = (unit == "g/L" || unit == "×10⁹/L" || unit == "mmol/L" || unit == "-");
            EXPECT_TRUE(valid_unit);
        }
    }

    EXPECT_EQ(total_records, 6); // 2 records per file * 3 files
}

/**
 * @brief Test extraction statistics with UK healthcare data
 * 
 * Validates that extraction statistics are properly calculated and reported
 * for large volumes of UK healthcare data processing.
 */
TEST_F(UKLocalizedExtractTest, UKHealthcareDataExtractionStatistics) {
    // Create large UK healthcare dataset
    std::ofstream file(test_dir_ / "large_uk_dataset.csv");
    file << "record_id,nhs_number,dob,postcode,gp_practice,cost_centre,admission_date,discharge_date\n";
    
    for (int i = 1; i <= 2500; ++i) {
        auto admission_time = current_time_ - std::chrono::days(30 - (i % 30));
        auto discharge_time = admission_time + std::chrono::hours(24 + (i % 72));
        
        file << "REC" << std::setfill('0') << std::setw(6) << i << ","
             << omop::test::uk::generateNHSNumber(100000000 + i) << ","
             << omop::common::UKLocalization::format_uk_date(current_time_ - std::chrono::days(365 * (25 + i % 40))) << ","
             << omop::test::uk::UKTestDataGenerator().generateUKPostcodeFromComponents("SW", 1 + (i % 5), 1 + (i % 9), "AB") << ","
             << "GP" << std::setfill('0') << std::setw(5) << (10000 + i % 1000) << ","
             << omop::common::UKLocalization::format_uk_currency((i % 10) * 125.50) << ","
             << omop::common::UKLocalization::format_uk_date(admission_time) << ","
             << omop::common::UKLocalization::format_uk_date(discharge_time) << "\n";
    }
    file.close();

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "large_uk_dataset.csv").string()},
        {"date_format", uk_date_format_},
        {"locale", std::string("en_GB")}
    };

    core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract all data in batches
    size_t total_extracted = 0;
    size_t batch_count = 0;
    auto start_time = std::chrono::steady_clock::now();

    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(250, context);
        total_extracted += batch.size();
        batch_count++;
    }

    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    // Validate extraction statistics
    auto stats = extractor->get_statistics();
    
    EXPECT_EQ(total_extracted, 2500);
    EXPECT_EQ(batch_count, 10); // 2500 records / 250 per batch
    
    // Verify statistics contain expected fields
    ASSERT_TRUE(stats.find("extracted_count") != stats.end());
    EXPECT_EQ(std::any_cast<size_t>(stats["extracted_count"]), 2500);
    
    ASSERT_TRUE(stats.find("error_count") != stats.end());
    EXPECT_EQ(std::any_cast<size_t>(stats["error_count"]), 0);
    
    // Performance validation (should process at reasonable rate)
    double records_per_ms = static_cast<double>(total_extracted) / duration.count();
    EXPECT_GT(records_per_ms, 1.0); // Should process at least 1 record per millisecond
}

/**
 * @brief Test error handling with malformed UK healthcare data
 * 
 * Validates proper error handling and reporting when processing malformed
 * UK healthcare data including invalid NHS numbers and postcodes.
 */
TEST_F(UKLocalizedExtractTest, UKHealthcareDataErrorHandling) {
    // Create CSV with various UK-specific data errors
    std::vector<std::string> malformed_data = {
        "nhs_number,postcode,cost,date_field",
        "123456789X,INVALID,£abc.de,32/13/2023", // Invalid NHS number, postcode, currency, date
        "**********,SW1A 1AA," + omop::common::UKLocalization::format_uk_currency(125.50) + ",15/01/2023", // Valid record
        "98765432,W1A,£-50.00,", // Short NHS number, incomplete postcode, negative cost, empty date
        omop::test::uk::generateNHSNumber(987654321) + ",EC1A 1BB," + omop::common::UKLocalization::format_uk_currency(75.25) + ",01/06/2023", // Valid record
        "000000000,12345,£999999.99,99/99/9999" // Invalid NHS number, invalid postcode, date
    };

    createCSVFile("malformed_uk_data.csv", malformed_data);

    auto extractor = std::make_unique<CsvExtractor>();
    std::unordered_map<std::string, std::any> config = {
        {"filepath", (test_dir_ / "malformed_uk_data.csv").string()},
        {"date_format", uk_date_format_},
        {"locale", std::string("en_GB")}
    };

    core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract with error tolerance
    auto batch = extractor->extract_batch(10, context);
    
    // Should still extract all records (CSV extractor extracts as strings)
    EXPECT_EQ(batch.size(), 5);
    
    // Verify we can identify data quality issues programmatically
    size_t invalid_nhs_count = 0;
    size_t invalid_postcode_count = 0;
    
    for (size_t i = 0; i < batch.size(); ++i) {
        const auto& record = batch.getRecord(i);
        
        std::string nhs_number = record.getFieldAs<std::string>("nhs_number");
        std::string postcode = record.getFieldAs<std::string>("postcode");
        
        // Count NHS numbers that don't match expected format
        if (nhs_number.length() != 10 || !std::all_of(nhs_number.begin(), nhs_number.end(), ::isdigit)) {
            invalid_nhs_count++;
        }
        
        // Count postcodes that don't contain space (basic UK postcode validation)
        if (postcode.find(' ') == std::string::npos || postcode.length() < 5) {
            invalid_postcode_count++;
        }
    }
    
    EXPECT_GT(invalid_nhs_count, 0);
    EXPECT_GT(invalid_postcode_count, 0);
}

} // namespace omop::extract::test