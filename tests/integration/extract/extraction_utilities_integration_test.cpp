/**
 * @file extraction_utilities_integration_test.cpp
 * @brief Integration tests for extraction utility functions (extract_csv, extract_json, extract_table)
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 * 
 * This file tests the utility functions that were skipped in unit tests due to 
 * AddressSanitizer compatibility issues with Google Mock. These tests use real
 * files and database connections to ensure 100% feature coverage.
 */

#include <gtest/gtest.h>
#include "extract/extract_utils.h"
#include "extract/database_connector.h"
#include "common/utilities.h"
#include "test_helpers/database_connection_factory.h"
#include <filesystem>
#include <fstream>
#include <locale>
#include <iomanip>
#include <chrono>

namespace omop::extract::test {

namespace fs = std::filesystem;

class ExtractionUtilitiesIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for test files
        test_dir_ = fs::temp_directory_path() / ("extract_utils_test_" + 
            omop::common::CryptoUtils::generate_uuid());
        fs::create_directories(test_dir_);
        
        // Set UK locale for proper date/time formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (...) {
            try {
                std::locale::global(std::locale("C.UTF-8"));
            } catch (...) {
                // Use C locale as last resort
            }
        }
        
        // Setup test database connection
        try {
            setupTestDatabase();
        } catch (const std::exception& e) {
            GTEST_SKIP() << "Test database setup failed: " << e.what();
        }
    }

    void TearDown() override {
        // Clean up test files
        if (fs::exists(test_dir_)) {
            fs::remove_all(test_dir_);
        }
        
        // Clean up database
        cleanupTestDatabase();
    }

    void cleanupTestDatabase() {
        if (connection_ && connection_->is_connected()) {
            try {
                // Drop tables in reverse dependency order
                connection_->execute_update("DROP TABLE IF EXISTS test_conditions CASCADE");
                connection_->execute_update("DROP TABLE IF EXISTS test_visits CASCADE");
                connection_->execute_update("DROP TABLE IF EXISTS test_patients CASCADE");
                connection_->disconnect();
            } catch (...) {
                // Ignore cleanup errors
            }
        }
    }

    void setupTestDatabase() {
        // Create test connection using the factory
        connection_ = omop::test::DatabaseConnectionFactory::createTestConnection();
        
        // Connect to the clinical-db container (not localhost)
        auto params = omop::extract::IDatabaseConnection::ConnectionParams{
            .host = "clinical-db",
            .port = 5432,
            .database = "clinical_db",
            .username = "clinical_user",
            .password = "clinical_pass",
            .options = {}
        };
        
        connection_->connect(params);
        
        // Clean up any existing tables first
        cleanupTestDatabase();
        
        // Create test tables
        createTestTables();
        insertTestData();
    }

    void createTestTables() {
        // Create patient table with UK healthcare data
        connection_->execute_update(R"(
            CREATE TABLE IF NOT EXISTS test_patients (
                patient_id INTEGER PRIMARY KEY,
                nhs_number VARCHAR(10) UNIQUE,
                first_name VARCHAR(100),
                last_name VARCHAR(100),
                birth_date DATE,
                gender CHAR(1),
                postcode VARCHAR(10),
                ethnicity VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )");

        // Create visits table
        connection_->execute_update(R"(
            CREATE TABLE IF NOT EXISTS test_visits (
                visit_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                visit_date DATE,
                visit_type VARCHAR(50),
                provider_name VARCHAR(100),
                ward VARCHAR(50),
                FOREIGN KEY (patient_id) REFERENCES test_patients(patient_id)
            )
        )");

        // Create conditions table with UK clinical codes
        connection_->execute_update(R"(
            CREATE TABLE IF NOT EXISTS test_conditions (
                condition_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                diagnosis VARCHAR(200),
                read_code VARCHAR(10),
                icd10_code VARCHAR(10),
                diagnosis_date DATE,
                FOREIGN KEY (patient_id) REFERENCES test_patients(patient_id)
            )
        )");
    }

    void insertTestData() {
        // Insert UK NHS patient data
        connection_->execute_update(R"(
            INSERT INTO test_patients (patient_id, nhs_number, first_name, last_name, 
                                     birth_date, gender, postcode, ethnicity) VALUES
            (1, '**********', 'James', 'Smith', '1985-03-15', 'M', 'SW1A 1AA', 'White British'),
            (2, '**********', 'Emma', 'Johnson', '1990-07-22', 'F', 'M1 1AA', 'White English'),
            (3, '**********', 'Oliver', 'Williams', '1978-11-08', 'M', 'CF10 3AT', 'Welsh'),
            (4, '**********', 'Sophie', 'Brown', '1995-01-30', 'F', 'EH1 1YZ', 'British Pakistani'),
            (5, '**********', 'Harry', 'Davis', '1982-09-12', 'M', 'B33 8TH', 'British Caribbean')
        )");

        // Insert visit data
        connection_->execute_update(R"(
            INSERT INTO test_visits (visit_id, patient_id, visit_date, visit_type, 
                                   provider_name, ward) VALUES
            (1, 1, '2024-01-15', 'Outpatient', 'Dr. Smith', 'Cardiology'),
            (2, 2, '2024-02-20', 'Emergency', 'Dr. Patel', 'A&E'),
            (3, 3, '2024-03-10', 'Outpatient', 'Dr. Jones', 'Diabetes Clinic'),
            (4, 4, '2024-04-05', 'Inpatient', 'Dr. Williams', 'CCU'),
            (5, 5, '2024-05-12', 'Day Case', 'Mr. Thompson', 'Day Surgery')
        )");

        // Insert condition data with UK clinical codes
        connection_->execute_update(R"(
            INSERT INTO test_conditions (condition_id, patient_id, diagnosis, 
                                       read_code, icd10_code, diagnosis_date) VALUES
            (1, 1, 'Essential Hypertension', 'G20..00', 'I10', '2024-01-15'),
            (2, 2, 'Upper Respiratory Infection', 'H05..00', 'J06.9', '2024-02-20'),
            (3, 3, 'Type 2 Diabetes Mellitus', 'C10E.00', 'E11.9', '2024-03-10'),
            (4, 4, 'Acute Myocardial Infarction', 'G30..00', 'I21.9', '2024-04-05'),
            (5, 5, 'Day Case Surgery', '7L1B.00', 'Z51.1', '2024-05-12')
        )");
    }

    // Helper to create CSV file with UK healthcare data
    std::string createUKHealthcareCSV() {
        fs::path filepath = test_dir_ / "uk_healthcare.csv";
        std::ofstream file(filepath);
        
        // Write header with UK terminology
        file << "patient_id,nhs_number,surname,forename,date_of_birth,postcode,telephone,gender,ethnicity\n";
        
        // Write UK localized patient data
        file << "1,**********,Smith,James,15/03/1985,SW1A 1AA,020 7946 0958,Male,White British\n";
        file << "2,**********,Johnson,Emma,22/07/1990,M1 1AA,0161 496 0000,Female,White English\n";
        file << "3,**********,Williams,Oliver,08/11/1978,CF10 3AT,029 2087 7500,Male,Welsh\n";
        file << "4,**********,Brown,Sophie,30/01/1995,EH1 1YZ,0131 496 0000,Female,British Pakistani\n";
        file << "5,**********,Davis,Harry,12/09/1982,B33 8TH,0121 496 0000,Male,British Caribbean\n";
        
        file.close();
        return filepath.string();
    }

    // Helper to create JSON file with UK healthcare data
    std::string createUKHealthcareJSON() {
        fs::path filepath = test_dir_ / "uk_healthcare.json";
        std::ofstream file(filepath);
        
        file << R"([
            {
                "patient_id": 1,
                "nhs_number": "**********",
                "surname": "Smith",
                "forename": "James",
                "date_of_birth": "15/03/1985",
                "postcode": "SW1A 1AA",
                "telephone": "020 7946 0958",
                "gender": "Male",
                "ethnicity": "White British",
                "clinical_data": {
                    "conditions": [
                        {
                            "diagnosis": "Essential Hypertension",
                            "read_code": "G20..00",
                            "icd10_code": "I10",
                            "diagnosis_date": "15/01/2024"
                        }
                    ]
                }
            },
            {
                "patient_id": 2,
                "nhs_number": "**********",
                "surname": "Johnson",
                "forename": "Emma",
                "date_of_birth": "22/07/1990",
                "postcode": "M1 1AA",
                "telephone": "0161 496 0000",
                "gender": "Female",
                "ethnicity": "White English",
                "clinical_data": {
                    "conditions": [
                        {
                            "diagnosis": "Upper Respiratory Infection",
                            "read_code": "H05..00",
                            "icd10_code": "J06.9",
                            "diagnosis_date": "20/02/2024"
                        }
                    ]
                }
            }
        ])";
        
        file.close();
        return filepath.string();
    }

    fs::path test_dir_;
    std::unique_ptr<omop::extract::IDatabaseConnection> connection_;
};

// =============================================================================
// TEST 1: extract_csv utility function integration testing
// =============================================================================

// Tests extract_csv utility function with UK healthcare data
TEST_F(ExtractionUtilitiesIntegrationTest, ExtractCsvUtilityFunction) {
    std::string csv_file = createUKHealthcareCSV();
    
    // Test the utility function directly
    auto records = omop::extract::utils::extract_csv(csv_file);
    
    // Verify extraction results
    EXPECT_EQ(records.size(), 5);
    
    // Check first record
    const auto& first_record = records[0];
    EXPECT_TRUE(first_record.hasField("patient_id"));
    EXPECT_TRUE(first_record.hasField("nhs_number"));
    EXPECT_TRUE(first_record.hasField("surname"));
    EXPECT_TRUE(first_record.hasField("date_of_birth"));
    EXPECT_TRUE(first_record.hasField("postcode"));
    
    // Verify UK-specific data using type-safe access
    if (first_record.hasField("patient_id")) {
        // Try different types that the CSV might infer
        try {
            int patient_id = first_record.getFieldAs<int>("patient_id");
            EXPECT_EQ(patient_id, 1);
        } catch (const std::bad_any_cast&) {
            try {
                long long patient_id = first_record.getFieldAs<long long>("patient_id");
                EXPECT_EQ(patient_id, 1);
            } catch (const std::bad_any_cast&) {
                std::string patient_id = first_record.getFieldAs<std::string>("patient_id");
                EXPECT_EQ(patient_id, "1");
            }
        }
    }
    
    if (first_record.hasField("nhs_number")) {
        std::string nhs_number = first_record.getFieldAs<std::string>("nhs_number");
        EXPECT_EQ(nhs_number, "**********");
    }
    
    if (first_record.hasField("surname")) {
        std::string surname = first_record.getFieldAs<std::string>("surname");
        EXPECT_EQ(surname, "Smith");
    }
    
    if (first_record.hasField("postcode")) {
        std::string postcode = first_record.getFieldAs<std::string>("postcode");
        EXPECT_EQ(postcode, "SW1A 1AA");
    }
    
    // Check UK date format (DD/MM/YYYY)
    if (first_record.hasField("date_of_birth")) {
        std::string birth_date = first_record.getFieldAs<std::string>("date_of_birth");
        EXPECT_EQ(birth_date, "15/03/1985");
    }
    
    // Check last record
    const auto& last_record = records[4];
    if (last_record.hasField("nhs_number")) {
        std::string nhs_number = last_record.getFieldAs<std::string>("nhs_number");
        EXPECT_EQ(nhs_number, "**********");
    }
    if (last_record.hasField("postcode")) {
        std::string postcode = last_record.getFieldAs<std::string>("postcode");
        EXPECT_EQ(postcode, "B33 8TH");
    }
}

// Tests extract_csv utility function with custom options
TEST_F(ExtractionUtilitiesIntegrationTest, ExtractCsvUtilityFunctionWithOptions) {
    std::string csv_file = createUKHealthcareCSV();
    
    // Create CSV options with UK localization
    omop::extract::CsvOptions options;
    options.has_header = true;
    options.delimiter = ',';
    options.date_format = "%d/%m/%Y";  // UK date format
    
    // Test the utility function with options
    auto records = omop::extract::utils::extract_csv(csv_file, options);
    
    EXPECT_EQ(records.size(), 5);
    
    // Verify UK formatting is preserved
    const auto& first_record = records[0];
    std::string birth_date = first_record.getFieldAs<std::string>("date_of_birth");
    EXPECT_EQ(birth_date, "15/03/1985");  // UK format DD/MM/YYYY
}

// =============================================================================
// TEST 2: extract_json utility function integration testing
// =============================================================================

// Tests extract_json utility function with UK healthcare data
TEST_F(ExtractionUtilitiesIntegrationTest, ExtractJsonUtilityFunction) {
    std::string json_file = createUKHealthcareJSON();
    
    // Test the utility function directly
    auto records = omop::extract::utils::extract_json(json_file);
    
    // Verify extraction results
    EXPECT_EQ(records.size(), 2);
    
    // Check first record
    const auto& first_record = records[0];
    EXPECT_TRUE(first_record.hasField("patient_id"));
    EXPECT_TRUE(first_record.hasField("nhs_number"));
    EXPECT_TRUE(first_record.hasField("surname"));
    EXPECT_TRUE(first_record.hasField("clinical_data"));
    
    // Verify UK-specific data
    EXPECT_EQ(first_record.getFieldAs<long long>("patient_id"), 1);
    EXPECT_EQ(first_record.getFieldAs<std::string>("nhs_number"), "**********");
    EXPECT_EQ(first_record.getFieldAs<std::string>("surname"), "Smith");
    EXPECT_EQ(first_record.getFieldAs<std::string>("postcode"), "SW1A 1AA");
    
    // Check UK date format
    std::string birth_date = first_record.getFieldAs<std::string>("date_of_birth");
    EXPECT_EQ(birth_date, "15/03/1985");
    
    // Check nested clinical data
    auto clinical_data = std::any_cast<std::unordered_map<std::string, std::any>>(
        first_record.getField("clinical_data"));
    EXPECT_TRUE(clinical_data.find("conditions") != clinical_data.end());
    
    auto conditions = std::any_cast<std::vector<std::any>>(clinical_data["conditions"]);
    EXPECT_EQ(conditions.size(), 1);
    
    auto first_condition = std::any_cast<std::unordered_map<std::string, std::any>>(conditions[0]);
    EXPECT_EQ(std::any_cast<std::string>(first_condition["diagnosis"]), "Essential Hypertension");
    EXPECT_EQ(std::any_cast<std::string>(first_condition["read_code"]), "G20..00");
    EXPECT_EQ(std::any_cast<std::string>(first_condition["icd10_code"]), "I10");
}

// Tests extract_json utility function with custom options
TEST_F(ExtractionUtilitiesIntegrationTest, ExtractJsonUtilityFunctionWithOptions) {
    std::string json_file = createUKHealthcareJSON();
    
    // Create JSON options
    omop::extract::JsonOptions options;
    options.flatten_nested = true;
    options.max_depth = 3;
    options.date_formats = {"%d/%m/%Y"};  // UK date format
    
    // Test the utility function with options
    auto records = omop::extract::utils::extract_json(json_file, options);
    
    EXPECT_EQ(records.size(), 2);
    
    // With flattening, nested fields should be accessible with dot notation
    const auto& first_record = records[0];
    EXPECT_TRUE(first_record.hasField("clinical_data.conditions"));
    
    // Verify UK formatting is preserved
    std::string birth_date = first_record.getFieldAs<std::string>("date_of_birth");
    EXPECT_EQ(birth_date, "15/03/1985");
}

// =============================================================================
// TEST 3: extract_table utility function integration testing
// =============================================================================

// Tests extract_table utility function with real database connection
TEST_F(ExtractionUtilitiesIntegrationTest, ExtractTableUtilityFunction) {
    ASSERT_TRUE(connection_ && connection_->is_connected());
    
    // Test the utility function directly
    auto records = omop::extract::utils::extract_table(
        std::move(connection_), "test_patients");
    
    // Verify extraction results
    EXPECT_EQ(records.size(), 5);
    
    // Check first record
    const auto& first_record = records[0];
    EXPECT_TRUE(first_record.hasField("patient_id"));
    EXPECT_TRUE(first_record.hasField("nhs_number"));
    EXPECT_TRUE(first_record.hasField("first_name"));
    EXPECT_TRUE(first_record.hasField("last_name"));
    EXPECT_TRUE(first_record.hasField("birth_date"));
    EXPECT_TRUE(first_record.hasField("postcode"));
    
    // Verify UK-specific data
    EXPECT_EQ(first_record.getFieldAs<long long>("patient_id"), 1);
    EXPECT_EQ(first_record.getFieldAs<std::string>("nhs_number"), "**********");
    EXPECT_EQ(first_record.getFieldAs<std::string>("first_name"), "James");
    EXPECT_EQ(first_record.getFieldAs<std::string>("last_name"), "Smith");
    EXPECT_EQ(first_record.getFieldAs<std::string>("postcode"), "SW1A 1AA");
    
    // Check last record
    const auto& last_record = records[4];
    EXPECT_EQ(last_record.getFieldAs<long long>("patient_id"), 5);
    EXPECT_EQ(last_record.getFieldAs<std::string>("nhs_number"), "**********");
    EXPECT_EQ(last_record.getFieldAs<std::string>("first_name"), "Harry");
    EXPECT_EQ(last_record.getFieldAs<std::string>("last_name"), "Davis");
}

// Tests extract_table utility function with custom SQL query
TEST_F(ExtractionUtilitiesIntegrationTest, ExtractTableUtilityFunctionWithCustomQuery) {
    // Create a new connection for this test
    auto new_connection = omop::test::DatabaseConnectionFactory::createTestConnection();
    ASSERT_TRUE(new_connection && new_connection->is_connected());
    
    // Test with custom SQL query
    std::string custom_query = R"(
        SELECT p.patient_id, p.nhs_number, p.first_name, p.last_name,
               v.visit_date, v.visit_type, c.diagnosis, c.read_code
        FROM test_patients p
        LEFT JOIN test_visits v ON p.patient_id = v.patient_id
        LEFT JOIN test_conditions c ON p.patient_id = c.patient_id
        WHERE p.ethnicity = 'White British'
        ORDER BY p.patient_id
    )";
    
    auto records = omop::extract::utils::extract_table(
        std::move(new_connection), custom_query);
    
    // Should have at least one record for White British patients
    EXPECT_GT(records.size(), 0);
    
    // Check that the joined data is present
    const auto& first_record = records[0];
    EXPECT_TRUE(first_record.hasField("patient_id"));
    EXPECT_TRUE(first_record.hasField("nhs_number"));
    EXPECT_TRUE(first_record.hasField("visit_date"));
    EXPECT_TRUE(first_record.hasField("diagnosis"));
    
    // Verify UK clinical codes
    if (first_record.hasField("read_code")) {
        std::string read_code = first_record.getFieldAs<std::string>("read_code");
        EXPECT_FALSE(read_code.empty());
        // Read codes should follow UK NHS format (e.g., G20..00)
        EXPECT_EQ(read_code.length(), 7);
        EXPECT_EQ(read_code[2], '.');
        EXPECT_EQ(read_code[3], '.');
    }
}

// =============================================================================
// TEST 4: Cross-utility function consistency testing
// =============================================================================

// Tests that all utility functions produce consistent data types
TEST_F(ExtractionUtilitiesIntegrationTest, CrossUtilityFunctionConsistency) {
    // Test CSV extraction
    std::string csv_file = createUKHealthcareCSV();
    auto csv_records = omop::extract::utils::extract_csv(csv_file);
    
    // Test JSON extraction
    std::string json_file = createUKHealthcareJSON();
    auto json_records = omop::extract::utils::extract_json(json_file);
    
    // Test database extraction
    auto db_connection = omop::test::DatabaseConnectionFactory::createTestConnection();
    ASSERT_TRUE(db_connection && db_connection->is_connected());
    auto db_records = omop::extract::utils::extract_table(
        std::move(db_connection), "test_patients");
    
    // All should produce records with consistent structure
    EXPECT_GT(csv_records.size(), 0);
    EXPECT_GT(json_records.size(), 0);
    EXPECT_GT(db_records.size(), 0);
    
    // Check that patient_id is consistently typed across all sources
    if (!csv_records.empty() && !json_records.empty() && !db_records.empty()) {
        auto csv_id = csv_records[0].getField("patient_id");
        auto json_id = json_records[0].getField("patient_id");
        auto db_id = db_records[0].getField("patient_id");
        
        // All should have the same type for patient_id
        EXPECT_EQ(csv_id.type(), json_id.type());
        EXPECT_EQ(json_id.type(), db_id.type());
        
        // All should have the same value for the first patient
        // Use getFieldAs for type-safe access
        try {
            int csv_patient_id = csv_records[0].getFieldAs<int>("patient_id");
            int json_patient_id = json_records[0].getFieldAs<int>("patient_id");
            int db_patient_id = db_records[0].getFieldAs<int>("patient_id");
            EXPECT_EQ(csv_patient_id, 1);
            EXPECT_EQ(json_patient_id, 1);
            EXPECT_EQ(db_patient_id, 1);
        } catch (const std::bad_any_cast&) {
            // Try string type
            std::string csv_patient_id = csv_records[0].getFieldAs<std::string>("patient_id");
            std::string json_patient_id = json_records[0].getFieldAs<std::string>("patient_id");
            std::string db_patient_id = db_records[0].getFieldAs<std::string>("patient_id");
            EXPECT_EQ(csv_patient_id, "1");
            EXPECT_EQ(json_patient_id, "1");
            EXPECT_EQ(db_patient_id, "1");
        }
    }
}

// Tests UK localization consistency across all utility functions
TEST_F(ExtractionUtilitiesIntegrationTest, UKLocalizationConsistency) {
    // Test CSV with UK options
    std::string csv_file = createUKHealthcareCSV();
    omop::extract::CsvOptions csv_options;
    csv_options.date_format = "%d/%m/%Y";
    
    auto csv_records = omop::extract::utils::extract_csv(csv_file, csv_options);
    
    // Test JSON with UK options
    std::string json_file = createUKHealthcareJSON();
    omop::extract::JsonOptions json_options;
    json_options.date_formats = {"%d/%m/%Y"};
    
    auto json_records = omop::extract::utils::extract_json(json_file, json_options);
    
    // Test database with UK formatting
    auto db_connection = omop::test::DatabaseConnectionFactory::createTestConnection();
    ASSERT_TRUE(db_connection && db_connection->is_connected());
    auto db_records = omop::extract::utils::extract_table(
        std::move(db_connection), "test_patients");
    
    // Verify UK date format consistency (DD/MM/YYYY)
    if (!csv_records.empty() && !json_records.empty() && !db_records.empty()) {
        std::string csv_date = csv_records[0].getFieldAs<std::string>("date_of_birth");
        std::string json_date = json_records[0].getFieldAs<std::string>("date_of_birth");
        
        // Both CSV and JSON should use UK date format
        EXPECT_EQ(csv_date, "15/03/1985");
        EXPECT_EQ(json_date, "15/03/1985");
        
        // Verify UK postcode format consistency
        std::string csv_postcode = csv_records[0].getFieldAs<std::string>("postcode");
        std::string json_postcode = json_records[0].getFieldAs<std::string>("postcode");
        std::string db_postcode = db_records[0].getFieldAs<std::string>("postcode");
        
        // All should have UK postcode format
        EXPECT_EQ(csv_postcode, "SW1A 1AA");
        EXPECT_EQ(json_postcode, "SW1A 1AA");
        EXPECT_EQ(db_postcode, "SW1A 1AA");
    }
}

} // namespace omop::extract::test
