// Integration test for configuration management

#include <gtest/gtest.h>
#include <filesystem>
#include <fstream>
#include <regex>
#include <thread>
#include <chrono>
#include "common/configuration.h"
#include "common/logging.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/integration_test_base.h"

using namespace omop::test;
using namespace omop::common;

class ConfigurationIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();

        // Create test configuration files
        create_test_configs();
    }

    void create_test_configs() {
        // Create a complex configuration file
        std::string config_content = R"(
# Test configuration with all features

source:
  type: postgresql
  host: test-db.example.com
  port: 5432
  database: source_db
  username: etl_user
  password: secret123
  options:
    sslmode: require
    connect_timeout: 30

target:
  type: postgresql
  host: cdm-db.example.com
  port: 5432
  database: omop_cdm
  username: cdm_user
  password: cdm_pass
  schema: cdm_v5_4

etl:
  batch_size: 5000
  commit_interval: 10000
  error_threshold: 0.05
  validate_foreign_keys: true
  parallel_workers: 4
  checkpoint_interval: 300

vocabulary:
  gender:
    M: 8507
    F: 8532
    U: 8551
  cache_size: 50000
  case_sensitive: false
  auto_refresh: true
  refresh_interval: 86400

tables:
  - source_table: patient_data
    target_table: person
    enabled: true
    filters:
      - field: patient_id
        operator: is_not_null
      - field: birth_date
        operator: ">"
        value: "1900-01-01"
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
      - source_column: gender_code
        target_column: gender_concept_id
        type: vocabulary_mapping
        parameters:
          vocabulary: gender
          default: 0
    validations:
      - field: person_id
        rule: required
      - field: year_of_birth
        rule: range
        min: 1900
        max: 2024

  - source_table: encounters
    target_table: visit_occurrence
    enabled: true
    pre_process_sql: |
      DELETE FROM staging.encounters_temp
      WHERE encounter_date < '2020-01-01'
    post_process_sql: |
      UPDATE cdm.visit_occurrence
      SET visit_end_date = visit_start_date
      WHERE visit_end_date IS NULL
    transformations:
      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct
)";

        main_config_path_ = create_temp_file("main_config.yaml", config_content);

        // Create additional config files for testing includes
        std::string db_config = R"(
database_connections:
  dev:
    host: dev-db.local
    port: 5432
  prod:
    host: prod-db.example.com
    port: 5432
)";

        create_temp_file("db_config.yaml", db_config);

        // Create invalid config for error testing
        std::string invalid_config = R"(
invalid yaml content [
  missing closing bracket
  bad: formatting: here
)";

        invalid_config_path_ = create_temp_file("invalid_config.yaml", invalid_config);
    }

    std::filesystem::path main_config_path_;
    std::filesystem::path invalid_config_path_;
};

TEST_F(ConfigurationIntegrationTest, LoadComplexConfiguration) {
    // Test loading and parsing complex configuration file

    config_->load_config(main_config_path_.string());
    ASSERT_TRUE(config_->is_loaded());

    // Verify source database configuration
    auto source_db = config_->get_source_db();
    EXPECT_EQ(omop::common::DatabaseConfig::Type::PostgreSQL, source_db.type());
    EXPECT_EQ("test-db.example.com", source_db.host());
    EXPECT_EQ(5432, source_db.port());
    EXPECT_EQ("source_db", source_db.database());
    EXPECT_EQ("etl_user", source_db.username());

    // Verify target database configuration
    auto target_db = config_->get_target_db();
    EXPECT_EQ("cdm-db.example.com", target_db.host());
    EXPECT_EQ("omop_cdm", target_db.database());

    // Verify ETL settings
    auto batch_size = config_->get_value_or("etl.batch_size", 1000);
    EXPECT_EQ(5000, batch_size);

    auto error_threshold = config_->get_value_or("etl.error_threshold", 0.01);
    EXPECT_NEAR(0.05, error_threshold, 1e-8);

    // Verify table mappings
    auto all_mappings = config_->get_all_mappings();
    EXPECT_EQ(2, all_mappings.size());

    auto person_mapping = config_->get_table_mapping("person");
    ASSERT_TRUE(person_mapping.has_value());
    EXPECT_EQ("patient_data", person_mapping->source_table());
    EXPECT_EQ("person", person_mapping->target_table());
}

TEST_F(ConfigurationIntegrationTest, TableMappingDetails) {
    // Test detailed table mapping configuration

    config_->load_config(main_config_path_.string());

    auto person_mapping = config_->get_table_mapping("person");
    ASSERT_TRUE(person_mapping.has_value());

    // Check transformations
    auto transformations = person_mapping->transformations();
    EXPECT_EQ(2, transformations.size());

    // Verify first transformation (direct mapping)
    EXPECT_EQ("patient_id", transformations[0].source_column());
    EXPECT_EQ("person_id", transformations[0].target_column());
    EXPECT_EQ(TransformationRule::Type::Direct, transformations[0].type());

    // Verify second transformation (vocabulary mapping)
    EXPECT_EQ("gender_code", transformations[1].source_column());
    EXPECT_EQ("gender_concept_id", transformations[1].target_column());
    EXPECT_EQ(TransformationRule::Type::VocabularyMapping, transformations[1].type());

    // Check filters
    auto filters = person_mapping->filters();
    EXPECT_TRUE(filters.IsDefined());
    EXPECT_TRUE(filters.IsSequence());
    EXPECT_EQ(2, filters.size());

    // Check validations
    auto validations = person_mapping->validations();
    EXPECT_TRUE(validations.IsDefined());
    EXPECT_EQ(2, validations.size());
}

TEST_F(ConfigurationIntegrationTest, ConfigurationValidation) {
    // Test configuration validation

    config_->load_config(main_config_path_.string());

    // Should validate successfully
    EXPECT_NO_THROW(config_->validate_config());

    // Test with missing required fields
    std::string minimal_config = R"(
source:
  type: postgresql
  host: localhost
  database: source_db
  username: source_user
  password: source_pass
target:
  type: postgresql
  host: localhost
  database: target_db
  username: target_user
  password: target_pass
tables:
  - source_table: test_table
    target_table: test_table
    enabled: true
    transformations:
      - source_column: id
        target_column: id
        type: direct
)";

    auto minimal_path = create_temp_file("minimal_config.yaml", minimal_config);

    ConfigurationManager minimal_config_mgr;
    minimal_config_mgr.load_config(minimal_path.string());

    // Should still validate (minimal requirements met)
    EXPECT_NO_THROW(minimal_config_mgr.validate_config());
}

TEST_F(ConfigurationIntegrationTest, ErrorHandling) {
    // Test error handling for invalid configurations

    // Test loading invalid YAML
    EXPECT_THROW({
        config_->load_config(invalid_config_path_.string());
    }, ConfigurationException);

    // Test loading non-existent file
    EXPECT_THROW({
        config_->load_config("/non/existent/path/config.yaml");
    }, ConfigurationException);

    // Test accessing configuration before loading
    ConfigurationManager empty_config;
    EXPECT_FALSE(empty_config.is_loaded());
    EXPECT_FALSE(empty_config.get_table_mapping("person").has_value());
}

TEST_F(ConfigurationIntegrationTest, NestedValueAccess) {
    // Test accessing nested configuration values

    config_->load_config(main_config_path_.string());

    // Test dot notation access
    auto vocab_cache = config_->get_value("vocabulary.cache_size");
    ASSERT_TRUE(vocab_cache.has_value());
    EXPECT_EQ(50000, vocab_cache->as<int>());

    // Debug: Check if tables section exists
    auto tables_section = config_->get_value("tables");
    ASSERT_TRUE(tables_section.has_value()) << "Tables section not found";
    EXPECT_TRUE(tables_section->IsSequence()) << "Tables section is not a sequence";
    EXPECT_GT(tables_section->size(), 0) << "Tables sequence is empty";

    // Test accessing array elements
    auto first_table = config_->get_value("tables[0].source_table");
    ASSERT_TRUE(first_table.has_value()) << "Failed to access tables[0].source_table";
    EXPECT_EQ("patient_data", first_table->as<std::string>());

    // Test non-existent keys
    auto missing = config_->get_value("non.existent.key");
    EXPECT_FALSE(missing.has_value());

    // Test with defaults
    auto with_default = config_->get_value_or("missing.key", 42);
    EXPECT_EQ(42, with_default);
}

TEST_F(ConfigurationIntegrationTest, MultiFileConfiguration) {
    // Test loading configuration from multiple files

    // Create main config that references other files
    std::string main_with_includes = R"(
# Main configuration with includes

!include db_config.yaml

source:
  <<: *dev
  database: source_db

target:
  <<: *prod
  database: target_db

etl:
  batch_size: 1000
)";

    // Note: YAML include functionality would need to be implemented
    // This test demonstrates the pattern

    auto config_path = create_temp_file("config_with_includes.yaml",
                                       main_with_includes);

    // For this test, we'll simulate the expected behavior
    // In real implementation, the YAML parser would handle includes
}

TEST_F(ConfigurationIntegrationTest, EnvironmentVariableSubstitution) {
    // Test environment variable substitution in configuration

    // Set test environment variables
    setenv("TEST_DB_HOST", "env-test-host", 1);
    setenv("TEST_DB_PORT", "5433", 1);
    setenv("TEST_DB_PASSWORD", "env-secret", 1);

    std::string config_with_env = R"(
source:
  type: postgresql
  host: ${TEST_DB_HOST}
  port: ${TEST_DB_PORT}
  database: test_db
  username: test_user
  password: ${TEST_DB_PASSWORD}

target:
  type: postgresql
  host: ${TEST_DB_HOST:-default-host}
  port: ${TEST_DB_PORT:-5432}
  database: target_db
  username: ${TEST_DB_USER:-default_user}
  password: ${TEST_DB_PASSWORD}
)";

    // This would require custom YAML parsing to handle env vars
    // For now, we test the concept
    auto env_config_path = create_temp_file("env_config.yaml", config_with_env);

    // Clean up environment variables
    unsetenv("TEST_DB_HOST");
    unsetenv("TEST_DB_PORT");
    unsetenv("TEST_DB_PASSWORD");
}

TEST_F(ConfigurationIntegrationTest, ConcurrentConfigAccess) {
    // Test thread-safe configuration access

    config_->load_config(main_config_path_.string());

    const int num_threads = 10;
    const int reads_per_thread = 1000;
    std::vector<std::thread> threads;
    std::atomic<int> successful_reads{0};

    auto reader_func = [this, &successful_reads]() {
        for (int i = 0; i < reads_per_thread; ++i) {
            // Read various configuration values
            auto batch_size = config_->get_value_or("etl.batch_size", 0);
            auto mapping = config_->get_table_mapping("person");
            auto source_db = config_->get_source_db();

            if (batch_size == 5000 && mapping.has_value() &&
                source_db.host() == "test-db.example.com") {
                successful_reads++;
            }
        }
    };

    // Launch reader threads
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(reader_func);
    }

    // Wait for completion
    for (auto& thread : threads) {
        thread.join();
    }

    // All reads should be successful
    EXPECT_EQ(num_threads * reads_per_thread, successful_reads.load());
}

TEST_F(ConfigurationIntegrationTest, LargeConfigurationPerformance) {
    // Test performance with large configuration files

    // Generate large configuration with many table mappings
    std::stringstream large_config;
    large_config << R"(
source:
  type: postgresql
  host: localhost
  port: 5432
  database: source_db
  username: source_user
  password: source_pass

target:
  type: postgresql
  host: localhost
  port: 5432
  database: target_db
  username: target_user
  password: target_pass

tables:
)";

    const int num_tables = 100;
    for (int i = 0; i < num_tables; ++i) {
        large_config << std::format(R"(
  - source_table: source_table_{}
    target_table: target_table_{}
    transformations:
      - source_column: id
        target_column: id
        type: direct
      - source_column: value
        target_column: value
        type: numeric_transform
        parameters:
          operation: multiply
          factor: 1.5
)", i, i);
    }

    auto large_config_path = create_temp_file("large_config.yaml",
                                             large_config.str());

    // Measure loading time
    auto start = std::chrono::high_resolution_clock::now();
    config_->load_config(large_config_path.string());
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end - start);

    logger_->info("Loaded configuration with {} tables in {} ms",
                  num_tables, duration.count());

    // Verify all tables loaded
    auto all_mappings = config_->get_all_mappings();
    EXPECT_EQ(num_tables, all_mappings.size());

    // Performance assertion - should load quickly even with many tables
    // Adjusted for test environment - allow up to 2 seconds for 100 tables
    EXPECT_LT(duration.count(), 2000); // Less than 2 seconds
}

TEST_F(ConfigurationIntegrationTest, ConfigurationReloading) {
    // Test configuration reloading and hot updates

    // Load initial configuration
    config_->load_config(main_config_path_.string());
    auto initial_batch_size = config_->get_value_or("etl.batch_size", 0);
    EXPECT_EQ(5000, initial_batch_size);

    // Modify configuration file
    auto config_content = read_file(main_config_path_);
    auto modified_content = std::regex_replace(
        config_content,
        std::regex("batch_size: 5000"),
        "batch_size: 10000"
    );

    // Write modified content
    std::ofstream file(main_config_path_);
    file << modified_content;
    file.close();

    // Reload configuration
    config_->load_config(main_config_path_.string());

    // Verify updated value
    auto new_batch_size = config_->get_value_or("etl.batch_size", 0);
    EXPECT_EQ(10000, new_batch_size);
}

// Test configuration singleton pattern
TEST_F(ConfigurationIntegrationTest, SingletonBehavior) {
    // Test global configuration singleton

    Config::instance().load_config(main_config_path_.string());

    // Access from different points should return same instance
    auto& config1 = Config::instance();
    auto& config2 = Config::instance();

    // Both should have the same configuration
    auto batch1 = config1.get_value_or("etl.batch_size", 0);
    auto batch2 = config2.get_value_or("etl.batch_size", 0);

    EXPECT_EQ(batch1, batch2);
    EXPECT_EQ(5000, batch1);
}