/**
 * @file comprehensive_configuration_performance_benchmark.cpp
 * @brief Comprehensive performance benchmarks for configuration system
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#include <gtest/gtest.h>
#include "common/configuration.h"
#include "common/config/configuration_performance.h"
#include "common/config/configuration_cache.h"
#include "common/config/configuration_environment.h"
#include "common/config/configuration_audit.h"
#include "common/config/configuration_backup.h"
#include "common/logging.h"
#include "common/metrics_collector.h"
#include <chrono>
#include <thread>
#include <atomic>
#include <memory>
#include <future>
#include <random>
#include <vector>
#include <fstream>
#include <filesystem>
#include <algorithm>
#include <numeric>
#include <yaml-cpp/yaml.h>

using namespace omop::common;
using namespace omop::common::config;
using namespace std::chrono_literals;

class ComprehensiveConfigurationPerformanceBenchmark : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_config_perf_bench";
        std::filesystem::create_directories(test_dir_);
        
        // Setup logging with minimal output during benchmarks
        common::LoggingConfig logging_config;
        logging_config.level = common::LogLevel::ERROR;
        logging_config.async_logging = true;
        logging_config.buffer_size = 1000;
        common::LoggingConfig::initialize(logging_config);
        
        logger_ = common::Logger::get("config-perf-benchmark");
        
        // Setup performance monitor with strict SLAs
        ConfigurationPerformance::SLAThresholds strict_sla;
        strict_sla.config_load = 50ms;      // Stricter than default 100ms
        strict_sla.config_hot_reload = 25ms; // Stricter than default 50ms
        strict_sla.cache_lookup = 500us;     // Stricter than default 1ms
        strict_sla.validation = 5ms;         // Stricter than default 10ms
        strict_sla.backup = 250ms;          // Stricter than default 500ms
        strict_sla.restore = 500ms;         // Stricter than default 1000ms
        
        perf_monitor_ = std::make_unique<ConfigurationPerformance>(strict_sla);
        perf_monitor_->enable_detailed_timing(true);
        
        // Setup metrics collector
        metrics_collector_ = std::make_shared<omop::monitoring::MetricsCollector>();
        
        // Create test configuration files
        create_test_configurations();
    }
    
    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }
    
    void create_test_configurations() {
        // Small configuration (~1KB)
        small_config_path_ = test_dir_ / "small_config.yaml";
        create_yaml_config(small_config_path_, 10, 5);
        
        // Medium configuration (~10KB)
        medium_config_path_ = test_dir_ / "medium_config.yaml";
        create_yaml_config(medium_config_path_, 100, 20);
        
        // Large configuration (~100KB)
        large_config_path_ = test_dir_ / "large_config.yaml";
        create_yaml_config(large_config_path_, 1000, 50);
        
        // Very large configuration (~1MB)
        xlarge_config_path_ = test_dir_ / "xlarge_config.yaml";
        create_yaml_config(xlarge_config_path_, 10000, 100);
        
        // Complex nested configuration
        complex_config_path_ = test_dir_ / "complex_config.yaml";
        create_complex_yaml_config(complex_config_path_);
        
        // Configuration with includes
        include_config_path_ = test_dir_ / "include_config.yaml";
        create_include_config(include_config_path_);
    }
    
    void create_yaml_config(const std::filesystem::path& path, int sections, int items_per_section) {
        YAML::Node config;
        
        // Add metadata
        config["version"] = "1.0.0";
        config["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        // Add sections with various data types
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> int_dist(1, 10000);
        std::uniform_real_distribution<> real_dist(0.0, 1000.0);
        std::uniform_int_distribution<> bool_dist(0, 1);
        
        for (int s = 0; s < sections; ++s) {
            std::string section_name = "section_" + std::to_string(s);
            YAML::Node section;
            
            for (int i = 0; i < items_per_section; ++i) {
                std::string item_name = "item_" + std::to_string(i);
                
                // Mix different data types
                switch (i % 4) {
                    case 0: section[item_name] = int_dist(gen); break;
                    case 1: section[item_name] = real_dist(gen); break;
                    case 2: section[item_name] = bool_dist(gen) == 1; break;
                    case 3: section[item_name] = "string_value_" + std::to_string(i); break;
                }
            }
            
            config[section_name] = section;
        }
        
        std::ofstream file(path);
        file << config;
    }
    
    void create_complex_yaml_config(const std::filesystem::path& path) {
        YAML::Node config;
        
        // Deep nesting
        YAML::Node level1;
        YAML::Node level2;
        YAML::Node level3;
        YAML::Node level4;
        
        level4["deep_value"] = "nested_deep";
        level4["deep_number"] = 42;
        level4["deep_array"].push_back("item1");
        level4["deep_array"].push_back("item2");
        level4["deep_array"].push_back("item3");
        
        level3["level4"] = level4;
        level3["level3_value"] = "test";
        
        level2["level3"] = level3;
        level1["level2"] = level2;
        config["level1"] = level1;
        
        // Large arrays
        YAML::Node large_array;
        for (int i = 0; i < 1000; ++i) {
            YAML::Node item;
            item["id"] = i;
            item["name"] = "item_" + std::to_string(i);
            item["value"] = i * 2.5;
            large_array.push_back(item);
        }
        config["large_array"] = large_array;
        
        // Complex mappings
        YAML::Node mappings;
        for (int i = 0; i < 100; ++i) {
            std::string key = "mapping_key_" + std::to_string(i);
            YAML::Node mapping;
            mapping["enabled"] = (i % 2 == 0);
            mapping["priority"] = i % 10;
            mapping["description"] = "Description for mapping " + std::to_string(i);
            mappings[key] = mapping;
        }
        config["mappings"] = mappings;
        
        std::ofstream file(path);
        file << config;
    }
    
    void create_include_config(const std::filesystem::path& path) {
        // Create base config
        YAML::Node base_config;
        base_config["base"]["value"] = "base_value";
        base_config["base"]["number"] = 123;
        
        // Create included config
        auto include1_path = test_dir_ / "include1.yaml";
        YAML::Node include1;
        include1["include1"]["data"] = "included_data_1";
        include1["include1"]["enabled"] = true;
        
        std::ofstream include1_file(include1_path);
        include1_file << include1;
        
        auto include2_path = test_dir_ / "include2.yaml";
        YAML::Node include2;
        include2["include2"]["data"] = "included_data_2";
        include2["include2"]["count"] = 456;
        
        std::ofstream include2_file(include2_path);
        include2_file << include2;
        
        // Main config with includes
        base_config["includes"].push_back(include1_path.string());
        base_config["includes"].push_back(include2_path.string());
        
        std::ofstream main_file(path);
        main_file << base_config;
    }
    
    // Performance measurement utilities
    struct BenchmarkStats {
        std::chrono::nanoseconds min_time;
        std::chrono::nanoseconds max_time;
        std::chrono::nanoseconds avg_time;
        std::chrono::nanoseconds median_time;
        std::chrono::nanoseconds p95_time;
        std::chrono::nanoseconds p99_time;
        double throughput_ops_per_sec;
        size_t sample_count;
        std::vector<std::chrono::nanoseconds> all_timings;
        
        void calculate_from_timings() {
            if (all_timings.empty()) return;
            
            std::sort(all_timings.begin(), all_timings.end());
            
            min_time = all_timings.front();
            max_time = all_timings.back();
            
            auto total = std::accumulate(all_timings.begin(), all_timings.end(), 
                                       std::chrono::nanoseconds(0));
            avg_time = total / all_timings.size();
            
            median_time = all_timings[all_timings.size() / 2];
            p95_time = all_timings[static_cast<size_t>(all_timings.size() * 0.95)];
            p99_time = all_timings[static_cast<size_t>(all_timings.size() * 0.99)];
            
            sample_count = all_timings.size();
        }
        
        void print(const std::string& operation_name) const {
            std::cout << "\n=== " << operation_name << " Performance Results ===" << std::endl;
            std::cout << "Samples: " << sample_count << std::endl;
            std::cout << "Throughput: " << std::fixed << std::setprecision(2) 
                      << throughput_ops_per_sec << " ops/sec" << std::endl;
            std::cout << "Latency (μs):" << std::endl;
            std::cout << "  Min: " << (min_time.count() / 1000.0) << std::endl;
            std::cout << "  Avg: " << (avg_time.count() / 1000.0) << std::endl;
            std::cout << "  Median: " << (median_time.count() / 1000.0) << std::endl;
            std::cout << "  P95: " << (p95_time.count() / 1000.0) << std::endl;
            std::cout << "  P99: " << (p99_time.count() / 1000.0) << std::endl;
            std::cout << "  Max: " << (max_time.count() / 1000.0) << std::endl;
            std::cout << "============================================" << std::endl;
        }
    };
    
    BenchmarkStats measure_operation(std::function<void()> operation, 
                                   int iterations, 
                                   int warmup_iterations = 10) {
        // Warmup
        for (int i = 0; i < warmup_iterations; ++i) {
            operation();
        }
        
        BenchmarkStats stats;
        auto overall_start = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < iterations; ++i) {
            auto start = std::chrono::high_resolution_clock::now();
            operation();
            auto end = std::chrono::high_resolution_clock::now();
            
            stats.all_timings.push_back(end - start);
        }
        
        auto overall_end = std::chrono::high_resolution_clock::now();
        auto total_duration = std::chrono::duration_cast<std::chrono::nanoseconds>(
            overall_end - overall_start);
        
        stats.calculate_from_timings();
        stats.throughput_ops_per_sec = static_cast<double>(iterations) / 
                                     (total_duration.count() / 1e9);
        
        return stats;
    }

protected:
    std::filesystem::path test_dir_;
    std::shared_ptr<common::Logger> logger_;
    std::unique_ptr<ConfigurationPerformance> perf_monitor_;
    std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector_;
    
    // Test configuration paths
    std::filesystem::path small_config_path_;
    std::filesystem::path medium_config_path_;
    std::filesystem::path large_config_path_;
    std::filesystem::path xlarge_config_path_;
    std::filesystem::path complex_config_path_;
    std::filesystem::path include_config_path_;
};

// Benchmark configuration loading performance across different file sizes
TEST_F(ComprehensiveConfigurationPerformanceBenchmark, ConfigurationLoadingScalability) {
    std::vector<std::pair<std::string, std::filesystem::path>> test_configs = {
        {"Small (~1KB)", small_config_path_},
        {"Medium (~10KB)", medium_config_path_},
        {"Large (~100KB)", large_config_path_},
        {"XLarge (~1MB)", xlarge_config_path_},
        {"Complex", complex_config_path_},
        {"With Includes", include_config_path_}
    };
    
    for (const auto& [name, path] : test_configs) {
        auto stats = measure_operation([&]() {
            auto timer = perf_monitor_->start_timer("load_" + name);
            ConfigurationManager config;
            config.load_from_file(path.string());
        }, 50, 5);
        
        stats.print("Configuration Loading - " + name);
        
        // Verify SLA compliance
        auto avg_ms = stats.avg_time.count() / 1e6;
        auto p95_ms = stats.p95_time.count() / 1e6;
        
        // Expect reasonable performance even for large configs
        if (name.find("XLarge") == std::string::npos) {
            EXPECT_LT(avg_ms, 100.0) << "Average load time exceeded 100ms for " << name;
            EXPECT_LT(p95_ms, 200.0) << "P95 load time exceeded 200ms for " << name;
        }
        
        EXPECT_GT(stats.throughput_ops_per_sec, 10.0) << "Throughput too low for " << name;
        
        // File size impact analysis
        auto file_size = std::filesystem::file_size(path);
        double throughput_mb_per_sec = (file_size / (1024.0 * 1024.0)) * stats.throughput_ops_per_sec;
        
        std::cout << "File size: " << (file_size / 1024.0) << " KB" << std::endl;
        std::cout << "Throughput: " << throughput_mb_per_sec << " MB/sec" << std::endl;
        std::cout << "Load rate: " << (avg_ms / (file_size / 1024.0)) << " ms/KB" << std::endl;
    }
}

// Benchmark configuration hot reload performance
TEST_F(ComprehensiveConfigurationPerformanceBenchmark, HotReloadPerformance) {
    ConfigurationManager config;
    config.load_from_file(medium_config_path_.string());
    
    // Enable hot reload monitoring
    config.enable_hot_reload(100ms);
    
    auto stats = measure_operation([&]() {
        auto timer = perf_monitor_->start_timer("hot_reload");
        
        // Modify config file to trigger reload
        auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        YAML::Node updated_config = YAML::LoadFile(medium_config_path_.string());
        updated_config["timestamp"] = timestamp;
        
        std::ofstream file(medium_config_path_);
        file << updated_config;
        
        // Wait for hot reload to complete
        std::this_thread::sleep_for(50ms);
    }, 20, 2);
    
    stats.print("Configuration Hot Reload");
    
    auto avg_ms = stats.avg_time.count() / 1e6;
    EXPECT_LT(avg_ms, 50.0) << "Hot reload exceeded 50ms SLA";
    EXPECT_GT(stats.throughput_ops_per_sec, 15.0) << "Hot reload throughput too low";
    
    config.disable_hot_reload();
}

// Benchmark configuration caching performance
TEST_F(ComprehensiveConfigurationPerformanceBenchmark, ConfigurationCachePerformance) {
    ConfigurationCache cache;
    ConfigurationCache::Config cache_config;
    cache_config.max_size = 100;
    cache_config.eviction_policy = ConfigurationCache::EvictionPolicy::LRU;
    cache_config.enable_metrics = true;
    
    cache.configure(cache_config);
    
    // Pre-populate cache
    for (int i = 0; i < 50; ++i) {
        std::string key = "config_key_" + std::to_string(i);
        std::string value = "config_value_" + std::to_string(i) + "_large_content_to_simulate_real_config";
        cache.put(key, value);
    }
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> key_dist(0, 49);
    
    // Benchmark cache hits
    auto hit_stats = measure_operation([&]() {
        auto timer = perf_monitor_->start_timer("cache_hit");
        std::string key = "config_key_" + std::to_string(key_dist(gen));
        auto result = cache.get(key);
        EXPECT_TRUE(result.has_value());
    }, 10000, 100);
    
    hit_stats.print("Configuration Cache Hits");
    
    // Benchmark cache misses
    auto miss_stats = measure_operation([&]() {
        auto timer = perf_monitor_->start_timer("cache_miss");
        std::string key = "missing_key_" + std::to_string(key_dist(gen));
        auto result = cache.get(key);
        EXPECT_FALSE(result.has_value());
    }, 1000, 10);
    
    miss_stats.print("Configuration Cache Misses");
    
    // Verify cache performance SLAs
    auto hit_avg_us = hit_stats.avg_time.count() / 1000.0;
    auto miss_avg_us = miss_stats.avg_time.count() / 1000.0;
    
    EXPECT_LT(hit_avg_us, 1.0) << "Cache hit exceeded 1μs SLA";
    EXPECT_LT(miss_avg_us, 5.0) << "Cache miss exceeded 5μs SLA";
    EXPECT_GT(hit_stats.throughput_ops_per_sec, 1000000) << "Cache hit throughput too low";
    
    // Cache hits should be faster than misses
    EXPECT_LT(hit_avg_us, miss_avg_us) << "Cache hits should be faster than misses";
    
    auto cache_stats = cache.get_statistics();
    double hit_rate = static_cast<double>(cache_stats.hits) / (cache_stats.hits + cache_stats.misses);
    std::cout << "Cache hit rate: " << (hit_rate * 100.0) << "%" << std::endl;
    EXPECT_GT(hit_rate, 0.9) << "Cache hit rate should be > 90%";
}

// Benchmark configuration validation performance
TEST_F(ComprehensiveConfigurationPerformanceBenchmark, ConfigurationValidationPerformance) {
    // Create configurations with validation issues
    auto invalid_config_path = test_dir_ / "invalid_config.yaml";
    YAML::Node invalid_config;
    invalid_config["database"]["host"] = ""; // Invalid: empty host
    invalid_config["database"]["port"] = "invalid_port"; // Invalid: non-numeric port
    invalid_config["database"]["timeout"] = -1; // Invalid: negative timeout
    
    std::ofstream invalid_file(invalid_config_path);
    invalid_file << invalid_config;
    
    ConfigurationValidator validator;
    
    // Benchmark validation of valid config
    auto valid_stats = measure_operation([&]() {
        auto timer = perf_monitor_->start_timer("validation_valid");
        auto result = validator.validate_file(medium_config_path_.string());
        EXPECT_TRUE(result.is_valid());
    }, 100, 10);
    
    valid_stats.print("Configuration Validation - Valid Config");
    
    // Benchmark validation of invalid config
    auto invalid_stats = measure_operation([&]() {
        auto timer = perf_monitor_->start_timer("validation_invalid");
        auto result = validator.validate_file(invalid_config_path.string());
        EXPECT_FALSE(result.is_valid());
    }, 100, 10);
    
    invalid_stats.print("Configuration Validation - Invalid Config");
    
    auto valid_avg_ms = valid_stats.avg_time.count() / 1e6;
    auto invalid_avg_ms = invalid_stats.avg_time.count() / 1e6;
    
    EXPECT_LT(valid_avg_ms, 10.0) << "Valid config validation exceeded 10ms SLA";
    EXPECT_LT(invalid_avg_ms, 15.0) << "Invalid config validation exceeded 15ms SLA";
    EXPECT_GT(valid_stats.throughput_ops_per_sec, 100.0) << "Validation throughput too low";
}

// Benchmark configuration backup and restore performance
TEST_F(ComprehensiveConfigurationPerformanceBenchmark, BackupRestorePerformance) {
    ConfigurationBackup backup_manager;
    ConfigurationBackup::Config backup_config;
    backup_config.backup_directory = test_dir_ / "backups";
    backup_config.compression_enabled = true;
    backup_config.encryption_enabled = false; // Disable for performance testing
    backup_config.max_backup_count = 10;
    
    std::filesystem::create_directories(backup_config.backup_directory);
    backup_manager.configure(backup_config);
    
    // Benchmark backup operations
    auto backup_stats = measure_operation([&]() {
        auto timer = perf_monitor_->start_timer("backup");
        auto backup_result = backup_manager.create_backup(large_config_path_.string());
        EXPECT_TRUE(backup_result.success);
    }, 20, 2);
    
    backup_stats.print("Configuration Backup");
    
    // Get list of created backups for restoration testing
    auto backups = backup_manager.list_backups(large_config_path_.string());
    ASSERT_FALSE(backups.empty()) << "No backups created";
    
    // Benchmark restore operations
    auto restore_stats = measure_operation([&]() {
        auto timer = perf_monitor_->start_timer("restore");
        auto restore_path = test_dir_ / "restored_config.yaml";
        auto restore_result = backup_manager.restore_backup(backups.back().backup_id, restore_path.string());
        EXPECT_TRUE(restore_result.success);
    }, 20, 2);
    
    restore_stats.print("Configuration Restore");
    
    auto backup_avg_ms = backup_stats.avg_time.count() / 1e6;
    auto restore_avg_ms = restore_stats.avg_time.count() / 1e6;
    
    EXPECT_LT(backup_avg_ms, 500.0) << "Backup exceeded 500ms SLA";
    EXPECT_LT(restore_avg_ms, 1000.0) << "Restore exceeded 1000ms SLA";
    EXPECT_GT(backup_stats.throughput_ops_per_sec, 2.0) << "Backup throughput too low";
    EXPECT_GT(restore_stats.throughput_ops_per_sec, 1.0) << "Restore throughput too low";
}

// Benchmark concurrent configuration access
TEST_F(ComprehensiveConfigurationPerformanceBenchmark, ConcurrentAccessPerformance) {
    ConfigurationManager config;
    config.load_from_file(large_config_path_.string());
    
    std::atomic<int> read_operations{0};
    std::atomic<int> write_operations{0};
    std::atomic<bool> stop_test{false};
    
    // Launch reader threads
    std::vector<std::future<void>> reader_futures;
    for (int i = 0; i < 8; ++i) {
        reader_futures.emplace_back(std::async(std::launch::async, [&]() {
            while (!stop_test.load()) {
                try {
                    auto value = config.get<std::string>("section_0.item_0");
                    read_operations.fetch_add(1);
                } catch (const std::exception&) {
                    // Ignore read errors for performance test
                }
            }
        }));
    }
    
    // Launch writer threads
    std::vector<std::future<void>> writer_futures;
    for (int i = 0; i < 2; ++i) {
        writer_futures.emplace_back(std::async(std::launch::async, [&, i]() {
            int counter = 0;
            while (!stop_test.load()) {
                try {
                    std::string key = "concurrent_write_" + std::to_string(i);
                    std::string value = "value_" + std::to_string(counter++);
                    config.set(key, value);
                    write_operations.fetch_add(1);
                } catch (const std::exception&) {
                    // Ignore write errors for performance test
                }
                std::this_thread::sleep_for(10ms); // Limit write frequency
            }
        }));
    }
    
    // Run test for 5 seconds
    std::this_thread::sleep_for(5s);
    stop_test.store(true);
    
    // Wait for all threads to complete
    for (auto& future : reader_futures) {
        future.wait();
    }
    for (auto& future : writer_futures) {
        future.wait();
    }
    
    double read_ops_per_sec = read_operations.load() / 5.0;
    double write_ops_per_sec = write_operations.load() / 5.0;
    
    std::cout << "\n=== Concurrent Access Performance ===" << std::endl;
    std::cout << "Read operations/sec: " << read_ops_per_sec << std::endl;
    std::cout << "Write operations/sec: " << write_ops_per_sec << std::endl;
    std::cout << "Total operations: " << (read_operations.load() + write_operations.load()) << std::endl;
    std::cout << "======================================" << std::endl;
    
    EXPECT_GT(read_ops_per_sec, 1000.0) << "Concurrent read performance too low";
    EXPECT_GT(write_ops_per_sec, 50.0) << "Concurrent write performance too low";
    EXPECT_EQ(read_operations.load() + write_operations.load(), 
              read_operations.load() + write_operations.load()) << "Operation count mismatch";
}

// Benchmark environment-specific configuration loading
TEST_F(ComprehensiveConfigurationPerformanceBenchmark, EnvironmentConfigurationPerformance) {
    ConfigurationEnvironment env_manager;
    
    // Create environment-specific configs
    std::vector<std::string> environments = {"dev", "test", "stage", "prod"};
    std::vector<std::filesystem::path> env_config_paths;
    
    for (const auto& env : environments) {
        auto env_path = test_dir_ / ("config_" + env + ".yaml");
        create_yaml_config(env_path, 50, 10);
        env_config_paths.push_back(env_path);
    }
    
    // Benchmark environment resolution and loading
    for (size_t i = 0; i < environments.size(); ++i) {
        const auto& env = environments[i];
        const auto& path = env_config_paths[i];
        
        auto stats = measure_operation([&]() {
            auto timer = perf_monitor_->start_timer("env_load_" + env);
            auto resolved_config = env_manager.resolve_environment_config(path.string(), env);
            EXPECT_TRUE(resolved_config.has_value());
        }, 50, 5);
        
        stats.print("Environment Configuration Loading - " + env);
        
        auto avg_ms = stats.avg_time.count() / 1e6;
        EXPECT_LT(avg_ms, 150.0) << "Environment config loading exceeded 150ms for " << env;
    }
    
    // Benchmark environment switching
    auto switch_stats = measure_operation([&]() {
        static size_t env_index = 0;
        const auto& env = environments[env_index % environments.size()];
        const auto& path = env_config_paths[env_index % environments.size()];
        env_index++;
        
        auto timer = perf_monitor_->start_timer("env_switch");
        env_manager.switch_environment(env, path.string());
    }, 100, 10);
    
    switch_stats.print("Environment Switching");
    
    auto switch_avg_ms = switch_stats.avg_time.count() / 1e6;
    EXPECT_LT(switch_avg_ms, 100.0) << "Environment switching exceeded 100ms";
}

// Comprehensive SLA validation test
TEST_F(ComprehensiveConfigurationPerformanceBenchmark, SLAComplianceValidation) {
    // Run comprehensive benchmark using the built-in performance monitor
    auto benchmark_results = perf_monitor_->run_benchmark(medium_config_path_.string(), 100);
    
    std::cout << "\n=== SLA Compliance Validation ===" << std::endl;
    std::cout << "Total operations benchmarked: " << benchmark_results.total_samples << std::endl;
    std::cout << "Overall SLA compliance: " << (benchmark_results.overall_sla_compliance ? "PASS" : "FAIL") << std::endl;
    
    bool all_sla_met = true;
    std::vector<std::string> violations;
    
    for (const auto& operation : benchmark_results.operations) {
        std::cout << "\nOperation: " << operation.name << std::endl;
        std::cout << "  Average time: " << operation.avg_time.count() << "μs" << std::endl;
        std::cout << "  P95 time: " << operation.p95_time.count() << "μs" << std::endl;
        std::cout << "  P99 time: " << operation.p99_time.count() << "μs" << std::endl;
        std::cout << "  SLA threshold: " << operation.sla_threshold.count() << "μs" << std::endl;
        std::cout << "  Meets SLA: " << (operation.meets_sla ? "YES" : "NO") << std::endl;
        
        if (!operation.meets_sla) {
            all_sla_met = false;
            violations.push_back(operation.name + " exceeded SLA (" + 
                                std::to_string(operation.avg_time.count()) + "μs > " +
                                std::to_string(operation.sla_threshold.count()) + "μs)");
        }
    }
    
    if (!violations.empty()) {
        std::cout << "\nSLA Violations:" << std::endl;
        for (const auto& violation : violations) {
            std::cout << "  - " << violation << std::endl;
        }
    }
    
    std::cout << "======================================" << std::endl;
    
    // Export detailed results
    auto json_results = perf_monitor_->export_benchmark_json(benchmark_results);
    auto results_file = test_dir_ / "benchmark_results.json";
    std::ofstream results_output(results_file);
    results_output << json_results;
    
    std::cout << "Detailed benchmark results exported to: " << results_file << std::endl;
    
    // Verify overall SLA compliance
    EXPECT_TRUE(benchmark_results.overall_sla_compliance) << "Configuration system failed SLA compliance";
    EXPECT_TRUE(all_sla_met) << "Individual operations failed SLA requirements";
    
    // Additional performance assertions
    EXPECT_GT(benchmark_results.total_samples, 500) << "Insufficient benchmark samples";
    
    // Verify specific SLA requirements mentioned in gap report
    bool load_sla_met = false;
    bool reload_sla_met = false;
    bool cache_sla_met = false;
    
    for (const auto& operation : benchmark_results.operations) {
        if (operation.name.find("load") != std::string::npos) {
            load_sla_met = operation.avg_time < std::chrono::microseconds(100000); // 100ms
        }
        if (operation.name.find("reload") != std::string::npos) {
            reload_sla_met = operation.avg_time < std::chrono::microseconds(50000); // 50ms
        }
        if (operation.name.find("cache") != std::string::npos) {
            cache_sla_met = operation.avg_time < std::chrono::microseconds(1000); // 1ms
        }
    }
    
    EXPECT_TRUE(load_sla_met) << "Configuration load SLA not met (<100ms requirement)";
    EXPECT_TRUE(reload_sla_met) << "Configuration hot-reload SLA not met (<50ms requirement)";
    EXPECT_TRUE(cache_sla_met) << "Configuration cache SLA not met (<1ms requirement)";
    
    // Memory usage validation
    auto current_metrics = perf_monitor_->get_metrics();
    double config_size_mb = current_metrics.config_size_bytes / (1024.0 * 1024.0);
    EXPECT_LT(config_size_mb, 50.0) << "Configuration memory usage too high";
    
    // Cache effectiveness validation
    if (current_metrics.cache_hit_count + current_metrics.cache_miss_count > 0) {
        double cache_hit_rate = static_cast<double>(current_metrics.cache_hit_count) /
                               (current_metrics.cache_hit_count + current_metrics.cache_miss_count);
        EXPECT_GT(cache_hit_rate, 0.8) << "Configuration cache hit rate too low";
    }
}

// Memory usage and leak detection
TEST_F(ComprehensiveConfigurationPerformanceBenchmark, MemoryUsageValidation) {
    size_t initial_memory = get_current_memory_usage();
    
    // Perform many configuration operations
    for (int i = 0; i < 100; ++i) {
        ConfigurationManager config;
        config.load_from_file(large_config_path_.string());
        
        // Perform various operations
        try {
            config.get<std::string>("section_0.item_0");
            config.set("temp_key_" + std::to_string(i), "temp_value");
            config.validate();
        } catch (const std::exception&) {
            // Ignore errors for memory test
        }
    }
    
    // Force garbage collection (if applicable)
    std::this_thread::sleep_for(100ms);
    
    size_t final_memory = get_current_memory_usage();
    size_t memory_growth = final_memory > initial_memory ? final_memory - initial_memory : 0;
    
    std::cout << "\n=== Memory Usage Analysis ===" << std::endl;
    std::cout << "Initial memory: " << (initial_memory / 1024.0 / 1024.0) << " MB" << std::endl;
    std::cout << "Final memory: " << (final_memory / 1024.0 / 1024.0) << " MB" << std::endl;
    std::cout << "Memory growth: " << (memory_growth / 1024.0 / 1024.0) << " MB" << std::endl;
    std::cout << "Memory per operation: " << (memory_growth / 100.0 / 1024.0) << " KB" << std::endl;
    std::cout << "==============================" << std::endl;
    
    // Memory growth should be reasonable
    EXPECT_LT(memory_growth, 50 * 1024 * 1024) << "Memory growth exceeded 50MB";
    EXPECT_LT(memory_growth / 100.0, 100 * 1024) << "Memory per operation exceeded 100KB";
}

private:
    size_t get_current_memory_usage() {
#ifdef __linux__
        std::ifstream statm("/proc/self/statm");
        if (statm) {
            size_t size, resident;
            statm >> size >> resident;
            return resident * getpagesize();
        }
#elif defined(__APPLE__)
        mach_task_basic_info info;
        mach_msg_type_number_t info_count = MACH_TASK_BASIC_INFO_COUNT;
        if (task_info(mach_task_self(), MACH_TASK_BASIC_INFO, 
                      (task_info_t)&info, &info_count) == KERN_SUCCESS) {
            return info.resident_size;
        }
#endif
        return 0;
    }
};