/**
 * @file error_recovery_integration_test.cpp
 * @brief Comprehensive integration tests for error recovery mechanisms
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#include <gtest/gtest.h>
#include "common/error_recovery.h"
#include "common/exceptions.h"
#include "common/metrics_collector.h"
#include "common/logging.h"
#include "test_helpers/integration_test_base.h"
#include <chrono>
#include <thread>
#include <atomic>
#include <memory>
#include <future>
#include <random>
#include <vector>
#include <fstream>

using namespace omop::common;
using namespace std::chrono_literals;

class ErrorRecoveryIntegrationTest : public testing::IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        
        // Initialize logging for integration tests
        common::LoggingConfig::initialize_default();
        logger_ = common::Logger::get("error-recovery-integration");
        
        // Setup metrics collection
        metrics_collector_ = std::make_shared<omop::monitoring::MetricsCollector>();
        
        // Configure error recovery engine
        ErrorRecoveryEngine::Config config;
        config.enable_retry = true;
        config.enable_fallback = true;
        config.enable_circuit_breaker = true;
        config.enable_bulkhead = true;
        config.collect_statistics = true;
        config.default_timeout = 5s;
        
        recovery_engine_ = std::make_unique<ErrorRecoveryEngine>(config);
        recovery_engine_->set_metrics_collector(metrics_collector_);
        
        // Initialize test counters
        operation_count_.store(0);
        success_count_.store(0);
        failure_count_.store(0);
        retry_count_.store(0);
        fallback_count_.store(0);
        circuit_breaker_trips_.store(0);
    }
    
    void TearDown() override {
        recovery_engine_.reset();
        metrics_collector_.reset();
        IntegrationTestBase::TearDown();
    }
    
    // Simulated network operation with configurable failure rate
    class NetworkOperation {
    public:
        NetworkOperation(double failure_rate = 0.3, std::chrono::milliseconds latency = 100ms)
            : failure_rate_(failure_rate), latency_(latency), rng_(std::random_device{}()) {}
        
        std::string call_api(const std::string& endpoint) {
            std::this_thread::sleep_for(latency_);
            
            std::uniform_real_distribution<double> dist(0.0, 1.0);
            if (dist(rng_) < failure_rate_) {
                throw NetworkException("Simulated network failure for endpoint: " + endpoint);
            }
            
            return "success_response_" + endpoint;
        }
        
        void set_failure_rate(double rate) { failure_rate_ = rate; }
        void set_latency(std::chrono::milliseconds latency) { latency_ = latency; }
        
    private:
        double failure_rate_;
        std::chrono::milliseconds latency_;
        mutable std::mt19937 rng_;
    };
    
    // Simulated database operation with various failure modes
    class DatabaseOperation {
    public:
        enum class FailureMode { None, Timeout, Connection, DeadLock, OutOfMemory };
        
        DatabaseOperation(FailureMode mode = FailureMode::None, int fail_count = 3)
            : failure_mode_(mode), fail_count_(fail_count), call_count_(0) {}
        
        std::vector<std::string> execute_query(const std::string& query) {
            call_count_++;
            
            // Simulate various failure scenarios
            if (call_count_ <= fail_count_) {
                switch (failure_mode_) {
                    case FailureMode::Timeout:
                        std::this_thread::sleep_for(100ms);
                        throw TimeoutException("Database query timeout");
                    case FailureMode::Connection:
                        throw ConnectionException("Database connection lost");
                    case FailureMode::DeadLock:
                        throw DatabaseException("Deadlock detected");
                    case FailureMode::OutOfMemory:
                        throw ResourceException("Out of memory");
                    case FailureMode::None:
                        break;
                }
            }
            
            return {"result1", "result2", "result3"};
        }
        
        void reset() { call_count_ = 0; }
        int get_call_count() const { return call_count_; }
        
    private:
        FailureMode failure_mode_;
        int fail_count_;
        std::atomic<int> call_count_;
    };
    
    // Simulated file operation
    class FileOperation {
    public:
        FileOperation(const std::string& base_path) : base_path_(base_path) {}
        
        void write_file(const std::string& filename, const std::string& content) {
            std::string full_path = base_path_ + "/" + filename;
            std::ofstream file(full_path);
            if (!file) {
                throw FileSystemException("Cannot write file: " + full_path);
            }
            file << content;
            if (!file) {
                throw FileSystemException("Write operation failed for: " + full_path);
            }
        }
        
        std::string read_file(const std::string& filename) {
            std::string full_path = base_path_ + "/" + filename;
            std::ifstream file(full_path);
            if (!file) {
                throw FileSystemException("Cannot read file: " + full_path);
            }
            
            std::string content((std::istreambuf_iterator<char>(file)),
                               std::istreambuf_iterator<char>());
            return content;
        }
        
    private:
        std::string base_path_;
    };

protected:
    std::unique_ptr<ErrorRecoveryEngine> recovery_engine_;
    std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector_;
    std::shared_ptr<common::Logger> logger_;
    
    // Test counters
    std::atomic<int> operation_count_;
    std::atomic<int> success_count_;
    std::atomic<int> failure_count_;
    std::atomic<int> retry_count_;
    std::atomic<int> fallback_count_;
    std::atomic<int> circuit_breaker_trips_;
};

// Test retry mechanism with transient network failures
TEST_F(ErrorRecoveryIntegrationTest, RetryMechanismWithNetworkFailures) {
    NetworkOperation network_op(0.7, 50ms); // 70% failure rate
    
    RetryPolicy policy;
    policy.max_attempts = 5;
    policy.base_delay = 10ms;
    policy.backoff_multiplier = 2.0;
    policy.jitter = true;
    policy.applicable_errors = {ErrorType::Transient};
    
    auto start_time = std::chrono::steady_clock::now();
    
    auto result = recovery_engine_->with_retry(policy, [&]() -> std::string {
        operation_count_++;
        try {
            auto response = network_op.call_api("test_endpoint");
            success_count_++;
            return response;
        } catch (const NetworkException& e) {
            retry_count_++;
            throw; // Re-throw for retry mechanism
        }
    });
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result.value(), "success_response_test_endpoint");
    EXPECT_GT(operation_count_.load(), 1); // Should have retried
    EXPECT_EQ(success_count_.load(), 1);
    EXPECT_GT(retry_count_.load(), 0);
    
    // Verify exponential backoff timing
    EXPECT_GT(duration.count(), 50); // At least one operation latency
    
    logger_->info("Network retry test completed - Operations: {}, Retries: {}, Duration: {}ms",
                  operation_count_.load(), retry_count_.load(), duration.count());
}

// Test fallback mechanism with database failures
TEST_F(ErrorRecoveryIntegrationTest, FallbackMechanismWithDatabaseFailures) {
    DatabaseOperation primary_db(DatabaseOperation::FailureMode::Connection, 10); // Always fails
    DatabaseOperation fallback_db(DatabaseOperation::FailureMode::None, 0); // Always succeeds
    
    FallbackPolicy policy;
    policy.applicable_errors = {ErrorType::Persistent};
    policy.enable_statistics = true;
    
    auto result = recovery_engine_->with_fallback(
        policy,
        // Primary operation
        [&]() -> std::vector<std::string> {
            operation_count_++;
            return primary_db.execute_query("SELECT * FROM patients");
        },
        // Fallback operation
        [&]() -> std::vector<std::string> {
            fallback_count_++;
            logger_->warn("Using fallback database for query execution");
            return fallback_db.execute_query("SELECT * FROM patients_backup");
        }
    );
    
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result.value().size(), 3);
    EXPECT_EQ(operation_count_.load(), 1);
    EXPECT_EQ(fallback_count_.load(), 1);
    
    logger_->info("Database fallback test completed - Used fallback: {}", fallback_count_.load());
}

// Test circuit breaker pattern under high failure rate
TEST_F(ErrorRecoveryIntegrationTest, CircuitBreakerPatternHighFailureRate) {
    NetworkOperation failing_network(1.0, 10ms); // 100% failure rate
    
    CircuitBreakerPolicy policy;
    policy.failure_threshold = 3;
    policy.recovery_timeout = 100ms;
    policy.success_threshold = 2;
    policy.applicable_errors = {ErrorType::Transient};
    
    std::vector<std::future<void>> futures;
    std::atomic<int> circuit_open_count{0};
    std::atomic<int> total_attempts{0};
    
    // Launch multiple concurrent operations
    for (int i = 0; i < 10; ++i) {
        futures.emplace_back(std::async(std::launch::async, [&, i]() {
            try {
                auto result = recovery_engine_->with_circuit_breaker(policy, [&]() -> std::string {
                    total_attempts++;
                    return failing_network.call_api("endpoint_" + std::to_string(i));
                });
                if (result.has_value()) {
                    success_count_++;
                }
            } catch (const CircuitOpenException& e) {
                circuit_open_count++;
                logger_->info("Circuit breaker open for operation {}", i);
            } catch (const std::exception& e) {
                failure_count_++;
            }
        }));
    }
    
    // Wait for all operations to complete
    for (auto& future : futures) {
        future.wait();
    }
    
    EXPECT_GT(circuit_open_count.load(), 0); // Circuit should have opened
    EXPECT_LT(total_attempts.load(), 30); // Should prevent excessive attempts
    EXPECT_EQ(success_count_.load(), 0); // No successes expected with 100% failure rate
    
    logger_->info("Circuit breaker test completed - Circuit opens: {}, Total attempts: {}",
                  circuit_open_count.load(), total_attempts.load());
}

// Test bulkhead pattern for resource isolation
TEST_F(ErrorRecoveryIntegrationTest, BulkheadPatternResourceIsolation) {
    BulkheadPolicy critical_policy;
    critical_policy.name = "critical_operations";
    critical_policy.max_concurrent_calls = 2;
    critical_policy.queue_size = 5;
    
    BulkheadPolicy normal_policy;
    normal_policy.name = "normal_operations";
    normal_policy.max_concurrent_calls = 5;
    normal_policy.queue_size = 10;
    
    std::atomic<int> critical_operations{0};
    std::atomic<int> normal_operations{0};
    std::atomic<int> rejected_operations{0};
    
    std::vector<std::future<void>> futures;
    
    // Launch critical operations (limited concurrency)
    for (int i = 0; i < 8; ++i) {
        futures.emplace_back(std::async(std::launch::async, [&, i]() {
            try {
                auto result = recovery_engine_->with_bulkhead(critical_policy, [&]() -> int {
                    critical_operations++;
                    std::this_thread::sleep_for(100ms); // Simulate work
                    return i;
                });
                if (result.has_value()) {
                    logger_->debug("Critical operation {} completed", i);
                }
            } catch (const ResourceException& e) {
                rejected_operations++;
                logger_->warn("Critical operation {} rejected: {}", i, e.what());
            }
        }));
    }
    
    // Launch normal operations (higher concurrency)
    for (int i = 0; i < 15; ++i) {
        futures.emplace_back(std::async(std::launch::async, [&, i]() {
            try {
                auto result = recovery_engine_->with_bulkhead(normal_policy, [&]() -> int {
                    normal_operations++;
                    std::this_thread::sleep_for(50ms); // Simulate work
                    return i;
                });
                if (result.has_value()) {
                    logger_->debug("Normal operation {} completed", i);
                }
            } catch (const ResourceException& e) {
                rejected_operations++;
                logger_->warn("Normal operation {} rejected: {}", i, e.what());
            }
        }));
    }
    
    // Wait for all operations
    for (auto& future : futures) {
        future.wait();
    }
    
    // Verify bulkhead constraints were enforced
    EXPECT_GT(critical_operations.load(), 0);
    EXPECT_GT(normal_operations.load(), 0);
    EXPECT_GT(rejected_operations.load(), 0); // Some operations should be rejected
    
    logger_->info("Bulkhead test completed - Critical: {}, Normal: {}, Rejected: {}",
                  critical_operations.load(), normal_operations.load(), rejected_operations.load());
}

// Test comprehensive error recovery in realistic ETL scenario
TEST_F(ErrorRecoveryIntegrationTest, ComprehensiveETLErrorRecovery) {
    NetworkOperation data_source(0.2, 30ms); // 20% failure rate
    DatabaseOperation primary_db(DatabaseOperation::FailureMode::DeadLock, 2);
    DatabaseOperation backup_db(DatabaseOperation::FailureMode::None, 0);
    FileOperation file_ops("/tmp/omop_test");
    
    // Create comprehensive policy combining all patterns
    CompositeRecoveryPolicy composite_policy;
    
    // Retry policy for transient failures
    RetryPolicy retry_policy;
    retry_policy.max_attempts = 3;
    retry_policy.base_delay = 50ms;
    retry_policy.backoff_multiplier = 1.5;
    retry_policy.applicable_errors = {ErrorType::Transient};
    
    // Fallback policy for persistent failures
    FallbackPolicy fallback_policy;
    fallback_policy.applicable_errors = {ErrorType::Persistent};
    
    // Circuit breaker for preventing cascade failures
    CircuitBreakerPolicy circuit_policy;
    circuit_policy.failure_threshold = 5;
    circuit_policy.recovery_timeout = 200ms;
    
    composite_policy.add_policy(std::make_unique<RetryPolicy>(retry_policy));
    composite_policy.add_policy(std::make_unique<FallbackPolicy>(fallback_policy));
    composite_policy.add_policy(std::make_unique<CircuitBreakerPolicy>(circuit_policy));
    
    std::atomic<int> etl_jobs_completed{0};
    std::atomic<int> etl_jobs_failed{0};
    
    auto run_etl_job = [&](int job_id) -> bool {
        try {
            // Step 1: Extract data (with retry for network issues)
            auto extract_result = recovery_engine_->with_retry(retry_policy, [&]() -> std::string {
                return data_source.call_api("extract_endpoint_" + std::to_string(job_id));
            });
            
            if (!extract_result.has_value()) {
                throw std::runtime_error("Data extraction failed");
            }
            
            // Step 2: Transform data (simulated)
            std::string transformed_data = "transformed_" + extract_result.value();
            
            // Step 3: Load data (with fallback for database issues)
            auto load_result = recovery_engine_->with_fallback(
                fallback_policy,
                // Primary load
                [&]() -> std::vector<std::string> {
                    return primary_db.execute_query("INSERT INTO patients_new VALUES (...)");
                },
                // Fallback load
                [&]() -> std::vector<std::string> {
                    logger_->warn("Using backup database for job {}", job_id);
                    return backup_db.execute_query("INSERT INTO patients_backup VALUES (...)");
                }
            );
            
            if (!load_result.has_value()) {
                throw std::runtime_error("Data loading failed");
            }
            
            // Step 4: Write audit log
            file_ops.write_file("job_" + std::to_string(job_id) + "_audit.log",
                              "Job completed: " + std::to_string(job_id));
            
            etl_jobs_completed++;
            return true;
            
        } catch (const std::exception& e) {
            etl_jobs_failed++;
            logger_->error("ETL job {} failed: {}", job_id, e.what());
            return false;
        }
    };
    
    // Run multiple ETL jobs concurrently
    std::vector<std::future<bool>> job_futures;
    for (int i = 0; i < 20; ++i) {
        job_futures.emplace_back(
            std::async(std::launch::async, run_etl_job, i)
        );
    }
    
    // Wait for all jobs to complete
    int successful_jobs = 0;
    for (auto& future : job_futures) {
        if (future.get()) {
            successful_jobs++;
        }
    }
    
    EXPECT_GT(successful_jobs, 15); // Expect most jobs to succeed despite failures
    EXPECT_EQ(etl_jobs_completed.load(), successful_jobs);
    EXPECT_LT(etl_jobs_failed.load(), 5); // Expect minimal failures due to recovery
    
    // Verify metrics collection
    auto stats = recovery_engine_->get_statistics();
    EXPECT_GT(stats.total_operations, 0);
    EXPECT_GT(stats.successful_recoveries, 0);
    
    logger_->info("Comprehensive ETL test completed - Success: {}, Failed: {}, Recovery stats: {}",
                  successful_jobs, etl_jobs_failed.load(), stats.successful_recoveries);
}

// Test timeout and resource management
TEST_F(ErrorRecoveryIntegrationTest, TimeoutAndResourceManagement) {
    std::atomic<int> timeout_operations{0};
    std::atomic<int> resource_exhaustion_ops{0};
    
    TimeoutPolicy timeout_policy;
    timeout_policy.operation_timeout = 100ms;
    timeout_policy.enable_cancellation = true;
    
    auto slow_operation = [&](int delay_ms) -> std::string {
        std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms));
        return "completed";
    };
    
    // Test operations with various delays
    std::vector<std::pair<int, bool>> test_cases = {
        {50, true},   // Should succeed (under timeout)
        {150, false}, // Should timeout
        {75, true},   // Should succeed
        {200, false}, // Should timeout
        {25, true}    // Should succeed
    };
    
    for (const auto& [delay, should_succeed] : test_cases) {
        try {
            auto result = recovery_engine_->with_timeout(timeout_policy, [&]() -> std::string {
                return slow_operation(delay);
            });
            
            if (should_succeed) {
                EXPECT_TRUE(result.has_value());
                EXPECT_EQ(result.value(), "completed");
            } else {
                EXPECT_FALSE(result.has_value()); // Should timeout
                timeout_operations++;
            }
            
        } catch (const TimeoutException& e) {
            timeout_operations++;
            EXPECT_FALSE(should_succeed);
            logger_->debug("Operation with {}ms delay timed out as expected", delay);
        }
    }
    
    EXPECT_GT(timeout_operations.load(), 0);
    
    logger_->info("Timeout management test completed - Timeouts: {}", timeout_operations.load());
}

// Test error aggregation and statistics
TEST_F(ErrorRecoveryIntegrationTest, ErrorAggregationAndStatistics) {
    NetworkOperation unreliable_service(0.5, 20ms); // 50% failure rate
    
    RetryPolicy policy;
    policy.max_attempts = 3;
    policy.base_delay = 10ms;
    policy.collect_statistics = true;
    
    std::atomic<int> total_calls{0};
    std::atomic<int> network_errors{0};
    std::atomic<int> success_after_retry{0};
    
    // Make many calls to gather statistics
    std::vector<std::future<void>> futures;
    for (int i = 0; i < 100; ++i) {
        futures.emplace_back(std::async(std::launch::async, [&, i]() {
            int retry_count = 0;
            try {
                auto result = recovery_engine_->with_retry(policy, [&]() -> std::string {
                    total_calls++;
                    try {
                        return unreliable_service.call_api("service_" + std::to_string(i));
                    } catch (const NetworkException& e) {
                        network_errors++;
                        retry_count++;
                        throw;
                    }
                });
                
                if (result.has_value() && retry_count > 0) {
                    success_after_retry++;
                }
                
            } catch (const std::exception& e) {
                // Final failure after retries
            }
        }));
    }
    
    // Wait for all operations
    for (auto& future : futures) {
        future.wait();
    }
    
    // Get comprehensive statistics
    auto stats = recovery_engine_->get_statistics();
    auto error_stats = recovery_engine_->get_error_aggregation_stats();
    
    EXPECT_GT(total_calls.load(), 100); // Should have retry attempts
    EXPECT_GT(network_errors.load(), 0);
    EXPECT_GT(success_after_retry.load(), 0);
    
    EXPECT_GT(stats.total_operations, 0);
    EXPECT_GT(stats.retry_operations, 0);
    EXPECT_GT(error_stats.total_errors, 0);
    EXPECT_GT(error_stats.error_types.count(ErrorType::Transient), 0);
    
    logger_->info("Statistics test completed - Total calls: {}, Network errors: {}, Success after retry: {}",
                  total_calls.load(), network_errors.load(), success_after_retry.load());
    logger_->info("Recovery stats - Operations: {}, Retries: {}, Errors: {}",
                  stats.total_operations, stats.retry_operations, error_stats.total_errors);
}

// Test cascading failure prevention
TEST_F(ErrorRecoveryIntegrationTest, CascadingFailurePrevention) {
    // Simulate a service that becomes overloaded and starts failing
    class OverloadedService {
    public:
        OverloadedService() : load_factor_(0.0), max_capacity_(10) {}
        
        std::string process_request(const std::string& request) {
            std::lock_guard<std::mutex> lock(mutex_);
            active_requests_++;
            
            // Calculate current load factor
            load_factor_ = static_cast<double>(active_requests_) / max_capacity_;
            
            // Higher failure rate as load increases
            double failure_probability = load_factor_ > 1.0 ? 0.8 : load_factor_ * 0.5;
            
            std::uniform_real_distribution<double> dist(0.0, 1.0);
            std::random_device rd;
            std::mt19937 gen(rd());
            
            if (dist(gen) < failure_probability) {
                active_requests_--;
                throw ServiceException("Service overloaded, load factor: " + std::to_string(load_factor_));
            }
            
            // Simulate processing time proportional to load
            std::this_thread::sleep_for(std::chrono::milliseconds(
                static_cast<int>(20 + load_factor_ * 50)
            ));
            
            active_requests_--;
            return "processed_" + request;
        }
        
        double get_load_factor() const {
            std::lock_guard<std::mutex> lock(mutex_);
            return load_factor_;
        }
        
    private:
        mutable std::mutex mutex_;
        std::atomic<int> active_requests_{0};
        double load_factor_;
        int max_capacity_;
    };
    
    OverloadedService service;
    
    // Configure circuit breaker to prevent cascading failures
    CircuitBreakerPolicy circuit_policy;
    circuit_policy.failure_threshold = 3;
    circuit_policy.recovery_timeout = 500ms;
    circuit_policy.success_threshold = 2;
    
    BulkheadPolicy bulkhead_policy;
    bulkhead_policy.name = "service_calls";
    bulkhead_policy.max_concurrent_calls = 5; // Limit concurrent calls
    bulkhead_policy.queue_size = 10;
    
    std::atomic<int> successful_requests{0};
    std::atomic<int> failed_requests{0};
    std::atomic<int> circuit_breaker_rejections{0};
    std::atomic<int> bulkhead_rejections{0};
    
    auto make_service_call = [&](int request_id) {
        try {
            // Combine circuit breaker and bulkhead patterns
            auto result = recovery_engine_->with_circuit_breaker(circuit_policy, [&]() -> std::string {
                return recovery_engine_->with_bulkhead(bulkhead_policy, [&]() -> std::string {
                    return service.process_request("request_" + std::to_string(request_id));
                }).value_or("bulkhead_rejected");
            });
            
            if (result.has_value() && result.value() != "bulkhead_rejected") {
                successful_requests++;
            } else if (result.value() == "bulkhead_rejected") {
                bulkhead_rejections++;
            }
            
        } catch (const CircuitOpenException& e) {
            circuit_breaker_rejections++;
        } catch (const ResourceException& e) {
            bulkhead_rejections++;
        } catch (const std::exception& e) {
            failed_requests++;
        }
    };
    
    // Launch many concurrent requests to stress the system
    std::vector<std::future<void>> request_futures;
    for (int i = 0; i < 50; ++i) {
        request_futures.emplace_back(
            std::async(std::launch::async, make_service_call, i)
        );
        
        // Add some jitter to request timing
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // Wait for all requests
    for (auto& future : request_futures) {
        future.wait();
    }
    
    // Verify that circuit breaker prevented excessive load
    EXPECT_GT(successful_requests.load(), 10); // Some should succeed
    EXPECT_GT(circuit_breaker_rejections.load(), 0); // Circuit should have opened
    EXPECT_GT(bulkhead_rejections.load(), 0); // Bulkhead should have limited concurrency
    
    double total_rejections = circuit_breaker_rejections.load() + bulkhead_rejections.load();
    double protection_rate = total_rejections / 50.0;
    EXPECT_GT(protection_rate, 0.2); // At least 20% of requests should be rejected for protection
    
    logger_->info("Cascading failure prevention test completed - Success: {}, Circuit rejections: {}, Bulkhead rejections: {}",
                  successful_requests.load(), circuit_breaker_rejections.load(), bulkhead_rejections.load());
    logger_->info("Protection rate: {:.2f}%, Service load factor: {:.2f}",
                  protection_rate * 100.0, service.get_load_factor());
}

} // namespace omop::test