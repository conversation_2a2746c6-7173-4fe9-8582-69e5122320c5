/**
 * @file error_recovery_performance_benchmark.cpp
 * @brief Performance benchmarks for error recovery mechanisms
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#include <gtest/gtest.h>
#include <benchmark/benchmark.h>
#include "common/error_recovery.h"
#include "common/exceptions.h"
#include "common/metrics_collector.h"
#include "common/logging.h"
#include <chrono>
#include <thread>
#include <atomic>
#include <memory>
#include <future>
#include <random>
#include <vector>
#include <algorithm>
#include <numeric>

using namespace omop::common;
using namespace std::chrono_literals;

class ErrorRecoveryBenchmark : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup error recovery engine
        ErrorRecoveryEngine::Config config;
        config.enable_retry = true;
        config.enable_fallback = true;
        config.enable_circuit_breaker = true;
        config.enable_bulkhead = true;
        config.collect_statistics = true;
        config.default_timeout = 1s;
        
        recovery_engine_ = std::make_unique<ErrorRecoveryEngine>(config);
        
        // Setup metrics collection
        metrics_collector_ = std::make_shared<omop::monitoring::MetricsCollector>();
        recovery_engine_->set_metrics_collector(metrics_collector_);
        
        // Initialize logger (minimize logging for benchmarks)
        common::LoggingConfig config_logging;
        config_logging.level = common::LogLevel::ERROR; // Only log errors during benchmarks
        config_logging.async_logging = true;
        config_logging.buffer_size = 1000;
        common::LoggingConfig::initialize(config_logging);
        
        logger_ = common::Logger::get("error-recovery-benchmark");
    }
    
    void TearDown() override {
        recovery_engine_.reset();
        metrics_collector_.reset();
    }
    
    // Performance measurement utilities
    struct BenchmarkResult {
        std::chrono::nanoseconds min_duration;
        std::chrono::nanoseconds max_duration;
        std::chrono::nanoseconds avg_duration;
        std::chrono::nanoseconds median_duration;
        std::chrono::nanoseconds p95_duration;
        std::chrono::nanoseconds p99_duration;
        double throughput_ops_per_second;
        size_t total_operations;
        size_t successful_operations;
        size_t failed_operations;
        double success_rate;
    };
    
    BenchmarkResult measure_performance(std::function<bool()> operation, 
                                       int iterations,
                                       int concurrent_threads = 1) {
        std::vector<std::chrono::nanoseconds> durations;
        std::atomic<size_t> successful_ops{0};
        std::atomic<size_t> failed_ops{0};
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        if (concurrent_threads == 1) {
            // Sequential execution
            for (int i = 0; i < iterations; ++i) {
                auto op_start = std::chrono::high_resolution_clock::now();
                bool success = operation();
                auto op_end = std::chrono::high_resolution_clock::now();
                
                durations.push_back(op_end - op_start);
                
                if (success) {
                    successful_ops++;
                } else {
                    failed_ops++;
                }
            }
        } else {
            // Concurrent execution
            std::vector<std::future<void>> futures;
            int ops_per_thread = iterations / concurrent_threads;
            
            for (int t = 0; t < concurrent_threads; ++t) {
                futures.emplace_back(std::async(std::launch::async, [&, ops_per_thread]() {
                    for (int i = 0; i < ops_per_thread; ++i) {
                        auto op_start = std::chrono::high_resolution_clock::now();
                        bool success = operation();
                        auto op_end = std::chrono::high_resolution_clock::now();
                        
                        {
                            static std::mutex duration_mutex;
                            std::lock_guard<std::mutex> lock(duration_mutex);
                            durations.push_back(op_end - op_start);
                        }
                        
                        if (success) {
                            successful_ops++;
                        } else {
                            failed_ops++;
                        }
                    }
                }));
            }
            
            for (auto& future : futures) {
                future.wait();
            }
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto total_duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end_time - start_time);
        
        // Sort durations for percentile calculations
        std::sort(durations.begin(), durations.end());
        
        BenchmarkResult result;
        result.total_operations = durations.size();
        result.successful_operations = successful_ops;
        result.failed_operations = failed_ops;
        result.success_rate = static_cast<double>(successful_ops) / durations.size();
        
        if (!durations.empty()) {
            result.min_duration = durations.front();
            result.max_duration = durations.back();
            result.avg_duration = std::chrono::nanoseconds(
                std::accumulate(durations.begin(), durations.end(), 0LL,
                [](long long sum, const std::chrono::nanoseconds& d) {
                    return sum + d.count();
                }) / durations.size()
            );
            
            result.median_duration = durations[durations.size() / 2];
            result.p95_duration = durations[static_cast<size_t>(durations.size() * 0.95)];
            result.p99_duration = durations[static_cast<size_t>(durations.size() * 0.99)];
        }
        
        result.throughput_ops_per_second = static_cast<double>(durations.size()) / 
                                          (static_cast<double>(total_duration.count()) / 1e9);
        
        return result;
    }
    
    void print_benchmark_result(const std::string& test_name, const BenchmarkResult& result) {
        std::cout << "\n=== " << test_name << " Performance Results ===" << std::endl;
        std::cout << "Total Operations: " << result.total_operations << std::endl;
        std::cout << "Successful: " << result.successful_operations 
                  << " (" << (result.success_rate * 100.0) << "%)" << std::endl;
        std::cout << "Failed: " << result.failed_operations << std::endl;
        std::cout << "Throughput: " << std::fixed << std::setprecision(2) 
                  << result.throughput_ops_per_second << " ops/sec" << std::endl;
        std::cout << "Latency (μs):" << std::endl;
        std::cout << "  Min: " << (result.min_duration.count() / 1000.0) << std::endl;
        std::cout << "  Avg: " << (result.avg_duration.count() / 1000.0) << std::endl;
        std::cout << "  Median: " << (result.median_duration.count() / 1000.0) << std::endl;
        std::cout << "  P95: " << (result.p95_duration.count() / 1000.0) << std::endl;
        std::cout << "  P99: " << (result.p99_duration.count() / 1000.0) << std::endl;
        std::cout << "  Max: " << (result.max_duration.count() / 1000.0) << std::endl;
        std::cout << "==============================" << std::endl;
    }

protected:
    std::unique_ptr<ErrorRecoveryEngine> recovery_engine_;
    std::shared_ptr<omop::monitoring::MetricsCollector> metrics_collector_;
    std::shared_ptr<common::Logger> logger_;
};

// Benchmark retry mechanism overhead
TEST_F(ErrorRecoveryBenchmark, RetryMechanismOverhead) {
    RetryPolicy policy;
    policy.max_attempts = 1; // No retries, measure overhead
    policy.base_delay = 0ms;
    
    std::atomic<int> call_count{0};
    
    auto operation = [&]() -> bool {
        call_count++;
        // Simple successful operation
        return true;
    };
    
    // Benchmark without retry mechanism
    auto baseline_result = measure_performance([&]() -> bool {
        return operation();
    }, 10000);
    
    call_count = 0;
    
    // Benchmark with retry mechanism (no actual retries)
    auto retry_result = measure_performance([&]() -> bool {
        auto result = recovery_engine_->with_retry(policy, [&]() -> int {
            operation();
            return 42;
        });
        return result.has_value();
    }, 10000);
    
    print_benchmark_result("Baseline Operation", baseline_result);
    print_benchmark_result("Retry Mechanism (No Retries)", retry_result);
    
    // Calculate overhead
    double overhead_percentage = ((retry_result.avg_duration.count() - baseline_result.avg_duration.count()) /
                                static_cast<double>(baseline_result.avg_duration.count())) * 100.0;
    
    std::cout << "Retry Mechanism Overhead: " << std::fixed << std::setprecision(2) 
              << overhead_percentage << "%" << std::endl;
    
    // Expect overhead to be minimal (< 20%)
    EXPECT_LT(overhead_percentage, 20.0);
    EXPECT_GT(retry_result.throughput_ops_per_second, 50000); // Should handle 50k+ ops/sec
}

// Benchmark retry mechanism with actual failures
TEST_F(ErrorRecoveryBenchmark, RetryMechanismWithFailures) {
    RetryPolicy policy;
    policy.max_attempts = 3;
    policy.base_delay = 1ms;
    policy.backoff_multiplier = 1.5;
    
    // Test different failure rates
    std::vector<double> failure_rates = {0.1, 0.3, 0.5, 0.7};
    
    for (double failure_rate : failure_rates) {
        std::mt19937 rng(std::random_device{}());
        std::uniform_real_distribution<double> dist(0.0, 1.0);
        std::atomic<int> attempt_count{0};
        
        auto operation = [&]() -> bool {
            attempt_count++;
            if (dist(rng) < failure_rate) {
                throw std::runtime_error("Simulated failure");
            }
            return true;
        };
        
        auto result = measure_performance([&]() -> bool {
            try {
                auto res = recovery_engine_->with_retry(policy, [&]() -> int {
                    operation();
                    return 42;
                });
                return res.has_value();
            } catch (const std::exception&) {
                return false;
            }
        }, 1000);
        
        print_benchmark_result("Retry with " + std::to_string(static_cast<int>(failure_rate * 100)) + "% failure rate", result);
        
        // Verify expected behavior
        EXPECT_GT(result.success_rate, 1.0 - failure_rate); // Should recover most failures
    }
}

// Benchmark circuit breaker performance
TEST_F(ErrorRecoveryBenchmark, CircuitBreakerPerformance) {
    CircuitBreakerPolicy policy;
    policy.failure_threshold = 5;
    policy.recovery_timeout = 100ms;
    policy.success_threshold = 2;
    
    std::atomic<int> operation_count{0};
    std::atomic<bool> should_fail{true};
    
    auto operation = [&]() -> bool {
        operation_count++;
        if (should_fail.load()) {
            throw std::runtime_error("Service failure");
        }
        return true;
    };
    
    // Phase 1: Circuit closed, operations failing
    should_fail = true;
    auto failing_result = measure_performance([&]() -> bool {
        try {
            auto result = recovery_engine_->with_circuit_breaker(policy, [&]() -> int {
                operation();
                return 42;
            });
            return result.has_value();
        } catch (const CircuitOpenException&) {
            return false; // Circuit open rejection
        } catch (const std::exception&) {
            return false; // Operation failure
        }
    }, 1000);
    
    print_benchmark_result("Circuit Breaker (Failing Operations)", failing_result);
    
    // Phase 2: Circuit open, fast rejections
    std::this_thread::sleep_for(10ms); // Let circuit open
    
    auto rejection_result = measure_performance([&]() -> bool {
        try {
            auto result = recovery_engine_->with_circuit_breaker(policy, [&]() -> int {
                operation();
                return 42;
            });
            return result.has_value();
        } catch (const CircuitOpenException&) {
            return false; // Circuit open rejection (expected)
        } catch (const std::exception&) {
            return false;
        }
    }, 1000);
    
    print_benchmark_result("Circuit Breaker (Fast Rejections)", rejection_result);
    
    // Circuit open should be much faster than operation failures
    EXPECT_LT(rejection_result.avg_duration.count(), failing_result.avg_duration.count() * 0.5);
    EXPECT_GT(rejection_result.throughput_ops_per_second, failing_result.throughput_ops_per_second * 2);
}

// Benchmark bulkhead isolation performance
TEST_F(ErrorRecoveryBenchmark, BulkheadIsolationPerformance) {
    BulkheadPolicy policy;
    policy.name = "test_bulkhead";
    policy.max_concurrent_calls = 10;
    policy.queue_size = 20;
    
    std::atomic<int> active_operations{0};
    std::atomic<int> max_concurrent{0};
    
    auto operation = [&]() -> bool {
        int current_active = active_operations.fetch_add(1) + 1;
        int current_max = max_concurrent.load();
        while (current_active > current_max && 
               !max_concurrent.compare_exchange_weak(current_max, current_active)) {}
        
        std::this_thread::sleep_for(10ms); // Simulate work
        
        active_operations--;
        return true;
    };
    
    // Test with bulkhead
    auto bulkhead_result = measure_performance([&]() -> bool {
        try {
            auto result = recovery_engine_->with_bulkhead(policy, [&]() -> int {
                operation();
                return 42;
            });
            return result.has_value();
        } catch (const ResourceException&) {
            return false; // Bulkhead rejection
        }
    }, 100, 20); // 20 concurrent threads
    
    print_benchmark_result("Bulkhead Isolation", bulkhead_result);
    
    // Verify bulkhead constraints were enforced
    EXPECT_LE(max_concurrent.load(), policy.max_concurrent_calls + 5); // Some tolerance
    EXPECT_GT(bulkhead_result.success_rate, 0.5); // At least 50% should succeed
    
    std::cout << "Max concurrent operations observed: " << max_concurrent.load() << std::endl;
}

// Benchmark fallback mechanism performance
TEST_F(ErrorRecoveryBenchmark, FallbackMechanismPerformance) {
    FallbackPolicy policy;
    policy.applicable_errors = {ErrorType::Persistent};
    
    std::atomic<int> primary_calls{0};
    std::atomic<int> fallback_calls{0};
    
    auto primary_operation = [&]() -> bool {
        primary_calls++;
        throw std::runtime_error("Primary always fails");
    };
    
    auto fallback_operation = [&]() -> bool {
        fallback_calls++;
        std::this_thread::sleep_for(1ms); // Fallback takes longer
        return true;
    };
    
    auto fallback_result = measure_performance([&]() -> bool {
        auto result = recovery_engine_->with_fallback(
            policy,
            [&]() -> int {
                primary_operation();
                return 42;
            },
            [&]() -> int {
                fallback_operation();
                return 84;
            }
        );
        return result.has_value();
    }, 1000);
    
    print_benchmark_result("Fallback Mechanism", fallback_result);
    
    // Verify fallback behavior
    EXPECT_EQ(primary_calls.load(), 1000); // All should try primary first
    EXPECT_EQ(fallback_calls.load(), 1000); // All should fallback
    EXPECT_GT(fallback_result.success_rate, 0.99); // Should succeed via fallback
}

// Benchmark composite recovery policies
TEST_F(ErrorRecoveryBenchmark, CompositeRecoveryPerformance) {
    // Setup composite policy with retry + circuit breaker + bulkhead
    CompositeRecoveryPolicy composite_policy;
    
    RetryPolicy retry_policy;
    retry_policy.max_attempts = 2;
    retry_policy.base_delay = 1ms;
    
    CircuitBreakerPolicy circuit_policy;
    circuit_policy.failure_threshold = 10;
    circuit_policy.recovery_timeout = 50ms;
    
    BulkheadPolicy bulkhead_policy;
    bulkhead_policy.name = "composite_test";
    bulkhead_policy.max_concurrent_calls = 5;
    bulkhead_policy.queue_size = 10;
    
    composite_policy.add_policy(std::make_unique<RetryPolicy>(retry_policy));
    composite_policy.add_policy(std::make_unique<CircuitBreakerPolicy>(circuit_policy));
    composite_policy.add_policy(std::make_unique<BulkheadPolicy>(bulkhead_policy));
    
    std::mt19937 rng(std::random_device{}());
    std::uniform_real_distribution<double> dist(0.0, 1.0);
    double failure_rate = 0.3;
    
    auto operation = [&]() -> bool {
        if (dist(rng) < failure_rate) {
            throw std::runtime_error("Random failure");
        }
        std::this_thread::sleep_for(5ms); // Simulate work
        return true;
    };
    
    auto composite_result = measure_performance([&]() -> bool {
        try {
            // Apply composite policy (simplified - in real implementation would be more integrated)
            auto result = recovery_engine_->with_retry(retry_policy, [&]() -> int {
                return recovery_engine_->with_circuit_breaker(circuit_policy, [&]() -> int {
                    return recovery_engine_->with_bulkhead(bulkhead_policy, [&]() -> int {
                        operation();
                        return 42;
                    }).value_or(0);
                }).value_or(0);
            });
            return result.has_value() && result.value() != 0;
        } catch (const std::exception&) {
            return false;
        }
    }, 500, 10); // 500 ops with 10 threads
    
    print_benchmark_result("Composite Recovery Policy", composite_result);
    
    // Verify reasonable performance despite multiple layers
    EXPECT_GT(composite_result.throughput_ops_per_second, 100); // Should handle 100+ ops/sec
    EXPECT_GT(composite_result.success_rate, 0.6); // Should recover most failures
}

// Benchmark error classification performance
TEST_F(ErrorRecoveryBenchmark, ErrorClassificationPerformance) {
    std::vector<std::unique_ptr<std::exception>> test_errors;
    test_errors.push_back(std::make_unique<NetworkException>("Network timeout"));
    test_errors.push_back(std::make_unique<DatabaseException>("Connection lost"));
    test_errors.push_back(std::make_unique<TimeoutException>("Operation timeout"));
    test_errors.push_back(std::make_unique<ResourceException>("Out of memory"));
    test_errors.push_back(std::make_unique<ConfigurationException>("Invalid config"));
    test_errors.push_back(std::make_unique<ValidationException>("Data validation failed"));
    test_errors.push_back(std::make_unique<SecurityException>("Access denied"));
    test_errors.push_back(std::make_unique<std::runtime_error>("Generic error"));
    
    std::mt19937 rng(std::random_device{}());
    std::uniform_int_distribution<size_t> error_dist(0, test_errors.size() - 1);
    
    auto classification_result = measure_performance([&]() -> bool {
        size_t error_index = error_dist(rng);
        const auto& error = test_errors[error_index];
        
        // Measure error classification performance
        ErrorType error_type = recovery_engine_->classify_error(*error);
        
        return error_type != ErrorType::Unknown;
    }, 10000);
    
    print_benchmark_result("Error Classification", classification_result);
    
    // Error classification should be very fast
    EXPECT_LT(classification_result.avg_duration.count(), 1000); // < 1μs average
    EXPECT_GT(classification_result.throughput_ops_per_second, 100000); // > 100k classifications/sec
}

// Benchmark statistics collection overhead
TEST_F(ErrorRecoveryBenchmark, StatisticsCollectionOverhead) {
    RetryPolicy policy_with_stats;
    policy_with_stats.max_attempts = 1;
    policy_with_stats.collect_statistics = true;
    
    RetryPolicy policy_without_stats;
    policy_without_stats.max_attempts = 1;
    policy_without_stats.collect_statistics = false;
    
    auto operation = [&]() -> bool {
        return true;
    };
    
    // Benchmark with statistics collection
    auto with_stats_result = measure_performance([&]() -> bool {
        auto result = recovery_engine_->with_retry(policy_with_stats, [&]() -> int {
            operation();
            return 42;
        });
        return result.has_value();
    }, 5000);
    
    // Benchmark without statistics collection
    auto without_stats_result = measure_performance([&]() -> bool {
        auto result = recovery_engine_->with_retry(policy_without_stats, [&]() -> int {
            operation();
            return 42;
        });
        return result.has_value();
    }, 5000);
    
    print_benchmark_result("With Statistics Collection", with_stats_result);
    print_benchmark_result("Without Statistics Collection", without_stats_result);
    
    // Statistics overhead should be minimal
    double overhead_percentage = ((with_stats_result.avg_duration.count() - without_stats_result.avg_duration.count()) /
                                static_cast<double>(without_stats_result.avg_duration.count())) * 100.0;
    
    std::cout << "Statistics Collection Overhead: " << std::fixed << std::setprecision(2) 
              << overhead_percentage << "%" << std::endl;
    
    EXPECT_LT(overhead_percentage, 10.0); // Should be less than 10% overhead
}

// Benchmark memory usage and allocation patterns
TEST_F(ErrorRecoveryBenchmark, MemoryUsageBenchmark) {
    // This test measures memory allocation patterns during recovery operations
    RetryPolicy policy;
    policy.max_attempts = 3;
    policy.base_delay = 1ms;
    
    size_t initial_memory = getCurrentMemoryUsage();
    
    // Perform many operations to test memory usage
    std::vector<std::future<void>> futures;
    std::atomic<int> completed_ops{0};
    
    for (int i = 0; i < 1000; ++i) {
        futures.emplace_back(std::async(std::launch::async, [&]() {
            try {
                auto result = recovery_engine_->with_retry(policy, [&]() -> std::string {
                    // Create some temporary objects
                    std::string data(100, 'x');
                    if (completed_ops.load() % 5 == 0) {
                        throw std::runtime_error("Temporary failure");
                    }
                    return data;
                });
                if (result.has_value()) {
                    completed_ops++;
                }
            } catch (const std::exception&) {
                // Failure after retries
            }
        }));
    }
    
    // Wait for completion
    for (auto& future : futures) {
        future.wait();
    }
    
    size_t final_memory = getCurrentMemoryUsage();
    size_t memory_growth = final_memory > initial_memory ? final_memory - initial_memory : 0;
    
    std::cout << "Memory Usage Benchmark Results:" << std::endl;
    std::cout << "Initial memory: " << initial_memory << " bytes" << std::endl;
    std::cout << "Final memory: " << final_memory << " bytes" << std::endl;
    std::cout << "Memory growth: " << memory_growth << " bytes" << std::endl;
    std::cout << "Memory per operation: " << (memory_growth / 1000.0) << " bytes" << std::endl;
    std::cout << "Completed operations: " << completed_ops.load() << std::endl;
    
    // Memory growth should be reasonable
    EXPECT_LT(memory_growth, 10 * 1024 * 1024); // Less than 10MB growth
    EXPECT_GT(completed_ops.load(), 700); // Most operations should succeed
}

private:
    size_t getCurrentMemoryUsage() {
        // Platform-specific memory usage measurement
#ifdef __linux__
        std::ifstream statm("/proc/self/statm");
        if (statm) {
            size_t size;
            statm >> size;
            return size * getpagesize();
        }
#elif defined(__APPLE__)
        mach_task_basic_info info;
        mach_msg_type_number_t info_count = MACH_TASK_BASIC_INFO_COUNT;
        if (task_info(mach_task_self(), MACH_TASK_BASIC_INFO, 
                      (task_info_t)&info, &info_count) == KERN_SUCCESS) {
            return info.resident_size;
        }
#endif
        return 0; // Fallback
    }

// Performance requirements validation
TEST_F(ErrorRecoveryBenchmark, PerformanceRequirementsValidation) {
    // Define performance SLA requirements
    struct PerformanceSLA {
        double min_throughput_ops_per_sec = 1000;   // Minimum throughput
        double max_avg_latency_us = 1000;           // Maximum average latency (1ms)
        double max_p99_latency_us = 5000;           // Maximum P99 latency (5ms)
        double max_overhead_percentage = 15;        // Maximum overhead vs baseline
        double min_success_rate = 0.95;             // Minimum success rate with retries
    };
    
    PerformanceSLA sla;
    
    // Test basic retry mechanism
    RetryPolicy policy;
    policy.max_attempts = 3;
    policy.base_delay = 1ms;
    
    std::mt19937 rng(std::random_device{}());
    std::uniform_real_distribution<double> dist(0.0, 1.0);
    
    auto result = measure_performance([&]() -> bool {
        try {
            auto res = recovery_engine_->with_retry(policy, [&]() -> int {
                if (dist(rng) < 0.3) { // 30% failure rate
                    throw std::runtime_error("Simulated failure");
                }
                return 42;
            });
            return res.has_value();
        } catch (const std::exception&) {
            return false;
        }
    }, 2000);
    
    print_benchmark_result("SLA Validation Test", result);
    
    // Validate against SLA requirements
    bool sla_passed = true;
    std::vector<std::string> failures;
    
    if (result.throughput_ops_per_second < sla.min_throughput_ops_per_sec) {
        sla_passed = false;
        failures.push_back("Throughput below minimum: " + 
            std::to_string(result.throughput_ops_per_second) + " < " + 
            std::to_string(sla.min_throughput_ops_per_sec));
    }
    
    double avg_latency_us = result.avg_duration.count() / 1000.0;
    if (avg_latency_us > sla.max_avg_latency_us) {
        sla_passed = false;
        failures.push_back("Average latency too high: " + 
            std::to_string(avg_latency_us) + "μs > " + 
            std::to_string(sla.max_avg_latency_us) + "μs");
    }
    
    double p99_latency_us = result.p99_duration.count() / 1000.0;
    if (p99_latency_us > sla.max_p99_latency_us) {
        sla_passed = false;
        failures.push_back("P99 latency too high: " + 
            std::to_string(p99_latency_us) + "μs > " + 
            std::to_string(sla.max_p99_latency_us) + "μs");
    }
    
    if (result.success_rate < sla.min_success_rate) {
        sla_passed = false;
        failures.push_back("Success rate too low: " + 
            std::to_string(result.success_rate) + " < " + 
            std::to_string(sla.min_success_rate));
    }
    
    std::cout << "\n=== SLA Validation Results ===" << std::endl;
    if (sla_passed) {
        std::cout << "✅ All SLA requirements PASSED" << std::endl;
    } else {
        std::cout << "❌ SLA requirements FAILED:" << std::endl;
        for (const auto& failure : failures) {
            std::cout << "  - " << failure << std::endl;
        }
    }
    std::cout << "==============================" << std::endl;
    
    EXPECT_TRUE(sla_passed) << "Performance SLA requirements not met";
}

} // End of test class

// Google Benchmark integration (if available)
#ifdef BENCHMARK_HAS_CXX11
static void BM_RetryMechanism(benchmark::State& state) {
    ErrorRecoveryEngine::Config config;
    auto recovery_engine = std::make_unique<ErrorRecoveryEngine>(config);
    
    RetryPolicy policy;
    policy.max_attempts = 3;
    policy.base_delay = std::chrono::milliseconds(1);
    
    std::mt19937 rng;
    std::uniform_real_distribution<double> dist(0.0, 1.0);
    double failure_rate = 0.3;
    
    for (auto _ : state) {
        try {
            auto result = recovery_engine->with_retry(policy, [&]() -> int {
                if (dist(rng) < failure_rate) {
                    throw std::runtime_error("Failure");
                }
                return 42;
            });
            benchmark::DoNotOptimize(result);
        } catch (const std::exception&) {
            // Failure after retries
        }
    }
    
    state.SetItemsProcessed(state.iterations());
}
BENCHMARK(BM_RetryMechanism);

static void BM_CircuitBreaker(benchmark::State& state) {
    ErrorRecoveryEngine::Config config;
    auto recovery_engine = std::make_unique<ErrorRecoveryEngine>(config);
    
    CircuitBreakerPolicy policy;
    policy.failure_threshold = 5;
    policy.recovery_timeout = std::chrono::milliseconds(100);
    
    for (auto _ : state) {
        try {
            auto result = recovery_engine->with_circuit_breaker(policy, [&]() -> int {
                return 42;
            });
            benchmark::DoNotOptimize(result);
        } catch (const std::exception&) {
            // Circuit open or other failure
        }
    }
    
    state.SetItemsProcessed(state.iterations());
}
BENCHMARK(BM_CircuitBreaker);

BENCHMARK_MAIN();
#endif