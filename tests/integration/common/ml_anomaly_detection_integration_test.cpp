/**
 * @file ml_anomaly_detection_integration_test.cpp
 * @brief Integration tests for ML-based anomaly detection systems
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#include <gtest/gtest.h>
#include <chrono>
#include <random>
#include <algorithm>
#include <memory>

#include "common/ml_anomaly_detectors.h"
#include "common/anomaly_detector.h"

namespace omop::test {

/**
 * @brief Integration tests for ML-based anomaly detection
 */
class MLAnomalyDetectionIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Generate synthetic time series data for testing
        generate_normal_time_series();
        generate_anomalous_time_series();
        generate_seasonal_time_series();
    }

    void TearDown() override {
        normal_series_.reset();
        anomalous_series_.reset();
        seasonal_series_.reset();
    }

    /**
     * @brief Generate normal time series data
     */
    void generate_normal_time_series() {
        normal_series_ = std::make_unique<omop::common::TimeSeries>("normal_metric", 2000);
        
        std::mt19937 gen(42);
        std::normal_distribution<double> normal_dist(100.0, 10.0);
        
        auto now = std::chrono::system_clock::now();
        
        for (int i = 0; i < 1000; ++i) {
            auto timestamp = now - std::chrono::minutes(1000 - i);
            double value = normal_dist(gen);
            
            omop::common::TimeSeriesPoint point(timestamp, value);
            normal_series_->add_point(point);
        }
    }

    /**
     * @brief Generate time series with anomalies
     */
    void generate_anomalous_time_series() {
        anomalous_series_ = std::make_unique<omop::common::TimeSeries>("anomalous_metric", 2000);
        
        std::mt19937 gen(123);
        std::normal_distribution<double> normal_dist(100.0, 10.0);
        std::uniform_real_distribution<double> anomaly_dist(200.0, 300.0);
        std::uniform_int_distribution<int> anomaly_trigger(1, 50);
        
        auto now = std::chrono::system_clock::now();
        
        for (int i = 0; i < 1000; ++i) {
            auto timestamp = now - std::chrono::minutes(1000 - i);
            
            // Inject anomalies randomly (about 2% of data)
            double value;
            if (anomaly_trigger(gen) == 1) {
                value = anomaly_dist(gen);  // Anomalous value
            } else {
                value = normal_dist(gen);   // Normal value
            }
            
            omop::common::TimeSeriesPoint point(timestamp, value);
            anomalous_series_->add_point(point);
        }
    }

    /**
     * @brief Generate seasonal time series data
     */
    void generate_seasonal_time_series() {
        seasonal_series_ = std::make_unique<omop::common::TimeSeries>("seasonal_metric", 2000);
        
        std::mt19937 gen(456);
        std::normal_distribution<double> noise_dist(0.0, 5.0);
        
        auto now = std::chrono::system_clock::now();
        
        for (int i = 0; i < 1000; ++i) {
            auto timestamp = now - std::chrono::minutes(1000 - i);
            
            // Create seasonal pattern (hourly cycle)
            double seasonal = 50.0 * std::sin(2 * M_PI * i / 60.0);  // 60-minute cycle
            double trend = 100.0 + 0.1 * i;  // Linear trend
            double noise = noise_dist(gen);
            
            double value = trend + seasonal + noise;
            
            omop::common::TimeSeriesPoint point(timestamp, value);
            seasonal_series_->add_point(point);
        }
    }

    std::unique_ptr<omop::common::TimeSeries> normal_series_;
    std::unique_ptr<omop::common::TimeSeries> anomalous_series_;
    std::unique_ptr<omop::common::TimeSeries> seasonal_series_;
};

/**
 * @brief Test Isolation Forest anomaly detection
 */
TEST_F(MLAnomalyDetectionIntegrationTest, IsolationForestDetection) {
    omop::common::ml::IsolationForestDetector::Config config;
    config.n_estimators = 50;
    config.contamination = 0.1;
    config.max_samples = 256;
    config.confidence_threshold = 0.5;
    
    omop::common::ml::IsolationForestDetector detector(config);
    
    // Train on normal data
    auto normal_points = normal_series_->get_latest_points(800);
    
    // Extract features for training (simplified)
    std::vector<std::vector<double>> training_features;
    for (size_t i = 0; i < normal_points.size(); ++i) {
        std::vector<double> feature_vector;
        feature_vector.push_back(normal_points[i].value);
        
        if (i > 0) {
            feature_vector.push_back(normal_points[i].value - normal_points[i-1].value);
        } else {
            feature_vector.push_back(0.0);
        }
        
        training_features.push_back(feature_vector);
    }
    
    ASSERT_TRUE(detector.train_model(training_features)) << "Isolation Forest training should succeed";
    ASSERT_TRUE(detector.is_trained()) << "Model should be trained";
    
    // Test detection on normal data (should find few anomalies)
    auto normal_anomalies = detector.detect_anomalies(*normal_series_, 1.0);
    
    // Test detection on anomalous data (should find more anomalies)
    auto anomalous_anomalies = detector.detect_anomalies(*anomalous_series_, 1.0);
    
    // Validation
    EXPECT_LT(normal_anomalies.size(), 20) << "Should find few anomalies in normal data";
    EXPECT_GT(anomalous_anomalies.size(), normal_anomalies.size()) 
        << "Should find more anomalies in anomalous data";
    
    // Check anomaly properties
    for (const auto& anomaly : anomalous_anomalies) {
        EXPECT_EQ(anomaly.detection_algorithm, omop::common::AnomalyDetectionAlgorithm::ISOLATION_FOREST);
        EXPECT_GE(anomaly.confidence_score, config.confidence_threshold);
        EXPECT_FALSE(anomaly.id.empty());
        EXPECT_EQ(anomaly.metric_name, "anomalous_metric");
    }
    
    // Check model statistics
    auto stats = detector.get_model_stats();
    EXPECT_EQ(stats.n_trees, config.n_estimators);
    EXPECT_GT(stats.training_samples, 0);
    EXPECT_NEAR(stats.contamination_rate, config.contamination, 0.01);
}

/**
 * @brief Test LSTM Autoencoder anomaly detection
 */
TEST_F(MLAnomalyDetectionIntegrationTest, LSTMAutoencoderDetection) {
    omop::common::ml::LSTMAutoencoderDetector::Config config;
    config.sequence_length = 20;
    config.encoding_dim = 16;
    config.epochs = 10;  // Reduced for testing
    config.min_training_samples = 100;
    config.confidence_threshold = 0.6;
    
    omop::common::ml::LSTMAutoencoderDetector detector(config);
    
    // Extract training data from normal series
    auto normal_points = normal_series_->get_latest_points(500);
    std::vector<double> training_values;
    for (const auto& point : normal_points) {
        training_values.push_back(point.value);
    }
    
    ASSERT_TRUE(detector.train_model(training_values)) << "LSTM Autoencoder training should succeed";
    ASSERT_TRUE(detector.is_trained()) << "Model should be trained";
    
    // Test detection on normal data (should find few anomalies)
    auto normal_anomalies = detector.detect_anomalies(*normal_series_, 0.5);
    
    // Test detection on anomalous data (should find more anomalies)
    auto anomalous_anomalies = detector.detect_anomalies(*anomalous_series_, 0.5);
    
    // Validation (note: simplified LSTM implementation may not perform perfectly)
    EXPECT_LT(normal_anomalies.size(), 50) << "Should find relatively few anomalies in normal data";
    
    // Check anomaly properties
    for (const auto& anomaly : anomalous_anomalies) {
        EXPECT_EQ(anomaly.detection_algorithm, omop::common::AnomalyDetectionAlgorithm::LSTM_AUTOENCODER);
        EXPECT_GE(anomaly.confidence_score, config.confidence_threshold);
        EXPECT_FALSE(anomaly.id.empty());
        EXPECT_EQ(anomaly.metric_name, "anomalous_metric");
        EXPECT_TRUE(anomaly.metadata.count("reconstruction_error") > 0);
    }
    
    // Check model statistics
    auto stats = detector.get_model_stats();
    EXPECT_EQ(stats.sequence_length, config.sequence_length);
    EXPECT_EQ(stats.encoding_dim, config.encoding_dim);
    EXPECT_GT(stats.training_samples, 0);
    EXPECT_GT(stats.reconstruction_threshold, 0.0);
}

/**
 * @brief Test Advanced ML Ensemble anomaly detection
 */
TEST_F(MLAnomalyDetectionIntegrationTest, AdvancedMLEnsembleDetection) {
    omop::common::ml::AdvancedMLAnomalyDetector::Config config;
    config.enable_isolation_forest = true;
    config.enable_lstm_autoencoder = true;
    config.ensemble_weight_isolation = 0.6;
    config.ensemble_weight_lstm = 0.4;
    config.consensus_threshold = 0.5;
    
    // Configure individual detectors
    config.isolation_config.n_estimators = 30;
    config.isolation_config.contamination = 0.05;
    config.lstm_config.sequence_length = 15;
    config.lstm_config.epochs = 5;
    config.lstm_config.min_training_samples = 100;
    
    omop::common::ml::AdvancedMLAnomalyDetector ensemble_detector(config);
    
    // Prepare training data
    auto training_points = normal_series_->get_latest_points(600);
    
    ASSERT_TRUE(ensemble_detector.train_models(training_points)) 
        << "ML Ensemble training should succeed";
    ASSERT_TRUE(ensemble_detector.is_trained()) << "Ensemble should be trained";
    
    // Test detection on different data types
    auto normal_ensemble_anomalies = ensemble_detector.detect_anomalies(*normal_series_, 1.0);
    auto anomalous_ensemble_anomalies = ensemble_detector.detect_anomalies(*anomalous_series_, 1.0);
    auto seasonal_ensemble_anomalies = ensemble_detector.detect_anomalies(*seasonal_series_, 1.0);
    
    // Validation
    EXPECT_LT(normal_ensemble_anomalies.size(), 30) 
        << "Ensemble should find few anomalies in normal data";
    
    // Check ensemble anomaly properties
    for (const auto& anomaly : anomalous_ensemble_anomalies) {
        EXPECT_EQ(anomaly.detection_algorithm, omop::common::AnomalyDetectionAlgorithm::ENSEMBLE);
        EXPECT_FALSE(anomaly.id.empty());
        EXPECT_TRUE(anomaly.metadata.count("ensemble_consensus") > 0);
        EXPECT_TRUE(anomaly.metadata.count("ensemble_confidence") > 0);
    }
    
    // Check ensemble statistics
    auto stats = ensemble_detector.get_ensemble_stats();
    EXPECT_TRUE(stats.isolation_forest_trained);
    EXPECT_TRUE(stats.lstm_autoencoder_trained);
    EXPECT_GT(stats.total_parameters, 0);
}

/**
 * @brief Test ML anomaly detection performance and scalability
 */
TEST_F(MLAnomalyDetectionIntegrationTest, MLDetectionPerformance) {
    // Generate large dataset for performance testing
    auto large_series = std::make_unique<omop::common::TimeSeries>("performance_test", 5000);
    
    std::mt19937 gen(789);
    std::normal_distribution<double> dist(150.0, 25.0);
    
    auto now = std::chrono::system_clock::now();
    
    // Generate 3000 data points
    for (int i = 0; i < 3000; ++i) {
        auto timestamp = now - std::chrono::minutes(3000 - i);
        double value = dist(gen);
        
        // Add some anomalies
        if (i % 100 == 0) {
            value += 100.0;  // Spike anomaly
        }
        
        omop::common::TimeSeriesPoint point(timestamp, value);
        large_series->add_point(point);
    }
    
    // Test Isolation Forest performance
    omop::common::ml::IsolationForestDetector::Config iso_config;
    iso_config.n_estimators = 100;
    iso_config.max_samples = 512;
    iso_config.contamination = 0.02;
    
    omop::common::ml::IsolationForestDetector iso_detector(iso_config);
    
    auto training_points = large_series->get_latest_points(2000);
    std::vector<std::vector<double>> features;
    for (const auto& point : training_points) {
        features.push_back({point.value, (features.empty() ? 0.0 : point.value - features.back()[0])});
    }
    
    // Measure training time
    auto train_start = std::chrono::high_resolution_clock::now();
    ASSERT_TRUE(iso_detector.train_model(features));
    auto train_end = std::chrono::high_resolution_clock::now();
    
    auto training_duration = std::chrono::duration_cast<std::chrono::milliseconds>(train_end - train_start);
    
    // Measure detection time
    auto detect_start = std::chrono::high_resolution_clock::now();
    auto anomalies = iso_detector.detect_anomalies(*large_series, 2.0);
    auto detect_end = std::chrono::high_resolution_clock::now();
    
    auto detection_duration = std::chrono::duration_cast<std::chrono::milliseconds>(detect_end - detect_start);
    
    // Performance validation
    EXPECT_LT(training_duration.count(), 10000) << "Training should complete within 10 seconds";
    EXPECT_LT(detection_duration.count(), 5000) << "Detection should complete within 5 seconds";
    EXPECT_GT(anomalies.size(), 5) << "Should detect some anomalies in large dataset";
    EXPECT_LT(anomalies.size(), 150) << "Should not have too many false positives";
    
    std::cout << "\n=== ML Anomaly Detection Performance Results ===" << std::endl;
    std::cout << "Training time: " << training_duration.count() << " ms" << std::endl;
    std::cout << "Detection time: " << detection_duration.count() << " ms" << std::endl;
    std::cout << "Data points processed: " << training_points.size() << std::endl;
    std::cout << "Anomalies detected: " << anomalies.size() << std::endl;
    std::cout << "Throughput: " << (training_points.size() * 1000.0 / detection_duration.count()) 
              << " points/second" << std::endl;
}

/**
 * @brief Test ML model retraining and adaptation
 */
TEST_F(MLAnomalyDetectionIntegrationTest, MLModelRetraining) {
    omop::common::ml::AdvancedMLAnomalyDetector::Config config;
    config.auto_retrain = true;
    config.retrain_interval = std::chrono::hours(1);  // Short interval for testing
    config.min_retrain_samples = 200;
    
    omop::common::ml::AdvancedMLAnomalyDetector detector(config);
    
    // Initial training
    auto initial_training_data = normal_series_->get_latest_points(500);
    ASSERT_TRUE(detector.train_models(initial_training_data));
    
    // Get baseline performance
    auto baseline_anomalies = detector.detect_anomalies(*anomalous_series_, 1.0);
    
    // Simulate data drift by creating new training data with different characteristics
    auto drift_series = std::make_unique<omop::common::TimeSeries>("drift_metric", 1000);
    
    std::mt19937 gen(999);
    std::normal_distribution<double> drift_dist(120.0, 15.0);  // Different mean and std
    
    auto now = std::chrono::system_clock::now();
    for (int i = 0; i < 400; ++i) {
        auto timestamp = now - std::chrono::minutes(400 - i);
        double value = drift_dist(gen);
        
        omop::common::TimeSeriesPoint point(timestamp, value);
        drift_series->add_point(point);
    }
    
    auto drift_training_data = drift_series->get_latest_points(350);
    
    // Retrain with new data
    ASSERT_TRUE(detector.retrain_models(drift_training_data)) 
        << "Model retraining should succeed";
    
    // Test that retrained model works
    auto post_retrain_anomalies = detector.detect_anomalies(*anomalous_series_, 1.0);
    
    // Validate retraining
    EXPECT_TRUE(detector.is_trained()) << "Model should remain trained after retraining";
    
    // Check that retraining updated model statistics
    auto stats = detector.get_ensemble_stats();
    EXPECT_GT(std::chrono::duration_cast<std::chrono::seconds>(
                  std::chrono::system_clock::now() - stats.last_training_time).count(), 0)
        << "Last training time should be recent";
}

/**
 * @brief Test ML anomaly detection with different data patterns
 */
TEST_F(MLAnomalyDetectionIntegrationTest, MLDetectionDataPatterns) {
    omop::common::ml::IsolationForestDetector detector;
    
    // Test with seasonal data
    auto seasonal_points = seasonal_series_->get_latest_points(800);
    std::vector<std::vector<double>> seasonal_features;
    
    for (size_t i = 0; i < seasonal_points.size(); ++i) {
        std::vector<double> feature;
        feature.push_back(seasonal_points[i].value);
        
        // Add seasonal component as feature
        double seasonal_component = std::sin(2 * M_PI * i / 60.0);
        feature.push_back(seasonal_component);
        
        seasonal_features.push_back(feature);
    }
    
    ASSERT_TRUE(detector.train_model(seasonal_features));
    
    // Test detection on seasonal data
    auto seasonal_anomalies = detector.detect_anomalies(*seasonal_series_, 2.0);
    
    // Create spike pattern data
    auto spike_series = std::make_unique<omop::common::TimeSeries>("spike_metric", 1000);
    
    std::mt19937 gen(111);
    std::normal_distribution<double> base_dist(100.0, 5.0);
    
    auto now = std::chrono::system_clock::now();
    for (int i = 0; i < 300; ++i) {
        auto timestamp = now - std::chrono::minutes(300 - i);
        
        double value = base_dist(gen);
        
        // Add periodic spikes
        if (i % 30 == 0) {
            value += 50.0;  // Spike
        }
        
        omop::common::TimeSeriesPoint point(timestamp, value);
        spike_series->add_point(point);
    }
    
    auto spike_anomalies = detector.detect_anomalies(*spike_series, 1.0);
    
    // Validation
    EXPECT_GT(spike_anomalies.size(), 3) << "Should detect spike anomalies";
    EXPECT_LT(seasonal_anomalies.size(), spike_anomalies.size() * 2) 
        << "Seasonal data should have fewer anomalies than spike data";
    
    // Check anomaly severity distribution
    std::map<omop::common::AnomalySeverity, size_t> severity_counts;
    for (const auto& anomaly : spike_anomalies) {
        severity_counts[anomaly.severity]++;
    }
    
    EXPECT_GT(severity_counts[omop::common::AnomalySeverity::HIGH] + 
              severity_counts[omop::common::AnomalySeverity::CRITICAL], 0)
        << "Should detect some high-severity anomalies in spike data";
}

/**
 * @brief Integration test with real-world-like healthcare metrics
 */
TEST_F(MLAnomalyDetectionIntegrationTest, HealthcareMetricsPatterns) {
    // Simulate patient vitals monitoring data
    auto vitals_series = std::make_unique<omop::common::TimeSeries>("patient_heart_rate", 2000);
    
    std::mt19937 gen(222);
    std::normal_distribution<double> heart_rate_dist(75.0, 8.0);  // Normal heart rate
    std::uniform_real_distribution<double> anomaly_prob(0.0, 1.0);
    
    auto now = std::chrono::system_clock::now();
    
    for (int i = 0; i < 1440; ++i) {  // 24 hours of minute-by-minute data
        auto timestamp = now - std::chrono::minutes(1440 - i);
        
        double heart_rate = heart_rate_dist(gen);
        
        // Simulate medical events (arrhythmia, tachycardia)
        if (anomaly_prob(gen) < 0.005) {  // 0.5% chance of arrhythmia
            if (anomaly_prob(gen) < 0.5) {
                heart_rate = 45.0 + (anomaly_prob(gen) * 15.0);  // Bradycardia
            } else {
                heart_rate = 120.0 + (anomaly_prob(gen) * 60.0);  // Tachycardia
            }
        }
        
        // Add circadian rhythm
        double circadian = 5.0 * std::sin(2 * M_PI * i / 1440.0);  // 24-hour cycle
        heart_rate += circadian;
        
        omop::common::TimeSeriesPoint point(timestamp, heart_rate);
        vitals_series->add_point(point);
    }
    
    // Train ML detector on normal patterns
    omop::common::ml::AdvancedMLAnomalyDetector healthcare_detector;
    
    auto training_data = vitals_series->get_latest_points(1000);
    ASSERT_TRUE(healthcare_detector.train_models(training_data));
    
    // Detect anomalies
    auto healthcare_anomalies = healthcare_detector.detect_anomalies(*vitals_series, 4.0);
    
    // Validate healthcare-specific patterns
    EXPECT_GT(healthcare_anomalies.size(), 2) << "Should detect some medical anomalies";
    EXPECT_LT(healthcare_anomalies.size(), 50) << "Should not over-detect in healthcare data";
    
    // Check for critical severity anomalies (medical emergencies)
    size_t critical_count = 0;
    for (const auto& anomaly : healthcare_anomalies) {
        if (anomaly.severity == omop::common::AnomalySeverity::CRITICAL) {
            critical_count++;
            
            // Critical heart rate anomalies should be extreme values
            EXPECT_TRUE(anomaly.observed_value < 50.0 || anomaly.observed_value > 150.0)
                << "Critical heart rate anomalies should be extreme values";
        }
    }
    
    std::cout << "\n=== Healthcare Metrics ML Detection Results ===" << std::endl;
    std::cout << "Total anomalies detected: " << healthcare_anomalies.size() << std::endl;
    std::cout << "Critical anomalies: " << critical_count << std::endl;
    std::cout << "Data points analyzed: " << training_data.size() << std::endl;
    std::cout << "Anomaly rate: " << (healthcare_anomalies.size() * 100.0 / training_data.size()) 
              << "%" << std::endl;
}

} // namespace omop::test