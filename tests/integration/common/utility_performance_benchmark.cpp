/**
 * @file utility_performance_benchmark.cpp
 * @brief Performance benchmarks for optimized utility functions
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-25
 */

#include <gtest/gtest.h>
#include <chrono>
#include <vector>
#include <random>
#include <string>
#include <algorithm>
#include <thread>
#include <future>
#include <functional>
#include <any>

#include "common/utilities.h"
#include "common/validation.h"

namespace omop::test {

/**
 * @brief High-throughput utility performance benchmarks
 */
class UtilityPerformanceBenchmark : public ::testing::Test {
protected:
    void SetUp() override {
        // Generate test data for benchmarks
        generate_test_strings(10000);
        generate_test_any_values(10000);
        generate_test_validation_data(10000);
    }

    void TearDown() override {
        test_strings_.clear();
        test_any_values_.clear();
        test_validation_data_.clear();
    }

    struct BenchmarkResult {
        std::chrono::nanoseconds duration;
        size_t operations_count;
        double throughput_ops_per_second;
        size_t memory_used_bytes;
        
        BenchmarkResult(std::chrono::nanoseconds dur, size_t ops, size_t mem = 0)
            : duration(dur), operations_count(ops), memory_used_bytes(mem) {
            throughput_ops_per_second = static_cast<double>(ops) / 
                                      (static_cast<double>(dur.count()) / 1e9);
        }
    };

    BenchmarkResult measure_operation(std::function<void()> operation, size_t operation_count) {
        auto memory_before = get_memory_usage();
        auto start = std::chrono::high_resolution_clock::now();
        
        operation();
        
        auto end = std::chrono::high_resolution_clock::now();
        auto memory_after = get_memory_usage();
        
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
        size_t memory_used = (memory_after > memory_before) ? 
                           (memory_after - memory_before) : 0;
        
        return BenchmarkResult(duration, operation_count, memory_used);
    }

    void print_benchmark_result(const std::string& test_name, const BenchmarkResult& result) {
        std::cout << "\n=== " << test_name << " ===" << std::endl;
        std::cout << "Duration: " << result.duration.count() / 1e6 << " ms" << std::endl;
        std::cout << "Operations: " << result.operations_count << std::endl;
        std::cout << "Throughput: " << static_cast<size_t>(result.throughput_ops_per_second) 
                  << " ops/sec" << std::endl;
        std::cout << "Memory used: " << result.memory_used_bytes / 1024 << " KB" << std::endl;
        
        // Performance SLA validation
        double min_throughput = 1000.0;  // Minimum 1000 ops/sec
        bool meets_sla = result.throughput_ops_per_second >= min_throughput;
        std::cout << "SLA Status: " << (meets_sla ? "PASS" : "FAIL") 
                  << " (min " << min_throughput << " ops/sec)" << std::endl;
        
        EXPECT_GT(result.throughput_ops_per_second, min_throughput) 
            << test_name << " performance below SLA threshold";
    }

    void generate_test_strings(size_t count) {
        std::mt19937 gen(42); // Fixed seed for reproducibility
        std::uniform_int_distribution<> length_dist(10, 100);
        std::uniform_int_distribution<> char_dist(33, 126); // Printable ASCII
        
        test_strings_.reserve(count);
        
        for (size_t i = 0; i < count; ++i) {
            size_t length = length_dist(gen);
            std::string str;
            str.reserve(length);
            
            for (size_t j = 0; j < length; ++j) {
                str += static_cast<char>(char_dist(gen));
            }
            
            test_strings_.push_back(std::move(str));
        }
    }

    void generate_test_any_values(size_t count) {
        std::mt19937 gen(42);
        std::uniform_int_distribution<> type_dist(0, 6);
        
        test_any_values_.reserve(count);
        
        for (size_t i = 0; i < count; ++i) {
            int type = type_dist(gen);
            
            switch (type) {
                case 0:
                    test_any_values_.emplace_back(static_cast<int>(i));
                    break;
                case 1:
                    test_any_values_.emplace_back(static_cast<double>(i * 1.5));
                    break;
                case 2:
                    test_any_values_.emplace_back(std::string("test_value_") + std::to_string(i));
                    break;
                case 3:
                    test_any_values_.emplace_back(i % 2 == 0);
                    break;
                case 4:
                    test_any_values_.emplace_back(static_cast<long>(i * 1000));
                    break;
                case 5:
                    test_any_values_.emplace_back(static_cast<float>(i * 0.5f));
                    break;
                default:
                    test_any_values_.emplace_back(static_cast<char>('A' + (i % 26)));
                    break;
            }
        }
    }

    void generate_test_validation_data(size_t count) {
        std::mt19937 gen(42);
        std::uniform_int_distribution<> type_dist(0, 2);
        
        test_validation_data_.reserve(count);
        
        for (size_t i = 0; i < count; ++i) {
            int type = type_dist(gen);
            
            switch (type) {
                case 0: // Valid email
                    test_validation_data_.push_back("test" + std::to_string(i) + "@example.com");
                    break;
                case 1: // Valid UK postcode
                    test_validation_data_.push_back("M1 1AA");
                    break;
                default: // Mixed valid/invalid data
                    test_validation_data_.push_back(i % 3 == 0 ? "invalid_data" : "valid_data_" + std::to_string(i));
                    break;
            }
        }
    }

    size_t get_memory_usage() {
        // Simplified memory usage measurement
        // In production, use platform-specific memory measurement
        return 0; // Placeholder
    }

    std::vector<std::string> test_strings_;
    std::vector<std::any> test_any_values_;
    std::vector<std::string> test_validation_data_;
};

/**
 * @brief Benchmark any_to_string conversion performance
 */
TEST_F(UtilityPerformanceBenchmark, AnyToStringConversionPerformance) {
    auto result = measure_operation([this]() {
        for (const auto& value : test_any_values_) {
            volatile auto str = omop::common::any_to_string(value);
            (void)str; // Prevent optimization
        }
    }, test_any_values_.size());
    
    print_benchmark_result("AnyToString Conversion", result);
    
    // Verify correctness
    std::string first_result = omop::common::any_to_string(test_any_values_[0]);
    EXPECT_FALSE(first_result.empty()) << "any_to_string should produce non-empty result";
}

/**
 * @brief Benchmark bulk any_to_string conversion performance
 */
TEST_F(UtilityPerformanceBenchmark, BulkAnyToStringPerformance) {
    auto result = measure_operation([this]() {
        volatile auto results = omop::common::OptimizationUtils::bulk_any_to_string(test_any_values_);
        (void)results; // Prevent optimization
    }, test_any_values_.size());
    
    print_benchmark_result("Bulk AnyToString Conversion", result);
    
    // Verify correctness
    auto bulk_results = omop::common::OptimizationUtils::bulk_any_to_string(test_any_values_);
    EXPECT_EQ(bulk_results.size(), test_any_values_.size()) << "Bulk conversion should preserve count";
}

/**
 * @brief Benchmark string validation performance
 */
TEST_F(UtilityPerformanceBenchmark, StringValidationPerformance) {
    auto email_validator = [](const std::string& str) {
        return omop::common::ValidationUtils::is_valid_email(str);
    };
    
    auto result = measure_operation([&]() {
        for (const auto& str : test_validation_data_) {
            volatile bool valid = email_validator(str);
            (void)valid; // Prevent optimization
        }
    }, test_validation_data_.size());
    
    print_benchmark_result("String Validation", result);
    
    // Verify correctness
    bool first_result = email_validator(test_validation_data_[0]);
    // Result depends on generated data, just ensure it's computable
    (void)first_result;
}

/**
 * @brief Benchmark bulk string validation performance
 */
TEST_F(UtilityPerformanceBenchmark, BulkStringValidationPerformance) {
    auto email_validator = [](const std::string& str) {
        return omop::common::ValidationUtils::is_valid_email(str);
    };
    
    auto result = measure_operation([&]() {
        volatile auto results = omop::common::OptimizationUtils::bulk_validate<std::string>(
            test_validation_data_, email_validator, 4);
        (void)results; // Prevent optimization
    }, test_validation_data_.size());
    
    print_benchmark_result("Bulk String Validation", result);
    
    // Verify correctness
    auto bulk_results = omop::common::OptimizationUtils::bulk_validate<std::string>(
        test_validation_data_, email_validator, 4);
    EXPECT_EQ(bulk_results.size(), test_validation_data_.size()) 
        << "Bulk validation should preserve count";
}

/**
 * @brief Benchmark string processing performance
 */
TEST_F(UtilityPerformanceBenchmark, StringProcessingPerformance) {
    auto string_processor = [](const std::string& str) {
        return omop::common::string_utils::to_upper(omop::common::string_utils::trim(str));
    };
    
    auto result = measure_operation([&]() {
        for (const auto& str : test_strings_) {
            volatile auto processed = string_processor(str);
            (void)processed; // Prevent optimization
        }
    }, test_strings_.size());
    
    print_benchmark_result("String Processing", result);
    
    // Verify correctness
    std::string first_result = string_processor(test_strings_[0]);
    EXPECT_FALSE(first_result.empty()) << "String processing should produce non-empty result";
}

/**
 * @brief Benchmark batch string processing performance
 */
TEST_F(UtilityPerformanceBenchmark, BatchStringProcessingPerformance) {
    auto string_processor = [](const std::string& str) {
        return omop::common::string_utils::to_upper(omop::common::string_utils::trim(str));
    };
    
    auto result = measure_operation([&]() {
        volatile auto results = omop::common::OptimizationUtils::batch_string_processing(
            test_strings_, string_processor, 4);
        (void)results; // Prevent optimization
    }, test_strings_.size());
    
    print_benchmark_result("Batch String Processing", result);
    
    // Verify correctness
    auto batch_results = omop::common::OptimizationUtils::batch_string_processing(
        test_strings_, string_processor, 4);
    EXPECT_EQ(batch_results.size(), test_strings_.size()) 
        << "Batch processing should preserve count";
}

/**
 * @brief Benchmark string pool performance
 */
TEST_F(UtilityPerformanceBenchmark, StringPoolPerformance) {
    const size_t pool_operations = 1000;
    
    auto result = measure_operation([&]() {
        auto& pool = omop::common::OptimizationUtils::get_string_pool();
        
        std::vector<std::string*> acquired_strings;
        acquired_strings.reserve(pool_operations);
        
        // Acquire strings
        for (size_t i = 0; i < pool_operations; ++i) {
            std::string* str = pool.acquire();
            *str = "test_string_" + std::to_string(i);
            acquired_strings.push_back(str);
        }
        
        // Release strings
        for (std::string* str : acquired_strings) {
            pool.release(str);
        }
    }, pool_operations * 2); // Count both acquire and release operations
    
    print_benchmark_result("String Pool Operations", result);
}

/**
 * @brief Comprehensive utility performance comparison test
 */
TEST_F(UtilityPerformanceBenchmark, ComprehensivePerformanceComparison) {
    struct TestScenario {
        std::string name;
        std::function<void()> operation;
        size_t operation_count;
        double expected_min_throughput;
    };
    
    std::vector<TestScenario> scenarios = {
        {
            "Any to String Sequential",
            [this]() {
                for (const auto& value : test_any_values_) {
                    volatile auto str = omop::common::any_to_string(value);
                    (void)str;
                }
            },
            test_any_values_.size(),
            5000.0
        },
        {
            "Any to String Bulk",
            [this]() {
                volatile auto results = omop::common::OptimizationUtils::bulk_any_to_string(test_any_values_);
                (void)results;
            },
            test_any_values_.size(),
            8000.0  // Expected higher throughput for bulk operations
        },
        {
            "String Validation Sequential",
            [this]() {
                for (const auto& str : test_validation_data_) {
                    volatile bool valid = omop::common::ValidationUtils::is_valid_email(str);
                    (void)valid;
                }
            },
            test_validation_data_.size(),
            2000.0
        },
        {
            "String Validation Bulk",
            [this]() {
                auto validator = [](const std::string& str) {
                    return omop::common::ValidationUtils::is_valid_email(str);
                };
                volatile auto results = omop::common::OptimizationUtils::bulk_validate<std::string>(
                    test_validation_data_, validator, 4);
                (void)results;
            },
            test_validation_data_.size(),
            4000.0  // Expected higher throughput for parallel operations
        }
    };
    
    std::cout << "\n=== COMPREHENSIVE PERFORMANCE COMPARISON ===" << std::endl;
    std::cout << "Scenario\t\t\tThroughput (ops/sec)\tSLA Status\tMemory (KB)" << std::endl;
    std::cout << "--------\t\t\t--------------------\t----------\t-----------" << std::endl;
    
    bool all_pass = true;
    
    for (const auto& scenario : scenarios) {
        auto result = measure_operation(scenario.operation, scenario.operation_count);
        
        bool meets_sla = result.throughput_ops_per_second >= scenario.expected_min_throughput;
        all_pass &= meets_sla;
        
        std::cout << scenario.name << "\t\t"
                  << static_cast<size_t>(result.throughput_ops_per_second) << "\t\t\t"
                  << (meets_sla ? "PASS" : "FAIL") << "\t\t"
                  << result.memory_used_bytes / 1024 << std::endl;
    }
    
    EXPECT_TRUE(all_pass) << "One or more performance scenarios failed to meet SLA requirements";
    
    std::cout << "\nOverall Performance Status: " << (all_pass ? "PASS" : "FAIL") << std::endl;
}

/**
 * @brief Scalability test for different data sizes
 */
TEST_F(UtilityPerformanceBenchmark, ScalabilityTest) {
    std::vector<size_t> data_sizes = {100, 1000, 5000, 10000};
    
    std::cout << "\n=== SCALABILITY TEST ===" << std::endl;
    std::cout << "Data Size\tThroughput (ops/sec)\tLinear Scale Factor" << std::endl;
    std::cout << "---------\t--------------------\t-------------------" << std::endl;
    
    double baseline_throughput = 0.0;
    
    for (size_t data_size : data_sizes) {
        // Create subset of test data
        std::vector<std::any> subset_data(test_any_values_.begin(), 
                                        test_any_values_.begin() + std::min(data_size, test_any_values_.size()));
        
        auto result = measure_operation([&]() {
            volatile auto results = omop::common::OptimizationUtils::bulk_any_to_string(subset_data);
            (void)results;
        }, subset_data.size());
        
        if (baseline_throughput == 0.0) {
            baseline_throughput = result.throughput_ops_per_second;
        }
        
        double scale_factor = result.throughput_ops_per_second / baseline_throughput;
        
        std::cout << data_size << "\t\t"
                  << static_cast<size_t>(result.throughput_ops_per_second) << "\t\t\t"
                  << std::fixed << std::setprecision(2) << scale_factor << std::endl;
        
        // Ensure throughput doesn't degrade significantly as data size increases
        EXPECT_GT(scale_factor, 0.5) << "Performance degradation too severe at data size " << data_size;
    }
}

} // namespace omop::test