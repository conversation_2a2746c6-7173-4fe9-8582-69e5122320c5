/**
 * @file performance_benchmarking_test.cpp
 * @brief Performance benchmarking tests for common library components
 * <AUTHOR> Cancer Data Engineering
 * @date 2025-08-24
 * 
 * This file implements comprehensive performance benchmarking for Priority 1
 * components identified in the gap analysis report.
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "common/configuration.h"
#include "common/logging.h"
#include "common/validation.h"
#include "common/performance_monitor.h"
#include "common/metrics_collector.h"
#include "common/utilities.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>
#include <atomic>
#include <future>
#include <vector>
#include <random>
#include <memory>

using namespace omop::common;
using namespace std::chrono;

namespace omop::common::benchmarks {

/**
 * @brief Performance benchmark test fixture
 */
class PerformanceBenchmarkTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_performance_bench";
        std::filesystem::create_directories(test_dir_);
        
        monitor_ = std::make_unique<PerformanceMonitor>();
        
        // Performance targets from design specifications
        config_loading_target_ms_ = 100.0;
        log_throughput_target_msgs_per_sec_ = 100000.0;
        log_latency_target_us_ = 1.0;
        validation_target_ms_ = 10.0;
        memory_target_mb_ = 10.0;
        
        setupPerformanceConfiguration();
    }

    void TearDown() override {
        if (std::filesystem::exists(test_dir_)) {
            std::filesystem::remove_all(test_dir_);
        }
        Logger::shutdown();
    }

    void setupPerformanceConfiguration() {
        // Create test configuration files of varying complexity
        createSimpleConfigFile();
        createMediumConfigFile();
        createComplexConfigFile();
    }

    void createSimpleConfigFile() {
        simple_config_file_ = test_dir_ / "simple_config.yaml";
        std::ofstream stream(simple_config_file_);
        stream << R"(
database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_db
  username: test_user
  password: test_pass
)";
    }

    void createMediumConfigFile() {
        medium_config_file_ = test_dir_ / "medium_config.yaml";
        std::ofstream stream(medium_config_file_);
        stream << R"(
database:
  source:
    type: postgresql
    host: source-host
    port: 5432
    database: source_db
    username: source_user
    password: source_pass
    connection_pool:
      min_connections: 5
      max_connections: 20
      connection_timeout: 30
  target:
    type: postgresql
    host: target-host
    port: 5432
    database: target_db
    username: target_user
    password: target_pass
    schema: cdm
    connection_pool:
      min_connections: 10
      max_connections: 50
      connection_timeout: 60

etl_settings:
  batch_size: 10000
  parallel_workers: 4
  retry_attempts: 3
  error_threshold: 0.05
  
logging:
  level: INFO
  format: JSON
  sinks:
    - type: file
      path: /var/log/omop/etl.log
      rotation: daily
    - type: console
      format: text

validation:
  strict_mode: true
  nhs_numbers: true
  postcodes: true
  dates: true
)";
    }

    void createComplexConfigFile() {
        complex_config_file_ = test_dir_ / "complex_config.yaml";
        std::ofstream stream(complex_config_file_);
        stream << R"(
# Complex OMOP ETL Configuration
database:
  source:
    type: postgresql
    host: ${SOURCE_HOST:-source-db}
    port: ${SOURCE_PORT:-5432}
    database: ${SOURCE_DB:-uk_clinical_data}
    username: ${SOURCE_USER:-clinical_user}
    password: ${SOURCE_PASSWORD:-clinical_pass}
    connection_pool:
      min_connections: 10
      max_connections: 50
      connection_timeout: 30
      idle_timeout: 300
      validation_query: "SELECT 1"
    ssl:
      enabled: true
      mode: require
      cert_path: /etc/ssl/certs/client-cert.pem
      key_path: /etc/ssl/private/client-key.pem
      ca_path: /etc/ssl/certs/ca-cert.pem
  target:
    type: postgresql
    host: ${OMOP_HOST:-omop-db}
    port: ${OMOP_PORT:-5432}
    database: ${OMOP_DB:-uk_omop_cdm}
    username: ${OMOP_USER:-omop_user}
    password: ${OMOP_PASSWORD:-omop_pass}
    schema: cdm
    vocab_schema: vocab
    connection_pool:
      min_connections: 20
      max_connections: 100
      connection_timeout: 60
      idle_timeout: 600
      max_lifetime: 3600
    batch_settings:
      batch_size: 50000
      commit_frequency: 10000
      parallel_inserts: 8
    performance:
      use_copy_command: true
      disable_triggers: false
      analyze_after_load: true

uk_settings:
  locale: en_GB.UTF-8
  currency: GBP
  timezone: Europe/London
  date_formats:
    - "%d/%m/%Y"
    - "%d-%m-%Y"
    - "%Y-%m-%d"
  time_formats:
    - "%H:%M:%S"
    - "%H:%M"
  postal_code_validation:
    enabled: true
    strict: true
    allow_spaces: true
  nhs_number_validation:
    enabled: true
    checksum_validation: true
    allow_formatting: true
  phone_validation:
    enabled: true
    uk_mobile_prefixes:
      - "07"
      - "+447"
    uk_landline_prefixes:
      - "01"
      - "02"
      - "+441"
      - "+442"

etl_pipeline:
  extract:
    batch_size: 25000
    parallel_workers: 6
    memory_limit_mb: 2048
    timeout_seconds: 1800
    source_tables:
      - patients
      - episodes
      - diagnoses
      - procedures
      - medications
      - observations
      - measurements
  transform:
    batch_size: 10000
    parallel_workers: 8
    memory_limit_mb: 1024
    validation_mode: strict
    mapping_files:
      - uk_concept_mappings.yaml
      - nhs_specialty_mappings.yaml
      - icd10_snomed_mappings.yaml
    custom_transformations:
      - name: uk_date_standardization
        function: standardize_uk_dates
        priority: 1
      - name: nhs_number_formatting
        function: format_nhs_numbers
        priority: 2
      - name: postcode_normalization
        function: normalize_uk_postcodes
        priority: 3
  load:
    batch_size: 20000
    parallel_workers: 4
    memory_limit_mb: 1536
    constraint_checking: deferred
    foreign_key_checking: false
    target_tables:
      - person
      - visit_occurrence
      - condition_occurrence
      - procedure_occurrence
      - drug_exposure
      - measurement
      - observation

performance_monitoring:
  enabled: true
  metrics_collection:
    interval_seconds: 30
    retention_days: 7
    export_formats:
      - prometheus
      - json
  benchmarking:
    enabled: true
    warmup_iterations: 5
    test_iterations: 100
    timeout_seconds: 300
    failure_threshold_percent: 5.0
  alerting:
    enabled: true
    thresholds:
      memory_usage_percent: 85.0
      cpu_usage_percent: 90.0
      error_rate_percent: 2.0
      latency_ms: 1000.0

logging:
  level: ${LOG_LEVEL:-INFO}
  format: ${LOG_FORMAT:-JSON}
  async_logging:
    enabled: true
    queue_size: 65536
    flush_interval_ms: 100
    thread_count: 2
  sinks:
    - type: file
      path: /var/log/omop/etl-${DATE}.log
      rotation: daily
      max_files: 30
      max_size_mb: 100
      compression: gzip
    - type: file
      path: /var/log/omop/etl-audit-${DATE}.log
      level: AUDIT
      format: JSON
      rotation: daily
      max_files: 365
    - type: console
      level: ${CONSOLE_LOG_LEVEL:-WARN}
      format: text
      colors: true
    - type: syslog
      facility: LOG_USER
      format: JSON
      enabled: false

monitoring:
  health_checks:
    enabled: true
    interval_seconds: 60
    endpoints:
      - database_connectivity
      - memory_usage
      - disk_space
      - network_connectivity
  metrics:
    enabled: true
    collection_interval_seconds: 15
    retention_hours: 168
    export_interval_seconds: 60
  tracing:
    enabled: false
    sampling_rate: 0.1
    jaeger_endpoint: ${JAEGER_ENDPOINT:-http://localhost:14268/api/traces}

security:
  encryption:
    enabled: true
    algorithm: AES-256-GCM
    key_rotation_days: 90
  data_masking:
    enabled: true
    fields:
      - patient_id
      - nhs_number
      - postcode
      - date_of_birth
  audit_logging:
    enabled: true
    log_all_queries: false
    log_data_access: true
    log_configuration_changes: true

error_handling:
  retry_policy:
    max_attempts: 3
    backoff_strategy: exponential
    base_delay_ms: 100
    max_delay_ms: 30000
  circuit_breaker:
    failure_threshold: 10
    timeout_ms: 60000
    recovery_timeout_ms: 30000
  fallback_strategies:
    - type: skip_record
      conditions:
        - validation_failure
        - data_conversion_error
    - type: default_value
      conditions:
        - missing_required_field
      mappings:
        gender: "U"
        race: "0"
)";
    }

protected:
    std::filesystem::path test_dir_;
    std::filesystem::path simple_config_file_;
    std::filesystem::path medium_config_file_;
    std::filesystem::path complex_config_file_;
    std::unique_ptr<PerformanceMonitor> monitor_;
    
    // Performance targets from design specifications
    double config_loading_target_ms_;
    double log_throughput_target_msgs_per_sec_;
    double log_latency_target_us_;
    double validation_target_ms_;
    double memory_target_mb_;
};

/**
 * @brief Benchmark configuration loading performance
 * Target: < 100ms for configuration loading
 */
TEST_F(PerformanceBenchmarkTest, ConfigurationLoadingPerformance) {
    BenchmarkConfig bench_config;
    bench_config.benchmark_name = "configuration_loading";
    bench_config.iterations = 100;
    bench_config.warmup_iterations = 10;
    bench_config.timeout = std::chrono::seconds(30);

    ASSERT_TRUE(monitor_->initialize(bench_config));
    ASSERT_TRUE(monitor_->start_monitoring());

    std::vector<double> simple_load_times;
    std::vector<double> medium_load_times;
    std::vector<double> complex_load_times;

    // Benchmark simple configuration loading
    for (size_t i = 0; i < bench_config.iterations; ++i) {
        auto start = high_resolution_clock::now();
        
        try {
            auto config = ConfigurationManager::load_config(simple_config_file_.string());
            [[maybe_unused]] auto db_type = config->database.type;
        } catch (const std::exception&) {
            // Ignore individual failures for benchmark
        }
        
        auto end = high_resolution_clock::now();
        double duration_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        simple_load_times.push_back(duration_ms);
    }

    // Benchmark medium configuration loading
    for (size_t i = 0; i < bench_config.iterations; ++i) {
        auto start = high_resolution_clock::now();
        
        try {
            auto config = ConfigurationManager::load_config(medium_config_file_.string());
            [[maybe_unused]] auto batch_size = config->etl_settings.batch_size;
        } catch (const std::exception&) {
            // Ignore individual failures for benchmark
        }
        
        auto end = high_resolution_clock::now();
        double duration_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        medium_load_times.push_back(duration_ms);
    }

    // Benchmark complex configuration loading
    for (size_t i = 0; i < bench_config.iterations; ++i) {
        auto start = high_resolution_clock::now();
        
        try {
            auto config = ConfigurationManager::load_config(complex_config_file_.string());
            [[maybe_unused]] auto workers = config->etl_pipeline.extract.parallel_workers;
        } catch (const std::exception&) {
            // Ignore individual failures for benchmark
        }
        
        auto end = high_resolution_clock::now();
        double duration_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        complex_load_times.push_back(duration_ms);
    }

    ASSERT_TRUE(monitor_->stop_monitoring());

    // Calculate statistics
    auto calculate_stats = [](const std::vector<double>& times) {
        if (times.empty()) return std::make_tuple(0.0, 0.0, 0.0, 0.0);
        
        double sum = std::accumulate(times.begin(), times.end(), 0.0);
        double mean = sum / times.size();
        
        std::vector<double> sorted_times = times;
        std::sort(sorted_times.begin(), sorted_times.end());
        
        double min_time = sorted_times.front();
        double max_time = sorted_times.back();
        double median = sorted_times[sorted_times.size() / 2];
        
        return std::make_tuple(mean, median, min_time, max_time);
    };

    auto [simple_mean, simple_median, simple_min, simple_max] = calculate_stats(simple_load_times);
    auto [medium_mean, medium_median, medium_min, medium_max] = calculate_stats(medium_load_times);
    auto [complex_mean, complex_median, complex_min, complex_max] = calculate_stats(complex_load_times);

    // Report results
    std::cout << "Configuration Loading Performance Results:" << std::endl;
    std::cout << "Simple Config - Mean: " << simple_mean << "ms, Median: " << simple_median 
              << "ms, Min: " << simple_min << "ms, Max: " << simple_max << "ms" << std::endl;
    std::cout << "Medium Config - Mean: " << medium_mean << "ms, Median: " << medium_median 
              << "ms, Min: " << medium_min << "ms, Max: " << medium_max << "ms" << std::endl;
    std::cout << "Complex Config - Mean: " << complex_mean << "ms, Median: " << complex_median 
              << "ms, Min: " << complex_min << "ms, Max: " << complex_max << "ms" << std::endl;

    // Verify performance targets
    EXPECT_LT(simple_mean, config_loading_target_ms_) 
        << "Simple configuration loading exceeds target of " << config_loading_target_ms_ << "ms";
    EXPECT_LT(medium_mean, config_loading_target_ms_) 
        << "Medium configuration loading exceeds target of " << config_loading_target_ms_ << "ms";
    EXPECT_LT(complex_mean, config_loading_target_ms_) 
        << "Complex configuration loading exceeds target of " << config_loading_target_ms_ << "ms";
}

/**
 * @brief Benchmark logging throughput performance
 * Target: > 100,000 messages/second
 */
TEST_F(PerformanceBenchmarkTest, LoggingThroughputPerformance) {
    BenchmarkConfig bench_config;
    bench_config.benchmark_name = "logging_throughput";
    bench_config.iterations = 10;
    bench_config.timeout = std::chrono::seconds(60);

    ASSERT_TRUE(monitor_->initialize(bench_config));
    ASSERT_TRUE(monitor_->start_monitoring());

    // Create a custom memory sink for performance testing
    class MemoryLogSink : public ILogSink {
    public:
        void log(const LogEntry& entry) override {
            message_count_.fetch_add(1, std::memory_order_relaxed);
        }
        void flush() override {}
        void set_level(LogLevel level) override { level_ = level; }
        LogLevel get_level() const override { return level_; }
        std::atomic<size_t> message_count_{0};
    private:
        LogLevel level_{LogLevel::INFO};
    };

    auto memory_sink = std::make_shared<MemoryLogSink>();
    auto logger = Logger::get("performance_test");
    logger->add_sink(memory_sink);

    std::vector<double> throughput_results;
    const size_t messages_per_test = 1000000; // 1M messages per test

    for (size_t test = 0; test < bench_config.iterations; ++test) {
        memory_sink->message_count_ = 0;
        
        auto start = high_resolution_clock::now();
        
        // Multi-threaded logging test
        const size_t num_threads = std::thread::hardware_concurrency();
        const size_t messages_per_thread = messages_per_test / num_threads;
        
        std::vector<std::thread> threads;
        std::atomic<bool> start_flag{false};
        
        for (size_t t = 0; t < num_threads; ++t) {
            threads.emplace_back([&, t]() {
                // Wait for start signal
                while (!start_flag.load()) {
                    std::this_thread::yield();
                }
                
                for (size_t i = 0; i < messages_per_thread; ++i) {
                    logger->info("Performance test message {} from thread {}", i, t);
                }
            });
        }
        
        // Start all threads simultaneously
        start_flag = true;
        
        // Wait for completion
        for (auto& thread : threads) {
            thread.join();
        }
        
        auto end = high_resolution_clock::now();
        
        double duration_seconds = duration_cast<microseconds>(end - start).count() / 1000000.0;
        double throughput = memory_sink->message_count_.load() / duration_seconds;
        throughput_results.push_back(throughput);
    }

    ASSERT_TRUE(monitor_->stop_monitoring());

    // Calculate statistics
    double total_throughput = std::accumulate(throughput_results.begin(), throughput_results.end(), 0.0);
    double mean_throughput = total_throughput / throughput_results.size();
    
    std::sort(throughput_results.begin(), throughput_results.end());
    double min_throughput = throughput_results.front();
    double max_throughput = throughput_results.back();
    double median_throughput = throughput_results[throughput_results.size() / 2];

    // Report results
    std::cout << "Logging Throughput Performance Results:" << std::endl;
    std::cout << "Mean: " << std::fixed << std::setprecision(0) << mean_throughput << " msgs/sec" << std::endl;
    std::cout << "Median: " << median_throughput << " msgs/sec" << std::endl;
    std::cout << "Min: " << min_throughput << " msgs/sec" << std::endl;
    std::cout << "Max: " << max_throughput << " msgs/sec" << std::endl;

    // Verify performance target
    EXPECT_GT(mean_throughput, log_throughput_target_msgs_per_sec_)
        << "Logging throughput " << mean_throughput << " msgs/sec is below target of " 
        << log_throughput_target_msgs_per_sec_ << " msgs/sec";
}

/**
 * @brief Benchmark validation performance
 * Target: < 10ms for complex schemas
 */
TEST_F(PerformanceBenchmarkTest, ValidationPerformance) {
    BenchmarkConfig bench_config;
    bench_config.benchmark_name = "validation_performance";
    bench_config.iterations = 1000;
    bench_config.warmup_iterations = 100;

    ASSERT_TRUE(monitor_->initialize(bench_config));
    ASSERT_TRUE(monitor_->start_monitoring());

    // Create validation engine with complex rules
    BasicValidationEngine engine;
    
    // Add comprehensive UK healthcare validation rules
    engine.add_rule(std::make_shared<NotNullRule>("nhs_number"));
    engine.add_rule(std::make_shared<RegexRule>("nhs_number", R"(\d{10})"));
    engine.add_rule(std::make_shared<CustomRule>("nhs_number", [](const std::any& value) -> ValidationResult {
        try {
            std::string nhs = std::any_cast<std::string>(value);
            return MedicalUtils::is_valid_nhs_number(nhs) ? 
                ValidationResult{true} : 
                ValidationResult{false, "Invalid NHS number checksum"};
        } catch (...) {
            return ValidationResult{false, "Invalid NHS number format"};
        }
    }));
    
    engine.add_rule(std::make_shared<NotNullRule>("postcode"));
    engine.add_rule(std::make_shared<CustomRule>("postcode", [](const std::any& value) -> ValidationResult {
        try {
            std::string postcode = std::any_cast<std::string>(value);
            return ValidationUtils::is_valid_uk_postcode(postcode) ? 
                ValidationResult{true} : 
                ValidationResult{false, "Invalid UK postcode format"};
        } catch (...) {
            return ValidationResult{false, "Invalid postcode format"};
        }
    }));
    
    engine.add_rule(std::make_shared<NotNullRule>("date_of_birth"));
    engine.add_rule(std::make_shared<DateRangeRule>("date_of_birth", "1900-01-01", "2025-12-31"));
    
    engine.add_rule(std::make_shared<NotNullRule>("gender"));
    engine.add_rule(std::make_shared<InSetRule>("gender", {"M", "F", "U", "O"}));

    // Create test records with varying complexity
    std::vector<Record> test_records;
    std::random_device rd;
    std::mt19937 gen(rd());
    
    // Generate test data
    std::vector<std::string> valid_nhs_numbers = {
        "**********", "**********", "**********", "**********", "**********"
    };
    std::vector<std::string> valid_postcodes = {
        "M60 1QD", "SW1A 1AA", "B33 8TH", "W1A 0AX", "EC1A 1BB"
    };
    std::vector<std::string> valid_genders = {"M", "F", "U", "O"};
    std::vector<std::string> valid_dates = {
        "1990-01-15", "1985-06-20", "1978-12-03", "2000-03-25", "1965-09-10"
    };

    for (size_t i = 0; i < 10000; ++i) {
        Record record;
        std::uniform_int_distribution<> idx_dist(0, 4);
        
        record.set_field("patient_id", std::to_string(i + 1));
        record.set_field("nhs_number", valid_nhs_numbers[idx_dist(gen)]);
        record.set_field("postcode", valid_postcodes[idx_dist(gen)]);
        record.set_field("date_of_birth", valid_dates[idx_dist(gen)]);
        record.set_field("gender", valid_genders[idx_dist(gen)]);
        
        test_records.push_back(std::move(record));
    }

    std::vector<double> validation_times;

    // Benchmark validation performance
    for (size_t test = 0; test < bench_config.iterations; ++test) {
        auto start = high_resolution_clock::now();
        
        // Validate batch of records
        std::vector<ValidationResult> results;
        for (const auto& record : test_records) {
            auto result = engine.validate_record(record);
            results.push_back(result);
        }
        
        auto end = high_resolution_clock::now();
        double duration_ms = duration_cast<microseconds>(end - start).count() / 1000.0;
        validation_times.push_back(duration_ms);
    }

    ASSERT_TRUE(monitor_->stop_monitoring());

    // Calculate statistics
    double sum = std::accumulate(validation_times.begin(), validation_times.end(), 0.0);
    double mean_time = sum / validation_times.size();
    
    std::sort(validation_times.begin(), validation_times.end());
    double min_time = validation_times.front();
    double max_time = validation_times.back();
    double median_time = validation_times[validation_times.size() / 2];

    // Report results
    std::cout << "Validation Performance Results (10,000 records):" << std::endl;
    std::cout << "Mean: " << std::fixed << std::setprecision(2) << mean_time << "ms" << std::endl;
    std::cout << "Median: " << median_time << "ms" << std::endl;
    std::cout << "Min: " << min_time << "ms" << std::endl;
    std::cout << "Max: " << max_time << "ms" << std::endl;

    // Verify performance target
    EXPECT_LT(mean_time, validation_target_ms_)
        << "Validation time " << mean_time << "ms exceeds target of " << validation_target_ms_ << "ms";
}

/**
 * @brief Benchmark memory usage during operations
 * Target: < 10MB for configurations
 */
TEST_F(PerformanceBenchmarkTest, MemoryUsagePerformance) {
    BenchmarkConfig bench_config;
    bench_config.benchmark_name = "memory_usage";
    bench_config.collect_memory_metrics = true;

    ASSERT_TRUE(monitor_->initialize(bench_config));
    ASSERT_TRUE(monitor_->start_monitoring());

    // Memory measurement utility
    auto get_memory_usage_mb = []() -> double {
        #ifdef __linux__
            std::ifstream status("/proc/self/status");
            std::string line;
            while (std::getline(status, line)) {
                if (line.find("VmRSS:") == 0) {
                    std::istringstream iss(line);
                    std::string label;
                    size_t size;
                    std::string unit;
                    iss >> label >> size >> unit;
                    return size / 1024.0; // Convert from KB to MB
                }
            }
        #endif
        return 0.0; // Fallback for other platforms
    };

    double baseline_memory = get_memory_usage_mb();
    std::vector<double> memory_measurements;

    // Test configuration loading memory usage
    for (int i = 0; i < 100; ++i) {
        try {
            auto config = ConfigurationManager::load_config(complex_config_file_.string());
            double current_memory = get_memory_usage_mb();
            memory_measurements.push_back(current_memory - baseline_memory);
        } catch (const std::exception&) {
            // Continue with benchmark even if some loads fail
        }
    }

    ASSERT_TRUE(monitor_->stop_monitoring());

    if (!memory_measurements.empty()) {
        double max_memory_increase = *std::max_element(memory_measurements.begin(), memory_measurements.end());
        double avg_memory_increase = std::accumulate(memory_measurements.begin(), memory_measurements.end(), 0.0) / memory_measurements.size();

        std::cout << "Memory Usage Performance Results:" << std::endl;
        std::cout << "Baseline memory: " << std::fixed << std::setprecision(2) << baseline_memory << " MB" << std::endl;
        std::cout << "Max memory increase: " << max_memory_increase << " MB" << std::endl;
        std::cout << "Average memory increase: " << avg_memory_increase << " MB" << std::endl;

        // Verify memory usage target
        EXPECT_LT(max_memory_increase, memory_target_mb_)
            << "Memory usage increase " << max_memory_increase << "MB exceeds target of " << memory_target_mb_ << "MB";
    }
}

/**
 * @brief Comprehensive performance regression test
 */
TEST_F(PerformanceBenchmarkTest, ComprehensivePerformanceRegression) {
    // This test runs all performance benchmarks together and ensures
    // that the system maintains acceptable performance under combined load
    
    std::cout << "\n=== Comprehensive Performance Regression Test ===" << std::endl;
    std::cout << "Running all performance benchmarks together..." << std::endl;
    
    // Start system-wide performance monitoring
    BenchmarkConfig overall_config;
    overall_config.benchmark_name = "comprehensive_regression";
    overall_config.collect_system_metrics = true;
    overall_config.collect_memory_metrics = true;
    overall_config.collect_cpu_metrics = true;
    overall_config.timeout = std::chrono::minutes(10);

    ASSERT_TRUE(monitor_->initialize(overall_config));
    ASSERT_TRUE(monitor_->start_monitoring());

    auto start_time = high_resolution_clock::now();

    // Run concurrent operations to simulate real-world load
    std::vector<std::future<bool>> futures;
    
    // Configuration loading in background
    futures.push_back(std::async(std::launch::async, [this]() {
        for (int i = 0; i < 50; ++i) {
            try {
                auto config = ConfigurationManager::load_config(complex_config_file_.string());
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            } catch (...) {}
        }
        return true;
    }));
    
    // Logging in background
    futures.push_back(std::async(std::launch::async, []() {
        auto logger = Logger::get("regression_test");
        for (int i = 0; i < 10000; ++i) {
            logger->info("Regression test log message {}", i);
            if (i % 100 == 0) {
                std::this_thread::sleep_for(std::chrono::microseconds(100));
            }
        }
        return true;
    }));
    
    // Validation in background
    futures.push_back(std::async(std::launch::async, [this]() {
        BasicValidationEngine engine;
        engine.add_rule(std::make_shared<NotNullRule>("test_field"));
        
        for (int i = 0; i < 1000; ++i) {
            Record record;
            record.set_field("test_field", "test_value_" + std::to_string(i));
            auto result = engine.validate_record(record);
            std::this_thread::sleep_for(std::chrono::microseconds(500));
        }
        return true;
    }));

    // Wait for all operations to complete
    bool all_successful = true;
    for (auto& future : futures) {
        all_successful &= future.get();
    }

    auto end_time = high_resolution_clock::now();
    auto total_duration = duration_cast<seconds>(end_time - start_time);

    ASSERT_TRUE(monitor_->stop_monitoring());

    std::cout << "Comprehensive test completed in " << total_duration.count() << " seconds" << std::endl;
    std::cout << "All operations successful: " << (all_successful ? "YES" : "NO") << std::endl;

    // Verify that all operations completed successfully
    EXPECT_TRUE(all_successful) << "Some performance regression operations failed";
    
    // Verify reasonable completion time (should complete within 5 minutes under normal conditions)
    EXPECT_LT(total_duration.count(), 300) << "Comprehensive performance test took too long: " << total_duration.count() << " seconds";
}

} // namespace omop::common::benchmarks