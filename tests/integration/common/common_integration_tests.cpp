/**
 * @file common_integration_tests.cpp
 * @brief Integration tests for common library components
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include "common/configuration.h"
#include "common/logging.h"
#include "common/http_client.h"
#include "common/metrics_collector.h"
#include "common/validation.h"
#include "common/utilities.h"
#include "common/performance_monitor.h"
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>
#include <locale>
#include <iomanip>
#include <sstream>
#include <cstdlib>

using namespace omop::common;
using namespace omop::monitoring;

namespace omop::common::integration::test {

// Integration test fixture with UK locale setup and real database connections
class CommonIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set UK locale for consistent regional formatting
        try {
            std::locale::global(std::locale("en_GB.UTF-8"));
        } catch (const std::runtime_error&) {
            std::locale::global(std::locale("C"));
        }
        
        // Create temporary directory for test files
        test_dir_ = std::filesystem::temp_directory_path() / "omop_integration_test";
        std::filesystem::create_directories(test_dir_);
        
        // UK-specific test data
        uk_currency_symbol_ = "£";
        uk_date_format_ = "%d/%m/%Y";
        nhs_number_ = "**********"; // Valid NHS number with checksum
        uk_postcode_ = "M60 1QD";
        
        setupTestConfiguration();
        setupTestDatabase();
        setupTestDatabaseSchemas();
    }

    void TearDown() override {
        // Clean up test files
        if (std::filesystem::exists(test_dir_)) {
            std::filesystem::remove_all(test_dir_);
        }
        
        // Clean up logging
        Logger::shutdown();
    }

    void setupTestConfiguration() {
        // Create a comprehensive test configuration file
        config_file_ = test_dir_ / "test_config.yaml";
        std::ofstream config_stream(config_file_);
        config_stream << R"(
# UK NHS ETL Configuration
database:
  source:
    type: "postgresql"
    host: ")" << getEnvOrDefault("POSTGRES_HOST", "clinical-db") << R"("
    port: )" << getEnvOrDefault("POSTGRES_PORT", "5432") << R"(
    database: ")" << getEnvOrDefault("POSTGRES_DB", "uk_clinical_data") << R"("
    username: ")" << getEnvOrDefault("POSTGRES_USER", "clinical_user") << R"("
    password: ")" << getEnvOrDefault("POSTGRES_PASSWORD", "clinical_pass") << R"("
    connection_timeout: 30
  target:
    type: "postgresql"
    host: ")" << getEnvOrDefault("OMOP_HOST", "omop-db") << R"("
    port: )" << getEnvOrDefault("OMOP_PORT", "5432") << R"(
    database: ")" << getEnvOrDefault("OMOP_DB", "uk_omop_cdm") << R"("
    username: ")" << getEnvOrDefault("OMOP_USER", "omop_user") << R"("
    password: ")" << getEnvOrDefault("OMOP_PASSWORD", "omop_pass") << R"("
    schema: "cdm"

uk_settings:
  locale: "en_GB.UTF-8"
  currency: "GBP"
  timezone: "Europe/London"
  date_format: "%d/%m/%Y"
  time_format: "%H:%M:%S"
  postal_code_validation: true
  nhs_number_validation: true

logging:
  level: "INFO"
  output_directory: ")" + test_dir_.string() + R"(/logs"
  max_file_size: "10MB"
  max_files: 5
  format: "json"

metrics:
  enabled: true
  collection_interval: 10
  retention_period: 3600
  export_format: "prometheus"
  export_endpoint: "/metrics"

validation:
  strict_mode: true
  uk_specific_rules: true
  nhs_number_checksum: true
  postcode_format_check: true

transformation:
  uk_date_formats: ["dd/MM/yyyy", "dd-MM-yyyy", "yyyy-MM-dd"]
  currency_symbol: "£"
  temperature_unit: "celsius"

performance:
  monitoring_enabled: true
  memory_threshold_mb: 1024
  cpu_threshold_percent: 80.0
  disk_io_threshold_mb: 100.0
)";
        config_stream.close();
    }

    void setupTestDatabase() {
        // Set up real database connections using Docker container endpoints
        db_config_["source_host"] = getEnvOrDefault("POSTGRES_HOST", "clinical-db");
        db_config_["source_port"] = getEnvOrDefault("POSTGRES_PORT", "5432");
        db_config_["source_database"] = getEnvOrDefault("POSTGRES_DB", "uk_clinical_data");
        db_config_["source_user"] = getEnvOrDefault("POSTGRES_USER", "clinical_user");
        db_config_["source_password"] = getEnvOrDefault("POSTGRES_PASSWORD", "clinical_pass");
        
        db_config_["target_host"] = getEnvOrDefault("OMOP_HOST", "omop-db");
        db_config_["target_port"] = getEnvOrDefault("OMOP_PORT", "5432");
        db_config_["target_database"] = getEnvOrDefault("OMOP_DB", "uk_omop_cdm");
        db_config_["target_user"] = getEnvOrDefault("OMOP_USER", "omop_user");
        db_config_["target_password"] = getEnvOrDefault("OMOP_PASSWORD", "omop_pass");
        
        // Test database connectivity and wait for containers to be ready
        waitForDatabaseConnectivity();
    }

    std::filesystem::path test_dir_;
    std::filesystem::path config_file_;
    std::unordered_map<std::string, std::string> db_config_;
    
private:
    std::string getEnvOrDefault(const std::string& env_var, const std::string& default_value) {
        const char* value = std::getenv(env_var.c_str());
        return value ? std::string(value) : default_value;
    }
    
    void waitForDatabaseConnectivity() {
        const int max_retries = 30;
        const std::chrono::seconds retry_delay{2};
        
        auto logger = Logger::get("integration-test");
        
        // Test source database connectivity
        for (int i = 0; i < max_retries; ++i) {
            try {
                auto source_connection = createDatabaseConnection(
                    db_config_["source_host"],
                    std::stoi(db_config_["source_port"]),
                    db_config_["source_database"],
                    db_config_["source_user"],
                    db_config_["source_password"]
                );
                
                if (source_connection && source_connection->is_connected()) {
                    logger->info("Source database connection established: {}@{}:{}/{}",
                               db_config_["source_user"], db_config_["source_host"],
                               db_config_["source_port"], db_config_["source_database"]);
                    break;
                }
            } catch (const std::exception& e) {
                logger->warn("Source database connection attempt {} failed: {}", i + 1, e.what());
            }
            
            if (i == max_retries - 1) {
                throw std::runtime_error("Failed to connect to source database after " + 
                                       std::to_string(max_retries) + " attempts");
            }
            
            std::this_thread::sleep_for(retry_delay);
        }
        
        // Test target database connectivity
        for (int i = 0; i < max_retries; ++i) {
            try {
                auto target_connection = createDatabaseConnection(
                    db_config_["target_host"],
                    std::stoi(db_config_["target_port"]),
                    db_config_["target_database"],
                    db_config_["target_user"],
                    db_config_["target_password"]
                );
                
                if (target_connection && target_connection->is_connected()) {
                    logger->info("Target database connection established: {}@{}:{}/{}",
                               db_config_["target_user"], db_config_["target_host"],
                               db_config_["target_port"], db_config_["target_database"]);
                    break;
                }
            } catch (const std::exception& e) {
                logger->warn("Target database connection attempt {} failed: {}", i + 1, e.what());
            }
            
            if (i == max_retries - 1) {
                throw std::runtime_error("Failed to connect to target database after " + 
                                       std::to_string(max_retries) + " attempts");
            }
            
            std::this_thread::sleep_for(retry_delay);
        }
    }
    
    std::unique_ptr<omop::extract::IDatabaseConnection> createDatabaseConnection(
        const std::string& host, int port, const std::string& database,
        const std::string& username, const std::string& password) {
        
        using namespace omop::extract;
        
        auto connection = std::make_unique<PostgreSQLConnection>();
        IDatabaseConnection::ConnectionParams params;
        params.host = host;
        params.port = port;
        params.database = database;
        params.username = username;
        params.password = password;
        params.options["connect_timeout"] = "10";
        
        connection->connect(params);
        return std::move(connection);
    }
    
    void setupTestDatabaseSchemas() {
        auto logger = Logger::get("integration-test");
        
        try {
            // Setup source database schema
            auto source_connection = createDatabaseConnection(
                db_config_["source_host"],
                std::stoi(db_config_["source_port"]),
                db_config_["source_database"],
                db_config_["source_user"],
                db_config_["source_password"]
            );
            
            // Create test tables in source database
            source_connection->execute_update(R"(
                CREATE TABLE IF NOT EXISTS uk_patients (
                    patient_id SERIAL PRIMARY KEY,
                    nhs_number VARCHAR(10) UNIQUE NOT NULL,
                    first_name VARCHAR(50) NOT NULL,
                    last_name VARCHAR(50) NOT NULL,
                    date_of_birth DATE NOT NULL,
                    gender CHAR(1) NOT NULL,
                    postcode VARCHAR(8),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            )");
            
            // Insert sample UK healthcare data
            source_connection->execute_update(R"(
                INSERT INTO uk_patients (nhs_number, first_name, last_name, date_of_birth, gender, postcode) 
                VALUES 
                ('**********', 'John', 'Smith', '1980-05-15', 'M', 'M60 1QD'),
                ('**********', 'Sarah', 'Johnson', '1975-11-22', 'F', 'SW1A 1AA'),
                ('**********', 'David', 'Williams', '1990-03-08', 'M', 'E1 6AN'),
                ('**********', 'Emma', 'Brown', '1985-09-12', 'F', 'B1 1HQ'),
                ('**********', 'Michael', 'Jones', '1970-12-03', 'M', 'LS1 1UR')
                ON CONFLICT (nhs_number) DO NOTHING
            )");
            
            logger->info("Source database schema and test data setup complete");
            
            // Setup target OMOP CDM database schema
            auto target_connection = createDatabaseConnection(
                db_config_["target_host"],
                std::stoi(db_config_["target_port"]),
                db_config_["target_database"],
                db_config_["target_user"],
                db_config_["target_password"]
            );
            
            // Create basic OMOP CDM tables for testing
            target_connection->execute_update(R"(
                CREATE SCHEMA IF NOT EXISTS cdm
            )");
            
            target_connection->execute_update(R"(
                CREATE TABLE IF NOT EXISTS cdm.person (
                    person_id INTEGER PRIMARY KEY,
                    gender_concept_id INTEGER NOT NULL,
                    year_of_birth INTEGER NOT NULL,
                    month_of_birth INTEGER,
                    day_of_birth INTEGER,
                    birth_datetime TIMESTAMP,
                    race_concept_id INTEGER NOT NULL,
                    ethnicity_concept_id INTEGER NOT NULL,
                    location_id INTEGER,
                    provider_id INTEGER,
                    care_site_id INTEGER,
                    person_source_value VARCHAR(50),
                    gender_source_value VARCHAR(50),
                    gender_source_concept_id INTEGER,
                    race_source_value VARCHAR(50),
                    race_source_concept_id INTEGER,
                    ethnicity_source_value VARCHAR(50),
                    ethnicity_source_concept_id INTEGER
                )
            )");
            
            logger->info("Target OMOP CDM database schema setup complete");
            
        } catch (const std::exception& e) {
            logger->error("Failed to setup test database schemas: {}", e.what());
            throw;
        }
    }
    
    std::string uk_currency_symbol_;
    std::string uk_date_format_;
    std::string nhs_number_;
    std::string uk_postcode_;

};

// Test complete UK NHS data processing workflow
TEST_F(CommonIntegrationTest, CompleteUKNHSDataProcessingWorkflow) {
    auto logger = Logger::get("uk_nhs_workflow");
    
    // 1. Test real database connectivity and data extraction
    auto source_connection = createDatabaseConnection(
        db_config_["source_host"],
        std::stoi(db_config_["source_port"]),
        db_config_["source_database"],
        db_config_["source_user"],
        db_config_["source_password"]
    );
    
    ASSERT_TRUE(source_connection->is_connected()) << "Source database connection failed";
    
    // 2. Verify UK patient data exists and is valid
    auto result = source_connection->execute_query(
        "SELECT patient_id, nhs_number, first_name, last_name, postcode FROM uk_patients ORDER BY patient_id"
    );
    
    int patient_count = 0;
    while (result->next()) {
        patient_count++;
        auto nhs_number = std::any_cast<std::string>(result->get_value("nhs_number"));
        auto postcode = std::any_cast<std::string>(result->get_value("postcode"));
        
        // Validate NHS number format and checksum
        EXPECT_TRUE(MedicalUtils::is_valid_nhs_number(nhs_number)) 
            << "Invalid NHS number: " << nhs_number;
        
        // Validate UK postcode format
        EXPECT_TRUE(ValidationUtils::is_valid_uk_postcode(postcode))
            << "Invalid UK postcode: " << postcode;
    }
    
    EXPECT_GT(patient_count, 0) << "No patient data found in source database";
    logger->info("Validated {} UK NHS patients in source database", patient_count);
    
    // 3. Load configuration and verify UK-specific settings
    auto config_manager = std::make_unique<ConfigurationManager>();
    ASSERT_TRUE(config_manager->loadFromFile(config_file_.string()));
    
    // Verify UK-specific configuration
    EXPECT_EQ(config_manager->getString("uk_settings.locale", ""), "en_GB.UTF-8");
    EXPECT_EQ(config_manager->getString("uk_settings.currency", ""), "GBP");
    EXPECT_EQ(config_manager->getString("uk_settings.timezone", ""), "Europe/London");
    EXPECT_TRUE(config_manager->getBool("uk_settings.nhs_number_validation", false));
    
    // 4. Test OMOP CDM database connectivity and schema
    auto target_connection = createDatabaseConnection(
        db_config_["target_host"],
        std::stoi(db_config_["target_port"]),
        db_config_["target_database"],
        db_config_["target_user"],
        db_config_["target_password"]
    );
    
    ASSERT_TRUE(target_connection->is_connected()) << "OMOP CDM database connection failed";
    
    // Verify CDM schema exists
    auto schema_result = target_connection->execute_query(
        "SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'cdm'"
    );
    
    ASSERT_TRUE(schema_result->next()) << "CDM schema not found in target database";
    logger->info("CDM schema verified in target database");
    
    // Verify person table structure
    auto table_result = target_connection->execute_query(
        "SELECT column_name FROM information_schema.columns WHERE table_schema = 'cdm' AND table_name = 'person' ORDER BY ordinal_position"
    );
    
    std::vector<std::string> person_columns;
    while (table_result->next()) {
        person_columns.push_back(std::any_cast<std::string>(table_result->get_value("column_name")));
    }
    
    EXPECT_FALSE(person_columns.empty()) << "Person table not found or has no columns";
    EXPECT_TRUE(std::find(person_columns.begin(), person_columns.end(), "person_id") != person_columns.end());
    EXPECT_TRUE(std::find(person_columns.begin(), person_columns.end(), "gender_concept_id") != person_columns.end());
    logger->info("OMOP CDM person table structure validated with {} columns", person_columns.size());
    
    // 5. Initialize logging with UK formatting
    auto file_sink = std::make_shared<FileSink>(
        (test_dir_ / "uk_integration.log").string(), 
        1024 * 1024, 3
    );
    auto json_formatter = std::make_unique<JsonLogFormatter>();
    json_formatter->set_pretty_print(true);
    file_sink->set_formatter(std::move(json_formatter));
    logger->add_sink(file_sink);
    logger->set_level(LogLevel::Info);
    
    // 3. Initialize metrics collection for UK ETL operations
    auto metrics_collector = create_metrics_collector();
    auto metrics_config = get_default_metrics_config();
    metrics_config.collection_interval = std::chrono::seconds(10);
    metrics_config.enabled = false; // Disable collection thread for test
    ASSERT_TRUE(metrics_collector->initialize(metrics_config));
    
    // Create standard ETL metrics
    ASSERT_TRUE(create_standard_etl_metrics(*metrics_collector));
    
    // 4. Set up validation engine with UK-specific rules
    auto validation_engine = std::make_unique<BasicValidationEngine>();
    
    // Add UK NHS number validation rule
    auto nhs_validator = [](const std::any& value, const std::unordered_map<std::string, std::any>&) -> bool {
        try {
            std::string nhs_num = std::any_cast<std::string>(value);
            return MedicalUtils::is_valid_nhs_number(nhs_num);
        } catch (...) {
            return false;
        }
    };
    validation_engine->addRule(std::make_unique<CustomRule>("nhs_number", nhs_validator, "Invalid UK NHS number"));
    
    // Add UK postcode validation rule
    std::string uk_postcode_pattern = R"([A-Z]{1,2}[0-9R][0-9A-Z]?\s?[0-9][A-Z]{2})";
    validation_engine->addRule(std::make_unique<RegexRule>("postcode", uk_postcode_pattern));
    
    // 5. Create sample UK NHS patient records
    std::vector<std::unordered_map<std::string, std::any>> uk_patient_records = {
        {
            {"nhs_number", std::string("**********")},
            {"postcode", std::string("M60 1QD")},
            {"birth_date", std::string("15/01/1985")},
            {"gender", std::string("Male")},
            {"gp_practice_code", std::string("M12345")},
            {"treatment_cost", omop::common::UKLocalization::format_uk_currency(125.50)}
        },
        {
            {"nhs_number", std::string("**********")},
            {"postcode", std::string("B33 8TH")},
            {"birth_date", std::string("22/03/1978")},
            {"gender", std::string("Female")},
            {"gp_practice_code", std::string("B67890")},
            {"treatment_cost", omop::common::UKLocalization::format_uk_currency(89.75)}
        }
    };
    
    // 6. Process records through validation pipeline
    logger->info("Starting UK NHS data validation workflow");
    
    size_t valid_records = 0;
    size_t invalid_records = 0;
    
    for (size_t i = 0; i < uk_patient_records.size(); ++i) {
        const auto& record = uk_patient_records[i];
        
        // Log record processing start
        std::unordered_map<std::string, std::any> log_context;
        log_context["record_id"] = i;
        log_context["nhs_number"] = std::any_cast<std::string>(record.at("nhs_number"));
        log_context["postcode"] = std::any_cast<std::string>(record.at("postcode"));
        
        logger->log_structured(LogLevel::Info, "Processing UK NHS patient record", log_context);
        
        // Validate record
        auto validation_result = validation_engine->validateRecord(record);
        
        if (validation_result.is_valid()) {
            valid_records++;
            logger->info("Record {} validation passed", i);
            
            // Update metrics
            std::unordered_map<std::string, std::string> labels = {
                {"status", "valid"},
                {"region", "uk"}
            };
            metrics_collector->increment_counter("etl_records_processed_total", 1.0, labels);
        } else {
            invalid_records++;
            logger->error("Record {} validation failed with {} errors", i, validation_result.error_count());
            
            // Log validation errors
            for (const auto& error : validation_result.errors()) {
                logger->error("Validation error - Field: {}, Message: {}, Rule: {}", 
                            error.field_name, error.error_message, error.rule_name);
            }
            
            // Update metrics
            std::unordered_map<std::string, std::string> labels = {
                {"status", "invalid"},
                {"region", "uk"}
            };
            metrics_collector->increment_counter("etl_records_processed_total", 1.0, labels);
            metrics_collector->increment_counter("etl_errors_total", 1.0, labels);
        }
    }
    
    // 7. Test HTTP client with UK healthcare API endpoints
    auto http_client = HttpClientFactory::create_client();
    http_client->set_timeout(30);
    
    std::unordered_map<std::string, std::string> headers = {
        {"Accept", "application/json"},
        {"Content-Type", "application/json"},
        {"User-Agent", "OMOP-ETL-UK/1.0"}
    };
    http_client->set_default_headers(headers);
    
    // Simulate UK healthcare data submission
    std::string uk_health_data = R"({
        "submission_date": "15/01/2025",
        "region": "Greater Manchester",
        "currency": "GBP",
        "timezone": "Europe/London",
        "patient_count": )" + std::to_string(valid_records) + R"(,
        "validation_errors": )" + std::to_string(invalid_records) + R"(,
        "cost_total": ")" + omop::common::UKLocalization::format_uk_currency(215.25) + R"("
    })";
    
    // Note: In real integration tests, this would call actual NHS or OMOP endpoints
    // For now, we test the HTTP client structure
    HttpClient::Response response;
    response.status_code = 200;
    response.success = true;
    response.body = R"({"status": "accepted", "submission_id": "UK-ETL-2025-001"})";
    
    EXPECT_EQ(response.status_code, 200);
    EXPECT_TRUE(response.success);
    EXPECT_FALSE(response.body.empty());
    
    // 8. Test performance monitoring for UK ETL operations
    auto performance_monitor = std::make_unique<PerformanceMonitor>();
    
    // Simulate UK ETL processing performance
    performance_monitor->startOperation("uk_nhs_data_processing");
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    auto duration = performance_monitor->endOperation("uk_nhs_data_processing");
    
    EXPECT_GT(duration, 0.0);
    
    // Test memory and resource monitoring
    auto memory_usage = performance_monitor->getCurrentMemoryUsage();
    auto cpu_usage = performance_monitor->getCurrentCPUUsage();
    
    EXPECT_GE(memory_usage, 0u);
    EXPECT_GE(cpu_usage, 0.0);
    
    // 9. Export metrics in Prometheus format for UK monitoring
    std::string prometheus_metrics = metrics_collector->export_metrics("prometheus");
    EXPECT_FALSE(prometheus_metrics.empty());
    EXPECT_TRUE(prometheus_metrics.find("etl_records_processed_total") != std::string::npos);
    
    // 10. Final validation and cleanup
    logger->info("UK NHS data processing workflow completed successfully");
    logger->info("Valid records processed: {}", valid_records);
    logger->info("Invalid records found: {}", invalid_records);
    
    // Verify all UK patient records were processed
    EXPECT_EQ(valid_records + invalid_records, uk_patient_records.size());
    EXPECT_GT(valid_records, 0);
    
    // Verify log file was created
    EXPECT_TRUE(std::filesystem::exists(test_dir_ / "uk_integration.log"));
    
    // Verify log file contains UK-specific content
    std::ifstream log_file(test_dir_ / "uk_integration.log");
    std::string log_content((std::istreambuf_iterator<char>(log_file)),
                           std::istreambuf_iterator<char>());
    EXPECT_TRUE(log_content.find("NHS") != std::string::npos);
    EXPECT_TRUE(log_content.find("uk_integration_test") != std::string::npos);
}

// Test UK-specific data transformation and validation integration
TEST_F(CommonIntegrationTest, UKSpecificDataTransformationAndValidationIntegration) {
    // Initialize components
    auto logger = Logger::get("uk_transformation_test");
    auto console_sink = std::make_shared<ConsoleSink>();
    logger->add_sink(console_sink);
    
    auto validation_engine = std::make_unique<BasicValidationEngine>();
    
    // Test UK date format transformation and validation
    std::vector<std::string> uk_date_inputs = {
        "15/01/2025",   // DD/MM/YYYY
        "22-03-1985",   // DD-MM-YYYY
        "2023-12-31",   // YYYY-MM-DD
        "01/01/1900",   // Edge case - old date
        "31/12/2099"    // Edge case - future date
    };
    
    for (const auto& date_str : uk_date_inputs) {
        logger->info("Processing UK date format: {}", date_str);
        
        // Test date parsing with multiple UK formats
        std::optional<std::chrono::system_clock::time_point> parsed_date;
        
        // Try DD/MM/YYYY format first
        parsed_date = date_utils::parse_date(date_str, "%d/%m/%Y");
        if (!parsed_date) {
            // Try DD-MM-YYYY format
            parsed_date = date_utils::parse_date(date_str, "%d-%m-%Y");
        }
        if (!parsed_date) {
            // Try YYYY-MM-DD format
            parsed_date = date_utils::parse_date(date_str, "%Y-%m-%d");
        }
        
        if (parsed_date) {
            // Convert to UK display format
            std::string uk_formatted = UKLocalization::format_uk_date(*parsed_date);
            logger->info("Successfully parsed and formatted to UK format: {}", uk_formatted);
            
            // Validate the formatted date
            EXPECT_TRUE(ValidationUtils::is_valid_date_format(uk_formatted, "%d/%m/%Y"));
        } else {
            logger->warn("Failed to parse date: {}", date_str);
        }
    }
    
    // Test UK postcode normalization and validation
    std::vector<std::string> uk_postcodes = {
        "M601QD",       // Without space
        "M60 1QD",      // With space
        "B338TH",       // Birmingham
        "W1A0AX",       // Central London
        "GIR0AA",       // Special case
        "INVALID"       // Invalid postcode
    };
    
    size_t valid_postcodes = 0;
    for (const auto& postcode : uk_postcodes) {
        logger->info("Validating UK postcode: {}", postcode);
        
        if (ValidationUtils::is_valid_uk_postcode(postcode)) {
            valid_postcodes++;
            logger->info("Valid UK postcode: {}", postcode);
        } else {
            logger->warn("Invalid UK postcode: {}", postcode);
        }
    }
    
    EXPECT_EQ(valid_postcodes, 5); // All except "INVALID"
    
    // Test UK NHS number validation and checksum
    std::vector<std::string> nhs_numbers = {
        "**********",   // Valid with checksum
        "************", // Valid with spaces
        "**********",   // Another valid number
        "**********",   // Invalid checksum
        "123456789",    // Too short
        "abcdefghij"    // Non-numeric
    };
    
    size_t valid_nhs_numbers = 0;
    for (const auto& nhs_num : nhs_numbers) {
        logger->info("Validating NHS number: {}", nhs_num);
        
        if (MedicalUtils::is_valid_nhs_number(nhs_num)) {
            valid_nhs_numbers++;
            logger->info("Valid NHS number: {}", nhs_num);
        } else {
            logger->warn("Invalid NHS number: {}", nhs_num);
        }
    }
    
    EXPECT_EQ(valid_nhs_numbers, 3); // First three are valid
    
    // Test UK currency formatting and validation
    std::vector<double> amounts = {125.50, 1000.00, 0.01, 999999.99};
    
    for (double amount : amounts) {
        std::string formatted = UKLocalization::format_uk_currency(amount);
        logger->info("Formatted currency: {}", formatted);
        
        EXPECT_TRUE(formatted.find("£") == 0); // Starts with £
        EXPECT_TRUE(formatted.find(".") != std::string::npos); // Contains decimal point
    }
    
    // Test UK temperature conversion
    std::vector<double> celsius_temps = {0.0, 36.5, 100.0, -10.0};
    
    for (double celsius : celsius_temps) {
        double fahrenheit = UKLocalization::celsius_to_fahrenheit(celsius);
        double back_to_celsius = UKLocalization::fahrenheit_to_celsius(fahrenheit);
        
        logger->info("Temperature conversion: {}°C = {}°F", celsius, fahrenheit);
        
        EXPECT_NEAR(celsius, back_to_celsius, 0.01); // Round-trip conversion
    }
}

// Test integration between logging, metrics, and performance monitoring
TEST_F(CommonIntegrationTest, LoggingMetricsPerformanceIntegration) {
    // Set up comprehensive logging
    auto logger = Logger::get("uk_metrics_integration");
    
    // Create multiple sinks for comprehensive logging
    auto console_sink = std::make_shared<ConsoleSink>();
    auto file_sink = std::make_shared<FileSink>(
        (test_dir_ / "metrics_integration.log").string(),
        1024 * 1024, 3
    );
    auto rotating_sink = std::make_shared<RotatingFileSink>(
        (test_dir_ / "rotating.log").string(),
        1024, 3
    );
    
    // Set formatters
    auto text_formatter = std::make_unique<TextLogFormatter>();
    auto json_formatter = std::make_unique<JsonLogFormatter>();
    json_formatter->set_pretty_print(true);
    
    console_sink->set_formatter(std::make_unique<TextLogFormatter>());
    file_sink->set_formatter(std::move(json_formatter));
    rotating_sink->set_formatter(std::move(text_formatter));
    
    logger->add_sink(console_sink);
    logger->add_sink(file_sink);
    logger->add_sink(rotating_sink);
    logger->set_level(LogLevel::Debug);
    
    // Set up metrics collection
    auto metrics_collector = create_metrics_collector();
    auto metrics_config = get_default_metrics_config();
    metrics_config.enabled = false; // Disable collection thread
    ASSERT_TRUE(metrics_collector->initialize(metrics_config));
    ASSERT_TRUE(create_standard_etl_metrics(*metrics_collector));
    
    // Set up performance monitoring
    auto performance_monitor = std::make_unique<PerformanceMonitor>();
    auto perf_logger = std::make_shared<PerformanceLogger>(logger);
    auto audit_logger = std::make_shared<AuditLogger>(logger);
    
    // Simulate UK NHS ETL processing with integrated logging and metrics
    const int num_operations = 10;
    const int records_per_operation = 100;
    
    logger->info("Starting integrated UK NHS ETL processing test");
    
    for (int op = 0; op < num_operations; ++op) {
        std::string operation_name = "uk_nhs_batch_" + std::to_string(op);
        
        // Start performance timing
        performance_monitor->startOperation(operation_name);
        perf_logger->start_timing(operation_name);
        
        // Log operation start
        std::unordered_map<std::string, std::any> context;
        context["operation_id"] = op;
        context["batch_size"] = records_per_operation;
        context["region"] = std::string("uk");
        
        logger->log_structured(LogLevel::Info, "Starting NHS batch processing", context);
        
        // Simulate processing time
        std::this_thread::sleep_for(std::chrono::milliseconds(10 + op * 2));
        
        // Update metrics during processing
        std::unordered_map<std::string, std::string> labels = {
            {"batch_id", std::to_string(op)},
            {"region", "uk"},
            {"status", "processing"}
        };
        
        metrics_collector->set_gauge("etl_active_connections", op + 1, labels);
        metrics_collector->increment_counter("etl_records_processed_total", records_per_operation, labels);
        metrics_collector->observe_histogram("etl_processing_duration_seconds", 0.01 + op * 0.002, labels);
        
        // Log audit trail
        audit_logger->log_data_access("uk_nhs_patient_data", "read", records_per_operation, "etl_system");
        
        // End performance timing
        auto duration = performance_monitor->endOperation(operation_name);
        perf_logger->end_timing(operation_name, records_per_operation);
        
        // Log operation completion
        context["duration_ms"] = duration * 1000;
        context["records_processed"] = records_per_operation;
        logger->log_structured(LogLevel::Info, "Completed NHS batch processing", context);
        
        // Log resource usage
        perf_logger->log_resource_usage(50.0 + op * 2, 512.0 + op * 50, 10.0 + op);
    }
    
    // Test metrics export
    std::string prometheus_export = metrics_collector->export_metrics("prometheus");
    std::string json_export = metrics_collector->export_metrics("json");
    
    EXPECT_FALSE(prometheus_export.empty());
    EXPECT_FALSE(json_export.empty());
    
    // Verify metrics contain expected UK ETL data
    EXPECT_TRUE(prometheus_export.find("etl_records_processed_total") != std::string::npos);
    EXPECT_TRUE(prometheus_export.find("region=\"uk\"") != std::string::npos);
    
    // Test performance metrics export
    auto perf_metrics = perf_logger->export_metrics();
    EXPECT_FALSE(perf_metrics.empty());
    
    // Verify audit trail
    auto audit_trail = audit_logger->get_audit_trail();
    EXPECT_EQ(audit_trail.size(), num_operations);
    
    // Verify log files were created and contain expected content
    EXPECT_TRUE(std::filesystem::exists(test_dir_ / "metrics_integration.log"));
    EXPECT_TRUE(std::filesystem::exists(test_dir_ / "rotating.log"));
    
    // Check log file content
    std::ifstream log_file(test_dir_ / "metrics_integration.log");
    std::string log_content((std::istreambuf_iterator<char>(log_file)),
                           std::istreambuf_iterator<char>());
    
    EXPECT_TRUE(log_content.find("NHS batch processing") != std::string::npos);
    EXPECT_TRUE(log_content.find("uk_nhs_batch_") != std::string::npos);
    EXPECT_TRUE(log_content.find("\"region\":\"uk\"") != std::string::npos);
    
    logger->info("Integrated UK NHS ETL processing test completed successfully");
}

// Test error handling and recovery in UK healthcare context
TEST_F(CommonIntegrationTest, ErrorHandlingAndRecoveryInUKHealthcareContext) {
    auto logger = Logger::get("uk_error_handling");
    auto console_sink = std::make_shared<ConsoleSink>();
    logger->add_sink(console_sink);
    logger->set_level(LogLevel::Debug);
    
    // Test exception handling with UK healthcare context
    std::vector<std::function<void()>> error_scenarios = {
        // NHS number validation failure
        [&]() {
            try {
                std::string invalid_nhs = "**********";
                if (!MedicalUtils::is_valid_nhs_number(invalid_nhs)) {
                    throw ValidationException(
                        "NHS number checksum validation failed",
                        "uk_nhs_number_checksum", 
                        invalid_nhs
                    );
                }
            } catch (const ValidationException& e) {
                std::unordered_map<std::string, std::any> context;
                context["nhs_number"] = std::string("**********");
                context["error_type"] = std::string("validation");
                logger->log_exception(e, context);
                EXPECT_TRUE(std::string(e.what()).find("NHS number") != std::string::npos);
            }
        },
        
        // UK postcode format error
        [&]() {
            try {
                std::string invalid_postcode = "INVALID";
                if (!ValidationUtils::is_valid_uk_postcode(invalid_postcode)) {
                    throw ValidationException(
                        "UK postcode format validation failed",
                        "uk_postcode_format",
                        invalid_postcode
                    );
                }
            } catch (const ValidationException& e) {
                std::unordered_map<std::string, std::any> context;
                context["postcode"] = std::string("INVALID");
                context["expected_format"] = std::string("XX## #XX");
                logger->log_exception(e, context);
                EXPECT_TRUE(std::string(e.what()).find("postcode") != std::string::npos);
            }
        },
        
        // Database connection error for UK NHS database
        [&]() {
            try {
                throw DatabaseException(
                    "Failed to connect to UK NHS clinical database",
                    "PostgreSQL",
                    28000
                );
            } catch (const DatabaseException& e) {
                std::unordered_map<std::string, std::any> context;
                context["database"] = std::string("uk_nhs_clinical");
                context["region"] = std::string("uk");
                context["retry_count"] = 3;
                logger->log_exception(e, context);
                EXPECT_EQ(e.error_code(), 28000);
                EXPECT_TRUE(std::string(e.what()).find("UK NHS") != std::string::npos);
            }
        },
        
        // Configuration error for UK settings
        [&]() {
            try {
                throw ConfigurationException(
                    "Missing UK locale configuration",
                    "uk_settings.locale"
                );
            } catch (const ConfigurationException& e) {
                std::unordered_map<std::string, std::any> context;
                context["config_section"] = std::string("uk_settings");
                context["missing_key"] = std::string("locale");
                logger->log_exception(e, context);
                EXPECT_TRUE(std::string(e.what()).find("UK locale") != std::string::npos);
            }
        }
    };
    
    // Execute all error scenarios
    for (size_t i = 0; i < error_scenarios.size(); ++i) {
        logger->info("Testing error scenario {}", i + 1);
        error_scenarios[i]();
    }
    
    // Test recovery mechanisms
    logger->info("Testing recovery mechanisms for UK healthcare operations");
    
    // Simulate retry logic for NHS number validation
    std::vector<std::string> nhs_numbers_to_retry = {"**********", "**********"};
    
    for (const auto& nhs_num : nhs_numbers_to_retry) {
        int retry_count = 0;
        const int max_retries = 3;
        bool validation_success = false;
        
        while (retry_count < max_retries && !validation_success) {
            try {
                if (MedicalUtils::is_valid_nhs_number(nhs_num)) {
                    validation_success = true;
                    logger->info("NHS number {} validated successfully on attempt {}", nhs_num, retry_count + 1);
                } else {
                    throw ValidationException("NHS number validation failed", "nhs_checksum", nhs_num);
                }
            } catch (const ValidationException& e) {
                retry_count++;
                logger->warn("NHS number {} validation failed, attempt {} of {}", nhs_num, retry_count, max_retries);
                
                if (retry_count >= max_retries) {
                    logger->error("NHS number {} validation failed after {} attempts", nhs_num, max_retries);
                }
                
                // Simulate retry delay
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
        
        if (nhs_num == "**********") {
            EXPECT_TRUE(validation_success);
        } else {
            EXPECT_FALSE(validation_success);
        }
    }
    
    logger->info("Error handling and recovery test completed");
}

} // namespace omop::common::integration::test