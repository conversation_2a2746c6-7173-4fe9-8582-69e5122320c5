#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <stdexcept>
#include "extract/database_connector.h"
#include "common/logging.h"
#include "common/exceptions.h"

#ifdef OMOP_HAS_POSTGRESQL
#include "extract/postgresql_connector.h"
#endif

#ifdef OMOP_HAS_MYSQL
#include "extract/mysql_connector.h"
#endif

namespace omop::test {

/**
 * @brief Database connection factory for integration tests
 * 
 * This factory creates real database connections to Docker containers
 * for integration testing. It supports PostgreSQL and MySQL databases
 * and uses environment variables or defaults for connection parameters.
 */
class DatabaseConnectionFactory {
public:
    /**
     * @brief Database types supported by the factory
     */
    enum class DatabaseType {
        PostgreSQL,
        MySQL
    };

    /**
     * @brief Connection configuration for different database instances
     */
    struct ConnectionConfig {
        std::string host;
        int port;
        std::string database;
        std::string username;
        std::string password;
        int connect_timeout = 30;
        int query_timeout = 60;
        std::unordered_map<std::string, std::string> additional_options;
    };

    /**
     * @brief Create connection to clinical PostgreSQL database container
     * @return Database connection
     * @throws common::DatabaseException if connection fails
     */
    static std::unique_ptr<extract::IDatabaseConnection> createClinicalConnection();

    /**
     * @brief Create connection to OMOP CDM PostgreSQL database container
     * @return Database connection
     * @throws common::DatabaseException if connection fails
     */
    static std::unique_ptr<extract::IDatabaseConnection> createOmopConnection();

    /**
     * @brief Create connection to MySQL test database container
     * @return Database connection
     * @throws common::DatabaseException if connection fails
     */
    static std::unique_ptr<extract::IDatabaseConnection> createMySQLConnection();

    /**
     * @brief Create connection to test PostgreSQL database container
     * @return Database connection
     * @throws common::DatabaseException if connection fails
     */
    static std::unique_ptr<extract::IDatabaseConnection> createTestConnection();

    /**
     * @brief Create connection with custom configuration
     * @param type Database type
     * @param config Connection configuration
     * @return Database connection
     * @throws common::DatabaseException if connection fails
     */
    static std::unique_ptr<extract::IDatabaseConnection> createConnection(
        DatabaseType type, const ConnectionConfig& config);

    /**
     * @brief Get default clinical database configuration
     * @return Connection configuration for clinical database
     */
    static ConnectionConfig getClinicalConfig();

    /**
     * @brief Get default OMOP database configuration
     * @return Connection configuration for OMOP database
     */
    static ConnectionConfig getOmopConfig();

    /**
     * @brief Get default MySQL database configuration
     * @return Connection configuration for MySQL database
     */
    static ConnectionConfig getMySQLConfig();

    /**
     * @brief Get default test database configuration
     * @return Connection configuration for test database
     */
    static ConnectionConfig getTestConfig();

    /**
     * @brief Test database connectivity
     * @param type Database type to test
     * @return true if connection successful, false otherwise
     */
    static bool testConnection(DatabaseType type);

    /**
     * @brief Get environment variable or default value
     * @param env_var Environment variable name
     * @param default_value Default value if environment variable not set
     * @return Environment variable value or default
     */
    static std::string getEnvOrDefault(const std::string& env_var, const std::string& default_value);

    /**
     * @brief Convert ConnectionConfig to IDatabaseConnection::ConnectionParams
     * @param config Connection configuration
     * @return Connection parameters
     */
    static extract::IDatabaseConnection::ConnectionParams configToParams(
        const ConnectionConfig& config);

private:
    /**
     * @brief Create PostgreSQL connection with configuration
     * @param config Connection configuration
     * @return PostgreSQL connection
     */
    static std::unique_ptr<extract::IDatabaseConnection> createPostgreSQLConnection(
        const ConnectionConfig& config);

    /**
     * @brief Create MySQL connection with configuration
     * @param config Connection configuration
     * @return MySQL connection
     */
    static std::unique_ptr<extract::IDatabaseConnection> createMySQLConnectionImpl(
        const ConnectionConfig& config);

private:
    /**
     * @brief Get logger instance (lazy initialization)
     * @return Logger instance
     */
    static std::shared_ptr<common::Logger> getLogger();

    static std::shared_ptr<common::Logger> logger_;
};

/**
 * @brief RAII helper for database connections in tests
 * 
 * Automatically manages database connection lifecycle and provides
 * convenient methods for common test operations.
 */
class TestDatabaseConnection {
public:
    /**
     * @brief Constructor with database type
     * @param type Database type to connect to
     */
    explicit TestDatabaseConnection(DatabaseConnectionFactory::DatabaseType type);

    /**
     * @brief Constructor with custom configuration
     * @param type Database type
     * @param config Connection configuration
     */
    TestDatabaseConnection(DatabaseConnectionFactory::DatabaseType type, 
                          const DatabaseConnectionFactory::ConnectionConfig& config);

    /**
     * @brief Destructor - automatically disconnects
     */
    ~TestDatabaseConnection();

    // Delete copy operations
    TestDatabaseConnection(const TestDatabaseConnection&) = delete;
    TestDatabaseConnection& operator=(const TestDatabaseConnection&) = delete;

    // Allow move operations
    TestDatabaseConnection(TestDatabaseConnection&&) = default;
    TestDatabaseConnection& operator=(TestDatabaseConnection&&) = default;

    /**
     * @brief Get the database connection
     * @return Database connection pointer
     */
    extract::IDatabaseConnection* get() const { return connection_.get(); }

    /**
     * @brief Get the database connection (operator->)
     * @return Database connection pointer
     */
    extract::IDatabaseConnection* operator->() const { return connection_.get(); }

    /**
     * @brief Check if connection is valid and connected
     * @return true if connected, false otherwise
     */
    bool isConnected() const;

    /**
     * @brief Execute SQL query and return result count
     * @param sql SQL query
     * @return Number of rows affected/returned
     */
    size_t executeQuery(const std::string& sql);

    /**
     * @brief Execute SQL update and return affected rows
     * @param sql SQL update statement
     * @return Number of rows affected
     */
    size_t executeUpdate(const std::string& sql);

    /**
     * @brief Check if table exists
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return true if table exists
     */
    bool tableExists(const std::string& table_name, const std::string& schema = "");

    /**
     * @brief Get row count from table
     * @param table_name Table name
     * @param schema Schema name (optional)
     * @return Number of rows
     */
    size_t getRowCount(const std::string& table_name, const std::string& schema = "");

    /**
     * @brief Clear all data from table
     * @param table_name Table name
     * @param schema Schema name (optional)
     */
    void clearTable(const std::string& table_name, const std::string& schema = "");

private:
    std::unique_ptr<extract::IDatabaseConnection> connection_;
    std::shared_ptr<common::Logger> logger_;
};

} // namespace omop::test
