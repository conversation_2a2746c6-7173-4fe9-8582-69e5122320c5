#pragma once

/**
 * @file mock_loader_utilities.h
 * @brief Mock loader utilities for integration testing
 *
 * This file contains mock implementations for loader components used in integration tests.
 * Only included when test dependencies are enabled.
 */

#ifdef OMOP_ENABLE_TEST_DEPENDENCIES

#include <string>
#include <memory>
#include <unordered_map>
#include <vector>
#include <chrono>
#include <any>

namespace omop::test::helpers {

/**
 * @brief Mock HTTP client for testing
 */
class MockHttpClient {
public:
    struct MockResponse {
        bool success{true};
        int status_code{200};
        std::string body{"Mock response"};
        std::unordered_map<std::string, std::string> headers;
    };

    /**
     * @brief Set mock response for a specific endpoint
     * @param endpoint Endpoint to mock
     * @param response Mock response to return
     */
    void set_mock_response(const std::string& endpoint, const MockResponse& response);

    /**
     * @brief Get mock response for a specific endpoint
     * @param endpoint Endpoint to get mock response for
     * @return MockResponse Mock response for the endpoint
     */
    MockResponse get_mock_response(const std::string& endpoint) const;

    /**
     * @brief Clear all mock responses
     */
    void clear_mock_responses();

    /**
     * @brief Check if endpoint has a mock response
     * @param endpoint Endpoint to check
     * @return true if endpoint has a mock response
     */
    bool has_mock_response(const std::string& endpoint) const;

private:
    std::unordered_map<std::string, MockResponse> mock_responses_;
};

/**
 * @brief Mock S3 client for testing
 */
class MockS3Client {
public:
    struct MockS3Object {
        std::string key;
        std::string data;
        std::string etag;
        std::string upload_id;
        std::chrono::system_clock::time_point last_modified;
    };

    /**
     * @brief Upload object to mock S3
     * @param bucket Bucket name
     * @param key Object key
     * @param data Object data
     * @return true if upload successful
     */
    bool upload_object(const std::string& bucket, const std::string& key, const std::string& data);

    /**
     * @brief Get object from mock S3
     * @param bucket Bucket name
     * @param key Object key
     * @return MockS3Object Object if found, empty object if not found
     */
    MockS3Object get_object(const std::string& bucket, const std::string& key) const;

    /**
     * @brief Start multipart upload
     * @param bucket Bucket name
     * @param key Object key
     * @return std::string Upload ID
     */
    std::string start_multipart_upload(const std::string& bucket, const std::string& key);

    /**
     * @brief Upload part
     * @param bucket Bucket name
     * @param key Object key
     * @param upload_id Upload ID
     * @param part_number Part number
     * @param data Part data
     * @return std::string ETag
     */
    std::string upload_part(const std::string& bucket, const std::string& key, 
                           const std::string& upload_id, int part_number, const std::string& data);

    /**
     * @brief Complete multipart upload
     * @param bucket Bucket name
     * @param key Object key
     * @param upload_id Upload ID
     * @param parts Vector of part ETags
     * @return true if completion successful
     */
    bool complete_multipart_upload(const std::string& bucket, const std::string& key,
                                 const std::string& upload_id, const std::vector<std::string>& parts);

    /**
     * @brief Clear all mock S3 data
     */
    void clear_mock_data();

private:
    std::unordered_map<std::string, std::unordered_map<std::string, MockS3Object>> mock_buckets_;
    std::unordered_map<std::string, std::string> upload_ids_;
    int upload_id_counter_{0};
};

/**
 * @brief Test data generator
 */
class TestDataGenerator {
public:
    /**
     * @brief Generate mock HTTP endpoint
     * @return std::string Mock HTTP endpoint
     */
    static std::string generate_mock_http_endpoint();

    /**
     * @brief Generate mock S3 endpoint
     * @return std::string Mock S3 endpoint
     */
    static std::string generate_mock_s3_endpoint();

    /**
     * @brief Generate mock S3 bucket name
     * @return std::string Mock S3 bucket name
     */
    static std::string generate_mock_s3_bucket();

    /**
     * @brief Generate test credentials
     * @return std::pair<std::string, std::string> Access key and secret key
     */
    static std::pair<std::string, std::string> generate_test_credentials();

    /**
     * @brief Generate test data
     * @param size Size of data to generate
     * @return std::string Generated test data
     */
    static std::string generate_test_data(size_t size);
};

} // namespace omop::test::helpers

#endif // OMOP_ENABLE_TEST_DEPENDENCIES
