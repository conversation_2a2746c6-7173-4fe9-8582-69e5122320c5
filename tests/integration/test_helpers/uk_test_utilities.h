// UK-specific test utilities and data generators
#pragma once

#include <string>
#include <vector>
#include <random>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <regex>
#include <cstdint>

namespace omop::test::uk {

/**
 * @brief Comprehensive UK test data generator with consistent implementations
 * 
 * This class consolidates all UK-specific generator functions that were previously
 * duplicated across various integration test files. It provides consistent and
 * maintainable UK test data generation utilities.
 */
class UKTestDataGenerator {
public:
    UKTestDataGenerator(unsigned int seed = 42) : rng_(seed), initial_seed_(seed) {}
    
    // ============================================================================
    // NHS Number Generation
    // ============================================================================
    
    /**
     * @brief Generate NHS number with valid checksum (10 digits)
     * @param id Base identifier for generating unique NHS numbers
     * @return std::string Valid NHS number
     */
    std::string generateNHSNumber(int64_t id) {
        // Generate a valid NHS number with proper checksum
        // Start with a base 9-digit number that avoids check digit 10
        int64_t base_number = 450557710 + (id % 999999); // Keep within reasonable range
        
        std::string base_str = std::to_string(base_number);
        while (base_str.length() < 9) {
            base_str = "0" + base_str;
        }
        
        // Calculate check digit using NHS algorithm
        int sum = 0;
        for (int i = 0; i < 9; ++i) {
            int digit = base_str[i] - '0';
            int weight = 10 - i;
            sum += digit * weight;
        }
        
        int remainder = sum % 11;
        int check_digit = (11 - remainder) % 11;
        
        // NHS doesn't use check digit 10, so we need to adjust the base number
        if (check_digit == 10) {
            // Try to find a valid base number by incrementing
            for (int attempt = 1; attempt <= 10; ++attempt) {
                base_number++;
                base_str = std::to_string(base_number);
                while (base_str.length() < 9) {
                    base_str = "0" + base_str;
                }
                
                // Recalculate check digit
                sum = 0;
                for (int i = 0; i < 9; ++i) {
                    int digit = base_str[i] - '0';
                    int weight = 10 - i;
                    sum += digit * weight;
                }
                
                remainder = sum % 11;
                check_digit = (11 - remainder) % 11;
                
                if (check_digit != 10) {
                    break; // Found a valid check digit
                }
            }
            
            // If we still have 10, use 0 as fallback
            if (check_digit == 10) {
                check_digit = 0;
            }
        }
        
        return base_str + std::to_string(check_digit);
    }
    
    /**
     * @brief Generate NHS number with valid checksum (alternative implementation)
     * @param index Index for generating unique NHS numbers
     * @return std::string Valid NHS number
     */
    std::string generateNHSNumberFromIndex(size_t index) {
        // Generate a valid NHS number format (10 digits with check digit)
        std::ostringstream oss;
        oss << std::setfill('0') << std::setw(9) << (********** + index % 999999999);
        std::string base = oss.str();
        
        // Calculate check digit using NHS algorithm
        int total = 0;
        for (int i = 0; i < 9; ++i) {
            total += (base[i] - '0') * (10 - i);
        }
        int check_digit = 11 - (total % 11);
        if (check_digit == 11) check_digit = 0;
        if (check_digit == 10) check_digit = 1; // Invalid, use 1 instead
        
        return base + std::to_string(check_digit);
    }
    
    /**
     * @brief Generate NHS number with valid checksum (formatted version)
     * @param index Index for generating unique NHS numbers
     * @return std::string Valid NHS number in ABC-DEF-GHIJ format
     */
    std::string generateNHSNumberFormatted(size_t index) {
        // NHS number format: ABC-DEF-GHIJ (10 digits with check digit)
        std::ostringstream oss;
        oss << std::setfill('0') << std::setw(3) << (450 + index % 100)
            << std::setw(3) << (557 + index % 100)  
            << std::setw(4) << (7100 + index);
        return oss.str();
    }
    
    /**
     * @brief Generate NHS number with valid checksum (base number version)
     * @param base_number Base 9-digit number
     * @return std::string Valid NHS number
     */
    std::string generateNHSNumberFromBase(long base_number) {
        std::string nhs = std::to_string(base_number);
        while (nhs.length() < 9) {
            nhs = "0" + nhs;
        }
        
        // Calculate check digit using NHS algorithm
        int sum = 0;
        for (int i = 0; i < 9; ++i) {
            sum += (nhs[i] - '0') * (10 - i);
        }
        int check_digit = 11 - (sum % 11);
        if (check_digit == 11) check_digit = 0;
        if (check_digit == 10) check_digit = 0;
        
        return nhs + std::to_string(check_digit);
    }
    
    // ============================================================================
    // UK Postcode Generation
    // ============================================================================
    
    /**
     * @brief Generate UK postcode from index
     * @param id Base identifier for generating unique postcodes
     * @return std::string Valid UK postcode
     */
    std::string generateUKPostcode(int64_t id) {
        // UK postcode patterns - all valid formats
        std::vector<std::string> patterns = {
            "SW1A 1AA", "W1A 0AX", "M1 1AA", "B33 8TH", "CR2 6XH", "DN55 1PT",
            "EH1 1YZ", "L1 8JQ", "S1 2HE", "NE1 4ST", "CF10 3AT", "BT1 5GS"
        };
        
        size_t pattern_index = id % patterns.size();
        std::string postcode = patterns[pattern_index];
        
        // Add some variation to the number part while maintaining valid format
        if (postcode.find("1AA") != std::string::npos) {
            int variation = (id % 9) + 1;
            postcode = std::regex_replace(postcode, std::regex("1AA"), std::to_string(variation) + "AA");
        }
        
        return postcode;
    }
    
    /**
     * @brief Generate UK postcode from index (alternative implementation)
     * @param index Index for generating unique postcodes
     * @return std::string Valid UK postcode
     */
    std::string generateUKPostcodeFromIndex(size_t index) {
        std::vector<std::string> uk_postcodes = {
            "SW1A 1AA", "W1A 0AX", "M1 1AA", "B33 8TH", "WV99 1AA",
            "L1 8JQ", "S1 2HE", "NE1 4ST", "CF10 3AT", "EH1 2NG",
            "BT1 5GS", "G1 1RE", "CB2 1TN", "OX1 2JD", "SE1 7JN"
        };
        return uk_postcodes[index % uk_postcodes.size()];
    }
    
    /**
     * @brief Generate UK postcode with custom components
     * @param area Area code (e.g., "SW", "EC")
     * @param district District number
     * @param sector Sector number
     * @param unit Unit letters
     * @return std::string Valid UK postcode
     */
    std::string generateUKPostcodeFromComponents(const std::string& area, int district, int sector, const std::string& unit) {
        return area + std::to_string(district) + " " + std::to_string(sector) + unit;
    }
    
    /**
     * @brief Generate UK postcode with random variation
     * @return std::string Valid UK postcode
     */
    std::string generateUKPostcodeRandom() {
        static const std::vector<std::string> areas = {
            "SW", "W", "EC", "N", "E", "SE", "NW", "WC",  // London
            "B", "M", "L", "G", "EH", "CF", "BT",          // Major cities
            "OX", "CB", "BA", "BS", "BH", "BR", "RG"       // Other areas
        };
        
        std::uniform_int_distribution<> area_dist(0, areas.size() - 1);
        std::uniform_int_distribution<> num_dist(1, 99);
        std::uniform_int_distribution<> letter_dist(0, 25);
        
        std::string postcode = areas[area_dist(rng_)];
        postcode += std::to_string(num_dist(rng_));
        if (rng_() % 2 == 0) {
            postcode += static_cast<char>('A' + letter_dist(rng_));
        }
        postcode += " ";
        postcode += std::to_string(rng_() % 10);
        postcode += static_cast<char>('A' + letter_dist(rng_));
        postcode += static_cast<char>('A' + letter_dist(rng_));
        
        return postcode;
    }
    
    // ============================================================================
    // UK National Insurance Number Generation
    // ============================================================================
    
    /**
     * @brief Generate UK National Insurance number
     * @param index Index for generating unique NI numbers
     * @return std::string Valid UK NI number
     */
    std::string generateUKNationalInsurance(int index) {
        std::ostringstream ni;
        ni << "QQ " << std::setw(2) << std::setfill('0') << (12 + (index % 88)) << " "
           << std::setw(2) << std::setfill('0') << (34 + (index % 66)) << " "
           << std::setw(2) << std::setfill('0') << (56 + (index % 44)) << " "
           << static_cast<char>('A' + (index % 26));
        return ni.str();
    }
    
    // ============================================================================
    // UK Personal Data Generation
    // ============================================================================
    
    /**
     * @brief Generate UK forename
     * @param index Index for generating unique forenames
     * @return std::string UK forename
     */
    std::string generateUKForename(size_t index) {
        std::vector<std::string> uk_forenames = {
            "Oliver", "Amelia", "George", "Isla", "Arthur", "Ava", "Noah", "Mia",
            "Muhammad", "Sophia", "Leo", "Grace", "Harry", "Lily", "Oscar", "Emily",
            "Archie", "Olivia", "Henry", "Ella", "Theodore", "Freya", "Jack", "Florence"
        };
        return uk_forenames[index % uk_forenames.size()];
    }
    
    /**
     * @brief Generate UK surname
     * @param index Index for generating unique surnames
     * @return std::string UK surname
     */
    std::string generateUKSurname(size_t index) {
        std::vector<std::string> uk_surnames = {
            "Smith", "Jones", "Taylor", "Brown", "Williams", "Wilson", "Johnson",
            "Davies", "Robinson", "Wright", "Thompson", "Evans", "Walker", "White",
            "Roberts", "Green", "Hall", "Wood", "Jackson", "Clarke"
        };
        return uk_surnames[index % uk_surnames.size()];
    }
    
    /**
     * @brief Generate UK date of birth
     * @param index Index for generating unique dates
     * @return std::string UK formatted date (DD/MM/YYYY)
     */
    std::string generateUKDateOfBirth(size_t index) {
        // Generate dates in UK format (DD/MM/YYYY)
        int year = 1950 + (index % 70);
        int month = 1 + (index % 12);
        int day = 1 + (index % 28);
        
        std::ostringstream oss;
        oss << std::setfill('0') << std::setw(2) << day << "/"
            << std::setw(2) << month << "/" << year;
        return oss.str();
    }
    
    /**
     * @brief Generate UK GP practice code
     * @param index Index for generating unique practice codes
     * @return std::string UK GP practice code
     */
    std::string generateGPPracticeCode(size_t index) {
        // UK GP practice codes (usually 6 characters)
        std::ostringstream oss;
        oss << "P" << std::setfill('0') << std::setw(5) << (10000 + index % 50000);
        return oss.str();
    }
    
    // ============================================================================
    // UK Contact Information Generation
    // ============================================================================
    
    /**
     * @brief Generate UK phone number
     * @param formatted Whether to format the phone number
     * @return std::string UK phone number
     */
    std::string generateUKPhoneNumber(bool formatted = true) {
        static const std::vector<std::string> area_codes = {
            "020", "0121", "0131", "0141", "0151", "0161",  // Major cities
            "01223", "01865", "01273", "01225"              // Other areas
        };
        
        std::uniform_int_distribution<> area_dist(0, area_codes.size() - 1);
        std::uniform_int_distribution<> digit_dist(0, 9);
        
        std::string phone = area_codes[area_dist(rng_)];
        
        // Generate remaining digits
        int remaining_digits = 11 - phone.length();
        std::string number_part;
        for (int i = 0; i < remaining_digits; ++i) {
            number_part += std::to_string(digit_dist(rng_));
        }
        
        if (formatted) {
            if (phone.length() == 3) {  // London format
                return phone + " " + number_part.substr(0, 4) + " " + number_part.substr(4);
            } else {  // Other formats
                return phone + " " + number_part;
            }
        }
        
        return phone + number_part;
    }
    
    // ============================================================================
    // UK Date and Time Generation
    // ============================================================================
    
    /**
     * @brief Generate UK date string
     * @param year_min Minimum year
     * @param year_max Maximum year
     * @return std::string UK formatted date (DD/MM/YYYY)
     */
    std::string generateUKDate(int year_min = 1920, int year_max = 2023) {
        std::uniform_int_distribution<> year_dist(year_min, year_max);
        std::uniform_int_distribution<> month_dist(1, 12);
        std::uniform_int_distribution<> day_dist(1, 28);  // Safe for all months
        
        int day = day_dist(rng_);
        int month = month_dist(rng_);
        int year = year_dist(rng_);
        
        std::ostringstream oss;
        oss << std::setfill('0') << std::setw(2) << day << "/"
            << std::setfill('0') << std::setw(2) << month << "/"
            << year;
        
        return oss.str();
    }
    
    // ============================================================================
    // UK Financial and Measurement Generation
    // ============================================================================
    
    /**
     * @brief Generate UK currency amount
     * @param min Minimum amount
     * @param max Maximum amount
     * @return std::string UK formatted currency (£X.XX)
     */
    std::string generateUKCurrencyAmount(double min = 0.01, double max = 10000.00) {
        std::uniform_real_distribution<> amount_dist(min, max);
        double amount = amount_dist(rng_);
        
        std::ostringstream oss;
        oss << "£" << std::fixed << std::setprecision(2) << amount;
        return oss.str();
    }
    
    /**
     * @brief Generate temperature in Celsius
     * @param min Minimum temperature
     * @param max Maximum temperature
     * @return double Temperature in Celsius
     */
    double generateTemperatureCelsius(double min = 35.0, double max = 42.0) {
        std::uniform_real_distribution<> temp_dist(min, max);
        return std::round(temp_dist(rng_) * 10) / 10;  // Round to 1 decimal place
    }
    
    // ============================================================================
    // Utility Methods
    // ============================================================================
    
    /**
     * @brief Set random seed for reproducible results
     * @param seed Random seed value
     */
    void setSeed(unsigned int seed) {
        rng_.seed(seed);
        initial_seed_ = seed;
    }
    
    /**
     * @brief Get current random seed
     * @return unsigned int Current random seed
     */
    unsigned int getSeed() const {
        // Note: std::mt19937 doesn't provide a way to retrieve the current seed
        // This method returns the initial seed that was set
        return initial_seed_;
    }

private:
    std::mt19937 rng_;
    unsigned int initial_seed_;
};

// ============================================================================
// Convenience Functions (for backward compatibility)
// ============================================================================

/**
 * @brief Generate NHS number (convenience function)
 * @param id Base identifier
 * @return std::string NHS number
 */
inline std::string generateNHSNumber(int64_t id) {
    static UKTestDataGenerator generator;
    return generator.generateNHSNumber(id);
}

/**
 * @brief Generate UK postcode (convenience function)
 * @param id Base identifier
 * @return std::string UK postcode
 */
inline std::string generateUKPostcode(int64_t id) {
    static UKTestDataGenerator generator;
    return generator.generateUKPostcode(id);
}

/**
 * @brief Generate UK National Insurance number (convenience function)
 * @param index Index for generation
 * @return std::string UK NI number
 */
inline std::string generateUKNationalInsurance(int index) {
    static UKTestDataGenerator generator;
    return generator.generateUKNationalInsurance(index);
}

} // namespace omop::test::uk 