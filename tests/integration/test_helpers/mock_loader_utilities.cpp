#include "mock_loader_utilities.h"

#ifdef OMOP_ENABLE_TEST_DEPENDENCIES

#include <random>
#include <sstream>
#include <iomanip>
#include <chrono>

namespace omop::test::helpers {

// MockHttpClient Implementation
void MockHttpClient::set_mock_response(const std::string& endpoint, const MockResponse& response) {
    mock_responses_[endpoint] = response;
}

MockHttpClient::MockResponse MockHttpClient::get_mock_response(const std::string& endpoint) const {
    auto it = mock_responses_.find(endpoint);
    if (it != mock_responses_.end()) {
        return it->second;
    }
    // Return default response if no mock is set
    return MockResponse{};
}

void MockHttpClient::clear_mock_responses() {
    mock_responses_.clear();
}

bool MockHttpClient::has_mock_response(const std::string& endpoint) const {
    return mock_responses_.find(endpoint) != mock_responses_.end();
}

// MockS3Client Implementation
bool MockS3Client::upload_object(const std::string& bucket, const std::string& key, const std::string& data) {
    MockS3Object obj;
    obj.key = key;
    obj.data = data;
    obj.etag = "\"mock-etag-" + key + "\"";
    obj.last_modified = std::chrono::system_clock::now();
    
    mock_buckets_[bucket][key] = obj;
    return true;
}

MockS3Client::MockS3Object MockS3Client::get_object(const std::string& bucket, const std::string& key) const {
    auto bucket_it = mock_buckets_.find(bucket);
    if (bucket_it != mock_buckets_.end()) {
        auto obj_it = bucket_it->second.find(key);
        if (obj_it != bucket_it->second.end()) {
            return obj_it->second;
        }
    }
    return MockS3Object{};
}

std::string MockS3Client::start_multipart_upload(const std::string& bucket, const std::string& key) {
    std::string upload_id = "mock-upload-" + std::to_string(++upload_id_counter_) + "-" + key;
    upload_ids_[upload_id] = bucket + "/" + key;
    return upload_id;
}

std::string MockS3Client::upload_part(const std::string& bucket, const std::string& key, 
                                     const std::string& upload_id, int part_number, const std::string& data) {
    // Store the part data (in a real implementation, this would be tracked)
    std::string etag = "\"mock-part-etag-" + std::to_string(part_number) + "-" + key + "\"";
    return etag;
}

bool MockS3Client::complete_multipart_upload(const std::string& bucket, const std::string& key,
                                            const std::string& upload_id, const std::vector<std::string>& parts) {
    // In a real implementation, this would combine all parts
    // For mock purposes, we just mark it as complete
    upload_ids_.erase(upload_id);
    return true;
}

void MockS3Client::clear_mock_data() {
    mock_buckets_.clear();
    upload_ids_.clear();
    upload_id_counter_ = 0;
}

// TestDataGenerator Implementation
std::string TestDataGenerator::generate_mock_http_endpoint() {
    return "mock://test";
}

std::string TestDataGenerator::generate_mock_s3_endpoint() {
    return "mock://s3";
}

std::string TestDataGenerator::generate_mock_s3_bucket() {
    return "mock-bucket";
}

std::pair<std::string, std::string> TestDataGenerator::generate_test_credentials() {
    return {"test_key", "test_secret"};
}

std::string TestDataGenerator::generate_test_data(size_t size) {
    static const std::string charset = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    static std::mt19937 gen([]() -> std::mt19937::result_type {
        try {
            return std::random_device{}();
        } catch (...) {
            return static_cast<std::mt19937::result_type>(
                std::chrono::steady_clock::now().time_since_epoch().count()
            );
        }
    }());
    static std::uniform_int_distribution<> dis(0, charset.size() - 1);
    
    std::string result;
    result.reserve(size);
    
    for (size_t i = 0; i < size; ++i) {
        result += charset[dis(gen)];
    }
    
    return result;
}

} // namespace omop::test::helpers

#endif // OMOP_ENABLE_TEST_DEPENDENCIES
