# Test Configuration for Integration Tests
# Basic configuration for testing pipeline functionality

pipeline:
  batch_size: 1000
  max_parallel_batches: 2
  error_threshold: 0.01
  stop_on_error: false
  enable_checkpointing: true
  checkpoint_dir: "/tmp/omop-etl/checkpoints"
  stages:
    - name: "extract"
      type: "extractor"
      config:
        type: "csv"
        file_path: "test_data/csv/patients.csv"
        delimiter: ","
        has_header: true
        processing_delay_ms: 500
        per_record_delay_ms: 1
    - name: "transform"
      type: "transformer"
      config:
        type: "identity"
        validate_records: true
    - name: "load"
      type: "loader"
      config:
        type: "database"
        host: "omop-db"
        port: 5432
        database: "test_db"
        username: "test_user"
        password: "test_pass"
        batch_size: 1000
