# Enhanced Validation Engine Configuration
# Demonstrates comprehensive validation capabilities including:
# - Rule-based validation system
# - Data quality metrics
# - Validation result caching
# - Advanced condition parsing
# - Performance optimization

validation_engine:
  cache_max_size: 50000
  strict_mode: true
  enable_metrics: true
  enable_caching: true
  validation_timeout_ms: 5000
  max_errors_per_record: 100
  continue_on_validation_error: false

validation_rules:
  # Person table comprehensive validations
  - rule_id: "person_id_required"
    rule_name: "Person ID Required"
    field: "person_id"
    type: "required"
    description: "Person ID must be present and non-null"
    error_message: "Person ID is required and cannot be null"
    severity: "error"
    enabled: true
    priority: 1
    cache_result: true
    cache_ttl: 600  # 10 minutes
    
  - rule_id: "person_id_positive"
    rule_name: "Person ID Positive Integer"
    field: "person_id"
    type: "range"
    description: "Person ID must be a positive integer"
    error_message: "Person ID must be a positive integer greater than 0"
    severity: "error"
    enabled: true
    priority: 2
    parameters:
      min_value: 1
      max_value: 999999999
      data_type: "integer"
    cache_result: true
    
  - rule_id: "birth_date_valid_range"
    rule_name: "Birth Date Valid Range"
    field: "birth_datetime"
    type: "date_range"
    description: "Birth date must be within reasonable range"
    error_message: "Birth date must be between 1900-01-01 and current date"
    severity: "error"
    enabled: true
    priority: 3
    parameters:
      min_date: "1900-01-01"
      max_date: "current_date"
      date_format: "%Y-%m-%d"
    cache_result: true
    
  - rule_id: "death_date_after_birth"
    rule_name: "Death Date After Birth"
    field: "death_datetime"
    type: "cross_field"
    description: "Death date must be after birth date"
    error_message: "Death date cannot be before birth date"
    severity: "error"
    enabled: true
    priority: 4
    condition: "death_datetime > birth_datetime"
    dependencies: ["birth_datetime"]
    cache_result: false  # Cross-field validation shouldn't be cached
    
  - rule_id: "gender_valid_concept"
    rule_name: "Gender Valid Concept"
    field: "gender_concept_id"
    type: "vocabulary"
    description: "Gender must be a valid concept from Gender vocabulary"
    error_message: "Gender concept ID must be valid and from Gender vocabulary"
    severity: "error"
    enabled: true
    priority: 5
    parameters:
      vocabulary: "Gender"
      require_standard: true
      validate_concept_id: true
    cache_result: true
    
  - rule_id: "gender_in_allowed_list"
    rule_name: "Gender In Allowed List"
    field: "gender_concept_id"
    type: "in_list"
    description: "Gender must be one of the standard gender concepts"
    error_message: "Gender must be one of: Male (8507), Female (8532), Unknown (0)"
    severity: "error"
    enabled: true
    priority: 6
    parameters:
      allowed_values: [8507, 8532, 0]
      data_type: "integer"
    cache_result: true
    
  # Visit occurrence validations
  - rule_id: "visit_start_required"
    rule_name: "Visit Start Date Required"
    field: "visit_start_datetime"
    type: "required"
    description: "Visit start date/time is required"
    error_message: "Visit start date/time cannot be null"
    severity: "error"
    enabled: true
    priority: 1
    cache_result: true
    
  - rule_id: "visit_end_after_start"
    rule_name: "Visit End After Start"
    field: "visit_end_datetime"
    type: "cross_field"
    description: "Visit end date must be after start date"
    error_message: "Visit end date must be after visit start date"
    severity: "error"
    enabled: true
    priority: 2
    condition: "visit_end_datetime > visit_start_datetime"
    dependencies: ["visit_start_datetime"]
    cache_result: false
    
  - rule_id: "visit_concept_valid"
    rule_name: "Visit Concept Valid"
    field: "visit_concept_id"
    type: "vocabulary"
    description: "Visit concept must be valid"
    error_message: "Visit concept ID must be valid"
    severity: "error"
    enabled: true
    priority: 3
    parameters:
      vocabulary: "Visit"
      require_standard: true
    cache_result: true
    
  # Condition occurrence validations
  - rule_id: "condition_start_required"
    rule_name: "Condition Start Date Required"
    field: "condition_start_datetime"
    type: "required"
    description: "Condition start date is required"
    error_message: "Condition start date cannot be null"
    severity: "error"
    enabled: true
    priority: 1
    cache_result: true
    
  - rule_id: "condition_concept_valid"
    rule_name: "Condition Concept Valid"
    field: "condition_concept_id"
    type: "vocabulary"
    description: "Condition concept must be valid"
    error_message: "Condition concept ID must be valid"
    severity: "error"
    enabled: true
    priority: 2
    parameters:
      vocabulary: "SNOMED"
      require_standard: true
    cache_result: true
    
  # Drug exposure validations
  - rule_id: "drug_start_required"
    rule_name: "Drug Start Date Required"
    field: "drug_exposure_start_datetime"
    type: "required"
    description: "Drug exposure start date is required"
    error_message: "Drug exposure start date cannot be null"
    severity: "error"
    enabled: true
    priority: 1
    cache_result: true
    
  - rule_id: "drug_end_after_start"
    rule_name: "Drug End After Start"
    field: "drug_exposure_end_datetime"
    type: "cross_field"
    description: "Drug end date must be after start date"
    error_message: "Drug exposure end date must be after start date"
    severity: "error"
    enabled: true
    priority: 2
    condition: "drug_exposure_end_datetime > drug_exposure_start_datetime"
    dependencies: ["drug_exposure_start_datetime"]
    cache_result: false
    
  # Measurement validations
  - rule_id: "measurement_value_range"
    rule_name: "Measurement Value Range"
    field: "value_as_number"
    type: "range"
    description: "Measurement value must be within reasonable range"
    error_message: "Measurement value must be between -1000 and 10000"
    severity: "warning"
    enabled: true
    priority: 1
    parameters:
      min_value: -1000.0
      max_value: 10000.0
      data_type: "float"
    cache_result: true
    
  - rule_id: "measurement_unit_valid"
    rule_name: "Measurement Unit Valid"
    field: "unit_concept_id"
    type: "vocabulary"
    description: "Measurement unit must be valid"
    error_message: "Measurement unit concept ID must be valid"
    severity: "warning"
    enabled: true
    priority: 2
    parameters:
      vocabulary: "UCUM"
      require_standard: true
    cache_result: true
    
  # Custom business rule validations
  - rule_id: "age_consistency"
    rule_name: "Age Consistency Check"
    field: "age"
    type: "custom"
    description: "Age must be consistent with birth date"
    error_message: "Calculated age does not match provided age"
    severity: "warning"
    enabled: true
    priority: 10
    condition: "age == calculate_age(birth_datetime)"
    dependencies: ["birth_datetime"]
    cache_result: false
    
  - rule_id: "duplicate_person_check"
    rule_name: "Duplicate Person Check"
    field: "person_id"
    type: "custom"
    description: "Check for potential duplicate person records"
    error_message: "Potential duplicate person record detected"
    severity: "warning"
    enabled: true
    priority: 15
    condition: "check_duplicate_person(person_id, birth_datetime, gender_concept_id)"
    dependencies: ["birth_datetime", "gender_concept_id"]
    cache_result: false

# Advanced condition examples
advanced_conditions:
  # Complex logical conditions
  complex_logic:
    - condition: "age >= 18 && (gender_concept_id == 8507 || gender_concept_id == 8532)"
      description: "Adult with valid gender"
      
    - condition: "visit_start_datetime < current_date && visit_end_datetime > visit_start_datetime"
      description: "Visit dates in valid range"
      
    - condition: "!(is_null(condition_concept_id) && is_null(condition_source_value))"
      description: "Either condition concept or source value must be present"
      
    - condition: "value_as_number > 0 && unit_concept_id in [8713, 8714, 8715]"
      description: "Positive value with valid unit"
      
  # Function-based conditions
  function_conditions:
    - condition: "length(name) >= 2 && length(name) <= 100"
      description: "Name length validation"
      
    - condition: "contains(email, '@') && ends_with(email, '.com')"
      description: "Email format validation"
      
    - condition: "starts_with(icd10_code, 'E') && length(icd10_code) == 7"
      description: "ICD-10 code format validation"
      
    - condition: "is_empty(notes) || length(notes) <= 1000"
      description: "Notes length validation"

# Performance optimization settings
performance_settings:
  batch_validation_size: 1000
  parallel_validation: true
  max_validation_threads: 8
  cache_cleanup_interval: 300  # 5 minutes
  metrics_collection_interval: 60  # 1 minute
  
  # Cache optimization
  cache_strategies:
    - strategy: "field_based"
      description: "Cache validation results by field name and value"
      enabled: true
      
    - strategy: "rule_based"
      description: "Cache validation results by rule ID and parameters"
      enabled: true
      
    - strategy: "cross_field"
      description: "Cache cross-field validation results"
      enabled: false  # Disabled for cross-field validations
      
  # Validation optimization
  validation_optimizations:
    - optimization: "early_exit"
      description: "Stop validation on first error if strict mode enabled"
      enabled: true
      
    - optimization: "rule_prioritization"
      description: "Execute high-priority rules first"
      enabled: true
      
    - optimization: "dependency_resolution"
      description: "Resolve rule dependencies before execution"
      enabled: true

# Data quality thresholds
quality_thresholds:
  overall_quality_score: 95.0  # Minimum acceptable quality score
  field_quality_scores:
    person_id: 99.9
    birth_datetime: 98.0
    gender_concept_id: 97.0
    visit_start_datetime: 96.0
    condition_concept_id: 95.0
    
  error_rate_thresholds:
    critical: 0.01    # 1% critical errors
    high: 0.05        # 5% high severity errors
    medium: 0.10      # 10% medium severity errors
    low: 0.20         # 20% low severity errors
    
  performance_thresholds:
    max_validation_time_ms: 1000
    max_cache_miss_rate: 0.20
    min_cache_hit_rate: 0.80

# Reporting configuration
reporting:
  generate_validation_report: true
  report_format: "html"
  include_metrics: true
  include_performance_data: true
  include_quality_scores: true
  include_error_details: true
  include_recommendations: true
  
  report_sections:
    - "validation_summary"
    - "data_quality_metrics"
    - "performance_metrics"
    - "error_analysis"
    - "rule_effectiveness"
    - "cache_performance"
    - "recommendations"
    
  export_formats:
    - "json"
    - "csv"
    - "xml"
    - "pdf"

# Error handling and recovery
error_handling:
  max_error_count: 10000
  error_threshold: 0.05  # 5% error rate
  continue_on_error: true
  quarantine_invalid_records: true
  detailed_error_reporting: true
  error_categorization: true
  
  error_categories:
    - category: "data_quality"
      description: "Data quality issues"
      severity: "warning"
      auto_fix: false
      
    - category: "business_logic"
      description: "Business logic violations"
      severity: "error"
      auto_fix: false
      
    - category: "system_error"
      description: "System-level errors"
      severity: "critical"
      auto_fix: false
      
    - category: "performance"
      description: "Performance issues"
      severity: "warning"
      auto_fix: true
      
  recovery_strategies:
    - strategy: "retry_validation"
      description: "Retry failed validations with exponential backoff"
      max_retries: 3
      backoff_multiplier: 2.0
      
    - strategy: "fallback_validation"
      description: "Use simplified validation rules as fallback"
      enabled: true
      
    - strategy: "partial_validation"
      description: "Continue validation with available data"
      enabled: true

# Testing and validation
testing:
  enable_mock_mode: true
  enable_test_rules: true
  test_data_generation: true
  
  test_scenarios:
    - scenario: "valid_data"
      description: "Test with completely valid data"
      expected_quality_score: 100.0
      expected_error_count: 0
      
    - scenario: "invalid_data"
      description: "Test with intentionally invalid data"
      expected_quality_score: 50.0
      expected_error_count: 100
      
    - scenario: "edge_cases"
      description: "Test with edge case data"
      expected_quality_score: 80.0
      expected_error_count: 20
      
    - scenario: "performance_stress"
      description: "Test with large dataset for performance"
      expected_quality_score: 95.0
      expected_error_count: 50
