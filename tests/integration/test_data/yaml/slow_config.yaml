# Slow configuration for testing long-running jobs
pipeline:
  batch_size: 100
  max_parallel_batches: 1
  error_threshold: 0.01
  stop_on_error: false
  enable_checkpointing: true
  checkpoint_dir: "/tmp/omop-etl/checkpoints"
  stages:
    - name: "extract"
      type: "extractor"
      config:
        type: "slow"
    - name: "transform"
      type: "transformer"
      config:
        type: "identity"
        validate_records: true
    - name: "load"
      type: "loader"
      config:
        type: "database"
        host: "omop-db"
        port: 5432
        database: "test_db"
        username: "test_user"
        password: "test_pass"
        batch_size: 100 