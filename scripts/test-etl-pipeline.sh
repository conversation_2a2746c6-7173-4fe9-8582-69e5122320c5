#!/bin/bash

# OMOP ETL Pipeline End-to-End Testing Script
# This script demonstrates complete ETL pipeline testing scenarios

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
API_BASE_URL="${API_BASE_URL:-http://localhost:8080/api/v1}"
CONFIG_FILE="${CONFIG_FILE:-config/etl/test_mappings.yaml}"
LOG_FILE="${LOG_FILE:-/tmp/etl-test-$(date +%Y%m%d-%H%M%S).log}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✓ $1${NC}" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ✗ $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠ $1${NC}" | tee -a "$LOG_FILE"
}

# Helper functions
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if required tools are installed
    for tool in curl jq docker-compose; do
        if ! command -v $tool &> /dev/null; then
            log_error "$tool is not installed or not in PATH"
            exit 1
        fi
    done
    
    # Check if configuration file exists
    if [[ ! -f "$PROJECT_ROOT/$CONFIG_FILE" ]]; then
        log_error "Configuration file not found: $CONFIG_FILE"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

start_services() {
    log "Starting required services..."
    
    cd "$PROJECT_ROOT/scripts"

    # Start database services
    docker-compose up -d mysql postgres omop-db redis
    
    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 30
    
    # Check MySQL connection
    if docker-compose exec -T mysql mysqladmin ping -h localhost --silent; then
        log_success "MySQL is ready"
    else
        log_error "MySQL is not ready"
        exit 1
    fi
    
    # Check PostgreSQL connections
    if docker-compose exec -T postgres pg_isready -U clinical_user -d clinical_db; then
        log_success "Source PostgreSQL is ready"
    else
        log_error "Source PostgreSQL is not ready"
        exit 1
    fi

    if docker-compose exec -T omop-db pg_isready -U omop_user -d omop_cdm; then
        log_success "OMOP PostgreSQL is ready"
    else
        log_error "OMOP PostgreSQL is not ready"
        exit 1
    fi
    
    log_success "All services are ready"
}

setup_test_data() {
    log "Setting up test data..."

    cd "$PROJECT_ROOT/scripts"
    
    # Load sample data into MySQL
    if [[ -f "tests/data/sample_clinical_data.sql" ]]; then
        docker-compose exec -T mysql mysql -u root -p"${MYSQL_ROOT_PASSWORD:-root_password}" mysql_clinical_db < ../tests/data/sample_clinical_data.sql
        log_success "Sample clinical data loaded into MySQL"
    else
        log_warning "Sample data file not found, creating minimal test data"
        
        # Create minimal test data
        docker-compose exec -T mysql mysql -u root -p"${MYSQL_ROOT_PASSWORD:-root_password}" mysql_clinical_db << 'EOF'
INSERT INTO test_extract.patients (patient_id, first_name, last_name, birth_date, gender) VALUES
(1001, 'John', 'Doe', '1985-03-15', 'M'),
(1002, 'Jane', 'Smith', '1990-07-22', 'F'),
(1003, 'Bob', 'Johnson', '1975-11-08', 'M');

INSERT INTO test_extract.visits (visit_id, patient_id, visit_date, visit_type, department) VALUES
(2001, 1001, '2024-01-15', 'Outpatient', 'Cardiology'),
(2002, 1002, '2024-01-16', 'Inpatient', 'Surgery'),
(2003, 1003, '2024-01-17', 'Emergency', 'Emergency');
EOF
        log_success "Minimal test data created"
    fi
    
    # Verify OMOP CDM schema exists
    docker-compose exec -T omop-db psql -U omop_user -d omop_cdm -c "\dt" > /dev/null
    log_success "OMOP CDM schema verified"
}

start_api_server() {
    log "Starting ETL API server..."
    
    cd "$PROJECT_ROOT"
    
    # Start API server in background
    if [[ -f "build/omop-etl-api" ]]; then
        ./build/omop-etl-api --config config/api/config.yaml --port 8080 > "$LOG_FILE.api" 2>&1 &
        API_PID=$!
        echo $API_PID > /tmp/etl-api.pid
        
        # Wait for API to be ready
        log "Waiting for API server to be ready..."
        for i in {1..30}; do
            if curl -s "$API_BASE_URL/health" > /dev/null 2>&1; then
                log_success "API server is ready"
                return 0
            fi
            sleep 2
        done
        
        log_error "API server failed to start"
        exit 1
    else
        log_error "API server binary not found. Please build the project first."
        exit 1
    fi
}

test_configuration_validation() {
    log "Testing configuration validation..."
    
    # Test valid configuration
    response=$(curl -s -w "%{http_code}" -X POST "$API_BASE_URL/config/validate" \
        -H "Content-Type: application/json" \
        -d "{\"config_path\": \"$CONFIG_FILE\"}")
    
    http_code="${response: -3}"
    if [[ "$http_code" == "200" ]]; then
        log_success "Configuration validation passed"
    else
        log_error "Configuration validation failed (HTTP $http_code)"
        return 1
    fi
    
    # Test database connections
    response=$(curl -s -w "%{http_code}" -X POST "$API_BASE_URL/config/test-connection" \
        -H "Content-Type: application/json" \
        -d '{
            "connection": {
                "type": "mysql",
                "host": "localhost",
                "port": 3306,
                "database": "clinical_db",
                "username": "clinical_user",
                "password": "clinical_password"
            }
        }')
    
    http_code="${response: -3}"
    if [[ "$http_code" == "200" ]]; then
        log_success "Database connection test passed"
    else
        log_error "Database connection test failed (HTTP $http_code)"
        return 1
    fi
}

test_single_table_etl() {
    local table_name="$1"
    log "Testing ETL for table: $table_name"
    
    # Submit ETL job
    job_response=$(curl -s -X POST "$API_BASE_URL/etl/jobs" \
        -H "Content-Type: application/json" \
        -d "{
            \"name\": \"Test ETL - $table_name\",
            \"description\": \"End-to-end test for $table_name table\",
            \"config_path\": \"$CONFIG_FILE\",
            \"table\": \"$table_name\",
            \"priority\": \"high\",
            \"parameters\": {
                \"batch_size\": 1000,
                \"parallel_threads\": 2
            }
        }")
    
    job_id=$(echo "$job_response" | jq -r '.job_id')
    
    if [[ "$job_id" == "null" || -z "$job_id" ]]; then
        log_error "Failed to create ETL job for $table_name"
        echo "$job_response" | jq '.' | tee -a "$LOG_FILE"
        return 1
    fi
    
    log "ETL job created with ID: $job_id"
    
    # Monitor job progress
    local max_wait=300  # 5 minutes
    local wait_time=0
    
    while [[ $wait_time -lt $max_wait ]]; do
        job_status=$(curl -s "$API_BASE_URL/etl/jobs/$job_id" | jq -r '.state')
        
        case "$job_status" in
            "completed")
                log_success "ETL job for $table_name completed successfully"
                
                # Get job statistics
                stats=$(curl -s "$API_BASE_URL/etl/jobs/$job_id/stats")
                records_loaded=$(echo "$stats" | jq -r '.records_loaded')
                data_quality_score=$(echo "$stats" | jq -r '.data_quality_score')
                
                log "Records loaded: $records_loaded"
                log "Data quality score: $data_quality_score"
                
                return 0
                ;;
            "failed")
                log_error "ETL job for $table_name failed"
                
                # Get error details
                error_msg=$(curl -s "$API_BASE_URL/etl/jobs/$job_id" | jq -r '.error_message')
                log_error "Error: $error_msg"
                
                # Get logs
                curl -s "$API_BASE_URL/etl/jobs/$job_id/logs?level=error" | jq -r '.logs[].message' | tee -a "$LOG_FILE"
                
                return 1
                ;;
            "running"|"pending")
                progress=$(curl -s "$API_BASE_URL/etl/jobs/$job_id/progress" | jq -r '.percentage_complete')
                log "Job progress: ${progress}%"
                ;;
            *)
                log_warning "Unknown job status: $job_status"
                ;;
        esac
        
        sleep 10
        wait_time=$((wait_time + 10))
    done
    
    log_error "ETL job for $table_name timed out"
    return 1
}

test_parallel_etl() {
    log "Testing parallel ETL for multiple tables..."
    
    local tables=("person" "visit_occurrence" "condition_occurrence")
    local job_ids=()
    
    # Submit jobs for all tables
    for table in "${tables[@]}"; do
        job_response=$(curl -s -X POST "$API_BASE_URL/etl/jobs" \
            -H "Content-Type: application/json" \
            -d "{
                \"name\": \"Parallel ETL - $table\",
                \"config_path\": \"$CONFIG_FILE\",
                \"table\": \"$table\",
                \"priority\": \"normal\",
                \"parameters\": {
                    \"batch_size\": 1000,
                    \"parallel_threads\": 1
                }
            }")
        
        job_id=$(echo "$job_response" | jq -r '.job_id')
        if [[ "$job_id" != "null" && -n "$job_id" ]]; then
            job_ids+=("$job_id")
            log "Submitted job for $table: $job_id"
        else
            log_error "Failed to submit job for $table"
        fi
    done
    
    # Monitor all jobs
    local max_wait=600  # 10 minutes
    local wait_time=0
    local completed_jobs=0
    
    while [[ $wait_time -lt $max_wait && $completed_jobs -lt ${#job_ids[@]} ]]; do
        completed_jobs=0
        
        for job_id in "${job_ids[@]}"; do
            job_status=$(curl -s "$API_BASE_URL/etl/jobs/$job_id" | jq -r '.state')
            
            case "$job_status" in
                "completed")
                    ((completed_jobs++))
                    ;;
                "failed")
                    log_error "Job $job_id failed"
                    ;;
            esac
        done
        
        log "Completed jobs: $completed_jobs/${#job_ids[@]}"
        
        if [[ $completed_jobs -eq ${#job_ids[@]} ]]; then
            log_success "All parallel ETL jobs completed successfully"
            return 0
        fi
        
        sleep 15
        wait_time=$((wait_time + 15))
    done
    
    log_error "Parallel ETL jobs timed out or failed"
    return 1
}

validate_results() {
    log "Validating ETL results..."
    
    # Check record counts in OMOP CDM tables
    cd "$PROJECT_ROOT/scripts"

    result=$(docker-compose exec -T omop-db psql -U omop_user -d omop_cdm -t -c "
        SELECT 
            'person' as table_name, COUNT(*) as record_count 
        FROM person
        UNION ALL
        SELECT 
            'visit_occurrence' as table_name, COUNT(*) as record_count 
        FROM visit_occurrence
        UNION ALL
        SELECT 
            'condition_occurrence' as table_name, COUNT(*) as record_count 
        FROM condition_occurrence;
    ")
    
    log "OMOP CDM record counts:"
    echo "$result" | while read -r line; do
        if [[ -n "$line" ]]; then
            log "  $line"
        fi
    done
    
    # Check for data quality issues
    quality_check=$(docker-compose exec -T omop-db psql -U omop_user -d omop_cdm -t -c "
        SELECT 
            COUNT(*) as invalid_persons
        FROM person 
        WHERE person_id IS NULL 
           OR gender_concept_id IS NULL 
           OR birth_datetime IS NULL;
    ")
    
    invalid_count=$(echo "$quality_check" | tr -d ' ')
    if [[ "$invalid_count" == "0" ]]; then
        log_success "Data quality validation passed"
    else
        log_warning "Found $invalid_count invalid person records"
    fi
}

cleanup() {
    log "Cleaning up..."
    
    # Stop API server
    if [[ -f "/tmp/etl-api.pid" ]]; then
        api_pid=$(cat /tmp/etl-api.pid)
        if kill -0 "$api_pid" 2>/dev/null; then
            kill "$api_pid"
            log "API server stopped"
        fi
        rm -f /tmp/etl-api.pid
    fi
    
    # Stop Docker services
    cd "$PROJECT_ROOT/scripts"
    docker-compose down
    
    log "Cleanup completed"
}

# Main execution
main() {
    log "Starting OMOP ETL Pipeline End-to-End Testing"
    log "Log file: $LOG_FILE"
    
    # Set up trap for cleanup
    trap cleanup EXIT
    
    # Run test scenarios
    check_prerequisites
    start_services
    setup_test_data
    start_api_server
    
    # Run tests
    test_configuration_validation
    test_single_table_etl "person"
    test_parallel_etl
    validate_results
    
    log_success "All tests completed successfully!"
}

# Parse command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [options]"
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --config FILE       Use specific configuration file"
        echo "  --api-url URL       Use specific API base URL"
        echo "  --log-file FILE     Use specific log file"
        exit 0
        ;;
    --config)
        CONFIG_FILE="$2"
        shift 2
        ;;
    --api-url)
        API_BASE_URL="$2"
        shift 2
        ;;
    --log-file)
        LOG_FILE="$2"
        shift 2
        ;;
esac

# Run main function
main "$@"
