-- Source Clinical Database Initialization Script (MySQL)
-- This script sets up a source clinical database for MySQL extraction testing
-- Contains UK healthcare data that will be extracted and transformed into OMOP CDM format

-- Create test database if it doesn't exist
CREATE DATABASE IF NOT EXISTS mysql_clinical_db;
USE mysql_clinical_db;

-- Create test schema for extraction testing
CREATE SCHEMA IF NOT EXISTS test_extract;

-- Create patients table for MySQL extraction testing
CREATE TABLE IF NOT EXISTS test_extract.patients (
    patient_id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    birth_date DATE,
    gender CHAR(1),
    nhs_number VARCHAR(10),
    postcode VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create visits table for MySQL extraction testing
CREATE TABLE IF NOT EXISTS test_extract.visits (
    visit_id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    visit_date DATE NOT NULL,
    visit_type VARCHAR(50),
    department VARCHAR(100),
    consultant VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES test_extract.patients(patient_id)
);

-- Create medications table for MySQL extraction testing
CREATE TABLE IF NOT EXISTS test_extract.medications (
    medication_id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    medication_name VARCHAR(200) NOT NULL,
    dosage VARCHAR(100),
    frequency VARCHAR(50),
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES test_extract.patients(patient_id)
);

-- Create multi-source test table
CREATE TABLE IF NOT EXISTS multi_source_test.source_patients (
    source_patient_id INT AUTO_INCREMENT PRIMARY KEY,
    external_id VARCHAR(50) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    source_system ENUM('NHS', 'GP_SYSTEM', 'HOSPITAL') NOT NULL,
    nhs_number VARCHAR(10),
    date_of_birth DATE,
    gender CHAR(1),
    postcode VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample UK patient data
INSERT INTO test_extract.patients (first_name, last_name, birth_date, gender, nhs_number, postcode) VALUES
('John', 'Smith', '1985-03-15', 'M', '**********', 'SW1A 1AA'),
('Sarah', 'Johnson', '1990-07-22', 'F', '**********', 'W1A 1AA'),
('Michael', 'Brown', '1978-11-08', 'M', '**********', 'EC1A 1BB'),
('Emma', 'Davis', '1992-04-30', 'F', '**********', 'NW1A 1AA'),
('David', 'Wilson', '1983-09-12', 'M', '**********', 'SE1A 1AA'),
('Lisa', 'Taylor', '1988-12-25', 'F', '**********', 'N1A 1AA'),
('Robert', 'Anderson', '1975-06-18', 'M', '**********', 'E1A 1AA'),
('Jennifer', 'Thomas', '1995-01-14', 'F', '**********', 'WC1A 1AA'),
('Christopher', 'Jackson', '1980-08-05', 'M', '**********', 'SW1A 2AA'),
('Amanda', 'White', '1987-02-28', 'F', '**********', 'W1A 2AA');

-- Insert sample visit data
INSERT INTO test_extract.visits (patient_id, visit_date, visit_type, department, consultant) VALUES
(1, '2024-01-15', 'Outpatient', 'Cardiology', 'Dr. Williams'),
(1, '2024-02-20', 'Inpatient', 'General Medicine', 'Dr. Thompson'),
(2, '2024-01-10', 'Outpatient', 'Dermatology', 'Dr. Garcia'),
(3, '2024-03-05', 'Emergency', 'A&E', 'Dr. Martinez'),
(4, '2024-02-12', 'Outpatient', 'Orthopaedics', 'Dr. Rodriguez'),
(5, '2024-01-28', 'Inpatient', 'Surgery', 'Dr. Lewis'),
(6, '2024-03-15', 'Outpatient', 'Neurology', 'Dr. Lee'),
(7, '2024-02-08', 'Emergency', 'A&E', 'Dr. Walker'),
(8, '2024-01-22', 'Outpatient', 'Oncology', 'Dr. Hall'),
(9, '2024-03-10', 'Inpatient', 'Respiratory', 'Dr. Allen');

-- Insert sample medication data
INSERT INTO test_extract.medications (patient_id, medication_name, dosage, frequency, start_date, end_date) VALUES
(1, 'Amlodipine', '5mg', 'Once daily', '2024-01-01', NULL),
(1, 'Atorvastatin', '20mg', 'Once daily', '2024-01-01', NULL),
(2, 'Metformin', '500mg', 'Twice daily', '2024-01-15', NULL),
(3, 'Omeprazole', '20mg', 'Once daily', '2024-02-01', '2024-03-01'),
(4, 'Ibuprofen', '400mg', 'Three times daily', '2024-02-10', '2024-02-17'),
(5, 'Paracetamol', '500mg', 'Four times daily', '2024-01-20', '2024-01-27'),
(6, 'Sertraline', '50mg', 'Once daily', '2024-01-01', NULL),
(7, 'Salbutamol', '100mcg', 'As needed', '2024-02-01', NULL),
(8, 'Lansoprazole', '30mg', 'Once daily', '2024-01-10', NULL),
(9, 'Bendroflumethiazide', '2.5mg', 'Once daily', '2024-01-01', NULL);

-- Insert multi-source test data
INSERT INTO multi_source_test.source_patients (external_id, first_name, last_name, source_system, nhs_number, date_of_birth, gender, postcode) VALUES
('NHS001', 'James', 'Wilson', 'NHS', '**********', '1985-03-15', 'M', 'SW1A 1AA'),
('GP001', 'Mary', 'Thompson', 'GP_SYSTEM', '**********', '1990-07-22', 'F', 'W1A 1AA'),
('HOSP001', 'Peter', 'Anderson', 'HOSPITAL', '**********', '1978-11-08', 'M', 'EC1A 1BB'),
('NHS002', 'Helen', 'Davis', 'NHS', '**********', '1992-04-30', 'F', 'NW1A 1AA'),
('GP002', 'Richard', 'Brown', 'GP_SYSTEM', '**********', '1983-09-12', 'M', 'SE1A 1AA'),
('HOSP002', 'Susan', 'Taylor', 'HOSPITAL', '**********', '1988-12-25', 'F', 'N1A 1AA'),
('NHS003', 'Andrew', 'Johnson', 'NHS', '**********', '1975-06-18', 'M', 'E1A 1AA'),
('GP003', 'Patricia', 'White', 'GP_SYSTEM', '**********', '1995-01-14', 'F', 'WC1A 1AA'),
('HOSP003', 'Daniel', 'Jackson', 'HOSPITAL', '**********', '1980-08-05', 'M', 'SW1A 2AA'),
('NHS004', 'Elizabeth', 'Thomas', 'NHS', '**********', '1987-02-28', 'F', 'W1A 2AA');

-- Create indexes for better performance
CREATE INDEX idx_patients_nhs_number ON test_extract.patients(nhs_number);
CREATE INDEX idx_patients_birth_date ON test_extract.patients(birth_date);
CREATE INDEX idx_visits_patient_id ON test_extract.visits(patient_id);
CREATE INDEX idx_visits_visit_date ON test_extract.visits(visit_date);
CREATE INDEX idx_medications_patient_id ON test_extract.medications(patient_id);
CREATE INDEX idx_source_patients_external_id ON multi_source_test.source_patients(external_id);
CREATE INDEX idx_source_patients_source_system ON multi_source_test.source_patients(source_system);

-- Grant permissions to test user
GRANT ALL PRIVILEGES ON mysql_clinical_db.* TO 'mysql_clinical_user'@'%';
GRANT ALL PRIVILEGES ON multi_source_test.* TO 'mysql_clinical_user'@'%';
FLUSH PRIVILEGES;

-- Show created tables
SHOW TABLES;
SELECT 'MySQL database initialized successfully' as status; 