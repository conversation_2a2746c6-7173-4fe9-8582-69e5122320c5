# OMOP ETL Docker Build and Testing Guide

## Table of Contents

1. [Quick Start (5-Minute Setup)](#quick-start-5-minute-setup)
2. [Prerequisites](#prerequisites)
3. [Unified Build System Overview](#unified-build-system-overview)
4. [Development Environment Setup](#development-environment-setup)
5. [Build System Architecture](#build-system-architecture)
6. [Build Targets and Workflows](#build-targets-and-workflows)
7. [Testing Framework](#testing-framework)
8. [Development Workflows](#development-workflows)
9. [Environment Configuration](#environment-configuration)
10. [Debug and Coverage](#debug-and-coverage)
11. [Troubleshooting](#troubleshooting)
12. [Advanced Usage](#advanced-usage)
13. [CI/CD Integration](#cicd-integration)

## Quick Start (5-Minute Setup)

```bash
# 1. Make sure you're in the project root directory
cd /path/to/omop-etl

# 2. Detect your system architecture
./scripts/build.sh detect-arch

# 3. Start development environment with unified build system
./scripts/build.sh dev -e dev

# 4. Build everything
./scripts/build.sh build -e dev --tests

# 5. Run tests
./scripts/build.sh test -e dev
```

## Prerequisites

### Required Software

- **Docker**: Version 20.10 or later
- **Docker Compose**: Version 2.0 or later
- **Git**: For version control
- **Bash**: For running the unified build script
- **Python 3.8+**: For configuration template rendering

### System Requirements

- **RAM**: Minimum 4GB, recommended 8GB+
- **Disk Space**: At least 10GB free space
- **CPU**: Multi-core processor (4+ cores recommended)

### Installation

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install docker.io docker-compose git python3

# macOS
brew install docker docker-compose git python3

# Windows
# Download Docker Desktop from https://www.docker.com/products/docker-desktop
```

### Verify Installation

```bash
# Check Docker installation
docker --version
docker-compose --version

# Verify Docker can run containers
docker run hello-world

# Check Python installation
python3 --version
```

### Clone Repository

```bash
# Clone the OMOP ETL repository
git clone <repository-url>
cd omop-etl

# Verify project structure
ls -la
```

## Unified Build System Overview

The OMOP ETL Pipeline uses a **unified build system** that consolidates all Docker builds and development workflows into a single, parameterized approach. This system replaces the previous multiple Dockerfile approach with a more maintainable and consistent solution.

### Key Features

- **Unified Build System**: Single script (`scripts/build.sh`) handles all build operations
- **Environment-Specific Configurations**: Support for dev, staging, and production environments
- **Multi-Architecture Support**: Built-in support for x86_64, ARM64, and ARMv7 architectures
- **Template-Based Configuration**: Jinja2 templates with environment-specific INI configurations
- **Comprehensive Testing**: Unit tests, integration tests, and performance tests
- **Code Quality Tools**: Integrated formatting, linting, and static analysis
- **CMake Presets**: Pre-configured build configurations for different scenarios

### Key Components

#### 1. Unified Build Script (`scripts/build.sh`)
- **Single entry point** for all build operations
- **Parameterized commands** for different targets and environments
- **Automatic architecture detection** and configuration
- **Integrated testing** and quality checks

#### 2. Unified Dockerfile (`Dockerfile`)
- **Single parameterized Dockerfile** replacing multiple Dockerfiles
- **Build arguments** for different targets (dev, api, cli, etc.)
- **Multi-stage builds** for optimized production images
- **Architecture-specific optimizations**

#### 3. Configuration System (`scripts/configs/`)
- **Jinja2 templates** (`build.env.j2`) for environment-specific configurations
- **INI configuration files** for dev, staging, and production
- **Python renderer** (`render_config.py`) for template processing
- **Validation and dry-run modes**

#### 4. Docker Compose Integration (`scripts/docker-compose.yml`)
- **Service definitions** for all build targets
- **Environment variable injection** from rendered configurations
- **Profile-based service selection**

### Environment Types

The unified build system supports three environment types:

- **Development (`dev`)**: Local development and testing with debug builds
- **Staging (`staging`)**: Pre-production testing and validation
- **Production (`prod`)**: Production deployment with optimized builds

### Build Targets

Available build targets include:
- **`all`**: Build all components (default)
- **`api`**: Build API service only
- **`cli`**: Build command-line interface only
- **`core`**: Build core library components only
- **`extract`**: Build data extraction components only
- **`transform`**: Build data transformation components only
- **`load`**: Build data loading components only

## Development Environment Setup

### Step 1: Verify Project Root Directory

**IMPORTANT**: All build commands should be run from the project root directory using the unified build script.

```bash
# Make sure you're in the project root directory
pwd  # Should show: /path/to/omop-etl

# Verify the unified build script exists
ls -la scripts/build.sh

# Make the script executable if needed
chmod +x scripts/build.sh
```

### Step 2: Detect System Architecture

```bash
# The unified build system will automatically detect your architecture
./scripts/build.sh detect-arch

# This will show your detected architecture and available platforms
```

### Step 3: Start Development Environment

```bash
# Start development environment with unified build system
./scripts/build.sh dev -e dev

# This command will:
# 1. Generate environment-specific configuration
# 2. Build the development Docker image
# 3. Start the development services
# 4. Set up the development environment
```

**Expected Output:**
```
Services Status:
omop-etl-dev: running
omop-etl-api: stopped
omop-etl-cli: stopped
```

### Step 4: Enter Development Shell

```bash
# Enter the development container for interactive work
./scripts/build.sh shell

# Inside the shell, verify tools are available
which cmake
which ninja
which gcc
which g++

# Check versions
cmake --version
ninja --version
gcc --version
g++ --version

# Check the workspace directory
pwd
ls -la

# Exit the shell
exit
```

### Step 5: Test Environment Access

```bash
# Test that you can access the environment
./scripts/build.sh shell -c "echo 'Environment access successful'"

# Verify the workspace is mounted correctly
./scripts/build.sh shell -c "ls -la /workspace"
```

## Build System Architecture

### Docker Images

The project uses a **unified Dockerfile** that supports all build targets and configurations:

#### Unified Dockerfile (`Dockerfile`)
- **Single parameterized Dockerfile** replacing multiple Dockerfiles
- **Build arguments** for different targets (dev, api, cli, etc.)
- **Multi-stage builds** for optimized production images
- **Architecture-specific optimizations**

#### Build Targets Supported
- **Development (`dev`)**: Full development environment with all tools
- **API (`api`)**: API service only
- **CLI (`cli`)**: Command-line interface only
- **Core (`core`)**: Core library components only
- **Extract (`extract`)**: Data extraction components only
- **Transform (`transform`)**: Data transformation components only
- **Load (`load`)**: Data loading components only

**Purpose**: Primary development environment with all tools and dependencies

**Features**:
- Ubuntu 22.04 base
- GCC 13 with C++20 support
- CMake 3.28.1
- Development tools (clang-format, clang-tidy, cppcheck, valgrind)
- Database clients (PostgreSQL, MySQL)
- Build dependencies (nlohmann-json, spdlog, fmt, httplib)

### Docker Compose Configuration

The project includes a `docker-compose.yml` file for managing the development environment:

```yaml
# Key services:
# - omop-etl-dev: Development container
# - postgres: PostgreSQL database for testing
# - mysql: MySQL database for testing
```

**Start development environment**:
```bash
docker-compose -f scripts/docker-compose.yml --profile dev up -d
```

## Build Targets and Workflows

### CMake Presets

The project uses CMake presets for consistent build configurations:

#### Configure Presets

| Preset | Description | Use Case |
|--------|-------------|----------|
| `docker-release` | Release build with tests | Production builds, CI/CD |
| `docker-debug` | Debug build with tests and coverage | Development, debugging |
| `docker-simple` | Simple release build without tests | Quick builds |
| `docker-multiarch-release` | Multi-architecture release build | Cross-platform deployment |
| `docker-multiarch-debug` | Multi-architecture debug build | Cross-platform development |

#### Build Presets

Each configure preset has a corresponding build preset that handles the actual compilation.

### Library Targets

The project is organized into modular libraries:

#### Core Libraries

| Target | Description | Dependencies |
|--------|-------------|--------------|
| `omop_common` | Common utilities and helpers | Standard library |
| `omop_core` | Core functionality and interfaces | omop_common |
| `omop_cdm` | Common Data Model implementation | omop_common, omop_core |
| `omop_extract` | Data extraction components | omop_common, omop_core |
| `omop_transform` | Data transformation components | omop_common, omop_core |
| `omop_load` | Data loading components | omop_common, omop_core |
| `omop_service` | Service layer (temporarily disabled) | omop_common, omop_core, gRPC |

#### Convenience Targets

| Target | Description |
|--------|-------------|
| `libraries` | Build all OMOP ETL libraries |
| `applications` | Build all applications (when enabled) |
| `all-components` | Build everything (libraries + applications) |

### Application Targets

| Target | Description | Status |
|--------|-------------|--------|
| `omop_etl_cli` | Command-line interface | Available |
| `omop_etl_api` | REST API server | Available |

### Build Commands

#### Using Unified Build Script

```bash
# Build all components with release configuration
./scripts/build.sh build -e dev

# Build all components with debug configuration
./scripts/build.sh build -e dev --debug

# Build specific target
./scripts/build.sh build -e dev -t api

# Build with tests
./scripts/build.sh build -e dev --tests

# Build with parallel compilation
./scripts/build.sh build -e dev --parallel 4
```

#### Using CMake Directly

```bash
# Configure
cmake --preset docker-release

# Build
cmake --build --preset docker-release

# Build specific target
cmake --build build/docker-release --target omop_common
```

#### Using Docker Compose

```bash
# Start development container
docker-compose -f scripts/docker-compose.yml --profile dev up -d

# Execute build command
docker-compose -f scripts/docker-compose.yml exec omop-etl-dev bash -c "
    cmake --preset docker-release &&
    cmake --build --preset docker-release
"
```

### Understanding Build Targets

| Target Category | Description | Targets |
|-----------------|-------------|---------|
| `libraries` | All library components | omop_common, omop_core, omop_cdm, omop_extract, omop_transform, omop_load |
| `applications` | All applications | omop_etl_cli, omop_etl_api |
| `test_all_tests` | All unit tests | test_common_unit, test_core_unit, test_cdm_unit, test_extract_unit, test_transform_unit, test_load_unit |
| `test_integration_only` | All integration tests | test_api_integration, test_cdm_integration, test_e2e_integration, etc. |
| `all-components` | Everything (libraries + applications + tests) | All targets |

### Build Individual Target Categories

```bash
# Build only libraries
./scripts/build.sh build -e dev -t core --parallel 4

# Build only applications
./scripts/build.sh build -e dev -t api,cli --parallel 4

# Build only unit tests
./scripts/build.sh build -e dev -t test --filter "unit" --parallel 4

# Build only integration tests
./scripts/build.sh build -e dev -t test --filter "integration" --parallel 4
```

### Build Individual Libraries

```bash
# Build individual libraries
./scripts/build.sh build -e dev -t common --parallel 4
./scripts/build.sh build -e dev -t core --parallel 4
./scripts/build.sh build -e dev -t cdm --parallel 4
./scripts/build.sh build -e dev -t extract --parallel 4
./scripts/build.sh build -e dev -t transform --parallel 4
./scripts/build.sh build -e dev -t load --parallel 4
```

### Thread-Limited Builds

```bash
# Build with 2 threads (for resource-constrained environments)
./scripts/build.sh build -e dev --parallel 2

# Build with 4 threads (balanced performance)
./scripts/build.sh build -e dev --parallel 4

# Build with 8 threads (maximum performance)
./scripts/build.sh build -e dev --parallel 8
```

### Verify Build Results

```bash
# Check built artifacts
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "
    echo 'Built libraries:' &&
    find build/docker-release/lib -name '*.a' -o -name '*.so' &&
    echo 'Built executables:' &&
    find build/docker-release/bin -name 'omop*' -type f &&
    echo 'Build summary:'
"
```

## Testing Framework

### Test Organization

The testing framework is organized into two main categories:

#### Unit Tests (`tests/unit/`)

- **Location**: `tests/unit/`
- **Purpose**: Test individual components in isolation
- **Framework**: Google Test (gtest)
- **Coverage**: Each library has corresponding unit tests

**Unit Test Targets**:
- `test_common_unit` - Common library tests
- `test_core_unit` - Core library tests
- `test_cdm_unit` - CDM library tests
- `test_extract_unit` - Extract library tests
- `test_transform_unit` - Transform library tests
- `test_load_unit` - Load library tests

#### Integration Tests (`tests/integration/`)

- **Location**: `tests/integration/`
- **Purpose**: Test component interactions and end-to-end workflows
- **Framework**: Google Test with database integration
- **Coverage**: API testing, database operations, ETL pipelines

**Integration Test Categories**:
- `api/` - REST API testing
- `cdm/` - CDM schema and operations
- `e2e/` - End-to-end workflow testing
- `performance/` - Performance and load testing
- `quality/` - Data quality validation
- `security/` - Security testing
- `workflow/` - ETL workflow testing

### Test Execution

#### Using Unified Build Script

```bash
# Run all tests
./scripts/build.sh test -e dev

# Run unit tests only
./scripts/build.sh test -e dev -t unit

# Run integration tests only
./scripts/build.sh test -e dev -t integration

# Run specific test categories
./scripts/build.sh test -e dev -t api
./scripts/build.sh test -e dev -t cdm
./scripts/build.sh test -e dev -t e2e
./scripts/build.sh test -e dev -t performance
./scripts/build.sh test -e dev -t quality
./scripts/build.sh test -e dev -t security
./scripts/build.sh test -e dev -t workflow

# Run tests with verbose output
./scripts/build.sh test -e dev --verbose

# Run tests with filters
./scripts/build.sh test -e dev -t extract --filter "ConnectionPool*"
```

#### Running Individual Unit Tests

##### Running All Library Unit Test Modules

```bash
# Run all unit tests for all libraries
./scripts/build.sh test -e dev -t unit

# Run all unit tests with verbose output
./scripts/build.sh test -e dev -t unit --verbose

# Run all unit tests in parallel for faster execution
./scripts/build.sh test -e dev -t unit --parallel 4

# Run all unit tests with coverage enabled
./scripts/build.sh test -e dev -t unit --coverage
```

##### Running Individual Library Unit Test Modules

```bash
# Run all unit tests for Common library
./scripts/build.sh test -e dev -t common

# Run all unit tests for Core library
./scripts/build.sh test -e dev -t core

# Run all unit tests for CDM library
./scripts/build.sh test -e dev -t cdm

# Run all unit tests for Extract library
./scripts/build.sh test -e dev -t extract

# Run all unit tests for Transform library
./scripts/build.sh test -e dev -t transform

# Run all unit tests for Load library
./scripts/build.sh test -e dev -t load
```

##### Running All Unit Tests with CTest

```bash
# Enter the development shell
./scripts/build.sh shell

# Navigate to build directory
cd build/docker-debug  # or docker-release

# Run all unit tests using CTest
ctest --test-dir . --output-on-failure

# Run all unit tests with verbose output
ctest --test-dir . --output-on-failure --verbose

# Run all unit tests in parallel
ctest --test-dir . --output-on-failure --parallel 4

# List all available unit tests
ctest --test-dir . --show-only | grep "test_.*_unit"
```

##### Examples of Individual Unit Test Execution

```bash
# Run specific common library tests
./scripts/build.sh test -e dev -t common --filter "ConfigManagementUnitTest.*"
./scripts/build.sh test -e dev -t common --filter "ExceptionHandlingUnitTest.*"

# Run specific core library tests
./scripts/build.sh test -e dev -t core --filter "ComponentFactoryCreationUnitTest.*"
./scripts/build.sh test -e dev -t core --filter "CoreInterfacesCompatibilityUnitTest.*"

# Run specific extract library tests
./scripts/build.sh test -e dev -t extract --filter "CsvFileExtractionUnitTest.*"
./scripts/build.sh test -e dev -t extract --filter "DatabaseSourceExtractionUnitTest.*"

# Run specific transform library tests
./scripts/build.sh test -e dev -t transform --filter "AlphabeticalDateTransformationUnitTest.*"
./scripts/build.sh test -e dev -t transform --filter "AnonymizationTransformationsUnitTest.*"

# Run specific load library tests
./scripts/build.sh test -e dev -t load --filter "BatchLoadingUnitTest.*"
./scripts/build.sh test -e dev -t load --filter "ConcurrentDataLoadingUnitTest.*"
```

##### Using CTest for Individual Test Execution

```bash
# Enter the development shell
./scripts/build.sh shell

# Navigate to build directory
cd build/docker-debug  # or docker-release

# List all available tests
ctest --test-dir . --show-only

# Run a specific test by name
ctest --test-dir . -R "TestSuiteName.TestName" --output-on-failure

# Run tests with verbose output
ctest --test-dir . -R "TestSuiteName.*" --output-on-failure --verbose

# Run tests with parallel execution
ctest --test-dir . -R "TestSuiteName.*" --output-on-failure --parallel 4
```

##### Running Tests with Debugger

```bash
# Run a specific test with gdb for debugging
./scripts/build.sh exec "
    cd build/docker-debug &&
    gdb --args bin/test_common_unit --gtest_filter='ConfigManagementUnitTest.*'
"

# Run tests with additional debug output
./scripts/build.sh test -e dev -t unit --filter "ConfigManagementUnitTest.*" --verbose --debug
```

#### Code Coverage Reporting

##### Enabling Code Coverage

```bash
# Build with coverage enabled
./scripts/build.sh build -e dev --debug --coverage

# Or configure manually
./scripts/build.sh exec "
    cmake -DCMAKE_BUILD_TYPE=Debug \
          -DBUILD_TESTS=ON \
          -DENABLE_COVERAGE=ON \
          -B build/docker-debug
"
```

##### Generating Coverage Reports

```bash
# Run tests to generate coverage data
./scripts/build.sh test -e dev --debug

# Generate coverage report using lcov
./scripts/build.sh exec "
    echo 'Generating coverage report...' &&
    cd build/docker-debug &&
    lcov --capture --directory . --output-file coverage.info &&
    lcov --remove coverage.info '/usr/*' '/opt/*' --output-file coverage.info &&
    genhtml coverage.info --output-directory coverage_report &&
    echo 'Coverage report generated in build/docker-debug/coverage_report/'
"
```

##### Viewing Coverage Reports

```bash
# Open coverage report in browser (if available)
./scripts/build.sh exec "
    cd build/docker-debug/coverage_report &&
    python3 -m http.server 8000
"

# View coverage summary
./scripts/build.sh exec "
    cd build/docker-debug &&
    lcov --summary coverage.info
"

# Generate detailed coverage report for specific files
./scripts/build.sh exec "
    cd build/docker-debug &&
    lcov --extract coverage.info '*/src/*' --output-file src_coverage.info &&
    genhtml src_coverage.info --output-directory src_coverage_report
"
```

##### Coverage for Specific Test Suites

```bash
# Generate coverage for specific unit test suites
./scripts/build.sh exec "
    cd build/docker-debug &&
    # Run specific test suite
    bin/test_common_unit --gtest_filter='ConfigManagementUnitTest.*' &&
    
    # Generate coverage for that run
    lcov --capture --directory . --output-file common_coverage.info &&
    lcov --remove common_coverage.info '/usr/*' '/opt/*' --output-file common_coverage.info &&
    genhtml common_coverage.info --output-directory common_coverage_report
"
```

##### Coverage Quality Gates

```bash
# Check if coverage meets minimum thresholds
./scripts/build.sh exec "
    cd build/docker-debug &&
    lcov --summary coverage.info | grep -E 'lines|functions|branches' | while read line; do
        percentage=\$(echo \$line | grep -o '[0-9]*%' | grep -o '[0-9]*')
        if [ \$percentage -lt 80 ]; then
            echo \"WARNING: Coverage below 80%: \$line\"
            exit 1
        fi
    done
"
```

##### Continuous Integration Coverage

```bash
# Generate coverage for CI/CD pipeline
./scripts/build.sh exec "
    cd build/docker-debug &&
    
    # Run all tests
    ctest --test-dir . --output-on-failure &&
    
    # Generate coverage
    lcov --capture --directory . --output-file coverage.info &&
    lcov --remove coverage.info '/usr/*' '/opt/*' --output-file coverage.info &&
    
    # Upload to coverage service (example)
    curl -F 'file=@coverage.info' https://coveralls.io/api/v1/jobs
"
```

#### Advanced Unit Testing Techniques

##### Test Discovery and Organization

```bash
# Discover all available test suites
./scripts/build.sh exec "
    cd build/docker-debug &&
    find . -name '*test*' -type f -executable | grep -v CMakeFiles
"

# List test suites with their test counts
./scripts/build.sh exec "
    cd build/docker-debug &&
    for test_bin in bin/test_*_unit; do
        echo \"\$(basename \$test_bin):\"
        \$test_bin --gtest_list_tests | head -20
        echo \"---\"
    done
"
```

##### Test Sharding and Parallel Execution

```bash
# Run tests in parallel with sharding
./scripts/build.sh exec "
    cd build/docker-debug &&
    # Run tests in 4 parallel processes
    ctest --test-dir . --parallel 4 --output-on-failure
"

# Use Google Test's built-in sharding
./scripts/build.sh exec "
    cd build/docker-debug &&
    # Run test suite in 3 shards (shard 1 of 3)
    bin/test_common_unit --gtest_shard_index=0 --gtest_total_shards=3 &&
    # Run test suite in 3 shards (shard 2 of 3)
    bin/test_common_unit --gtest_shard_index=1 --gtest_total_shards=3 &&
    # Run test suite in 3 shards (shard 3 of 3)
    bin/test_common_unit --gtest_shard_index=2 --gtest_total_shards=3
"
```

##### Test Repeat and Flakiness Detection

```bash
# Run tests multiple times to detect flaky tests
./scripts/build.sh exec "
    cd build/docker-debug &&
    # Run each test 3 times
    for i in {1..3}; do
        echo \"Test run \$i:\"
        ctest --test-dir . --output-on-failure
        echo \"---\"
    done
"

# Use Google Test's repeat feature
./scripts/build.sh exec "
    cd build/docker-debug &&
    # Run specific test 5 times
    bin/test_common_unit --gtest_filter='ConfigManagementUnitTest.*' --gtest_repeat=5
"
```

##### Test Timeout and Performance Monitoring

```bash
# Run tests with timeout
./scripts/build.sh exec "
    cd build/docker-debug &&
    # Run tests with 30-second timeout per test
    timeout 30s ctest --test-dir . --output-on-failure
"

# Monitor test execution time
./scripts/build.sh exec "
    cd build/docker-debug &&
    # Run tests and show timing information
    ctest --test-dir . --output-on-failure --verbose --timeout 30
"
```

##### Test Data and Fixtures

```bash
# Run tests with specific test data
./scripts/build.sh exec "
    cd build/docker-debug &&
    # Set test data directory
    export TEST_DATA_DIR=/workspace/tests/test_data &&
    bin/test_extract_unit --gtest_filter='CsvFileExtractionUnitTest.*'
"

# Run tests with custom fixtures
./scripts/build.sh exec "
    cd build/docker-debug &&
    # Set fixture configuration
    export TEST_FIXTURE_CONFIG=/workspace/tests/test_helpers/fixtures.yaml &&
    bin/test_transform_unit --gtest_filter='*Fixture*'
"
```

##### Debugging Failed Tests

```bash
# Run failing test with maximum verbosity
./scripts/build.sh exec "
    cd build/docker-debug &&
    bin/test_common_unit --gtest_filter='ConfigManagementUnitTest.*' \
                         --gtest_break_on_failure \
                         --gtest_catch_exceptions=0 \
                         --gtest_throw_on_failure
"

# Generate core dump for debugging
./scripts/build.sh exec "
    cd build/docker-debug &&
    ulimit -c unlimited &&
    bin/test_common_unit --gtest_filter='ConfigManagementUnitTest.*' &&
    # Analyze core dump if generated
    if [ -f core.* ]; then
        gdb bin/test_common_unit core.*
    fi
"
```

##### Test Output and Reporting

```bash
# Generate XML test report
./scripts/build.sh exec "
    cd build/docker-debug &&
    bin/test_common_unit --gtest_filter='ConfigManagementUnitTest.*' \
                         --gtest_output=xml:test_results.xml
"

# Generate JSON test report
./scripts/build.sh exec "
    cd build/docker-debug &&
    bin/test_common_unit --gtest_filter='ConfigManagementUnitTest.*' \
                         --gtest_output=json:test_results.json
"

# Combine multiple test reports
./scripts/build.sh exec "
    cd build/docker-debug &&
    # Run all unit tests and generate combined report
    for test_bin in bin/test_*_unit; do
        \$test_bin --gtest_output=xml:\$(basename \$test_bin).xml
    done
"
```

#### Practical Unit Testing Workflows

##### Daily Development Testing Workflow

```bash
# Complete daily testing workflow
echo "=== Daily Unit Testing Workflow ==="

# Step 1: Start development environment
./scripts/build.sh dev -e dev

# Step 2: Build with tests and coverage
./scripts/build.sh build -e dev --debug --coverage

# Step 3: Run quick unit tests for changed components
./scripts/build.sh test -e dev -t unit --filter "*Config*" --parallel 4

# Step 4: Generate coverage report for changed areas
./scripts/build.sh exec "
    cd build/docker-debug &&
    lcov --capture --directory . --output-file daily_coverage.info &&
    lcov --remove daily_coverage.info '/usr/*' '/opt/*' --output-file daily_coverage.info &&
    genhtml daily_coverage.info --output-directory daily_coverage_report
"

echo "Daily testing workflow completed!"
```

##### Feature Development Testing Workflow

```bash
# Complete feature testing workflow
echo "=== Feature Development Testing Workflow ==="

# Step 1: Run existing tests to ensure no regressions
./scripts/build.sh test -e dev -t unit --filter "*Extract*"

# Step 2: Run new feature tests with verbose output
./scripts/build.sh test -e dev -t unit --filter "*NewFeature*" --verbose

# Step 3: Run tests multiple times to ensure stability
./scripts/build.sh exec "
    cd build/docker-debug &&
    for i in {1..3}; do
        echo \"Test run \$i for new feature:\"
        bin/test_extract_unit --gtest_filter='*NewFeature*' --output-on-failure
        echo \"---\"
    done
"

# Step 4: Generate comprehensive coverage for new feature
./scripts/build.sh exec "
    cd build/docker-debug &&
    bin/test_extract_unit --gtest_filter='*NewFeature*' &&
    lcov --capture --directory . --output-file feature_coverage.info &&
    lcov --remove feature_coverage.info '/usr/*' '/opt/*' --output-file feature_coverage.info &&
    genhtml feature_coverage.info --output-directory feature_coverage_report
"

echo "Feature testing workflow completed!"
```

##### Debugging and Troubleshooting Workflow

```bash
# Complete debugging workflow
echo "=== Debugging and Troubleshooting Workflow ==="

# Step 1: Identify failing test
./scripts/build.sh test -e dev -t unit --filter "*FailingTest*" --verbose

# Step 2: Run test with debugger
./scripts/build.sh exec "
    cd build/docker-debug &&
    gdb --args bin/test_common_unit --gtest_filter='FailingTest.*' \
        --gtest_break_on_failure \
        --gtest_catch_exceptions=0
"

# Step 3: Run test with maximum verbosity
./scripts/build.sh exec "
    cd build/docker-debug &&
    bin/test_common_unit --gtest_filter='FailingTest.*' \
        --gtest_break_on_failure \
        --gtest_catch_exceptions=0 \
        --gtest_throw_on_failure \
        --gtest_repeat=3
"

# Step 4: Generate detailed test report
./scripts/build.sh exec "
    cd build/docker-debug &&
    bin/test_common_unit --gtest_filter='FailingTest.*' \
        --gtest_output=xml:failing_test_report.xml
"

echo "Debugging workflow completed!"
```

##### Performance Testing Workflow

```bash
# Complete performance testing workflow
echo "=== Performance Testing Workflow ==="

# Step 1: Run performance tests with timing
./scripts/build.sh exec "
    cd build/docker-debug &&
    ctest --test-dir . --output-on-failure --verbose --timeout 60
"

# Step 2: Run specific performance tests multiple times
./scripts/build.sh exec "
    cd build/docker-debug &&
    for i in {1..5}; do
        echo \"Performance test run \$i:\"
        time bin/test_transform_unit --gtest_filter='*Performance*'
        echo \"---\"
    done
"

# Step 3: Profile performance tests
./scripts/build.sh exec "
    cd build/docker-debug &&
    perf record -g bin/test_transform_unit --gtest_filter='*Performance*' &&
    perf report
"

echo "Performance testing workflow completed!"
```

#### Unit Testing Quick Reference

##### Essential Commands

| Command | Description |
|---------|-------------|
| `./scripts/build.sh test -e dev -t unit` | Run all unit tests |
| `./scripts/build.sh test -e dev -t unit --filter "TestSuite.*"` | Run specific test suite |
| `./scripts/build.sh test -e dev -t unit --filter "TestSuite.TestName"` | Run specific test case |
| `./scripts/build.sh test -e dev -t unit --parallel 4` | Run tests in parallel |
| `./scripts/build.sh test -e dev -t unit --verbose` | Run tests with verbose output |

##### Code Coverage Commands

| Command | Description |
|---------|-------------|
| `./scripts/build.sh build -e dev --debug --coverage` | Build with coverage enabled |
| `./scripts/build.sh test -e dev --debug` | Run tests to generate coverage data |
| `./scripts/build.sh exec "cd build/docker-debug && lcov --capture --directory . --output-file coverage.info"` | Generate coverage report |
| `./scripts/build.sh exec "cd build/docker-debug && genhtml coverage.info --output-directory coverage_report"` | Generate HTML coverage report |

##### Debugging Commands

| Command | Description |
|---------|-------------|
| `./scripts/build.sh exec "cd build/docker-debug && gdb --args bin/test_common_unit --gtest_filter='TestSuite.*'"` | Run test with debugger |
| `./scripts/build.sh test -e dev -t unit --filter "TestSuite.*" --verbose --debug` | Run test with debug output |
| `./scripts/build.sh exec "cd build/docker-debug && bin/test_common_unit --gtest_filter='TestSuite.*' --gtest_break_on_failure"` | Break on test failure |

##### Test Organization

| Test Category | Target | Description |
|---------------|--------|-------------|
| Common Library | `test_common_unit` | Configuration, exceptions, utilities |
| Core Library | `test_core_unit` | Core interfaces, component factory |
| CDM Library | `test_cdm_unit` | Common Data Model operations |
| Extract Library | `test_extract_unit` | Data extraction components |
| Transform Library | `test_transform_unit` | Data transformation components |
| Load Library | `test_load_unit` | Data loading components |

##### Best Practices

1. **Always run unit tests before committing code**
2. **Use specific test filters to run only relevant tests during development**
3. **Enable coverage for new features to ensure adequate testing**
4. **Run tests multiple times to detect flaky tests**
5. **Use parallel execution for faster test runs**
6. **Generate coverage reports to identify untested code**
7. **Debug failing tests with gdb for better understanding**
8. **Use test sharding for large test suites in CI/CD**

#### Using CMake Test Presets

```bash
# Run tests with release configuration
cmake --preset docker-release
ctest --preset docker-release

# Run tests with debug configuration
cmake --preset docker-debug
ctest --preset docker-debug
```

#### Using CTest Directly

```bash
# Run all tests
ctest --output-on-failure

# Run unit tests only
ctest -L unit --output-on-failure

# Run integration tests only
ctest -L integration --output-on-failure

# Run specific test
ctest -R test_name --output-on-failure

# Run tests with verbose output
ctest --output-on-failure --verbose
```

### Integration Testing

#### Overview of Integration Tests

The OMOP ETL project includes comprehensive integration tests for all libraries. These tests run inside the Docker container and require the development environment to be properly configured.

#### Prerequisites for Integration Tests

Before running integration tests, ensure:

1. **Development environment is running:**
   ```bash
   ./scripts/build.sh dev -e dev
   ```

2. **Tests are built:**
   ```bash
   ./scripts/build.sh build -e dev -t test --filter "integration"
   ```

3. **Database services are available** (for tests that require database connectivity)

#### Running Integration Tests by Library

##### CDM (Common Data Model) Integration Tests

```bash
# Build and run CDM integration tests
./scripts/build.sh test -e dev -t cdm --filter "integration"

# Run specific CDM test suites
./scripts/build.sh test -e dev -t cdm --filter "PersonIntegrationTest.*"
./scripts/build.sh test -e dev -t cdm --filter "VisitOccurrenceIntegrationTest.*"
./scripts/build.sh test -e dev -t cdm --filter "TableDefinitionsIntegrationTest.*"
```

##### API Integration Tests

```bash
# Build and run API integration tests
./scripts/build.sh test -e dev -t api --filter "integration"

# Run specific API test suites
./scripts/build.sh test -e dev -t api --filter "ApiIntegrationTest.*"
```

##### Extract Integration Tests

```bash
# Build and run extract integration tests
./scripts/build.sh test -e dev -t extract --filter "integration"

# Run specific extract test suites
./scripts/build.sh test -e dev -t extract --filter "ExtractorIntegrationTest.*"
```

##### Transform Integration Tests

```bash
# Build and run transform integration tests
./scripts/build.sh test -e dev -t transform --filter "integration"

# Run specific transform test suites
./scripts/build.sh test -e dev -t transform --filter "TransformIntegrationTest.*"
```

##### Load Integration Tests

```bash
# Build and run load integration tests
./scripts/build.sh test -e dev -t load --filter "integration"

# Run specific load test suites
./scripts/build.sh test -e dev -t load --filter "LoadIntegrationTest.*"
```

##### End-to-End Integration Tests

```bash
# Build and run E2E integration tests
./scripts/build.sh test -e dev -t e2e --filter "integration"

# Run specific E2E test suites
./scripts/build.sh test -e dev -t e2e --filter "E2EIntegrationTest.*"
```

### Test Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `BUILD_TESTS` | Enable unit tests | OFF |
| `BUILD_INTEGRATION_TESTS` | Enable integration tests | OFF |
| `ENABLE_COVERAGE` | Enable code coverage reporting | OFF |
| `ENABLE_VALGRIND` | Enable Valgrind memory checking | OFF |
| `ENABLE_SANITIZERS` | Enable AddressSanitizer and UBSan | OFF |

### Advanced Integration Testing

#### Running Tests with Custom Filters

```bash
# Run tests matching a pattern
./scripts/build.sh test -e dev -t cdm --filter "*Validation*"

# Run tests excluding certain patterns
./scripts/build.sh test -e dev -t cdm --filter "*-*Performance*"

# Run tests with specific test names
./scripts/build.sh test -e dev -t cdm --filter "PersonIntegrationTest.BirthDateValidation:VisitOccurrenceIntegrationTest.CompleteNHSHospitalAdmission"
```

#### Running Tests with Verbose Output

```bash
# Run tests with detailed output
./scripts/build.sh test -e dev -t cdm --verbose

# Run tests with color output
./scripts/build.sh test -e dev -t cdm --color

# Run tests with repeat count
./scripts/build.sh test -e dev -t cdm --repeat 3
```

#### Running Tests in Parallel

```bash
# Run tests with multiple workers
./scripts/build.sh test -e dev -t cdm --parallel 4
```

### Complete Integration Test Workflow

```bash
# Complete integration testing workflow using unified build system
echo "=== Integration Testing Workflow ==="

# Step 1: Ensure development environment is running
echo "Step 1: Starting development environment..."
./scripts/build.sh dev -e dev

# Step 2: Build all integration tests
echo "Step 2: Building integration tests..."
./scripts/build.sh build -e dev -t test --filter "integration"

# Step 3: Run all integration tests
echo "Step 3: Running all integration tests..."
./scripts/build.sh test -e dev -t integration

echo "Integration testing workflow completed!"
```

## Development Workflows

### Daily Development Workflow

```bash
# Complete development workflow
echo "=== Development Workflow ==="

# Step 1: Start development environment
echo "Step 1: Starting development environment..."
./scripts/build.sh dev -e dev

# Step 2: Enter shell for interactive development
echo "Step 2: Entering development shell..."
./scripts/build.sh shell

# Step 3: Make code changes
# (Edit source files in your preferred editor)

# Step 4: Build and test changes
echo "Step 4: Building and testing changes..."
./scripts/build.sh build -e dev --tests
./scripts/build.sh test -e dev

# Step 5: Format and lint code
echo "Step 5: Formatting and linting code..."
./scripts/build.sh format
./scripts/build.sh lint

# Step 6: Commit changes
git add .
git commit -m "Add new feature"

# Step 7: Stop environment when done
./scripts/build.sh down
```

### Feature Development Workflow

```bash
# 1. Create feature branch
git checkout -b feature/new-extractor

# 2. Start development environment
./scripts/build.sh dev -e dev

# 3. Develop feature
./scripts/build.sh shell
# ... make changes ...

# 4. Test feature thoroughly
./scripts/build.sh test -e dev -t extract --filter "NewExtractor*"
./scripts/build.sh test -e dev -t integration --filter "*extractor*"

# 5. Build and validate
./scripts/build.sh build -e dev -t extract --tests
./scripts/build.sh lint

# 6. Create pull request
git push origin feature/new-extractor
```

### Debugging Workflow

#### 1. Debug Build

```bash
# Build debug version with symbols
./scripts/build.sh build -e dev --debug
```

#### 2. Interactive Debugging

```bash
# Start interactive shell
./scripts/build.sh shell

# Run with gdb
gdb build/docker-debug/bin/test_executable
```

#### 3. Memory Debugging

```bash
# Run with Valgrind
./scripts/build.sh exec "
    cd build/docker-debug &&
    valgrind --leak-check=full --show-leak-kinds=all ./bin/test_executable
"
```

### Performance Analysis Workflow

#### 1. Performance Build

```bash
# Build release version for performance testing
./scripts/build.sh build -e dev
```

#### 2. Run Performance Tests

```bash
# Run performance tests
./scripts/build.sh test -e dev -t performance
```

#### 3. Profile with gprof

```bash
# Build with profiling
./scripts/build.sh exec "
    cmake -DCMAKE_BUILD_TYPE=Release \
          -DBUILD_TESTS=ON \
          -DCMAKE_CXX_FLAGS='-pg' \
          -B build/profile &&
    cmake --build build/profile
"
```

## Environment Configuration

### Overview of Environment Configuration

The unified build system uses a template-based configuration approach with environment-specific settings. This allows for consistent builds across different environments while maintaining flexibility for environment-specific requirements.

### Configuration Components

#### 1. Template File (`scripts/build.env.j2`)
The main template file that defines the structure of environment configurations:

```jinja2
# Build Configuration for {{ environment }}
BUILD_TYPE={{ build_type }}
BUILD_TARGETS={{ build_targets | join(',') }}
CMAKE_PRESET={{ cmake_preset }}
BUILD_PARALLEL={{ build_parallel }}

# Docker Configuration
DOCKER_REGISTRY={{ docker_registry }}
DOCKER_TAG={{ docker_tag }}
DOCKER_PLATFORM={{ docker_platform }}

# Database Configuration
DB_HOST={{ db_host }}
DB_PORT={{ db_port }}
DB_NAME={{ db_name }}
DB_USER={{ db_user }}
DB_PASSWORD={{ db_password }}
```

#### 2. Environment Configurations (`scripts/configs/`)
INI files that define environment-specific values:

**Development (`scripts/configs/dev.ini`)**
```ini
[build]
build_type = Debug
build_targets = all
cmake_preset = docker-debug
build_parallel = 1

[docker]
docker_registry = local
docker_tag = dev
docker_platform = linux/amd64

[database]
db_host = localhost
db_port = 5432
db_name = omop_dev
db_user = dev_user
db_password = dev_password
```

**Production (`scripts/configs/prod.ini`)**
```ini
[build]
build_type = Release
build_targets = api,cli
cmake_preset = docker-release
build_parallel = 4

[docker]
docker_registry = registry.example.com
docker_tag = latest
docker_platform = linux/amd64

[database]
db_host = prod-db.example.com
db_port = 5432
db_name = omop_prod
db_user = ${DB_USER}
db_password = ${DB_PASSWORD}
```

### Configuration Management Commands

```bash
# Generate configuration for specific environment
./scripts/build.sh config -e dev

# Validate configuration
./scripts/build.sh config -e prod --validate

# Dry-run configuration generation
./scripts/build.sh config -e staging --dry-run

# View generated configuration
cat scripts/build.env
```

### Environment Variables

The system supports environment variable expansion in configurations:

```bash
# Set environment variables
export DB_USER=production_user
export DB_PASSWORD=secure_password

# Generate configuration with environment variables
./scripts/build.sh config -e prod
```

### Custom Environment Configurations

Create custom environment configurations:

```bash
# Create custom environment configuration
cp scripts/configs/dev.ini scripts/configs/custom.ini

# Edit custom configuration
vim scripts/configs/custom.ini

# Use custom configuration
./scripts/build.sh dev -e custom
```

## Debug and Coverage

### Debug Build with Coverage

```bash
# Configure for debug build with coverage
./scripts/build.sh build -e dev --debug --coverage

# Build with coverage
./scripts/build.sh build -e dev --debug

# Run tests to generate coverage data
./scripts/build.sh test -e dev --debug
```

### Generate Coverage Report

```bash
# Generate coverage report
./scripts/build.sh exec "
    echo 'Generating coverage report...' &&
    cd build/docker-debug &&
    lcov --capture --directory . --output-file coverage.info &&
    lcov --remove coverage.info '/usr/*' --output-file coverage.info &&
    genhtml coverage.info --output-directory coverage_report &&
    echo 'Coverage report generated in build/docker-debug/coverage_report/'
"
```

### Debug Individual Tests

```bash
# Run a specific test with debugger
./scripts/build.sh exec "
    echo 'Running test with debugger...' &&
    cd build/docker-debug &&
    gdb --args bin/test_common_unit
"
```

### Memory Checking with Valgrind

#### Enable Valgrind

```bash
# Configure with Valgrind enabled
./scripts/build.sh build -e dev --debug --valgrind
```

#### Run Tests with Valgrind

```bash
# Run tests with Valgrind
./scripts/build.sh test -e dev --debug --valgrind
```

## Troubleshooting

### Common Issues

#### 1. Architecture Detection Issues
```bash
# Manual architecture detection
./scripts/build.sh detect-arch --verbose

# Force specific architecture
export DOCKER_DEFAULT_PLATFORM=linux/amd64
./scripts/build.sh dev -e dev
```

#### 2. Memory Issues During Build
```bash
# Reduce build parallelism
./scripts/build.sh build -e dev --parallel 1

# Disable code coverage
./scripts/build.sh build -e dev --no-coverage

# Use debug build (less memory intensive)
./scripts/build.sh build -e dev --debug
```

#### 3. Configuration Issues
```bash
# Validate configuration
./scripts/build.sh config -e dev --validate

# Regenerate configuration
./scripts/build.sh config -e dev --force

# Check configuration syntax
python3 scripts/render_config.py --validate --env dev
```

#### 4. Docker Issues
```bash
# Check Docker status
docker system info

# Clean Docker resources
docker system prune -f

# Restart Docker daemon
sudo systemctl restart docker
```

#### 5. Test Failures
```bash
# Run tests with verbose output
./scripts/build.sh test --verbose

# Run specific failing test
./scripts/build.sh test --filter "SpecificTestName"

# Run tests with debug output
./scripts/build.sh test --debug
```

#### 6. Service Issues
```bash
# Check if database services are running
./scripts/build.sh status

# Restart database services
./scripts/build.sh restart --service omop-cdm-db
```

#### 7. Permission Issues
```bash
# Check file permissions
./scripts/build.sh shell -c "ls -la /workspace/build/docker-debug/bin/"

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker
```

### Debug Mode

Enable debug mode for detailed output:

```bash
# Enable debug mode
export DEBUG=1

# Run commands with debug output
./scripts/build.sh dev -e dev --debug
./scripts/build.sh build --debug
./scripts/build.sh test --debug
```

### Log Analysis

```bash
# View build logs
./scripts/build.sh logs

# View specific service logs
./scripts/build.sh logs --service api

# Follow logs in real-time
./scripts/build.sh logs --follow

# Export logs to file
./scripts/build.sh logs --output build.log
```

### Performance Optimization

```bash
# Determine optimal thread count
./scripts/build.sh exec "
    CPU_CORES=\$(nproc) &&
    OPTIMAL_THREADS=\$((CPU_CORES - 1)) &&
    echo \"Using \$OPTIMAL_THREADS threads\" &&
    cmake --preset docker-release &&
    cmake --build --preset docker-release --target all-components --parallel \$OPTIMAL_THREADS
"
```

### Cleanup Commands

```bash
# Stop all services
./scripts/build.sh down

# Remove containers and volumes
./scripts/build.sh down -v

# Remove images
docker rmi omop-etl_omop-etl-dev

# Clean Docker system
docker system prune -f
```

## Advanced Usage

### Custom Build Configurations

#### Custom CMake Options

```bash
# Build with custom options
./scripts/build.sh exec "
    cmake -DCMAKE_BUILD_TYPE=Release \
          -DBUILD_TESTS=ON \
          -DBUILD_INTEGRATION_TESTS=ON \
          -DENABLE_COVERAGE=ON \
          -DENABLE_SANITIZERS=ON \
          -B build/custom
"
```

#### Custom Toolchain

```bash
# Use custom toolchain file
./scripts/build.sh exec "
    cmake -DCMAKE_TOOLCHAIN_FILE=/path/to/toolchain.cmake \
          -B build/custom
"
```

### Multi-Architecture Development

#### Build for Multiple Architectures

```bash
# Build for multiple architectures
./scripts/build.sh build -e prod --platforms "linux/amd64,linux/arm64"

# Push multi-architecture images
./scripts/build.sh push -e prod --platforms "linux/amd64,linux/arm64"
```

#### Cross-Compilation

```bash
# Use multi-architecture toolchain
./scripts/build.sh exec "
    cmake --preset docker-multiarch-release &&
    cmake --build --preset docker-multiarch-release
"
```

### Performance Optimization

#### Build Performance
```bash
# Use parallel builds
./scripts/build.sh build -e dev --parallel 4

# Use ccache for faster rebuilds
./scripts/build.sh build -e dev --ccache

# Use incremental builds
./scripts/build.sh build -e dev --incremental
```

#### Test Performance
```bash
# Run tests in parallel
./scripts/build.sh test --parallel 4

# Run specific test suites
./scripts/build.sh test -t unit --parallel 4

# Use test sharding
./scripts/build.sh test --shard 1/4
```

### Security Considerations

#### Production Security
```bash
# Generate secure production configuration
./scripts/build.sh config -e prod --secure

# Build with security scanning
./scripts/build.sh build -e prod --security-scan

# Deploy with security policies
./scripts/build.sh up -e prod --security-policies
```

#### Secret Management
```bash
# Use external secret management
export DB_PASSWORD=$(vault kv get -field=password secret/omop/db)

# Generate configuration with secrets
./scripts/build.sh config -e prod
```

## CI/CD Integration

### Automated Testing Pipeline

#### GitHub Actions Example
```yaml
name: Build and Test

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Build and Test
        run: |
          ./scripts/build.sh dev -e dev
          ./scripts/build.sh build -e dev --tests
          ./scripts/build.sh test -e dev
```

#### GitLab CI Example
```yaml
stages:
  - build
  - test
  - deploy

build:
  stage: build
  script:
    - ./scripts/build.sh build -e staging --clean

test:
  stage: test
  script:
    - ./scripts/build.sh test -e staging

deploy:
  stage: deploy
  script:
    - ./scripts/build.sh up -e prod
```

### Pre-commit Hooks

```bash
# Install pre-commit hooks
./scripts/build.sh exec "
    pre-commit install
"

# Run pre-commit checks
./scripts/build.sh exec "
    pre-commit run --all-files
"
```

### Quality Gates

```bash
# Run quality checks
./scripts/build.sh exec "
    # Format check
    find src -name '*.cpp' -o -name '*.h' | xargs clang-format --dry-run --Werror
    
    # Static analysis
    cppcheck --enable=all --error-exitcode=1 src/
    
    # Tests
    ctest --test-dir build/docker-release --output-on-failure
    
    # Coverage check
    lcov --capture --directory build/docker-release --output-file coverage.info
    lcov --summary coverage.info | grep -q 'lines......: [0-9]*%'
"
```

### Deployment Pipeline

#### Build Production Image

```bash
# Build production image
./scripts/build.sh build -e prod --target api

# Tag for registry
docker tag omop-etl-api:latest registry.example.com/omop-etl-api:latest

# Push to registry
docker push registry.example.com/omop-etl-api:latest
```

#### Deploy to Kubernetes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: omop-etl-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: omop-etl-api
  template:
    metadata:
      labels:
        app: omop-etl-api
    spec:
      containers:
      - name: omop-etl-api
        image: registry.example.com/omop-etl-api:latest
        ports:
        - containerPort: 8080
```

## Migration from Legacy System

### From Multiple Dockerfiles

If migrating from the old multiple Dockerfile system:

```bash
# 1. Remove old Dockerfiles
rm Dockerfile.dev Dockerfile.dev.arm64 Dockerfile.api Dockerfile.cli

# 2. Update docker-compose.yml to use unified Dockerfile
# (Already done in the unified system)

# 3. Update build scripts to use unified build system
# (Already done in the unified system)

# 4. Test the new system
./scripts/build.sh dev -e dev
./scripts/build.sh build -e dev --tests
```

### From Old Build Scripts

If migrating from old build scripts:

```bash
# 1. Remove old scripts
rm scripts/docker-build.sh scripts/docker-dev.sh scripts/docker-test.sh

# 2. Update CI/CD pipelines to use unified build system
# (See CI/CD Integration section above)

# 3. Update documentation
# (This guide replaces old documentation)
```

## Summary

### ✅ **Key Steps to Remember**

1. **Always run build commands from the project root directory**
2. **Use the unified build script: `./scripts/build.sh`**
3. **Start with architecture detection: `./scripts/build.sh detect-arch`**
4. **Start development environment: `./scripts/build.sh dev -e dev`**
5. **Use CMake presets for consistent builds: `cmake --preset docker-release`**
6. **Build and test: `./scripts/build.sh build -e dev --tests && ./scripts/build.sh test -e dev`**

### ✅ **Complete Workflow Checklist**

- [ ] Verify you're in the project root directory
- [ ] Detect architecture: `./scripts/build.sh detect-arch`
- [ ] Start environment: `./scripts/build.sh dev -e dev`
- [ ] Verify environment: `./scripts/build.sh status`
- [ ] Build components: `./scripts/build.sh build -e dev --tests`
- [ ] Run tests: `./scripts/build.sh test -e dev`
- [ ] Stop environment: `./scripts/build.sh down`

### 🎯 **Quick Reference Commands**

```bash
# Start development environment (from project root)
./scripts/build.sh dev -e dev

# Build everything
./scripts/build.sh build -e dev --tests

# Run tests
./scripts/build.sh test -e dev

# Enter shell
./scripts/build.sh shell

# Stop environment
./scripts/build.sh down
```

### Key Benefits

- **Reproducible Builds**: Same environment across all platforms
- **Isolated Development**: No conflicts with system dependencies
- **Easy Onboarding**: New developers can start quickly
- **CI/CD Ready**: Seamless integration with automated pipelines
- **Multi-Architecture Support**: Build for different target platforms

### Next Steps

1. **Explore the Codebase**: Familiarize yourself with the project structure
2. **Run the Examples**: Try the example configurations in `examples/`
3. **Contribute**: Follow the development workflows for contributions
4. **Extend**: Add new features following the established patterns

For additional information, refer to:
- [Project Structure Documentation](omop-project-structure.md)
- [Implementation Guide](implementation_guide.md)
- [API Documentation](api/openapi.yaml) 