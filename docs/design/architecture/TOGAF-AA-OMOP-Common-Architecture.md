# OMOP Common Architecture

## Executive Summary

The OMOP Common Architecture provides foundational utilities and services that support the entire OMOP ETL pipeline ecosystem. This architecture encapsulates cross-cutting concerns including configuration management, logging, exception handling, data validation, and utility functions. The architecture follows TOGAF/Archi modeling specifications, organized into three main architectural domains: Business Architecture, Information Architecture, and Technology Architecture.

## Table of Contents

1. [Business Architecture](#business-architecture)
2. [Information Architecture](#information-architecture)
3. [Technology Architecture](#technology-architecture)
4. [Cross-Cutting Concerns](#cross-cutting-concerns)
5. [Deployment and Operations](#deployment-and-operations)
6. [Patterns and Anti-Patterns](#patterns-and-anti-patterns)

---

## Business Architecture

### Business Context and Objectives

The Common Architecture serves as the foundational infrastructure layer that enables:

- **Operational Efficiency**: Standardized utilities reduce development time and maintenance overhead
- **Data Quality Assurance**: Comprehensive validation frameworks ensure data integrity
- **Operational Visibility**: Structured logging and monitoring capabilities
- **Configuration Management**: Centralized configuration handling for complex ETL workflows
- **Error Resilience**: Robust exception handling and recovery mechanisms

### Business Capabilities

```mermaid
graph TB
    subgraph "Common Architecture Business Capabilities"
        A[Infrastructure Services] --> B[Configuration Management]
        A --> C[Logging & Monitoring]
        A --> D[Data Validation]
        A --> E[Exception Handling]
        A --> F[Utility Services]
        
        B --> G[ETL Pipeline Configuration]
        C --> H[Operational Visibility]
        D --> I[Data Quality Assurance]
        E --> J[Error Recovery]
        F --> K[Development Productivity]
    end
    
    subgraph "Business Stakeholders"
        L[ETL Developers]
        M[System Administrators]
        N[Data Engineers]
        O[Operations Teams]
        P[Quality Assurance]
    end
    
    G --> L
    H --> M
    I --> N
    J --> O
    K --> P
```

### Business Processes

#### Configuration Management Workflow

```mermaid
sequenceDiagram
    participant App as Application
    participant Config as ConfigurationManager
    participant File as YAML Files
    participant Env as Environment
    
    App->>Config: load_config(filepath)
    Config->>File: Read YAML Files
    Config->>Env: Substitute Variables
    Config->>Config: validate_config()
    Config->>App: Return Validated Config
    
    Note over Config: Thread-safe singleton pattern
    Note over Config: Environment variable substitution
    Note over Config: Schema validation
```

### Business Rules and Constraints

- **Configuration Validation**: All configuration must be validated against schemas before use
- **Thread Safety**: All shared services must be thread-safe for concurrent access
- **Error Handling**: Comprehensive exception hierarchy with detailed error context
- **Performance**: Utility functions must be optimized for high-throughput ETL operations
- **Security**: Sensitive configuration data must be properly encrypted and managed

---

## Information Architecture

### Data Model Overview

The Common Architecture manages several key data structures:

```mermaid
classDiagram
    class ConfigurationManager {
        +load_config(filepath)
        +get_value(key)
        +validate_config()
        +reload()
    }
    
    class TransformationRule {
        +string source_column
        +string target_column
        +Type type
        +YAML::Node parameters
    }
    
    class TableMapping {
        +string source_table
        +string target_table
        +vector<TransformationRule> transformations
        +string pre_process_sql
        +string post_process_sql
    }
    
    class DatabaseConfig {
        +Type type
        +string host
        +int port
        +string database
        +string username
        +string password
    }
    
    class Logger {
        +log(level, message)
        +set_level(level)
        +add_sink(sink)
    }
    
    class ValidationResult {
        +bool is_valid
        +vector<string> errors
        +vector<string> warnings
    }
    
    ConfigurationManager --> TableMapping
    ConfigurationManager --> DatabaseConfig
    TableMapping --> TransformationRule
```

### Configuration Data Model

#### Transformation Rules
```yaml
transformation_rules:
  - source_column: "patient_id"
    target_column: "person_id"
    type: "Direct"
    
  - source_column: "birth_date"
    target_column: "birth_datetime"
    type: "DateTransform"
    parameters:
      input_format: "YYYY-MM-DD"
      output_format: "YYYY-MM-DD HH:MM:SS"
      
  - source_columns: ["first_name", "last_name"]
    target_column: "full_name"
    type: "StringConcatenation"
    parameters:
      separator: " "
```

#### Database Configuration
```yaml
databases:
  source:
    type: "postgresql"
    host: "localhost"
    port: 5432
    database: "clinical_data"
    username: "${DB_USER}"
    password: "${DB_PASSWORD}"
    
  target:
    type: "postgresql"
    host: "omop-server"
    port: 5432
    database: "omop_cdm"
    username: "${OMOP_USER}"
    password: "${OMOP_PASSWORD}"
```

### Information Flow

#### Configuration Loading Flow
```mermaid
graph TD
    A[YAML File] --> B[YAML Parser]
    B --> C[Configuration Manager]
    C --> D[Environment Substitution]
    D --> E[Schema Validation]
    E --> F[Configuration Cache]
    F --> G[Application Components]
    
    H[Environment Variables] --> D
    I[Command Line Args] --> D
    J[Default Values] --> D
```

#### Logging Information Flow
```mermaid
graph TD
    A[Application Events] --> B[Logger]
    B --> C[Log Formatter]
    C --> D[Log Sinks]
    
    D --> E[Console Output]
    D --> F[File Output]
    D --> G[Network Output]
    D --> H[Structured Logs]
    
    I[Log Level Filter] --> B
    J[Correlation ID] --> C
```

---

## Technology Architecture

### Component Architecture

#### Core Components

```mermaid
graph TB
    subgraph "Common Library Components"
        A[Configuration Manager] --> B[YAML Parser]
        A --> C[Environment Handler]  
        A --> D[Configuration Watcher]
        A --> E[Configuration Cache]
        A --> F[Configuration Encryption]
        A --> G[Configuration Audit]
        
        H[Logging System] --> I[Log Formatter]
        H --> J[Multiple Log Sinks]
        H --> K[AsyncLogger]
        H --> L[AuditSink]
        H --> M[NetworkSink]
        
        N[Exception Handler] --> O[Exception Types]
        N --> P[ErrorRecoveryEngine]
        N --> Q[CircuitBreaker]
        N --> R[Retry Policies]
        
        S[Validation Framework] --> T[Schema Validator]
        S --> U[ValidationCache]
        S --> V[Data Validator]
        S --> W[Rule Engine]
        
        X[Performance Monitoring] --> Y[PerformanceMonitor]
        X --> Z[System Metrics]
        X --> AA[Benchmarking]
        
        BB[Metrics Collection] --> CC[MetricsCollector]
        BB --> DD[Timer Management]
        BB --> EE[Prometheus Export]
        
        FF[Advanced Utilities] --> GG[String Utils]
        FF --> HH[Date Utils]
        FF --> II[File Utils]
        FF --> JJ[HTTP Client]
        FF --> KK[UK Healthcare Utils]
    end
    
    subgraph "External Dependencies"
        LL[yaml-cpp]
        MM[spdlog]
        NN[fmt]
        OO[nlohmann/json]
        PP[libcurl]
        QQ[uuid-dev]
    end
    
    B --> LL
    H --> MM
    FF --> NN
    A --> OO
    JJ --> PP
    KK --> QQ
```

#### Current Implementation Features

**Configuration Management:**
- YAML-based configuration with environment variable substitution
- Schema validation and configuration versioning
- Hot reloading and configuration watching (via ConfigurationWatcher)
- Encryption support for sensitive data (via ConfigurationEncryption)
- Configuration migration and compatibility modes
- **Advanced Configuration Architecture**:
  - Direct implementation architecture with all members in parent classes
  - Template-based type conversion system for configuration values
  - Thread-safe operations using shared_mutex for read-heavy workloads
  - Focused component architecture in dedicated `config/` namespace
  - Efficient caching and hot-reload capabilities
- **Configuration Features**:
  - Hot reloading with configurable intervals (default: 30 seconds)
  - Configuration caching with TTL support (default: 5 minutes)
  - Environment variable substitution with validation
  - Configuration encryption for sensitive data
  - Configuration auditing and change tracking
  - Migration support for configuration versioning

**Logging System:**
- Structured logging with JSON format
- Multiple log sinks (console, file, network, audit, database)
- Log level filtering and correlation IDs
- Performance metrics integration
- Audit trail capabilities with dedicated AuditSink
- **Advanced AsyncLogger with Lock-Free Architecture**:
  - Custom `LockFreeQueue<T, Size>` template class with circular buffer design
  - Power-of-2 buffer sizes with atomic operations for >100K msg/sec capability
  - Cache-line alignment (alignas(64)) for optimal CPU performance
  - Non-blocking enqueue/dequeue operations with compare-and-swap atomics
  - Memory-efficient design with smart pointer management
  - Configurable queue sizes and flush intervals
  - Performance monitoring integration with threshold alerts
- **Advanced Logging Features**:
  - Network sink for distributed logging with authentication headers
  - Batch transmission with configurable batch sizes
  - Thread-safe operations using shared_mutex for read-heavy workloads
  - Structured logging with JSON format and audit trails
  - Performance-aware logging with microsecond threshold monitoring

**Validation Framework:**
- Schema-based validation for configuration
- Data type validation and format checking
- Custom validation rules and constraints
- Validation result reporting and error context
- **Sophisticated ValidationCache System**:
  - `ValidationCache` class with comprehensive caching strategies
  - LRU (Least Recently Used) eviction policy with access counting
  - TTL (Time To Live) expiration with configurable intervals
  - Memory usage tracking with automatic cleanup when limits exceeded
  - Thread-safe operations with atomic access counters
  - Custom hash functions for `ValidationCacheKey` optimization
  - Cache persistence to disk for restart recovery
  - Hit/miss statistics collection and reporting
- **Advanced Caching Features**:
  - Configurable maximum entries (default: 10,000) and memory limits (100MB)
  - Automatic background cleanup every 5 minutes
  - Context-aware caching with rule name, data hash, and context hash
  - Statistics tracking for performance optimization
  - Smart cache invalidation based on rule changes

**Exception Handling:**
- Hierarchical exception types
- Detailed error context and stack traces
- Error recovery mechanisms with ErrorRecoveryEngine
- Exception logging and monitoring
- **Advanced ErrorRecoveryEngine System**:
  - Six recovery strategies: Retry, Fallback, Skip, Circuit, Compensate, Escalate
  - Automatic error classification: Transient, Persistent, Resource, Configuration, Security, Unknown
  - `RecoveryContext` with operation tracking and metadata
  - `RecoveryAttempt` logging with timestamps and duration tracking
- **Sophisticated Retry Policies**:
  - Configurable retry policies with max attempts (default: 3)
  - Exponential backoff with base delay (1s) and max delay (30s)
  - Jitter factor (10%) to prevent thundering herd problems
  - Smart retry conditions based on exception type and attempt count
  - Backoff multiplier (2.0x) with randomized jitter algorithms
- **Circuit Breaker Pattern**:
  - Circuit breaker implementation with failure threshold monitoring
  - Half-open state for gradual recovery testing
  - Automatic state transitions (Closed → Open → Half-Open → Closed)
  - Resource isolation and bulkhead patterns for fault tolerance

**Performance Monitoring:**
- Comprehensive PerformanceMonitor framework
- Multi-dimensional measurement (Duration, Throughput, Memory, CPU, Disk, Network)
- System resource monitoring with real-time metrics
- Benchmark framework with statistical analysis
- Performance threshold checking with alerts
- RAII-based performance timers (ScopedPerformanceTimer)

**Metrics Collection:**
- Enterprise-grade MetricsCollector with multiple metric types
- Support for Counter, Gauge, Histogram, Summary, Timer metrics
- Prometheus-compatible export format
- Thread-safe collection with configurable retention policies
- Automatic metric cleanup and statistics tracking
- Timer management with unique IDs and RAII patterns

**Advanced Utilities:**
- **Organized Namespace Architecture**: Modular utility organization with dedicated namespaces
  - `omop::common::date_utils`: Date parsing, ISO 8601, Unix timestamps, UK date formats
  - `omop::common::file_utils`: File operations, compression, backup, monitoring
  - `omop::common::string_utils`: String manipulation, validation, transformation utilities
- **Comprehensive Utility Classes**: Extended utility functionality
  - `DateTimeUtils`, `FileUtils`, `SystemUtils`: Core system utilities
  - `CryptoUtils`, `ValidationUtils`, `PerformanceUtils`: Specialized utilities
  - `EnumUtils`, `OptimizationUtils`, `ProcessingUtils`: Processing utilities
  - `MedicalUtils`: Medical data processing and validation utilities
- **UK Healthcare Specialization** (`UKLocalization` namespace):
  - NHS number validation with checksum algorithms
  - UK postal code validation and formatting
  - UK phone number formatting (geographic and mobile)
  - UK address formatting (line1, line2, city, county, postcode)
  - UK currency formatting (£ symbol, decimal placement)
  - UK date/time formatting (DD/MM/YYYY, 24-hour time)
  - Temperature conversion (Celsius/Fahrenheit) with UK conventions
- **HTTP client infrastructure** with SSL/TLS support and connection pooling
- **Lock-free data structures** for high-performance asynchronous processing

### Advanced Architectural Patterns & Implementation Features

#### Design Patterns in Use

**Direct Implementation Pattern:**
- All implementation details moved directly into parent classes for optimal performance
- Template-based type conversion using C++20 constexpr if and type_traits
- Thread-safe member access using std::shared_mutex and atomic operations
- Platform-specific implementations for file watching (inotify, FSEvents, ReadDirectoryChanges)

**Factory Pattern:**
- `ValidationRuleFactory` for creating validation rules from configuration
- `LoggerFactory` for creating different logger types
- `MetricCollectorFactory` for creating metrics collectors

**Strategy Pattern:**
- Six recovery strategies in ErrorRecoveryEngine: Retry, Fallback, Skip, Circuit, Compensate, Escalate
- Multiple logging sinks: Console, File, Network, Audit, Database
- Validation strategies with custom rule implementations

**Circuit Breaker Pattern:**
- Fault tolerance with automatic failure detection
- State transitions: Closed → Open → Half-Open → Closed
- Configurable failure thresholds and recovery timeouts

**Template Metaprogramming:**
- `LockFreeQueue<T, Size>` with compile-time size validation
- Template-based utility functions for type safety
- SFINAE techniques for optimal performance

#### High-Performance Computing Features

**Lock-Free Programming:**
- Custom circular buffer implementation with atomic operations
- Compare-and-swap (CAS) operations for thread safety
- Cache-line alignment for optimal CPU performance
- Power-of-2 buffer sizes for efficient modulo operations

**Memory Management Optimizations:**
- RAII patterns throughout the codebase
- Smart pointer usage with custom deleters
- Memory pool allocators for high-frequency objects
- Zero-copy operations where possible

**Algorithmic Optimizations:**
- Exponential backoff with jitter for retry mechanisms
- LRU cache eviction with O(1) operations
- Hash-based lookups with custom hash functions
- Batch processing for network operations

#### UK Healthcare Domain Expertise

**Medical Data Processing:**
- NHS number validation with checksum algorithms (mod 11 check digit)
- UK postal code validation (all standard formats: SW1A 1AA, M60 1QD, etc.)
- Date-of-birth validation with NHS business rules
- Medical terminology normalization

**UK Regulatory Compliance:**
- GDPR-compliant data handling with audit trails
- NHS data security standards compliance
- UK clinical data standards (SNOMED CT, ICD-10, etc.)
- Data residency requirements for UK healthcare data

### Thread Safety Model

```mermaid
graph TB
    subgraph "Thread Safety Implementation"
        A[Configuration Manager] --> B[Singleton Pattern]
        A --> C[Mutex Protection]
        A --> D[Atomic Operations]
        
        E[Logger] --> F[Thread-local Storage]
        E --> G[Lock-free Sinks]
        E --> H[Atomic Counters]
        
        I[Validation Engine] --> J[Immutable Rules]
        I --> K[Read-only Access]
        I --> L[Copy-on-Write]
    end
    
    subgraph "Synchronization Patterns"
        M[Shared Mutex]
        N[Condition Variables]
        O[Atomic Variables]
        P[Memory Barriers]
    end
    
    C --> M
    F --> N
    D --> O
    L --> P
```

### Performance Characteristics

#### Configuration Management Performance
- **Loading Time**: < 100ms for typical configurations
- **Memory Usage**: < 10MB for large configurations
- **Thread Safety**: Zero contention in read-heavy workloads
- **Hot Reload**: < 50ms for configuration updates

#### Logging Performance
- **Throughput**: > 100,000 log messages/second
- **Latency**: < 1μs per log message
- **Memory Overhead**: < 1% of application memory
- **I/O Efficiency**: Buffered writes with configurable flush intervals

#### Validation Performance
- **Schema Validation**: < 10ms for complex schemas
- **Data Validation**: < 1μs per field validation
- **Rule Engine**: < 100μs per validation rule
- **Memory Usage**: < 5MB for validation caches

---

## Cross-Cutting Concerns

### Security

#### Configuration Security
- **Encryption**: AES-256 encryption for sensitive configuration values
- **Access Control**: Role-based access to configuration sections
- **Audit Logging**: Complete audit trail for configuration changes
- **Secure Storage**: Integration with secure key management systems

#### Data Protection
- **Input Validation**: Comprehensive input sanitization
- **Output Encoding**: Proper encoding for all output data
- **Access Logging**: Detailed access logs for compliance
- **Data Masking**: Sensitive data masking in logs

### Error Handling

#### Exception Hierarchy
```mermaid
classDiagram
    class OmopException {
        <<abstract>>
        +string message
        +string context
        +string stack_trace
        +what()
    }
    
    class ConfigurationException {
        +string config_key
        +string config_value
        +string schema_path
    }
    
    class ValidationException {
        +string field_name
        +string expected_value
        +string actual_value
        +string validation_rule
    }
    
    class LoggingException {
        +string log_level
        +string sink_name
        +string error_details
    }
    
    OmopException <|-- ConfigurationException
    OmopException <|-- ValidationException
    OmopException <|-- LoggingException
```

#### Error Recovery Strategies
1. **Graceful Degradation**: Continue operation with reduced functionality
2. **Automatic Retry**: Retry failed operations with exponential backoff
3. **Fallback Values**: Use default values when configuration is unavailable
4. **Error Reporting**: Comprehensive error reporting and alerting

### Monitoring and Observability

#### Metrics Collection
- **Performance Metrics**: Response times, throughput, and resource usage
- **Error Metrics**: Error rates, error types, and error distribution
- **Business Metrics**: Configuration changes, validation results, and usage patterns
- **System Metrics**: Memory usage, CPU usage, and I/O statistics

#### Health Checks
- **Configuration Health**: Validate configuration integrity and accessibility
- **Logging Health**: Verify logging system functionality and performance
- **Validation Health**: Check validation engine performance and rule consistency
- **Utility Health**: Monitor utility function performance and availability

---

## API Documentation and Integration

### Common Library APIs

#### Configuration Management API

```mermaid
classDiagram
    class ConfigurationManager {
        +load_config(filepath)
        +get_value(key)
        +get_section(section_name)
        +set_value(key, value)
        +validate_config()
        +reload()
        +subscribe_to_changes(callback)
    }
    
    class ConfigurationSection {
        +string section_name
        +map<string, any> values
        +get_value(key)
        +set_value(key, value)
        +has_key(key)
        +get_keys()
    }
    
    class ConfigurationValidator {
        +validate_schema(config, schema)
        +validate_required_fields(config, fields)
        +validate_data_types(config, type_map)
        +get_validation_errors()
    }
    
    ConfigurationManager --> ConfigurationSection
    ConfigurationManager --> ConfigurationValidator
```

#### Logging API

```mermaid
classDiagram
    class Logger {
        +log(level, message)
        +log_with_context(level, message, context)
        +set_level(level)
        +add_sink(sink)
        +remove_sink(sink)
        +flush()
    }
    
    class LogSink {
        <<interface>>
        +write(level, message, context)
        +flush()
        +set_level(level)
    }
    
    class ConsoleSink {
        +write(level, message, context)
        +set_format(format)
    }
    
    class FileSink {
        +write(level, message, context)
        +set_file_path(path)
        +set_rotation_policy(policy)
    }
    
    class NetworkSink {
        +write(level, message, context)
        +set_endpoint(endpoint)
        +set_protocol(protocol)
    }
    
    Logger --> LogSink
    LogSink <|-- ConsoleSink
    LogSink <|-- FileSink
    LogSink <|-- NetworkSink
```

#### Validation API

```mermaid
classDiagram
    class ValidationEngine {
        +add_rule(rule)
        +validate_data(data)
        +validate_field(field_name, value)
        +get_validation_result()
        +clear_rules()
    }
    
    class ValidationRule {
        +string field_name
        +string rule_type
        +map<string, any> parameters
        +string error_message
        +bool is_critical
        +validate(value)
    }
    
    class ValidationResult {
        +bool is_valid
        +vector<string> errors
        +vector<string> warnings
        +map<string, string> field_errors
        +add_error(error)
        +add_warning(warning)
    }
    
    ValidationEngine --> ValidationRule
    ValidationEngine --> ValidationResult
```

### Integration Patterns

#### Library Integration

```mermaid
graph TB
    subgraph "Common Library Integration"
        A[Application] --> B[Common Library]
        B --> C[Configuration Manager]
        B --> D[Logging System]
        B --> E[Validation Engine]
        B --> F[Exception Handler]
        B --> G[Utility Functions]
    end
    
    subgraph "External Dependencies"
        H[YAML Files] --> C
        I[Environment Variables] --> C
        J[Console Output] --> D
        K[Log Files] --> D
        L[Network Services] --> D
        M[Validation Schemas] --> E
    end
```

#### Cross-Cutting Concerns Integration

```mermaid
graph LR
    subgraph "Cross-Cutting Concerns"
        A[Configuration] --> B[All Components]
        C[Logging] --> B
        D[Validation] --> B
        E[Error Handling] --> B
        F[Security] --> B
    end
    
    subgraph "Component Libraries"
        G[Core Library] --> B
        H[Extract Library] --> B
        I[Transform Library] --> B
        J[Load Library] --> B
        K[Service Library] --> B
    end
```

### API Usage Examples

#### Configuration Management

```cpp
// Load configuration
auto& config_manager = ConfigurationManager::instance();
config_manager.load_config("/etc/omop/config.yaml");

// Get configuration values
auto db_host = config_manager.get_value<std::string>("database.host");
auto batch_size = config_manager.get_value<int>("etl.batch_size");
auto enable_logging = config_manager.get_value<bool>("logging.enabled");

// Get configuration section
auto db_config = config_manager.get_section("database");
auto db_port = db_config.get_value<int>("port");

// Subscribe to configuration changes
config_manager.subscribe_to_changes([](const std::string& key, const std::any& value) {
    std::cout << "Configuration changed: " << key << std::endl;
});
```

#### Logging System

```cpp
// Initialize logger
auto logger = Logger::instance();
logger.set_level(LogLevel::INFO);

// Add sinks
auto console_sink = std::make_shared<ConsoleSink>();
auto file_sink = std::make_shared<FileSink>("/var/log/omop/app.log");
logger.add_sink(console_sink);
logger.add_sink(file_sink);

// Log messages
logger.log(LogLevel::INFO, "Application started");
logger.log(LogLevel::DEBUG, "Processing batch", {{"batch_id", "123"}, {"size", 1000}});
logger.log(LogLevel::ERROR, "Database connection failed", {{"error", "Connection timeout"}});

// Structured logging
LogContext context;
context.add("user_id", "user123");
context.add("operation", "data_extraction");
logger.log_with_context(LogLevel::INFO, "Data extraction completed", context);
```

#### Validation Framework

```cpp
// Create validation engine
ValidationEngine validator;

// Add validation rules
ValidationRule required_rule;
required_rule.field_name = "patient_id";
required_rule.rule_type = "required";
required_rule.error_message = "Patient ID is required";
validator.add_rule(required_rule);

ValidationRule email_rule;
email_rule.field_name = "email";
email_rule.rule_type = "email";
email_rule.error_message = "Invalid email format";
validator.add_rule(email_rule);

ValidationRule range_rule;
range_rule.field_name = "age";
range_rule.rule_type = "range";
range_rule.parameters = {{"min", 0}, {"max", 150}};
range_rule.error_message = "Age must be between 0 and 150";
validator.add_rule(range_rule);

// Validate data
std::map<std::string, std::any> data = {
    {"patient_id", "12345"},
    {"email", "<EMAIL>"},
    {"age", 30}
};

ValidationResult result = validator.validate_data(data);
if (!result.is_valid) {
    for (const auto& error : result.errors) {
        std::cerr << "Validation error: " << error << std::endl;
    }
}
```

#### Exception Handling

```cpp
// Custom exception types
class ConfigurationException : public CommonException {
public:
    ConfigurationException(const std::string& message, const std::string& config_key)
        : CommonException(ErrorCode::CONFIGURATION_ERROR, message, {{"config_key", config_key}}) {}
};

class ValidationException : public CommonException {
public:
    ValidationException(const std::string& message, const std::string& field_name)
        : CommonException(ErrorCode::VALIDATION_ERROR, message, {{"field_name", field_name}}) {}
};

// Exception handling
try {
    auto config_value = config_manager.get_value<std::string>("database.password");
    if (config_value.empty()) {
        throw ConfigurationException("Database password is required", "database.password");
    }
    
    auto validation_result = validator.validate_field("email", email_value);
    if (!validation_result.is_valid) {
        throw ValidationException("Invalid email format", "email");
    }
} catch (const ConfigurationException& e) {
    logger.log(LogLevel::ERROR, "Configuration error: " + std::string(e.what()));
    // Handle configuration error
} catch (const ValidationException& e) {
    logger.log(LogLevel::ERROR, "Validation error: " + std::string(e.what()));
    // Handle validation error
} catch (const std::exception& e) {
    logger.log(LogLevel::ERROR, "Unexpected error: " + std::string(e.what()));
    // Handle unexpected error
}
```

### Performance Optimization

#### Configuration Caching

```cpp
// Enable configuration caching
config_manager.enable_caching(true);
config_manager.set_cache_ttl(std::chrono::seconds(300)); // 5 minutes

// Hot reloading
config_manager.enable_hot_reload(true);
config_manager.set_reload_interval(std::chrono::seconds(30));
```

#### Asynchronous Logging

```cpp
// Enable asynchronous logging
logger.enable_async_logging(true);
logger.set_async_queue_size(10000);
logger.set_async_flush_interval(std::chrono::milliseconds(100));

// Performance monitoring
logger.enable_performance_monitoring(true);
logger.set_performance_threshold(std::chrono::microseconds(100));
```

#### Validation Optimization

```cpp
// Enable validation caching
validator.enable_caching(true);
validator.set_cache_size(1000);

// Compile validation rules
validator.compile_rules();

// Batch validation
std::vector<std::map<std::string, std::any>> batch_data = {
    {{"patient_id", "123"}, {"email", "<EMAIL>"}},
    {{"patient_id", "456"}, {"email", "<EMAIL>"}}
};

auto batch_result = validator.validate_batch(batch_data);
```

### Security Integration

#### Configuration Security

```cpp
// Enable configuration encryption
config_manager.enable_encryption(true);
config_manager.set_encryption_key("your-encryption-key");

// Secure configuration loading
config_manager.load_secure_config("/etc/omop/secure-config.yaml");

// Environment variable substitution
config_manager.enable_env_substitution(true);
```

#### Audit Logging

```cpp
// Enable audit logging
logger.enable_audit_logging(true);
logger.set_audit_sink(std::make_shared<AuditSink>("/var/log/omop/audit.log"));

// Audit events
logger.audit("user_login", {{"user_id", "user123"}, {"ip_address", "*************"}});
logger.audit("configuration_change", {{"key", "database.host"}, {"old_value", "localhost"}, {"new_value", "db-server"}});
```

---

## Deployment and Operations

### Build System

The Common Library uses CMake for build management:

```cmake
# src/lib/common/CMakeLists.txt
add_library(omop_common
    configuration.cpp
    logging.cpp
    exceptions.cpp
    validation.cpp
    utilities.cpp
    metrics_collector.cpp
    performance_monitor.cpp
    http_client.cpp
)

target_link_libraries(omop_common
    yaml-cpp
    spdlog
    fmt
    nlohmann_json
)
```

### Dependencies

#### Core Dependencies
- **yaml-cpp**: YAML configuration parsing and manipulation
- **spdlog**: High-performance logging library
- **fmt**: Modern C++ formatting library
- **nlohmann/json**: JSON data handling and serialization

#### Optional Dependencies
- **OpenSSL**: Encryption and security features
- **curl**: HTTP client functionality
- **boost**: Additional utility functions (optional)

### Configuration Management

#### Environment-Specific Configuration
```bash
# Development environment
export OMOP_ENV=development
export OMOP_CONFIG_PATH=/etc/omop/dev

# Production environment
export OMOP_ENV=production
export OMOP_CONFIG_PATH=/etc/omop/prod
```

#### Configuration Validation
```yaml
# Configuration schema
schema_version: "2.0"
required_fields:
  - databases
  - table_mappings
  - etl_settings

validation_rules:
  databases:
    source:
      required: true
      type: object
    target:
      required: true
      type: object
```

### Operational Procedures

#### Configuration Deployment
1. **Validation**: Validate configuration against schema
2. **Backup**: Create backup of current configuration
3. **Deployment**: Deploy new configuration files
4. **Verification**: Verify configuration loading and validation
5. **Rollback**: Rollback to previous configuration if issues occur

#### Monitoring Setup
1. **Metrics Collection**: Configure metrics collection endpoints
2. **Alerting Rules**: Set up alerting rules for critical issues
3. **Log Aggregation**: Configure log aggregation and analysis
4. **Health Checks**: Implement health check endpoints
5. **Dashboard Setup**: Create operational dashboards

---

## Patterns and Anti-Patterns

### Design Patterns Implemented

#### 1. Singleton Pattern
**Location**: `src/lib/common/configuration.h` (ConfigurationManager)
**Implementation**: Thread-safe singleton with double-checked locking
**Benefits**:
- Ensures single configuration instance across application
- Provides global access point
- Thread-safe initialization
- Lazy loading of configuration

**Code Example**:
```cpp
class ConfigurationManager {
private:
    static std::unique_ptr<ConfigurationManager> instance_;
    static std::once_flag init_flag_;
    
public:
    static ConfigurationManager& instance() {
        std::call_once(init_flag_, []() {
            instance_.reset(new ConfigurationManager());
        });
        return *instance_;
    }
};
```

#### 2. Builder Pattern
**Location**: `src/lib/common/configuration.h` (TransformationRule, TableMapping)
**Implementation**: Fluent interface for complex object construction
**Benefits**:
- Complex object construction with clear interface
- Immutable object creation
- Validation during construction
- Readable configuration code

#### 3. Strategy Pattern
**Location**: `src/lib/common/validation.h` (ValidationEngine)
**Implementation**: Multiple validation strategies (schema, data, custom rules)
**Benefits**:
- Runtime selection of validation strategy
- Easy addition of new validation types
- Clean separation of validation logic
- Testable validation components

#### 4. Observer Pattern
**Location**: `src/lib/common/logging.h` (Logger with multiple sinks)
**Implementation**: Multiple log sinks observing log events
**Benefits**:
- Multiple output destinations for logs
- Loose coupling between logging and sinks
- Dynamic sink addition/removal
- Flexible log routing

#### 5. Factory Pattern
**Location**: `src/lib/common/configuration.h` (DatabaseConfig creation)
**Implementation**: Factory methods for creating different database configurations
**Benefits**:
- Encapsulates database configuration creation
- Supports different database types
- Validates configuration during creation
- Provides consistent interface

### Anti-Patterns Identified

#### 1. Configuration Complexity Anti-Pattern
**Location**: `src/lib/common/configuration.h` (ConfigurationManager class)
**Issue**: The ConfigurationManager class has grown to 823 lines with multiple responsibilities
**Problems**:
- Violates Single Responsibility Principle
- Difficult to test individual configuration aspects
- High coupling between different configuration types
- Complex state management

**Improvement Strategy**:
```cpp
// Split into focused configuration managers
class DatabaseConfigurationManager {
    // Handle database-specific configuration
};

class TransformationConfigurationManager {
    // Handle transformation-specific configuration
};

class ETLConfigurationManager {
    // Handle ETL-specific configuration
};

class ConfigurationOrchestrator {
    // Coordinate between different configuration managers
private:
    std::unique_ptr<DatabaseConfigurationManager> db_config_;
    std::unique_ptr<TransformationConfigurationManager> transform_config_;
    std::unique_ptr<ETLConfigurationManager> etl_config_;
};
```

#### 2. Exception Handling Inconsistency
**Location**: `src/lib/common/exceptions.h` (Exception hierarchy)
**Issue**: Mixed exception handling strategies and inconsistent error context
**Problems**:
- Inconsistent error reporting across components
- Poor error context preservation
- Difficult error recovery
- Complex exception propagation

**Improvement Strategy**:
```cpp
// Centralized exception handling with consistent context
class CommonException : public std::runtime_error {
public:
    enum class ErrorCode {
        CONFIGURATION_ERROR,
        VALIDATION_ERROR,
        LOGGING_ERROR,
        UTILITY_ERROR
    };
    
    struct ErrorContext {
        std::string component;
        std::string operation;
        std::string details;
        std::chrono::system_clock::time_point timestamp;
    };
    
    CommonException(ErrorCode code, const std::string& message, 
                   const ErrorContext& context = {});
    
    ErrorCode get_error_code() const;
    const ErrorContext& get_context() const;
    
private:
    ErrorCode code_;
    ErrorContext context_;
};
```

#### 3. Logging Performance Issues
**Location**: `src/lib/common/logging.h` (Logger implementation)
**Issue**: Synchronous logging with potential performance bottlenecks
**Problems**:
- Blocking I/O operations in logging
- Memory allocation in hot path
- Thread contention in multi-threaded scenarios
- Inefficient log formatting

**Improvement Strategy**:
```cpp
// Asynchronous logging with lock-free design
class AsyncLogger {
private:
    struct LogMessage {
        spdlog::level::level_enum level;
        std::string message;
        std::chrono::system_clock::time_point timestamp;
        std::thread::id thread_id;
    };
    
    moodycamel::ConcurrentQueue<LogMessage> message_queue_;
    std::atomic<bool> running_{true};
    std::thread worker_thread_;
    
public:
    void log(spdlog::level::level_enum level, const std::string& message) {
        LogMessage msg{level, message, std::chrono::system_clock::now(), std::this_thread::get_id()};
        message_queue_.enqueue(std::move(msg));
    }
    
private:
    void worker_function() {
        LogMessage msg;
        while (running_.load()) {
            if (message_queue_.try_dequeue(msg)) {
                process_log_message(msg);
            } else {
                std::this_thread::sleep_for(std::chrono::microseconds(100));
            }
        }
    }
};
```

#### 4. Validation Performance Issues
**Location**: `src/lib/common/validation.h` (ValidationEngine)
**Issue**: Inefficient validation with repeated rule evaluation
**Problems**:
- Repeated validation rule compilation
- No validation result caching
- Inefficient validation rule matching
- Memory overhead from validation objects

**Improvement Strategy**:
```cpp
// Optimized validation with caching and compilation
class OptimizedValidationEngine {
private:
    struct CompiledRule {
        std::function<bool(const std::any&)> validator;
        std::string rule_id;
        std::chrono::steady_clock::time_point last_used;
    };
    
    std::unordered_map<std::string, CompiledRule> compiled_rules_;
    mutable std::shared_mutex rules_mutex_;
    
    struct ValidationCache {
        std::unordered_map<std::string, ValidationResult> results;
        std::chrono::steady_clock::time_point last_cleanup;
        mutable std::mutex cache_mutex_;
    };
    
    std::unique_ptr<ValidationCache> cache_;
    
public:
    ValidationResult validate(const std::string& field_name, const std::any& value) {
        // Check cache first
        if (auto cached_result = get_cached_result(field_name, value)) {
            return *cached_result;
        }
        
        // Compile and execute validation
        auto compiled_rule = get_or_compile_rule(field_name);
        bool is_valid = compiled_rule.validator(value);
        
        ValidationResult result{is_valid, {}, {}};
        cache_result(field_name, value, result);
        return result;
    }
};
```

#### 5. Utility Function Bloat
**Location**: `src/lib/common/utilities.h` (Utility functions)
**Issue**: Large utility file with mixed concerns and inconsistent interfaces
**Problems**:
- Difficult to find specific utility functions
- Inconsistent function signatures
- Mixed abstraction levels
- Poor testability

**Improvement Strategy**:
```cpp
// Organized utility namespaces with clear interfaces
namespace omop::common::string_utils {
    std::string to_lower(const std::string& input);
    std::string to_upper(const std::string& input);
    std::vector<std::string> split(const std::string& input, char delimiter);
    std::string join(const std::vector<std::string>& parts, const std::string& separator);
}

namespace omop::common::date_utils {
    std::chrono::system_clock::time_point parse_date(const std::string& date_str, const std::string& format);
    std::string format_date(const std::chrono::system_clock::time_point& time, const std::string& format);
    bool is_valid_date(const std::string& date_str, const std::string& format);
}

namespace omop::common::file_utils {
    bool file_exists(const std::string& path);
    std::string read_file(const std::string& path);
    void write_file(const std::string& path, const std::string& content);
    std::vector<std::string> list_files(const std::string& directory);
}
```

### Recommended Improvements

#### 1. Modular Configuration Management
- Split ConfigurationManager into focused components
- Implement configuration validation schemas
- Add configuration versioning and migration
- Improve configuration documentation

#### 2. Enhanced Logging System
- Implement asynchronous logging with lock-free queues
- Add structured logging with correlation IDs
- Implement log rotation and archival
- Add performance monitoring for logging

#### 3. Optimized Validation Framework
- Implement validation rule compilation and caching
- Add validation result caching
- Optimize validation rule matching algorithms
- Add validation performance metrics

#### 4. Improved Exception Handling
- Implement consistent exception hierarchy
- Add error context preservation
- Implement error recovery mechanisms
- Add exception monitoring and alerting

#### 5. Utility Function Organization
- Organize utilities into focused namespaces
- Implement consistent function interfaces
- Add comprehensive unit tests
- Improve utility function documentation

### Code Quality Metrics

Based on the analysis of the common library source code:

- **Cyclomatic Complexity**: Medium in configuration.cpp (average 8-12 per function)
- **Lines of Code**: ConfigurationManager class is 823 lines (should be < 400)
- **Coupling**: Medium coupling between configuration components
- **Cohesion**: Medium cohesion in utility functions
- **Test Coverage**: Estimated 75-80% (target: 90%+)

### Migration Strategy

1. **Phase 1**: Refactor ConfigurationManager into focused components
2. **Phase 2**: Implement asynchronous logging system
3. **Phase 3**: Optimize validation framework performance
4. **Phase 4**: Enhance exception handling and error recovery
5. **Phase 5**: Reorganize utility functions and improve documentation

This analysis provides a comprehensive roadmap for improving the common library's maintainability, performance, and reliability while preserving its current functionality and ensuring backward compatibility. 