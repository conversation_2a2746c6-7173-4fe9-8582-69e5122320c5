{"version": 3, "cmakeMinimumRequired": {"major": 3, "minor": 23}, "configurePresets": [{"name": "base", "hidden": true, "generator": "Ninja", "binaryDir": "${sourceDir}/build/${presetName}", "installDir": "${sourceDir}/install/${presetName}", "cacheVariables": {"CMAKE_CXX_STANDARD": "20", "CMAKE_CXX_STANDARD_REQUIRED": "ON", "CMAKE_EXPORT_COMPILE_COMMANDS": "ON", "CMAKE_CXX_COMPILER": "/usr/bin/clang++", "CMAKE_C_COMPILER": "/usr/bin/clang", "PostgreSQL_ROOT": "/opt/homebrew/opt/postgresql@15", "CMAKE_PREFIX_PATH": "/opt/homebrew", "CDM_SCHEMA": "cdm", "VOCAB_SCHEMA": "vocab"}}, {"name": "docker-base", "hidden": true, "generator": "Ninja", "binaryDir": "${sourceDir}/build/${presetName}", "installDir": "${sourceDir}/install/${presetName}", "toolchainFile": "${sourceDir}/cmake/docker-toolchain.cmake", "cacheVariables": {"CDM_SCHEMA": "cdm", "VOCAB_SCHEMA": "vocab", "BUILD_DOCS": "OFF", "BUILD_APPLICATIONS": "OFF", "CMAKE_CXX_STANDARD": "20", "CMAKE_CXX_STANDARD_REQUIRED": "ON"}}, {"name": "x86_64-release", "displayName": "x86_64 Release", "inherits": "base", "architecture": {"value": "x86_64", "strategy": "external"}, "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "ENABLE_COVERAGE": "OFF", "CMAKE_OSX_ARCHITECTURES": "x86_64"}}, {"name": "x86_64-debug", "displayName": "x86_64 Debug", "inherits": "base", "architecture": {"value": "x86_64", "strategy": "external"}, "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "ENABLE_COVERAGE": "ON", "CMAKE_OSX_ARCHITECTURES": "x86_64"}}, {"name": "docker-release", "displayName": "Docker Release", "inherits": "docker-base", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "ENABLE_COVERAGE": "OFF", "BUILD_TESTS": "ON", "CMAKE_CXX_CLANG_TIDY": ""}}, {"name": "docker-debug", "displayName": "<PERSON><PERSON>g", "inherits": "docker-base", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "ENABLE_COVERAGE": "ON", "BUILD_TESTS": "ON", "BUILD_INTEGRATION_TESTS": "ON", "CMAKE_CXX_CLANG_TIDY": ""}}, {"name": "docker-simple", "displayName": "Docker Simple Build", "inherits": "docker-base", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "ENABLE_COVERAGE": "OFF", "BUILD_TESTS": "OFF", "BUILD_INTEGRATION_TESTS": "OFF", "CMAKE_CXX_CLANG_TIDY": ""}}, {"name": "docker-multiarch-base", "hidden": true, "generator": "Ninja", "binaryDir": "${sourceDir}/build/${presetName}", "installDir": "${sourceDir}/install/${presetName}", "toolchainFile": "${sourceDir}/cmake/docker-multiarch-toolchain.cmake", "cacheVariables": {"CDM_SCHEMA": "cdm", "VOCAB_SCHEMA": "vocab", "BUILD_TESTS": "OFF", "BUILD_DOCS": "OFF", "BUILD_APPLICATIONS": "OFF"}}, {"name": "docker-multiarch-release", "displayName": "Docker Multi-Arch Release", "inherits": "docker-multiarch-base", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "ENABLE_COVERAGE": "OFF", "BUILD_TESTS": "ON", "CMAKE_CXX_CLANG_TIDY": ""}}, {"name": "docker-multiarch-debug", "displayName": "Docker Multi-Arch Debug", "inherits": "docker-multiarch-base", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "ENABLE_COVERAGE": "ON", "BUILD_TESTS": "ON", "CMAKE_CXX_CLANG_TIDY": ""}}], "buildPresets": [{"name": "x86_64-debug", "configurePreset": "x86_64-debug", "configuration": "Debug", "displayName": "x86 64-bit Debug", "description": "Debug build for x86 64-bit architecture"}, {"name": "x86_64-release", "configurePreset": "x86_64-release", "configuration": "Release", "displayName": "x86 64-bit Release", "description": "Release build for x86 64-bit architecture"}, {"name": "docker-debug", "configurePreset": "docker-debug", "configuration": "Debug", "displayName": "<PERSON><PERSON>g", "description": "Debug build for Docker environment"}, {"name": "docker-release", "configurePreset": "docker-release", "configuration": "Release", "displayName": "Docker Release", "description": "Release build for Docker environment"}, {"name": "docker-simple", "configurePreset": "docker-simple", "configuration": "Release", "displayName": "Docker Simple Build", "description": "Simple build for Docker environment (no tests, no clang-tidy)"}, {"name": "docker-multiarch-release", "configurePreset": "docker-multiarch-release", "configuration": "Release", "displayName": "Docker Multi-Arch Release", "description": "Release build for Docker multi-architecture environment"}, {"name": "docker-multiarch-debug", "configurePreset": "docker-multiarch-debug", "configuration": "Debug", "displayName": "Docker Multi-Arch Debug", "description": "Debug build for Docker multi-architecture environment"}], "testPresets": [{"name": "x86_64-debug", "configurePreset": "x86_64-debug", "description": "Run tests with x86_64 debug configuration"}, {"name": "x86_64-release", "configurePreset": "x86_64-release", "description": "Run tests with x86_64 release configuration"}, {"name": "docker-debug", "configurePreset": "docker-debug", "description": "Run tests with Docker debug configuration"}, {"name": "docker-release", "configurePreset": "docker-release", "description": "Run tests with Docker release configuration"}, {"name": "docker-multiarch-release", "configurePreset": "docker-multiarch-release", "description": "Run tests with Docker multi-architecture release configuration"}, {"name": "docker-multiarch-debug", "configurePreset": "docker-multiarch-debug", "description": "Run tests with Docker multi-architecture debug configuration"}]}